<?php

use common\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\LinkPager;

$this->title = '控制台管理-业务续费';
error_reporting(0);
?>
<?php $node = \Yii::$app->session['auth']['node']; ?>

<?php $this->beginBlock('content') ?>

<div class="row">
	<div class="clearfix">
		<div class="pull-right tableTools-container"></div>
	</div>
</div>
<div class="row" style="margin-bottom: 10px;">
	<form action="">
		<input type="hidden" name="r" value="renew-server">
		<div class="col-xs-12">
			<div style="float: left;margin-right: 6px;">
				<select class="form-control" name='query_mode' id="form-field-select-1">
					<option value="vague_query" <?php if (Html::encode(Yii::$app->controller->get('query_mode')) == "vague_query"): ?> selected='selected' <?php endif ?>>IP地址模糊查询</option>
					<option value="exact_query" <?php if (Html::encode(Yii::$app->controller->get('query_mode')) == "exact_query"): ?> selected='selected' <?php endif ?>>IP地址精确查询</option>
				</select>
			</div>
			<div style="float: left;margin-right: 6px;">
				<input type="text" name="ip" value='<?php echo Html::encode(Yii::$app->controller->get('ip')) ?>' placeholder="IP地址">
			</div>
			<div style="float: left;margin-right: 6px;">
				<input type="text" name="unionid" value='<?php echo Html::encode(Yii::$app->controller->get('unionid')) ?>' placeholder="业务ID">
			</div>
			<div style="float: left;margin-right: 6px;">
				<input type="text" name="user_nickname" value='<?php echo Html::encode(Yii::$app->controller->get('user_nickname')) ?>' placeholder="用户昵称">
			</div>
			<div style="float: left;margin-right: 6px;">
				<input type="text" name="qq" value='<?php echo Html::encode(Yii::$app->controller->get('qq')) ?>' placeholder="联系QQ">
			</div>
			<div style="float: left;margin-right: 6px;">
				<input type="text" name="end_time" id="end_time" value='<?php echo Html::encode(Yii::$app->controller->get('end_time')) ?>' placeholder="在这时间之前即将到期" readonly>
			</div>
			<div style="float: left;margin-right: 6px;">
				<select class="form-control" name='admin_id'>
					<option value="">所属销售/客服</option>
					<?php foreach ($UserAdminRes as $value): ?>
						<option value="<?php echo $value['admin_id'] ?>" rel="<?php echo $value['rename'] ?>"
							<?php if ($value['admin_id'] == Html::encode(Yii::$app->controller->get('admin_id'))): ?>
							selected='selected'
							<?php endif ?>><?php echo $value['uname'] ?></option>
					<?php endforeach ?>
				</select>
			</div>
			<!-- 只看已选记录 -->
			<input type="hidden" name="only_check" class="only_check" value='<?php echo Html::encode(Yii::$app->controller->get('only_check')) ?>'>
			<div>
				<button type="submit" class="btn btn-white btn-primary btn-bold searchSubmit" style="margin-left:15px">
					<span class="ace-icon fa fa-search icon-on-right bigger-110"></span>
					搜索
				</button>
				<button type="button" class="btn btn-white btn-success btn-bold" style="margin-left:10px;" onclick="javascript:location.href='<?php echo Url::to(['renew-server/index']) ?>'">
					<span class="ace-icon fa  fa-refresh"></span>
					刷新
				</button>
			</div>
		</div>
	</form>
</div>
<div class="row" style="margin-bottom: 10px;">
	<div class="col-xs-12">
		<a href="javascript:;" class="btn btn-primary btn-sm selectAll">本页全选</a>
		<a href="javascript:;" class="btn btn-primary btn-sm selectInvert">本页反选</a>
		<?php if (Html::encode(Yii::$app->controller->get('only_check')) == ''): ?>
			<a href="javascript:;" class="btn btn-warning btn-sm submitOnlyCheck">只看已选记录</a>
		<?php else: ?>
			<a href="javascript:;" class="btn btn-danger btn-sm submitOnlyCheck">查看全部记录</a>
		<?php endif; ?>
		<a href="javascript:;" class="btn btn-info btn-sm lookCheckInfo">复制已选信息</a>
	</div>
	<div class="col-xs-12" style="padding-top: 10px">
		<div class="well well-sm">
			<span>共有 <?php echo $iCount; ?> 条记录，已选择 <b id="checkNum">0</b> 条记录</span>
			<?php if (in_array("renew-server/create-renew-order", $node) || $this->params['is_administrator_user']): ?>
				<a href="javascript:;" class="btn btn-link" id="submitRenewOrder"><i class="fa fa-check"></i> 提交已选择记录为续费订单</a>
			<?php endif; ?>
		</div>
	</div>
</div>
<form action="" id="checkbox">
	<table id="simple-table" width="100%" class="table table-bordered table-hover">
		<thead>
			<tr role="row">
				<th>选择</th>
				<th>主IP地址</th>
				<th>服务器分类</th>
				<th>付费周期</th>
				<th>售价(元)</th>
				<th>用户昵称</th>
				<th>联系QQ</th>
				<th>销售名称</th>
				<th>自动续费</th>
				<th>使用时间</th>
				<th>到期时间</th>
				<th>机器状态</th>
				<th>租用状态</th>
				<th class="sorting_disabled">操作</th>
			</tr>
		</thead>
		<tbody>
			<?php foreach ($arrRes as $value): ?>
				<tr role="row">
					<td>
						<?php if (@$value['status'] == 1 || @$value['status'] == 4): ?>
							<?php if (@$value['can_order']): ?>
								<?php echo $value['can_order'] . '中'; ?>
							<?php else: ?>
								<div class="checkbox">
									<label>
										<input name="form-field-checkbox" class="ace ace-checkbox-2 checkRenew" type="checkbox" value="<?php echo $value['unionid'] ?>">
										<span class="lbl"></span>
									</label>
								</div>
							<?php endif; ?>
						<?php else: ?>
							<?php if (@$value['status'] == -2): ?>
								<span class='label label-warning'><?php echo '退款中' ?></span>
							<?php elseif (@$value['status'] == 2): ?>
								<span class='label label-warning'><?php echo '变配中' ?></span>
							<?php elseif (@$value['status'] == 3): ?>
								<span class='label label-warning'><?php echo '换机中' ?></span>
							<?php elseif (@$value['status'] == 5): ?>
								<span class='label label-warning'><?php echo '过户中' ?></span>
							<?php elseif (@$value['status'] == 99): ?>
								<span class='label label-warning'><?php echo '工单处理中' ?></span>
							<?php else: ?>
								-
							<?php endif; ?>
						<?php endif; ?>
					</td>
					<td>
						<?php if ($value['ip'] == null): ?>
						<?php else: ?>
							<?php $ipRes =  json_decode(@$value['ip'], true) ?>
							<?php echo $ipRes['0'] ?> &nbsp; <?php if (count($ipRes) > 1): ?><a class="blue" href="<?php echo Url::to(['member-pdt/config-item', 'unionid' => $value['unionid']]) ?>" >更多</a><?php endif; ?>
						<?php endif; ?>
					</td>
					<td><?php echo $value['servertype']['0']['type_name'] ?></td>
					<td>
						<?php
						if ($value['payment_cycle'] == 1) {
							echo '月付';
						} else if ($value['payment_cycle'] == 3) {
							echo '季付';
						} else if ($value['payment_cycle'] == 6) {
							echo '半年付';
						} else if ($value['payment_cycle'] == 12) {
							echo '年付';
						} else {
							echo '多月付';
						}
						?>
					</td>
					<td><?php echo $value['sell_price'] ?></td>
					<td><?php 
						$nickName = $value['usermember'][0]['uname'];
						if(!$nickName && $value['usermember'][0]['username']){
							$nickName = $value['usermember'][0]['username'];
						}
						else if(!$nickName && ArrayHelper::keyExists('member', $value['usermember'][0])){
							$nickName = ArrayHelper::getValue($value['usermember'][0], 'member.username');
						}
						echo $nickName;
					?></td>
					<td><?php echo $value['usermember'][0]['qq']; ?></td>
					<td><?php echo $value['usermember'][0]['admin_name'] ?></td>
					<td><?php echo $value['is_auto'] == 'Y' ? '<span class="label label-warning">是</span>' : '<span class="label label-default">否</span>'; ?></td>
					<td><?php if (!empty($value['start_time'])): ?>
							<?php echo date('Y-m-d H:i:s', $value['start_time']) ?>
						<?php else: ?>
							--
						<?php endif ?>
					</td>
					<td>
						<?php if (!empty($value['end_time'])): ?>
							<?php echo date('Y-m-d H:i:s', $value['end_time']) ?>
						<?php else: ?>
							--
						<?php endif ?>
					</td>
					<td>
						<?php
						$hasPreOff = Yii::$app->db->createCommand("select * from member_pdt_appoint_off where appoint_unionid = '" . $value["unionid"] . "'")->queryOne();
						?>

						<?php if ($hasPreOff): ?>
							<span class='label label-primary'><?php echo '预约下架 ' . date("m-d", $hasPreOff['appoint_time']) ?></span>
						<?php else: ?>
							<?php if (@$value['status'] == 1): ?>
								<span class='label label-success'><?php echo '正常' ?></span>
							<?php elseif (@$value['status'] == -1): ?>
								<span class='label label-danger'><?php echo '已删除' ?></span>
							<?php elseif (@$value['status'] == 0): ?>
								<span class='label label-warning'><?php echo '未开通' ?></span>
							<?php elseif (@$value['status'] == -2): ?>
								<span class='label label-warning'><?php echo '退款中' ?></span>
							<?php elseif (@$value['status'] == 2): ?>
								<span class='label label-warning'><?php echo '变配中' ?></span>
							<?php elseif (@$value['status'] == 3): ?>
								<span class='label label-warning'><?php echo '换机中' ?></span>
							<?php elseif (@$value['status'] == 4): ?>
								<span class='label label-warning'><?php echo '换IP中' ?></span>
							<?php elseif (@$value['status'] == 5): ?>
								<span class='label label-warning'><?php echo '过户中' ?></span>
							<?php elseif (@$value['status'] == 6): ?>
								<span class='label label-warning'><?php echo '关机下架中' ?></span>
							<?php elseif (@$value['status'] == 99): ?>
								<span class='label label-warning'><?php echo '工单处理中' ?></span>
							<?php endif; ?>
						<?php endif; ?>
					</td>
					<td class="" style="">
						<?php if (strtotime(date("Y-m-d", $value['end_time'])) < strtotime(date("Y-m-d", time()))): ?>
							<span class='label label-danger'><?php echo '已 到 期' ?></span>
						<?php elseif (strtotime(date("Y-m-d", $value['end_time'])) == strtotime(date("Y-m-d", time()))): ?>
							<span class='label label-warning'><?php echo '今日到期' ?></span>
						<?php elseif (strtotime(date("Y-m-d", $value['end_time'])) >= strtotime(date("Y-m-d", time())) + 86400 && strtotime(date("Y-m-d", $value['end_time'])) <= strtotime(date("Y-m-d", time())) + 86400 * 3): ?>
							<span class='label label-pink'><?php echo '即将到期' ?></span>
						<?php else: ?>
							<span class='label label-success'><?php echo '正常使用' ?></span>
						<?php endif; ?>
					</td>
					<td>
						<div class="">
							<?php if (in_array("member-pdt/config-item", $node) || $this->params['is_administrator_user']): ?>
								<a class="blue" href="<?php echo Url::to(['member-pdt/config-item', 'unionid' => $value['unionid']]) ?>"  title="查看配置">
									查看配置
								</a>
							<?php endif; ?>

							<?php if (strtotime(date("Y-m-d", $value['end_time'])) < strtotime(date("Y-m-d", time())) || strtotime(date("Y-m-d", $value['end_time'])) == strtotime(date("Y-m-d", time()))): ?>
								<?php if ($value['servicerprovider'] == 0): ?>
									&nbsp;&nbsp;<a class="blue power_management" href="javascript:void(0);" rel="<?php echo $value['idle_id']; ?>" title="电源管理">
										电源管理
									</a>
								<?php endif; ?>
							<?php endif; ?>
							&nbsp;&nbsp;
						</div>
					</td>
				</tr>
			<?php endforeach; ?>
		</tbody>
	</table>
	<div class="row">
		<div class="col-xs-6">
			<div class="dataTables_info" id="dynamic-table_info" role="status" aria-live="polite">每页<?php echo $pageSize; ?>条记录，共有<?php echo $iCount; ?>条记录</div>
		</div>
		<div class="col-xs-6 pagination1" style="text-align: right;">
			<?php
			echo LinkPager::widget([
				'pagination' => $page,
				'firstPageLabel' => "首页",
				'prevPageLabel' => '上一页',
				'nextPageLabel' => '下一页',
				'lastPageLabel' => '末页',
			]);
			?>
		</div>
	</div>
	<textarea id="copy" style="opacity:0" data-clipboard-action="copy"></textarea>
</form>
<!-- 电源管理 start -->
<style>
	.power_list {
		margin-bottom: 10px;
		display: inline-block;
		margin-left: 10px;
	}

	.alert-warn,
	.alert-error,
	.alert-success {
		padding: 7px 22px 5px 37px;
		background: url(img/uc/tip_icon_warn_16.png) no-repeat 10px 10px #fff3e5;
		border: 1px solid #ff8800;
		border-radius: 2px;
		color: #ff8800;
		font-size: 12px;
		line-height: 2em;
	}

	.margin-bottom-20 {
		margin-bottom: 20px;
	}
</style>
<div class="bootbox modal fade bootbox-prompt in" id="power_management_show" tabindex="-1" role="dialog" style="padding-right: 17px;">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">×</font>
					</font>
				</button>
				<h4 class="modal-title">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">电源管理</font>
					</font>
				</h4>
			</div>
			<div class="modal-body">
				<div class="bootbox-body">
					<div class="alert-warn margin-bottom-20">
						* 电源开关机，重启 请谨慎操作<br />
					</div>
					<div class="form-group">
						<input type="hidden" id="implement_machine_id" value="">
						<?php if (in_array("power-manage/power-query", $node) || $this->params['is_administrator_user']): ?>
							<span class="power_list"><a class="btn btn-xs btn-primary Powerquery" href="javascript:void(0);">电源状态查询</a></span>
						<?php endif; ?>

						<?php if (in_array("power-manage/switchgear", $node) || $this->params['is_administrator_user']): ?>
							<span class="power_list"><a class="btn btn-xs btn-primary Bootup" href="javascript:void(0);">开机</a></span>
							<span class="power_list"><a class="btn btn-xs btn-primary Shutdown" href="javascript:void(0);">关机</a></span>
						<?php endif; ?>

						<?php if (in_array("power-manage/restart-machine", $node) || $this->params['is_administrator_user']): ?>
							<span class="power_list"><a class="btn btn-xs btn-primary machine_restart" href="javascript:void(0);">重启机器</a></span>
						<?php endif; ?>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">关 闭</font>
					</font>
				</button>
			</div>
		</div>
	</div>
</div>
<!--电源管理 end -->


<!-- inline scripts related to this page -->
<script>
	var end = {
		elem: '#end_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas) {
			end.min = datas; //开始日选好后，重置结束日的最小日期
			end.start = datas //将结束日的初始值设定为开始日
		}
	};
	laydate(end);
	laydate.skin('molv');

	//初始化记录
	var recordlist = localStorage.getItem('renew_check');
	if (recordlist) {
		var recordArray = recordlist.split(',');
		var totalNum = recordArray.length;
		$("#checkNum").html(totalNum);
		var checkboxNum = $(".checkRenew").length;
		for (var i = 0; i < checkboxNum; i++) {
			for (var k in recordArray) {
				if ($(".checkRenew:eq(" + i + ")").val() == recordArray[k]) {
					$(".checkRenew:eq(" + i + ")").prop("checked", true);
				}
			}
		}
	}

	//添加到已选
	function addCheckRecord(list) {
		if (!list || list.length <= 0) {
			return false;
		}
		var recordlist = localStorage.getItem('renew_check');
		if (recordlist) {
			var recordArray = recordlist.split(',');
		} else {
			var recordArray = [];
		}

		var hasRecord = false;

		for (var i in list) {
			hasRecord = false;
			for (var k in recordArray) {
				if (list[i] == recordArray[k]) {
					hasRecord = true;
					continue;
				}
			}

			if (!hasRecord) {
				recordArray.push(list[i]);
			}
		}

		var totalNum = recordArray.length;
		$("#checkNum").html(totalNum);
		var newRecord = recordArray.join(',');
		localStorage.setItem('renew_check', newRecord);
	}

	//从已选删除
	function removeCheckRecord(list) {
		if (!list || list.length <= 0) {
			return false;
		}

		var recordlist = localStorage.getItem('renew_check');
		if (recordlist) {
			var recordArray = recordlist.split(',');
		} else {
			var recordArray = [];
		}

		for (var i in list) {
			for (var k in recordArray) {
				if (list[i] == recordArray[k]) {
					recordArray.splice(k, 1);
					break;
				}
			}
		}

		var totalNum = recordArray.length;
		$("#checkNum").html(totalNum);
		var newRecord = recordArray.join(',');
		localStorage.setItem('renew_check', newRecord);
	}

	//单选
	$(".checkRenew").change(function() {
		var type = $(this).prop("checked");
		var unionid = $(this).val();
		if (type) {
			addCheckRecord([unionid]);
		} else {
			removeCheckRecord([unionid]);
		}
	});

	//全选
	$(".selectAll").click(function() {
		$(".checkRenew").prop("checked", true);
		var checkboxNum = $(".checkRenew").length;
		var thisCheck = new Array();
		for (var i = 0; i < checkboxNum; i++) {
			thisCheck.push($(".checkRenew:eq(" + i + ")").val());
		}

		addCheckRecord(thisCheck);
	});

	//反选
	$(".selectInvert").click(function() {
		var checkboxNum = $(".checkRenew").length;
		var addArray = [];
		var removeArray = [];
		for (var i = 0; i < checkboxNum; i++) {
			if ($(".checkRenew:eq(" + i + ")").prop("checked") == true) {
				$(".checkRenew:eq(" + i + ")").prop("checked", false);
				removeArray.push($(".checkRenew:eq(" + i + ")").val());
			} else {
				$(".checkRenew:eq(" + i + ")").prop("checked", true);
				addArray.push($(".checkRenew:eq(" + i + ")").val());
			}
		}

		addCheckRecord(addArray);
		removeCheckRecord(removeArray);
	});

	//只看已选记录
	$(".submitOnlyCheck").click(function() {
		if ($(this).hasClass("btn-warning")) {
			var recordlist = localStorage.getItem('renew_check');
			$(".only_check").val(recordlist);
			$(".searchSubmit").click();
			$(this).addClass("btn-danger").removeClass("btn-warning");
			$(this).html("查看全部记录");
		} else {
			$(".only_check").val("");
			$(".searchSubmit").click();
			$(this).addClass("btn-warning").removeClass("btn-danger");
			$(this).html("只看已选记录");
		}
	});

	//查看信息
	$(".lookCheckInfo").click(function() {

		layer.confirm('请选择复制的使用类型', {
			btn: ['企业QQ', '文本文档']
		}, function() {
			var recordlist = localStorage.getItem('renew_check');
			var url = "<?php echo Url::to(['renew-server/copy-select']) ?>";

			$.ajax({
				type: "POST",
				url: url,
				data: {
					"list": recordlist,
					"type": "qq"
				},
				async: false,
				success: function(e) {
					if (e.data.status == 0) {
						layer.alert(e.data.info, {
							icon: 7
						});
					} else {
						$("#copy").val(e.data.data);
						$("#copy").focus();
						$("#copy").select();

						if (copyinfo()) {
							layer.alert("复制完成", {
								icon: 1
							});
						} else {
							layer.alert("复制失败，可能由于浏览器不支持<br/>请使用：chrome浏览器或360浏览器(极速模式)", {
								icon: 7
							});
						}

						/*
						try {
						    if(document.execCommand('copy', false, null)) {
						        layer.alert("复制完成", {icon:1});
						    } else {
						        layer.alert("复制失败，可能由于浏览器不支持<br/>请使用：chrome浏览器或360浏览器(极速模式)", {icon:7});
						    }
						} catch(err) {
						    layer.alert("复制失败，可能由于浏览器不支持<br/>请使用：chrome浏览器或360浏览器(极速模式)", {icon:7});
						}*/

					}
				},
				dataType: "json"
			});
		}, function() {
			var recordlist = localStorage.getItem('renew_check');
			var url = "<?php echo Url::to(['renew-server/copy-select']) ?>";

			$.ajax({
				type: "POST",
				url: url,
				data: {
					"list": recordlist,
					"type": "text"
				},
				async: false,
				success: function(e) {
					if (e.data.status == 0) {
						layer.alert(e.data.info, {
							icon: 7
						});
					} else {
						$("#copy").val(e.data.data);
						$("#copy").focus();
						$("#copy").select();
						if (copyinfo()) {
							layer.alert("复制完成", {
								icon: 1
							});
						} else {
							layer.alert("复制失败，可能由于浏览器不支持<br/>请使用：chrome浏览器或360浏览器(极速模式)", {
								icon: 7
							});
						}

						/*
						if(select_all_and_copy(document.getElementById('copy'))) {
						    layer.alert("复制完成", {icon:1});
						} else {
						    layer.alert("复制失败，可能由于浏览器不支持<br/>请使用：chrome浏览器或360浏览器(极速模式)", {icon:7});
						}
						*/

						/*
						try {
						    if(document.execCommand('copy', false, null)) {
						        layer.alert("复制完成", {icon:1});
						    } else {
						        layer.alert("复制失败，可能由于浏览器不支持<br/>请使用：chrome浏览器或360浏览器(极速模式)", {icon:7});
						    }
						} catch(err) {
						    layer.alert("复制失败，可能由于浏览器不支持<br/>请使用：chrome浏览器或360浏览器(极速模式)", {icon:7});
						}
						*/
					}
				},
				dataType: "json"
			});
		});
	});

	function copyinfo() {
		try {
			var clipboard = new ClipboardJS('.layui-layer-btn a', {
				text: function() {
					return $("#copy").val();
				}
			});
			clipboard.on('error', function(e) {
				return false;
			});
			return true;
		} catch {
			return false;
		}
	}

	$("#submitRenewOrder").click(function() {

		var recordlist = localStorage.getItem('renew_check');
		var url = "<?php echo Url::to(['renew-server/create-renew-order']) ?>";

		var recordNum = recordlist.split(',').length;

		var confirmIndex = layer.confirm('确定要将所选的' + recordNum + '条记录生成续费订单吗?', {
			btn: ['确定', '取消']
		}, function() {
			layer.close(confirmIndex);
			//重置筛选
			localStorage.setItem('renew_check', '');
			var index = layer.load(1, {
				shade: [0.7, '#fff']
			});
			$.post(url, {
				"list": recordlist
			}, function(e) {
				layer.close(index);
				if (e.data.status == 0) {
					layer.alert(e.data.info, {
						icon: 7
					}, function() {
						location.reload();
					});
				} else {
					//重置已选项
					localStorage.setItem('renew_check', "");
					layer.alert(e.data.info, {
						icon: 1
					}, function() {
						location.reload();
					});
				}
			}, "json");
		});
	});

	$(".power_management").click(function() {
		var loading = layer.load(2);
		var idle_id = $(this).attr("rel");
		if (!idle_id || idle_id == null || idle_id == '') {
			layer.alert('未知自有机器信息', {
				icon: 7
			});
		}
		$('#implement_machine_id').val(idle_id);
		$('#power_management_show').modal('show');
		layer.closeAll('loading');
	});

	//电源查询
	$('.Powerquery').click(function() {
		var _this = $(this);
		layer.confirm('确定查询电源状态吗？', {
			icon: 3,
			btn: ['确定', '取消']
		}, function() {
			var url = "<?php echo Url::to(['power-manage/power-query']) ?>";
			var loading = layer.load(0, {
				shade: [0.7, '#f1f3f5']
			});
			var idle_id = $('#implement_machine_id').val();
			$.post(url, {
				id: idle_id,
				str_action: 'status'
			}, function(data) {
				layer.close(loading);
				if (data['data']['status']) {
					layer.alert(data['data']['info'], {
						icon: 1
					});
				} else {
					layer.alert(data['data']['info'], {
						icon: 7
					});
				}
			}, 'json');
		}, function() {

		});
		return false;
	});

	//开机
	$('.Bootup').click(function() {
		var _this = $(this);
		layer.confirm('确定开机吗？', {
			icon: 3,
			btn: ['确定', '取消']
		}, function() {
			var url = "<?php echo Url::to(['power-manage/switchgear']) ?>";
			var loading = layer.load(0, {
				shade: [0.7, '#f1f3f5']
			});
			var idle_id = $('#implement_machine_id').val();
			$.post(url, {
				id: idle_id,
				str_action: 'on'
			}, function(data) {
				layer.close(loading);
				if (data['data']['status']) {
					layer.alert(data['data']['info'], {
						icon: 1
					});
				} else {
					layer.alert(data['data']['info'], {
						icon: 7
					});
				}
			}, 'json');
		}, function() {

		});
		return false;
	});

	//关机
	$('.Shutdown').click(function() {
		var _this = $(this);
		layer.confirm('确定关机吗？', {
			icon: 3,
			btn: ['确定', '取消']
		}, function() {
			var url = "<?php echo Url::to(['power-manage/switchgear']) ?>";
			var loading = layer.load(0, {
				shade: [0.7, '#f1f3f5']
			});
			var idle_id = $('#implement_machine_id').val();
			$.post(url, {
				id: idle_id,
				str_action: 'off'
			}, function(data) {
				layer.close(loading);
				if (data['data']['status']) {
					layer.alert(data['data']['info'], {
						icon: 1
					});
				} else {
					layer.alert(data['data']['info'], {
						icon: 7
					});
				}
			}, 'json');
		}, function() {

		});
		return false;
	});

	//重启
	$('.machine_restart').click(function() {
		var _this = $(this);
		layer.confirm('确定重启机器吗？', {
			icon: 3,
			btn: ['确定', '取消']
		}, function() {
			var url = "<?php echo Url::to(['power-manage/restart-machine']) ?>";
			var loading = layer.load(0, {
				shade: [0.7, '#f1f3f5']
			});
			var idle_id = $('#implement_machine_id').val();
			$.post(url, {
				id: idle_id,
				str_action: 'reset'
			}, function(data) {
				layer.closeAll('loading');
				if (data['data']['status']) {
					layer.alert(data['data']['info'], {
						icon: 1
					});
				} else {
					layer.alert(data['data']['info'], {
						icon: 7
					});
				}
			}, 'json');
		}, function() {

		});
		return false;
	});
</script>
<?php $this->endBlock(); ?>