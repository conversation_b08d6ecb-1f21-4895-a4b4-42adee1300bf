@charset "UTF-8";
html {
  color: #333;
  background: #fff;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-rendering: optimizelegibility;
}

body {
  background: #eff2f7;
  overflow-x: hidden;
  position: relative;
  color: var(--txtcolor);
}

body * {
  box-sizing: border-box;
  font: 14px codeB, "Microsoft YaHei", sans-serif;
}

@font-face {
  font-family: "codeB";
  src: url("../../yibazhan/font/DINPro-Medium.woff") format("truetype");
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
}
body,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
th,
td,
hr,
button,
article,
aside,
details,
figcaption,
figure,
footer,
header,
menu,
nav,
section {
  margin: 0;
  padding: 0;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
menu,
nav,
section {
  display: block;
}

audio,
canvas,
video {
  display: inline-block;
}

body,
button,
input,
select,
textarea {
  font: 14px codeB, "Microsoft YaHei", sans-serif;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  padding: 0;
  border: 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

fieldset,
img {
  border: 0;
}

blockquote {
  position: relative;
  color: var(--txtdesc);
  font-weight: 400;
  border-left: 1px solid #1abc9c;
  padding-left: 1em;
  margin: 1em 3em 1em 2em;
}

@media only screen and (max-width: 640px) {
  blockquote {
    margin: 1em 0;
  }
}
acronym,
abbr {
  border-bottom: 1px dotted;
  font-variant: normal;
  text-decoration: none;
}

abbr {
  cursor: help;
}

del {
  text-decoration: line-through;
}

address,
caption,
cite,
code,
dfn,
em,
th,
var {
  font-style: normal;
  font-weight: 400;
}

ul,
ol {
  list-style: none;
}

caption,
th {
  text-align: left;
}

q:before,
q:after {
  content: "";
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
}

:root sub,
:root sup {
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

a {
  cursor: pointer;
  color: var(--txtcolor);
}

a:hover {
  text-decoration: none;
}

ins,
a {
  text-decoration: none;
}

u,
.typo-u {
  text-decoration: underline;
}

mark {
  background: #fffdd1;
  border-bottom: 1px solid #ffedce;
  padding: 2px;
  margin: 0 4px;
}

::-webkit-scrollbar {
  width: 8px;
  height: 4px;
  background: transparent;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: rgb(201, 205, 212);
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(190, 190, 190);
}

body::-webkit-scrollbar {
  width: 10px;
  background: transparent;
}

pre,
code,
pre tt {
  font-family: Courier, "Courier New", monospace;
}

pre {
  background: #f8f8f8;
  border: 1px solid #ddd;
  padding: 1em 1.5em;
  display: block;
  -webkit-overflow-scrolling: touch;
}

hr {
  border: none;
  border-bottom: 1px solid #cfcfcf;
  margin-bottom: 0.8em;
  height: 10px;
}

/*通用布局*/
.sky-column {
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
  justify-content: flex-start;
  width: 100%;
}

.sky-row {
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-start;
  width: 100%;
  flex-direction: row;
  gap: 20px;
}

.sky-wrap {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  width: 100%;
  flex-direction: row;
  gap: 20px;
}

.sky-row-txt {
  display: flex;
  align-items: center;
  gap: 4px;
}
.sky-row-txt .numberOfInstances {
  color: #999;
}

.sky-card {
  background: #fff;
  border-radius: 1px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.06);
  padding: 20px;
  margin-bottom: 20px;
}

.sky-card .sky-title {
  font-size: 14px;
  font-weight: 700;
  width: 100%;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.productstate {
  color: #1a1a1a;
  background: var(--light);
  padding: 6px 10px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}
.productstate.bg-suspended, .productstate.bg-fraud {
  background: var(--danger);
  color: #fff;
}
.productstate.bg-active {
  background: #34c38f;
  color: #fff;
}
.productstate.bg-pending {
  background: var(--warning);
  color: #fff;
}

.expirationReminder {
  font-size: 12px;
  font-weight: 500;
}
.expirationReminder.danger {
  color: #ff584d;
}
.expirationReminder.success {
  color: #27cc85;
}
.expirationReminder.warning {
  color: #fca426;
}

.instance-state span {
  display: block;
  width: 14px;
  height: 14px;
  border-width: 2px;
  border-style: solid;
  background-color: #fff;
  border-radius: 100%;
  border: 2px solid #ccc;
}
.instance-state span.loader, .instance-state span.bx-loader {
  animation: spin 1s linear infinite !important;
  position: relative;
}
.instance-state span.loader:before, .instance-state span.bx-loader:before {
  display: none;
}
.instance-state span.loader::after, .instance-state span.bx-loader::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top-color: var(--primary);
  animation: spin 1s linear infinite;
}
.instance-state span.instance-start {
  border-color: var(--secondary);
}
.instance-state span.instance-closed, .instance-state span.instance-pause {
  border-color: var(--danger);
}
.instance-state span.instance-pending {
  border-color: var(--warning);
}
.instance-state span.instance-unknown {
  border-color: #ccc;
}
.instance-state span.instance-waiting {
  border-color: #ffc107;
}
.instance-state span.instance-waitOn {
  border-color: #fd7e14;
}

.sky-card .sky-title.title-interval {
  border-bottom: 1px dashed #E1E6F0;
  padding-bottom: 10px;
}

.sky-card .sky-title a {
  display: inline-flex;
  align-items: center;
  font-size: 12px;
  line-height: 20px;
  padding: 5px 12px;
  border: 1px solid #e9ecf1;
  transition: all 0.3s linear;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.91);
}

.sky-card .sky-title a.cutOneSHair {
  padding-right: 20px;
}

.sky-interval {
  padding-top: 20px;
  margin-top: 20px;
  border-top: 1px dashed #E1E6F0;
  display: flex;
  justify-content: space-between;
}

.sky-flex-container {
  display: flex;
  flex-wrap: nowrap;
  gap: var(--sky-gap);
  justify-content: space-between;
  flex-direction: row;
}

.sky-flex-container {
  display: flex;
  flex-wrap: nowrap;
  gap: var(--sky-gap);
  justify-content: space-between;
  flex-direction: row;
}

.sky-flex-column {
  display: flex;
  gap: var(--sky-gap);
  flex-wrap: nowrap;
  flex-direction: column;
}

.sky-flex-100 {
  flex: 1 1 100%;
  width: 100%;
}

.sky-flex-80 {
  flex: 1 1 80%;
  width: 80%;
}

.sky-flex-75 {
  flex: 1 1 75%;
  width: 75%;
}

.sky-flex-70 {
  flex: 1 1 70%;
  width: 70%;
}

.sky-flex-66 {
  flex: 1 1 66.6666%;
  width: 66.6666%;
}

.sky-flex-60 {
  flex: 1 1 60%;
  width: 60%;
}

.sky-flex-50 {
  flex: 1 1 50%;
  width: 50%;
}

.sky-flex-40 {
  flex: 1 1 40%;
  width: 40%;
}

.sky-flex-33 {
  flex: 1 1 33.3333%;
  width: 33.3333%;
}

.sky-flex-30 {
  flex: 1 1 30%;
  width: 30%;
}

.sky-flex-25 {
  flex: 1 1 25%;
  width: 25%;
}

.sky-flex-20 {
  flex: 1 1 20%;
  width: 20%;
}

.sky-flex-10 {
  flex: 1 1 10%;
  width: 10%;
}

.maximumRows1 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.maximumRows2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.maximumRows3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.maximumRows4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.layui-elem-quote {
  margin-bottom: 12px;
  padding: 10px 20px 12px;
  -webkit-text-size-adjust: none;
  background: rgba(52, 100, 255, 0.1);
  border: 1px solid rgba(52, 100, 255, 0.3);
  color: #0D236D;
}
.layui-elem-quote h5 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
}
.layui-elem-quote.bottom0 {
  margin-bottom: 0;
}
.layui-elem-quote.bottom5 {
  margin-bottom: 5px;
}
.layui-elem-quote.bottom10 {
  margin-bottom: 10px;
}
.layui-elem-quote * {
  font-size: 14px;
}
.layui-elem-quote.fz12 * {
  font-size: 12px;
}
.layui-elem-quote.fz14 * {
  font-size: 12px;
}
.layui-elem-quote.fw300 * {
  font-weight: 300;
}
.layui-elem-quote.fw500 * {
  font-weight: 500;
}
.layui-elem-quote.fw700 * {
  font-weight: 700;
}
.layui-elem-quote.b0 {
  margin-bottom: 0;
}
.layui-elem-quote a {
  color: var(--primary);
}
.layui-elem-quote.alert-icon {
  position: relative;
  padding-left: 52px;
}
.layui-elem-quote.alert-icon::before {
  content: "";
  position: absolute;
  left: 20px;
  top: 12px;
  width: 20px;
  height: 20px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.layui-elem-quote.alert-icon.centered::before {
  top: 50%;
  transform: translateY(-50%);
}
.layui-elem-quote.icon-info::before {
  background-image: url("../../yibazhan/images/ExclamationMark.svg");
  color: #0056ff;
}
.layui-elem-quote.icon-success::before {
  background-image: url("../../yibazhan/images/alert-success.svg");
  color: rgb(var(--success));
}
.layui-elem-quote.icon-warning::before {
  background-image: url("../../yibazhan/images/alert-warning.svg");
}
.layui-elem-quote.icon-error::before {
  background-image: url("../../yibazhan/images/alert-error.svg");
}
.layui-elem-quote.icon-normal::before {
  background-image: url("../../yibazhan/images/alert-normal.svg");
}
.layui-elem-quote.icon-msg::before {
  background-image: url("../../yibazhan/images/horn2.svg");
}
.layui-elem-quote.red-theme {
  background: rgba(255, 52, 52, 0.1);
  border: 1px solid rgba(255, 52, 52, 0.3);
  color: #B22222;
}
.layui-elem-quote.green-theme {
  background: rgba(52, 255, 52, 0.1);
  border: 1px solid rgba(52, 255, 52, 0.3);
  color: #008000;
}
.layui-elem-quote.yellow-theme {
  background: rgba(255, 235, 52, 0.1);
  border: 1px solid rgba(255, 235, 52, 0.3);
  color: #DAA520;
}
.layui-elem-quote.blue-white-theme {
  background: rgba(52, 100, 255, 0.1);
  border: 1px solid rgba(52, 100, 255, 0.3);
  color: #3464FF;
}
.layui-elem-quote.purple-theme {
  background: rgba(128, 52, 255, 0.1);
  border: 1px solid rgba(128, 52, 255, 0.3);
  color: #800080;
}
.layui-elem-quote.warning-theme {
  color: #a02800;
  background-color: #ffe8d5;
  border-color: #ffe8d5;
}
.layui-elem-quote.orange-theme {
  background: rgba(255, 165, 52, 0.1);
  border: 1px solid rgba(255, 165, 52, 0.3);
  color: #FF8C00;
}
.layui-elem-quote.info-theme {
  background-color: rgb(244, 247, 255);
  color: #000;
}
.layui-elem-quote.grey-theme {
  background: rgb(246, 246, 246);
  border: 1px solid rgba(52, 52, 52, 0.3);
  color: #000;
}
.layui-elem-quote.grey-theme * {
  font-size: 12px;
  font-weight: 500;
}
.layui-elem-quote.blue-theme {
  color: #002da0;
  border: 1px solid #d5e7ff;
  background: #d5e7ff;
}

.layui-elem-quote a,
.cutOneSHair {
  position: relative;
  cursor: pointer;
  display: inline-block;
  padding-right: 20px;
}
.layui-elem-quote a::after,
.cutOneSHair::after {
  content: "";
  position: absolute;
  right: 10px;
  top: 50%;
  width: 5px;
  height: 5px;
  border-right: 2px solid var(--primary);
  border-bottom: 2px solid var(--primary);
  transform: translateY(-50%) rotate(-45deg);
  transition: right 0.3s ease;
}
.layui-elem-quote a.size6::after,
.cutOneSHair.size6::after {
  width: 6px;
  height: 6px;
}
.layui-elem-quote a.size8::after,
.cutOneSHair.size8::after {
  width: 8px;
  height: 8px;
}
.layui-elem-quote a.size10::after,
.cutOneSHair.size10::after {
  width: 10px;
  height: 10px;
}
.layui-elem-quote a.black::after,
.cutOneSHair.black::after {
  border-right: 2px solid var(--txtcolor);
  border-bottom: 2px solid var(--txtcolor);
}
.layui-elem-quote a.white::after,
.cutOneSHair.white::after {
  border-right: 2px solid #fff;
  border-bottom: 2px solid #fff;
}
.layui-elem-quote a.black.border1::after,
.cutOneSHair.black.border1::after {
  border-right: 1px solid var(--txtcolor);
  border-bottom: 1px solid var(--txtcolor);
}
.layui-elem-quote a.white.border1::after,
.cutOneSHair.white.border1::after {
  border-right: 1px solid #fff;
  border-bottom: 1px solid #fff;
}
.layui-elem-quote a:hover::after,
.cutOneSHair:hover::after {
  right: 5px;
}

.no-data {
  height: 150px;
  background: url(../../yibazhan/images/noData.png) no-repeat center 50px;
  background-size: 220px 62px;
  text-align: center;
  line-height: 12px;
  padding-top: 126px;
  color: #8187A1;
  width: 100%;
  font-size: 14px;
}
.no-data a {
  color: var(--primary) !important;
}

.no-product {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
}
.no-product .desc {
  color: var(--primary);
  font-size: 12px;
  font-weight: 400;
}
.no-product .desc-text {
  font-size: 12px;
  font-weight: 400;
}
.no-product i {
  width: 80px;
  height: 80px;
  display: inline-block;
  vertical-align: middle;
  background-image: url("../../yibazhan/images/CurrentlyUnavailable.svg");
  background-repeat: no-repeat;
  background-position: inherit;
  font-size: 0;
}

.btnCopy {
  cursor: pointer;
  color: #999;
}

.sk-table-pageinfo .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 66px;
}

.sk-table-pageinfo .bootstrap-select > .dropdown-toggle {
  padding: 4px;
}

.sk-table-pageinfo .bootstrap-select::after {
  top: 13px;
}

.sky-tabs-card {
  box-shadow: 0 2px 4px 0 rgba(54, 58, 80, 0.32);
  background: #fff;
}
.sky-tabs-card .sky-tabs {
  width: 100%;
  height: 45px;
  background-color: #e7eaef;
  display: flex;
  overflow: hidden;
}
.sky-tabs-card .sky-tabs.Nobefore .sky-tabs-item.active {
  box-shadow: none;
}
.sky-tabs-card .sky-tabs .sky-tabs-item {
  width: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.4);
  position: relative;
  cursor: pointer;
  flex-shrink: 0;
  padding: 0 5px;
  box-sizing: border-box;
}
.sky-tabs-card .sky-tabs .sky-tabs-item.active {
  background-color: #fff;
  box-shadow: inset 0 2px 0 #006eff, inset -1px 0 0 #eaedf1;
}
.sky-tabs-card .sky-tabs .sky-tabs-item.active span {
  position: relative;
  padding-left: 26px;
}
.sky-tabs-card .sky-tabs .sky-tabs-item.active span:after {
  content: "\e650";
  position: absolute;
  font-family: "Uicon" !important;
  font-size: 20px;
  left: 0px;
}
.sky-tabs-card .sky-tabs .sky-tabs-item:last-child:before {
  display: none;
}
.sky-tabs-card .sky-tabs .sky-tabs-item:before {
  content: "";
  position: absolute;
  right: 0;
  width: 1px;
  height: 100%;
  background: #cfd5de;
  top: 0;
}
.sky-tabs-card .sky-tabs .sky-tabs-item i {
  margin-right: 5px;
  font-size: 20px;
  color: rgba(0, 0, 0, 0.8);
}
.sky-tabs-card .sky-tabs .sky-tabs-item span {
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.9);
}
.sky-tabs-card .sky-content-card {
  padding: 24px 20px 40px;
  background: #fff;
}
.sky-tabs-card .sky-content-card .sky-tabs-row {
  display: none;
}
.sky-tabs-card .sky-content-card .sky-tabs-row.active {
  display: flex;
}
.sky-tabs-card .card-body {
  padding: 0 1.25rem 1.25rem;
}

.container-img {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.container-img .image-preview-container {
  flex-grow: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  border: 1px dashed #E1E6F0;
  padding: 10px;
  justify-content: flex-start;
  align-items: flex-start;
}
.container-img .image-preview-container .placeholder {
  width: 165px;
  height: 103px;
  text-align: center;
  color: #ccc;
  background: url("../../yibazhan/images/zjzp_hz.png");
  background-size: cover;
  background-repeat: no-repeat;
}
.container-img .image-preview {
  width: 165px;
  height: 103px;
  background-size: cover;
  background-position: center center;
  position: relative;
  overflow: hidden;
}
.container-img .image-preview .delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(255, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 0;
  width: 30px;
  height: 30px;
  cursor: pointer;
}
.container-img .image-preview .filename {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  text-align: center;
  font-size: 14px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.container-img .upload-btn-container {
  padding-left: 20px;
}

.sky-step {
  margin-bottom: 20px;
  line-height: 28px;
  color: #888;
  counter-reset: step;
  padding: 20px 15px;
  background-color: #fff;
  box-shadow: 0 2px 4px 0 #ccc;
  box-sizing: border-box;
  border-radius: 0;
}
.sky-step ol {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.sky-step .step-item {
  display: flex;
  vertical-align: middle;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.sky-step .step-item .step-item-title {
  color: #000;
  font-size: 14px;
  cursor: pointer;
  font-weight: 700;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.sky-step .step-item .step-item-num {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-width: 1.5px;
  font-weight: 700;
  color: #999;
  border: 1.5px solid #ccc;
  margin-right: 5px;
  width: 26px;
  height: 26px;
  border-radius: 50%;
}
.sky-step .step-item .step-arrow {
  margin: 0 30px;
}
.sky-step .step-item .step-arrow:before {
  font-size: 20px;
  font-weight: bold;
  content: "\e70b";
  font-family: "Uicon";
}
.sky-step .step-item.active .step-item-num {
  color: #fff;
  background: #006eff;
  border: 1.5px solid #006eff;
}

#msgContent {
  padding: 20px;
}
#msgContent * {
  line-height: 32px;
}

.fz12 {
  font-size: 12px;
}

.fz13 {
  font-size: 13px;
}

.fz14 {
  font-size: 14px;
}

.fz15 {
  font-size: 15px;
}

.fz16 {
  font-size: 16px;
}

.fz18 {
  font-size: 18px;
}

.fw300 {
  font-weight: 300;
}

.fw500 {
  font-weight: 500;
}

.fw700 {
  font-weight: 700;
}

.fw900 {
  font-weight: 900;
}

@media (max-width: 768px) {
  .hide-xs {
    display: none !important;
  }
}
@media (min-width: 768px) {
  .hide-pc {
    display: none !important;
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/*# sourceMappingURL=typo.css.map */
