<?php

namespace addons\VymCloud\common\enums;

use common\enums\BaseEnum;

/**
 * 订单状态枚举
 *
 * Class OrderStatusEnum
 * @package addons\VymCloud\common\enums
 */
class OrderEnum extends BaseEnum
{
    ## 订单状态
    const ORDER_STATUS_PENDING   = 1;                                                                                                                                                                                                                                                                                                                 // 待支付
    const ORDER_STATUS_PAID      = 2;                                                                                                                                                                                                                                                                                                                 // 已支付
    const ORDER_STATUS_COMPLETED = 3;                                                                                                                                                                                                                                                                                                                 // 已完成
    const ORDER_STATUS_FAILED    = -1;                                                                                                                                                                                                                                                                                                                // 失败
    const ORDER_STATUS_REFUND    = -2;                                                                                                                                                                                                                                                                                                                // 已退款
    const ORDER_STATUS_CANCELLED = -3;                                                                                                                                                                                                                                                                                                                // 已取消

    ## 支付状态
    const PAYMENT_STATUS_PENDING = 0;                                                                                                                                                                                                                                                                                                                 // 待支付
    const PAYMENT_STATUS_PAID    = 1;                                                                                                                                                                                                                                                                                                                 // 已支付
    const PAYMENT_STATUS_FAILED  = -1;                                                                                                                                                                                                                                                                                                                // 失败

    ## 订单类型
    const ORDER_TYPE_BUY     = 1;                                                                                                                                                                                                                                                                                   // 购买
    const ORDER_TYPE_RENEW = 2;                                                                                                                                                                                                                                                                                                                       // 续费
    const ORDER_TYPE_UPGRADE = 3;                                                                                                                                                                                                                                                                                                                     // 升级

    /**
     * @return array
     */
    public static function getMap(): array
    {
        return [
            self::ORDER_STATUS_PENDING   => '待支付',
            self::ORDER_STATUS_PAID      => '已支付',
            self::ORDER_STATUS_COMPLETED => '已完成',
            self::ORDER_STATUS_FAILED    => '失败',
            self::ORDER_STATUS_REFUND    => '已退款',
            self::ORDER_STATUS_CANCELLED => '已取消',
        ];
    }

    /**
     * 获取状态颜色
     *
     * @return array
     */
    public static function getColors(): array
    {
        return [
            self::ORDER_STATUS_PENDING   => 'warning',
            self::ORDER_STATUS_PAID      => 'info',
            self::ORDER_STATUS_COMPLETED => 'success',
            self::ORDER_STATUS_FAILED    => 'danger',
            self::ORDER_STATUS_REFUND    => 'default',
            self::ORDER_STATUS_CANCELLED => 'secondary',
        ];
    }

    /**
     * 获取支付状态
     *
     * @return string|array
     */
    public static function getPaymentStatus($status = null)
    {
        $map = [
            self::PAYMENT_STATUS_PENDING => '待支付',
            self::PAYMENT_STATUS_PAID    => '已支付',
            self::PAYMENT_STATUS_FAILED  => '失败',
        ];
        if ($status) {
            return $map[$status] ?? '未知';
        }
        return $map;
    }

    /**
     * 获取订单类型
     *
     * @return array
     */
    public static function getOrderType($type = null)
    {
        $map = [
            self::ORDER_TYPE_BUY     => '购买',
            self::ORDER_TYPE_RENEW   => '续费',
            self::ORDER_TYPE_UPGRADE => '升级',
        ];
        if ($type) {
            return $map[$type] ?? '未知';
        }
        return $map;
    }

    /**
     * 获取订单类型颜色
     *
     * @return string|array
     */
    public static function getOrderTypeColors($type = null)
    {
        $map = [
            self::ORDER_TYPE_BUY     => 'info',
            self::ORDER_TYPE_RENEW   => 'success',
            self::ORDER_TYPE_UPGRADE => 'warning',
        ];
        if ($type) {
            return $map[$type] ?? 'default';
        }
        return $map;
    }
}
