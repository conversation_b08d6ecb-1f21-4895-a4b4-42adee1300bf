<?php

namespace addons\VymGeneral\merchant\controllers;

use Yii;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use common\enums\StatusEnum;
use common\models\base\SearchModel;
use common\helpers\ResultHelper;
use common\helpers\ArrayHelper;
use addons\VymGeneral\common\models\Ticket;

/**
 * 工单控制器
 *
 * Class TicketController
 * @package addons\VymGeneral\merchant\controllers
 */
class TicketController extends BaseController
{
    /**
     * 首页
     *
     * @return string
     * @throws \yii\web\NotFoundHttpException
     */
    public function actionIndex()
    {
        $searchModel = new SearchModel([
            'model' => Ticket::class,
            'scenario' => 'default',
            'partialMatchAttributes' => ['title'], // 模糊查询
            'defaultOrder' => [
                'id' => SORT_DESC,
            ],
            'pageSize' => $this->pageSize,
        ]);

        $dataProvider = $searchModel
            ->search(Yii::$app->request->queryParams);
        $dataProvider->query
            ->andFilterWhere(['merchant_id' => $this->getMerchantId()]);

        return $this->render($this->action->id, [
            'dataProvider' => $dataProvider,
            'searchModel' => $searchModel,
            'categoryDropDownList' => Yii::$app->vymGeneralService->ticketCategory->getDropDownList($this->getMerchantId()),
            'priorityList' => [1 => '低', 2 => '中', 3 => '高'],
            'statusList' => [1 => '待处理', 2 => '处理中', 3 => '已解决', 4 => '已关闭'],
            'statistics' => Yii::$app->vymGeneralService->ticket->getStatistics($this->getMerchantId()),
            'merchantList'=>ArrayHelper::map(Yii::$app->services->merchant->findAllByCondition([]), 'id', 'title')
        ]);
    }

    /**
     * 详情
     *
     * @param $id
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        
        return $this->render($this->action->id, [
            'model' => $model,
            'categoryDropDownList' => Yii::$app->vymGeneralService->ticketCategory->getDropDownList($this->getMerchantId()),
            'priorityList' => [1 => '低', 2 => '中', 3 => '高'],
            'statusList' => [1 => '待处理', 2 => '处理中', 3 => '已解决', 4 => '已关闭'],
        ]);
    }

    /**
     * 创建
     *
     * @return string|\yii\web\Response
     */
    public function actionCreate()
    {
        $model = new Ticket();
        $model->priority = 1;
        
        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $model->load($data);
            
            // 处理图片上传
            $model->images = json_encode($this->processUploadedImages(), JSON_UNESCAPED_UNICODE);
            $merId = $this->getMerchantId();
            if(empty($merId)){
                $merId =ArrayHelper::getValue($data,'Ticket.merchant_id');
            }
            
            if (Yii::$app->vymGeneralService->ticket->create($model, $merId)) {
                //新建插入行为时，总后台会把merchant_id 设置成0
                if($model->merchant_id == 0){
                    $model->merchant_id = $merId;
                    $model->save();
                }

                return $this->redirect(['index']);
            }
        }
        
        return $this->render('edit', [
            'model' => $model,
            'categoryDropDownList' => Yii::$app->vymGeneralService->ticketCategory->getDropDownList($this->getMerchantId()),
            'priorityList' => [1 => '低', 2 => '中', 3 => '高'],
        ]);
    }

    /**
     * 回复
     *
     * @param $id
     * @return Response
     * @throws NotFoundHttpException
     */
    public function actionReply($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $model = $this->findModel($id);
        $content = Yii::$app->request->post('content');
        
        if (empty($content)) {
            return ResultHelper::json(422, '回复内容不能为空');
        }
        
        // 处理图片上传
        $images = $this->processUploadedImages();
        
        // 获取当前管理员信息
        $admin = Yii::$app->user->identity;
        $adminId = $admin->id;
        $adminName = $admin->username;
        
        if (Yii::$app->vymGeneralService->ticket->reply($id, $content, $images, $adminId, 2, $adminName)) {
            return ResultHelper::json(200, '回复成功');
        }
        
        return ResultHelper::json(422, '回复失败');
    }

    /**
     * 更新状态
     *
     * @param $id
     * @param $status
     * @return Response
     * @throws NotFoundHttpException
     */
    public function actionStatus($id)
    {
        $status = $this->request->post('status');
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        if (!in_array($status, [1, 2, 3, 4])) {
            return ResultHelper::json(422, '状态参数错误');
        }
        
        if (Yii::$app->vymGeneralService->ticket->updateStatus($id, $status)) {
            return ResultHelper::json(200, '状态更新成功');
        }
        
        return ResultHelper::json(422, '状态更新失败');
    }

    /**
     * 分配处理人
     *
     * @param $id
     * @param $admin_id
     * @return Response
     * @throws NotFoundHttpException
     */
    public function actionAssign($id, $admin_id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        if (Yii::$app->vymGeneralService->ticket->assignAdmin($id, $admin_id)) {
            return ResultHelper::json(200, '分配成功');
        }
        
        return ResultHelper::json(422, '分配失败');
    }

    /**
     * 批量处理
     *
     * @param $status
     * @return Response
     */
    public function actionBatchStatus()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $status = Yii::$app->request->post('status');
        $ids = Yii::$app->request->post('ids', []);
        if (empty($ids)) {
            return ResultHelper::json(422, '请选择工单');
        }
        
        if (!in_array($status, [1, 2, 3, 4])) {
            return ResultHelper::json(422, '状态参数错误');
        }
        
        $count = 0;
        foreach ($ids as $id) {
            if (Yii::$app->vymGeneralService->ticket->updateStatus($id, $status)) {
                $count++;
            }
        }
        
        if ($count > 0) {
            return ResultHelper::json(200, "成功处理 {$count} 个工单");
        }
        
        return ResultHelper::json(422, '处理失败');
    }

    public function actionListCategory($merchant_id){
        $data = Yii::$app->vymGeneralService->ticketCategory->getDropDownList($merchant_id);
        return ResultHelper::json(200, '成功',$data);
    }

    /**
     * 处理上传的图片
     *
     * @return array
     */
    protected function processUploadedImages()
    {
        $images = [];
        $uploadedFiles = Yii::$app->request->post('images', []);
        
        if (!empty($uploadedFiles) && is_array($uploadedFiles)) {
            foreach ($uploadedFiles as $file) {
                if (!empty($file)) {
                    $images[] = $file;
                }
            }
        }
        
//        return !empty($images) ? json_encode($images, JSON_UNESCAPED_UNICODE) : '';
        return $images;
    }

    /**
     * 查找模型
     *
     * @param $id
     * @return Ticket
     * @throws NotFoundHttpException
     */
    protected function findModel($id)
    {
        if (empty($id) || empty(($model = Ticket::find()->where(['id' => $id])->andFilterWhere(['merchant_id' => $this->getMerchantId()])->one()))) {
            throw new NotFoundHttpException('找不到工单');
        }
        
        return $model;
    }
}
