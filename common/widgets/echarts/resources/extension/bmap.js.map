{"version": 3, "file": "bmap.js", "sources": ["../../extension-src/bmap/BMapCoordSys.js", "../../extension-src/bmap/BMapModel.js", "../../extension-src/bmap/BMapView.js", "../../extension-src/bmap/bmap.js"], "sourcesContent": ["/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/* global BMap */\n\nimport {\n    util as zrUtil,\n    graphic,\n    matrix\n} from 'echarts';\n\nfunction BMapCoordSys(bmap, api) {\n    this._bmap = bmap;\n    this.dimensions = ['lng', 'lat'];\n    this._mapOffset = [0, 0];\n\n    this._api = api;\n\n    this._projection = new BMap.MercatorProjection();\n}\n\nBMapCoordSys.prototype.dimensions = ['lng', 'lat'];\n\nBMapCoordSys.prototype.setZoom = function (zoom) {\n    this._zoom = zoom;\n};\n\nBMapCoordSys.prototype.setCenter = function (center) {\n    this._center = this._projection.lngLatToPoint(new BMap.Point(center[0], center[1]));\n};\n\nBMapCoordSys.prototype.setMapOffset = function (mapOffset) {\n    this._mapOffset = mapOffset;\n};\n\nBMapCoordSys.prototype.getBMap = function () {\n    return this._bmap;\n};\n\nBMapCoordSys.prototype.dataToPoint = function (data) {\n    var point = new BMap.Point(data[0], data[1]);\n    // TODO mercator projection is toooooooo slow\n    // var mercatorPoint = this._projection.lngLatToPoint(point);\n\n    // var width = this._api.getZr().getWidth();\n    // var height = this._api.getZr().getHeight();\n    // var divider = Math.pow(2, 18 - 10);\n    // return [\n    //     Math.round((mercatorPoint.x - this._center.x) / divider + width / 2),\n    //     Math.round((this._center.y - mercatorPoint.y) / divider + height / 2)\n    // ];\n    var px = this._bmap.pointToOverlayPixel(point);\n    var mapOffset = this._mapOffset;\n    return [px.x - mapOffset[0], px.y - mapOffset[1]];\n};\n\nBMapCoordSys.prototype.pointToData = function (pt) {\n    var mapOffset = this._mapOffset;\n    var pt = this._bmap.overlayPixelToPoint({\n        x: pt[0] + mapOffset[0],\n        y: pt[1] + mapOffset[1]\n    });\n    return [pt.lng, pt.lat];\n};\n\nBMapCoordSys.prototype.getViewRect = function () {\n    var api = this._api;\n    return new graphic.BoundingRect(0, 0, api.getWidth(), api.getHeight());\n};\n\nBMapCoordSys.prototype.getRoamTransform = function () {\n    return matrix.create();\n};\n\nBMapCoordSys.prototype.prepareCustoms = function (data) {\n    var rect = this.getViewRect();\n    return {\n        coordSys: {\n            // The name exposed to user is always 'cartesian2d' but not 'grid'.\n            type: 'bmap',\n            x: rect.x,\n            y: rect.y,\n            width: rect.width,\n            height: rect.height\n        },\n        api: {\n            coord: zrUtil.bind(this.dataToPoint, this),\n            size: zrUtil.bind(dataToCoordSize, this)\n        }\n    };\n};\n\nfunction dataToCoordSize(dataSize, dataItem) {\n    dataItem = dataItem || [0, 0];\n    return zrUtil.map([0, 1], function (dimIdx) {\n        var val = dataItem[dimIdx];\n        var halfSize = dataSize[dimIdx] / 2;\n        var p1 = [];\n        var p2 = [];\n        p1[dimIdx] = val - halfSize;\n        p2[dimIdx] = val + halfSize;\n        p1[1 - dimIdx] = p2[1 - dimIdx] = dataItem[1 - dimIdx];\n        return Math.abs(this.dataToPoint(p1)[dimIdx] - this.dataToPoint(p2)[dimIdx]);\n    }, this);\n}\n\nvar Overlay;\n\n// For deciding which dimensions to use when creating list data\nBMapCoordSys.dimensions = BMapCoordSys.prototype.dimensions;\n\nfunction createOverlayCtor() {\n    function Overlay(root) {\n        this._root = root;\n    }\n\n    Overlay.prototype = new BMap.Overlay();\n    /**\n     * 初始化\n     *\n     * @param {BMap.Map} map\n     * @override\n     */\n    Overlay.prototype.initialize = function (map) {\n        map.getPanes().labelPane.appendChild(this._root);\n        return this._root;\n    };\n    /**\n     * @override\n     */\n    Overlay.prototype.draw = function () {};\n\n    return Overlay;\n}\n\nBMapCoordSys.create = function (ecModel, api) {\n    var bmapCoordSys;\n    var root = api.getDom();\n\n    // TODO Dispose\n    ecModel.eachComponent('bmap', function (bmapModel) {\n        var painter = api.getZr().painter;\n        var viewportRoot = painter.getViewportRoot();\n        if (typeof BMap === 'undefined') {\n            throw new Error('BMap api is not loaded');\n        }\n        Overlay = Overlay || createOverlayCtor();\n        if (bmapCoordSys) {\n            throw new Error('Only one bmap component can exist');\n        }\n        if (!bmapModel.__bmap) {\n            // Not support IE8\n            var bmapRoot = root.querySelector('.ec-extension-bmap');\n            if (bmapRoot) {\n                // Reset viewport left and top, which will be changed\n                // in moving handler in BMapView\n                viewportRoot.style.left = '0px';\n                viewportRoot.style.top = '0px';\n                root.removeChild(bmapRoot);\n            }\n            bmapRoot = document.createElement('div');\n            bmapRoot.style.cssText = 'width:100%;height:100%';\n            // Not support IE8\n            bmapRoot.classList.add('ec-extension-bmap');\n            root.appendChild(bmapRoot);\n            var bmap = bmapModel.__bmap = new BMap.Map(bmapRoot);\n\n            var overlay = new Overlay(viewportRoot);\n            bmap.addOverlay(overlay);\n\n            // Override\n            painter.getViewportRootOffset = function () {\n                return {offsetLeft: 0, offsetTop: 0};\n            };\n        }\n        var bmap = bmapModel.__bmap;\n\n        // Set bmap options\n        // centerAndZoom before layout and render\n        var center = bmapModel.get('center');\n        var zoom = bmapModel.get('zoom');\n        if (center && zoom) {\n            var pt = new BMap.Point(center[0], center[1]);\n            bmap.centerAndZoom(pt, zoom);\n        }\n\n        bmapCoordSys = new BMapCoordSys(bmap, api);\n        bmapCoordSys.setMapOffset(bmapModel.__mapOffset || [0, 0]);\n        bmapCoordSys.setZoom(zoom);\n        bmapCoordSys.setCenter(center);\n\n        bmapModel.coordinateSystem = bmapCoordSys;\n    });\n\n    ecModel.eachSeries(function (seriesModel) {\n        if (seriesModel.get('coordinateSystem') === 'bmap') {\n            seriesModel.coordinateSystem = bmapCoordSys;\n        }\n    });\n};\n\nexport default BMapCoordSys;", "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\nimport * as echarts from 'echarts';\n\nfunction v2Equal(a, b) {\n    return a && b && a[0] === b[0] && a[1] === b[1];\n}\n\nexport default echarts.extendComponentModel({\n    type: 'bmap',\n\n    getBMap: function () {\n        // __bmap is injected when creating BMapCoordSys\n        return this.__bmap;\n    },\n\n    setCenterAndZoom: function (center, zoom) {\n        this.option.center = center;\n        this.option.zoom = zoom;\n    },\n\n    centerOrZoomChanged: function (center, zoom) {\n        var option = this.option;\n        return !(v2Equal(center, option.center) && zoom === option.zoom);\n    },\n\n    defaultOption: {\n\n        center: [104.114129, 37.550339],\n\n        zoom: 5,\n\n        mapStyle: {},\n\n        roam: false\n    }\n});", "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\nimport * as echarts from 'echarts';\n\nexport default echarts.extendComponentView({\n    type: 'bmap',\n\n    render: function (bMapModel, ecModel, api) {\n        var rendering = true;\n\n        var bmap = bMapModel.getBMap();\n        var viewportRoot = api.getZr().painter.getViewportRoot();\n        var coordSys = bMapModel.coordinateSystem;\n        var moveHandler = function (type, target) {\n            if (rendering) {\n                return;\n            }\n            var offsetEl = viewportRoot.parentNode.parentNode.parentNode;\n            var mapOffset = [\n                -parseInt(offsetEl.style.left, 10) || 0,\n                -parseInt(offsetEl.style.top, 10) || 0\n            ];\n            viewportRoot.style.left = mapOffset[0] + 'px';\n            viewportRoot.style.top = mapOffset[1] + 'px';\n\n            coordSys.setMapOffset(mapOffset);\n            bMapModel.__mapOffset = mapOffset;\n\n            api.dispatchAction({\n                type: 'bmapRoam'\n            });\n        };\n\n        function zoomEndHandler() {\n            if (rendering) {\n                return;\n            }\n            api.dispatchAction({\n                type: 'bmapRoam'\n            });\n        }\n\n        bmap.removeEventListener('moving', this._oldMoveHandler);\n        // FIXME\n        // Moveend may be triggered by centerAndZoom method when creating coordSys next time\n        // bmap.removeEventListener('moveend', this._oldMoveHandler);\n        bmap.removeEventListener('zoomend', this._oldZoomEndHandler);\n        bmap.addEventListener('moving', moveHandler);\n        // bmap.addEventListener('moveend', moveHandler);\n        bmap.addEventListener('zoomend', zoomEndHandler);\n\n        this._oldMoveHandler = moveHandler;\n        this._oldZoomEndHandler = zoomEndHandler;\n\n        var roam = bMapModel.get('roam');\n        if (roam && roam !== 'scale') {\n            bmap.enableDragging();\n        }\n        else {\n            bmap.disableDragging();\n        }\n        if (roam && roam !== 'move') {\n            bmap.enableScrollWheelZoom();\n            bmap.enableDoubleClickZoom();\n            bmap.enablePinchToZoom();\n        }\n        else {\n            bmap.disableScrollWheelZoom();\n            bmap.disableDoubleClickZoom();\n            bmap.disablePinchToZoom();\n        }\n\n        var originalStyle = bMapModel.__mapStyle;\n\n        var newMapStyle = bMapModel.get('mapStyle') || {};\n        // FIXME, Not use JSON methods\n        var mapStyleStr = JSON.stringify(newMapStyle);\n        if (JSON.stringify(originalStyle) !== mapStyleStr) {\n            // FIXME May have blank tile when dragging if setMapStyle\n            if (Object.keys(newMapStyle).length) {\n                bmap.setMapStyle(newMapStyle);\n            }\n            bMapModel.__mapStyle = JSON.parse(mapStyleStr);\n        }\n\n        rendering = false;\n    }\n});", "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * BMap component extension\n */\n\nimport * as echarts from 'echarts';\nimport BMapCoordSys from './BMapCoordSys';\n\nimport './BMapModel';\nimport './BMapView';\n\necharts.registerCoordinateSystem('bmap', BMapCoordSys);\n\n// Action\necharts.registerAction({\n    type: 'bmapRoam',\n    event: 'bmapRoam',\n    update: 'updateLayout'\n}, function (payload, ecModel) {\n    ecModel.eachComponent('bmap', function (bMapModel) {\n        var bmap = bMapModel.getBMap();\n        var center = bmap.getCenter();\n        bMapModel.setCenterAndZoom([center.lng, center.lat], bmap.getZoom());\n    });\n});\n\nexport var version = '1.0.0';\n"], "names": ["graphic", "matrix", "zrUtil", "echarts.extendComponentModel", "echarts.extendComponentView", "echarts.registerCoordinateSystem", "echarts.registerAction"], "mappings": ";;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;AAqBA,AAMA,SAAS,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE;IAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,IAAI,CAAC,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEzB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;;IAEhB,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;CACpD;;AAED,YAAY,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;;AAEnD,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,IAAI,EAAE;IAC7C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;CACrB,CAAC;;AAEF,YAAY,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,MAAM,EAAE;IACjD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACvF,CAAC;;AAEF,YAAY,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,SAAS,EAAE;IACvD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;CAC/B,CAAC;;AAEF,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,YAAY;IACzC,OAAO,IAAI,CAAC,KAAK,CAAC;CACrB,CAAC;;AAEF,YAAY,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,IAAI,EAAE;IACjD,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;IAW7C,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC/C,IAAI,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;IAChC,OAAO,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;CACrD,CAAC;;AAEF,YAAY,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,EAAE,EAAE;IAC/C,IAAI,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;IAChC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;QACpC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;QACvB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;KAC1B,CAAC,CAAC;IACH,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;CAC3B,CAAC;;AAEF,YAAY,CAAC,SAAS,CAAC,WAAW,GAAG,YAAY;IAC7C,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,OAAO,IAAIA,eAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;CAC1E,CAAC;;AAEF,YAAY,CAAC,SAAS,CAAC,gBAAgB,GAAG,YAAY;IAClD,OAAOC,cAAM,CAAC,MAAM,EAAE,CAAC;CAC1B,CAAC;;AAEF,YAAY,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,IAAI,EAAE;IACpD,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IAC9B,OAAO;QACH,QAAQ,EAAE;;YAEN,IAAI,EAAE,MAAM;YACZ,CAAC,EAAE,IAAI,CAAC,CAAC;YACT,CAAC,EAAE,IAAI,CAAC,CAAC;YACT,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;SACtB;QACD,GAAG,EAAE;YACD,KAAK,EAAEC,YAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;YAC1C,IAAI,EAAEA,YAAM,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC;SAC3C;KACJ,CAAC;CACL,CAAC;;AAEF,SAAS,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE;IACzC,QAAQ,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9B,OAAOA,YAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,MAAM,EAAE;QACxC,IAAI,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAI,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,EAAE,GAAG,EAAE,CAAC;QACZ,IAAI,EAAE,GAAG,EAAE,CAAC;QACZ,EAAE,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,QAAQ,CAAC;QAC5B,EAAE,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,QAAQ,CAAC;QAC5B,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;KAChF,EAAE,IAAI,CAAC,CAAC;CACZ;;AAED,IAAI,OAAO,CAAC;;;AAGZ,YAAY,CAAC,UAAU,GAAG,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC;;AAE5D,SAAS,iBAAiB,GAAG;IACzB,SAAS,OAAO,CAAC,IAAI,EAAE;QACnB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;KACrB;;IAED,OAAO,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;;;;;;IAOvC,OAAO,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,GAAG,EAAE;QAC1C,GAAG,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC,KAAK,CAAC;KACrB,CAAC;;;;IAIF,OAAO,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY,EAAE,CAAC;;IAExC,OAAO,OAAO,CAAC;CAClB;;AAED,YAAY,CAAC,MAAM,GAAG,UAAU,OAAO,EAAE,GAAG,EAAE;IAC1C,IAAI,YAAY,CAAC;IACjB,IAAI,IAAI,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;;;IAGxB,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,UAAU,SAAS,EAAE;QAC/C,IAAI,OAAO,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC;QAClC,IAAI,YAAY,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;QAC7C,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;SAC7C;QACD,OAAO,GAAG,OAAO,IAAI,iBAAiB,EAAE,CAAC;QACzC,IAAI,YAAY,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;SACxD;QACD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;;YAEnB,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;YACxD,IAAI,QAAQ,EAAE;;;gBAGV,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;gBAChC,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;gBAC/B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;aAC9B;YACD,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACzC,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,wBAAwB,CAAC;;YAElD,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YAC5C,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC3B,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;;YAErD,IAAI,OAAO,GAAG,IAAI,OAAO,CAAC,YAAY,CAAC,CAAC;YACxC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;;;YAGzB,OAAO,CAAC,qBAAqB,GAAG,YAAY;gBACxC,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;aACxC,CAAC;SACL;QACD,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC;;;;QAI5B,IAAI,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrC,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,MAAM,IAAI,IAAI,EAAE;YAChB,IAAI,EAAE,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;SAChC;;QAED,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC3C,YAAY,CAAC,YAAY,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3D,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC3B,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;;QAE/B,SAAS,CAAC,gBAAgB,GAAG,YAAY,CAAC;KAC7C,CAAC,CAAC;;IAEH,OAAO,CAAC,UAAU,CAAC,UAAU,WAAW,EAAE;QACtC,IAAI,WAAW,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,MAAM,EAAE;YAChD,WAAW,CAAC,gBAAgB,GAAG,YAAY,CAAC;SAC/C;KACJ,CAAC,CAAC;CACN,CAAC;;ACvNF;;;;;;;;;;;;;;;;;;;AAmBA,AAEA,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE;IACnB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;CACnD;;AAED,AAAeC,4BAA4B,CAAC;IACxC,IAAI,EAAE,MAAM;;IAEZ,OAAO,EAAE,YAAY;;QAEjB,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;;IAED,gBAAgB,EAAE,UAAU,MAAM,EAAE,IAAI,EAAE;QACtC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;KAC3B;;IAED,mBAAmB,EAAE,UAAU,MAAM,EAAE,IAAI,EAAE;QACzC,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC;KACpE;;IAED,aAAa,EAAE;;QAEX,MAAM,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;;QAE/B,IAAI,EAAE,CAAC;;QAEP,QAAQ,EAAE,EAAE;;QAEZ,IAAI,EAAE,KAAK;KACd;CACJ,CAAC;;ACrDF;;;;;;;;;;;;;;;;;;;AAmBA,AAEeC,2BAA2B,CAAC;IACvC,IAAI,EAAE,MAAM;;IAEZ,MAAM,EAAE,UAAU,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE;QACvC,IAAI,SAAS,GAAG,IAAI,CAAC;;QAErB,IAAI,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;QAC/B,IAAI,YAAY,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QACzD,IAAI,QAAQ,GAAG,SAAS,CAAC,gBAAgB,CAAC;QAC1C,IAAI,WAAW,GAAG,UAAU,IAAI,EAAE,MAAM,EAAE;YACtC,IAAI,SAAS,EAAE;gBACX,OAAO;aACV;YACD,IAAI,QAAQ,GAAG,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC;YAC7D,IAAI,SAAS,GAAG;gBACZ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC;gBACvC,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC;aACzC,CAAC;YACF,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YAC9C,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;;YAE7C,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACjC,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC;;YAElC,GAAG,CAAC,cAAc,CAAC;gBACf,IAAI,EAAE,UAAU;aACnB,CAAC,CAAC;SACN,CAAC;;QAEF,SAAS,cAAc,GAAG;YACtB,IAAI,SAAS,EAAE;gBACX,OAAO;aACV;YACD,GAAG,CAAC,cAAc,CAAC;gBACf,IAAI,EAAE,UAAU;aACnB,CAAC,CAAC;SACN;;QAED,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;;;;QAIzD,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7D,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;;QAE7C,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;;QAEjD,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC;QACnC,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC;;QAEzC,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,IAAI,IAAI,IAAI,KAAK,OAAO,EAAE;YAC1B,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;aACI;YACD,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;QACD,IAAI,IAAI,IAAI,IAAI,KAAK,MAAM,EAAE;YACzB,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;aACI;YACD,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7B;;QAED,IAAI,aAAa,GAAG,SAAS,CAAC,UAAU,CAAC;;QAEzC,IAAI,WAAW,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;;QAElD,IAAI,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC9C,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,WAAW,EAAE;;YAE/C,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE;gBACjC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;aACjC;YACD,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;SAClD;;QAED,SAAS,GAAG,KAAK,CAAC;KACrB;CACJ,CAAC;;ACxGF;;;;;;;;;;;;;;;;;;;;;;;AAuBA,AAMAC,gCAAgC,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;;;AAGvDC,sBAAsB,CAAC;IACnB,IAAI,EAAE,UAAU;IAChB,KAAK,EAAE,UAAU;IACjB,MAAM,EAAE,cAAc;CACzB,EAAE,UAAU,OAAO,EAAE,OAAO,EAAE;IAC3B,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,UAAU,SAAS,EAAE;QAC/C,IAAI,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;QAC/B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC9B,SAAS,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;KACxE,CAAC,CAAC;CACN,CAAC,CAAC;;AAEH,AAAO,IAAI,OAAO,GAAG,OAAO;;;;;;;;"}