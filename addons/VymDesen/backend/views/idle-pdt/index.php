<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\LinkPager;
$this->title = '控制台管理-自有服务器管理';
error_reporting(0);
?>
<?php $this->beginBlock('content') ?>
<?php $node = \Yii::$app->session['auth']['node'];?>


<div class="row">
	<div class="clearfix">
		<div class="pull-right tableTools-container"></div>
	</div>
</div>
<div class="row" style="margin-bottom: 10px;">
	<form action="">
		<input type="hidden" name="r" value="idle-pdt"> 
		<div class="col-xs-12">
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='query_mode' >
					<option value="vague_query" <?php if (Html::encode(Yii::$app->controller->get('query_mode')) == "vague_query" ): ?> selected='selected' <?php endif ?>>IP地址模糊查询</option>
					<option value="exact_query" <?php if (Html::encode(Yii::$app->controller->get('query_mode')) == "exact_query" ): ?> selected='selected' <?php endif ?>>IP地址精确查询</option>					
				</select>
			</div>
			<div style="float: left;margin-right: 6px;">   
				<input type="text" class="form-control search-query" name="ip" value='<?php echo Html::encode(Yii::$app->controller->get('ip')) ?>'  placeholder="IP地址">
			</div>	
			<div style="float: left;margin-right: 6px;">   
				<input type="text" class="form-control search-query" name="ipmi_ip" value='<?php echo Html::encode(Yii::$app->controller->get('ipmi_ip')) ?>'  placeholder="IPMI地址">
			</div>	
			
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='server_type_id' >
					<option value="">服务器分类</option>
					<?php foreach ($PdtManageTypeList as $value): ?>
					<option value="<?php echo $value['type_id'] ?>"  rel="<?php echo $value['type_id'] ?>"
					<?php if ($value['type_id'] == Html::encode(Yii::$app->controller->get('server_type_id')) ): ?>
					 selected='selected'
					 <?php endif ?>	
					><?php echo $value['type_name'] ?></option>
					<?php endforeach ?>
				</select>
			</div>
			
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='pdt_id' >
					<option value="">产品配置类别</option>
					<?php foreach ($pdtRes as $value): ?>
					<option value="<?php echo $value['id'] ?>" rel="<?php echo $value['name'] ?>"   
					<?php if ($value['id'] == Html::encode(Yii::$app->controller->get('pdt_id')) ): ?>
					 selected='selected'
					 <?php endif ?>	
					><?php echo $value['name'] ?></option>
					<?php endforeach ?>
				</select>
			</div>
			<br><br>
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='room_id' >
					<option value="">所属机房</option>
					<?php foreach ($roomRes as $value): ?>
					<option value="<?php echo $value['id'] ?>" rel="<?php echo $value['id'] ?>"   
					<?php if ($value['id'] == Html::encode(Yii::$app->controller->get('room_id')) ): ?>
					 selected='selected'
					 <?php endif ?>	
					><?php echo $value['name'] ?></option>
					<?php endforeach ?>
				</select>
			</div>
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='cabinet_id' >
					<option value="">所属机柜</option>
					<?php foreach ($cabinetRes as $value): ?>
					<option value="<?php echo $value['id'] ?>" rel="<?php echo $value['name'] ?>"   
					<?php if ($value['id'] == Html::encode(Yii::$app->controller->get('cabinet_id')) ): ?>
					 selected='selected'
					 <?php endif ?>	
					><?php echo $value['name'] ?></option>
					<?php endforeach ?>
				</select>
			</div>
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='attribute_id' style='margin-left:10px'>
					<option value="" >服务器状态属性</option> 
					 <?php foreach ($ServerAttributeRes as $value): ?>
					<option value="<?php echo $value['id'] ?>" rel="<?php echo $value['name'] ?>"   
					<?php if ($value['id'] == Html::encode(Yii::$app->controller->get('attribute_id')) ): ?>
					 selected='selected'
					 <?php endif ?>
					><?php echo $value['name'] ?></option>
					<?php endforeach ?>
				</select>
			</div>
			<div style="float: left;margin-right: 6px;">
				<select class="form-control" name='machine_status' style='margin-left:10px'>
					<option value="" >机器状态</option>
					<option value="健康" <?php if (Html::encode(Yii::$app->controller->get('machine_status')) == '健康'): ?>selected='selected'<?php endif ?>>健康</option>
					<option value="故障" <?php if (Html::encode(Yii::$app->controller->get('machine_status')) == '故障'): ?>selected='selected'<?php endif ?>>故障</option>						
					<option value="修理中" <?php if (Html::encode(Yii::$app->controller->get('machine_status')) == '修理中'): ?>selected='selected'<?php endif ?>>修理中</option>
				</select>
			</div>
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='status' style='margin-left:10px'>
					<option value="">使用状态</option>        									
					<option value="0" <?php if (Html::encode(Yii::$app->controller->get('status')) == 0 && Html::encode(Yii::$app->controller->get('status')) != null): ?>selected='selected'<?php endif ?>>闲置中</option>
					<option value="1" <?php if (Html::encode(Yii::$app->controller->get('status')) == 1): ?>selected='selected'<?php endif ?>>使用中</option>
					<option value="2" <?php if (Html::encode(Yii::$app->controller->get('status')) == 2): ?>selected='selected'<?php endif ?>>待定</option>						
					<option value="3" <?php if (Html::encode(Yii::$app->controller->get('status')) == 3): ?>selected='selected'<?php endif ?>>新购机器占用中</option>
					<option value="4" <?php if (Html::encode(Yii::$app->controller->get('status')) == 4): ?>selected='selected'<?php endif ?>>更换机器占用中</option>
					<option value="5" <?php if (Html::encode(Yii::$app->controller->get('status')) == 5): ?>selected='selected'<?php endif ?>>测试机占用中</option>
					<option value="6" <?php if (Html::encode(Yii::$app->controller->get('status')) == 6): ?>selected='selected'<?php endif ?>>更换IP中</option>
				</select>
			</div>
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='power_status' style='margin-left:10px'>
					<option value="">设备状态</option>					 
					<option value="开机" <?php if (Html::encode(Yii::$app->controller->get('power_status')) == '开机'): ?>selected='selected'<?php endif ?>>开机</option>	
					<option value="关机" <?php if (Html::encode(Yii::$app->controller->get('power_status')) == '关机'): ?>selected='selected'<?php endif ?>>关机</option>
					<option value="未知" <?php if (Html::encode(Yii::$app->controller->get('power_status')) == '未知'): ?>selected='selected'<?php endif ?>>未知</option>
				</select>
			</div>
			<div style="float: left; margin-left: 12px;">   
				<input type="text" class="form-control search-query" name="machine_model" value='<?php echo Html::encode(Yii::$app->controller->get('machine_model')) ?>'  placeholder="设备型号">
			</div>	
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='is_remarks' style='margin-left:10px'>
					<option value="">是否有备注</option>					 
					<option value="1" <?php if (Html::encode(Yii::$app->controller->get('is_remarks')) == '1'): ?>selected='selected'<?php endif ?>>有备注</option>	
					<option value="0" <?php if (Html::encode(Yii::$app->controller->get('is_remarks')) == '0'): ?>selected='selected'<?php endif ?>>无备注</option>
				</select>
			</div>
			<div>
				<button type="submit" class="btn btn-white btn-primary btn-bold" style="margin-left:15px">
					<span class="ace-icon fa fa-search icon-on-right bigger-110"></span>
					搜索
				</button>
				<?php if(in_array("idle-pdt/create", $node) || $this->params['is_administrator_user']):?>
					<button type="button" class="btn btn-white btn-primary btn-bold" style="margin-left:15px;" onclick="javascript:location.href='<?php echo Url::to(['idle-pdt/create']) ?>'">
						<span class="ace-icon fa fa-plus-circle blue"></span>
						添加
					</button>
				<?php endif; ?>
				<button type="button" class="btn btn-white btn-success btn-bold" style="margin-left:10px;" onclick="javascript:location.href='<?php echo Url::to(['idle-pdt/index']) ?>'"
					<span class="ace-icon fa  fa-refresh"></span>
					刷新
				</button>	
				<?php if(in_array("idle-pdt/export", $node) || $this->params['is_administrator_user']):?>
				<button type="button" class="btn btn-white btn-pink btn-bold" style="margin-left:10px;" 
						onclick="javascript:location.href='<?php echo Url::to(['export',
							'server_type_id'=>Html::encode(Yii::$app->controller->get('server_type_id')),
							'ip'=>Html::encode(Yii::$app->controller->get('ip')),
							'ipmi_ip'=>Html::encode(Yii::$app->controller->get('ipmi_ip')),
							'room_id'=>Html::encode(Yii::$app->controller->get('room_id')),
							'cabinet_id'=>Html::encode(Yii::$app->controller->get('cabinet_id')),
							'pdt_id'=>Html::encode(Yii::$app->controller->get('pdt_id')),
							'status'=>Html::encode(Yii::$app->controller->get('status')),
							'attribute_id'=>Html::encode(Yii::$app->controller->get('attribute_id')),
							'power_status'=>Html::encode(Yii::$app->controller->get('power_status')),							
						]) ?>'">
					<span class="ace-icon fa  fa-share"></span>
					导出
				</button>
				<?php endif;?>
				
				<?php if(in_array("idle-pdt/import", $node) || $this->params['is_administrator_user']):?>
				<button type="button" class="btn btn-white btn-purple btn-bold" style="margin-left:10px;" 
						onclick="javascript:location.href='<?php echo Url::to(['import']) ?>'">
					<span class="ace-icon fa fa-cloud-upload"></span>
					导入
				</button> 
				<?php endif;?>
				
				<?php if(in_array("idle-pdt/mac-import", $node) || $this->params['is_administrator_user']):?>
				<button type="button" class="btn btn-white btn-purple btn-bold" style="margin-left:10px;" 
						onclick="javascript:location.href='<?php echo Url::to(['mac-import']) ?>'">
					<span class="ace-icon fa fa-cloud-upload"></span>
					Mac地址批量导入
				</button> 
				<?php endif;?>
				
				<?php if(in_array("idle-pdt/resources-export", $node) || $this->params['is_administrator_user']):?>
				<button type="button" class="btn btn-white btn-pink btn-bold" style="margin-left:10px;" 
						onclick="javascript:location.href='<?php echo Url::to(['resources-export',
							'server_type_id'=>Html::encode(Yii::$app->controller->get('server_type_id')),						
							'room_id'=>Html::encode(Yii::$app->controller->get('room_id')),
							'attribute_id'=>Html::encode(Yii::$app->controller->get('attribute_id')),
						]) ?>'">
					<span class="ace-icon fa  fa-share"></span>
					机房资源统计导出
				</button>
				<?php endif;?>
			</div>
		</div>							
	</form>    	   		
</div>

<style type="text/css">
	.machine_config_sonmodel{display:none;}
	.machine_remark_sonmodel{display:none;}
</style>

<form action="" id="checkbox">
	<table id="simple-table" width="100%" class="table table-striped table-bordered table-hover">
		<thead>
			<tr role="row">
                <!--				<th>ID</th>-->
                <th class="sorting_disabled" >操作</th>
                <th>IP地址</th>
                <th>IPMI地址</th>
<!--				<th colspan="1" class="machine_config modelClose">产品名以及机器配置&nbsp;&nbsp;<a href="javascript:;" class="slide-btn" targetModel="machine_config">展开</a></th>-->
				<th>产品名</th>
				<th>机器配置</th>
                <th>机器备注</th>
				<th>服务器分类</th>
				<th>机 房</th>
				<th>机 柜</th>
				<th>产品配置类别</th>
<!--				<th colspan="1" class="machine_remark modelClose">机器型号以及机器备注&nbsp;&nbsp;<a href="javascript:;" class="slide-btn" targetModel="machine_remark">展开</a></th>-->
				<th>机器型号</th>
				<th>服务器状态属性</th>
				<!--<th>机器状态</th>-->
				<th>已组内网</th>
				<th>供应商</th>
				<th>使用状态</th>
				<th>设备状态</th>
			</tr>
		</thead>
		<tbody>
		   <?php foreach ($arrRes as $value):?>
		   <tr role="row" class="odd">
               <!--				<td>--><?php //echo $value['id'] ?><!--</td>-->
               <td>
                   <div class="">
                       <?php if(in_array("idle-pdt/info-item", $node) || $this->params['is_administrator_user']):?>
                           <a class="blue hide-option notranslate" href="<?php echo Url::to(['info-item', 'id'=>$value['id']]) ?>" title='查看详情'>
                               查看详情
                           </a>
                       <?php endif;?>&nbsp;
                       <?php if(in_array("idle-pdt/update", $node) || $this->params['is_administrator_user']):?>
                           <a class="blue hide-option notranslate" href="<?php echo Url::to(['update', 'id'=>$value['id']]) ?>" title='编辑信息'>
                               编辑
                           </a>
                       <?php endif;?>&nbsp;
                       <?php if(in_array("idle-pdt/del", $node) || $this->params['is_administrator_user']):?>
                           <a class="red hide-option del notranslate" href="javascript:void(0)" rel='<?php echo $value['id'] ?>' title='删除'>
                               删除
                           </a>
                       <?php endif;?>
                   </div>
               </td>

               <td>
                   <?php if ($value['ip'] == null):?>
                       未配置
                   <?php else: ?>
                       <?php $ipRes =  json_decode($value['ip'],true)?>
                       <?php echo $ipRes['0']?> &nbsp;
                       <?php if(in_array("idle-pdt/info-item", $node) || $this->params['is_administrator_user']):?>
                           <?php if ( count($ipRes) > 1):?><a class="notranslate" href="<?php echo Url::to(['info-item', 'id'=>$value['id']]) ?>" style="font-size:12px">查看更多</a><?php endif;?>
                       <?php endif;?>
                   <?php endif;?>
               </td>
               <td>
                   <?php if( $value['intranet_id'] != ''):?>
                       <a class="blue" href="<?php echo Url::to(['intranet-ip','id'=>$value['intranet_id']])?>" ><?php echo $value['ipmi_ip']?></a>
                   <?php else:?>
                       <?php echo $value['ipmi_ip']?>
                   <?php endif;?>
               </td>
				<td><a class="blue" href="<?php echo Url::to(['info-item', 'id'=>$value['id']]) ?>"><?php echo $value['idle_name'] ?></a></td>
				<td>
				<?php $config = json_decode($value['config'], true);?>
				<?php echo $config['cpu'].' / '.$config['ram'].' / '.$config['hdd'].' / '.$config['configbandwidth'].' / '.$config['ipnumber'];?>
				</td>
               <td><?php echo $value['remarks']?></td>

				<td><?php echo $value['servertype'][0]['type_name']?></td>
				<td><?php echo $value['pdtroom'][0]['name']?></td>
				<td><?php echo $value['pdtcabinet'][0]['name']?></td>
				<td><?php echo $value['pdtmanage'][0]['name']?></td>
				<td><?php echo $value['machine_model']?></td>
				<td>
				<?php if ($value['attribute_id'] == 1 ):?>
				<span class='label label-primary'><?php echo $value['serverattribute'][0]['name']?></span>
				<?php elseif ($value['attribute_id'] == 2 ):?>
				<span class='label label-success'><?php echo $value['serverattribute'][0]['name']?></span>
				<?php elseif ($value['attribute_id'] == 3 ):?>
				<span class='label label-warning'><?php echo $value['serverattribute'][0]['name']?></span>
				<?php elseif ($value['attribute_id'] == 4 ):?>
				<span class='label label-purple'><?php echo $value['serverattribute'][0]['name']?></span>
				<?php elseif ($value['attribute_id'] == 5 ):?>
				<span class='label label-danger'><?php echo $value['serverattribute'][0]['name']?></span>
				<?php elseif ($value['attribute_id'] == 7 ):?>
				<span class='label label-pink'><?php echo $value['serverattribute'][0]['name']?></span>
				<?php endif;?>

				<?php if(in_array("idle-pdt/change-attributes", $node) || $this->params['is_administrator_user']):?>
					<?php if( $value['status'] == 0):?>
						&nbsp;&nbsp;<a class="blue" href="<?php echo Url::to(['change-attributes', 'id'=>$value['id']]) ?>" title="更改服务器状态属性" style="font-size:12px">更改</a>
					<?php endif;?>

				<?php endif;?>
				</td>
               <?php if (false):?>
				<td>
					<a href="javascript:;" title="更改状态" class="change_machine_status" idle_id="<?php echo $value['id'];?>">
					<?php if ($value['machine_status'] == '健康' ):?>
						<span class='label label-success'><?php echo $value['machine_status'];?></span>
					<?php elseif ($value['machine_status'] == '修理中' ):?>
						<span class='label label-purple'><?php echo $value['machine_status'];?></span>
					<?php elseif ($value['machine_status'] == '故障' ):?>
						<span class='label label-danger'><?php echo $value['machine_status'];?></span>
					<?php endif;?>
					</a>
				</td>
               <?php endif;?>
				<td>
					<?php if( $value['intranet_id'] == ''):?>
						<span class="label label-warning">否</span>
					<?php else:?>
						<span class="label label-success">是</span>
					<?php endif;?>
				</td>

				<td>
					<?php if( $value['servicerprovider'] == 0):?>
						自有
					<?php elseif ($value['servicerprovider'] == 1):?>
						<?php echo $value['provider'][0]['name']?>
					<?php endif;?>
				</td>

				<td>
					<?php if ($value['status'] == 1): ?>
						<span class='label label-success <?php if($value['useradmin']):?>server_tips<?php endif;?>' rel="<?php echo $value['useradmin'][0]['rename']?>"><?php echo '正常使用' ?></span>
					<?php elseif($value['status'] == -1): ?>
						<span class='label label-danger <?php if($value['useradmin']):?>server_tips<?php endif;?>' rel="<?php echo $value['useradmin'][0]['rename']?>"><?php echo '已 下 架' ?></span>
					<?php elseif($value['status'] == 0): ?>
						<span class='label label-primary <?php if($value['useradmin']):?>server_tips<?php endif;?>' rel="<?php echo $value['useradmin'][0]['rename']?>"><?php echo '闲 置 中' ?></span>
					<?php elseif($value['status'] == 2): ?>
						<span class='label label-warning <?php if($value['useradmin']):?>server_tips<?php endif;?>' rel="<?php echo $value['useradmin'][0]['rename']?>"><?php echo '待定' ?></span>
					<?php elseif($value['status'] == 3): ?>
						<span class='label label-warning <?php if($value['useradmin']):?>server_tips<?php endif;?>' rel="<?php echo $value['useradmin'][0]['rename']?>"><?php echo '新购机器占用中' ?></span>
					<?php elseif($value['status'] == 4): ?>
						<span class='label label-warning <?php if($value['useradmin']):?>server_tips<?php endif;?>' rel="<?php echo $value['useradmin'][0]['rename']?>"><?php echo '更换机器占用中' ?></span>
					<?php elseif($value['status'] == 5): ?>
						<span class='label label-warning <?php if($value['useradmin']):?>server_tips<?php endif;?>' rel="<?php echo $value['useradmin'][0]['rename']?>"><?php echo '测试机占用中' ?></span>
					<?php elseif($value['status'] == 6): ?>
						<span class='label label-warning <?php if($value['useradmin']):?>server_tips<?php endif;?>' rel="<?php echo $value['useradmin'][0]['rename']?>"><?php echo '更换IP中' ?></span>
					<?php endif ?>
				</td>
				<td>
					<?php if ($value['power_status'] == '开机'): ?>
						<span class='label label-success' style="background-color: #69AA46!important;"><?php echo '运行中' ?></span>
					<?php elseif($value['power_status'] == '关机'): ?>
						<span class='label label-danger'><?php echo '关 机' ?></span>
					<?php else: ?>
						<span class='label label-warning'><?php echo '未 知' ?></span>
					<?php endif ?>
				</td>
			</tr>
			<?php endforeach; ?>
		</tbody>

	</table>
	<div class="row">
		<div class="col-xs-6">
			<div class="dataTables_info" id="dynamic-table_info" role="status" aria-live="polite">共有<?php echo $iCount;?>条记录</div>
		</div>
		<div class="col-xs-6 pagination1" style="text-align: right;">
			<?php 
				echo LinkPager::widget([
					'pagination' => $page,
					'firstPageLabel'=>"首页",
					'prevPageLabel'=>'上一页',
					'nextPageLabel'=>'下一页',
					'lastPageLabel'=>'末页',
				]);
			?>
		</div>
	</div>
</form>
<style>
.ny-form .ny-control-label {float: left;line-height: 30px;}
.ny-control-label {width: 160px;padding-right: 0;text-align: right;color: #808080;}
.ny-form-control {display: inline-block;white-space: nowrap;color: #555;background-color: #fff;background-image: none;outline: none;}
.ny-number-container {float: left;line-height: 1;}
</style>
<!--更改机器状态-->
<div class="bootbox modal fade bootbox-prompt in" id="change_machinestatus_modal" tabindex="-1" role="dialog" style="padding-right: 17px;">
	<div class="modal-dialog">
		<div class="modal-content" style="margin-top: 200px;">
			<div class="modal-header">
				<button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">×</font>
					</font>
				</button>
				<h4 class="modal-title">
				<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">更改机器状态:</font></font></h4>
			</div>
			<div class="modal-body">
				<div class="bootbox-body">
					<div class="clearfix">
						<form id="change_machinestatus_Form" action="" class="bootbox-form form-horizontal ny-form ny-tab-container font-size-12 tab-group-undefined"> 
						<input type="hidden" name="idle_id" value="">
						<div class="form-group">
							<div class="ny-control-label">机器状态：</div>
							<div class="ny-form-control">
								<div class="ny-number-container">																	
									<select name="machine_status" id="select_machinestatus" class="col-xs-12">
										<option value="健康">健康</option>
										<option value="故障">故障</option>
										<option value="修理中">修理中</option>
									</select>
								</div>
							</div>
						</div>
						</form>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
					<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">关 闭</font></font>
				</button>
				<button class="btn btn-success" id="change_machinestatus_go" data-last="Finish">
					<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">确定提交</font></font>
				</button>
			</div>
		</div>
	</div>
</div>


<!-- inline scripts related to this page -->
<script type="text/javascript">
	$(document).on('mouseenter', '.server_tips', function(){
		var str = $(this).attr('rel');
		layer.tips(str, $(this), {
		  tips: [1, '##f89406'],
		  time: 2000
		});
	});
	$(".slide-btn").click(function() {
		var model = $(this).attr("targetModel");
		//基础信息
		if(model == 'machine_config') {
			if($(".machine_config").hasClass('modelClose')) {
				$(".machine_config").removeClass('modelClose').addClass('modelOpen');
				$(this).html('收起');
				$(".machine_config").attr("colspan", "2");
				$(".machine_config_sonmodel").fadeIn(200, function() {
					$(".machine_config_sonmodel").show();
				});
			} else {
				$(".machine_config").removeClass('modelOpen').addClass('modelClose');
				$(this).html('展开');
				$(".machine_config").attr("colspan", "1");
				$(".machine_config_sonmodel").fadeOut(200, function() {
					$(".machine_config_sonmodel").hide();
				});
			}
		} else if(model == 'machine_remark') {
			if($(".machine_remark").hasClass('modelClose')) {
				$(".machine_remark").removeClass('modelClose').addClass('modelOpen');
				$(this).html('收起');
				$(".machine_remark").attr("colspan", "2");
				$(".machine_remark_sonmodel").fadeIn(200, function() {
					$(".machine_remark_sonmodel").show();
				});
			} else {
				$(".machine_remark").removeClass('modelOpen').addClass('modelClose');
				$(this).html('展开');
				$(".machine_remark").attr("colspan", "1");
				$(".machine_remark_sonmodel").fadeOut(200, function() {
					$(".machine_remark_sonmodel").hide();
				});
			}
		}
	});
	
	//弹出一个tips层
	 $('.open-event').on('click', function(){
		var str = $(this).attr('rel');
		layer.tips(str, $(this), {
		  tips: [1, '#82af6f'],
		  time: 3000
		});
	}); 
	
	$("[name='room_id']").change(function(){
		var url = "<?php echo Url::to(['ajax-roomchange']) ?>";
		var id = $(this).find('option:selected').attr('rel');
		var load = layer.load(2);
		$.post(url, {id:id}, function(data){    		
			$("[name='cabinet_id']").empty();
			$("[name='cabinet_id']").append('<option value="">所属机柜</option>');  			
			$.each(data['data']['Cabinet'], function(key, val){
				$("[name='cabinet_id']").append('<option value="'+val['id']+'" rel="'+val['id']+'">'+val['name']+'</option>');
			});
			layer.closeAll('loading');
		},'json');                                                                                                                                                 
	});
	
	//服务器分类改变
	$("[name='server_type_id']").change(function(){
		var url = "<?php echo Url::to(['ajax-servertypechange']) ?>";
		var id = $(this).find('option:selected').attr('rel');
		var load = layer.load(2);
		$.post(url, {id:id}, function(data){
			$("[name='pdt_id']").empty();
			$("[name='pdt_id']").append('<option value="">产品配置类别</option>');
			$.each(data['data']['pdtmanage'], function(key, val) {
				$("[name='pdt_id']").append('<option value="'+val['id']+'" rel="'+val['id']+'">'+val['name']+'</option>');
			});
			layer.closeAll('loading');
		},'json');
	});
	
	//单选删除操作
	$('.del').click(function(){
		var _this = $(this);
		layer.confirm('确定要删除该服务器产品吗？',{icon:3,
			btn: ['确定','取消'] //按钮
		}, function(){
			var url = "<?php echo Url::to(['del']) ?>";
			layer.msg('操作中...', {icon:16, time:0});
		    $.post(url, {id:_this.attr('rel')}, function(data){
				if ( data['data']['status'] ) {
					layer.alert(data['data']['info'], {icon:1}, function(index){				   		  
						layer.msg('正在更新数据...', {icon:16, time:0});
						location.reload();
					}); 
				} else {
					layer.alert(data['data']['info'], {icon:7});
				}
			   },'json');
			}, function(){		   
		});
		return false;
	});					
	
	$('.change_machine_status').click(function() {
		var idle_id = $(this).attr('idle_id');
		$('input[name="idle_id"]').val(idle_id);
		$('#change_machinestatus_modal').modal('show');
	});
	
	$('#change_machinestatus_go').click(function() {		
		var idle_id = $(this).attr('idle_id');
		
		change_machinestatus_Form
		var url = "<?php echo Url::to(['idle-pdt/change-machinestatus']);?>";
		
		layer.confirm('确定要更改该机器状态吗？',{icon:3, btn: ['确定','取消']}, function() {

		    $.post(url, $('#change_machinestatus_Form').serialize(), function(data) {
				if ( data['data']['status'] ) {
					layer.alert(data['data']['info'], {icon:1}, function(index){				   		  
						layer.msg('正在更新数据...', {icon:16, time:0});
						location.reload();
					});
				} else {
					layer.alert(data['data']['info'], {icon:7});
				}
			},'json');
			   
		}, function(){
	
		});
		
		return false;
	});
	
</script>		
<?php $this->endBlock(); ?>
