<?php

namespace addons\VymCloud\api\controllers;

use addons\VymCloud\common\models\CloudServer;
use addons\VymServer\common\enums\ServerEnum;
use api\controllers\OnAuthController;
use common\helpers\ArrayHelper;
use common\helpers\ResultHelper;
use common\helpers\StringHelper;
use Exception;
use Yii;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * 云服务器管理API控制器
 *
 * Class ServerController
 * @package addons\VymCloud\api\controllers
 */
class ServerController extends OnAuthController
{
    public $modelClass = '';

    /**
     * 云服务器列表
     *
     * @return array
     */
    public function actionIndex()
    {
        try {
            $page          = max(1, (int)$this->get('page', 1));
            $pageSize      = max(1, min(100, (int)$this->get('pageSize', 20)));
            $ip            = trim($this->get('ip'));
            $name          = trim($this->get('name'));
            $status_server = $this->get('status_server');
            $status_power  = $this->get('status_power');
            $startTime     = $this->get('startTime');
            $endTime       = $this->get('endTime');

            // 构建查询
            $query = CloudServer::find()->with('cluster')->with('ips')
                                ->where(['uid' => $this->_uid])
                                ->orderBy('id DESC');

            // 搜索条件
            if ($ip) {
                $query->andWhere(['like', 'ip', $ip]);
            }
            if ($name) {
                $query->andWhere(['like', 'name', $name]);
            }
            if ($startTime) {
                $query->andWhere(['>=', 'created_at', $startTime . ' 00:00:00']);
            }
            if ($endTime) {
                $query->andWhere(['<=', 'created_at', $endTime . ' 23:59:59']);
            }

            // 状态筛选
            if ($status_server !== null && $status_server !== '') {
                $query->andWhere(['status_server' => $status_server]);
            }
            if ($status_power !== null && $status_power !== '') {
                $query->andWhere(['status_power' => $status_power]);
            }


            // 分页
            $totalNum = $query->count();
            $offset   = ($page - 1) * $pageSize;
            $items    = $query->offset($offset)->limit($pageSize)->all();

            // 计算统计数据
            $allQuery = CloudServer::find()->where(['uid' => $this->_uid]);
            $total    = $allQuery->count();

            $result = $allQuery->select(["status_server", "count(*) as num"])->groupBy("status_server")->asArray()->all();
            $result = ArrayHelper::map($result, "status_server", "num");

            $statistics = [
                'total'    => $total,
                'running'  => ArrayHelper::getValue($result, ServerEnum::SERVER_RUNNING, 0),
                'stopped'  => ArrayHelper::getValue($result, ServerEnum::SERVER_STOPPED, 0),
                'fault'    => ArrayHelper::getValue($result, ServerEnum::SERVER_FAULT, 0),
                'expiring' => 0, // 可以后续实现
            ];

            $res = [
                'items'      => $items,
                'page'       => $page,
                'pageSize'   => $pageSize,
                'total'      => $totalNum,
                'statistics' => $statistics,
            ];
            return ResultHelper::json(200, 'ok',$res);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 云服务器详情
     *
     * @return CloudServer|array
     */
    public function actionDetail()
    {
        try {
            $id = $this->get('id');
            if (!$id) {
                return ResultHelper::json(422, '服务器ID不能为空');
            }

            return Yii::$app->vymCloudService->server->findById($id);
        } catch (NotFoundHttpException $e) {
            return ResultHelper::json(404, $e->getMessage());
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    public function actionStatistics()
    {
        try {
            // 计算统计数据
            $allQuery = CloudServer::find()->where(['uid' => $this->_uid]);
            return [
                'total'    => $allQuery->count(),
                'running'  => $allQuery->andWhere(['status_server' => 2])->count(),
                'stopped'  => $allQuery->andWhere(['status_server' => 3])->count(),
                'fault'    => $allQuery->andWhere(['status_server' => 4])->count(),
                'expiring' => 0, // 可以后续实现
            ];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 远程连接控制台
     *
     * @return array
     */
    public function actionRemoteConnect()
    {
        try {
            $id = $this->post('id');
            if (!$id) {
                return ResultHelper::json(422, '服务器ID不能为空');
            }

            $model  = $this->findModel($id);
            $result = Yii::$app->vymCloudService->server->instanceRemoteControl($model->id);

            if ($result['status']) {
                $consoleUrl = '';
                if (isset($result['data']) && is_array($result['data']) && isset($result['data']['consoleUrl'])) {
                    $consoleUrl = $result['data']['consoleUrl'];
                }
                return ['consoleUrl' => $consoleUrl,];
            } else {
                return ResultHelper::json(422, $result['msg']);
            }
        } catch (NotFoundHttpException $e) {
            return ResultHelper::json(404, $e->getMessage());
        } catch (Exception $e) {
            return ResultHelper::json(500, $e->getMessage());
        }
    }

    /**
     * 电源操作（开机、关机、重启等）
     *
     * @return array
     */
    public function actionPowerAction()
    {
        try {
            $id     = $this->post('id');
            $action = $this->post('action');

            if (!$id) {
                return ResultHelper::json(422, '服务器ID不能为空');
            }
            if (!$action) {
                return ResultHelper::json(422, '操作类型不能为空');
            }

            // 验证操作类型
            $allowedActions = ['start', 'stop', 'restart', 'force_stop'];
            if (!in_array($action, $allowedActions)) {
                return ResultHelper::json(422, '不支持的操作类型');
            }

            $model   = $this->findModel($id);
            $isForce = $action === 'force_stop';
            $result  = Yii::$app->vymCloudService->server->instancePower($model->id, $action, $isForce);

            if ($result['status']) {
                return ['success' => true, 'message' => '操作执行成功',];
            } else {
                return ResultHelper::json(422, $result['msg']);
            }
        } catch (NotFoundHttpException $e) {
            return ResultHelper::json(404, $e->getMessage());
        } catch (Exception $e) {
            return ResultHelper::json(500, $e->getMessage());
        }
    }

    /**
     * 重置密码
     *
     * @return array
     */
    public function actionPassword()
    {
        try {
            $id       = $this->post('id');
            $password = $this->post('password');

            if (!$id) {
                return ResultHelper::json(422, '服务器ID不能为空');
            }

            $model = $this->findModel($id);

            // 如果没有提供密码，生成随机密码
            if (empty($password)) {
                $password = StringHelper::random(12);
            }

            $result = Yii::$app->vymCloudService->server->instanceResetPassword($model->id, $password);

            if ($result['status']) {
                return [
                    'success'  => true,
                    'message'  => '密码重置成功',
                    'password' => $password,
                ];
            } else {
                return ResultHelper::json(422, $result['msg']);
            }
        } catch (NotFoundHttpException $e) {
            return ResultHelper::json(404, $e->getMessage());
        } catch (Exception $e) {
            return ResultHelper::json(500, $e->getMessage());
        }
    }

    /**
     * 添加数据盘
     *
     * @return Response
     */
    public function actionAddDataDisk()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $id   = Yii::$app->request->post('id');
        $size = Yii::$app->request->post('size');

        if (!$id) {
            return ResultHelper::json(422, '服务器ID不能为空');
        }
        if (!$size || $size <= 0) {
            return ResultHelper::json(422, '数据盘大小不能为空或小于等于0');
        }

        try {
            $model = $this->findModel($id);

            // 构建磁盘配置参数
            $diskConfig = [
                'size' => $size . 'G',
                'type' => 'data',
            ];

            $result = Yii::$app->vymCloudService->server->instanceDiskDataAdd($model->id, $diskConfig);

            if ($result['status'] == 1) {
                return ResultHelper::json(200, '数据盘添加成功');
            } else {
                return ResultHelper::json(422, $result['msg']);
            }
        } catch (NotFoundHttpException $e) {
            return ResultHelper::json(404, $e->getMessage());
        } catch (Exception $e) {
            return ResultHelper::json(500, $e->getMessage());
        }
    }

    /**
     * 系统盘扩容
     *
     * @return Response
     */
    public function actionExpandSystemDisk()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $id   = Yii::$app->request->post('id');
        $size = Yii::$app->request->post('size');

        if (!$id) {
            return ResultHelper::json(422, '服务器ID不能为空');
        }
        if (!$size || $size <= 0) {
            return ResultHelper::json(422, '扩容大小不能为空或小于等于0');
        }

        try {
            $model  = $this->findModel($id);
            $result = Yii::$app->vymCloudService->server->instanceDiskOsExpand($model->id, $size);

            if ($result['status'] == 1) {
                return ResultHelper::json(200, '系统盘扩容成功');
            } else {
                return ResultHelper::json(422, $result['msg']);
            }
        } catch (NotFoundHttpException $e) {
            return ResultHelper::json(404, $e->getMessage());
        } catch (Exception $e) {
            return ResultHelper::json(500, $e->getMessage());
        }
    }

    /**
     * 数据盘扩容
     *
     * @return Response
     */
    public function actionExpandDataDisk()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $id   = Yii::$app->request->post('id');
        $size = Yii::$app->request->post('size');

        if (!$id) {
            return ResultHelper::json(422, '服务器ID不能为空');
        }
        if (!$size || $size <= 0) {
            return ResultHelper::json(422, '扩容大小不能为空或小于等于0');
        }

        try {
            $model = $this->findModel($id);

            // 数据盘扩容需要指定磁盘ID
            $diskId = Yii::$app->request->post('disk_id', 'scsi1'); // 默认第一个数据盘

            $result = Yii::$app->vymCloudService->server->instanceDiskDataExpand($model->id, $diskId, $size);

            if ($result['status'] == 1) {
                return ResultHelper::json(200, '数据盘扩容成功');
            } else {
                return ResultHelper::json(422, $result['msg']);
            }
        } catch (NotFoundHttpException $e) {
            return ResultHelper::json(404, $e->getMessage());
        } catch (Exception $e) {
            return ResultHelper::json(500, $e->getMessage());
        }
    }

    /**
     * 增购IP
     *
     * @return Response
     */
    public function actionPurchaseIp()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $id = Yii::$app->request->post('id');
        if (!$id) {
            return ResultHelper::json(422, '服务器ID不能为空');
        }

        try {
            $model = $this->findModel($id);
            // todo 这里应该创建IP购买订单或跳转到购买页面
            return ResultHelper::json(200, 'IP购买功能开发中，请联系客服');
        } catch (NotFoundHttpException $e) {
            return ResultHelper::json(404, $e->getMessage());
        } catch (Exception $e) {
            return ResultHelper::json(500, $e->getMessage());
        }
    }

    /**
     * 升级带宽
     *
     * @return Response
     */
    public function actionUpgradeBandwidth()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $id        = Yii::$app->request->post('id');
        $bandwidth = Yii::$app->request->post('bandwidth');

        if (!$id) {
            return ResultHelper::json(422, '服务器ID不能为空');
        }
        if (!$bandwidth || $bandwidth <= 0) {
            return ResultHelper::json(422, '带宽值不能为空或小于等于0');
        }

        try {
            $model = $this->findModel($id);
            // todo 这里应该创建带宽升级订单
            return ResultHelper::json(200, '带宽升级功能开发中，请联系客服');
        } catch (NotFoundHttpException $e) {
            return ResultHelper::json(404, $e->getMessage());
        } catch (Exception $e) {
            return ResultHelper::json(500, $e->getMessage());
        }
    }

    /**
     * 升级配置
     *
     * @return Response
     */
    public function actionUpgradeConfig()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $id     = Yii::$app->request->post('id');
        $cpu    = Yii::$app->request->post('cpu');
        $memory = Yii::$app->request->post('memory');

        if (!$id) {
            return ResultHelper::json(422, '服务器ID不能为空');
        }
        if (!$cpu && !$memory) {
            return ResultHelper::json(422, 'CPU或内存配置不能都为空');
        }

        try {
            $model = $this->findModel($id);
            // todo 这里应该创建配置升级订单
            return ResultHelper::json(200, '配置升级功能开发中，请联系客服');
        } catch (NotFoundHttpException $e) {
            return ResultHelper::json(404, $e->getMessage());
        } catch (Exception $e) {
            return ResultHelper::json(500, $e->getMessage());
        }
    }

    /**
     * 手动续费
     *
     * @return Response
     */
    public function actionManualRenew()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $id     = Yii::$app->request->post('id');
        $months = Yii::$app->request->post('months');

        if (!$id) {
            return ResultHelper::json(422, '服务器ID不能为空');
        }
        if (!$months || $months <= 0) {
            return ResultHelper::json(422, '续费月数不能为空或小于等于0');
        }

        try {
            $model = $this->findModel($id);
            // todo 这里应该创建续费订单
            return ResultHelper::json(200, '续费功能开发中，请联系客服');
        } catch (NotFoundHttpException $e) {
            return ResultHelper::json(404, $e->getMessage());
        } catch (Exception $e) {
            return ResultHelper::json(500, $e->getMessage());
        }
    }

    /**
     * 重装系统
     *
     * @return array
     */
    public function actionReinstall()
    {
        try {
            $id       = $this->post('id');
            $systemId = $this->post('system_id');

            if (!$id) {
                return ResultHelper::json(422, '服务器ID不能为空');
            }
            if (!$systemId) {
                return ResultHelper::json(422, '系统模板ID不能为空');
            }

            $model  = $this->findModel($id);
            $result = Yii::$app->vymCloudService->server->instanceReinstall($model->id, $systemId);
            if ($result['status']) {
                return ['success' => true, 'message' => '系统重装任务已提交',];
            } else {
                return ResultHelper::json(422, $result['msg']);
            }
        } catch (NotFoundHttpException $e) {
            return ResultHelper::json(404, $e->getMessage());
        } catch (Exception $e) {
            return ResultHelper::json(500, $e->getMessage());
        }
    }

    /**
     * 查找模型
     *
     * @param int $id
     *
     * @return CloudServer
     * @throws NotFoundHttpException
     */
    protected function findModel($id)
    {
        if (($model = CloudServer::findOne(['id' => $id, 'uid' => $this->_uid])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('云服务器不存在或无权限访问');
    }

}