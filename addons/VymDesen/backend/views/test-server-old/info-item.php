<?php

use yii\helpers\Url;
$this->title = '控制台管理 - 测试服务器管理 - 详情 - '.$arrRes['idle_name'];
use addons\VymDesen\common\components\DataHelper;
?>
<?php $this->beginBlock('content') ?>
<?php $node = \Yii::$app->session['auth']['node'];?>
<div class="row">
	<div class="col-xs-12">		
		<div class="clearfix">
			<div class="pull-right tableTools-container"></div>
		</div>
		<!-- div.table-responsive -->
		<!-- div.dataTables_borderWrap -->
		<div>
			<div class="col-sm-10 col-sm-offset-1">
				<!-- #section:pages/invoice -->
				<div class="widget-box transparent">
				   <div class="widget-header widget-header-large">
						<h3 class="widget-title grey lighter">
							<i class="ace-icon fa fa-leaf green"></i>
							服务器详情
						</h3>
					</div>
					<div class="widget-body">
						<div class="widget-main padding-24">
							<div class="row">
								<div class="col-sm-6">
									<div class="row">
										<div class="col-xs-11 label label-lg label-info arrowed-in arrowed-right">
											<b>配置信息</b>
										</div>
									</div>
									<div>
										<ul class="list-unstyled spaced">
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>产品配置类别：
												<b class="red"><?php echo $arrRes['pdtmanage'][0]['name'];?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>CPU：
												<b class="red"><?php echo $config['cpu']?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>内存大小：
												<b class="red"><?php echo $config['ram']?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>硬盘：
												<b class="red"><?php echo $config['hdd']?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>带宽：
												<b class="red"><?php echo $config['configbandwidth']?></b>											
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>IP可用数：
												<b class="red"><?php echo $config['ipnumber']?></b>
											</li>
											
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>防御流量：
												<b class="red"><?php if (!empty($config['defense'])):?><?php echo $config['defense']?><?php endif;?></b>
											</li>
											
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>操作系统：
												<b class="red"><?php echo $config['operatsystem']?></b>
											</li>
										</ul>
									</div>
								</div><!-- /.col -->

								<div class="col-sm-6">
									<div class="row">
										<div class="col-xs-11 label label-lg label-success arrowed-in arrowed-right">
											<b>服务器信息</b>
										</div>
									</div>
									<div>
										<ul class="list-unstyled  spaced">									
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>服务器提供者：
												<b class="red"><?php echo $arrRes['servicerprovider'] == 0 ? "自有":"供应商"?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>服务器分类：
												<b class="red"><?php echo $arrRes['servertype'][0]['type_name']?></b>
											</li>
											<?php if(!empty($arrRes['provider']) ):?>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>供应商
												<b class="red"><?php echo $arrRes['provider'][0]['name']?></b>
											</li>
											<?php endif;?>										
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>机房：
												<b class="red"><?php if(!empty($arrRes['pdtroom']) ):?><?php echo $arrRes['pdtroom'][0]['name']?><?php endif;?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>机柜：
												<b class="red"><?php if(!empty($arrRes['pdtcabinet']) ):?><?php echo $arrRes['pdtcabinet'][0]['name']?> <?php endif;?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>机位：
												<b class="red"><?php echo $arrRes['occupies_position']?></b>
											</li>
										</ul>
									</div>
								</div><!-- /.col -->
							</div><!-- /.row -->
							<div class="row">
								<div class="col-sm-6">
									<div class="row">
										<div class="col-xs-11 label label-lg label-info arrowed-in arrowed-right">
											<b>IPMI与交换机信息</b>
										</div>
									</div>
									<div>
										<ul class="list-unstyled spaced">
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>IPMI地址：
												<b class="red"><?php echo $arrRes['ipmi_ip']?></b>											
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>IPMI用户名：
												<b class="red"><?php echo $arrRes['ipmi_name']?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>IPMI密码：
												<b class="red"><?php echo $arrRes['ipmi_pwd']?></b>
											</li>
											<?php if(!empty($arrRes['switch'][0])):?>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>交换机地址：
												<b class="red"><?php echo $arrRes['switch'][0]['ip']?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>交换机端口：
												<b class="red"><?php echo $arrRes['switch_port']?></b>											
											</li>
											<?php endif;?>
										</ul>
									</div>
								</div><!-- /.col -->
								<div class="col-sm-6">
									<div class="row">
										<div class="col-xs-11 label label-lg label-success arrowed-in arrowed-right">
											<b>产品IP地址与初始账户</b>
										</div>
									</div>
									<div>
										<ul class="list-unstyled  spaced">
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>IP地址：
												<div style="MARGIN-LEFT: 12%;">
													<b class="red" style="text-indent:2em">
														  <?php $ipRes =  json_decode($arrRes['ip'],true)?>
														  <?php if ($ipRes == null):?>
														  <?php else: ?>                      							     
															 <?php foreach ($ipRes as $key=>$ip):?>
															 <?php if ($key == "6"):?><?php break;?><?php endif;?><?php echo $ip."<br/>";?><?php endforeach;?>
															 <?php if (count($ipRes) > 4):?><a class="blue" href="javascript:void(0);" id="ipRes">查看更多</a><?php endif;?>
														  <?php endif;?>            							     												
													</b>
												</div>												
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>初始账户名：
												<b class="red"><?php echo $arrRes['account_name']?></b>
											</li>	
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>初始账户密码：
												<b class="red"><?php echo $arrRes['account_pwd']?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>初始账户端口：
												<b class="red"><?php echo $arrRes['account_port']?></b>
											</li>										
										</ul>
									</div>
								</div><!-- /.col -->
							</div><!-- /.row --> 
							<div class="space"></div>							
							<div class="well">	
								<div>备注：<?php echo $arrRes['server_remarks']?>  &nbsp;</div>
							</div>
							<div class="space-6"></div>
							<div class="row">								
								<div class="col-sm-7 pull-left">
									<button class="btn btn-info btn-sm" style="width:120px" onclick="location.href='<?php echo Url::to(['index'])?>'">
										<i class="ace-icon fa fa-reply icon-only"></i>&nbsp;返回列表
									</button>&nbsp;
									<button class="btn btn-danger btn-sm" style="width:120px" onclick="window.history.back()">
										<i class="ace-icon fa fa-reply icon-only"></i>&nbsp;返回上一页
									</button>
								</div>
							</div>
							<div class="hr hr8 hr-double hr-dotted"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>	

<!--更多IP -->
<div class="bootbox modal fade bootbox-prompt in" id="ipshow" tabindex="-1" role="dialog" style="padding-right: 17px;">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">×</font>
					</font>
				</button>
				<h4 class="modal-title">
				<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">IP地址:</font></font></h4>
			</div>
			<div class="modal-body">
				<div class="bootbox-body">                     
					<div class="clearfix">
						<ul class="list-unstyled  spaced">
						<?php $ipRes =  json_decode($arrRes['ip'],true)?>
						  <?php if ($ipRes == null):?>
						  <?php else: ?>                      							     
							 <?php foreach ($ipRes as $key=>$ip):?>
							 <li style="float: left;width:33%"><b class="red" style="text-indent:2em"><?php echo $ip;?></b></li>
							 <?php endforeach;?>
						  <?php endif;?> 		
						 </ul>
					</div>  
				</div>
			</div>
			<div class="modal-footer">
				<button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">关闭</font></font>
				</button>
			</div>
		</div>
	</div>
</div>
<!-- 更多IP end -->

<!--修改备注 -->
<div class="bootbox modal fade bootbox-prompt in" id="modify_remarks_show" tabindex="-1" role="dialog" style="padding-right: 17px;">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">×</font>
					</font>
				</button>
				<h4 class="modal-title">
				<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">修改备注:</font></font></h4>
			</div>
			<div class="modal-body">
				<div class="bootbox-body">    
					<div class="clearfix">
						<div class="form-group">	
							<div class="col-xs-12 col-sm-12">
								<textarea class="col-xs-12 col-sm-12" rows="6" id="server_remarks" name="server_remarks"><?php echo $arrRes['server_remarks']?></textarea>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
					<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">关 闭</font></font>
				</button>
				<button class="btn btn-success" id="modify_remarks_go" data-last="Finish">
					<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">确 定</font></font>
				</button>
			</div>
		</div>
	</div>
</div>
<!-- 修改备注 End -->

<!-- inline scripts related to this page -->
<script>
	var id = <?php echo $arrRes['id']?>;
	$('#ipRes').click(function(){
		$('#ipshow').modal('show');
		return ;
	});
	$('#modify_remarks').click(function(){
		$('#modify_remarks_show').modal('show');
		return;
	});
	$('#modify_remarks_go').click(function(){
		layer.msg('正在更新备注...', {icon:16, time:0});
		url = "<?php echo Url::to(['modify-remarks']); ?>";
		$.post(url, {id:id,server_remarks:$("[name='server_remarks']").val()}, function(data){
			if ( data['data']['status'] ) {
				layer.alert(data['data']['info'], {icon:1}, function(index){				   		  
		   			layer.msg('正在更新数据...', {icon:16, time:0});
			   		location.reload();
			   	});
			} else {
				layer.alert(data['data']['info'], {icon:7});
			}
		},'json');
	});
</script>
</body>
<?php $this->endBlock(); ?>
