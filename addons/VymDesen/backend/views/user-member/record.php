<?php

use yii\helpers\Url;
use addons\VymDesen\common\components\DataHelper;
use yii\helpers\ArrayHelper;
$this->title = '系统设置-用户记录';
?>
<?php $this->beginBlock('content') ?>
<div class="clearfix">
    <div class="pull-right tableTools-container"></div>
</div>
<form class="form-horizontal" action="" method='post' role="form" onSubmit="submit_load('正在提交...');">
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 用户ID </label>
        <div class="col-sm-9">
            <input readonly="readonly" type="text" name="member_id" value='<?php echo $arrRes['record']['u_id'] ?>' class="col-xs-10 col-sm-5" >
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 用户名 </label>
        <div class="col-sm-9">
            <input readonly="readonly" type="text" name="member_name" value='<?php echo $arrRes['record']['uname'] ?>' class="col-xs-10 col-sm-5" >
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 用户邮箱 </label>
        <div class="col-sm-9">
            <input readonly="readonly" type="text" name="member_email" value='<?php echo $arrRes['record']['email'] ?>' class="col-xs-10 col-sm-5" >
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 用户手机 </label>
        <div class="col-sm-9">
            <input readonly="readonly" type="text" name="member_mobile"  value='<?php echo $arrRes['record']['mobile_area'] . $arrRes['record']['mobile']?>'  class="col-xs-10 col-sm-5" >
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 项目</label>
        <div class="col-sm-9">
            <textarea class="form-control col-xs-10 col-sm-5 cn" id="form-field-8" name='items'><?php echo ArrayHelper::getValue($arrRes, 'items'); ?></textarea>
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 特殊处理</label>
        <div class="col-sm-9">
            <textarea class="form-control col-xs-10 col-sm-5 cn" id="form-field-8" name='special_handling'><?php echo ArrayHelper::getValue($arrRes, 'special_handling'); ?></textarea>
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 用户特征</label>
        <div class="col-sm-9">
            <textarea class="form-control col-xs-10 col-sm-5 cn" id="form-field-8" name='features'><?php echo ArrayHelper::getValue($arrRes, 'features'); ?></textarea>
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 服务器请求时间段</label>
        <div class="col-sm-9">
            <textarea class="form-control col-xs-10 col-sm-5 cn" id="form-field-8" name='service_period'><?php echo ArrayHelper::getValue($arrRes, 'service_period'); ?></textarea>
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 折扣 </label>
        <div class="col-sm-9">
            <input type="text" name="discount" value='<?php echo ArrayHelper::getValue($arrRes, 'discount'); ?>'  class="col-xs-10 col-sm-5" >
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 付款方式 </label>
        <div class="col-sm-9">
            <input type="text" name="payment_method" value='<?php echo  ArrayHelper::getValue($arrRes, 'payment_method'); ?>'  class="col-xs-10 col-sm-5" >
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 常需要 </label>
        <div class="col-sm-9">
            <input type="text" name="server_mode" value='<?php echo ArrayHelper::getValue($arrRes, 'server_mode'); ?>'  class="col-xs-10 col-sm-5" >
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 售价 </label>
        <div class="col-sm-9">
            <input type="text" name="selling_price" value='<?php echo ArrayHelper::getValue($arrRes, 'selling_price'); ?>'  class="col-xs-10 col-sm-5" >
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 客戶的未完成报价</label>
        <div class="col-sm-9">
            <textarea class="form-control col-xs-10 col-sm-5 cn" id="form-field-8" name='incomplete_price'><?php echo ArrayHelper::getValue($arrRes, 'incomplete_price'); ?></textarea>
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 欠款 </label>
        <div class="col-sm-9">
            <input type="text" name="arrears" value='<?php echo ArrayHelper::getValue($arrRes, 'arrears'); ?>'  class="col-xs-10 col-sm-5" >
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 服务器数量 </label>
        <div class="col-sm-9">
            <input type="text" name="servers_num" value='<?php echo ArrayHelper::getValue($arrRes, 'servers_num'); ?>'  class="col-xs-10 col-sm-5" >
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 近期报价对话</label>
        <div class="col-sm-9">
            <textarea class="form-control col-xs-10 col-sm-5 cn" id="form-field-8" name='conversations'><?php echo ArrayHelper::getValue($arrRes, 'conversations'); ?></textarea>
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-padding-right"> 售后群 </label>
        <div class="col-sm-9">
            <input type="text" name="sales_group" value='<?php echo ArrayHelper::getValue($arrRes, 'conversations'); ?>'  class="col-xs-10 col-sm-5" >
            <span class="middle" style="color:red;margin-left:5px"></span>
        </div>
    </div>
    <div class="clearfix form-actions">
        <div class="col-md-offset-3 col-md-9">
            <button id="go" class="btn btn-info" type="submit">
                <i class="ace-icon fa fa-check bigger-110"></i>
                提交信息
            </button>
            &nbsp; &nbsp; &nbsp;
            <button class="btn" type="reset">
                <i class="ace-icon fa fa-undo bigger-110"></i>
                还原内容
            </button>
            &nbsp; &nbsp; &nbsp;
            <button class="btn btn-danger" type="button" style="width:120px" onclick="window.history.back()">
                <i class="ace-icon fa fa-reply icon-only"></i>
                返回上一页
            </button>

        </div>
    </div>
    <div class="hr hr-24"></div>
</form>

<script>
    $('#go').click(function(){
        var url = "<?php echo Url::to(['record']); ?>";
        var _this = $(this);
        var load = layer.load(2);
        $('.middle').html("");
        $.post(url, $('form').serialize(), function(data){
            layer.closeAll('loading');
            if ( data['data']['status'] )
            {
                layer.alert(data['data']['info'], {icon:1}, function(index){
                    window.location.href='<?php echo Url::to(["user-member/index"])?>';
                });
            }
            else
            {
                if ( typeof data['data']['info'] === 'string' )
                {
                    layer.alert(data['data']['info'], {icon:7});
                    return ;
                }
                $.each(data['data']['info'], function(key, val){

                    layer.alert('用户信息填写有误', {icon:7});

                    $("[name='"+key+"']").next('span').html('<i class="ace-icon fa fa-exclamation-circle bigger-110">'+val[0]+'</i>');

                });
            }
        },'json');
        return false;
    });
</script>
<?php $this->endBlock(); ?>
