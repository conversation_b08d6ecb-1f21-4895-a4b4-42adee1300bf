<?php

use addons\VymServer\common\enums\ProductEnum;
use kartik\select2\Select2;
use yii\widgets\ActiveForm;
use addons\VymCloud\common\models\CloudProduct;

/** @var yii\web\View $this */
/** @var addons\VymCloud\common\models\CloudProduct $model */
/** @var $clusterList */

$this->title                   = '创建业务产品';
$this->params['breadcrumbs'][] = ['label' => '业务产品', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="cloud-product-create">
    <div class="box-body">
        <?php $form = ActiveForm::begin(); ?>
        <div class="nav-tabs-custom">
            <ul class="nav nav-tabs" id="myTab">
                <li class="active"><a href="#tab_1" data-toggle="tab" class="no-loading">基本信息</a></li>
                <li><a href="#tab_2" data-toggle="tab" class="no-loading">规格配置</a></li>
                <li><a href="#tab_3" data-toggle="tab" class="no-loading">性能配置</a></li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane active" id="tab_1">

                    <div class="row">
                        <div class="col-sm-3">
                            <?= $form->field($model, 'title')->textInput(['maxlength' => true]) ?>
                        </div>
                        <div class="col-sm-3">
                            <?php
                                if ($model->cluster_ids) {
                                    $model->cluster_ids = explode(',', $model->cluster_ids);
                                }
                            ?>
                            <?= $form->field($model, 'cluster_ids')->widget(Select2::class, [
                                'data'          => $clusterList,
                                'options'       => ['multiple' => true, 'placeholder' => '请选择集群绑定'],
                                'pluginOptions' => ['allowClear' => true],
                            ])->hint('集群绑定后,该业务产品将在该集群可用') ?>
                        </div>
                        <div class="col-sm-2">
                            <?= $form->field($model, 'stock')->textInput() ?>
                        </div>
                        <div class="col-sm-2">
                            <?= $form->field($model, 'sales')->textInput() ?>
                        </div>
                        <div class="col-sm-2">
                            <?= $form->field($model, 'product_st')->radioList(CloudProduct::getStatusMap(), [
                                'value' => $model->product_st ?? CloudProduct::STATUS_ENABLED,
                            ]) ?>
                        </div>
                        <div class="col-sm-2">
                            <?= $form->field($model, 'recommend')->radioList(ProductEnum::getRecommendMap(), [
                                'value' => $model->recommend ?? ProductEnum::RECOMMEND_Y,
                            ]) ?>
                        </div>
                    </div>

                    <?= $form->field($model, 'desc')->textarea() ?>

                    <?= $this->render('_spec/price', [
                        'form'  => $form,
                        'model' => $model,
                    ]) ?>
                </div>
                <div class="tab-pane" id="tab_2">
                    <?= $this->render('_spec/cpu', [
                        'form'  => $form,
                        'model' => $model,
                    ]) ?>

                    <?= $this->render('_spec/memory', [
                        'form'  => $form,
                        'model' => $model,
                    ]) ?>

                    <?= $this->render('_spec/disk', [
                        'form'  => $form,
                        'model' => $model,
                    ]) ?>

                    <?= $this->render('_spec/bandwidth', [
                        'form'  => $form,
                        'model' => $model,
                    ]) ?>

                    <?= $this->render('_spec/other', [
                        'form'  => $form,
                        'model' => $model,
                    ]) ?>
                </div>
                <div class="tab-pane" id="tab_3">
                    <?= $this->render('_spec/options', [
                        'form'  => $form,
                        'model' => $model,
                    ]) ?>
                </div>
            </div>
        </div>
        <div class="box-footer text-center">
            <button class="btn btn-primary" type="submit">保存</button>
            <span class="btn btn-white" onclick="history.go(-1)">返回</span>
        </div>
        <?php ActiveForm::end(); ?>
    </div>
</div>

<script>
    $('#myTab li a').on('click', function (event) {
        //移除原先的active
        $('#myTab li').removeClass('active');
        //添加当前的active
        $(this).parent().addClass('active');
        event.preventDefault()
        $(this).tab('show')
    })
</script>