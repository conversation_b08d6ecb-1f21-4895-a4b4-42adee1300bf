<?php

namespace addons\VymCloud\merchant\controllers;

use addons\VymCloud\common\enums\OrderEnum;
use addons\VymCloud\common\models\CloudOrder;
use addons\VymCloud\common\models\CloudServer;
use common\helpers\ArrayHelper;
use common\models\base\SearchModel;
use common\models\member\Member;
use Exception;
use Yii;
use yii\filters\VerbFilter;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * OrderController implements the CRUD actions for CloudOrder model.
 */
class OrderController extends BaseController
{


    /**
     * @var CloudOrder
     */
    public $modelClass = CloudOrder::class;

    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class'   => VerbFilter::className(),
                    'actions' => [
                        'delete'      => ['POST'],
                        'balance-pay' => ['POST'],
                        'refund'      => ['POST'],
                    ],
                ],
            ]
        );
    }

    /**
     * Lists all CloudOrder models.
     *
     * @return string
     */
    public function actionIndex()
    {
        $searchModel = new SearchModel([
            'model'                  => $this->modelClass,
            'scenario'               => 'default',
            'relations'              => ['member' => []], // 关联memebr 表的所有字段
            'partialMatchAttributes' => ['title'], // 模糊查询
            'defaultOrder'           => [
                'id' => SORT_DESC,
            ],
            'pageSize'               => $this->pageSize,
        ]);

        $dataProvider = $searchModel->search($this->request->queryParams);
        $this->addQueryParam($dataProvider, 's');
        return $this->render('index', [
            'dataProvider' => $dataProvider,
            'searchModel'  => $searchModel,
        ]);
    }

    /**
     * Displays a single CloudOrder model.
     *
     * @param int $id ID
     *
     * @return string
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * 余额支付
     *
     * @param int $id 订单ID
     *
     * @return Response
     * @throws NotFoundHttpException
     */
    public function actionBalancePay($id)
    {
        $order = $this->findModel($id);
        try {
            // 验证订单
            if (!$order) {
                throw new Exception('订单不存在');
            }
            if (ArrayHelper::getValue($order, 'order_st') == OrderEnum::ORDER_STATUS_CANCELLED) {
                throw new Exception('订单已取消');
            }
            if (ArrayHelper::getValue($order, 'order_st') == OrderEnum::ORDER_STATUS_PAID) {
                throw new Exception('订单已支付', 1);
            }
            /** @var CloudOrder $order */
            // 检查用户余额是否足够
            $paymentMethod = "balance_cloud";
            $member        = Member::find()->with('userMember')->where(['id' => $order->uid])->one();
            // 构建支付数据
            $payData = [
                'userId'      => ArrayHelper::getValue($member, 'userMember.u_id'),
                'orderNo'     => $order->order_no,
                'payment'     => $paymentMethod,
                'payAmount'   => ArrayHelper::getValue($order, 'amount_pay'),
                'productName' => ArrayHelper::getValue($order, 'server_name', '购买云服务器'),
            ];
            $result  = Yii::$app->vymCloudService->payment->getPayUrl($paymentMethod, $payData);
            $message = ArrayHelper::getValue($result, 'message');
            $success = ArrayHelper::getValue($result, 'success');
            // 调用订单服务进行余额支付
            // $result = Yii::$app->vymCloudService->order->balancePay($id, [
            //     'uid'    => $model->uid,
            //     'amount' => $model->amount_pay,
            // ]);
            if (!ArrayHelper::getValue($result, 'success')) {
                throw new Exception($message);
            }
            if ($success) {
                Yii::$app->session->setFlash('success', '余额支付成功');
            } else {
                Yii::$app->session->setFlash('error', $message ?? '余额支付失败');
            }

        } catch (Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
        }

        return $this->redirect(['view', 'id' => $id]);
    }

    /**
     * 在线支付
     *
     * @param int $id 订单ID
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionOnlinePay($id)
    {
        $model = $this->findModel($id);

        try {
            // 检查订单状态
            if ($model->order_st != OrderEnum::ORDER_STATUS_PENDING) {
                throw new Exception('订单当前状态不允许支付');
            }

            // 获取支付方式列表
            $paymentMethods = Yii::$app->vymCloudService->payment->findAll($model->uid, $model->amount_pay);

            return $this->render('online-pay', [
                'model'          => $model,
                'paymentMethods' => $paymentMethods,
            ]);

        } catch (Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
            return $this->redirect(['view', 'id' => $id]);
        }
    }

    /**
     * 跳转到支付
     *
     * @param int    $id      订单ID
     * @param string $payment 支付方式
     *
     * @return Response
     * @throws NotFoundHttpException
     */
    public function actionPaymentJump($id, $payment = null)
    {
        $model = $this->findModel($id);

        // 如果payment参数在URL路径中，尝试从路径中获取
        if (!$payment) {
            $pathInfo = Yii::$app->request->pathInfo;
            $segments = explode('/', $pathInfo);
            $payment  = end($segments); // 获取最后一段作为支付方式
        }

        try {
            // 检查订单状态
            if ($model->order_st != OrderEnum::ORDER_STATUS_PENDING) {
                throw new Exception('订单当前状态不允许支付');
            }

            // 构造支付数据
            $payData = [
                'userId'      => $model->uid,
                'orderNo'     => $model->order_no,
                'payAmount'   => $model->amount_pay,
                'payment'     => $payment,
                'productName' => $model->server_detail['server_name'] ?? $model->server_detail['title'] ?? '云服务器',
            ];

            // 获取支付链接
            $payUrlResult = Yii::$app->vymCloudService->payment->getPayUrl($payment, $payData);

            if (!$payUrlResult['success']) {
                throw new Exception($payUrlResult['message'] ?? '支付链接获取失败');
            }

            $redirectUrl = $payUrlResult['payUrl'];
            return $this->redirect($redirectUrl);

        } catch (Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
            return $this->redirect(['view', 'id' => $id]);
        }
    }

    /**
     * 查看业务实例详情
     *
     * @param int $id 订单ID
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionServerDetail($id)
    {
        $model = $this->findModel($id);

        if (!$model->server_id) {
            Yii::$app->session->setFlash('error', '该订单未创建业务实例');
            return $this->redirect(['view', 'id' => $id]);
        }

        $server = CloudServer::findOne($model->server_id);
        if (!$server) {
            Yii::$app->session->setFlash('error', '业务实例不存在');
            return $this->redirect(['view', 'id' => $id]);
        }

        return $this->render('server-detail', [
            'model'  => $model,
            'server' => $server,
        ]);
    }

    /**
     * 申请退款
     *
     * @param int $id 订单ID
     *
     * @return Response
     * @throws NotFoundHttpException
     */
    public function actionRefund($id)
    {
        $model = $this->findModel($id);

        try {
            // 检查订单状态
            if (!in_array($model->order_st, [OrderEnum::ORDER_STATUS_PAID, OrderEnum::ORDER_STATUS_COMPLETED])) {
                throw new Exception('订单当前状态不允许退款');
            }

            if ($model->order_st == OrderEnum::ORDER_STATUS_REFUND) {
                throw new Exception('订单已退款');
            }

            // 调用订单服务进行退款
            $result = Yii::$app->vymCloudService->order->refundOrder($id);

            if ($result['code'] == 1) {
                Yii::$app->session->setFlash('success', '退款申请已提交，请等待处理');
            } else {
                Yii::$app->session->setFlash('error', $result['msg'] ?? '退款申请失败');
            }

        } catch (Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
        }

        return $this->redirect(['view', 'id' => $id]);
    }

    /**
     * Deletes an existing CloudOrder model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param int $id ID
     *
     * @return Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);

        try {
            // 只允许删除待支付或失败的订单
            if (!in_array($model->order_st, [OrderEnum::ORDER_STATUS_PENDING, OrderEnum::ORDER_STATUS_FAILED, OrderEnum::ORDER_STATUS_CANCELLED])) {
                throw new Exception('该订单状态不允许删除');
            }

            $model->delete();
            Yii::$app->session->setFlash('success', '订单删除成功');

        } catch (Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
        }

        return $this->redirect(['index']);
    }

    /**
     * 支付订单
     *
     * @param int $id
     *
     * @return string|Response
     */
    public function actionPay($id)
    {
        try {
            $order         = $this->findModel($id, true);
            $paymentMethod = Yii::$app->request->post('payment_method', 'alipay');

            $result = Yii::$app->vymCloudService->order->processPayment($id, $paymentMethod);

            if ($result['success']) {
                if (!empty($result['payment_url'])) {
                    return $this->redirect($result['payment_url']);
                } else {
                    Yii::$app->session->setFlash('success', '支付成功');
                    return $this->redirect(['view', 'id' => $id]);
                }
            } else {
                Yii::$app->session->setFlash('error', $result['message'] ?? '支付失败');
                return $this->redirect(['view', 'id' => $id]);
            }
        } catch (\Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
            return $this->redirect(['index']);
        }
    }

    /**
     * 取消订单
     *
     * @param int $id
     *
     * @return Response
     */
    public function actionCancel($id)
    {
        try {
            $order = $this->findModel($id, true);

            if ($order->order_st != 0) {
                Yii::$app->session->setFlash('error', '只能取消待支付的订单');
                return $this->redirect(['view', 'id' => $id]);
            }

            $result = Yii::$app->vymCloudService->order->cancelOrder($id);

            if ($result['success']) {
                Yii::$app->session->setFlash('success', '订单已取消');
            } else {
                Yii::$app->session->setFlash('error', $result['message'] ?? '订单取消失败');
            }

            return $this->redirect(['view', 'id' => $id]);
        } catch (\Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
            return $this->redirect(['index']);
        }
    }
}
