<?php

$this->title = '控制台管理-订单管理-订单详情';

use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\helpers\Html;
use addons\VymDesen\common\components\DataHelper;

$TradeMainRes = isset($TradeMainRes) ? $TradeMainRes : [];
$trade_oid    = ArrayHelper::getValue($TradeMainRes, 'trade_orderid');
?>
<?php $this->beginBlock('content') ?>
<?php $node = \Yii::$app->session['auth']['node'];?>
<style type="text/css">
	*{margin:0;padding:0;}
	ul,li{list-style:none;margin:0;padding:0;}
	a{text-decoration:none;}
	.clearfix{clear:both;}

	.info-title{height:40px;width:100%;line-height:50px;font-weight:bold;border-bottom:2px solid rgba(232,233,234,1);margin-bottom:10px;}
	.info-box{margin:40px 0px;padding:0 20px;margin-top:30px;}
	.info-box .customertable{border:none;width:700px;}
	.info-box .customertable tr{border:none;}
	.info-box .customertable tr td{border:none;padding:4px;}
	.info-box .customertable tr td:nth-child(odd){width:100px;text-align:right;}

	.table tr td input{height:30px;line-height:30px;}
	.table tr td .fact_price{width:80px;}
	.table tr td .trade_num{width:40px;}

	.updatebox{margin-bottom:10px;}
	.updatebox .total-box{float:left;display:inline-block;margin-right:30px;}
	.updatebox .total-box div{float:left;height:35px;line-height:35px;display:block;}
	.updatebox .total-box input{float:left;height:35px;line-height:35px;}
	.updatebox .total-box .tips{color:#f00;width:100%;}
	.main_price_total, .main_price_fact, .main_pay_money, .main_pay_remark{margin-right:20px;font-size:20px;line-height:30px!important;}
	.main_price_total{color:#2b7dbc;}
	.main_price_fact{color:rgba(221, 55, 81, 1)}
	.main_price_fact button{margin-left:10px;}

	.float_table{border:none;}
	.float_table tr td{height:40px;line-height:40px!important;}
	.float_table tr td:first-child{text-align:right;width:150px;}
	.float_table tr td input,textarea{width:180px;}
	.float_table tr td select{width:300px;}
	.float_table tr td textarea{line-height:24px;}

	.dobox{float:left;padding:8px 15px;color:#fff;cursor:pointer;margin-right:20px;}
	.do-paylater{background:#428bca;}
	.do-payunderline{background:#82af6f;}

	.editbox a{float:right;margin-bottom:10px;}


	.changeConfigBox{width:400px;height:30px;line-height:30px;}
	.changeConfigBox .title{width:70px;float:left;text-align:center;height:30px;line-height:30px;border-right:1px #ccc solid;}
	.changeConfigBox .content{width:320px;float:left;text-align:left;height:30px;line-height:30px;}
	.changeConfigBox .content .front_content{width:140px;height:30px;line-height:30px;float:left;padding:0 5px;border-right:1px #ccc solid;}
	.changeConfigBox .content .after_content{width:140px;height:30px;line-height:30px;float:left;padding:0 5px;}
	.changeConfigBox .content .small{width:100px!important;}
	.changeConfigBox .smallcontent{width:240px!important;}
	.smallconfigbox{width:320px!important}
	.renewBox{float:left;height:30px;width:30px;display:inline-block;text-align:center;line-height:30px;}
	.minusRenew{border:1px #ccc solid;cursor:pointer;font-size:30px;line-height:28px;}
	.plusRenew{border:1px #ccc solid;cursor:pointer;font-size:26px;}

</style>
<div id="mainContainer">
	<div class="info-title">主订单信息</div>
	<div class="info-box">
        <div class="updatebox">
            <div class="total-box">
                <?php if(ArrayHelper::getValue($OriginalInfo,'order_status') == '进行中' && ArrayHelper::getValue($OriginalInfo,'order_pay_status')=='未支付'):?>
                    <button type="button" id="cancel_order" class="btn btn-warning btn-sm">取消订单</button>
                <?php endif;?>
            </div>
            <div class="clearfix"></div>
        </div>
		<table class="table customertable">
			<tr>
				<td>订单号：</td>
				<td><?php echo $OriginalInfo['order_id'];?></td>
				<td>订单类型：</td>
				<td><?php echo $OriginalInfo['order_type']?></td>
				<td>订单状态：</td>
				<td><?php echo $OriginalInfo['order_status'];?></td>
			</tr>
			<tr>
				<td>销售名称：</td>
				<td><?php echo $OriginalInfo['order_admin_name'];?></td>
				<td>用户账户：</td>
				<td><?php echo (\common\helpers\MemberHelper::userAccount($OriginalInfo["userMember"]));?></td>
				<td>用户昵称：</td>
				<td><?php echo $OriginalInfo['order_user_nickname'];?></td>
			</tr>
			<tr>
				<td>下单时间：</td>
				<td><?php echo $OriginalInfo['order_time_create'] ? date("Y-m-d H:i:s", $OriginalInfo['order_time_create']) : '--';?></td>
				<td>已付款金额：</td>
				<td><?php echo $OriginalInfo['order_finish_pay'];?></td>
			</tr>

		</table>
	</div>

	<div class="info-title">付款单信息</div>
	<div class="info-box">
		<div class="updatebox">
			<div class="total-box">
				<?php if(in_array("trade-manage/init-payorder", $node) || $this->params['is_administrator_user']):?>
					<?php if($OriginalInfo['order_finish_pay'] <= 0):?><button type="button" id="remove_split_order" class="btn btn-warning btn-sm">初始化付款单</button>&nbsp;&nbsp;<?php endif;?>
				<?php endif;?>

				<?php if(in_array("trade-manage/add-payorder", $node) || $this->params['is_administrator_user']):?>
					<?php if($OriginalInfo['order_status'] == '进行中'):?><button type="button" id="add_custom_order" class="btn btn-info btn-sm">新增自定义付款单</button><?php endif;?>
				<?php endif;?>
				<div class="tips">*注：初始化付款单后所有拆分的付款单会合并为一张付款单，请谨慎，若其中一笔已支付则不能进行初始化！</div>
			</div>
			<div class="clearfix"></div>
		</div>
		<table class="table table-bordered detailTable">
			<tr>
				<td>付款单项目</td>
				<td>付款单号</td>
				<td>付款金额</td>
				<td>付款平台</td>
				<td>付款渠道</td>
				<td>付款时间</td>
				<td>付款状态</td>
				<td>财务审核</td>
				<td>付款单属性</td>
				<td>支付操作</td>
				<td>其他操作</td>
			</tr>
			<?php foreach($GeneralList as $key => $val):?>
				<tr>
					<td><?php echo $val['general_payorder_title'];?></td>
					<td><?php echo $val['general_payorder'];?></td>
					<td><?php echo $val['general_pay_money'];?></td>
					<td><?php echo $val['general_pay_platform'] ? $val['general_pay_platform'] : '-';?></td>
					<td><?php echo $val['general_pay_channel'] ? $val['general_pay_channel'] : '-';?></td>
					<td><?php echo $val['general_pay_time'] ? date("Y-m-d H:i:s", $val['general_pay_time']) : '-';?></td>
					<td><?php echo $val['general_pay_lock'];?></td>
					<td><?php echo $val['general_payinfo_review'];?></td>
					<td><?php echo $val['general_payorder_type'] == 'original' ? '<label class="label label-default">原始付款单</label>' : '<label class="label label-warning">自定义付款单</label>';?></td>
					<td>
						<?php if($val['general_pay_lock'] == '等待支付'):?>
							<a class="blue" href="<?php echo Url::to(['payment-order/list', 'general_payorder' => $val['general_payorder']])?>">去付款</a>
						<?php endif;?>
						<?php if($val['general_payment_userid'] != $OriginalInfo['order_user_id']):?>
							<span class="label label-warning">指定单</span>
						<?php endif;?>
					</td>
					<td>
						<?php if($val['general_payorder_type'] == 'original' && $val['general_pay_lock'] == '等待支付'):?>

							<?php if(in_array("trade-manage/split-general", $node) || $this->params['is_administrator_user']):?>
								<a href="javascript:;" class="splitPayorder" data-money="<?php echo $val['general_pay_money']?>" data-id="<?php echo $val['general_id']?>">拆分付款单</a>
							<?php endif;?>

							<?php if(in_array("payment-order/designated-payment", $node) || $this->params['is_administrator_user']):?>
								<a href="javascript:;" class="blue Designated-Payment" data-id="<?php echo $val['general_id']?>" data-money="<?php echo $val['general_pay_money']?>" data-payorder="<?php echo $val['general_payorder']?>">指定用户付款</a>
							<?php endif;?>

						<?php endif;?>
						<?php if($val['general_payorder_type'] == 'custom' && $val['general_pay_lock'] == '等待支付'):?>

							<?php if(in_array("trade-manage/edit-payorder", $node) || $this->params['is_administrator_user']):?>
								<a href="javascript:;" class="edit_custom_payorder" general_id="<?php echo $val['general_id']?>" general_pay_money="<?php echo $val['general_pay_money'];?>" general_payorder_title="<?php echo $val['general_payorder_title'];?>">编辑</a>
							<?php endif?>

							<?php if(in_array("trade-manage/del-payorder", $node) || $this->params['is_administrator_user']):?>
								<a href="javascript:;" class="del_custom_payorder" general_id="<?php echo $val['general_id']?>">删除</a>
							<?php endif?>

							<?php if(in_array("payment-order/designated-payment", $node) || $this->params['is_administrator_user']):?>
								<a href="javascript:;" class="blue Designated-Payment" data-id="<?php echo $val['general_id']?>" data-money="<?php echo $val['general_pay_money']?>" data-payorder="<?php echo $val['general_payorder']?>">指定用户付款</a>
							<?php endif?>
						<?php endif?>
					</td>
				</tr>
			<?php endforeach?>
		</table>
		<div class="clearfix"></div>
	</div>


	<input type="hidden" id="order_id_form" value="<?php echo $OriginalInfo['order_id']?>"/>
	<div class="info-title">订单明细</div>
	<div class="info-box">
		<div class="updatebox">
			<div class="total-box">
				<div>订单总金额：</div>
				<div class="main_price_total"><?php echo $OriginalInfo['order_original_price']?></div>
				<div>付款总金额：</div>
				<div class="main_price_fact">
					<?php echo $OriginalInfo['order_update_price']?>
				</div>
				<!--
				<div>抹零金额：</div>
				<div class="main_price_fact">
					<?php echo $OriginalInfo['order_amount_money']?>
				</div>
				-->
				<div class="main_price_fact">
					<!--
					<?php if(in_array("trade-manage/update-order-price", $node) || $this->params['is_administrator_user']):?>
						<?php if($OriginalInfo['order_update_status'] == '未锁定' && $OriginalInfo['order_status'] == '进行中'):?>
							<button type="button" class="btn btn-info btn-sm" id="updateOrderTotalPrice">修改订单价格</button>
						<?php endif?>
					<?php endif?>
					-->
					<?php if(in_array("trade-manage/lock-and-unlock-price", $node) || $this->params['is_administrator_user']):?>

						<?php if($OriginalInfo['order_update_status'] == '未锁定' && $OriginalInfo['order_status'] == '进行中'):?>
							<button type="button" id="lock_order_price" class="btn btn-warning btn-sm">锁定订单价格</button>
						<?php elseif($OriginalInfo['order_update_status'] == '已锁定' && $OriginalInfo['order_status'] == '进行中'):?>
							<button type="button" id="lock_order_price" class="btn btn-danger btn-sm locked">解除价格锁定</button>
						<?php endif;?>

					<?php endif;?>
				</div>
				<!--
				<div>订单改价备注：</div>
				<div><?php echo $OriginalInfo['order_update_remark']?></div>
				-->
				<div class="tips">*注：新购 订单可以修改价格，变更配置 / 更换机器 订单不能修改价格与数量，续费订单只可修改数量，但不能修改价格</div>
				<div class="tips">*注：订单金额锁定之后，不能修改订单信息和订单总价</div>
			</div>
			<div class="clearfix"></div>
		</div>
		<?php if(in_array($OriginalInfo['order_type'], ['新购', '续费']) && $OriginalInfo['order_update_status'] == '未锁定' && $OriginalInfo['order_status'] == '进行中'):?>
			<div class="editbox">

				<?php if(in_array("trade-manage/update-order-detail", $node) || $this->params['is_administrator_user']):?>
					<a href="javascript:;" class="btn btn-info btn-sm" id="editDetailInfo" order_type="<?php echo $OriginalInfo['order_type']?>">编辑信息</a>
				<?php endif;?>

			</div>
		<?php endif?>
		<table class="table table-bordered detailTable infoTable">
			<?php if($OriginalInfo['order_type'] == '新购'):?>
				<tr>
					<td>新购区域</td>
					<td>新购配置</td>
					<td>新购续费价</td>
					<td>本次单价</td>
					<td>付费周期</td>
					<td>下单备注</td>
				</tr>
				<?php foreach($DetailList as $key => $val):?>
					<?php $renewDetailConfig = json_decode($val['detail_content'], true);?>
					<tr detail_id="<?php echo $val['detail_id']?>">
						<td><?php echo $val['servername'].' / '.$val['pdtname']?></td>
						<td><?php echo $val['config']['cpu'].' / '.$val['config']['ram'].' / '.$val['config']['hdd'].' / '.$val['config']['ipnumber'].' / '.$val['config']['defense'].' / '.$val['config']['operatsystem']?></td>
						<td><?php echo isset($renewDetailConfig['renew_price']) && $renewDetailConfig['renew_price'] != '' ? $renewDetailConfig['renew_price'] : $val['detail_price']?></td>
						<td><?php echo $val['detail_price']?></td>
						<td><?php echo $val['payment_cycle']?></td>
						<td><?php echo $val['detail_remark']?></td>
					</tr>
				<?php endforeach?>
			<?php elseif($OriginalInfo['order_type'] == '续费'):?>
				<tr>
					<td>主IP地址</td>
					<td>续费配置</td>
					<td>到期时间</td>
					<td>续费单价</td>
					<td>续费周期</td>
					<td>续费数</td>
					<td>续费总价</td>
				</tr>
				<?php foreach($DetailList as $key => $val):?>
					<tr detail_id="<?php echo $val['detail_id']?>">
						<td><?php echo $val['ip']?></td>
						<td><?php echo $val['config']['cpu'].' / '.$val['config']['ram'].' / '.$val['config']['hdd'].' / '.$val['config']['ipnumber'].' / '.$val['config']['defense'].' / '.$val['config']['operatsystem']?></td>
						<td><?php echo $val['end_time']?></td>
						<td><?php echo $val['sell_price']?></td>
						<td><?php echo $val['payment_cycle']?></td>
						<td><?php echo $val['renew_num']?></td>
						<td><?php echo $val['detail_price']?></td>
					</tr>
				<?php endforeach?>
			<?php elseif($OriginalInfo['order_type'] == '变更配置'):?>
				<tr>
					<td>产品区域</td>
					<td>主IP地址</td>
					<td>变更配置内容</td>
					<td>变更续费单价 / 续费周期</td>
					<td>付款金额</td>
				</tr>
				<?php foreach($DetailList as $key => $val):?>
					<tr>
						<td><?php echo $val['servername'].' / '.$val['pdtname']?></td>
						<td><?php echo $val['mainip']?></td>
						<td>
							<div class="changeConfigBox"><div class="title">变更项</div><div class="content"><div class="front_content">变更前配置</div><div class="after_content">变更后配置</div></div>
							<?php foreach($val['modify_data'] as $k => $v):?>
								<div class="changeConfigBox"><div class="title"><?php echo $v['modify_type']?></div><div class="content"><div class="front_content"><?php echo $v['modify_data']['old_config']?></div><div class="after_content"><?php echo $v['modify_data']['new_config']?></div></div></div>
							<?php endforeach;?>
						</td>
						<td>
							<div class="changeConfigBox"><div class="title">变更项</div><div class="content"><div class="front_content">变更前</div><div class="after_content">变更后</div></div>
							<div class="changeConfigBox"><div class="title">续费价</div><div class="content"><div class="front_content"><?php echo $val['old_sell_price']?></div><div class="after_content"><?php echo $val['modify_sell_price']?></div></div>
						</td>
						<td><?php echo $val['detail_price'];?></td>
					</tr>
				<?php endforeach?>
			<?php elseif($OriginalInfo['order_type'] == '更换机器'):?>
				<tr>
					<td></td>
					<td>产品区域</td>
					<td>主IP地址</td>
					<td>产品配置</td>
					<td>产品续费单价 / 续费周期</td>
					<td>补款总金额</td>
				</tr>
				<tr>
					<tr>
						<td>更换前</td>
						<td><?php echo $DetailList[0]['oldpdtname'].' / '.$DetailList[0]['oldservername']?></td>
						<td><?php echo $DetailList[0]['oldiplist']?></td>
						<td><?php echo $DetailList[0]['oldconfiginfo']?></td>
						<td><?php echo $DetailList[0]['oldsellprice']?></td>
						<td rowspan="2"><?php echo $DetailList[0]['paymoney']?></td>
					</tr>
					<tr>
						<td>更换后</td>
						<td><?php echo $DetailList[0]['newpdtname'].' / '.$DetailList[0]['newservername']?></td>
						<td><?php echo $DetailList[0]['newiplist']?></td>
						<td><?php echo $DetailList[0]['newconfiginfo']?></td>
						<td><?php echo $DetailList[0]['newsellprice']?></td>
					</tr>
				</tr>
			<?php endif?>
		</table>
	</div>
</div>

<!-- -->
<div id="addPayorder" style="display:none;">
	<table class="table float_table">
		<tr>
			<td><span style="color:red">*</span>付款名称：</td>
			<td><input type="text" class="new_payorder_title" value="" placeholder="例：专享网络费用"></td>
		</tr>
		<tr>
			<td><span style="color:red">*</span>付款金额：</td>
			<td><input type="text" class="new_payorder_money" value="" placeholder="例：1000"></td>
		</tr>
	</table>
</div>

<div id="updatePrice" style="display:none;">
	<table class="table float_table">
		<tr>
			<td><span style="color:red">*</span>原订单金额：</td>
			<td style="color:red" class="presave order_original_price"><?php echo $OriginalInfo['order_original_price'];?></td>
		</tr>
		<tr>
			<td><span style="color:red">*</span>修改后金额：</td>
			<td><input type="text" class="order_update_price" value="<?php echo $OriginalInfo['order_update_price'];?>"></td>
		</tr>
		<tr>
			<td><span style="color:red">*</span>抹零金额：</td>
			<td><input type="text" class="order_amount_money" value="<?php echo $OriginalInfo['order_amount_money'];?>"></td>
		</tr>
		<tr>
			<td><span style="color:red">*</span>修改金额备注：</td>
			<td><textarea class="order_update_remark" style="resize:none;height:150px;"><?php echo $OriginalInfo['order_update_remark'];?></textarea></td>
		</tr>
	</table>
</div>

<div id="splitBox" style="display:none;">
	<table class="table float_table">
		<tr>
			<td><span style="color:red">*</span>原付款总金额：</td>
			<td style="color:red" class="presave pay_total_money"></td>
		</tr>
		<tr>
			<td><span style="color:red">*</span>要拆分出来的金额：</td>
			<td><input type="text" class="pay_split_money" value=""></td>
		</tr>
	</table>
</div>

<!-- 指定用户支付 -->
<div id="Designated_modal" style="display:none;">
	<table class="table float_table">
		<tr>
			<td><span style="color:red">*</span>付款单：</td>
			<td style="color:red" class="designated_payorder"></td>
		</tr>
		<tr>
			<td><span style="color:red">*</span>金额：</td>
			<td style="color:red" class="designated_money"></td>
		</tr>
		<tr>
			<td><span style="color:red">*</span>指定用户账户：</td>
			<td><input type="text" class="designated_useremail" value="" placeholder="例：<EMAIL>" style="width: 240px;"></td>
		</tr>
		<input type="hidden" class="designated_general_id" value="">
	</table>
</div>


<script type="text/javascript">
$(function() {


	//拆分付款单
	$(".splitPayorder").click(function() {
		var general_id = $(this).attr("data-id");
		var general_money = $(this).attr("data-money");

		$(".pay_total_money").html(general_money);
		$(".pay_split_money").val('');

		layer.open({
			type: 1,
			area: ['500px', '300px'],
			title:"" || "新增自定义付款单",
			content: $("#splitBox"),
			maxmin: true, //最大化按钮
			anim:3, //动画
			btn: ['确定', '关闭'],
			yes: function(index, layero) {
				layer.close(index);

				var splitResultMoney = $(".pay_split_money").val();

				if(Number(splitResultMoney) >= Number(general_money)) {
					console.log(splitResultMoney, general_money);
					layer.alert("拆分出来的金额不能大于等于原金额", {icon:7});
					return false;
				}

				if(splitResultMoney < 0) {
					layer.alert("拆分出来的金额不能小于0元", {icon:7});
					return false;
				}

				var url = "<?php echo Url::to(['trade-manage/split-general'])?>";
				$.post(url, {"general_id":general_id, "split_money":splitResultMoney}, function(e) {
					if(e.data.status == 0) {
						layer.alert(e.data.info, {icon:7}, function() {
							location.reload();
						});
					} else {
						layer.alert(e.data.info, {icon:1}, function() {
							location.reload();
						});
					}
				}, "json");

			}
		});

	});

	//初始化付款单
	$("#remove_split_order").click(function() {
		var index = layer.confirm("确定要初始化所有付款单吗？", {btn:['是的', '取消']}, function() {
			layer.close(index);
			var order_id = $("#order_id_form").val();
			var url = "<?php echo Url::to(['trade-manage/init-payorder'])?>";
			$.post(url, {"order_id":order_id}, function(e) {
				if(e.data.status == 0) {
					layer.alert(e.data.info, {icon:7}, function() {
						location.reload();
					});
				} else {
					layer.alert(e.data.info, {icon:1}, function() {
						location.reload();
					});
				}
			}, "json");
		});

	});

	//取消订单
	$("#cancel_order").click(function() {
		var index = layer.confirm("确定要取消订单吗？", {btn:['是的', '取消']}, function() {
			layer.close(index);
			var order_id = $("#order_id_form").val();
			var url = "<?php echo Url::to(['trade-manage/cancel-payorder'])?>";
			$.post(url, {"order_id":order_id}, function(e) {
				if(e.data.status == 0) {
					layer.alert(e.data.info, {icon:7}, function() {
						location.reload();
					});
				} else {
					layer.alert(e.data.info, {icon:1}, function() {
						location.reload();
					});
				}
			}, "json");
		});

	});

	//添加自定义付款单
	$("#add_custom_order").click(function() {
		var order_id = $("#order_id_form").val();
		layer.open({
			type: 1,
			area: ['500px', '300px'],
			title:"" || "新增自定义付款单",
			content: $("#addPayorder"),
			maxmin: true, //最大化按钮
			anim:3, //动画
			btn: ['确定', '关闭'],
			yes: function(index, layero) {
				layer.close(index);
				var general_payorder_title = $(".new_payorder_title").val();
				var general_pay_money = $(".new_payorder_money").val();
				var url = "<?php echo Url::to(['trade-manage/add-payorder'])?>";
				var loadindex = layer.load(1, {shade: [0.7, '#fff']});
				$.post(url, {"order_id":order_id, "general_payorder_title":general_payorder_title, "general_pay_money":general_pay_money}, function(e) {
					layer.close(loadindex);
					if(e.data.status == 0) {
						layer.alert(e.data.info, {icon:7}, function() {
							location.reload();
						});
					} else {
						layer.alert(e.data.info, {icon:1}, function() {
							location.reload();
						});
					}
				}, "json");
			}
		});
	});

	//编辑自定义付款单
	$(".edit_custom_payorder").click(function() {
		var order_id = $("#order_id_form").val();
		var general_id = $(this).attr("general_id");
		var general_payorder_title = $(this).attr("general_payorder_title");
		var general_pay_money = $(this).attr("general_pay_money");

		$(".new_payorder_title").val(general_payorder_title);
		$(".new_payorder_money").val(general_pay_money);

		layer.open({
			type: 1,
			area: ['500px', '300px'],
			title:"" || "编辑自定义付款单",
			content: $("#addPayorder"),
			maxmin: true, //最大化按钮
			anim:3, //动画
			btn: ['确定', '关闭'],
			yes: function(index, layero) {
				layer.close(index);

				var general_payorder_title = $(".new_payorder_title").val();
				var general_pay_money = $(".new_payorder_money").val();
				var url = "<?php echo Url::to(['trade-manage/edit-payorder'])?>";
				var loadindex = layer.load(1, {shade: [0.7, '#fff']});
				$.post(url, {"order_id":order_id, "general_id":general_id, "general_payorder_title":general_payorder_title, "general_pay_money":general_pay_money}, function(e) {
					layer.close(loadindex);
					if(e.data.status == 0) {
						layer.alert(e.data.info, {icon:7}, function() {
							location.reload();
						});
					} else {
						layer.alert(e.data.info, {icon:1}, function() {
							location.reload();
						});
					}
				}, "json");
			}
		});
	});

	//删除自定义付款单
	$(".del_custom_payorder").click(function() {
		var general_id = $(this).attr("general_id");
		var order_id = $("#order_id_form").val();
		layer.confirm("确定要删除此项自定义付款单吗？", {btn:["是的", "取消"]}, function(e) {
			var url = "<?php echo Url::to(['trade-manage/del-payorder'])?>";
			var loadindex = layer.load(1, {shade: [0.7, '#fff']});
			$.post(url, {"general_id":general_id, 'order_id':order_id}, function(e) {
				layer.close(loadindex);
				if(e.data.status == 0) {
					layer.alert(e.data.info, {icon:7}, function() {
						location.reload();
					});
				} else {
					layer.alert(e.data.info, {icon:1}, function() {
						location.reload();
					});
				}
			}, "json");
		});
	});

	//指定用户支付
	$('.Designated-Payment').click(function(){
		var general_id = $(this).attr("data-id");
		var general_money = $(this).attr("data-money");
		var general_payorder = $(this).attr("data-payorder");

		$(".designated_payorder").html(general_payorder);
		$(".designated_money").html(general_money);
		$(".designated_general_id").val(general_id);
		$(".designated_useremail").val('');

		layer.open({
			type: 1,
			area: ['500px', '300px'],
			title:"" || "指定用户支付付款单",
			content: $("#Designated_modal"),
			maxmin: false, //最大化按钮
			anim:3, //动画
			btn: ['确定', '关闭'],
			yes: function(index, layero) {
				var general_id = $(".designated_general_id").val();
				var designated_useremail = $(".designated_useremail").val();
				if(!designated_useremail) {
					layer.alert("必须选择指定用户", {icon:7});
					return false;
				}
				var url = "<?php echo Url::to(['payment-order/designated-payment'])?>";
				$.post(url, {"general_id":general_id, "user_email":designated_useremail}, function(e) {
					layer.close(index);
					if(e.data.status == 0) {
						layer.alert(e.data.info, {icon:7});
					} else {
						layer.alert(e.data.info, {icon:1}, function() {
							location.reload();
						});
					}
				}, "json");

			}
		});
	});
	$("#lock_order_price").click(function() {
		if($(this).hasClass("locked")) {
			//解除锁定
			layer.confirm("确定要解锁当前订单的价格吗？<br/><span style='color:#f00'>解锁订单价之后，所有的付款单会合成一个付款单，恢复初始状态</span>", {btn:["是的", "取消"]}, function(e) {
				var url = "<?php echo Url::to(['trade-manage/lock-and-unlock-price'])?>";
				var order_id = $("#order_id_form").val();

				$.post(url, {"order_id":order_id, "type":"unlock"}, function(e) {
					if(e.data.status == 0) {
						layer.alert(e.data.info, {icon:7}, function() {
							location.reload();
						});
					} else {
						layer.alert(e.data.info, {icon:1}, function() {
							location.reload();
						});
					}
				}, "json");
			});
		} else {
			//锁定
			layer.confirm("确定要锁定当前订单的价格吗？<br/>只有锁定之后才可以拆分付款单", {btn:["是的", "取消"]}, function(e) {
				var url = "<?php echo Url::to(['trade-manage/lock-and-unlock-price'])?>";
				var order_id = $("#order_id_form").val();

				$.post(url, {"order_id":order_id, "type":"lock"}, function(e) {
					if(e.data.status == 0) {
						layer.alert(e.data.info, {icon:7}, function() {
							location.reload();
						});
					} else {
						layer.alert(e.data.info, {icon:1}, function() {
							location.reload();
						});
					}
				}, "json");
			});
		}
	});

	$("#updateOrderTotalPrice").click(function() {
		layer.open({
			type: 1,
			area: ['500px', '500px'],
			title:"" || "修改订单金额",
			content: $("#updatePrice"),
			maxmin: true, //最大化按钮
			anim:3, //动画
			btn: ['确定', '关闭'],
			yes: function(index, layero) {
				layer.close(index);
				var order_update_price = $(".order_update_price").val();
				var order_amount_money = $(".order_amount_money").val();
				var order_update_remark = $(".order_update_remark").val();
				var order_id = $("#order_id_form").val();

				if(!order_update_price || order_update_price < 0 || order_update_remark == '') {
					layer.alert("修改后的付款金额必须大于0元，修改后的付款备注为必填选项", {icon:7});
					return false;
				}

				var url = "<?php echo Url::to(['trade-manage/update-order-price'])?>";
				var loadindex = layer.load(1, {shade: [0.7, '#fff']});
				$.post(url, {"order_update_price":order_update_price, "order_amount_money":order_amount_money, "order_update_remark":order_update_remark, "order_id":order_id}, function(e) {
					if(e.data.status == 0) {
						layer.alert(e.data.info, {icon:7}, function() {
							location.reload();
						});
					} else {
						layer.alert(e.data.info, {icon:1}, function() {
							location.reload();
						});
					}
				}, "json");
			}
		});
	});

	$("#editDetailInfo").click(function() {

		var order_type = $(this).attr("order_type");

		if(order_type != '新购' && order_type != '续费') {
			layer.alert('只有新购订单和续费订单可以修改详情信息');
			return false;
		}

		if($(this).hasClass('editing')) {
			//修改编辑中状态为初始状态
			var _this = $(this);
			//提交编辑
			var submitData = new Array();

			if(order_type == '新购') {
				var length = $(".infoTable > tbody > tr").length - 1;
				for(var i=1; i<=length; i++) {

					var tmpData = new Array();
					var buy_price = $(".infoTable > tbody > tr:eq("+i+")").children("td:eq(-3)").children('input').val();
					var renew_price = $(".infoTable > tbody > tr:eq("+i+")").children("td:eq(-4)").children('input').val();
					var detail_id = $(".infoTable > tbody > tr:eq("+i+")").children("td:eq(-3)").children('input').attr('detail_id');

					if(renew_price <= 0 || !renew_price) {

						_this.addClass('btn-info').removeClass('editing').removeClass('btn-success');
						_this.html('编辑信息');

						layer.alert("新购续费金额异常", {icon:7}, function() {
							location.reload();
						});
						return false;
					}

					if(buy_price <= 0 || !buy_price) {

						_this.addClass('btn-info').removeClass('editing').removeClass('btn-success');
						_this.html('编辑信息');

						layer.alert("本次支付金额异常", {icon:7}, function() {
							location.reload();
						});
						return false;
					}

					tmpData = [renew_price, buy_price, detail_id];
					submitData.push(tmpData);
				}
			}

			if(order_type == '续费') {
				var length = $(".infoTable > tbody > tr").length - 1;
				for(var i=1; i<=length; i++) {
					var tmpData = new Array();
					var renew_num = $(".infoTable > tbody > tr:eq("+i+")").children("td:eq(-2)").children('input').val();
					var total_money = $(".infoTable > tbody > tr:eq("+i+")").children("td:eq(-1)").children('input').val();
					var detail_id = $(".infoTable > tbody > tr:eq("+i+")").children("td:eq(-2)").children('input').attr('detail_id');

					if(renew_num <= 0 || !renew_num) {

						_this.addClass('btn-info').removeClass('editing').removeClass('btn-success');
						_this.html('编辑信息');

						layer.alert("续费数异常", {icon:7}, function() {
							location.reload();
						});
						return false;
					}

					if(total_money <= 0 || !total_money) {
						_this.addClass('btn-info').removeClass('editing').removeClass('btn-success');
						_this.html('编辑信息');

						layer.alert("付款金额异常", {icon:7}, function() {
							location.reload();
						});
						return false;
					}

					tmpData = [total_money, renew_num, detail_id];
					submitData.push(tmpData);
				}
			}

			var index = layer.confirm("订单详情修改之后，若与之前价格存在差异，则会更新订单价格，若已修改订单价格，需要重新改价", {btn:["确定","取消"]}, function(indexo) {
				layer.close(index);
				var loadindex = layer.load(1, {shade: [0.7, '#fff']});
				var url = "<?php echo Url::to(['trade-manage/update-order-detail'])?>";
				var order_id = $("#order_id_form").val();

				$.post(url, {"order_type":order_type, "data":submitData, "order_id":order_id}, function(e) {
					layer.close(loadindex);

					_this.addClass('btn-info').removeClass('editing').removeClass('btn-success');
					_this.html('编辑信息');

					if(e.data.status == 0) {
						layer.alert(e.data.info, {icon:7}, function() {
							location.reload();
						});
					} else {
						layer.alert(e.data.info, {icon:1}, function() {
							location.reload();
						});
					}

				}, "json");
			});




		} else {

			//开始编辑

			$(this).addClass('btn-success').addClass('editing').removeClass('btn-info');
			$(this).html('修改完成提交');

			if(order_type == '新购') {
				var length = $(".infoTable > tbody > tr").length - 1;
				for(var i=1; i<=length; i++) {

					var buy_price = $(".infoTable > tbody > tr:eq("+i+")").children("td:eq(-3)").html();
					var detail_id = $(".infoTable > tbody > tr:eq("+i+")").attr("detail_id");
					$(".infoTable > tbody > tr:eq("+i+")").children("td:eq(-3)").html('<input type="text" name="" class="renew_num_input" detail_id="'+detail_id+'" value="'+buy_price+'" />');

					var renew_price = $(".infoTable > tbody > tr:eq("+i+")").children("td:eq(-4)").html();
					$(".infoTable > tbody > tr:eq("+i+")").children("td:eq(-4)").html('<input type="text" name="" class="renew_num_input" detail_id="'+detail_id+'" value="'+renew_price+'" />');
				}
			}

			if(order_type == '续费') {
				var length = $(".infoTable > tbody > tr").length - 1;
				for(var i=1; i<=length; i++) {
					var renew_num = $(".infoTable > tbody > tr:eq("+i+")").children("td:eq(-2)").html();
					var total_money = $(".infoTable > tbody > tr:eq("+i+")").children("td:eq(-1)").html();
					var detail_id = $(".infoTable > tbody > tr:eq("+i+")").attr("detail_id");
					$(".infoTable > tbody > tr:eq("+i+")").children("td:eq(-2)").html('<input type="text" name="" class="renew_num_input" detail_id="'+detail_id+'" value="'+renew_num+'" />');
					$(".infoTable > tbody > tr:eq("+i+")").children("td:eq(-1)").html('<input type="text" name="" class="renew_num_input" detail_id="'+detail_id+'" value="'+total_money+'" />');
				}
			}
		}
	});

	$(".do-payunderline").click(function() {
		layer.open({
			type: 1,
			area: ['500px', '600px'],
			title:"" || "线下打款",
			content: $("#payUnder"),
			maxmin: true, //最大化按钮
			anim:3, //动画
			btn: ['确定', '关闭'],
			yes: function(index, layero) {

				//获取实时的订单金额数据
				var tradeUrl = "<?php echo Url::to(['trade-manage/ajax-get-trade'])?>";
				var factPayMoney = "";
				$.post(tradeUrl, {"orderid":"<?php echo $trade_oid?>"}, function(s) {
					factPayMoney = s.data.data.trade_pay_money ? s.data.data.trade_pay_money : s.data.data.trade_price_fact;
					var data = {};

					if(!$(".pay_platform").val()) {
						layer.alert("请选择打款银行/平台", {icon:7});
						return false;
					} else {
						data.platform = $(".pay_platform").val();
					}

					if(!$(".pay_platform_money").val() || !checkRate($(".pay_platform_money").val())) {
						layer.alert("请填写打款金额或金额格式不正确", {icon:7});
						return false;
					} else {

						if(Number($(".pay_platform_money").val()) > Number(factPayMoney)) {
							$(".presave").html(decimalTwo(Number($(".pay_platform_money").val()) - Number(factPayMoney)));
						} else if(Number($(".pay_platform_money").val()) < Number(factPayMoney)) {
							layer.alert("打款金额小于应支付金额，请核对后重新填写", {icon:7});
							return false;
						}

						data.money = $(".pay_platform_money").val();
					}

					if(!$(".pay_time").val()) {
						layer.alert("请选择打款时间", {icon:7});
						return false;
					} else {
						data.paytime = $(".pay_time").val();
					}

					data.proof = $(".pay_proof").val();
					data.remark = $(".pay_remark").val();

					layer.close(index);

					//发起请求
					var url = "<?php echo Url::to(['trade-manage/underline-pay'])?>";
					var trade_orderid = "<?php echo $trade_oid?>";

					var loading = layer.load(1, {shade: [0.7, '#fff']});
					$.post(url, {"orderid":trade_orderid, "data":data}, function(e) {
						layer.close(loading);
						if(e.data.status == 1) {
							layer.alert(e.data.info, {icon:1}, function(index) {
								window.location.reload();
							});
						} else {
							layer.alert(e.data.info, {icon:7});
						}
					}, "json");

				}, "json");





			}
		});
	});

	$(".pay_platform_money").keyup(function() {

		//获取实时的订单金额数据
		var tradeUrl = "<?php echo Url::to(['trade-manage/ajax-get-trade'])?>";
		var factPayMoney = "";
		var orderid = "<?php echo $trade_oid?>";
		$.post(tradeUrl, {"orderid":orderid}, function(s) {
			factPayMoney = s.data.data.trade_pay_money ? s.data.data.trade_pay_money : s.data.data.trade_price_fact;
			if(Number($(".pay_platform_money").val()) > Number(factPayMoney)) {
				$(".presave").html(decimalTwo(Number($(".pay_platform_money").val()) - Number(factPayMoney)));
			} else {
				$(".presave").html("0.00");
			}
		}, "json");



	});


	//后付款
	$(".do-paylater").click(function() {
		layer.confirm("确定要让这笔订单改为后付款单吗？", {btn: ['确定', '取消'], title:"后付款单再次审核"}, function(e) {
			var trade_orderid = "<?php echo $trade_oid?>";
			var url = "<?php echo Url::to(['trade-manage/pay-later']) ?>";

			var loading = layer.load(1, {shade: [0.7, '#fff']});
			$.post(url, {"trade_orderid":trade_orderid}, function(e) {
				layer.close(loading);
				if(e.data.status == 1) {
					layer.alert(e.data.info, {icon:1}, function(index) {
						window.location.reload();
					});
				} else {
					layer.alert(e.data.info, {icon:7});
				}
			}, "json");
		});
	});

	function checkRate(number) {
	　　var re = /^[0-9]+.?[0-9]*$/;
	　　if (!re.test(number)) {
	　　　　return false;
	　　} else {
			return true;
		}
	}

	function decimalTwo(num) {
			var result = parseFloat(num);
		if (isNaN(result)) {
			return false;
		}
			result = Math.round(num * 100) / 100;
		var s_x = result.toString();
		var pos_decimal = s_x.indexOf('.');
		if (pos_decimal < 0) {
			pos_decimal = s_x.length;
			s_x += '.';
		}
		while (s_x.length <= pos_decimal + 2) {
			s_x += '0';
		}
		return s_x;
	}

});
</script>
<?php $this->endBlock(); ?>