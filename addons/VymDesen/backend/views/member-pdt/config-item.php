<?php

use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\helpers\Html;

$this->title = '控制台管理-用户产品管理-产品详情';
use addons\VymDesen\common\components\DataHelper;
?>
<?php $this->beginBlock('content') ?>
<?php $node = \Yii::$app->session['auth']['node']; ?>
<link rel="stylesheet" href="/css/layui.css" media="all">
<style type="text/css">
	.disabled {
		display: none
	}

	a {
		color: #337ab7;
	}
</style>
<div class="row">
	<div class="col-xs-12">
		<div class="clearfix">
			<div class="pull-right tableTools-container"></div>
		</div>
		<!-- div.table-responsive -->
		<!-- div.dataTables_borderWrap -->
		<div>
			<div class="col-sm-10 col-sm-offset-1">
				<!-- #section:pages/invoice -->
				<div class="widget-box transparent">
					<div class="widget-header widget-header-large">
						<h3 class="widget-title grey lighter">
							<i class="ace-icon fa fa-leaf green"></i>
							用户产品详情
						</h3>
					</div>
					<div class="widget-body">
						<div class="widget-main padding-24">
							<div class="row">
								<div class="col-sm-6">
									<div class="row">
										<div class="col-xs-11 label label-lg label-info arrowed-in arrowed-right">
											<b>产品配置信息</b>
										</div>
									</div>
									<div>
										<ul class="list-unstyled spaced">
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>产品标识：
												<b class="red notranslate"><?php echo $MemberPdtRes['unionid'] ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>产品配置类别：
												<b class="red notranslate"><?php echo $MemberPdtRes['pdtmanage'][0]['name'] ?>
												</b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>CPU：
												<b class="red notranslate"><?php echo $config['cpu'] ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>内存大小：
												<b class="red notranslate"><?php echo $config['ram'] ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>硬盘：
												<b class="red notranslate"><?php echo $config['hdd'] ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>自有库带宽：
												<b class="red notranslate"><?php echo $config['configbandwidth'] ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>客户要求带宽：
												<b class="red notranslate"><?php echo $MemberPdtRes['bandwidth'] ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>实际带宽：
												<b class="red notranslate"><?php echo $MemberPdtRes['real_bandwidth'] ?></b>

												<!--<?php if (in_array("member-pdt/modify-realbandwidth", $node) || $this->params['is_administrator_user']): ?>
												&nbsp;&nbsp;<a class="btn btn-xs btn-primary" href="javascript:void(0);" rel="<?php echo $MemberPdtRes['real_bandwidth'] ?>" id="modify_bandwidth">更改实际带宽</a>
											<?php endif; ?>-->
											</li>

											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>IP可用数：
												<b class="red notranslate"><?php echo $config['ipnumber'] ?></b>
											</li>
											<?php if (!empty($config['defense'])): ?>
												<li>
													<i class="ace-icon fa fa-caret-right blue"></i>防御流量：
													<b class="red notranslate"><?php echo $config['defense'] ?></b>
												</li>
											<?php endif; ?>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>操作系统：
												<b class="red notranslate"><?php echo $config['operatsystem'] ?></b>
											</li>

										</ul>
									</div>
								</div><!-- /.col -->

								<div class="col-sm-6">
									<div class="row">
										<div class="col-xs-11 label label-lg label-success arrowed-in arrowed-right">
											<b>基本信息</b>
										</div>
									</div>
									<div>
										<ul class="list-unstyled  spaced">
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>所属用户：
												<b class="red notranslate">
													<?php if ($is_allow): ?>
														<?php echo (empty($UserMemberRes['email'])?
                                                            (empty($UserMemberRes['mobile'])?$UserMemberRes['username']:$UserMemberRes['mobile'])
                                                            :$UserMemberRes['email']) ?>&nbsp;( 名字： <?php echo $UserMemberRes['truename'] ?> )
														&nbsp;
														<?php if (in_array("user-member/info-item", $node) || $this->params['is_administrator_user']): ?>
															<a class="blue" href="<?php echo Url::to(['user-member/info-item', 'u_id' => $UserMemberRes['u_id']]) ?>"
																>查看用户</a>
														<?php endif; ?>
													<?php else: ?>
														---
													<?php endif; ?>

												</b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>用户联系QQ：
												<b class="red notranslate"><?php if ($is_allow): ?><?php echo $UserMemberRes['qq'] ?> <?php else: ?>
														--- <?php endif; ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>所属销售：
												<b class="red notranslate"><?php echo $UserMemberRes['admin_name'] ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>服务提供者：
												<b class="red notranslate"><?php echo $MemberPdtRes['servicerprovider'] == 0 ? "自有" : "供应商" ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>服务器分类：
												<b class="red notranslate"><?php echo $MemberPdtRes['servertype'][0]['type_name'] ?></b>
											</li>
											<?php if (!empty($MemberPdtRes['provider'])): ?>
												<li>
													<i class="ace-icon fa fa-caret-right green"></i>供应商
													<b class="red notranslate"><?php echo $MemberPdtRes['provider'][0]['name'] ?></b>
												</li>
											<?php endif; ?>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>机房：
												<b
													class="red notranslate"><?php if (!empty($MemberPdtRes['pdtroom'])): ?><?php echo $MemberPdtRes['pdtroom'][0]['name'] ?><?php endif; ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>机柜：
												<b class="red notranslate"><?php if (!empty($MemberPdtRes['pdtcabinet'])): ?><?php echo $MemberPdtRes['pdtcabinet'][0]['name'] ?>
													<?php endif; ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>机位：
												<b class="red notranslate"><?php echo $MemberPdtRes['occupies_position'] ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>服务器状态属性：
												<b class="red notranslate"><?php echo $MemberPdtRes['serverattribute'][0]['name'] ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>售出价格：
												<b class="red notranslate"><?php if ($is_allow): ?><?php echo $MemberPdtRes['sell_price'] ?> 元
													<?php else: ?> --- <?php endif; ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right green"></i>付款周期：
												<b class="red notranslate"><?php echo $MemberPdtRes['payment_cycle'] ?> 个月</b>
											</li>
										</ul>
									</div>
								</div><!-- /.col -->
							</div><!-- /.row -->
							<div class="row">
								<div class="col-sm-6">
									<div class="row">
										<div class="col-xs-11 label label-lg label-info arrowed-in arrowed-right">
											<b>IPMI与交换机信息</b>
										</div>
									</div>
									<div>
										<ul class="list-unstyled spaced">
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>IPMI地址：
												<b class="red notranslate"><?php echo $MemberPdtRes['ipmi_ip'] ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>IPMI用户名：
												<b class="red notranslate"><?php echo $MemberPdtRes['ipmi_name'] ?></b>
											</li>
											<li>
												<i class="ace-icon fa fa-caret-right blue"></i>IPMI密码：
												<b class="red notranslate"><?php echo $MemberPdtRes['ipmi_pwd'] ?></b>
											</li>
											<?php if ($MemberPdtRes['servicerprovider'] == 0): ?>
												<?php if (!empty($MemberPdtRes['switch'])): ?>
													<li>
														<i class="ace-icon fa fa-caret-right blue"></i>交换机地址：
														<b class="red notranslate"><?php echo $MemberPdtRes['switch'][0]['ip'] ?></b>
													</li>
													<li>
														<i class="ace-icon fa fa-caret-right blue"></i>交换机端口：
														<b class="red notranslate"><?php echo $MemberPdtRes['switch_port'] ?></b>
													</li>
												<?php endif; ?>
											<?php endif; ?>
										</ul>
									</div>
								</div><!-- /.col -->

								<div class="col-sm-6">
									<div class="row">
										<div class="col-xs-11 label label-lg label-success arrowed-in arrowed-right">
											<b>产品IP地址与初始账户</b>
										</div>
									</div>
									<div>
										<ul class="list-unstyled  spaced">
											<li>
												<?php $ip2Res = json_decode(@$MemberPdtRes['ip2'], true) ?>
												<i class="ace-icon fa fa-caret-right green"></i>IP地址：
												<?php if ($MemberPdtRes['servicerprovider'] == 0): ?>
													<a href="javascript:;" class="get_ip_properties" data-ip="<?php echo $ip2Res[0]; ?>">IP相关属性</a>
												<?php endif; ?>
												<div style="MARGIN-LEFT: 12%;">
													<b class="red notranslate" style="text-indent:2em">
														<?php $ipRes = json_decode(@$MemberPdtRes['ip'], true) ?>
														<?php if ($ipRes == null): ?>
														<?php else: ?>
															<?php foreach ($ipRes as $key => $ip): ?>
																<?php echo $ip . "<br/>"; ?>
															<?php endforeach; ?>
															<!--<a class="blue" href="javascript:;" id="ipRes">查看更多</a>-->
														<?php endif; ?>
													</b>
												</div>
											</li>
											<?php if (empty($InitialAccountRes)): ?>
												<li>
													<i class="ace-icon fa fa-caret-right green"></i>
													<b class="red notranslate">暂未设置登录账户&nbsp; </b>
												</li>
											<?php else: ?>
												<li>
													<i class="ace-icon fa fa-caret-right green"></i>账户名：
													<b class="red notranslate" style="font-family:'宋体'"><?php echo $InitialAccountRes['name'] ?>

														<?php if ($is_allow): ?>
															<?php if (in_array("member-pdt/modfiyaccount", $node) || $this->params['is_administrator_user']): ?>
																<a class="blue" href="javascript:void(0);" data-rel="tooltip" id="account">修改账户</a>
															<?php endif; ?>
														<?php endif; ?>
													</b>
												</li>
												<li>
													<i class="ace-icon fa fa-caret-right green"></i>账户密码：
													<b class="red notranslate" style="font-family:'宋体'"><?php echo $InitialAccountRes['pwd'] ?> </b>
												</li>
												<li>
													<i class="ace-icon fa fa-caret-right green"></i>远程端口：
													<b class="red notranslate"><?php echo $InitialAccountRes['port'] ?> </b>
												</li>
											<?php endif; ?>
										</ul>
									</div>
								</div><!-- /.col -->
							</div><!-- /.row -->
							<?php if (!in_array($MemberPdtRes['status'], ['80', '81', '82'])): ?>
								<div>
									<div style="margin-bottom: 10px;font-size: 16px;">产品操作：</div>
									<table class="">
										<thead>
											<td colspan="4">
												<?php if ($is_allow): ?>
													<a class="btn btn-sm btn-primary"
														href="<?php echo Url::to(['member-pdt/update', 'id' => $MemberPdtRes['id']]) ?>">编 辑</a>
													<a class="btn btn-sm btn-primary"
														href="javascript:layer.alert('请前往“新购/续费->续费下单”页面选择服务器进行续费操作。', {icon:7, title:'温馨提示'});">续 费</a>
													<a class="btn btn-sm btn-primary"
														href="<?php echo Url::to(['member-pdt/business-transfer', 'id' => $MemberPdtRes['id']]) ?>">过 户</a>
													<a class="btn btn-sm btn-primary"
														href="<?php echo Url::to(['member-pdt/refund', 'id' => $MemberPdtRes['id']]) ?>">退 款</a>
													<a class="btn btn-sm btn-primary"
														href="<?php echo Url::to(['member-pdt/replace-ip', 'id' => $MemberPdtRes['id']]) ?>">更换IP</a>
													<a class="btn btn-sm btn-primary" href="javascript:void(0);" id="upgrade">变更配置</a>
													<a class="btn btn-sm btn-primary"
														href="<?php echo Url::to(['member-pdt/replace', 'id' => $MemberPdtRes['id']]) ?>">更换机器</a>
													<?php if (!$AppointOff): ?>
														<?php if ($MemberPdtRes['status'] == 1 && $MemberPdtRes['end_time'] > time()): ?>
															<a class="btn btn-sm btn-primary appoint-off" href="javascript:;" id="AppointOff">预约下架</a>
														<?php endif; ?>
														<?php if ($MemberPdtRes['status'] == 1): ?>
															<a class="btn btn-sm btn-primary" href="javascript:;" id="OffNow"
																member_pdt_id="<?php echo $MemberPdtRes["id"]; ?>">立即下架</a>
														<?php endif; ?>
													<?php else: ?>
														<a class="btn btn-sm btn-primary appoint-cancel" href="javascript:;"
															unionid="<?php echo $MemberPdtRes["unionid"]; ?>" id="AppointCancel">取消预约关机下架</a>
													<?php endif; ?>
													<?php if ($MemberPdtRes['status'] == -1): ?>
														<a class="btn btn-sm btn-primary" href="javascript:void(0);" id="do_recovery">业务恢复</a>
													<?php endif; ?>
													<!-- allow结束-->
													<!-- $MemberPdtRes['servicerprovider'] != 0 && -->
													<?php if ((in_array("member-pdt/extInfo", $node) || $this->params['is_administrator_user'])): ?>
														<a class="btn btn-sm btn-primary ext_info_btn" href="javascript:void(0);">供应商额外信息</a>
													<?php endif; ?>
												<?php endif; ?>
											</td>
										</thead>
									</table>
									<div style="margin-bottom: 10px;margin-top: 10px;font-size: 14px;">
										<label>产品续费方式：</label>
										<?php if ($MemberPdtRes['is_auto'] == "Y"): ?>
											<b class="red notranslate">自动续费</b>
											<?php if ($is_allow): ?>

												<?php if (in_array("member-pdt/change-renewalmode", $node) || $this->params['is_administrator_user']): ?>
													<a class="btn btn-xs btn-primary" href="javascript:void(0);" rel="N"
														id="change_renewal_mode">设为手动续费</a>
												<?php endif; ?>

												<!-- allow结束-->
											<?php endif; ?>
										<?php else: ?>
											<b class="red notranslate">手动续费</b>
											<?php if ($is_allow): ?>
												<?php if (in_array("member-pdt/change-renewalmode", $node) || $this->params['is_administrator_user']): ?>
													<a class="btn btn-xs btn-primary" href="javascript:void(0);" rel="Y"
														id="change_renewal_mode">设为自动续费</a>
												<?php endif; ?>
												<!-- allow结束-->
											<?php endif; ?>

										<?php endif; ?>
									</div>
								</div>
								<div class="space"></div>
								<div class="well">
									<div>用户备注：<?php echo $MemberPdtRes['remark'] ?> &nbsp;
										<?php if (in_array("member-pdt/modify-remarks", $node) || $this->params['is_administrator_user']): ?>
                                            <a class="blue" href="javascript:void(0);" id="modify_remarks">修改</a>
										<?php endif; ?>
									</div><br />
									<div>配置备注说明：<?php echo $MemberPdtRes['note'] ?> &nbsp;
										<?php if (in_array("member-pdt/modify-remarks", $node) || $this->params['is_administrator_user']): ?>
											<a class="blue" href="javascript:void(0);" id="modify_note">修改</a><?php endif; ?>
									</div>
								</div>
								<div class="space"></div>
								<div>
									<div style="margin-bottom: 10px;font-size: 16px;">过户记录</div>
									<table class="table table-striped table-bordered">
										<thead>
											<tr>
												<th class="center">业务ID</th>
												<th>过户前用户</th>
												<th>过户后用户</th>
												<th>过户时间</th>
												<th>过户备注</th>
											</tr>
										</thead>
										<tbody>
											<?php foreach ($TransferRecordRes as $value2): ?>
												<tr>
													<td class="center"><?php echo $value2['unionid'] ?></td>
													<td><?php echo $value2['front_username'] ?></td>
													<td><?php echo $value2['after_username'] ?></td>
													<td class="hidden-480"><?php echo date('Y-m-d H:i:s', $value2['transfer_time']) ?></td>
													<td><?php echo $value2['transfer_remarks'] ?></td>
												</tr>
											<?php endforeach; ?>
										</tbody>
									</table>
								</div>
							<?php endif; ?>
							<div class="space-6"></div>
							<div class="row">
								<div class="col-sm-7 pull-left">
									<button class="btn btn-danger btn-sm" style="width:120px" onclick="window.history.back()">
										<i class="ace-icon fa fa-reply icon-only"></i>
									</button>
								</div>
							</div>
							<div class="hr hr8 hr-double hr-dotted"></div>
						</div>
					</div>
				</div>

				<!-- /section:pages/invoice -->
			</div>
		</div>
	</div>
</div>
<!--修改初始账户 -->
<div id="accountshow" class="modal">
	<div class="modal-dialog">
		<div class="modal-content">
			<div id="modal-wizard-container">
				<div class="modal-header">
					<h4 class="widget-title">修改账户：</h4>
				</div>
				<div class="modal-body step-content">
					<div class="step-pane active" style="min-height:70px" data-step="1">
						<div class="form-group" style="padding: 15px;">
							<label class="control-label col-xs-12 col-sm-3 no-padding-right" for="name">账户名：</label>
							<div class="col-xs-12 col-sm-9">
								<div class="clearfix">
									<input type="text" id="account_name" name="account_name"
										value="<?php echo @$InitialAccountRes['name'] ?>" class="col-xs-12 col-sm-6" />
								</div>
							</div>
						</div>
						<div class="form-group" style="padding: 15px;">
							<label class="control-label col-xs-12 col-sm-3 no-padding-right" for="name">账户密码：</label>
							<div class="col-xs-12 col-sm-9">
								<div class="clearfix">
									<input type="text" id="account_pwd" name="account_pwd" value="<?php echo @$InitialAccountRes['pwd'] ?>"
										class="col-xs-12 col-sm-6" />
								</div>
							</div>
						</div>
						<div class="form-group" style="padding: 15px;">
							<label class="control-label col-xs-12 col-sm-3 no-padding-right" for="name">远程端口：</label>
							<div class="col-xs-12 col-sm-9">
								<div class="clearfix">
									<input type="text" id="account_port" name="account_port"
										value="<?php echo @$InitialAccountRes['port'] ?>" class="col-xs-12 col-sm-6" />
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="modal-footer wizard-actions">
			<input type="hidden" name="account_id" value="<?php echo ArrayHelper::getValue($InitialAccountRes, 'id') ?>">
			<input type="hidden" name="unionid" value="<?php echo $MemberPdtRes['unionid'] ?>">
			<button class="btn btn-success btn-sm btn-next" id="modfiyaccountgo" data-last="Finish">确定修改
				<i class="ace-icon fa fa-arrow-right icon-on-right"></i>
			</button>
			<button class="btn btn-danger btn-sm pull-left" data-dismiss="modal"><i class="ace-icon fa fa-times"></i>
				取消
			</button>
		</div>
	</div>
</div>
<!-- 修改初始账户end -->

<!--更多IP -->
<div class="bootbox modal fade bootbox-prompt in" id="ipshow" tabindex="-1" role="dialog" style="padding-right: 17px;">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">×</font>
					</font>
				</button>
				<h4 class="modal-title">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">IP地址:</font>
					</font>
				</h4>
			</div>
			<div class="modal-body">
				<div class="bootbox-body">
					<div class="clearfix">
						<ul class="list-unstyled  spaced">
							<?php $ipRes = json_decode(@$MemberPdtRes['ip'], true) ?>
							<?php if ($ipRes == null): ?>
							<?php else: ?>
								<?php foreach ($ipRes as $key => $ip): ?>
									<li style="float: left;width:33%"><b class="red notranslate" style="text-indent:2em"><?php echo $ip; ?></b>
									</li>
								<?php endforeach; ?>
							<?php endif; ?>
						</ul>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">关闭</font>
					</font>
				</button>
			</div>
		</div>
	</div>
</div>
<!-- 更多IPend -->

<!--修改备注 -->
<div class="bootbox modal fade bootbox-prompt in" id="modify_remarks_show" tabindex="-1" role="dialog"
	style="padding-right: 17px;">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">×</font>
					</font>
				</button>
				<h4 class="modal-title">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">修改备注:</font>
					</font>
				</h4>
			</div>
			<div class="modal-body">
				<div class="bootbox-body">
					<div class="clearfix">
						<div class="form-group">
							<label class="control-label col-sm-3 no-padding-right" for="name">用户备注<br />(用户可看)：</label>
							<div class="col-sm-9">
								<textarea class="form-control col-sm-9" rows="6" id="remark"
									name="remark"><?php echo $MemberPdtRes['remark'] ?></textarea>
								<span class="middle" style="color:red;margin-left:5px"></span>
							</div>
						</div>
					</div>
					<hr />
					<div class="clearfix">
						<div class="form-group">
							<label class="control-label col-sm-3 no-padding-right" for="name">配置备注说明：</label>
							<div class="col-sm-9">
								<textarea class="form-control col-xs-9 col-sm-9" rows="6" id="note"
									name="note"><?php echo $MemberPdtRes['note'] ?></textarea>
								<span class="middle" style="color:red;margin-left:5px"></span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">关 闭</font>
					</font>
				</button>
				<button class="btn btn-success" id="modify_remarks_go" data-last="Finish">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">确 定</font>
					</font>
				</button>
			</div>
		</div>
	</div>
</div>
<!-- 修改备注 End -->

<style type="text/css">
	.form-horizontal {
		padding-left: 30px;
	}

	.font-size-12 {
		font-size: 12px;
	}

	.ny-form .form-group {
		margin-bottom: 12px;
		line-height: 30px;
	}

	.form-horizontal .form-group {
		margin-right: -15px;
		margin-left: -15px;
	}

	.ny-form .ny-control-label {
		float: left;
	}

	.ny-control-label {
		width: 160px;
		padding-right: 0;
		text-align: right;
		color: #808080;
	}

	.ny-form-control {
		width: 100px;
		display: inline-block;
		white-space: nowrap;
		color: #555;
		background-color: #fff;
		background-image: none;
		outline: none;
	}

	.ny-number-container {
		float: left;
		line-height: 1;
	}

	.number-input-box {
		width: 131px;
	}

	.number-input-box {
		float: left;
		position: relative;
		width: 100px;
		border-radius: 2px;
	}

	.alert-warn,
	.alert-error,
	.alert-success {
		padding: 7px 22px 5px 37px;
		background: url(img/uc/tip_icon_warn_16.png) no-repeat 10px 10px #fff3e5;
		border: 1px solid #ff8800;
		border-radius: 2px;
		color: #ff8800;
		font-size: 12px;
		line-height: 2em;
	}

	.margin-bottom-20 {
		margin-bottom: 20px;
	}
</style>
<!--变更配置 -->
<div class="bootbox modal fade bootbox-prompt in" id="upgrade_show" tabindex="-1" role="dialog"
	style="padding-right: 17px;">
	<div class="modal-dialog" style="width:800px;">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">×</font>
					</font>
				</button>
				<h4 class="modal-title">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">变更配置</font>
					</font>
				</h4>
			</div>
			<div class="modal-body maxModalHeight" style="overflow: auto;max-height:850px">
				<div class="bootbox-body">
					<form id="upgradeForm" action=""
						class="bootbox-form form-horizontal ny-form ny-tab-container font-size-12 tab-group-undefined">
						<div class="form-group">
							<div class="ny-control-label">当前配置类别：</div>
							<div class="col-xs-8 ny-form-control"><?php echo $MemberPdtRes['pdtmanage'][0]['name'] ?></div>
							<div class="ny-control-label">变更后配置类别：</div>
							<div class="col-xs-5">
								<select name="pdt_id" id="pdt_id" class="col-xs-6">
									<?php foreach ($PdtManageList as $val): ?>
										<option value="<?php echo $val['id']; ?>" <?php if ($val['id'] == $MemberPdtRes['pdt_id']): ?>
												selected="selected" <?php endif; ?>><?php echo $val['name'] ?></option>
									<?php endforeach; ?>
								</select>
							</div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">当前CPU：</div>
							<div class="col-xs-8 ny-form-control"><?php echo $config['cpu'] ?></div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">当前内存：</div>
							<div class="col-xs-8 ny-form-control"><?php echo $config['ram'] ?></div>
							<div class="ny-control-label">变更后的内存：</div>
							<div class="col-xs-8 ny-form-control">
								<div class="ny-number-container"><span class="number-input-box"><input type="text" name="ram"
											class="ny-input-reset ny-number-input"></span></div>
							</div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">当前硬盘：</div>
							<div class="col-xs-8 ny-form-control"><?php echo $config['hdd'] ?></div>
							<div class="ny-control-label">变更后硬盘：</div>
							<div class="col-xs-8 ny-form-control">
								<div class="ny-number-container"><span class="number-input-box"><input type="text" name="hdd"
											class="ny-input-reset ny-number-input"></span></div>
							</div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">当前机器实际带宽为：</div>
							<div class="col-xs-8 ny-form-control"><?php echo $MemberPdtRes['real_bandwidth'] ?></div>
							<div class="ny-control-label">变更后机器实际带宽为：</div>
							<div class="col-xs-8 ny-form-control">
								<div class="ny-number-container"><span class="number-input-box"><input type="text" name="real_bandwidth"
											class="ny-input-reset ny-number-input"></span></div>
							</div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">客户要求带宽：</div>
							<div class="col-xs-8 ny-form-control"><?php echo $MemberPdtRes['bandwidth'] ?></div>
							<div class="ny-control-label">变更后要求带宽：</div>
							<div class="col-xs-8 ny-form-control">
								<div class="ny-number-container"><span class="number-input-box"><input type="text" name="bandwidth"
											class="ny-input-reset ny-number-input"></span></div>
							</div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">当前可用IP数：</div>
							<div class="col-xs-8 ny-form-control"><?php echo $config['ipnumber'] ?></div>
							<div class="ny-control-label">
								变更后可用IP数：
							</div>
							<div class="col-xs-8 ny-form-control">
								<div class="ny-number-container"><span class="number-input-box"><input type="text" name="ipnumber"
											class="ny-input-reset ny-number-input"></span></div>
							</div>
						</div>

						<div class="form-group">
							<div class="ny-control-label">当前防御值：</div>
							<div class="col-xs-8 ny-form-control"><?php echo @$config['defense'] ?></div>
							<div class="ny-control-label">变更后防御值：</div>
							<div class="col-xs-8 ny-form-control">
								<div class="ny-number-container"><span class="number-input-box"><input type="text" name="defense"
											class="ny-input-reset ny-number-input"></span></div>
							</div>

						</div>
						<div class="form-group">
							<div class="ny-control-label">业务到期日期：</div>
							<div class="col-xs-8 ny-form-control"><?php echo date('Y-m-d H:i:s', $MemberPdtRes['end_time']) ?></div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">是否需要技术参与处理：</div>
							<div class="col-xs-8">
								<div class="ny-number-container">
									<label>
										<input name="need_workorder" checked="checked" value="Y" type="radio" class="ace">
										<span class="lbl"> 需要</span>
									</label>
									<label style="margin-left: 20px;">
										<input name="need_workorder" value="N" type="radio" class="ace">
										<span class="lbl"> 不需要</span>
									</label>
								</div>
								<div class="col-xs-8 ny-form-control" style="text-align:left;margin-left: 80px;">
									选择不需要将不会生成售后工单
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">配置变更后 产品续费价：</div>
							<div class="col-xs-8">
								<div class="ny-number-container">
									<span class="number-input-box">
										<input type="text" name="normal_price" class="ny-input-reset ny-number-input">
										<span class="ny-number-unit"></span>
										<br />
									</span>
								</div>
								<div class="col-xs-8 ny-form-control" style="text-align:left;margin-left: 80px;">
									当前续费周期与续费价格：<?php echo $MemberPdtRes['payment_cycle'] . '个月' ?>&nbsp;&nbsp;<?php echo $MemberPdtRes['sell_price'] ? $MemberPdtRes['sell_price'] : '0.00' ?>
								</div>
							</div>

						</div>

						<div class="form-group">
							<div class="ny-control-label">本次变更 补款价：</div>
							<div class="col-xs-8">
								<div class="ny-number-container">
									<span class="number-input-box">
										<input type="text" name="price" class="ny-input-reset ny-number-input">
										<span class="ny-number-unit"></span>
									</span>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">服务起始日期：</div>
							<div class="col-xs-8">
								<div class="ny-number-container">
									<span class="number-input-box">
										<input type="text" name="service_starttime" id="service_starttime" readonly
											value="<?php echo date("Y-m-d", time()); ?>" class="ny-input-reset ny-number-input">
										<span class="ny-number-unit"></span>
									</span>
								</div>
								<div class="col-xs-8 ny-form-control" style="text-align:left;margin-left: 80px;">
									默认为今天日期
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">服务结束日期：</div>
							<div class="col-xs-8">
								<div class="ny-number-container">
									<span class="number-input-box">
										<input type="text" name="service_endtime" id="service_endtime" readonly
											class="ny-input-reset ny-number-input">
										<span class="ny-number-unit"></span>
									</span>
								</div>
								<div class="col-xs-8 ny-form-control" style="text-align:left;margin-left: 80px;">
									默认为机器到期日期
								</div>
							</div>
						</div>
						<input type="hidden" name="id" value="<?php echo $MemberPdtRes['id'] ?>">
						<input type="text" class="hide">
					</form>
				</div>
			</div>
			<div class="modal-footer">
				<button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">取消</font>
					</font>
				</button>
				<button data-bb-handler="confirm" data-last="Finish" type="button" class="btn btn-primary" id="do_upgrade">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">确定</font>
					</font>
				</button>
			</div>
		</div>
	</div>
</div>
<!-- 变更配置end -->

<!--预约关机下架 -->
<div class="bootbox modal fade bootbox-prompt in" id="AppointOff_show" tabindex="-1" role="dialog"
	style="padding-right: 17px;">
	<div class="modal-dialog" style="width:800px;">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
					<font style="vertical-align: inherit;">×</font>
				</button>
				<h4 class="modal-title">
					<font style="vertical-align: inherit;">变更配置</font>
				</h4>
			</div>
			<div class="modal-body maxModalHeight" style="overflow: auto;max-height:850px">
				<div class="bootbox-body">
					<form id="AppointForm" action=""
						class="bootbox-form form-horizontal ny-form ny-tab-container font-size-12 tab-group-undefined">
						<div class="form-group">
							<div class="ny-control-label">预约下架时间：</div>
							<div class="col-xs-8">
								<div class="ny-number-container">
									<span class="number-input-box">
										<input type="text" name="appoint_time" id="appoint_time" readonly
											class="ny-input-reset ny-number-input">
										<span class="ny-number-unit"></span>
									</span>
								</div>
								<div class="col-xs-8 ny-form-control" style="text-align:left;margin-left: 80px;">
									默认延后2天
								</div>
							</div>
						</div>
						<input type="hidden" name="unionid" value="<?php echo $MemberPdtRes['unionid'] ?>">
						<input type="text" class="hide">
					</form>
				</div>
			</div>
			<div class="modal-footer">
				<button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">取消</font>
					</font>
				</button>
				<button data-bb-handler="confirm" data-last="Finish" type="button" class="btn btn-primary" id="do_AppointOff">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">确定</font>
					</font>
				</button>
			</div>
		</div>
	</div>
</div>
<!-- 预约关机下架end -->

<!-- 更改实际带宽 -->
<div class="bootbox modal fade bootbox-prompt in" id="modify_real_bandwidth" tabindex="-1" role="dialog"
	style="padding-right: 17px;">
	<div class="modal-dialog" style="">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">×</font>
					</font>
				</button>
				<h4 class="modal-title">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">更改实际带宽</font>
					</font>
				</h4>
			</div>
			<div class="modal-body">
				<div class="bootbox-body">
					<form id="modifybandwidthForm" action=""
						class="bootbox-form form-horizontal ny-form ny-tab-container font-size-12 tab-group-undefined">
						<div class="form-group">
							<div class="ny-control-label">当前实际带宽：</div>
							<div class="col-xs-8 ny-form-control"><?php echo $MemberPdtRes['real_bandwidth'] ?></div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">
								实际带宽更改为：
							</div>
							<div class="col-xs-8 ny-form-control">
								<div class="ny-number-container"><span class="number-input-box"><input type="text" name="real_bandwidth"
											class="ny-input-reset ny-number-input"></span></div>
							</div>
						</div>
						<input type="hidden" name="id" value="<?php echo $MemberPdtRes['id'] ?>">
					</form>
				</div>
			</div>
			<div class="modal-footer">
				<button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">取消</font>
					</font>
				</button>
				<button data-bb-handler="confirm" data-last="Finish" type="button" class="btn btn-primary"
					id="do_modify_bandwidth">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">确定</font>
					</font>
				</button>
			</div>
		</div>
	</div>
</div>
<!-- 更改实际带宽end -->


<!-- inline scripts related to this page -->

<script src='/console/extInfo.js' type='text/javascript' ></script>
<script>
	var memberpdt_id = <?php echo $MemberPdtRes['id'] ?>;
	var memberpdt_name = "<?php echo $MemberPdtRes['unionid'] ?>";

	var end = {
		elem: '#service_endtime',
		format: 'YYYY-MM-DD',
		min: '1900-01-01', //设定最小日期为当前日期
		max: '2099-06-16', //最大日期
		istime: false,
		istoday: true,
		choose: function (datas) {
			end.min = datas; //开始日选好后，重置结束日的最小日期
		}
	};
	laydate(end);
	laydate.skin('molv');
	$('#service_endtime').val("<?php echo date("Y-m-d", $MemberPdtRes['end_time']); ?>");


	var appoint = {
		elem: '#appoint_time',
		format: 'YYYY-MM-DD',
		min: '1900-01-01', //设定最小日期为当前日期
		max: '2099-06-16', //最大日期
		istime: false,
		istoday: true,
		choose: function (datas) {
		}
	};
	laydate(appoint);
	laydate.skin('molv');
	$('#appoint_time').val("<?php echo date("Y-m-d H:i:s", strtotime(date("Y-m-d", time())) + (86400 * 2)); ?>");

	/*$('#service_starttime').val(laydate.now(0, 'YYYY-MM-DD'));*/



	//预约关机下架
	$(".appoint-off").click(function () {
		$('#AppointOff_show').modal('show');
	});

	$("#do_AppointOff").click(function () {

		layer.confirm("确定要预约关机下架吗？关机下架会重置配置和带宽", {
			icon: 3,
			btn: ['确定', '取消'] //按钮
		}, function () {
			layer.msg('正在执行...', { icon: 16, time: 0 });
			url = "<?php echo Url::to(['member-pdt/appoint-off']); ?>";

			$.post(url, $('#AppointForm').serialize(), function (data) {
				if (data['data']['status']) {
					layer.alert(data['data']['info'], { icon: 1 }, function (index) {
						layer.msg('正在更新数据...', { icon: 16, time: 0 });
						location.reload();
					});
				} else {
					layer.alert(data['data']['info'], { icon: 7 });
				}
			}, 'json');

		});
	});

	//取消预约关机
	$("#AppointCancel").click(function () {

		var unionid = $(this).attr("unionid");

		layer.confirm("确定要取消预约关机下架吗", {
			icon: 3,
			btn: ['确定', '取消'] //按钮
		}, function () {
			layer.msg('正在执行...', { icon: 16, time: 0 });
			url = "<?php echo Url::to(['member-pdt/appoint-cancel']); ?>";

			$.post(url, { "unionid": unionid }, function (data) {
				if (data['data']['status']) {
					layer.alert(data['data']['info'], { icon: 1 }, function (index) {
						layer.msg('正在更新数据...', { icon: 16, time: 0 });
						location.reload();
					});
				} else {
					layer.alert(data['data']['info'], { icon: 7 });
				}
			}, 'json');

		});
	});


	//立即下架
	$("#OffNow").click(function () {
		var id = $(this).attr("member_pdt_id");

		layer.confirm("确定要立即下架这台机器吗", {
			icon: 3,
			btn: ['确定', '取消'] //按钮
		}, function () {
			layer.msg('正在执行...', { icon: 16, time: 0 });
			url = "<?php echo Url::to(['member-pdt/overduedel']); ?>";

			$.post(url, { "id": id }, function (data) {
				if (data['data']['status']) {
					layer.alert(data['data']['info'], { icon: 1 }, function (index) {
						layer.msg('正在更新数据...', { icon: 16, time: 0 });
						location.reload();
					});
				} else {
					layer.alert(data['data']['info'], { icon: 7 });
				}
			}, 'json');

		});
	});


	$('#account').click(function () {
		$('#accountshow').modal('show');
		return;
	});
	$('#ipRes').click(function () {
		$('#ipshow').modal('show');
		return;
	});
	$('#upgrade').click(function () {
		//var maxModalHeight = document.documentElement.clienceHeight - 500 + 'px';
		//$(".maxModalHeight").css({
		//	'max-height': 'maxModalHeight',
		//	'overflow' : 'auto'
		//});
		$('#upgrade_show').modal('show');
		return;
	});
	$('#modify_remarks').click(function () {
		$('#modify_remarks_show').modal('show');
		return;
	});
	$('#modify_note').click(function () {
		$('#modify_remarks_show').modal('show');
		return;
	});
	$('#modify_bandwidth').click(function () {
		$('#modify_real_bandwidth').modal('show');
		return;
	});
	//IP属性
	$(".get_ip_properties").mouseover(function () {
		var _this = $(this);
		var ip = _this.attr('data-ip');

		var url = "<?php echo Url::to(['get-ipnotes']); ?>";
		$.post(url, { 'ip': ip }, function (data) {
			if (data['data']['status']) {
				var remarks = data['data']['remarks'];
				if (remarks) {
					layer.tips(remarks, _this, {
						tips: [1, "#3595CC"],
						time: 3000
					});
				} else {
					layer.tips('未有信息', _this, {
						tips: [1, "#3595CC"],
						time: 3000
					});
				}

			} else {
				layer.alert(data['data']['info'], { icon: 7 });
			}
		}, 'json');

	});
	$('.exchangeip').click(function () {
		layer.confirm('此操作为特殊的IP对换操作，仅限于自有机器，请谨慎使用！', {
			icon: 3,
			btn: ['确定', '取消'] //按钮
		}, function () {
			window.location.href = "<?php echo Url::to(['member-pdt/exchangeip', 'unionid' => $MemberPdtRes['unionid']]) ?>";
		}, function () {

		});
		return false;
	});

	$('input[name="ipnumber"]').bind("input propertychange", function () {
		var ipnumber = $(this).val();
		var now_ipnumber = '<?php echo $config["ipnumber"] ?>';
		if (ipnumber) {
			if (ipnumber == now_ipnumber) {
				$(".isneed_replaceip_div").hide();
			} else {
				$(".isneed_replaceip_div").show();
			}
		} else {
			$("input[type=radio][name=isneed_replaceip][value=0]").attr("checked", 'checked');
			$(".isneed_replaceip_div").hide();
		}
	});

	//变更配置
	$('#do_upgrade').click(function () {
		var isNum = /^\d+(\.\d+)?$/;
		var price = $("[name='price']").val();
		var normal_price = $("[name='normal_price']").val();
		if (!price != 0 || !price != '' || !isNum.test(normal_price)) {
			layer.alert("请输入正确的价格", { icon: 7 });
			return;
		}
		if (parseInt(price) > parseInt(0)) {
			var Prompting = "此操作为升配操作,将生成新的变配订单,确定吗？";
		}
		else if (parseInt(price) < parseInt(0)) {
			var Prompting = "此操作为降配操作,将有款项进入用户余额,确定吗？";
		} else {
			var Prompting = "确定变更配置吗";
		}
		layer.confirm(Prompting, {
			icon: 3,
			btn: ['确定', '取消'] //按钮
		}, function () {
			layer.msg('正在执行...', { icon: 16, time: 0 });
			//url = "<?php echo Url::to(['member-pdt/upgrade']); ?>";
			url = "<?php echo Url::to(['member-pdt/change-config']); ?>";

			$.post(url, $('#upgradeForm').serialize(), function (data) {
				if (data['data']['status']) {
					layer.alert(data['data']['info'], { icon: 1 }, function (index) {
						layer.msg('正在更新数据...', { icon: 16, time: 0 });
						location.reload();
					});
				} else {
					layer.alert(data['data']['info'], { icon: 7 });
				}
			}, 'json');

		}, function () {

		});
	});

	//业务恢复
	$('#do_recovery').click(function () {
		var _this = $(this);
		layer.confirm('确定要恢复该用户服务器产品吗？', {
			icon: 3,
			btn: ['确定', '取消'] //按钮
		}, function () {
			var url = "<?php echo Url::to(['member-pdt/recovery']) ?>";
			var loading = layer.load(0, { shade: [0.7, '#f1f3f5'] });
			$.post(url, { id: memberpdt_id }, function (data) {
				layer.closeAll('loading');
				if (data['data']['status']) {
					layer.alert(data['data']['info'], { icon: 1 }, function (index) {
						location.reload();
					});
				} else {
					layer.alert(data['data']['info'], { icon: 7 });
				}
			}, 'json');
		}, function () {

		});
		return false;
	});

	//添加初始账户请求
	$('#addaccountgo').click(function () {
		layer.msg('正在添加初始账户...', { icon: 16, time: 0 });
		url = "<?php echo Url::to(['member-pdt/addaccount']); ?>";
		$.post(url, { memberpdt_id: memberpdt_id, memberpdt_name: memberpdt_name, name: $("[name='account_name']").val(), pwd: $("[name='account_pwd']").val(), port: $("[name='account_port']").val() }, function (data) {
			if (data['data']['status']) {
				layer.alert(data['data']['info'], { icon: 1 }, function (index) {
					layer.msg('正在更新数据...', { icon: 16, time: 0 });
					location.reload();
				});
			} else {
				layer.alert(data['data']['info'], { icon: 7 });
			}
		}, 'json');
	});

	//修改账户请求
	$('#modfiyaccountgo').click(function () {
		layer.msg('正在修改初始账户...', { icon: 16, time: 0 });
		url = "<?php echo Url::to(['member-pdt/modfiyaccount']); ?>";
		$.post(url, { id: $("[name='account_id']").val(), name: $("[name='account_name']").val(), pwd: $("[name='account_pwd']").val(), port: $("[name='account_port']").val() }, function (data) {
			if (data['data']['status']) {
				layer.alert(data['data']['info'], { icon: 1 }, function (index) {
					layer.msg('正在更新数据...', { icon: 16, time: 0 });
					location.reload();
				});
			} else {
				layer.alert(data['data']['info'], { icon: 7 });
			}
		}, 'json');
	});

	//修改备注
	$('#modify_remarks_go').click(function () {
		layer.msg('正在更新备注信息...', { icon: 16, time: 0 });
		url = "<?php echo Url::to(['member-pdt/modify-remarks']); ?>";
		$(".middle").empty();
		$.post(url, { id: memberpdt_id, remark: $("[name='remark']").val(), note: $("[name='note']").val() }, function (data) {
			if (data['data']['status']) {
				layer.alert(data['data']['info'], { icon: 1 }, function (index) {
					layer.msg('正在更新信息...', { icon: 16, time: 0 });
					location.reload();
				});
			} else {
				if (typeof data['data']['info'] === 'string') {
					layer.alert(data['data']['info'], { icon: 7 });
					return;
				}
				$.each(data['data']['info'], function (key, val) {
					layer.alert('信息填写有误', { icon: 7 });
					$("[name='" + key + "']").next('span').html('<i class="ace-icon fa fa-exclamation-circle bigger-110">' + val[0] + '</i>');
				});
			}
		}, 'json');
	});

	//修改续费方式
	$('#change_renewal_mode').click(function () {
		var _this = $(this);
		layer.confirm('确定更换续费方式吗？', {
			icon: 3,
			btn: ['确定', '取消'] //按钮
		}, function () {
			var url = "<?php echo Url::to(['change-renewalmode']) ?>";
			layer.msg('操作中...', { icon: 16, time: 0 });
			$.post(url, { id: memberpdt_id, is_auto: _this.attr('rel') }, function (data) {
				if (data['data']['status']) {
					layer.alert(data['data']['info'], { icon: 1 }, function (index) {
						layer.msg('正在更新数据...', { icon: 16, time: 0 });
						location.reload();
					});
				} else {
					layer.alert(data['data']['info'], { icon: 7 });
				}
			}, 'json');
		}, function () {

		});
		return false;
	});

	//更改实际带宽
	$('#do_modify_bandwidth').click(function () {
		var _this = $(this);
		layer.confirm('确定要更改实际带宽吗？', {
			icon: 3,
			btn: ['确定', '取消'] //按钮
		}, function () {
			var url = "<?php echo Url::to(['member-pdt/modify-realbandwidth']) ?>";
			layer.msg('操作中...', { icon: 16, time: 0 });
			$.post(url, $('#modifybandwidthForm').serialize(), function (data) {
				if (data['data']['status']) {
					layer.alert(data['data']['info'], { icon: 1 }, function (index) {
						layer.msg('正在更新数据...', { icon: 16, time: 0 });
						location.reload();
					});
				} else {
					layer.alert(data['data']['info'], { icon: 7 });
				}
			}, 'json');
		}, function () {

		});
		return false;
	});

</script>
</body>
<?php $this->endBlock(); ?>