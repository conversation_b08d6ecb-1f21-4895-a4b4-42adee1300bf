@charset "UTF-8";
body {
  background: #f6f8fb;
}

.et-main {
  position: relative;
  margin-top: calc(-1 * (var(--banner_height) - var(--navheight) - 40px));
  margin-bottom: 80px;
  width: 100%;
}
.et-main.articleContent {
  margin-top: calc(-1 * (var(--banner_height) - var(--navheight) - 20px));
}
.et-main.articleContent .newsPage_row {
  padding: 20px;
}
.et-main.notPresent {
  margin: 0px;
}
.et-main.notPresent .nullValue {
  padding: 300px 0 200px;
  margin: auto 0;
  background-color: #eee;
  display: flex;
  align-items: center;
  justify-content: center;
}
.et-main.notPresent .nullValue p {
  text-align: center;
  color: #e4e4e4;
  width: 300px;
  background: #fff;
}
.et-main .main-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-direction: row-reverse;
  gap: 20px;
}
.et-main .main-row.KeepToTheleft {
  flex-direction: row;
}
.et-main .main-row .et-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 1;
}
.et-main .main-row .et-right {
  min-width: var(--news_left_w);
  max-width: var(--news_left_w);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.et-main .pagebanner-relative {
  width: 100%;
  padding: 40px 30px;
  border-radius: 2px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.01);
  margin-bottom: 20px;
  background-color: #fff;
  background-image: url(news.css?v6.6.8.css);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  height: 150px;
}
.et-main .pagebanner-relative .pagebanner {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.et-main .pagebanner-relative .pagebanner.white .pagebanner-text .pagebanner-title {
  color: #fff;
}
.et-main .pagebanner-relative .pagebanner.white .pagebanner-text .searchTerms a {
  color: #fff;
}
.et-main .pagebanner-relative .pagebanner.white .unique_search_container form .unique_search_input {
  border: 1px solid #fff;
}
.et-main .pagebanner-relative .pagebanner.white .unique_search_container form .unique_search_button {
  border: 1px solid #fff;
  background: var(--gradient2);
}
.et-main .pagebanner-relative .pagebanner.white .unique_search_container form .unique_search_button:hover {
  background: var(--gradient1);
}
.et-main .pagebanner-relative .pagebanner-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
}
.et-main .pagebanner-relative .pagebanner-text .pagebanner-title {
  font-size: 22px;
  font-weight: 900;
  color: var(--txtcl);
}
.et-main .pagebanner-relative .pagebanner-text .searchTerms {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.et-main .pagebanner-relative .pagebanner-text .searchTerms a {
  margin-right: 22px;
  font-size: 14px;
  font-weight: 400;
  color: #8e97ac;
  line-height: 24px;
  border-radius: 4px;
  transition: all 0.3s ease;
  position: relative;
}
.et-main .pagebanner-relative .pagebanner-text .searchTerms a:after {
  content: "";
  position: absolute;
  width: 1px;
  height: 12px;
  background: #8e97ac;
  opacity: 0.5;
  top: 50%;
  right: -11px;
  transform: translateY(-50%);
  transform-origin: left center;
  transition: all 0.3s ease;
}
.et-main .pagebanner-relative .pagebanner-text .searchTerms a:first-child {
  margin-right: 10px;
  color: #000;
}
.et-main .pagebanner-relative .pagebanner-text .searchTerms a:first-child:after {
  display: none;
}
.et-main .pagebanner-relative .pagebanner-text .searchTerms a:last-child {
  margin-right: 0;
}
.et-main .pagebanner-relative .pagebanner-text .searchTerms a:last-child:after {
  display: none;
}
.et-main .pagebanner-relative .pagebanner-text .searchTerms a:hover {
  color: #000;
}
.et-main .pagebanner-relative .unique_search_container {
  width: fit-content;
}
.et-main .pagebanner-relative .unique_search_container form {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.et-main .pagebanner-relative .unique_search_container .unique_search_input {
  width: 264px;
  padding: 0px 34px 0px 10px;
  height: 38px;
  border: 1px solid #b4bdd0;
  border-right: none;
  color: var(--txtcl);
  border-radius: 2px 0 0 2px;
  outline: none;
  background: #fff url("../../yibazhan/images/remoteL17091860929725.svg");
  background-size: 24px;
  background-position: 97% 50%;
  background-repeat: no-repeat;
}
.et-main .pagebanner-relative .unique_search_container .unique_search_input::placeholder {
  color: #b4bdd0;
  opacity: 1;
  font-size: 13px;
}
.et-main .pagebanner-relative .unique_search_container .unique_search_button {
  height: 38px;
  width: 66px;
  border: 1px solid var(--Original);
  border-left: none;
  background: var(--gradient2);
  color: #fff;
  cursor: pointer;
  border-radius: 0 2px 2px 0;
  outline: none;
}
.et-main .pagebanner-relative .unique_search_container .unique_search_button:hover {
  background: var(--gradient3);
  border: 1px solid var(--darker1);
  border-left: 1px solid var(--darker1);
}
.et-main .newsPage_left_list {
  width: 100%;
  padding: 0 100px 0 30px;
  margin-bottom: 6px;
  border-radius: 2px;
  background: linear-gradient(1turn, #fff, #f7f9fd);
  border: 2px solid #fff;
  box-shadow: 8px 8px 20px rgba(55, 99, 170, 0.1);
  position: relative;
}
.et-main .newsPage_left_list #dropdownArrow {
  display: block;
  position: absolute;
  right: 0;
  top: 0;
  width: 45px;
  height: 50px;
  background: #fff;
  box-shadow: -3px 0px 2px 0px rgba(0, 0, 0, 0.05);
  z-index: 1;
  cursor: pointer;
  transition: all 0.3s ease;
}
.et-main .newsPage_left_list #dropdownArrow:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transition: transform 0.6s;
  background: url("../../yibazhan/images/remoteL17093637255813.svg");
  background-size: cover;
  background-repeat: no-repeat;
  width: 14px;
  height: 14px;
  transform: translateY(-50%) translateX(-50%);
}
.et-main .newsPage_left_list #dropdownArrow.active:before {
  transform: translateY(-50%) translateX(-50%) rotate(180deg);
}
.et-main .newsPage_left_list #dropdownContent {
  height: 0;
  min-width: 150px;
  opacity: 0;
  border: none;
}
.et-main .newsPage_left_list #dropdownContent.active {
  position: absolute;
  right: 0;
  z-index: 1;
  overflow: hidden;
  height: fit-content;
  background: #fff;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  margin-top: 5px;
  padding: 10px 0;
  max-height: 260px;
  overflow-y: auto;
  border: 1px solid #f7f9fd;
  transition: all 0.3s ease;
  opacity: 1;
}
.et-main .newsPage_left_list #dropdownContent .newsPage_left_list_item {
  padding: 8px 20px;
  cursor: pointer;
  transition: background 0.3s ease;
  position: relative;
  white-space: nowrap;
  list-style: none;
  display: flex;
  justify-content: space-between;
}
.et-main .newsPage_left_list #dropdownContent .newsPage_left_list_item:hover {
  background: #f7f9fd;
}
.et-main .newsPage_left_list #dropdownContent .newsPage_left_list_item a {
  color: var(--txtcl);
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  width: 100%;
}
.et-main .newsPage_left_list #dropdownContent .newsPage_left_list_item.active::before {
  content: "";
  position: absolute;
  left: 20px;
  bottom: 0;
  width: 28px;
  height: 4px;
  background: var(--Original);
}
.et-main .newsPage_left_list #dropdownContent .newsPage_left_list_item.active a {
  color: var(--Original);
  font-weight: 500;
}
.et-main .newsPage_left_list ul {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  overflow: hidden;
  opacity: 0;
  transition: all 0.6s ease;
}
.et-main .newsPage_left_list ul .newsPage_left_list_item {
  padding-left: 20px;
  cursor: pointer;
  transition: background 0.3s ease;
  position: relative;
  white-space: nowrap;
}
.et-main .newsPage_left_list ul .newsPage_left_list_item:first-child {
  padding-left: 0;
}
.et-main .newsPage_left_list ul .newsPage_left_list_item:first-child.active::before {
  left: 0px;
}
.et-main .newsPage_left_list ul .newsPage_left_list_item a {
  display: block;
  color: var(--txtcl);
  font-size: 14px;
  font-weight: 400;
  line-height: 48px;
}
.et-main .newsPage_left_list ul .newsPage_left_list_item:hover a {
  color: var(--Original);
  font-weight: 500;
}
.et-main .newsPage_left_list ul .newsPage_left_list_item.active::before {
  content: "";
  position: absolute;
  left: 20px;
  bottom: 0;
  width: 28px;
  height: 4px;
  background: var(--Original);
}
.et-main .newsPage_left_list ul .newsPage_left_list_item.active a {
  color: var(--Original);
  font-weight: 500;
}
.et-main .newsPage_row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  padding: 40px 30px;
  border-radius: 2px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.01);
  width: 100%;
}
.et-main .newsPage_row .news-container {
  width: 100%;
}
.et-main .newsPage_row .news-list-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
}
.et-main .newsPage_row .news-list {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
}
.et-main .newsPage_row .news-list .news-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  width: 100%;
  border-bottom: 1px solid #e9eef3;
  padding-bottom: 20px;
}
.et-main .newsPage_row .news-list .news-item .news-link {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 20px;
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  gap: 12px;
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-item-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-item-text .news-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-item-text .news-title h1 {
  font-size: 16px;
  font-weight: 700;
  color: var(--txtcl);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-item-text .news-title h1:hover {
  color: var(--Original);
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-item-text .toppingwrap {
  width: 66px;
  height: 18px;
  background: url("../../yibazhan/images/remoteL17127272327088.svg");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-item-text .news-description {
  font-size: 14px;
  font-weight: 400;
  color: var(--txtdesc);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  cursor: text;
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-articleInformation {
  display: flex;
  gap: 12px;
  justify-content: flex-start;
  align-items: center;
  font-family: "codeB";
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-articleInformation .news-create-time {
  font-size: 12px;
  font-weight: 400;
  color: var(--txtcl);
  position: relative;
  padding-left: 18px;
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-articleInformation .news-create-time:before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  height: 14px;
  background-image: url("../../yibazhan/images/remoteL17091848258410.svg");
  background-size: cover;
  background-repeat: no-repeat;
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-articleInformation .news-category {
  font-size: 12px;
  font-weight: 400;
  color: var(--txtcl);
  position: relative;
  padding-left: 18px;
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-articleInformation .news-category:before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  height: 14px;
  background-image: url("../../yibazhan/images/remoteL17091848047440.svg");
  background-size: cover;
  background-repeat: no-repeat;
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-articleInformation .news-reads {
  font-size: 12px;
  font-weight: 400;
  color: var(--txtcl);
  padding-left: 18px;
  position: relative;
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-articleInformation .news-reads:before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  height: 14px;
  background-image: url("../../yibazhan/images/remoteL17091848739785.svg");
  background-size: cover;
  background-repeat: no-repeat;
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-img {
  width: 100%;
  max-width: 220px;
  height: 106px;
  overflow: hidden;
  border-radius: 1px;
}
.et-main .newsPage_row .news-list .news-item .news-link .news-item-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.et-main .newsPage_row .news-page-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 0 0;
  font-family: "codeB";
}
.et-main .newsPage_row .news-page-container .news-page-count {
  margin-right: 20px;
  color: #666;
}
.et-main .newsPage_row .news-page-container .news-page-list {
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
}
.et-main .newsPage_row .news-page-container .news-page-list .pagination-item {
  margin: 0 5px;
}
.et-main .newsPage_row .news-page-container .news-page-list .pagination-item:first-child {
  margin-left: 0;
}
.et-main .newsPage_row .news-page-container .news-page-list .pagination-item.disabled .pagination-link {
  pointer-events: none;
  color: #ccc;
}
.et-main .newsPage_row .news-page-container .news-page-list .pagination-item.active .pagination-link {
  background: var(--gradient3);
  color: #ffffff;
}
.et-main .newsPage_row .news-page-container .news-page-list .pagination-item .pagination-link {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  line-height: 28px;
  background-color: #f0f0f0;
  font-size: 12px;
  font-weight: 500;
  color: #333;
  text-decoration: none;
  border-radius: 2px;
  transition: background-color 0.3s, color 0.3s;
}
.et-main .newsPage_row .news-page-container .news-page-list .goTo {
  margin-left: 10px;
}
.et-main .newsPage_row .news-page-container .news-page-list .goTo .pagination-label {
  margin-right: 6px;
}
.et-main .newsPage_row .news-page-container .news-page-list .goTo .pagination-input {
  width: 52px;
  line-height: 26px;
  border: 1px solid #ccc;
  border-radius: 2px;
  text-align: center;
}
.et-main .newsPage_row .news-page-container .news-page-list .goTo .pagination-input:focus {
  border-color: #007bff;
  outline: none;
}
.et-main .news-right-row {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
}
.et-main .news-right-row .news_righ_item {
  width: 100%;
  padding: 20px;
  border-radius: 2px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.01);
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
  padding-bottom: 32px;
}
.et-main .news-right-row .news_righ_item .article-module-title {
  width: 100%;
  font-size: 14px;
  font-weight: 500;
  padding-bottom: 8px;
  color: var(--txtcl);
  border-bottom: 1px solid #e9eef3;
}
.et-main .news-right-row .create-article {
  width: 100%;
  display: flex;
  justify-content: space-around;
  flex-direction: row;
  align-items: center;
  padding: 10px 0;
  gap: 28px;
}
.et-main .news-right-row .create-article a {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.et-main .news-right-row .create-article a:hover img {
  -webkit-animation: bounce-down 0.6s linear 1;
  animation: bounce-down 0.6s linear 1;
}
.et-main .news-right-row .create-article a:hover span {
  color: var(--Original);
}
.et-main .news-right-row .create-article a img {
  width: 38px;
  height: 38px;
  margin-bottom: 3px;
}
.et-main .news-right-row .create-article a span {
  font-size: 12px;
  font-weight: 400;
  color: var(--txtcl);
}
.et-main .news-right-row .newsPage_left_activity_list {
  width: 100%;
  height: fit-content;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  overflow: hidden;
}
.et-main .news-right-row .newsPage_left_activity_list .newsPage_left_activity_list_item {
  width: 100%;
  min-height: 132px;
  position: relative;
}
.et-main .news-right-row .newsPage_left_activity_list .newsPage_left_activity_list_item .activity_list_item_img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
  border-radius: 2px;
}
.et-main .news-right-row .newsPage_left_activity_list .newsPage_left_activity_list_item .activity_list_item_img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 2px;
}
.et-main .news-right-row .newsPage_left_activity_list .newsPage_left_activity_list_item .activity_list_item_title {
  width: 100%;
  height: 100%;
  z-index: 2;
  position: absolute;
}
.et-main .news-right-row .newsPage_left_activity_list .newsPage_left_activity_list_item .activity_list_item_title a {
  background: hsla(219, 100%, 7.84%, 0.18);
  width: 100%;
  position: absolute;
  bottom: 0px;
  line-height: 24px;
  font-size: 13px;
  font-weight: 500;
  color: #fff;
  padding: 10px;
}
.et-main .news-right-row .newsPage_left_activity_list .newsPage_left_activity_list_item:hover .activity_list_item_img img {
  -webkit-animation: bounce-down 0.6s linear 1;
  animation: bounce-down 0.6s linear 1;
}
.et-main .news-right-row .newsPage_left_activity_list .newsPage_left_activity_list_item:hover .activity_list_item_title a {
  background: hsla(219, 100%, 7.84%, 0.4);
}
.et-main .news-right-row .newsPage_left_hot_list {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  margin-top: -10px;
  overflow: auto;
}
.et-main .news-right-row .newsPage_left_hot_list .newsPage_left_hot_list_item {
  width: 100%;
  height: fit-content;
}
.et-main .news-right-row .newsPage_left_hot_list .newsPage_left_hot_list_item:nth-child(-n+3) a:before {
  content: "";
  width: 24px;
  height: 24px;
  margin-right: 6px;
  display: inline-block;
  vertical-align: top;
  background-size: contain;
  background-repeat: no-repeat;
  margin-top: 7px;
}
.et-main .news-right-row .newsPage_left_hot_list .newsPage_left_hot_list_item:nth-child(1) a:before {
  background-image: url("../../yibazhan/images/sort1.svg");
}
.et-main .news-right-row .newsPage_left_hot_list .newsPage_left_hot_list_item:nth-child(2) a:before {
  background-image: url("../../yibazhan/images/sort2.svg");
}
.et-main .news-right-row .newsPage_left_hot_list .newsPage_left_hot_list_item:nth-child(3) a:before {
  background-image: url("../../yibazhan/images/sort3.svg");
}
.et-main .news-right-row .newsPage_left_hot_list .newsPage_left_hot_list_item:last-child a {
  border-bottom: none;
}
.et-main .news-right-row .newsPage_left_hot_list .newsPage_left_hot_list_item:nth-child(n+4) a:before {
  content: attr(data-index);
  width: 22px;
  height: 22px;
  margin-right: 6px;
  display: inline-block;
  vertical-align: top;
  border: 2px solid #fff;
  background: linear-gradient(1turn, #fff, #f3f5f8);
  box-shadow: 8px 8px 20px rgba(55, 99, 170, 0.06);
  border-radius: 50%;
  text-align: center;
  line-height: 22px;
  color: var(--txtcl);
  font-size: 14px;
  font-weight: 500;
  margin-top: 7px;
}
.et-main .news-right-row .newsPage_left_hot_list .newsPage_left_hot_list_item a {
  font-size: 14px;
  font-weight: 400;
  color: var(--txtdesc);
  width: 100%;
  line-height: 42px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  position: relative;
  border-bottom: 1px solid #f7f9fd;
}
.et-main .news-right-row .newsPage_left_hot_list .newsPage_left_hot_list_item a:hover {
  color: var(--Original);
}
.et-main .nullValue {
  width: 100%;
  background: #ffffff;
  cursor: not-allowed;
  background-color: #fff;
  background-image: none;
  border-radius: 1px;
  outline: 0;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}
.et-main .nullValue p {
  line-height: 40px;
  color: var(--Original);
  font-size: 14px;
  font-weight: 600;
}
.et-main .nullValue p:before {
  content: "";
  width: 22px;
  height: 22px;
  margin-right: 10px;
  display: inline-block;
  vertical-align: top;
  background: url("../../yibazhan/images/remoteL17092508285141.png");
  background-size: 100%;
  margin-top: 9px;
}
.et-main .breadcrumb {
  display: flex;
  margin-bottom: 20px;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
  white-space: nowrap;
  max-width: 90%;
}
.et-main .breadcrumb a {
  font-size: 14px;
  font-weight: 500;
  color: rgba(34, 34, 34, 0.4);
  text-decoration: none;
}
.et-main .breadcrumb a:hover {
  color: var(--Original);
  text-decoration: underline;
}
.et-main .breadcrumb span {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.et-main .social-icons {
  position: fixed;
  top: 50%;
  left: -50px;
  transform: translateY(calc(-50% + var(--navheight) / 2));
  display: flex;
  flex-direction: column;
  gap: 26px;
  transition: all 0.3s ease;
}
.et-main .social-icons a {
  display: flex;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border: 2px solid #fff;
  background: linear-gradient(1turn, #fff, #f3f5f8);
  box-shadow: 8px 8px 20px rgba(55, 99, 170, 0.06);
  border-radius: 50%;
  justify-content: center;
}
.et-main .social-icons a.BackToTop {
  opacity: 0;
  height: 0;
  transition: all 0.3s ease;
}
.et-main .social-icons a:hover {
  background: linear-gradient(1turn, #fff, #edf4ff);
  box-shadow: 8px 8px 20px rgba(55, 99, 170, 0.22);
}
.et-main .social-icons img {
  width: 20px; /* 或您希望的任何尺寸 */
  height: auto;
}
.et-main .news-txt-container {
  width: 100%;
  height: fit-content;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 2px;
  min-height: 400px;
}
.et-main .news-txt-container > div {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.et-main .news-txt-container .news-txt-title {
  width: 100%;
  margin-bottom: 20px;
  border-bottom: 1px solid #e9eef3;
}
.et-main .news-txt-container .news-txt-title h1 {
  font-size: 24px;
  font-weight: 600;
  color: var(--txtcl);
  line-height: 32px;
}
.et-main .news-txt-container .news-txt-title .news-txt-title-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  margin: 20px 0;
  border-radius: 4px;
  background: #f1f6ff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 4px 8px;
  margin-bottom: 12px;
  font-size: 12px;
  color: #0052d9;
  line-height: 20px;
  font-family: "codeB";
}
.et-main .news-txt-container .news-txt-title .news-txt-title-info .news-txt-title-info-author,
.et-main .news-txt-container .news-txt-title .news-txt-title-info .news-txt-title-info-read,
.et-main .news-txt-container .news-txt-title .news-txt-title-info .news-txt-title-info-time {
  font-size: 14px;
  font-weight: 400;
  color: var(--txtcl);
  line-height: 24px;
}
.et-main .news-txt-container .news-txt-content {
  margin: 0px 0 34px;
  font-size: 14px;
  font-weight: 400;
  color: var(--txtcl);
  line-height: 30px;
  word-break: break-all;
  word-wrap: break-word;
  overflow: hidden;
  overflow-x: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  /* 按钮样式 */
}
.et-main .news-txt-container .news-txt-content .toolbar {
  top: 16px;
}
.et-main .news-txt-container .news-txt-content .code-toolbar .toolbar-item .copy-to-clipboard-button span:hover {
  background: #3E4151;
}
.et-main .news-txt-container .news-txt-content .code-toolbar .toolbar-item span {
  height: 30px;
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.2);
  font-family: "codeB";
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  line-height: 22px;
  border-radius: 2px;
  background: #6A718E;
  color: #fff;
  margin-left: 8px;
  display: flex;
  align-items: center;
}
.et-main .news-txt-container .news-txt-content .code-toolbar .toolbar-item button {
  background: transparent;
  box-shadow: none;
}
.et-main .news-txt-container .news-txt-content > div {
  max-width: 100%;
  min-width: 100%;
}
.et-main .news-txt-container .news-txt-content .code-toolbar,
.et-main .news-txt-container .news-txt-content > pre {
  border: none;
}
.et-main .news-txt-container .news-txt-content .code-toolbar .code-toolbar,
.et-main .news-txt-container .news-txt-content > pre .code-toolbar {
  position: relative;
  border-radius: 4px;
}
.et-main .news-txt-container .news-txt-content .code-toolbar .code-toolbar pre,
.et-main .news-txt-container .news-txt-content > pre .code-toolbar pre {
  border: none;
}
.et-main .news-txt-container .news-txt-content .code-toolbar span,
.et-main .news-txt-container .news-txt-content > pre span {
  font-size: 14px;
  font-weight: 500;
}
.et-main .news-txt-container .news-txt-content img {
  max-width: 100%;
  min-width: auto;
  width: fit-content;
}
.et-main .news-txt-container .news-txt-content h1, .et-main .news-txt-container .news-txt-content h2, .et-main .news-txt-container .news-txt-content h3, .et-main .news-txt-container .news-txt-content h4, .et-main .news-txt-container .news-txt-content h5, .et-main .news-txt-container .news-txt-content h6 {
  color: #000000;
  font-weight: bold;
  position: relative;
}
.et-main .news-txt-container .news-txt-content h1 + h2, .et-main .news-txt-container .news-txt-content h1 + h3, .et-main .news-txt-container .news-txt-content h1 h2 + h3, .et-main .news-txt-container .news-txt-content h2 + h2, .et-main .news-txt-container .news-txt-content h2 + h3, .et-main .news-txt-container .news-txt-content h2 h2 + h3, .et-main .news-txt-container .news-txt-content h3 + h2, .et-main .news-txt-container .news-txt-content h3 + h3, .et-main .news-txt-container .news-txt-content h3 h2 + h3, .et-main .news-txt-container .news-txt-content h4 + h2, .et-main .news-txt-container .news-txt-content h4 + h3, .et-main .news-txt-container .news-txt-content h4 h2 + h3, .et-main .news-txt-container .news-txt-content h5 + h2, .et-main .news-txt-container .news-txt-content h5 + h3, .et-main .news-txt-container .news-txt-content h5 h2 + h3, .et-main .news-txt-container .news-txt-content h6 + h2, .et-main .news-txt-container .news-txt-content h6 + h3, .et-main .news-txt-container .news-txt-content h6 h2 + h3 {
  margin-top: 12px;
}
.et-main .news-txt-container .news-txt-content h1 {
  margin-top: 16px;
  margin-bottom: 12px;
  line-height: 28px;
  font-size: 18px;
}
.et-main .news-txt-container .news-txt-content h2 {
  margin-top: 16px;
  margin-bottom: 8px;
  line-height: 26px;
  font-size: 16px;
}
.et-main .news-txt-container .news-txt-content h3 {
  margin-top: 16px;
  margin-bottom: 8px;
  line-height: 24px;
  font-size: 14px;
}
.et-main .news-txt-container .news-txt-content p, .et-main .news-txt-container .news-txt-content div.paragraph {
  margin-top: 0;
  margin-bottom: 8px;
  min-height: 24px;
}
.et-main .news-txt-container .news-txt-content ul {
  margin: 0 0 16px;
  padding: 0;
  list-style: none;
}
.et-main .news-txt-container .news-txt-content ul > li {
  position: relative;
  margin-bottom: 4px;
  padding-left: 18px;
}
.et-main .news-txt-container .news-txt-content ul > li::before {
  content: "";
  display: block;
  position: absolute;
  box-sizing: border-box;
  left: 0;
  top: 9px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #9BA6B7;
}
.et-main .news-txt-container .news-txt-content ul ul, .et-main .news-txt-container .news-txt-content ul ol {
  margin: 8px 0;
}
.et-main .news-txt-container .news-txt-content ul ul > li::before, .et-main .news-txt-container .news-txt-content ul ol > li::before {
  width: 7px;
  height: 7px;
  border: 1px solid #99A7B9;
  background-color: transparent;
}
.et-main .news-txt-container .news-txt-content ol {
  margin-top: 0;
  margin-bottom: 16px;
  padding-left: 6px;
  list-style-type: decimal;
  list-style-position: inside;
}
.et-main .news-txt-container .news-txt-content ol ol {
  list-style-type: lower-roman;
}
.et-main .news-txt-container .news-txt-content ol ol ol {
  list-style-type: lower-alpha;
}
.et-main .news-txt-container .news-txt-content tbody {
  font-size: 14px;
}
.et-main .news-txt-container .news-txt-content table {
  border-collapse: collapse;
  width: 100%;
  margin-top: 12px;
  margin-bottom: 12px;
}
.et-main .news-txt-container .news-txt-content table th, .et-main .news-txt-container .news-txt-content table td {
  padding: 12px 16px;
  border: 1px solid #E1E6F0;
}
.et-main .news-txt-container .news-txt-content table th {
  background-color: #F5F7FA;
  color: #677489;
  text-align: left;
  font-weight: normal;
  word-break: keep-all;
}
.et-main .news-txt-container .news-txt-content blockquote {
  margin: 0 0 24px;
  padding: 0 15px;
  color: #777;
  border-left: 4px solid #ddd;
}
.et-main .news-txt-container .news-txt-content blockquote p {
  margin: 0;
}
.et-main .news-txt-container .news-txt-content blockquote + blockquote {
  margin-top: -24px;
}
.et-main .news-txt-container .news-txt-content blockquote > :first-child {
  margin-top: 0;
}
.et-main .news-txt-container .news-txt-content blockquote > :last-child {
  margin-bottom: 0;
}
.et-main .news-txt-container .news-txt-content code {
  display: inline-block;
  margin-left: 10px;
  margin-right: 10px;
  background-color: #F3F5F9;
  padding: 0 12px;
  color: #0ABF5B;
  line-height: 20px;
  border-radius: 4px;
}
.et-main .news-txt-container .news-txt-content pre {
  margin: 12px 0;
  padding: 16px 20px;
  color: #fff;
  background-color: #50556B;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  word-wrap: normal;
  border-radius: 4px;
}
.et-main .news-txt-container .news-txt-content pre > code {
  display: inline;
  padding: 0;
  background: transparent;
  color: inherit;
}
.et-main .news-txt-container .news-txt-content button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 16px;
}
.et-main .news-txt-container .news-txt-content button:hover {
  background-color: #0056b3;
}
.et-main .news-txt-container .news-txtpaging {
  width: 100%;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}
.et-main .news-txt-container .news-txtpaging .news-label-item {
  display: flex;
  gap: 10px;
  align-items: baseline;
  margin-bottom: 10px;
}
.et-main .news-txt-container .news-txtpaging .news-label-item span {
  font-size: 14px;
  font-weight: 500;
  color: var(--txtcl);
}
.et-main .news-txt-container .news-txtpaging .news-label-item a {
  padding: 5px 10px;
  border-radius: 1px;
  font-size: 12px;
  font-weight: 500;
  background-color: #f0f0f0;
  border: 1px solid #f0f0f0;
  color: #212529;
}
.et-main .news-txt-container .news-txtpaging .news-txtpaging-item {
  width: fit-content;
  padding: 10px 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.et-main .news-txt-container .news-txtpaging .news-txtpaging-item:hover a {
  border-bottom: 1px solid #0056ff;
}
.et-main .news-txt-container .news-txtpaging .news-txtpaging-item a {
  color: var(--Original);
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5;
  border: 1px solid transparent;
}

.newsPage_left_sticky {
  top: calc(var(--navheight) + 20px);
  position: sticky;
  height: fit-content;
  width: 100%;
}

.directoryStructure {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  margin-top: -20px;
  max-height: 380px;
  overflow: hidden;
  transition: all 0.3s ease;
}
.directoryStructure:hover {
  max-height: 600px;
  overflow-y: auto;
}
.directoryStructure .nothing {
  line-height: 24px;
  font-size: 13px;
  font-weight: 400;
  position: relative;
  padding: 8px 0;
  color: #9E9E9E;
}
.directoryStructure .catalogbox {
  list-style: none;
  padding: 0;
  background: #fff;
  width: 100%;
}
.directoryStructure .catalogbox li {
  cursor: pointer;
  border-bottom: 1px solid #eee;
  transition: background-color 0.3s ease;
  line-height: 24px;
  font-size: 13px;
  font-weight: 500;
  position: relative;
  padding: 8px 0;
  font-family: "codeB";
}
.directoryStructure .catalogbox li:hover, .directoryStructure .catalogbox li.current-catalog {
  color: var(--Original);
}
.directoryStructure .catalogbox li:before {
  transition: all 0.3s ease;
  content: "";
  display: block;
  position: absolute;
  box-sizing: border-box;
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 0;
  background: transparent;
}
.directoryStructure .catalogbox li.current-catalog:before {
  height: 12px;
  background: var(--Original);
}
.directoryStructure .catalogbox li:last-child {
  border-bottom: none;
}
.directoryStructure .catalogbox li.level-1 {
  padding-left: 5px;
  font-weight: 400;
}
.directoryStructure .catalogbox li.level-2 {
  padding-left: 10px;
  font-weight: 400;
}
.directoryStructure .catalogbox li.level-3 {
  padding-left: 15px;
  font-weight: 400;
}
.directoryStructure .catalogbox li.level-4 {
  padding-left: 20px;
  font-weight: 400;
}
.directoryStructure .catalogbox li.level-5 {
  padding-left: 25px;
  font-weight: 400;
}

@media (max-width: 768px) {
  .et-main {
    margin-top: calc(-100px - var(--wapnavheight) - 10px);
    margin-bottom: 38px;
  }
  .et-main .main-row {
    flex-direction: column;
    gap: 20px;
  }
  .et-main .main-row .et-left {
    width: 100%;
  }
  .et-main .main-row .et-left .newsPage_row {
    padding: 20px;
  }
  .et-main .main-row .et-right {
    display: none;
  }
  .et-main .pagebanner-relative {
    height: 138px;
    display: flex;
    align-items: center;
    flex-direction: row;
  }
  .et-main .pagebanner-relative .pagebanner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    gap: 12px;
  }
  .et-main .pagebanner-relative .pagebanner-text .pagebanner-title {
    font-size: 18px;
  }
  .et-main .pagebanner-relative .pagebanner-text .searchTerms {
    display: none;
  }
  .et-main .pagebanner-relative .unique_search_container form .unique_search_input {
    width: 100%;
  }
  .et-main .pagebanner-relative .unique_search_container form .unique_search_button {
    width: 100px;
  }
  .et-main .newsPage_left_list {
    padding: 0 12px;
  }
  .et-main .newsPage_row .news-container {
    width: 100%;
  }
  .et-main .newsPage_row .news-list-container {
    gap: 10px;
  }
  .et-main .newsPage_row .news-list .news-item {
    flex-direction: column;
    gap: 10px;
    padding-bottom: 10px;
  }
  .et-main .newsPage_row .news-list .news-item .news-link {
    flex-direction: column;
    gap: 10px;
  }
  .et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-item-text .news-title {
    font-size: 16px;
  }
  .et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-item-text .news-description {
    font-size: 14px;
  }
  .et-main .newsPage_row .news-list .news-item .news-link .news-item-container .news-articleInformation {
    gap: 10px;
    font-size: 12px;
  }
  .et-main .newsPage_row .news-list .news-item .news-link .news-item-img {
    max-width: 100%;
    height: 126px;
  }
  .et-main .newsPage_row .news-page-container .news-page-list {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    gap: 10px;
  }
  .et-main .newsPage_row .news-page-container .news-page-list .pagination-item {
    margin: 0;
  }
  .et-main.articleContent {
    margin-top: calc(-100px - var(--wapnavheight) - 10px);
  }
  .et-main.articleContent .et-left .newsPage_row {
    padding: 12px;
  }
  .et-main.articleContent .breadcrumb {
    margin-bottom: 12px;
  }
  .et-main.articleContent .breadcrumb a, .et-main.articleContent .breadcrumb span {
    font-size: 14px;
  }
  .et-main.articleContent .news-container .news-txt-container {
    min-height: 200px;
  }
  .et-main.articleContent .news-container .news-txt-container .news-txt-title {
    margin-bottom: 6px;
  }
  .et-main.articleContent .news-container .news-txt-container .news-txt-title h1 {
    font-size: 18px;
  }
  .et-main.articleContent .news-container .news-txt-container .news-txt-title .news-txt-title-info {
    font-size: 12px;
    margin: 12px 0;
    gap: 6px;
    flex-wrap: wrap;
  }
  .et-main.articleContent .social-icons {
    display: none;
  }
}

/*# sourceMappingURL=news.css.map */
