<?php

use common\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/** @var yii\web\View $this */
/** @var addons\VymServer\common\models\ServerOrder $model */
/** @var array $orderFullInfo */
/** @var array $configNames */

$this->title                   = '订单详情 #' . $model->id;
$this->params['breadcrumbs'][] = ['label' => '服务器订单', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);

// 从 orderFullInfo 中提取数据，使用 ArrayHelper::getValue 避免报错
$orderInfo   = ArrayHelper::getValue($orderFullInfo, 'order_info', []);
$paymentInfo = ArrayHelper::getValue($orderFullInfo, 'payment_info', []);
$productInfo = ArrayHelper::getValue($orderFullInfo, 'product_info', []);
$cartData    = ArrayHelper::getValue($orderFullInfo, 'cart_data', []);

// 从购物车数据中提取配置信息
$cartConfig = ArrayHelper::getValue($cartData, 'cart_config.config', []);
$cartParams = ArrayHelper::getValue($cartData, 'params', []);
?>
<div class="row">
    <div class="col-12">
        <div class="box">
            <div class="box-header">
                <h3 class="box-title"><?= Html::encode($this->title) ?></h3>
                <div class="box-tools pull-right">
                    <?= Html::a('<i class="fa fa-arrow-left"></i> 返回列表', ['index'], ['class' => 'btn btn-default btn-sm']) ?>
                </div>
            </div>
            <div class="box-body">

                <!-- 基本订单信息 -->
                <div class="row">
                    <div class="col-md-6">
                        <h4>基本信息</h4>
                        <?= DetailView::widget([
                            'model'      => $model,
                            'options'    => ['class' => 'table table-striped table-bordered detail-view'],
                            'attributes' => [
                                'id:text:订单ID',
                                'uid:text:会员ID',
                                [
                                    'label' => '订单号',
                                    'value' => ArrayHelper::getValue($orderInfo, 'order_id', '-'),
                                ],
                                [
                                    'label' => '订单类型',
                                    'value' => ArrayHelper::getValue($orderInfo, 'order_type', '-'),
                                ],
                                [
                                    'label'  => '订单状态',
                                    'value'  => ArrayHelper::getValue($orderInfo, 'order_status', '-'),
                                    'format' => 'raw',
                                    'value'  => function ($model) use ($orderInfo) {
                                        $status     = ArrayHelper::getValue($orderInfo, 'order_status', '-');
                                        $labelClass = $status == '已支付' ? 'success' : 'warning';
                                        return "<span class=\"label label-{$labelClass}\">" . Html::encode($status) . "</span>";
                                    },
                                ],
                                [
                                    'label'  => '支付状态',
                                    'format' => 'raw',
                                    'value'  => function ($model) use ($orderInfo) {
                                        $status     = ArrayHelper::getValue($orderInfo, 'order_pay_status', '-');
                                        $labelClass = $status == '已支付' ? 'success' : 'danger';
                                        return "<span class=\"label label-{$labelClass}\">" . Html::encode($status) . "</span>";
                                    },
                                ],
                                'created_at:text:创建时间',
                                'updated_at:text:更新时间',
                            ],
                        ]) ?>

                        <h4>购买参数</h4>
                        <?= DetailView::widget([
                            'model'      => $cartParams,
                            'options'    => ['class' => 'table table-striped table-bordered detail-view'],
                            'attributes' => [
                                [
                                    'label' => '服务器名称',
                                    'value' => ArrayHelper::getValue($cartParams, 'server_name', '-'),
                                ],
                                [
                                    'label' => '购买周期',
                                    'value' => ArrayHelper::getValue($cartParams, 'expire', '-') . '个月',
                                ],
                                [
                                    'label' => '购买数量',
                                    'value' => ArrayHelper::getValue($cartParams, 'buynum', '-') . '台',
                                ],
                                [
                                    'label' => '产品ID',
                                    'value' => ArrayHelper::getValue($cartParams, 'product_id', '-'),
                                ],
                                [
                                    'label' => '商品ID',
                                    'value' => ArrayHelper::getValue($cartParams, 'goods_id', '-'),
                                ],
                                [
                                    'label' => '服务器类型ID',
                                    'value' => ArrayHelper::getValue($cartParams, 'server_type_id', '-'),
                                ],
                                [
                                    'label'   => '购买备注',
                                    'value'   => ArrayHelper::getValue($cartParams, 'remark', ''),
                                    'visible' => !empty(ArrayHelper::getValue($cartParams, 'remark')),
                                ],
                            ],
                        ]) ?>
                    </div>

                    <div class="col-md-6">
                        <h4>价格信息</h4>
                        <?= DetailView::widget([
                            'model'      => $orderInfo,
                            'options'    => ['class' => 'table table-striped table-bordered detail-view'],
                            'attributes' => [
                                [
                                    'label' => '原价',
                                    'value' => '¥' . number_format(ArrayHelper::getValue($orderInfo, 'order_original_price', 0), 2),
                                ],
                                [
                                    'label' => '更新价格',
                                    'value' => '¥' . number_format(ArrayHelper::getValue($orderInfo, 'order_update_price', 0), 2),
                                ],
                                [
                                    'label' => '实际金额',
                                    'value' => '¥' . number_format(ArrayHelper::getValue($orderInfo, 'order_amount_money', 0), 2),
                                ],
                                [
                                    'label' => '单价',
                                    'value' => '¥' . number_format(ArrayHelper::getValue($cartData, 'cart_price_single', 0), 2),
                                ],
                                [
                                    'label' => '总价',
                                    'value' => '¥' . number_format(ArrayHelper::getValue($cartData, 'cart_price_total', 0), 2),
                                ],
                                [
                                    'label' => '优惠金额',
                                    'value' => '¥' . number_format(ArrayHelper::getValue($cartData, 'discount_price', 0), 2),
                                ],
                            ],
                        ]) ?>

                        <h5>支付信息</h5>
                        <?= DetailView::widget([
                            'model'      => $paymentInfo,
                            'options'    => ['class' => 'table table-striped table-bordered detail-view'],
                            'attributes' => [
                                [
                                    'label' => '支付单号',
                                    'value' => ArrayHelper::getValue($paymentInfo, 'general_payorder_number', '-'),
                                ],
                                [
                                    'label' => '支付金额',
                                    'value' => '¥' . number_format(ArrayHelper::getValue($paymentInfo, 'general_pay_money', 0), 2),
                                ],
                                [
                                    'label' => '支付状态',
                                    'value' => ArrayHelper::getValue($paymentInfo, 'general_pay_lock', '-'),
                                ],
                                [
                                    'label' => '审核状态',
                                    'value' => ArrayHelper::getValue($paymentInfo, 'general_payinfo_review', '-'),
                                ],
                                [
                                    'label' => '支付时间',
                                    'value' => ArrayHelper::getValue($paymentInfo, 'general_pay_time') ?
                                        date('Y-m-d H:i:s', strtotime(ArrayHelper::getValue($paymentInfo, 'general_pay_time'))) : '未支付',
                                ],
                            ],
                        ]) ?>
                    </div>
                </div>

                <!-- 产品配置信息 -->
                <div class="row">
                    <div class="col-md-6">
                        <h4>产品配置</h4>
                        <?= DetailView::widget([
                            'model'      => $cartConfig,
                            'options'    => ['class' => 'table table-striped table-bordered detail-view'],
                            'attributes' => [
                                [
                                    'label' => '产品名称',
                                    'value' => ArrayHelper::getValue($productInfo, 'name', '-'),
                                ],
                                [
                                    'label' => 'CPU',
                                    'value' => ArrayHelper::getValue($cartConfig, 'cpu', '-'),
                                ],
                                [
                                    'label' => '内存',
                                    'value' => ArrayHelper::getValue($cartConfig, 'ram', '-'),
                                ],
                                [
                                    'label' => '硬盘',
                                    'value' => ArrayHelper::getValue($cartConfig, 'hdd', '-'),
                                ],
                                [
                                    'label' => '带宽',
                                    'value' => ArrayHelper::getValue($cartConfig, 'configbandwidth', '-'),
                                ],
                                [
                                    'label' => 'IP数量',
                                    'value' => ArrayHelper::getValue($cartConfig, 'ipnumber', '-'),
                                ],
                                [
                                    'label' => '操作系统',
                                    'value' => ArrayHelper::getValue($cartConfig, 'operatsystem', '-'),
                                ],
                                [
                                    'label'   => '防御',
                                    'value'   => ArrayHelper::getValue($cartConfig, 'defense', ''),
                                    'visible' => !empty(ArrayHelper::getValue($cartConfig, 'defense')),
                                ],
                                [
                                    'label'   => '显卡',
                                    'value'   => ArrayHelper::getValue($cartConfig, 'card', ''),
                                    'visible' => !empty(ArrayHelper::getValue($cartConfig, 'card')),
                                ],
                            ],
                        ]) ?>
                    </div>
                </div>

                <!-- 购物车详情 -->
                <div class="row">
                    <div class="col-md-6">
                        <h4>购物车信息</h4>
                        <?= DetailView::widget([
                            'model'      => $cartData,
                            'options'    => ['class' => 'table table-striped table-bordered detail-view'],
                            'attributes' => [
                                [
                                    'label' => '用户ID',
                                    'value' => ArrayHelper::getValue($cartData, 'user_id', '-'),
                                ],
                                [
                                    'label' => '购物车ID',
                                    'value' => ArrayHelper::getValue($cartData, 'cart_id', '-'),
                                ],
                                [
                                    'label' => '购买数量',
                                    'value' => ArrayHelper::getValue($cartData, 'cart_num', '-') . '台',
                                ],
                                [
                                    'label' => '单价',
                                    'value' => '¥' . number_format(ArrayHelper::getValue($cartData, 'cart_price_single', 0), 2),
                                ],
                                [
                                    'label' => '总价',
                                    'value' => '¥' . number_format(ArrayHelper::getValue($cartData, 'cart_price_total', 0), 2),
                                ],
                                [
                                    'label' => '单位价格',
                                    'value' => '¥' . number_format(ArrayHelper::getValue($cartData, 'unit_price', 0), 2),
                                ],
                                [
                                    'label' => '优惠价格',
                                    'value' => '¥' . number_format(ArrayHelper::getValue($cartData, 'discount_price', 0), 2),
                                ],
                                [
                                    'label'   => '购物车备注',
                                    'value'   => ArrayHelper::getValue($cartData, 'cart_remark', ''),
                                    'visible' => !empty(ArrayHelper::getValue($cartData, 'cart_remark')),
                                ],
                                [
                                    'label'   => '优惠券',
                                    'value'   => ArrayHelper::getValue($cartData, 'coupon_sn', ''),
                                    'visible' => !empty(ArrayHelper::getValue($cartData, 'coupon_sn')),
                                ],
                            ],
                        ]) ?>
                    </div>
                    <div class="col-md-6">
                        <h4>原始数据</h4>
                        <pre style="max-height: 260px; overflow-y: auto; font-size: 12px; border: 1px solid #ccc; padding: 10px;">
                            <?= Html::encode(json_encode($cartData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?>
                        </pre>
                    </div>
                </div>

                <!-- 备注信息 -->
                <?php if ($model->remark): ?>
                    <div class="row">
                        <div class="col-md-12">
                            <h4>备注</h4>
                            <?= DetailView::widget([
                                'model'      => $model,
                                'options'    => ['class' => 'table table-striped table-bordered detail-view'],
                                'attributes' => [
                                    'remark:ntext:备注',
                                ],
                            ]) ?>
                        </div>
                    </div>
                <?php endif; ?>

            </div>
        </div>
    </div>
</div>
