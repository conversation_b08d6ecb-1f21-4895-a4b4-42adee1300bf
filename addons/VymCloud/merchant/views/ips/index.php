<?php

use addons\VymCloud\common\models\CloudIps;
use common\helpers\ArrayHelper;
use common\helpers\Html;
use common\helpers\Url;
use yii\grid\ActionColumn;
use yii\grid\GridView;
use yii\widgets\ActiveForm;
use kartik\select2\Select2;
use common\enums\StatusEnum;
use addons\VymCloud\common\enums\IpsEnum;
use common\enums\AppEnum;

/** @var yii\web\View $this */
/** @var yii\data\ActiveDataProvider $dataProvider */
/** @var common\models\base\SearchModel $searchModel */
/** @var array $queryParams 搜索参数 */
/** @var array $clusterList 集群列表 */

$this->title                   = 'IP资源池';
$this->params['breadcrumbs'][] = $this->title;

//处理搜索条件
$type     = ArrayHelper::getValue($queryParams, 'SearchModel.type');
$isInside = ArrayHelper::getValue($queryParams, 'SearchModel.is_inside');
?>
    <div class="row">
        <div class="col-sm-12">
            <div class="nav-tabs-custom">
                <ul class="nav nav-tabs">
                    <li class="<?= $type === null && $isInside === null ? 'active' : '' ?>">
                        <a href="<?= Url::to(['ips/index']) ?>"> 全部</a>
                    </li>
                    <li class="<?= $type == 'ip4' && $isInside == 0 ? 'active' : '' ?>">
                        <a href="<?= Url::to(['ips/index', 'SearchModel[type]' => 'ip4', 'SearchModel[is_inside]' => 0]) ?>">
                            外网IPv4</a>
                    </li>
                    <li class="<?= $type == 'ip4' && $isInside == 1 ? 'active' : '' ?>">
                        <a href="<?= Url::to(['ips/index', 'SearchModel[type]' => 'ip4', 'SearchModel[is_inside]' => 1]) ?>">
                            内网IPv4</a>
                    </li>
                    <li class="<?= $type == 'ip6' && $isInside == 0 ? 'active' : '' ?>">
                        <a href="<?= Url::to(['ips/index', 'SearchModel[type]' => 'ip6', 'SearchModel[is_inside]' => 0]) ?>">
                            外网IPv6</a>
                    </li>
                    <li class="<?= $type == 'ip6' && $isInside == 1 ? 'active' : '' ?>">
                        <a href="<?= Url::to(['ips/index', 'SearchModel[type]' => 'ip6', 'SearchModel[is_inside]' => 1]) ?>">
                            内网IPv6</a>
                    </li>
                    <li class="pull-right">
                        <div class="box-tools">
                            <?= Html::create(['edit']); ?>
                            <?= Html::a('批量创建', ['batch-add'], ['class' => 'btn btn-success btn-sm']) ?>
                            <?= Html::a('导出Excel', ['export'] + Yii::$app->request->get(), ['class' => 'btn btn-info btn-sm']) ?>
                        </div>
                    </li>
                </ul>
                <div class="tab-content">
                    <div class="active tab-pane">
                        <?= GridView::widget([
                            'dataProvider' => $dataProvider,
                            'filterModel'  => $searchModel,
                            'tableOptions' => [
                                'class'            => 'table table-hover rf-table',
                                'fixedNumber'      => 2,
                                'fixedRightNumber' => 1,
                            ],
                            'columns'      => [
                                [
                                    'class'         => 'yii\grid\CheckboxColumn',
                                    'name'          => 'selection',
                                    'headerOptions' => ['class' => 'col-md-1'],
                                ],
                                ['attribute' => 'id', 'headerOptions' => ['class' => 'col-md-1', 'style' => 'width: 60px;']],
                                [
                                    'label'         => '所属商户',
                                    'headerOptions' => ['class' => 'col-md-1'],
                                    'format'        => 'raw',
                                    'visible'       => Yii::$app->id == AppEnum::BACKEND,
                                    'value'         => function ($model) {
                                        if ($model->merchant && $mId = $model->merchant->id) {
                                            if ($mTitle = $model->merchant->title) {
                                                return Html::tag('span', $mTitle, ['class' => 'badge badge-primary']);
                                            }
                                            return Html::tag('span', "ID:{$mId}", ['class' => 'badge badge-primary']);
                                        }
                                        return Html::tag('span', '平台直属', ['class' => 'badge badge-secondary']);
                                    },
                                    'filter'        => Yii::$app->id == AppEnum::BACKEND ? Html::activeDropDownList(
                                        $searchModel,
                                        'merchant_id',
                                        ArrayHelper::map(Yii::$app->services->merchant->findAllByCondition([]), 'id', 'title'),
                                        [
                                            'prompt' => '全部',
                                            'class'  => 'form-control',
                                        ]
                                    ) : false,
                                ],
                                ['attribute' => 'ip', 'headerOptions' => ['class' => 'col-md-2']],
                                [
                                    'attribute'     => 'cluster_id',
                                    'headerOptions' => ['class' => 'col-md-1'],
                                    'value'         => function ($model) use ($clusterList) {
                                        return $clusterList[$model->cluster_id] ?? '-';
                                    },
                                    'filter'        => Html::activeDropDownList($searchModel, 'cluster_id', $clusterList, [
                                        'prompt' => '全部',
                                        'class'  => 'form-control',
                                    ]),
                                ],
                                [
                                    'attribute'     => 'server_id',
                                    'format'        => 'raw',
                                    'headerOptions' => ['class' => 'col-md-1'],
                                    'value'         => function ($model) {
                                        $server  = $model->server;
                                        $id      = $model->server_id;
                                        $htmlStr = '-';
                                        if ($server) {
                                            $str     = "{$server->name}(ID:{$server->id})";
                                            $htmlStr = Html::tag('span', $str, ['class' => 'badge badge-primary']);
                                        } else if ($id) {
                                            $htmlStr = Html::tag('span', "ID: {$id}", ['class' => 'badge badge-info']);
                                        }
                                        return $htmlStr;
                                    },
                                ],
                                [
                                    'attribute'     => 'type',
                                    'format'        => 'raw',
                                    'headerOptions' => ['class' => 'col-md-1'],
                                    'value'         => function ($model) {
                                        $type      = $model->type == 'ip4' ? 'IPV4' : 'IPV6';
                                        $typeClass = $model->type == 'ip4' ? 'primary' : 'success';
                                        return Html::tag('span', $type, ['class' => 'label label-' . $typeClass]);
                                    },
                                    'filter'        => Html::activeDropDownList($searchModel, 'type', ['' => '全部', 'ip4' => 'IPV4', 'ip6' => 'IPV6',], ['class' => 'form-control']),
                                ],
                                [
                                    'attribute'     => 'is_inside',
                                    'format'        => 'raw',
                                    'headerOptions' => ['class' => 'col-md-1'],
                                    'value'         => function ($model) {
                                        $type      = $model->is_inside == 1 ? '内网IP' : '外网IP';
                                        $typeClass = $model->is_inside == 1 ? 'success' : 'primary';
                                        return Html::tag('span', $type, ['class' => 'label label-' . $typeClass]);
                                    },
                                    'filter'        => Html::activeDropDownList($searchModel, 'is_inside', ['' => '全部', '1' => '内网IP', '0' => '外网IP'], ['class' => 'form-control']),
                                ],
                                [
                                    'attribute'     => 'is_use',
                                    'format'        => 'raw',
                                    'headerOptions' => ['class' => 'col-md-1'],
                                    'value'         => function ($model) {
                                        $statusMap    = IpsEnum::getMap();
                                        $statusColors = IpsEnum::getStatusColors();
                                        $use          = $statusMap[$model->is_use] ?? '未知';
                                        $useClass     = $statusColors[$model->is_use] ?? '';
                                        return Html::tag(
                                            'span',
                                            $use,
                                            ['class' => "label label-{$useClass}"]
                                        );
                                    },
                                    'filter'        => Html::activeDropDownList($searchModel, 'is_use', IpsEnum::getMap(), ['prompt' => '全部', 'class' => 'form-control']),
                                ],
                                [
                                    'header'        => "操作",
                                    'headerOptions' => ['class' => 'col-md-1'],
                                    'class'         => 'yii\grid\ActionColumn',
                                    'template'      => '{edit} {delete}',
                                    'buttons'       => [
                                        'edit'   => function ($url, $model, $key) {
                                            return Html::edit(['edit', 'id' => $model->id]);
                                        },
                                        'delete' => function ($url, $model, $key) {
                                            return Html::delete(['delete', 'id' => $model->id]);
                                        },
                                    ],
                                ],
                            ],
                        ]); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php
$js = <<<JS
    // 批量删除
    $('#batch-delete').click(function() {
        var keys = $('#grid').yiiGridView('getSelectedRows');
        if (keys.length == 0) {
            alert('请选择要删除的记录');
            return;
        }
        if (confirm('确定要删除选中的记录吗？')) {
            $.post('batch-delete', {ids: keys}, function(response) {
                location.reload();
            });
        }
    });
JS;
$this->registerJs($js);
?>