<?php

use yii\widgets\LinkPager;
// $this->context->layout = true; //不使用布局
use yii\helpers\Html;
error_reporting(0);
?>


<?php $this->beginBlock('content') ?>
<?php $node = \Yii::$app->session['auth']['node']; ?>

<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs" id="breadcrumbs" style="background-color:#ffffff">
            <div class="row">
                <div class="clearfix">
                    <div class="pull-right tableTools-container"></div>
                </div>
            </div>
            <div class="page-content">
                <div class="row" style="margin-bottom: 10px;">
                    <form action="">
                        <input type="hidden" name="r" value="user-member/select">
                        <div class="col-xs-12">
                            <div style="float: left;margin-right: 6px;">
                                <input type="text" class="form-control search-query" name="email" value='<?php echo Html::encode(Yii::$app->controller->get('email')) ?>'  placeholder="用户名">
                            </div>
                            <div style="float: left;margin-right: 6px;">
                                <input type="text" class="form-control search-query" name="uname" value='<?php echo Html::encode(Yii::$app->controller->get('uname')) ?>'  placeholder="用户昵称">
                            </div>
                            <div>
                                <button type="submit" class="btn btn-white btn-primary" style="margin-left:15px">
                                    <span class="fa fa-search"></span>
                                    搜索
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <form action="" id="checkbox">
                    <table id="simple-table" width="100%" class="table table-striped table-bordered table-hover">
                        <thead>
                        <tr role="row">
                            <th></th>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>昵称</th>
                            <th>联系人</th>
                            <th>备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php if(empty($arrRes)):?>
                            <tr><td colspan="5" style=" text-align:center;">暂无数据</td></tr>
                        <?php else:?>
                            <?php foreach ($arrRes as $value):?>
                                <tr role="row" class="odd">
                                    <td style="vertical-align:middle">
                                        <div class="iradio_square-green checked">
                                            <input type="radio" name="uid" value="<?php echo $value['u_id'] ?>" data-name="<?php echo $value['email']?>" data-nickname="<?php echo $value['uname']?>" class="i-checks" >

                                        </div>
                                    </td>
                                    <td><?php echo $value['u_id'] ?></td>
                                    <td><?php echo empty($value['email'])?(empty($value['mobile'])?$value['username']:$value['mobile']):$value['email'] ?></td>
                                    <td><?php echo $value['uname']?></td>
                                    <td><?php echo $value['truename']?></td>
                                    <td><?php echo $value['remarks']?></td>
                                </tr>
                            <?php endforeach;?>
                        <?php endif;?>
                        </tbody>
                    </table>
                    <div class="row">
                        <div class="col-xs-3">
                            <div class="dataTables_info" id="dynamic-table_info" role="status" aria-live="polite">共有<?php echo $iCount;?>条记录</div>
                        </div>
                        <div class="col-xs-9 pagination1" style="text-align: right;">
                            <?php
                            echo LinkPager::widget([
                                'pagination' => $page,
                                'firstPageLabel'=>"首页",
                                'prevPageLabel'=>'上一页',
                                'nextPageLabel'=>'下一页',
                                'lastPageLabel'=>'末页',
                            ]);
                            ?>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<style>
    input[type="radio"] + label::before {
        content: "\a0"; /*不换行空格*/
        display: inline-block;
        vertical-align: middle;
        font-size: 18px;
        width: 1em;
        height: 1em;
        margin-right: .4em;
        border-radius: 50%;
        border: 1px solid #01cd78;
        text-indent: .15em;
        line-height: 1;
    }
</style>

<!--[if !IE]> -->
<script src="/ace-iframe/assets/js/jquery-2.1.4.min.js"></script>
<!-- <![endif]-->
<!--[if IE]>
<script src="/ace-iframe/assets/js/jquery-1.11.3.min.js"></script>
<![endif]-->
<script type="text/javascript">
    $(function () {
        //And for the first simple table, which doesn't have TableTools or dataTables
        //select/deselect all rows according to table header checkbox
        var active_class = 'active';
        $('#simple-table > thead > tr > th input[type=checkbox]').eq(0).on('click', function(){
            var th_checked = this.checked;//checkbox inside "TH" table header
            $(this).closest('table').find('tbody > tr').each(function(){
                var row = this;
                if(th_checked) $(row).addClass(active_class).find('input[type=checkbox]').eq(0).prop('checked', true);
                else $(row).removeClass(active_class).find('input[type=checkbox]').eq(0).prop('checked', false);
            });
        });
        //select/deselect a row when the checkbox is checked/unchecked
        $('#simple-table').on('click', 'td input[type=checkbox]' , function(){
            var $row = $(this).closest('tr');
            if($row.is('.detail-row ')) return;
            if(this.checked) $row.addClass(active_class);
            else $row.removeClass(active_class);
        });
        /***************/
        $('.show-details-btn').on('click', function(e) {
            e.preventDefault();
            $(this).closest('tr').next().toggleClass('open');
            $(this).find(ace.vars['.icon']).toggleClass('fa-angle-double-down').toggleClass('fa-angle-double-up');
        });
    })
</script>
<?php $this->endBlock(); ?>