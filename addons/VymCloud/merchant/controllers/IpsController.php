<?php

namespace addons\VymCloud\merchant\controllers;

use addons\VymCloud\common\enums\IpsEnum;
use addons\VymCloud\common\models\CloudIps;
use yii\data\ActiveDataProvider;
use addons\VymCloud\merchant\controllers\BaseController;
use common\exceptions\CustomException;
use common\helpers\ArrayHelper;
use common\models\base\SearchModel;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;
use yii\web\UploadedFile;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Yii;

/**
 * IpsController implements the CRUD actions for CloudIps model.
 */
class IpsController extends BaseController
{

    /**
     * @var CloudIps
     */
    public $modelClass = CloudIps::class;

    /**
     * 首页
     *
     * @return string
     * @throws \yii\web\NotFoundHttpException
     */
    public function actionIndex()
    {

        $queryParams = $this->request->queryParams;
        $searchModel = new SearchModel([
            'model'                  => $this->modelClass,
            'scenario'               => 'default',
            'relations'               => ['merchant' => []],
            'partialMatchAttributes' => ['ip', 'gateway', 'mask', 'desc'], // 模糊查询
            'defaultOrder'           => [
                'id' => SORT_DESC,
            ],
            'pageSize'               => $this->pageSize,
        ]);

        $dataProvider = $searchModel->search($queryParams);
        $this->addQueryParam($dataProvider);
        return $this->render($this->action->id, [
            'dataProvider' => $dataProvider,
            'searchModel'  => $searchModel,
            'queryParams'  => $queryParams,
            'clusterList'  => Yii::$app->vymCloudService->cluster->getDropDown(),
        ]);
    }


    /**
     * 新增/编辑
     *
     * @param int|null $id IP ID
     *
     * @return string|Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionEdit($id = null)
    {

        $model = $this->findModel($id);

        if ($this->request->isPost && $model->load($this->request->post())) {
            return $model->save()
                ? $this->message('保存成功', $this->redirect(['index']))
                : $this->message($this->getError($model), $this->redirect(['index']), 'error');
        }

        // dd($model);
        return $this->render($this->action->id, [
            'model'        => $model,
            'dropDownList' => [
                'clusters' => Yii::$app->vymCloudService->cluster->getDropDown(),
            ],
        ]);
    }

    /**
     * 新增/编辑
     *
     * @param int|null $id IP ID
     *
     * @return string|Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionBatchAdd()
    {
        $model = $this->findModel(null); // 新建空模型

        if ($this->request->isPost) {
            $post = $this->request->post();
            $ipLines = explode("\n", ArrayHelper::getValue($post, 'CloudIps.ip'));
            $model->load($post);
            // 移除 ip 字段，避免每个模型实例都保存相同的 ip 字段
            unset($model->ip);
            $transaction = Yii::$app->db->beginTransaction();

            try {
                foreach ($ipLines as $ip) {
                    $ip = trim($ip);
                    if (empty($ip)) continue;
                    $newModel = new CloudIps();
                    $newModel->attributes = $model->attributes;
                    $newModel->ip = $ip;

                    if (!$newModel->validate()) {
                        throw new CustomException('IP: ' . $ip . ' 验证失败: ' . json_encode($newModel->getErrors()));
                    }
                    if (!$newModel->save()) {
                        throw new CustomException('IP: ' . $ip . ' 保存失败');
                    }
                }

                $transaction->commit();
                return $this->message('批量保存成功', $this->redirect(['index']));
            } catch (\Exception $e) {
                $transaction->rollBack();
                return $this->message('批量保存失败: ' . $e->getMessage(), $this->redirect(['index']), 'error');
            }
        }

        return $this->render('batch_add', [
            'model' => $model,
            'dropDownList' => [
                'clusters' => Yii::$app->vymCloudService->cluster->getDropDown(),
            ],
        ]);
    }

    /**
     * 导出IP资源
     */
    public function actionExport()
    {
        $query = CloudIps::find();

        // 添加搜索条件
        if ($this->request->get('cluster_id')) {
            $query->andWhere(['cluster_id' => $this->request->get('cluster_id')]);
        }
        if ($this->request->get('is_use') !== null) {
            $query->andWhere(['is_use' => $this->request->get('is_use')]);
        }
        if ($this->request->get('is_inside') !== null) {
            $query->andWhere(['is_inside' => $this->request->get('is_inside')]);
        }

        $ips = $query->all();

        $spreadsheet = new Spreadsheet();
        $sheet       = $spreadsheet->getActiveSheet();

        // 设置表头
        $sheet->setCellValue('A1', '集群ID');
        $sheet->setCellValue('B1', 'IP地址');
        $sheet->setCellValue('C1', '网关');
        $sheet->setCellValue('D1', '子网掩码');
        $sheet->setCellValue('E1', 'VLAN');
        $sheet->setCellValue('F1', '是否内网');
        $sheet->setCellValue('G1', '备注');

        // 填充数据
        $row = 2;
        foreach ($ips as $ip) {
            $sheet->setCellValue('A' . $row, $ip->cluster_id);
            $sheet->setCellValue('B' . $row, $ip->ip);
            $sheet->setCellValue('C' . $row, $ip->gateway);
            $sheet->setCellValue('D' . $row, $ip->mask);
            $sheet->setCellValue('E' . $row, $ip->vlan);
            $sheet->setCellValue('F' . $row, $ip->is_inside);
            $sheet->setCellValue('G' . $row, $ip->desc);
            $row++;
        }

        // 输出Excel文件
        $writer   = new Xlsx($spreadsheet);
        $filename = 'IP资源池_' . date('YmdHis') . '.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    /**
     * 删除
     *
     * @param $id
     *
     * @return mixed
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    public function actionDelete($id)
    {
        /** @var CloudIps $model */
        $model = $this->findModel($id);
        $status = $model->is_use;
        if ($status == IpsEnum::STATUS_USED) {
            return $this->message("IP已使用，请先解除使用关系", $this->redirect($this->request->referrer), 'error');
        }
        if ($model->delete()) {
            return $this->message("删除成功", $this->redirect($this->request->referrer));
        }

        return $this->message("删除失败", $this->redirect($this->request->referrer), 'error');
    }

    /**
     * 批量删除
     */
    public function actionBatchDelete()
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            return $this->message("请选择要删除的记录", $this->redirect($this->request->referrer), 'error');
        }

        $transaction = $this->beginTransaction();
        try {
            CloudIps::deleteAll(['id' => $ids]);
            $transaction->commit();
            return $this->message("批量删除成功", $this->redirect($this->request->referrer));
        } catch (\Exception $e) {
            $transaction->rollBack();
            return $this->message("批量删除失败：" . $e->getMessage(), $this->redirect($this->request->referrer), 'error');
        }
    }
}
