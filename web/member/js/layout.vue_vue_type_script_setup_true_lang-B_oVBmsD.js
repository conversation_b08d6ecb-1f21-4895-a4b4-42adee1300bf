var Wl=Object.defineProperty,Ol=Object.defineProperties;var Dl=Object.getOwnPropertyDescriptors;var kt=Object.getOwnPropertySymbols;var xa=Object.prototype.hasOwnProperty,ka=Object.prototype.propertyIsEnumerable;var wa=(a,l,t)=>l in a?Wl(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,ne=(a,l)=>{for(var t in l||(l={}))xa.call(l,t)&&wa(a,t,l[t]);if(kt)for(var t of kt(l))ka.call(l,t)&&wa(a,t,l[t]);return a},ke=(a,l)=>Ol(a,Dl(l));var we=(a,l)=>{var t={};for(var o in a)xa.call(a,o)&&l.indexOf(o)<0&&(t[o]=a[o]);if(a!=null&&kt)for(var o of kt(a))l.indexOf(o)<0&&ka.call(a,o)&&(t[o]=a[o]);return t};var Y=(a,l,t)=>new Promise((o,s)=>{var n=d=>{try{i(t.next(d))}catch(f){s(f)}},r=d=>{try{i(t.throw(d))}catch(f){s(f)}},i=d=>d.done?o(d.value):Promise.resolve(d.value).then(n,r);i((t=t.apply(a,l)).next())});import{d0 as Nl,aQ as me,bW as Rl,d1 as Fl,d2 as Rt,c$ as at,d3 as Kl,d4 as Gl,bX as He,d5 as jl,d6 as ql,d7 as Yl,d8 as Xl,d9 as Zl,da as Ql,db as Jl,dc as eo,dd as to,de as ao,df as lo,dg as oo,dh as no,di as so,dj as ro,dk as io,dl as uo,dm as co,dn as po,bb as Te,b3 as Ae,dp as fo,dq as mo,V as ut,cM as Ee,aq as Se,aM as Vt,dr as Ft,ds as ho,dt as bo,du as vo,dv as go,dw as yo,dx as wo,c7 as tt,dy as xo,dz as ko,u as lt,b2 as ot,bo as je,aO as v,X as Be,dA as Co,b6 as Ua,b8 as Ha,bk as Wa,dB as Oa,aP as nt,dC as Da,b_ as So,dD as To,dE as st,dF as ct,dG as Mo,dH as _o,dI as $o,dJ as Bo,dK as Vo,dL as Lo,dM as Eo,c3 as zo,dN as Na,dO as Po,bg as Io,bh as Ao,dP as Uo,dQ as Ho,dR as Wo,dS as Oo,dT as Do,c6 as No,dU as Ra,dV as Ro,dW as Ze,dX as Fo,c5 as Ko,dY as Go,dZ as jo,d_ as qo}from"./bootstrap-DaIAlhlN.js";import{bz as Yo,bA as Xo,bR as Fa,a1 as z,r as Zo,a4 as L,av as x,ab as u,aV as F,a7 as e,aW as re,a8 as P,aa as k,ac as c,x as p,J as w,ad as pe,af as Ue,ag as Qe,aw as se,a_ as be,b4 as Qo,a$ as y,P as X,Y as de,F as Z,aA as oe,aq as U,i as Tt,a5 as $t,bS as Lt,bF as Jo,a9 as qe,aC as $,aj as _,ai as I,ah as Le,bT as en,a2 as Ye,ax as wt,O as tn,bU as an,bV as ln,M as Ka,bW as Ct,aD as Me,bX as on,by as nn,n as Xe,aZ as Dt,bY as Ga,bZ as bt,b_ as Bt,T as xt,b$ as sn,c0 as St,c1 as rn,U as Kt,as as We,b2 as rt,bq as dn,c2 as un,c3 as cn,c4 as pn,c5 as Ca,c6 as fn,c7 as Sa,ae as ja,bk as Ta,aJ as mn,ao as qa,c8 as hn,c9 as bn,b6 as dt,ca as Ma,cb as vn,bm as _a,q as Et,aF as Ya,R as Xa,az as Za,aS as Nt,_ as gn,cc as yn,bQ as wn,aB as Qa,N as xn,a3 as $a}from"../jse/index-index-Dn0hz0U0.js";import{_ as vt}from"./avatar.vue_vue_type_script_setup_true_lang-DTexRDTe.js";import{u as gt}from"./use-modal-B565-vty.js";import{_ as Gt,a as jt,b as qt,c as mt,d as kn,e as Ja,f as el,S as Cn,M as Sn,g as Tn,h as Mn,i as _n,j as $n}from"./theme-toggle.vue_vue_type_script_setup_true_lang-B779hhJq.js";import{P as Bn}from"./plus-hPom66QX.js";import{u as tl}from"./use-drawer-Cm7sv36g.js";import{R as Yt}from"./rotate-cw-DgF-1ZnN.js";import{X as zt}from"./x-CHknV7Zj.js";import{c as Vn}from"./index-D2XaLkiW.js";function Xt(a,l){for(const t of a){if(t.path===l)return t;const o=t.children&&Xt(t.children,l);if(o)return o}return null}function ht(a,l,t=0){var r;const o=Xt(a,l),s=(r=o==null?void 0:o.parents)==null?void 0:r[t],n=s?a.find(i=>i.path===s):void 0;return{findMenu:o,rootMenu:n,rootMenuPath:s}}const pt=Nl("core-tabbar",{actions:{_bulkCloseByKeys(a){return Y(this,null,function*(){const l=new Set(a);this.tabs=this.tabs.filter(t=>!l.has(Ne(t))),yield this.updateCacheTabs()})},_close(a){if(Fe(a))return;const l=this.tabs.findIndex(t=>Ke(t,a));l!==-1&&this.tabs.splice(l,1)},_goToDefaultTab(a){return Y(this,null,function*(){if(this.getTabs.length<=0)return;const l=this.getTabs[0];l&&(yield this._goToTab(l,a))})},_goToTab(a,l){return Y(this,null,function*(){const{params:t,path:o,query:s}=a,n={params:t||{},path:o,query:s||{}};yield l.replace(n)})},addTab(a){var o,s;let l=Ln(a);if(l.key||(l.key=Re(a)),!En(l))return l;const t=this.tabs.findIndex(n=>Ke(n,l));if(t===-1){const n=z.tabbar.maxCount,r=(s=(o=a==null?void 0:a.meta)==null?void 0:o.maxNumOfOpenTab)!=null?s:-1;if(r>0&&this.tabs.filter(i=>i.name===a.name).length>=r){const i=this.tabs.findIndex(d=>d.name===a.name);i!==-1&&this.tabs.splice(i,1)}else if(n>0&&this.tabs.length>=n){const i=this.tabs.findIndex(d=>!Reflect.has(d.meta,"affixTab")||!d.meta.affixTab);i!==-1&&this.tabs.splice(i,1)}this.tabs.push(l)}else{const n=Zo(this.tabs)[t],r=ke(ne(ne({},n),l),{meta:ne(ne({},n==null?void 0:n.meta),l.meta)});if(n){const i=n.meta;Reflect.has(i,"affixTab")&&(r.meta.affixTab=i.affixTab),Reflect.has(i,"newTabTitle")&&(r.meta.newTabTitle=i.newTabTitle)}l=r,this.tabs.splice(t,1,r)}return this.updateCacheTabs(),l},closeAllTabs(a){return Y(this,null,function*(){const l=this.tabs.filter(t=>Fe(t));this.tabs=l.length>0?l:[...this.tabs].splice(0,1),yield this._goToDefaultTab(a),this.updateCacheTabs()})},closeLeftTabs(a){return Y(this,null,function*(){const l=this.tabs.findIndex(s=>Ke(s,a));if(l<1)return;const t=this.tabs.slice(0,l),o=[];for(const s of t)Fe(s)||o.push(s.key);yield this._bulkCloseByKeys(o)})},closeOtherTabs(a){return Y(this,null,function*(){const l=this.tabs.map(o=>Ne(o)),t=[];for(const o of l)if(o!==Ne(a)){const s=this.tabs.find(n=>Ne(n)===o);if(!s)continue;Fe(s)||t.push(s.key)}yield this._bulkCloseByKeys(t)})},closeRightTabs(a){return Y(this,null,function*(){const l=this.tabs.findIndex(t=>Ke(t,a));if(l!==-1&&l<this.tabs.length-1){const t=this.tabs.slice(l+1),o=[];for(const s of t)Fe(s)||o.push(s.key);yield this._bulkCloseByKeys(o)}})},closeTab(a,l){return Y(this,null,function*(){const{currentRoute:t}=l;if(Re(t.value)!==Ne(a)){this._close(a),this.updateCacheTabs();return}const o=this.getTabs.findIndex(r=>Ne(r)===Re(t.value)),s=this.getTabs[o-1],n=this.getTabs[o+1];n?(this._close(a),yield this._goToTab(n,l)):s?(this._close(a),yield this._goToTab(s,l)):console.error("Failed to close the tab; only one tab remains open.")})},closeTabByKey(a,l){return Y(this,null,function*(){const t=decodeURIComponent(a),o=this.tabs.findIndex(n=>Ne(n)===t);if(o===-1)return;const s=this.tabs[o];s&&(yield this.closeTab(s,l))})},getTabByKey(a){return this.getTabs.find(l=>Ne(l)===a)},openTabInNewWindow(a){return Y(this,null,function*(){Fa(a.fullPath||a.path)})},pinTab(a){return Y(this,null,function*(){var n;const l=this.tabs.findIndex(r=>Ke(r,a));if(l===-1)return;const t=this.tabs[l];a.meta.affixTab=!0,a.meta.title=(n=t==null?void 0:t.meta)==null?void 0:n.title,this.tabs.splice(l,1,a);const s=this.tabs.filter(r=>Fe(r)).findIndex(r=>Ke(r,a));yield this.sortTabs(l,s)})},refresh(a){return Y(this,null,function*(){if(typeof a=="string")return yield this.refreshByName(a);const{currentRoute:l}=a,{name:t}=l.value;this.excludeCachedTabs.add(t),this.renderRouteView=!1,Yo(),yield new Promise(o=>setTimeout(o,200)),this.excludeCachedTabs.delete(t),this.renderRouteView=!0,Xo()})},refreshByName(a){return Y(this,null,function*(){this.excludeCachedTabs.add(a),yield new Promise(l=>setTimeout(l,200)),this.excludeCachedTabs.delete(a)})},resetTabTitle(a){return Y(this,null,function*(){var t;if((t=a==null?void 0:a.meta)!=null&&t.newTabTitle)return;const l=this.tabs.find(o=>Ke(o,a));l&&(l.meta.newTabTitle=void 0,yield this.updateCacheTabs())})},setAffixTabs(a){for(const l of a)l.meta.affixTab=!0,this.addTab(zn(l))},setMenuList(a){this.menuList=a},setTabTitle(a,l){return Y(this,null,function*(){const t=this.tabs.find(o=>Ke(o,a));t&&(t.meta.newTabTitle=l,yield this.updateCacheTabs())})},setUpdateTime(){this.updateTime=Date.now()},sortTabs(a,l){return Y(this,null,function*(){const t=this.tabs[a];t&&(this.tabs.splice(a,1),this.tabs.splice(l,0,t),this.dragEndIndex=this.dragEndIndex+1)})},toggleTabPin(a){return Y(this,null,function*(){var t,o;yield((o=(t=a==null?void 0:a.meta)==null?void 0:t.affixTab)!=null?o:!1)?this.unpinTab(a):this.pinTab(a)})},unpinTab(a){return Y(this,null,function*(){var n;const l=this.tabs.findIndex(r=>Ke(r,a));if(l===-1)return;const t=this.tabs[l];a.meta.affixTab=!1,a.meta.title=(n=t==null?void 0:t.meta)==null?void 0:n.title,this.tabs.splice(l,1,a);const s=this.tabs.filter(r=>Fe(r)).length;yield this.sortTabs(l,s)})},updateCacheTabs(){return Y(this,null,function*(){var l;const a=new Set;for(const t of this.tabs){if(!((l=t.meta)==null?void 0:l.keepAlive))continue;(t.matched||[]).forEach((n,r)=>{r>0&&a.add(n.name)});const s=t.name;a.add(s)}this.cachedTabs=a})}},getters:{affixTabs(){return this.tabs.filter(l=>Fe(l)).sort((l,t)=>{var n,r,i,d;const o=(r=(n=l.meta)==null?void 0:n.affixTabOrder)!=null?r:0,s=(d=(i=t.meta)==null?void 0:i.affixTabOrder)!=null?d:0;return o-s})},getCachedTabs(){return[...this.cachedTabs]},getExcludeCachedTabs(){return[...this.excludeCachedTabs]},getMenuList(){return this.menuList},getTabs(){const a=this.tabs.filter(l=>!Fe(l));return[...this.affixTabs,...a].filter(Boolean)}},persist:[{pick:["tabs"],storage:sessionStorage}],state:()=>({cachedTabs:new Set,dragEndIndex:0,excludeCachedTabs:new Set,menuList:["close","affix","maximize","reload","open-in-new-window","close-left","close-right","close-other","close-all"],renderRouteView:!0,tabs:[],updateTime:Date.now()})});function Ln(a){if(!a)return a;const s=a,{matched:l,meta:t}=s,o=we(s,["matched","meta"]);return ke(ne({},o),{matched:l?l.map(n=>({meta:n.meta,name:n.name,path:n.path})):void 0,meta:ke(ne({},t),{newTabTitle:t.newTabTitle})})}function Fe(a){var l,t;return(t=(l=a==null?void 0:a.meta)==null?void 0:l.affixTab)!=null?t:!1}function En(a){var t;const l=(t=a==null?void 0:a.matched)!=null?t:[];return!a.meta.hideInTab&&l.every(o=>!o.meta.hideInTab)}function Re(a){const{fullPath:l,path:t,meta:{fullPathKey:o}={},query:s={}}=a,n=Array.isArray(s.pageKey)?s.pageKey[0]:s.pageKey;let r;n?r=n:r=o===!1?t:l!=null?l:t;try{return decodeURIComponent(r)}catch(i){return r}}function Ne(a){var l;return(l=a.key)!=null?l:Re(a)}function Ke(a,l){return Ne(a)===Ne(l)}function zn(a){return{meta:a.meta,name:a.name,path:a.path,key:Re(a)}}const Pn=me("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);const In=me("arrow-left-to-line",[["path",{d:"M3 19V5",key:"rwsyhb"}],["path",{d:"m13 6-6 6 6 6",key:"1yhaz7"}],["path",{d:"M7 12h14",key:"uoisry"}]]);const An=me("arrow-right-left",[["path",{d:"m16 3 4 4-4 4",key:"1x1c3m"}],["path",{d:"M20 7H4",key:"zbl0bi"}],["path",{d:"m8 21-4-4 4-4",key:"h9nckh"}],["path",{d:"M4 17h16",key:"g4d7ey"}]]);const Un=me("arrow-right-to-line",[["path",{d:"M17 12H3",key:"8awo09"}],["path",{d:"m11 18 6-6-6-6",key:"8c2y43"}],["path",{d:"M21 5v14",key:"nzette"}]]);const Hn=me("arrow-up-to-line",[["path",{d:"M5 3h14",key:"7usisc"}],["path",{d:"m18 13-6-6-6 6",key:"1kf1n9"}],["path",{d:"M12 7v14",key:"1akyts"}]]);const Wn=me("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);const On=me("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);const Dn=me("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);const Nn=me("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);const Rn=me("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);const Fn=me("corner-down-left",[["polyline",{points:"9 10 4 15 9 20",key:"r3jprv"}],["path",{d:"M20 4v7a4 4 0 0 1-4 4H4",key:"6o5b7l"}]]);const Kn=me("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);const Gn=me("fold-horizontal",[["path",{d:"M2 12h6",key:"1wqiqv"}],["path",{d:"M22 12h-6",key:"1eg9hc"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 8v2",key:"1woqiv"}],["path",{d:"M12 14v2",key:"8jcxud"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m19 9-3 3 3 3",key:"12ol22"}],["path",{d:"m5 15 3-3-3-3",key:"1kdhjc"}]]);const al=me("fullscreen",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}],["rect",{width:"10",height:"8",x:"7",y:"8",rx:"1",key:"vys8me"}]]);const ll=me("lock-keyhole",[["circle",{cx:"12",cy:"16",r:"1",key:"1au0dj"}],["rect",{x:"3",y:"10",width:"18",height:"12",rx:"2",key:"6s8ecr"}],["path",{d:"M7 10V7a5 5 0 0 1 10 0v3",key:"1pqi11"}]]);const jn=me("log-out",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);const qn=me("mail-check",[["path",{d:"M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8",key:"12jkf8"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}],["path",{d:"m16 19 2 2 4-4",key:"1b14m6"}]]);const Yn=me("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);const ol=me("minimize-2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);const Xn=me("minimize",[["path",{d:"M8 3v3a2 2 0 0 1-2 2H3",key:"hohbtr"}],["path",{d:"M21 8h-3a2 2 0 0 1-2-2V3",key:"5jw1f3"}],["path",{d:"M3 16h3a2 2 0 0 1 2 2v3",key:"198tvr"}],["path",{d:"M16 21v-3a2 2 0 0 1 2-2h3",key:"ph8mxp"}]]);const nl=me("pin-off",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M15 9.34V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H7.89",key:"znwnzq"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M9 9v1.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h11",key:"c9qhm2"}]]);const Pt=me("pin",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]]);const Zn=me("search-x",[["path",{d:"m13.5 8.5-5 5",key:"1cs55j"}],["path",{d:"m8.5 8.5 5 5",key:"a8mexj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);const Ba=me("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);const sl=me("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);const Qn=me("user-round-pen",[["path",{d:"M2 21a8 8 0 0 1 10.821-7.487",key:"1c8h7z"}],["path",{d:"M21.378 16.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1817ys"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}]]),Jn=Rl("inline-flex items-center rounded-md border border-border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-accent hover:bg-accent text-primary-foreground shadow",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive-hover",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}}),es=L({__name:"Badge",props:{class:{},variant:{}},setup(a){const l=a;return(t,o)=>(u(),x("div",{class:F(e(re)(e(Jn)({variant:t.variant}),l.class))},[P(t.$slots,"default")],2))}}),ts=L({__name:"Breadcrumb",props:{class:{}},setup(a){const l=a;return(t,o)=>(u(),x("nav",{class:F(l.class),"aria-label":"breadcrumb",role:"navigation"},[P(t.$slots,"default")],2))}}),as=L({__name:"BreadcrumbItem",props:{class:{}},setup(a){const l=a;return(t,o)=>(u(),x("li",{class:F(e(re)("hover:text-foreground inline-flex items-center gap-1.5",l.class))},[P(t.$slots,"default")],2))}}),ls=L({__name:"BreadcrumbLink",props:{asChild:{type:Boolean},as:{default:"a"},class:{}},setup(a){const l=a;return(t,o)=>(u(),k(e(Fl),{as:t.as,"as-child":t.asChild,class:F(e(re)("hover:text-foreground transition-colors",l.class))},{default:c(()=>[P(t.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),os=L({__name:"BreadcrumbList",props:{class:{}},setup(a){const l=a;return(t,o)=>(u(),x("ol",{class:F(e(re)("text-muted-foreground flex flex-wrap items-center gap-1.5 break-words text-sm sm:gap-2.5",l.class))},[P(t.$slots,"default")],2))}}),ns=L({__name:"BreadcrumbPage",props:{class:{}},setup(a){const l=a;return(t,o)=>(u(),x("span",{class:F(e(re)("text-foreground font-normal",l.class)),"aria-current":"page","aria-disabled":"true",role:"link"},[P(t.$slots,"default")],2))}}),ss=L({__name:"BreadcrumbSeparator",props:{class:{}},setup(a){const l=a;return(t,o)=>(u(),x("li",{class:F(e(re)("[&>svg]:size-3.5",l.class)),"aria-hidden":"true",role:"presentation"},[P(t.$slots,"default",{},()=>[p(e(Rt))])],2))}}),rs=L({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},setup(a){const l=a,t=w(()=>{const r=l,{class:s}=r;return we(r,["class"])}),o=at(t);return(s,n)=>(u(),k(e(Kl),pe(e(o),{class:e(re)("px-2 py-1.5 text-sm font-semibold",s.inset&&"pl-8",l.class)}),{default:c(()=>[P(s.$slots,"default")]),_:3},16,["class"]))}}),Mt=L({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean},as:{},class:{}},setup(a){const l=a,t=w(()=>{const n=l,{class:o}=n;return we(n,["class"])});return(o,s)=>(u(),k(e(Gl),pe(t.value,{class:e(re)("bg-border -mx-1 my-1 h-px",l.class)}),null,16,["class"]))}}),Va=L({__name:"DropdownMenuShortcut",props:{class:{}},setup(a){const l=a;return(t,o)=>(u(),x("span",{class:F(e(re)("ml-auto text-xs tracking-widest opacity-60",l.class))},[P(t.$slots,"default")],2))}}),is=L({__name:"HoverCard",props:{defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(a,{emit:l}){const s=He(a,l);return(n,r)=>(u(),k(e(jl),Ue(Qe(e(s))),{default:c(()=>[P(n.$slots,"default")]),_:3},16))}}),ds=L({__name:"HoverCardContent",props:{forceMount:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(a){const l=a,t=w(()=>{const r=l,{class:s}=r;return we(r,["class"])}),o=at(t);return(s,n)=>(u(),k(e(ql),null,{default:c(()=>[p(e(Yl),pe(e(o),{class:e(re)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup w-64 rounded-md border p-4 shadow-md outline-none",l.class)}),{default:c(()=>[P(s.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),us=L({__name:"HoverCardTrigger",props:{asChild:{type:Boolean},as:{}},setup(a){const l=a;return(t,o)=>(u(),k(e(Xl),Ue(Qe(l)),{default:c(()=>[P(t.$slots,"default")]),_:3},16))}}),cs=L({__name:"NumberField",props:{defaultValue:{},modelValue:{},min:{},max:{},step:{},formatOptions:{},locale:{},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},asChild:{type:Boolean},as:{},class:{}},emits:["update:modelValue"],setup(a,{emit:l}){const t=a,o=l,s=w(()=>{const d=t,{class:r}=d;return we(d,["class"])}),n=He(s,o);return(r,i)=>(u(),k(e(Zl),pe(e(n),{class:e(re)("grid gap-1.5",t.class)}),{default:c(()=>[P(r.$slots,"default")]),_:3},16,["class"]))}}),ps=L({__name:"NumberFieldContent",props:{class:{}},setup(a){const l=a;return(t,o)=>(u(),x("div",{class:F(e(re)("relative [&>[data-slot=input]]:has-[[data-slot=decrement]]:pl-5 [&>[data-slot=input]]:has-[[data-slot=increment]]:pr-5",l.class))},[P(t.$slots,"default")],2))}}),fs=L({__name:"NumberFieldDecrement",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(a){const l=a,t=w(()=>{const r=l,{class:s}=r;return we(r,["class"])}),o=at(t);return(s,n)=>(u(),k(e(Ql),pe({"data-slot":"decrement"},e(o),{class:e(re)("absolute left-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",l.class)}),{default:c(()=>[P(s.$slots,"default",{},()=>[p(e(Jl),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),ms=L({__name:"NumberFieldIncrement",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(a){const l=a,t=w(()=>{const r=l,{class:s}=r;return we(r,["class"])}),o=at(t);return(s,n)=>(u(),k(e(eo),pe({"data-slot":"increment"},e(o),{class:e(re)("absolute right-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",l.class)}),{default:c(()=>[P(s.$slots,"default",{},()=>[p(e(Bn),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),hs=L({__name:"NumberFieldInput",setup(a){return(l,t)=>(u(),k(e(to),{class:F(e(re)("border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent py-1 text-center text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50")),"data-slot":"input"},null,8,["class"]))}}),rl=L({__name:"ScrollBar",props:{orientation:{default:"vertical"},forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(a){const l=a,t=w(()=>{const n=l,{class:o}=n;return we(n,["class"])});return(o,s)=>(u(),k(e(ao),pe(t.value,{class:e(re)("flex touch-none select-none transition-colors",o.orientation==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-px",o.orientation==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-px",l.class)}),{default:c(()=>[p(e(lo),{class:"bg-border relative flex-1 rounded-full"})]),_:1},16,["class"]))}}),bs=L({__name:"ScrollArea",props:{type:{},dir:{},scrollHideDelay:{},asChild:{type:Boolean},as:{},class:{},onScroll:{type:Function,default:()=>{}},viewportProps:{}},setup(a){const l=a,t=w(()=>{const n=l,{class:o}=n;return we(n,["class"])});return(o,s)=>(u(),k(e(oo),pe(t.value,{class:e(re)("relative overflow-hidden",l.class)}),{default:c(()=>[p(e(no),{"as-child":"",class:"h-full w-full rounded-[inherit] focus:outline-none",onScroll:o.onScroll},{default:c(()=>[P(o.$slots,"default")]),_:3},8,["onScroll"]),p(rl),p(e(so))]),_:3},16,["class"]))}}),vs=L({__name:"Switch",props:{defaultChecked:{type:Boolean},checked:{type:Boolean},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},value:{},asChild:{type:Boolean},as:{},class:{}},emits:["update:checked"],setup(a,{emit:l}){const t=a,o=l,s=w(()=>{const d=t,{class:r}=d;return we(d,["class"])}),n=He(s,o);return(r,i)=>(u(),k(e(ro),pe(e(n),{class:e(re)("focus-visible:ring-ring focus-visible:ring-offset-background data-[state=checked]:bg-primary data-[state=unchecked]:bg-input peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t.class)}),{default:c(()=>[p(e(io),{class:F(e(re)("bg-background pointer-events-none block h-4 w-4 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"))},null,8,["class"])]),_:1},16,["class"]))}}),gs=L({__name:"Tabs",props:{defaultValue:{},orientation:{},dir:{},activationMode:{},modelValue:{},asChild:{type:Boolean},as:{}},emits:["update:modelValue"],setup(a,{emit:l}){const s=He(a,l);return(n,r)=>(u(),k(e(uo),Ue(Qe(e(s))),{default:c(()=>[P(n.$slots,"default")]),_:3},16))}}),ys=L({__name:"TabsContent",props:{value:{},forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(a){const l=a,t=w(()=>{const n=l,{class:o}=n;return we(n,["class"])});return(o,s)=>(u(),k(e(co),pe({class:e(re)("ring-offset-background focus-visible:ring-ring mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",l.class)},t.value),{default:c(()=>[P(o.$slots,"default")]),_:3},16,["class"]))}}),ws=L({__name:"TabsList",props:{loop:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(a){const l=a,t=w(()=>{const n=l,{class:o}=n;return we(n,["class"])});return(o,s)=>(u(),k(e(po),pe(t.value,{class:e(re)("bg-muted text-muted-foreground inline-flex h-9 items-center justify-center rounded-lg p-1",l.class)}),{default:c(()=>[P(o.$slots,"default")]),_:3},16,["class"]))}}),xs=L({name:"VbenButtonGroup",__name:"button-group",props:{border:{type:Boolean,default:!1},gap:{default:0},size:{default:"middle"}},setup(a){return(l,t)=>(u(),x("div",{class:F(e(re)("vben-button-group rounded-md",`size-${l.size}`,l.gap?"with-gap":"no-gap",l.$attrs.class)),style:se({gap:l.gap?`${l.gap}px`:"0px"})},[P(l.$slots,"default",{},void 0,!0)],6))}}),ks=Te(xs,[["__scopeId","data-v-ba11c217"]]),Cs={key:0,class:"icon-wrapper"},Ss=L({__name:"check-button-group",props:be({allowClear:{type:Boolean,default:!1},beforeChange:{},btnClass:{},gap:{default:0},maxCount:{default:0},multiple:{type:Boolean,default:!1},options:{},showIcon:{type:Boolean,default:!0},size:{default:"middle"},disabled:{type:Boolean}},{modelValue:{},modelModifiers:{}}),emits:be(["btnClick"],["update:modelValue"]),setup(a,{emit:l}){const t=a,o=l,s=w(()=>ke(ne({},Qo(t,["options","btnClass","size","disabled"])),{class:re(t.btnClass)})),n=y(a,"modelValue"),r=X([]),i=X([]);de(()=>t.multiple,f=>{f?n.value=r.value:n.value=r.value.length>0?r.value[0]:void 0}),de(()=>n.value,f=>{if(Array.isArray(f)){const m=f.filter(g=>g!==void 0);m.length>0?r.value=t.multiple?[...m]:[m[0]]:r.value=[]}else r.value=f===void 0?[]:[f]},{deep:!0,immediate:!0});function d(f){return Y(this,null,function*(){if(t.beforeChange&&Tt(t.beforeChange))try{if(i.value.push(f),(yield t.beforeChange(f,!r.value.includes(f)))===!1)return}finally{i.value.splice(i.value.indexOf(f),1)}if(t.multiple)r.value.includes(f)?r.value=r.value.filter(m=>m!==f):(t.maxCount>0&&r.value.length>=t.maxCount&&(r.value=r.value.slice(0,t.maxCount-1)),r.value.push(f)),n.value=r.value;else if(t.allowClear&&r.value.includes(f)){r.value=[],n.value=void 0,o("btnClick",void 0);return}else r.value=[f],n.value=f;o("btnClick",f)})}return(f,m)=>(u(),k(ks,{size:t.size,gap:t.gap,class:"vben-check-button-group"},{default:c(()=>[(u(!0),x(Z,null,oe(t.options,(g,b)=>(u(),k(Ae,pe({key:b,class:e(re)("border",t.btnClass),disabled:t.disabled||i.value.includes(g.value)||!t.multiple&&i.value.length>0,ref_for:!0},s.value,{variant:r.value.includes(g.value)?"default":"outline",onClick:h=>d(g.value),type:"button"}),{default:c(()=>[t.showIcon?(u(),x("div",Cs,[P(f.$slots,"icon",{loading:i.value.includes(g.value),checked:r.value.includes(g.value)},()=>[i.value.includes(g.value)?(u(),k(e(fo),{key:0,class:"animate-spin"})):r.value.includes(g.value)?(u(),k(e(Dn),{key:1})):(u(),k(e(Nn),{key:2}))],!0)])):U("",!0),P(f.$slots,"option",{label:g.label,value:g.value,data:g},()=>[p(e(mo),{content:g.label},null,8,["content"])],!0)]),_:2},1040,["class","disabled","variant","onClick"]))),128))]),_:3},8,["size","gap"]))}}),Ts=Te(Ss,[["__scopeId","data-v-9a0233de"]]),Ms=a=>{const l=$t(),t=$t(),o=X(!1),s=()=>{var i;l.value&&(o.value=l.value.scrollTop>=((i=a==null?void 0:a.visibilityHeight)!=null?i:0))},n=()=>{var i;(i=l.value)==null||i.scrollTo({behavior:"smooth",top:0})},r=Lt(s,300,!0);return Jo(t,"scroll",r),qe(()=>{var i;if(t.value=document,l.value=document.documentElement,a.target){if(l.value=(i=document.querySelector(a.target))!=null?i:void 0,!l.value)throw new Error(`target does not exist: ${a.target}`);t.value=l.value}s()}),{handleClick:n,visible:o}},_s=L({name:"BackTop",__name:"back-top",props:{bottom:{default:20},isGroup:{type:Boolean,default:!1},right:{default:24},target:{default:""},visibilityHeight:{default:200}},setup(a){const l=a,t=w(()=>({bottom:`${l.bottom}px`,right:`${l.right}px`})),{handleClick:o,visible:s}=Ms(l);return(n,r)=>(u(),k(ut,{name:"fade-down"},{default:c(()=>[e(s)?(u(),k(e(Ae),{key:0,style:se(t.value),class:"dark:bg-accent dark:hover:bg-heavy bg-background hover:bg-heavy data shadow-float z-popup fixed bottom-10 size-10 rounded-full duration-500",size:"icon",variant:"icon",onClick:e(o)},{default:c(()=>[p(e(Hn),{class:"size-4"})]),_:1},8,["style","onClick"])):U("",!0)]),_:1}))}}),$s={class:"flex"},Bs=["onClick"],Vs={class:"flex-center z-10 h-full"},Ls=L({name:"Breadcrumb",__name:"breadcrumb-background",props:{breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(a,{emit:l}){const t=l;function o(s,n){!n||s===a.breadcrumbs.length-1||t("select",n)}return(s,n)=>(u(),x("ul",$s,[p(Vt,{name:"breadcrumb-transition"},{default:c(()=>[(u(!0),x(Z,null,oe(s.breadcrumbs,(r,i)=>(u(),x("li",{key:`${r.path}-${r.title}-${i}`},[$("a",{href:"javascript:void 0",onClick:Se(d=>o(i,r.path),["stop"])},[$("span",Vs,[s.showIcon?(u(),k(e(Ee),{key:0,icon:r.icon,class:"mr-1 size-4 flex-shrink-0"},null,8,["icon"])):U("",!0),$("span",{class:F({"text-foreground font-normal":i===s.breadcrumbs.length-1})},_(r.title),3)])],8,Bs)]))),128))]),_:1})]))}}),Es=Te(Ls,[["__scopeId","data-v-da1498bb"]]),zs={key:0},Ps={class:"flex-center"},Is={class:"flex-center"},As=L({name:"Breadcrumb",__name:"breadcrumb",props:{breadcrumbs:{},showIcon:{type:Boolean,default:!1},styleType:{}},emits:["select"],setup(a,{emit:l}){const t=l;function o(s){s&&t("select",s)}return(s,n)=>(u(),k(e(ts),null,{default:c(()=>[p(e(os),null,{default:c(()=>[p(Vt,{name:"breadcrumb-transition"},{default:c(()=>[(u(!0),x(Z,null,oe(s.breadcrumbs,(r,i)=>(u(),k(e(as),{key:`${r.path}-${r.title}-${i}`},{default:c(()=>{var d,f;return[(f=(d=r.items)==null?void 0:d.length)!=null&&f?(u(),x("div",zs,[p(e(Gt),null,{default:c(()=>[p(e(jt),{class:"flex items-center gap-1"},{default:c(()=>[s.showIcon?(u(),k(e(Ee),{key:0,icon:r.icon,class:"size-5"},null,8,["icon"])):U("",!0),I(" "+_(r.title)+" ",1),p(e(Ft),{class:"size-4"})]),_:2},1024),p(e(qt),{align:"start"},{default:c(()=>[(u(!0),x(Z,null,oe(r.items,m=>(u(),k(e(mt),{key:`sub-${m.path}`,onClick:Se(g=>o(m.path),["stop"])},{default:c(()=>[I(_(m.title),1)]),_:2},1032,["onClick"]))),128))]),_:2},1024)]),_:2},1024)])):i!==s.breadcrumbs.length-1?(u(),k(e(ls),{key:1,href:"javascript:void 0",onClick:Se(m=>o(r.path),["stop"])},{default:c(()=>[$("div",Ps,[s.showIcon?(u(),k(e(Ee),{key:0,class:F([{"size-5":r.isHome},"mr-1 size-4"]),icon:r.icon},null,8,["class","icon"])):U("",!0),I(" "+_(r.title),1)])]),_:2},1032,["onClick"])):(u(),k(e(ns),{key:2},{default:c(()=>[$("div",Is,[s.showIcon?(u(),k(e(Ee),{key:0,class:F([{"size-5":r.isHome},"mr-1 size-4"]),icon:r.icon},null,8,["class","icon"])):U("",!0),I(" "+_(r.title),1)])]),_:2},1024)),i<s.breadcrumbs.length-1&&!r.isHome?(u(),k(e(ss),{key:3})):U("",!0)]}),_:2},1024))),128))]),_:1})]),_:1})]),_:1}))}}),Us=L({__name:"breadcrumb-view",props:{class:{},breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(a,{emit:l}){const s=He(a,l);return(n,r)=>(u(),x(Z,null,[n.styleType==="normal"?(u(),k(As,pe({key:0},e(s),{class:"vben-breadcrumb"}),null,16)):U("",!0),n.styleType==="background"?(u(),k(Es,pe({key:1},e(s),{class:"vben-breadcrumb"}),null,16)):U("",!0)],64))}}),Hs=Te(Us,[["__scopeId","data-v-4cd036dd"]]),Ws=L({__name:"ContextMenu",props:{dir:{},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(a,{emit:l}){const s=He(a,l);return(n,r)=>(u(),k(e(ho),Ue(Qe(e(s))),{default:c(()=>[P(n.$slots,"default")]),_:3},16))}}),Os=L({__name:"ContextMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},sticky:{},hideWhenDetached:{type:Boolean},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(a,{emit:l}){const t=a,o=l,s=w(()=>{const d=t,{class:r}=d;return we(d,["class"])}),n=He(s,o);return(r,i)=>(u(),k(e(bo),null,{default:c(()=>[p(e(vo),pe(e(n),{class:e(re)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup min-w-32 overflow-hidden rounded-md border p-1 shadow-md",t.class)}),{default:c(()=>[P(r.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),Ds=L({__name:"ContextMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},emits:["select"],setup(a,{emit:l}){const t=a,o=l,s=w(()=>{const d=t,{class:r}=d;return we(d,["class"])}),n=He(s,o);return(r,i)=>(u(),k(e(go),pe(e(n),{class:e(re)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r.inset&&"pl-8",t.class)}),{default:c(()=>[P(r.$slots,"default")]),_:3},16,["class"]))}}),Ns=L({__name:"ContextMenuSeparator",props:{asChild:{type:Boolean},as:{},class:{}},setup(a){const l=a,t=w(()=>{const n=l,{class:o}=n;return we(n,["class"])});return(o,s)=>(u(),k(e(yo),pe(t.value,{class:e(re)("bg-border -mx-1 my-1 h-px",l.class)}),null,16,["class"]))}}),Rs=L({__name:"ContextMenuShortcut",props:{class:{}},setup(a){const l=a;return(t,o)=>(u(),x("span",{class:F(e(re)("text-muted-foreground ml-auto text-xs tracking-widest",l.class))},[P(t.$slots,"default")],2))}}),Fs=L({__name:"ContextMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(a){const t=at(a);return(o,s)=>(u(),k(e(wo),Ue(Qe(e(t))),{default:c(()=>[P(o.$slots,"default")]),_:3},16))}}),il=L({__name:"context-menu",props:{dir:{},modal:{type:Boolean},class:{},contentClass:{},contentProps:{},handlerData:{},itemClass:{},menus:{type:Function}},emits:["update:open"],setup(a,{emit:l}){const t=a,o=l,s=w(()=>{const h=t,{class:d,contentClass:f,contentProps:m,itemClass:g}=h;return we(h,["class","contentClass","contentProps","itemClass"])}),n=He(s,o),r=w(()=>{var d;return(d=t.menus)==null?void 0:d.call(t,t.handlerData)});function i(d){var f;d.disabled||(f=d==null?void 0:d.handler)==null||f.call(d,t.handlerData)}return(d,f)=>(u(),k(e(Ws),Ue(Qe(e(n))),{default:c(()=>[p(e(Fs),{"as-child":""},{default:c(()=>[P(d.$slots,"default")]),_:3}),p(e(Os),pe({class:d.contentClass},d.contentProps,{class:"side-content z-popup"}),{default:c(()=>[(u(!0),x(Z,null,oe(r.value,m=>(u(),x(Z,{key:m.key},[p(e(Ds),{class:F([d.itemClass,"cursor-pointer"]),disabled:m.disabled,inset:m.inset||!m.icon,onClick:g=>i(m)},{default:c(()=>[m.icon?(u(),k(Le(m.icon),{key:0,class:"mr-2 size-4 text-lg"})):U("",!0),I(" "+_(m.text)+" ",1),m.shortcut?(u(),k(e(Rs),{key:1},{default:c(()=>[I(_(m.shortcut),1)]),_:2},1024)):U("",!0)]),_:2},1032,["class","disabled","inset","onClick"]),m.separator?(u(),k(e(Ns),{key:0})):U("",!0)],64))),128))]),_:1},16,["class"])]),_:3},16))}}),Ks=L({name:"DropdownMenu",__name:"dropdown-menu",props:{menus:{}},setup(a){const l=a;function t(o){var s;o.disabled||(s=o==null?void 0:o.handler)==null||s.call(o,l)}return(o,s)=>(u(),k(e(Gt),null,{default:c(()=>[p(e(jt),{class:"flex h-full items-center gap-1"},{default:c(()=>[P(o.$slots,"default")]),_:3}),p(e(qt),{align:"start"},{default:c(()=>[p(e(kn),null,{default:c(()=>[(u(!0),x(Z,null,oe(o.menus,n=>(u(),x(Z,{key:n.value},[p(e(mt),{disabled:n.disabled,class:"data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground text-foreground/80 mb-1 cursor-pointer",onClick:r=>t(n)},{default:c(()=>[n.icon?(u(),k(Le(n.icon),{key:0,class:"mr-2 size-4"})):U("",!0),I(" "+_(n.label),1)]),_:2},1032,["disabled","onClick"]),n.separator?(u(),k(e(Mt),{key:0,class:"bg-border"})):U("",!0)],64))),128))]),_:1})]),_:1})]),_:3}))}}),Gs=L({name:"FullScreen",__name:"full-screen",setup(a){const{isFullscreen:l,toggle:t}=en();return l.value=!!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement),(o,s)=>(u(),k(e(tt),{onClick:e(t)},{default:c(()=>[e(l)?(u(),k(e(Xn),{key:0,class:"text-foreground size-4"})):(u(),k(e(Yn),{key:1,class:"text-foreground size-4"}))]),_:1},8,["onClick"]))}}),js={class:"h-full cursor-pointer"},qs=L({__name:"hover-card",props:{class:{},contentClass:{},contentProps:{},defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(a,{emit:l}){const t=a,o=l,s=w(()=>{const m=t,{class:r,contentClass:i,contentProps:d}=m;return we(m,["class","contentClass","contentProps"])}),n=He(s,o);return(r,i)=>(u(),k(e(is),Ue(Qe(e(n))),{default:c(()=>[p(e(us),{"as-child":"",class:"h-full"},{default:c(()=>[$("div",js,[P(r.$slots,"trigger")])]),_:3}),p(e(ds),pe({class:r.contentClass},r.contentProps,{class:"side-content z-popup"}),{default:c(()=>[P(r.$slots,"default")]),_:3},16,["class"])]),_:3},16))}}),Ys=["href"],Xs={class:"text-foreground truncate text-nowrap font-semibold"},La=L({name:"VbenLogo",__name:"logo",props:{collapsed:{type:Boolean,default:!1},fit:{default:"cover"},href:{default:"javascript:void 0"},logoSize:{default:32},src:{default:""},text:{},theme:{default:"light"}},setup(a){return(l,t)=>(u(),x("div",{class:F([l.theme,"flex h-full items-center text-lg"])},[$("a",{class:F([l.$attrs.class,"flex h-full items-center gap-2 overflow-hidden px-3 text-lg leading-normal transition-all duration-500"]),href:l.href},[l.src?(u(),k(e(vt),{key:0,alt:l.text,src:l.src,size:l.logoSize,fit:l.fit,class:"relative rounded-none bg-transparent"},null,8,["alt","src","size","fit"])):U("",!0),l.collapsed?U("",!0):P(l.$slots,"text",{key:1},()=>[$("span",Xs,_(l.text),1)])],10,Ys)],2))}}),Ea=1,Zs=L({__name:"scrollbar",props:{class:{default:""},horizontal:{type:Boolean,default:!1},scrollBarClass:{},shadow:{type:Boolean,default:!1},shadowBorder:{type:Boolean,default:!1},shadowBottom:{type:Boolean,default:!0},shadowLeft:{type:Boolean,default:!1},shadowRight:{type:Boolean,default:!1},shadowTop:{type:Boolean,default:!0}},emits:["scrollAt"],setup(a,{emit:l}){const t=a,o=l,s=X(!0),n=X(!1),r=X(!1),i=X(!0),d=w(()=>t.shadow&&t.shadowTop),f=w(()=>t.shadow&&t.shadowBottom),m=w(()=>t.shadow&&t.shadowLeft),g=w(()=>t.shadow&&t.shadowRight),b=w(()=>({"both-shadow":!i.value&&!n.value&&m.value&&g.value,"left-shadow":!i.value&&m.value,"right-shadow":!n.value&&g.value}));function h(T){var K,E,B,N,q,te;const H=T.target,O=(K=H==null?void 0:H.scrollTop)!=null?K:0,M=(E=H==null?void 0:H.scrollLeft)!=null?E:0,D=(B=H==null?void 0:H.clientHeight)!=null?B:0,R=(N=H==null?void 0:H.clientWidth)!=null?N:0,A=(q=H==null?void 0:H.scrollHeight)!=null?q:0,V=(te=H==null?void 0:H.scrollWidth)!=null?te:0;s.value=O<=0,i.value=M<=0,r.value=Math.abs(O)+D>=A-Ea,n.value=Math.abs(M)+R>=V-Ea,o("scrollAt",{bottom:r.value,left:i.value,right:n.value,top:s.value})}return(T,H)=>(u(),k(e(bs),{class:F([[e(re)(t.class),b.value],"vben-scrollbar relative"]),"on-scroll":h},{default:c(()=>[d.value?(u(),x("div",{key:0,class:F([{"opacity-100":!s.value,"border-border border-t":T.shadowBorder&&!s.value},"scrollbar-top-shadow pointer-events-none absolute top-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):U("",!0),P(T.$slots,"default",{},void 0,!0),f.value?(u(),x("div",{key:1,class:F([{"opacity-100":!s.value&&!r.value,"border-border border-b":T.shadowBorder&&!s.value&&!r.value},"scrollbar-bottom-shadow pointer-events-none absolute bottom-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):U("",!0),T.horizontal?(u(),k(e(rl),{key:2,class:F(T.scrollBarClass),orientation:"horizontal"},null,8,["class"])):U("",!0)]),_:3},8,["class"]))}}),yt=Te(Zs,[["__scopeId","data-v-c94474ed"]]),Qs={class:"bg-background text-foreground inline-flex h-full w-full items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"},Js=L({__name:"tabs-indicator",props:{asChild:{type:Boolean},as:{},class:{}},setup(a){const l=a,t=w(()=>{const r=l,{class:s}=r;return we(r,["class"])}),o=at(t);return(s,n)=>(u(),k(e(xo),pe(e(o),{class:e(re)("absolute bottom-0 left-0 z-10 h-full w-1/2 translate-x-[--radix-tabs-indicator-position] rounded-full px-0 py-1 pr-1 transition-[width,transform] duration-300",l.class)}),{default:c(()=>[$("div",Qs,[P(s.$slots,"default")])]),_:3},16,["class"]))}}),er=L({__name:"segmented",props:be({defaultValue:{default:""},tabs:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(a){const l=a,t=y(a,"modelValue"),o=w(()=>{var r;return l.defaultValue||((r=l.tabs[0])==null?void 0:r.value)}),s=w(()=>({"grid-template-columns":`repeat(${l.tabs.length}, minmax(0, 1fr))`})),n=w(()=>({width:`${(100/l.tabs.length).toFixed(0)}%`}));return(r,i)=>(u(),k(e(gs),{modelValue:t.value,"onUpdate:modelValue":i[0]||(i[0]=d=>t.value=d),"default-value":o.value},{default:c(()=>[p(e(ws),{style:se(s.value),class:"bg-accent relative grid w-full"},{default:c(()=>[p(Js,{style:se(n.value)},null,8,["style"]),(u(!0),x(Z,null,oe(r.tabs,d=>(u(),k(e(ko),{key:d.value,value:d.value,class:"z-20 inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium disabled:pointer-events-none disabled:opacity-50"},{default:c(()=>[I(_(d.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["style"]),(u(!0),x(Z,null,oe(r.tabs,d=>(u(),k(e(ys),{key:d.value,value:d.value},{default:c(()=>[P(r.$slots,d.value)]),_:2},1032,["value"]))),128))]),_:3},8,["modelValue","default-value"]))}});function dl(){const{contentIsMaximize:a}=lt();function l(){const t=a.value;Ye({header:{hidden:!t},sidebar:{hidden:!t}})}return{contentIsMaximize:a,toggleMaximize:l}}const Ht=500,Wt=0;function tr(a,l=Ht){const t=typeof l=="number"||Tt(l)?{enterDelay:Wt,leaveDelay:l}:ne({enterDelay:Wt,leaveDelay:Ht},l),o=X(!1),s=X(),n=X(),r=X([]),i=w(()=>{const M=e(a);return M===null?[]:Array.isArray(M)?M:[M]}),d=X([]);function f(){r.value.forEach(M=>M.stop()),r.value=[],d.value=i.value.map(M=>{if(!M)return X(!1);const D=w(()=>{const V=e(M);return V instanceof Element?V:V==null?void 0:V.$el}),R=tn(),A=R.run(()=>an(D))||X(!1);return r.value.push(R),A})}const m=w(()=>{const M=e(a);return M===null?0:Array.isArray(M)?M.length:1});f();const g=de(m,f,{deep:!1}),b=w(()=>d.value.every(M=>!M.value));function h(){s.value&&(clearTimeout(s.value),s.value=void 0),n.value&&(clearTimeout(n.value),n.value=void 0)}function T(M){var D,R;if(h(),M){const A=(D=t.enterDelay)!=null?D:Wt,V=Tt(A)?A():A;V<=0?o.value=!0:s.value=setTimeout(()=>{o.value=!0,s.value=void 0},V)}else{const A=(R=t.leaveDelay)!=null?R:Ht,V=Tt(A)?A():A;V<=0?o.value=!1:n.value=setTimeout(()=>{o.value=!1,n.value=void 0},V)}}const H=de(b,M=>{T(!M)},{immediate:!0}),O={enable(){H.resume()},disable(){H.pause()}};return wt(()=>{h(),g(),r.value.forEach(M=>M.stop())}),[o,O]}function ul(){const a=ot(),l=pt();function t(){return Y(this,null,function*(){yield l.refresh(a)})}return{refresh:t}}function cl(){const a=ot(),l=je(),t=pt();function o(M){return Y(this,null,function*(){yield t.closeLeftTabs(M||l)})}function s(){return Y(this,null,function*(){yield t.closeAllTabs(a)})}function n(M){return Y(this,null,function*(){yield t.closeRightTabs(M||l)})}function r(M){return Y(this,null,function*(){yield t.closeOtherTabs(M||l)})}function i(M){return Y(this,null,function*(){yield t.closeTab(M||l,a)})}function d(M){return Y(this,null,function*(){yield t.pinTab(M||l)})}function f(M){return Y(this,null,function*(){yield t.unpinTab(M||l)})}function m(M){return Y(this,null,function*(){yield t.toggleTabPin(M||l)})}function g(M){return Y(this,null,function*(){yield t.refresh(M||a)})}function b(M){return Y(this,null,function*(){yield t.openTabInNewWindow(M||l)})}function h(M){return Y(this,null,function*(){yield t.closeTabByKey(M,a)})}function T(M){return Y(this,null,function*(){t.setUpdateTime(),yield t.setTabTitle(l,M)})}function H(){return Y(this,null,function*(){t.setUpdateTime(),yield t.resetTabTitle(l)})}function O(M=l){var ue;const D=t.getTabs,R=t.affixTabs,A=D.findIndex(ve=>ve.path===M.path),V=D.length<=1,{meta:K}=M,E=(ue=K==null?void 0:K.affixTab)!=null?ue:!1,B=l.path===M.path,N=A===0||A-R.length<=0||!B,q=!B||A===D.length-1,te=V||!B||D.length-R.length<=1;return{disabledCloseAll:V,disabledCloseCurrent:!!E||V,disabledCloseLeft:N,disabledCloseOther:te,disabledCloseRight:q,disabledRefresh:!B}}return{closeAllTabs:s,closeCurrentTab:i,closeLeftTabs:o,closeOtherTabs:r,closeRightTabs:n,closeTabByKey:h,getTabDisableState:O,openTabInNewWindow:b,pinTab:d,refreshTab:g,resetTabTitle:H,setTabTitle:T,toggleTabPin:m,unpinTab:f}}const ar=L({__name:"breadcrumb",props:{hideWhenOnlyOne:{type:Boolean},showHome:{type:Boolean,default:!1},showIcon:{type:Boolean,default:!1},type:{default:"normal"}},setup(a){const l=a,t=je(),o=ot(),s=w(()=>{const r=t.matched,i=[];for(const d of r){const{meta:f,path:m}=d,{hideChildrenInMenu:g,hideInBreadcrumb:b,icon:h,name:T,title:H}=f||{};b||g||!m||i.push({icon:h,path:m||t.path,title:H?v(H||T):""})}return l.showHome&&i.unshift({icon:"mdi:home-outline",isHome:!0,path:"/"}),l.hideWhenOnlyOne&&i.length===1?[]:i});function n(r){o.push(r)}return(r,i)=>(u(),k(e(Hs),{breadcrumbs:s.value,"show-icon":r.showIcon,"style-type":r.type,class:"ml-2",onSelect:n},null,8,["breadcrumbs","show-icon","style-type"]))}}),lr=L({name:"CheckUpdates",__name:"check-updates",props:{checkUpdatesInterval:{default:1},checkUpdateUrl:{default:"/member/"}},setup(a){const l=a;let t=!1;const o=X(""),s=X(""),n=X(),[r,i]=gt({closable:!1,closeOnPressEscape:!1,closeOnClickModal:!1,onConfirm(){s.value=o.value,window.location.reload()}});function d(){return Y(this,null,function*(){try{if(location.hostname==="localhost"||location.hostname==="127.0.0.1")return null;const T=yield fetch(l.checkUpdateUrl,{cache:"no-cache",method:"HEAD",redirect:"manual"});return T.headers.get("etag")||T.headers.get("last-modified")}catch(T){return console.error("Failed to fetch version tag"),null}})}function f(){return Y(this,null,function*(){const T=yield d();if(T){if(!s.value){s.value=T;return}s.value!==T&&T&&(clearInterval(n.value),m(T))}})}function m(T){o.value=T,i.open()}function g(){l.checkUpdatesInterval<=0||(n.value=setInterval(f,l.checkUpdatesInterval*60*1e3))}function b(){document.hidden?h():t||(t=!0,f().finally(()=>{t=!1,g()}))}function h(){clearInterval(n.value)}return qe(()=>{g(),document.addEventListener("visibilitychange",b)}),wt(()=>{h(),document.removeEventListener("visibilitychange",b)}),(T,H)=>(u(),k(e(r),{"cancel-text":e(v)("common.cancel"),"confirm-text":e(v)("common.refresh"),"fullscreen-button":!1,title:e(v)("ui.widgets.checkUpdatesTitle"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[I(_(e(v)("ui.widgets.checkUpdatesDescription")),1)]),_:1},8,["cancel-text","confirm-text","title"]))}}),or={class:"!flex h-full justify-center px-2 sm:max-h-[450px]"},nr={key:0,class:"text-muted-foreground text-center"},sr={class:"mb-10 mt-6 text-xs"},rr={class:"text-foreground text-sm font-medium"},ir={key:1,class:"text-muted-foreground text-center"},dr={class:"my-10 text-xs"},ur={class:"w-full"},cr={key:0,class:"text-muted-foreground mb-2 text-xs"},pr=["data-index","data-search-item"],fr={class:"flex-1"},mr=["onClick"],hr=L({name:"SearchPanel",__name:"search-panel",props:{keyword:{default:""},menus:{default:()=>[]}},emits:["close"],setup(a,{emit:l}){const t=a,o=l,s=ot(),n=ln(`__search-history-${location.hostname}__`,[]),r=X(-1),i=$t([]),d=X([]),f=Lt(m,200);function m(V){if(V=V.trim(),!V){d.value=[];return}const K=A(V),E=[];nn(i.value,B=>{var N;K.test((N=B.name)==null?void 0:N.toLowerCase())&&E.push(B)}),d.value=E,E.length>0&&(r.value=0),r.value=0}function g(){const V=document.querySelector(`[data-search-item="${r.value}"]`);V&&V.scrollIntoView({block:"nearest"})}function b(){return Y(this,null,function*(){if(d.value.length===0)return;const V=d.value,K=r.value;if(V.length===0||K<0)return;const E=V[K];E&&(n.value.push(E),H(),yield Xe(),Dt(E.path)?window.open(E.path,"_blank"):s.push({path:E.path,replace:!0}))})}function h(){d.value.length!==0&&(r.value--,r.value<0&&(r.value=d.value.length-1),g())}function T(){d.value.length!==0&&(r.value++,r.value>d.value.length-1&&(r.value=0),g())}function H(){d.value=[],o("close")}function O(V){var E;const K=(E=V.target)==null?void 0:E.dataset.index;r.value=Number(K)}function M(V){t.keyword?d.value.splice(V,1):n.value.splice(V,1),r.value=Math.max(r.value-1,0),g()}const D=new Set(["$","(",")","*","+",".","?","[","\\","]","^","{","|","}"]);function R(V){return D.has(V)?`\\${V}`:V}function A(V){const K=[...V].map(E=>R(E)).join(".*");return new RegExp(`.*${K}.*`)}return de(()=>t.keyword,V=>{V?f(V):d.value=[...n.value]}),qe(()=>{i.value=Ka(t.menus,V=>ke(ne({},V),{name:v(V==null?void 0:V.name)})),n.value.length>0&&(d.value=n.value),Ct("Enter",b),Ct("ArrowUp",h),Ct("ArrowDown",T),Ct("Escape",H)}),(V,K)=>(u(),k(e(yt),null,{default:c(()=>[$("div",or,[V.keyword&&d.value.length===0?(u(),x("div",nr,[p(e(Zn),{class:"mx-auto mt-4 size-12"}),$("p",sr,[I(_(e(v)("ui.widgets.search.noResults"))+" ",1),$("span",rr,' "'+_(V.keyword)+'" ',1)])])):U("",!0),!V.keyword&&d.value.length===0?(u(),x("div",ir,[$("p",dr,_(e(v)("ui.widgets.search.noRecent")),1)])):U("",!0),Me($("ul",ur,[e(n).length>0&&!V.keyword?(u(),x("li",cr,_(e(v)("ui.widgets.search.recent")),1)):U("",!0),(u(!0),x(Z,null,oe(e(on)(d.value,"path"),(E,B)=>(u(),x("li",{key:E.path,class:F([r.value===B?"active bg-primary text-primary-foreground":"","bg-accent flex-center group mb-3 w-full cursor-pointer rounded-lg px-4 py-4"]),"data-index":B,"data-search-item":B,onClick:b,onMouseenter:O},[p(e(Ee),{icon:E.icon,class:"mr-2 size-5 flex-shrink-0",fallback:""},null,8,["icon"]),$("span",fr,_(E.name),1),$("div",{class:"flex-center dark:hover:bg-accent hover:text-primary-foreground rounded-full p-1 hover:scale-110",onClick:Se(N=>M(B),["stop"])},[p(e(zt),{class:"size-4"})],8,mr)],42,pr))),128))],512),[[Be,d.value.length>0]])])]),_:1}))}}),br={class:"flex items-center"},vr=["placeholder"],gr={class:"flex w-full justify-start text-xs"},yr={class:"mr-2 flex items-center"},wr={class:"mr-2 flex items-center"},xr={class:"flex items-center"},kr={class:"text-muted-foreground group-hover:text-foreground hidden text-xs duration-300 md:block"},Cr={key:0,class:"bg-background border-foreground/60 text-muted-foreground group-hover:text-foreground relative hidden rounded-sm rounded-r-xl px-1.5 py-1 text-xs leading-none group-hover:opacity-100 md:block"},Sr={key:1},Tr=L({name:"GlobalSearch",__name:"global-search",props:{enableShortcutKey:{type:Boolean,default:!0},menus:{default:()=>[]}},setup(a){const l=a,t=X(""),o=X(),[s,n]=gt({onCancel(){n.close()},onOpenChange(h){h||(t.value="")}}),r=n.useStore(h=>h.isOpen);function i(){n.close(),t.value=""}const d=Ga(),f=bt()?d["ctrl+k"]:d["cmd+k"];Bt(f,()=>{l.enableShortcutKey&&n.open()}),Bt(r,()=>{Xe(()=>{var h;(h=o.value)==null||h.focus()})});const m=h=>{var T;((T=h.key)==null?void 0:T.toLowerCase())==="k"&&(h.metaKey||h.ctrlKey)&&h.preventDefault()},g=()=>{l.enableShortcutKey?window.addEventListener("keydown",m):window.removeEventListener("keydown",m)},b=()=>{r.value?n.close():n.open()};return de(()=>l.enableShortcutKey,g),qe(()=>{g(),wt(()=>{window.removeEventListener("keydown",m)})}),(h,T)=>(u(),x("div",null,[p(e(s),{"fullscreen-button":!1,class:"w-[600px]","header-class":"py-2 border-b"},{title:c(()=>[$("div",br,[p(e(Ba),{class:"text-muted-foreground mr-2 size-4"}),Me($("input",{ref_key:"searchInputRef",ref:o,"onUpdate:modelValue":T[0]||(T[0]=H=>t.value=H),placeholder:e(v)("ui.widgets.search.searchNavigate"),class:"ring-none placeholder:text-muted-foreground w-[80%] rounded-md border border-none bg-transparent p-2 pl-0 text-sm font-normal outline-none ring-0 ring-offset-transparent focus-visible:ring-transparent"},null,8,vr),[[Co,t.value]])])]),footer:c(()=>[$("div",gr,[$("div",yr,[p(e(Fn),{class:"mr-1 size-3"}),I(" "+_(e(v)("ui.widgets.search.select")),1)]),$("div",wr,[p(e(Wn),{class:"mr-1 size-3"}),p(e(Pn),{class:"mr-1 size-3"}),I(" "+_(e(v)("ui.widgets.search.navigate")),1)]),$("div",xr,[p(e(Vn),{class:"mr-1 size-3"}),I(" "+_(e(v)("ui.widgets.search.close")),1)])])]),default:c(()=>[p(hr,{keyword:t.value,menus:h.menus,onClose:i},null,8,["keyword","menus"])]),_:1}),$("div",{class:"md:bg-accent group flex h-8 cursor-pointer items-center gap-3 rounded-2xl border-none bg-none px-2 py-0.5 outline-none",onClick:T[1]||(T[1]=H=>b())},[p(e(Ba),{class:"text-muted-foreground group-hover:text-foreground size-4 group-hover:opacity-100"}),$("span",kr,_(e(v)("ui.widgets.search.title")),1),h.enableShortcutKey?(u(),x("span",Cr,[I(_(e(bt)()?"Ctrl":"⌘")+" ",1),T[2]||(T[2]=$("kbd",null,"K",-1))])):(u(),x("span",Sr))])]))}}),Mr=["onKeydown"],_r={class:"w-full"},$r={class:"ml-2 flex w-full flex-col items-center"},Br={class:"text-foreground my-6 flex items-center font-medium"},Vr=L({name:"LockScreenModal",__name:"lock-screen-modal",props:{avatar:{default:""},text:{default:""}},emits:["submit"],setup(a,{emit:l}){const t=l,[o,{resetForm:s,validate:n,getValues:r}]=Ua(xt({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:w(()=>[{component:"VbenInputPassword",componentProps:{placeholder:v("ui.widgets.lockScreen.placeholder")},fieldName:"lockScreenPassword",formFieldProps:{validateOnBlur:!1},label:v("authentication.password"),rules:Ha().min(1,{message:v("ui.widgets.lockScreen.placeholder")})}]),showDefaultActions:!1})),[i]=gt({onConfirm(){d()},onOpenChange(f){f&&s()}});function d(){return Y(this,null,function*(){const{valid:f}=yield n(),m=yield r();f&&t("submit",m==null?void 0:m.lockScreenPassword)})}return(f,m)=>(u(),k(e(i),{footer:!1,"fullscreen-button":!1,title:e(v)("ui.widgets.lockScreen.title")},{default:c(()=>[$("div",{class:"mb-10 flex w-full flex-col items-center px-10",onKeydown:Wa(Se(d,["prevent"]),["enter"])},[$("div",_r,[$("div",$r,[p(e(vt),{src:f.avatar,class:"size-20","dot-class":"bottom-0 right-1 border-2 size-4 bg-green-500"},null,8,["src"]),$("div",Br,_(f.text),1)]),p(e(o)),p(e(Ae),{class:"mt-1 w-full",onClick:d},{default:c(()=>[I(_(e(v)("ui.widgets.lockScreen.screenButton")),1)]),_:1})])],40,Mr)]),_:1},8,["title"]))}}),Lr={class:"bg-background fixed z-[2000] size-full"},Er={class:"size-full"},zr={class:"flex h-full justify-center px-[10%]"},Pr={class:"bg-accent flex-center relative mb-14 mr-20 h-4/5 w-2/5 flex-auto rounded-3xl text-center text-[260px]"},Ir={class:"absolute left-4 top-4 text-xl font-semibold"},Ar={class:"bg-accent flex-center mb-14 h-4/5 w-2/5 flex-auto rounded-3xl text-center text-[260px]"},Ur=["onKeydown"],Hr={class:"flex-col-center mb-10 w-[300px]"},Wr={class:"enter-x mb-2 w-full items-center"},Or={class:"enter-y absolute bottom-5 w-full text-center xl:text-xl 2xl:text-3xl"},Dr={key:0,class:"enter-x mb-2 text-3xl"},Nr={class:"text-lg"},Rr={class:"text-3xl"},Ju=L({name:"LockScreen",__name:"lock-screen",props:{avatar:{default:""}},emits:["toLogin"],setup(a){const{locale:l}=Oa(),t=nt(),o=sn(),s=St(o,"A"),n=St(o,"HH"),r=St(o,"mm"),i=St(o,"YYYY-MM-DD dddd",{locales:l.value}),d=X(!1),{lockScreenPassword:f}=Da(t),[m,{form:g,validate:b}]=Ua(xt({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:w(()=>[{component:"VbenInputPassword",componentProps:{placeholder:v("ui.widgets.lockScreen.placeholder")},fieldName:"password",label:v("authentication.password"),rules:Ha().min(1,{message:v("authentication.passwordTip")})}]),showDefaultActions:!1})),h=w(()=>{var O;return(f==null?void 0:f.value)===((O=g==null?void 0:g.values)==null?void 0:O.password)});function T(){return Y(this,null,function*(){const{valid:O}=yield b();O&&(h.value?t.unlockScreen():g.setFieldError("password",v("authentication.passwordErrorTip")))})}function H(){d.value=!d.value}return So(),(O,M)=>(u(),x("div",Lr,[p(ut,{name:"slide-left"},{default:c(()=>[Me($("div",Er,[$("div",{class:"flex-col-center text-foreground/80 hover:text-foreground group my-4 cursor-pointer text-xl font-semibold",onClick:H},[p(e(ll),{class:"size-5 transition-all duration-300 group-hover:scale-125"}),$("span",null,_(e(v)("ui.widgets.lockScreen.unlock")),1)]),$("div",zr,[$("div",Pr,[$("span",Ir,_(e(s)),1),I(" "+_(e(n)),1)]),$("div",Ar,_(e(r)),1)])],512),[[Be,!d.value]])]),_:1}),p(ut,{name:"slide-right"},{default:c(()=>[d.value?(u(),x("div",{key:0,class:"flex-center size-full",onKeydown:Wa(Se(T,["prevent"]),["enter"])},[$("div",Hr,[p(e(vt),{src:O.avatar,class:"enter-x mb-6 size-20"},null,8,["src"]),$("div",Wr,[p(e(m))]),p(e(Ae),{class:"enter-x w-full",onClick:T},{default:c(()=>[I(_(e(v)("ui.widgets.lockScreen.entry")),1)]),_:1}),p(e(Ae),{class:"enter-x my-2 w-full",variant:"ghost",onClick:M[0]||(M[0]=D=>O.$emit("toLogin"))},{default:c(()=>[I(_(e(v)("ui.widgets.lockScreen.backToLogin")),1)]),_:1}),p(e(Ae),{class:"enter-x mr-2 w-full",variant:"ghost",onClick:H},{default:c(()=>[I(_(e(v)("common.back")),1)]),_:1})])],40,Ur)):U("",!0)]),_:1}),$("div",Or,[d.value?(u(),x("div",Dr,[I(_(e(n))+":"+_(e(r))+" ",1),$("span",Nr,_(e(s)),1)])):U("",!0),$("div",Rr,_(e(i)),1)])]))}}),Fr={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},Kr={class:"relative"},Gr={class:"flex items-center justify-between p-4 py-3"},jr={class:"text-foreground"},qr={class:"!flex max-h-[360px] w-full flex-col"},Yr=["onClick"],Xr={key:0,class:"bg-primary absolute right-2 top-2 h-2 w-2 rounded"},Zr={class:"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full"},Qr=["src"],Jr={class:"flex flex-col gap-1 leading-none"},ei={class:"font-semibold"},ti={class:"text-muted-foreground my-1 line-clamp-2 text-xs"},ai={class:"text-muted-foreground line-clamp-2 text-xs"},li={key:1,class:"flex-center text-muted-foreground min-h-[150px] w-full"},oi={class:"border-border flex items-center justify-between border-t px-4 py-3"},ni=L({name:"NotificationPopup",__name:"notification",props:{dot:{type:Boolean,default:!1},notifications:{default:()=>[]}},emits:["clear","makeAll","read","viewAll"],setup(a,{emit:l}){const t=l,[o,s]=rn();function n(){o.value=!1}function r(){t("viewAll"),n()}function i(){t("makeAll")}function d(){t("clear")}function f(m){t("read",m)}return(m,g)=>(u(),k(e(To),{open:e(o),"onUpdate:open":g[1]||(g[1]=b=>Kt(o)?o.value=b:null),"content-class":"relative right-2 w-[360px] p-0"},{trigger:c(()=>[$("div",{class:"flex-center mr-2 h-full",onClick:g[0]||(g[0]=Se(b=>e(s)(),["stop"]))},[p(e(tt),{class:"bell-button text-foreground relative"},{default:c(()=>[m.dot?(u(),x("span",Fr)):U("",!0),p(e(On),{class:"size-4"})]),_:1})])]),default:c(()=>[$("div",Kr,[$("div",Gr,[$("div",jr,_(e(v)("ui.widgets.notifications")),1),p(e(tt),{disabled:m.notifications.length<=0,tooltip:e(v)("ui.widgets.markAllAsRead"),onClick:i},{default:c(()=>[p(e(qn),{class:"size-4"})]),_:1},8,["disabled","tooltip"])]),m.notifications.length>0?(u(),k(e(yt),{key:0},{default:c(()=>[$("ul",qr,[(u(!0),x(Z,null,oe(m.notifications,b=>(u(),x("li",{key:b.title,class:"hover:bg-accent border-border relative flex w-full cursor-pointer items-start gap-5 border-t px-3 py-3",onClick:h=>f(b)},[b.isRead?U("",!0):(u(),x("span",Xr)),$("span",Zr,[$("img",{src:b.avatar,class:"aspect-square h-full w-full object-cover",role:"img"},null,8,Qr)]),$("div",Jr,[$("p",ei,_(b.title),1),$("p",ti,_(b.message),1),$("p",ai,_(b.date),1)])],8,Yr))),128))])]),_:1})):(u(),x("div",li,_(e(v)("common.noData")),1)),$("div",oi,[p(e(Ae),{disabled:m.notifications.length<=0,size:"sm",variant:"ghost",onClick:d},{default:c(()=>[I(_(e(v)("ui.widgets.clearNotifications")),1)]),_:1},8,["disabled"]),p(e(Ae),{size:"sm",onClick:r},{default:c(()=>[I(_(e(v)("ui.widgets.viewAll")),1)]),_:1})])])]),_:1},8,["open"]))}}),ec=Te(ni,[["__scopeId","data-v-d7a4acd4"]]),si={class:"flex flex-col py-4"},ri={class:"mb-3 font-semibold leading-none tracking-tight"},Ce=L({name:"PreferenceBlock",__name:"block",props:{title:{default:""}},setup(a){return(l,t)=>(u(),x("div",si,[$("h3",ri,_(l.title),1),P(l.$slots,"default")]))}}),ii={class:"flex items-center text-sm"},di={key:0,class:"ml-auto mr-2 text-xs opacity-60"},Q=L({name:"PreferenceSwitchItem",__name:"switch-item",props:be({disabled:{type:Boolean,default:!1},tip:{default:""}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(a){const l=y(a,"modelValue"),t=We();function o(){l.value=!l.value}return(s,n)=>(u(),x("div",{class:F([{"pointer-events-none opacity-50":s.disabled},"hover:bg-accent my-1 flex w-full items-center justify-between rounded-md px-2 py-2.5"]),onClick:o},[$("span",ii,[P(s.$slots,"default"),e(t).tip||s.tip?(u(),k(e(st),{key:0,side:"bottom"},{trigger:c(()=>[p(e(ct),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[P(s.$slots,"tip",{},()=>[s.tip?(u(!0),x(Z,{key:0},oe(s.tip.split(`
`),(r,i)=>(u(),x("p",{key:i},_(r),1))),128)):U("",!0)])]),_:3})):U("",!0)]),s.$slots.shortcut?(u(),x("span",di,[P(s.$slots,"shortcut")])):U("",!0),p(e(vs),{checked:l.value,"onUpdate:checked":n[0]||(n[0]=r=>l.value=r),onClick:n[1]||(n[1]=Se(()=>{},["stop"]))},null,8,["checked"])],2))}}),ui={key:0,class:"mb-2 mt-3 flex justify-between gap-3 px-2"},ci=["onClick"],pi=L({name:"PreferenceAnimation",__name:"animation",props:{transitionProgress:{type:Boolean,default:!1},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{}},emits:["update:transitionProgress","update:transitionName","update:transitionEnable","update:transitionLoading"],setup(a){const l=y(a,"transitionProgress"),t=y(a,"transitionName"),o=y(a,"transitionEnable"),s=y(a,"transitionLoading"),n=["fade","fade-slide","fade-up","fade-down"];function r(i){t.value=i}return(i,d)=>(u(),x(Z,null,[p(Q,{modelValue:l.value,"onUpdate:modelValue":d[0]||(d[0]=f=>l.value=f)},{default:c(()=>[I(_(e(v)("preferences.animation.progress")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:s.value,"onUpdate:modelValue":d[1]||(d[1]=f=>s.value=f)},{default:c(()=>[I(_(e(v)("preferences.animation.loading")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:o.value,"onUpdate:modelValue":d[2]||(d[2]=f=>o.value=f)},{default:c(()=>[I(_(e(v)("preferences.animation.transition")),1)]),_:1},8,["modelValue"]),o.value?(u(),x("div",ui,[(u(),x(Z,null,oe(n,f=>$("div",{key:f,class:F([{"outline-box-active":t.value===f},"outline-box p-2"]),onClick:m=>r(f)},[$("div",{class:F([`${f}-slow`,"bg-accent h-10 w-12 rounded-md"])},null,2)],10,ci)),64))])):U("",!0)],64))}}),fi={class:"flex items-center text-sm"},It=L({name:"PreferenceSelectItem",__name:"select-item",props:be({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(a){const l=y(a,"modelValue"),t=We();return(o,s)=>(u(),x("div",{class:F([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[$("span",fi,[P(o.$slots,"default"),e(t).tip?(u(),k(e(st),{key:0,side:"bottom"},{trigger:c(()=>[p(e(ct),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[P(o.$slots,"tip")]),_:3})):U("",!0)]),p(e(Mo),{modelValue:l.value,"onUpdate:modelValue":s[0]||(s[0]=n=>l.value=n)},{default:c(()=>[p(e(_o),{class:"h-8 w-[165px]"},{default:c(()=>[p(e($o),{placeholder:o.placeholder},null,8,["placeholder"])]),_:1}),p(e(Bo),null,{default:c(()=>[(u(!0),x(Z,null,oe(o.items,n=>(u(),k(e(Vo),{key:n.value,value:n.value},{default:c(()=>[I(_(n.label),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"])],2))}}),mi=L({name:"PreferenceGeneralConfig",__name:"general",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{}},emits:["update:appLocale","update:appDynamicTitle","update:appWatermark","update:appEnableCheckUpdates"],setup(a){const l=y(a,"appLocale"),t=y(a,"appDynamicTitle"),o=y(a,"appWatermark"),s=y(a,"appEnableCheckUpdates");return(n,r)=>(u(),x(Z,null,[p(It,{modelValue:l.value,"onUpdate:modelValue":r[0]||(r[0]=i=>l.value=i),items:e(Lo)},{default:c(()=>[I(_(e(v)("preferences.language")),1)]),_:1},8,["modelValue","items"]),p(Q,{modelValue:t.value,"onUpdate:modelValue":r[1]||(r[1]=i=>t.value=i)},{default:c(()=>[I(_(e(v)("preferences.dynamicTitle")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:o.value,"onUpdate:modelValue":r[2]||(r[2]=i=>o.value=i)},{default:c(()=>[I(_(e(v)("preferences.watermark")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:s.value,"onUpdate:modelValue":r[3]||(r[3]=i=>s.value=i)},{default:c(()=>[I(_(e(v)("preferences.checkUpdates")),1)]),_:1},8,["modelValue"])],64))}}),hi={class:"text-sm"},Zt=L({name:"PreferenceToggleItem",__name:"toggle-item",props:be({disabled:{type:Boolean,default:!1},items:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(a){const l=y(a,"modelValue");return(t,o)=>(u(),x("div",{class:F([{"pointer-events-none opacity-50":t.disabled},"hover:bg-accent flex w-full items-center justify-between rounded-md px-2 py-2"]),disabled:""},[$("span",hi,[P(t.$slots,"default")]),p(e(Ja),{modelValue:l.value,"onUpdate:modelValue":o[0]||(o[0]=s=>l.value=s),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(u(!0),x(Z,null,oe(t.items,s=>(u(),k(e(el),{key:s.value,value:s.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 rounded-sm"},{default:c(()=>[I(_(s.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])],2))}}),bi=L({name:"PreferenceBreadcrumbConfig",__name:"breadcrumb",props:be({disabled:{type:Boolean}},{breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{}}),emits:["update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbStyleType","update:breadcrumbShowHome","update:breadcrumbHideOnlyOne"],setup(a){const l=a,t=y(a,"breadcrumbEnable"),o=y(a,"breadcrumbShowIcon"),s=y(a,"breadcrumbStyleType"),n=y(a,"breadcrumbShowHome"),r=y(a,"breadcrumbHideOnlyOne"),i=[{label:v("preferences.normal"),value:"normal"},{label:v("preferences.breadcrumb.background"),value:"background"}],d=w(()=>!t.value||l.disabled);return(f,m)=>(u(),x(Z,null,[p(Q,{modelValue:t.value,"onUpdate:modelValue":m[0]||(m[0]=g=>t.value=g),disabled:f.disabled},{default:c(()=>[I(_(e(v)("preferences.breadcrumb.enable")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:r.value,"onUpdate:modelValue":m[1]||(m[1]=g=>r.value=g),disabled:d.value},{default:c(()=>[I(_(e(v)("preferences.breadcrumb.hideOnlyOne")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:o.value,"onUpdate:modelValue":m[2]||(m[2]=g=>o.value=g),disabled:d.value},{default:c(()=>[I(_(e(v)("preferences.breadcrumb.icon")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:n.value,"onUpdate:modelValue":m[3]||(m[3]=g=>n.value=g),disabled:d.value||!o.value},{default:c(()=>[I(_(e(v)("preferences.breadcrumb.home")),1)]),_:1},8,["modelValue","disabled"]),p(Zt,{modelValue:s.value,"onUpdate:modelValue":m[4]||(m[4]=g=>s.value=g),disabled:d.value,items:i},{default:c(()=>[I(_(e(v)("preferences.breadcrumb.style")),1)]),_:1},8,["modelValue","disabled"])],64))}}),vi={},gi={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function yi(a,l){return u(),x("svg",gi,l[0]||(l[0]=[rt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="53.60438" x="43.484" y="13.66705"></rect><path id="svg_14" d="m3.43932,15.53192c0,-1.08676 1.03344,-2 2.26323,-2l30.33036,0c1.22979,0 2.26323,0.91324 2.26323,2l0,17.24865c0,1.08676 -1.03344,2 -2.26323,2l-30.33036,0c-1.22979,0 -2.26323,-0.91324 -2.26323,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="95.02528" x="3.30419" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const pl=Te(vi,[["render",yi]]),wi={},xi={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function ki(a,l){return u(),x("svg",xi,l[0]||(l[0]=[rt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="41.98275" x="45.37589" y="13.53192"></rect><path id="svg_14" d="m16.4123,15.53192c0,-1.08676 0.74096,-2 1.62271,-2l21.74653,0c0.88175,0 1.62271,0.91324 1.62271,2l0,17.24865c0,1.08676 -0.74096,2 -1.62271,2l-21.74653,0c-0.88175,0 -1.62271,-0.91324 -1.62271,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="71.10636" x="16.54743" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const Ci=Te(wi,[["render",ki]]),Si={},Ti={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Mi(a,l){return u(),x("svg",Ti,l[0]||(l[0]=[$("g",null,[$("path",{id:"svg_1",d:"m0.13514,4.13514c0,-2.17352 1.82648,-4 4,-4l96,0c2.17352,0 4,1.82648 4,4l0,58c0,2.17352 -1.82648,4 -4,4l-96,0c-2.17352,0 -4,-1.82648 -4,-4l0,-58z",fill:"currentColor","fill-opacity":"0.02",opacity:"undefined",stroke:"null"}),$("rect",{id:"svg_13",fill:"currentColor","fill-opacity":"0.08",height:"26.57155",rx:"2",stroke:"null",width:"53.18333",x:"45.79979",y:"3.77232"}),$("path",{id:"svg_14",d:"m4.28142,5.96169c0,-1.37748 1.06465,-2.53502 2.33158,-2.53502l31.2463,0c1.26693,0 2.33158,1.15754 2.33158,2.53502l0,21.86282c0,1.37748 -1.06465,2.53502 -2.33158,2.53502l-31.2463,0c-1.26693,0 -2.33158,-1.15754 -2.33158,-2.53502l0,-21.86282z",fill:"currentColor","fill-opacity":"0.08",opacity:"undefined",stroke:"null"}),$("rect",{id:"svg_15",fill:"currentColor","fill-opacity":"0.08",height:"25.02247",rx:"2",stroke:"null",width:"94.39371",x:"4.56735",y:"34.92584"})],-1)]))}const _i=Te(Si,[["render",Mi]]),$i={},Bi={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Vi(a,l){return u(),x("svg",Bi,l[0]||(l[0]=[rt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><path id="svg_2" d="m-3.37838,3.7543a1.93401,4.02457 0 0 1 1.93401,-4.02457l11.3488,0l0,66.40541l-11.3488,0a1.93401,4.02457 0 0 1 -1.93401,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="15.46086"></rect><rect id="svg_4" fill="#ffffff" height="7.67897" rx="2" stroke="null" width="8.18938" x="0.58676" y="1.42154"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" rx="2" stroke="null" width="75.91967" x="25.38277" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="27.91529" y="3.69284"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.78868" y="3.69981"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="42.9287" x="58.75427" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="28.36894" x="26.14342" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="75.09493" x="26.34264" y="39.68822"></rect><rect id="svg_5" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.79832" y="28.39462"></rect><rect id="svg_6" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="41.80156"></rect><rect id="svg_7" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="55.36623"></rect><rect id="svg_16" fill="currentColor" fill-opacity="0.08" height="65.72065" stroke="null" width="12.49265" x="9.85477" y="-0.02618"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="35.14924" y="4.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="47.25735" y="4.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="59.23033" y="4.07319"></rect></g>',1)]))}const Li=Te($i,[["render",Vi]]),Ei={},zi={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Pi(a,l){return u(),x("svg",zi,l[0]||(l[0]=[rt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#b2b2b2" height="1.689" rx="1.395" stroke="null" width="6.52486" x="10.08168" y="3.50832"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="2.89362"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.58249" y="2.89362"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="44.13071" x="53.37873" y="13.45652"></rect><path id="svg_14" d="m19.4393,15.74245c0,-1.08676 0.79001,-2 1.73013,-2l23.18605,0c0.94011,0 1.73013,0.91324 1.73013,2l0,17.24865c0,1.08676 -0.79001,2 -1.73013,2l-23.18605,0c-0.94011,0 -1.73013,-0.91324 -1.73013,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="78.39372" x="19.93575" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="5.13843" rx="2" stroke="null" width="5.78397" x="1.5327" y="1.081"></rect><rect id="svg_5" fill="hsl(var(--primary))" height="56.81191" stroke="null" width="15.44642" x="-0.06423" y="9.03113"></rect><path id="svg_2" d="m2.38669,15.38074c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_6" d="m2.38669,28.43336c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_7" d="m2.17616,41.27545c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_9" d="m2.17616,54.32806c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path></g>',1)]))}const Ii=Te(Ei,[["render",Pi]]),Ai={},Ui={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Hi(a,l){return u(),x("svg",Ui,l[0]||(l[0]=[rt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="44.13071" x="53.37873" y="13.45652"></rect><path id="svg_14" d="m19.4393,15.74245c0,-1.08676 0.79001,-2 1.73013,-2l23.18605,0c0.94011,0 1.73013,0.91324 1.73013,2l0,17.24865c0,1.08676 -0.79001,2 -1.73013,2l-23.18605,0c-0.94011,0 -1.73013,-0.91324 -1.73013,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="78.39372" x="19.93575" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect><rect id="svg_5" fill="currentColor" fill-opacity="0.08" height="56.81191" stroke="null" width="15.44642" x="-0.06423" y="9.03113"></rect><path id="svg_2" d="m2.38669,15.38074c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_6" d="m2.38669,28.43336c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_7" d="m2.17616,41.27545c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_9" d="m2.17616,54.32806c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path></g>',1)]))}const Wi=Te(Ai,[["render",Hi]]),Oi={},Di={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Ni(a,l){return u(),x("svg",Di,l[0]||(l[0]=[rt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><path id="svg_2" d="m-3.37838,3.7543a1.93401,4.02457 0 0 1 1.93401,-4.02457l11.3488,0l0,66.40541l-11.3488,0a1.93401,4.02457 0 0 1 -1.93401,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="15.46086"></rect><rect id="svg_4" fill="#ffffff" height="7.67897" rx="2" stroke="null" width="8.18938" x="0.58676" y="1.42154"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="75.91967" x="25.38277" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="27.91529" y="3.69284"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.78868" y="3.69981"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="42.9287" x="58.75427" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="28.36894" x="26.14342" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="75.09493" x="26.34264" y="39.68822"></rect><rect id="svg_5" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.79832" y="28.39462"></rect><rect id="svg_6" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="41.80156"></rect><rect id="svg_7" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="55.36623"></rect><rect id="svg_16" fill="currentColor" fill-opacity="0.08" height="65.72065" stroke="null" width="12.49265" x="9.85477" y="-0.02618"></rect></g>',1)]))}const Ri=Te(Oi,[["render",Ni]]),Fi={},Ki={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Gi(a,l){return u(),x("svg",Ki,l[0]||(l[0]=[rt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104"></rect><path id="svg_2" d="m-3.37838,3.61916a4.4919,4.02457 0 0 1 4.4919,-4.02457l26.35848,0l0,66.40541l-26.35848,0a4.4919,4.02457 0 0 1 -4.4919,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" width="17.66" x="4.906" y="23.884"></rect><rect id="svg_4" fill="#ffffff" height="9.706" rx="2" width="9.811" x="8.83" y="5.881"></rect><path id="svg_5" d="m4.906,35.833c0,-0.75801 0.63699,-1.395 1.395,-1.395l14.87,0c0.75801,0 1.395,0.63699 1.395,1.395l0,-0.001c0,0.75801 -0.63699,1.395 -1.395,1.395l-14.87,0c-0.75801,0 -1.395,-0.63699 -1.395,-1.395l0,0.001z" fill="#ffffff" opacity="undefined"></path><rect id="svg_6" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="44.992"></rect><rect id="svg_7" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="55.546"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="73.53879" x="28.97986" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="32.039" y="3.89903"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.58249" y="3.49362"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="45.63141" x="56.05157" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="22.82978" x="29.38527" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="72.45771" x="28.97986" y="39.48203"></rect></g>',1)]))}const ji=Te(Fi,[["render",Gi]]),qi=pl,Yi={class:"flex w-full gap-5"},Xi=["onClick"],Zi={class:"text-muted-foreground mt-2 text-center text-xs"},Qi=L({name:"PreferenceLayoutContent",__name:"content",props:{modelValue:{default:"wide"},modelModifiers:{}},emits:["update:modelValue"],setup(a){const l=y(a,"modelValue"),t={compact:Ci,wide:qi},o=w(()=>[{name:v("preferences.wide"),type:"wide"},{name:v("preferences.compact"),type:"compact"}]);function s(n){return n===l.value?["outline-box-active"]:[]}return(n,r)=>(u(),x("div",Yi,[(u(!0),x(Z,null,oe(o.value,i=>(u(),x("div",{key:i.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:d=>l.value=i.type},[$("div",{class:F([s(i.type),"outline-box flex-center"])},[(u(),k(Le(t[i.type])))],2),$("div",Zi,_(i.name),1)],8,Xi))),128))]))}}),Ji={class:"flex items-center text-sm"},ft=L({name:"PreferenceSelectItem",__name:"input-item",props:be({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(a){const l=y(a,"modelValue"),t=We();return(o,s)=>(u(),x("div",{class:F([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[$("span",Ji,[P(o.$slots,"default"),e(t).tip?(u(),k(e(st),{key:0,side:"bottom"},{trigger:c(()=>[p(e(ct),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[P(o.$slots,"tip")]),_:3})):U("",!0)]),p(e(Eo),{modelValue:l.value,"onUpdate:modelValue":s[0]||(s[0]=n=>l.value=n),class:"h-8 w-[165px]"},null,8,["modelValue"])],2))}}),ed=L({__name:"copyright",props:be({disabled:{type:Boolean}},{copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{}}),emits:["update:copyrightEnable","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:copyrightCompanyName","update:copyrightCompanySiteLink"],setup(a){const l=a,t=y(a,"copyrightEnable"),o=y(a,"copyrightDate"),s=y(a,"copyrightIcp"),n=y(a,"copyrightIcpLink"),r=y(a,"copyrightCompanyName"),i=y(a,"copyrightCompanySiteLink"),d=w(()=>l.disabled||!t.value);return(f,m)=>(u(),x(Z,null,[p(Q,{modelValue:t.value,"onUpdate:modelValue":m[0]||(m[0]=g=>t.value=g),disabled:f.disabled},{default:c(()=>[I(_(e(v)("preferences.copyright.enable")),1)]),_:1},8,["modelValue","disabled"]),p(ft,{modelValue:r.value,"onUpdate:modelValue":m[1]||(m[1]=g=>r.value=g),disabled:d.value},{default:c(()=>[I(_(e(v)("preferences.copyright.companyName")),1)]),_:1},8,["modelValue","disabled"]),p(ft,{modelValue:i.value,"onUpdate:modelValue":m[2]||(m[2]=g=>i.value=g),disabled:d.value},{default:c(()=>[I(_(e(v)("preferences.copyright.companySiteLink")),1)]),_:1},8,["modelValue","disabled"]),p(ft,{modelValue:o.value,"onUpdate:modelValue":m[3]||(m[3]=g=>o.value=g),disabled:d.value},{default:c(()=>[I(_(e(v)("preferences.copyright.date")),1)]),_:1},8,["modelValue","disabled"]),p(ft,{modelValue:s.value,"onUpdate:modelValue":m[4]||(m[4]=g=>s.value=g),disabled:d.value},{default:c(()=>[I(_(e(v)("preferences.copyright.icp")),1)]),_:1},8,["modelValue","disabled"]),p(ft,{modelValue:n.value,"onUpdate:modelValue":m[5]||(m[5]=g=>n.value=g),disabled:d.value},{default:c(()=>[I(_(e(v)("preferences.copyright.icpLink")),1)]),_:1},8,["modelValue","disabled"])],64))}}),td=L({__name:"footer",props:{footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{}},emits:["update:footerEnable","update:footerFixed"],setup(a){const l=y(a,"footerEnable"),t=y(a,"footerFixed");return(o,s)=>(u(),x(Z,null,[p(Q,{modelValue:l.value,"onUpdate:modelValue":s[0]||(s[0]=n=>l.value=n)},{default:c(()=>[I(_(e(v)("preferences.footer.visible")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:t.value,"onUpdate:modelValue":s[1]||(s[1]=n=>t.value=n),disabled:!l.value},{default:c(()=>[I(_(e(v)("preferences.footer.fixed")),1)]),_:1},8,["modelValue","disabled"])],64))}}),ad=L({__name:"header",props:be({disabled:{type:Boolean}},{headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{},headerMenuAlign:{},headerMenuAlignModifiers:{}}),emits:["update:headerEnable","update:headerMode","update:headerMenuAlign"],setup(a){const l=y(a,"headerEnable"),t=y(a,"headerMode"),o=y(a,"headerMenuAlign"),s=[{label:v("preferences.header.modeStatic"),value:"static"},{label:v("preferences.header.modeFixed"),value:"fixed"},{label:v("preferences.header.modeAuto"),value:"auto"},{label:v("preferences.header.modeAutoScroll"),value:"auto-scroll"}],n=[{label:v("preferences.header.menuAlignStart"),value:"start"},{label:v("preferences.header.menuAlignCenter"),value:"center"},{label:v("preferences.header.menuAlignEnd"),value:"end"}];return(r,i)=>(u(),x(Z,null,[p(Q,{modelValue:l.value,"onUpdate:modelValue":i[0]||(i[0]=d=>l.value=d),disabled:r.disabled},{default:c(()=>[I(_(e(v)("preferences.header.visible")),1)]),_:1},8,["modelValue","disabled"]),p(It,{modelValue:t.value,"onUpdate:modelValue":i[1]||(i[1]=d=>t.value=d),disabled:!l.value,items:s},{default:c(()=>[I(_(e(v)("preferences.mode")),1)]),_:1},8,["modelValue","disabled"]),p(Zt,{modelValue:o.value,"onUpdate:modelValue":i[2]||(i[2]=d=>o.value=d),disabled:!l.value,items:n},{default:c(()=>[I(_(e(v)("preferences.header.menuAlign")),1)]),_:1},8,["modelValue","disabled"])],64))}}),ld={class:"flex w-full flex-wrap gap-5"},od=["onClick"],nd={class:"text-muted-foreground flex-center hover:text-foreground mt-2 text-center text-xs"},sd=L({name:"PreferenceLayout",__name:"layout",props:{modelValue:{default:"sidebar-nav"},modelModifiers:{}},emits:["update:modelValue"],setup(a){const l=y(a,"modelValue"),t={"full-content":_i,"header-nav":pl,"mixed-nav":Wi,"sidebar-mixed-nav":Ri,"sidebar-nav":ji,"header-mixed-nav":Li,"header-sidebar-nav":Ii},o=w(()=>[{name:v("preferences.vertical"),tip:v("preferences.verticalTip"),type:"sidebar-nav"},{name:v("preferences.twoColumn"),tip:v("preferences.twoColumnTip"),type:"sidebar-mixed-nav"},{name:v("preferences.horizontal"),tip:v("preferences.horizontalTip"),type:"header-nav"},{name:v("preferences.headerSidebarNav"),tip:v("preferences.headerSidebarNavTip"),type:"header-sidebar-nav"},{name:v("preferences.mixedMenu"),tip:v("preferences.mixedMenuTip"),type:"mixed-nav"},{name:v("preferences.headerTwoColumn"),tip:v("preferences.headerTwoColumnTip"),type:"header-mixed-nav"},{name:v("preferences.fullContent"),tip:v("preferences.fullContentTip"),type:"full-content"}]);function s(n){return n===l.value?["outline-box-active"]:[]}return(n,r)=>(u(),x("div",ld,[(u(!0),x(Z,null,oe(o.value,i=>(u(),x("div",{key:i.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:d=>l.value=i.type},[$("div",{class:F([s(i.type),"outline-box flex-center"])},[(u(),k(Le(t[i.type])))],2),$("div",nd,[I(_(i.name)+" ",1),i.tip?(u(),k(e(st),{key:0,side:"bottom"},{trigger:c(()=>[p(e(ct),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[I(" "+_(i.tip),1)]),_:2},1024)):U("",!0)])],8,od))),128))]))}}),rd=L({name:"PreferenceNavigationConfig",__name:"navigation",props:be({disabled:{type:Boolean},disabledNavigationSplit:{type:Boolean}},{navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{}}),emits:["update:navigationStyleType","update:navigationSplit","update:navigationAccordion"],setup(a){const l=y(a,"navigationStyleType"),t=y(a,"navigationSplit"),o=y(a,"navigationAccordion"),s=[{label:v("preferences.rounded"),value:"rounded"},{label:v("preferences.plain"),value:"plain"}];return(n,r)=>(u(),x(Z,null,[p(Zt,{modelValue:l.value,"onUpdate:modelValue":r[0]||(r[0]=i=>l.value=i),disabled:n.disabled,items:s},{default:c(()=>[I(_(e(v)("preferences.navigationMenu.style")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:t.value,"onUpdate:modelValue":r[1]||(r[1]=i=>t.value=i),disabled:n.disabledNavigationSplit||n.disabled},{tip:c(()=>[I(_(e(v)("preferences.navigationMenu.splitTip")),1)]),default:c(()=>[I(_(e(v)("preferences.navigationMenu.split"))+" ",1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:o.value,"onUpdate:modelValue":r[2]||(r[2]=i=>o.value=i),disabled:n.disabled},{default:c(()=>[I(_(e(v)("preferences.navigationMenu.accordion")),1)]),_:1},8,["modelValue","disabled"])],64))}}),id={class:"flex items-center text-sm"},dd=L({name:"PreferenceCheckboxItem",__name:"checkbox-item",props:be({disabled:{type:Boolean,default:!1},items:{default:()=>[]},multiple:{type:Boolean,default:!1},onBtnClick:{type:Function,default:()=>{}},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(a){const l=y(a,"modelValue"),t=We();return(o,s)=>(u(),x("div",{class:F([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[$("span",id,[P(o.$slots,"default"),e(t).tip?(u(),k(e(st),{key:0,side:"bottom"},{trigger:c(()=>[p(e(ct),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[P(o.$slots,"tip")]),_:3})):U("",!0)]),p(e(Ts),{modelValue:l.value,"onUpdate:modelValue":s[0]||(s[0]=n=>l.value=n),class:"h-8 w-[165px]",options:o.items,disabled:o.disabled,multiple:o.multiple,onBtnClick:o.onBtnClick},null,8,["modelValue","options","disabled","multiple","onBtnClick"])],2))}}),ud={class:"flex items-center text-sm"},fl=L({name:"PreferenceSelectItem",__name:"number-field-item",props:be({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""},tip:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(a){const l=y(a,"modelValue"),t=We();return(o,s)=>(u(),x("div",{class:F([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[$("span",ud,[P(o.$slots,"default"),e(t).tip||o.tip?(u(),k(e(st),{key:0,side:"bottom"},{trigger:c(()=>[p(e(ct),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[P(o.$slots,"tip",{},()=>[o.tip?(u(!0),x(Z,{key:0},oe(o.tip.split(`
`),(n,r)=>(u(),x("p",{key:r},_(n),1))),128)):U("",!0)])]),_:3})):U("",!0)]),p(e(cs),pe({modelValue:l.value,"onUpdate:modelValue":s[0]||(s[0]=n=>l.value=n)},o.$attrs,{class:"w-[165px]"}),{default:c(()=>[p(e(ps),null,{default:c(()=>[p(e(fs)),p(e(hs)),p(e(ms))]),_:1})]),_:1},16,["modelValue"])],2))}}),cd=L({__name:"sidebar",props:be({currentLayout:{},disabled:{type:Boolean}},{sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},sidebarAutoActivateChild:{type:Boolean},sidebarAutoActivateChildModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{},sidebarExpandOnHover:{type:Boolean},sidebarExpandOnHoverModifiers:{},sidebarButtons:{default:[]},sidebarButtonsModifiers:{},sidebarCollapsedButton:{type:Boolean},sidebarCollapsedButtonModifiers:{},sidebarFixedButton:{type:Boolean},sidebarFixedButtonModifiers:{}}),emits:["update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsedShowTitle","update:sidebarAutoActivateChild","update:sidebarCollapsed","update:sidebarExpandOnHover","update:sidebarButtons","update:sidebarCollapsedButton","update:sidebarFixedButton"],setup(a){const l=y(a,"sidebarEnable"),t=y(a,"sidebarWidth"),o=y(a,"sidebarCollapsedShowTitle"),s=y(a,"sidebarAutoActivateChild"),n=y(a,"sidebarCollapsed"),r=y(a,"sidebarExpandOnHover"),i=y(a,"sidebarButtons"),d=y(a,"sidebarCollapsedButton"),f=y(a,"sidebarFixedButton");qe(()=>{d.value&&!i.value.includes("collapsed")&&i.value.push("collapsed"),f.value&&!i.value.includes("fixed")&&i.value.push("fixed")});const m=()=>{d.value=!!i.value.includes("collapsed"),f.value=!!i.value.includes("fixed")};return(g,b)=>(u(),x(Z,null,[p(Q,{modelValue:l.value,"onUpdate:modelValue":b[0]||(b[0]=h=>l.value=h),disabled:g.disabled},{default:c(()=>[I(_(e(v)("preferences.sidebar.visible")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:n.value,"onUpdate:modelValue":b[1]||(b[1]=h=>n.value=h),disabled:!l.value||g.disabled},{default:c(()=>[I(_(e(v)("preferences.sidebar.collapsed")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:r.value,"onUpdate:modelValue":b[2]||(b[2]=h=>r.value=h),disabled:!l.value||g.disabled||!n.value,tip:e(v)("preferences.sidebar.expandOnHoverTip")},{default:c(()=>[I(_(e(v)("preferences.sidebar.expandOnHover")),1)]),_:1},8,["modelValue","disabled","tip"]),p(Q,{modelValue:o.value,"onUpdate:modelValue":b[3]||(b[3]=h=>o.value=h),disabled:!l.value||g.disabled||!n.value},{default:c(()=>[I(_(e(v)("preferences.sidebar.collapsedShowTitle")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:s.value,"onUpdate:modelValue":b[4]||(b[4]=h=>s.value=h),disabled:!l.value||!["sidebar-mixed-nav","mixed-nav","header-mixed-nav"].includes(g.currentLayout)||g.disabled,tip:e(v)("preferences.sidebar.autoActivateChildTip")},{default:c(()=>[I(_(e(v)("preferences.sidebar.autoActivateChild")),1)]),_:1},8,["modelValue","disabled","tip"]),p(dd,{items:[{label:e(v)("preferences.sidebar.buttonCollapsed"),value:"collapsed"},{label:e(v)("preferences.sidebar.buttonFixed"),value:"fixed"}],multiple:"",modelValue:i.value,"onUpdate:modelValue":b[5]||(b[5]=h=>i.value=h),"on-btn-click":m},{default:c(()=>[I(_(e(v)("preferences.sidebar.buttons")),1)]),_:1},8,["items","modelValue"]),p(fl,{modelValue:t.value,"onUpdate:modelValue":b[6]||(b[6]=h=>t.value=h),disabled:!l.value||g.disabled,max:320,min:160,step:10},{default:c(()=>[I(_(e(v)("preferences.sidebar.width")),1)]),_:1},8,["modelValue","disabled"])],64))}}),pd=L({name:"PreferenceTabsConfig",__name:"tabbar",props:be({disabled:{type:Boolean}},{tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarWheelable:{type:Boolean},tabbarWheelableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{},tabbarMaxCount:{},tabbarMaxCountModifiers:{},tabbarMiddleClickToClose:{type:Boolean},tabbarMiddleClickToCloseModifiers:{}}),emits:["update:tabbarEnable","update:tabbarShowIcon","update:tabbarPersist","update:tabbarDraggable","update:tabbarWheelable","update:tabbarStyleType","update:tabbarShowMore","update:tabbarShowMaximize","update:tabbarMaxCount","update:tabbarMiddleClickToClose"],setup(a){const l=y(a,"tabbarEnable"),t=y(a,"tabbarShowIcon"),o=y(a,"tabbarPersist"),s=y(a,"tabbarDraggable"),n=y(a,"tabbarWheelable"),r=y(a,"tabbarStyleType"),i=y(a,"tabbarShowMore"),d=y(a,"tabbarShowMaximize"),f=y(a,"tabbarMaxCount"),m=y(a,"tabbarMiddleClickToClose"),g=w(()=>[{label:v("preferences.tabbar.styleType.chrome"),value:"chrome"},{label:v("preferences.tabbar.styleType.plain"),value:"plain"},{label:v("preferences.tabbar.styleType.card"),value:"card"},{label:v("preferences.tabbar.styleType.brisk"),value:"brisk"}]);return(b,h)=>(u(),x(Z,null,[p(Q,{modelValue:l.value,"onUpdate:modelValue":h[0]||(h[0]=T=>l.value=T),disabled:b.disabled},{default:c(()=>[I(_(e(v)("preferences.tabbar.enable")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:o.value,"onUpdate:modelValue":h[1]||(h[1]=T=>o.value=T),disabled:!l.value},{default:c(()=>[I(_(e(v)("preferences.tabbar.persist")),1)]),_:1},8,["modelValue","disabled"]),p(fl,{modelValue:f.value,"onUpdate:modelValue":h[2]||(h[2]=T=>f.value=T),disabled:!l.value,max:30,min:0,step:5,tip:e(v)("preferences.tabbar.maxCountTip")},{default:c(()=>[I(_(e(v)("preferences.tabbar.maxCount")),1)]),_:1},8,["modelValue","disabled","tip"]),p(Q,{modelValue:s.value,"onUpdate:modelValue":h[3]||(h[3]=T=>s.value=T),disabled:!l.value},{default:c(()=>[I(_(e(v)("preferences.tabbar.draggable")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:n.value,"onUpdate:modelValue":h[4]||(h[4]=T=>n.value=T),disabled:!l.value,tip:e(v)("preferences.tabbar.wheelableTip")},{default:c(()=>[I(_(e(v)("preferences.tabbar.wheelable")),1)]),_:1},8,["modelValue","disabled","tip"]),p(Q,{modelValue:m.value,"onUpdate:modelValue":h[5]||(h[5]=T=>m.value=T),disabled:!l.value},{default:c(()=>[I(_(e(v)("preferences.tabbar.middleClickClose")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:t.value,"onUpdate:modelValue":h[6]||(h[6]=T=>t.value=T),disabled:!l.value},{default:c(()=>[I(_(e(v)("preferences.tabbar.icon")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:i.value,"onUpdate:modelValue":h[7]||(h[7]=T=>i.value=T),disabled:!l.value},{default:c(()=>[I(_(e(v)("preferences.tabbar.showMore")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:d.value,"onUpdate:modelValue":h[8]||(h[8]=T=>d.value=T),disabled:!l.value},{default:c(()=>[I(_(e(v)("preferences.tabbar.showMaximize")),1)]),_:1},8,["modelValue","disabled"]),p(It,{modelValue:r.value,"onUpdate:modelValue":h[9]||(h[9]=T=>r.value=T),items:g.value},{default:c(()=>[I(_(e(v)("preferences.tabbar.styleType.title")),1)]),_:1},8,["modelValue","items"])],64))}}),fd=L({name:"PreferenceInterfaceControl",__name:"widget",props:{widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:["update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:appPreferencesButtonPosition","update:widgetRefresh"],setup(a){const l=y(a,"widgetGlobalSearch"),t=y(a,"widgetFullscreen"),o=y(a,"widgetLanguageToggle"),s=y(a,"widgetNotification"),n=y(a,"widgetThemeToggle"),r=y(a,"widgetSidebarToggle"),i=y(a,"widgetLockScreen"),d=y(a,"appPreferencesButtonPosition"),f=y(a,"widgetRefresh"),m=w(()=>[{label:v("preferences.position.auto"),value:"auto"},{label:v("preferences.position.header"),value:"header"},{label:v("preferences.position.fixed"),value:"fixed"}]);return(g,b)=>(u(),x(Z,null,[p(Q,{modelValue:l.value,"onUpdate:modelValue":b[0]||(b[0]=h=>l.value=h)},{default:c(()=>[I(_(e(v)("preferences.widget.globalSearch")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:n.value,"onUpdate:modelValue":b[1]||(b[1]=h=>n.value=h)},{default:c(()=>[I(_(e(v)("preferences.widget.themeToggle")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:o.value,"onUpdate:modelValue":b[2]||(b[2]=h=>o.value=h)},{default:c(()=>[I(_(e(v)("preferences.widget.languageToggle")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:t.value,"onUpdate:modelValue":b[3]||(b[3]=h=>t.value=h)},{default:c(()=>[I(_(e(v)("preferences.widget.fullscreen")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:s.value,"onUpdate:modelValue":b[4]||(b[4]=h=>s.value=h)},{default:c(()=>[I(_(e(v)("preferences.widget.notification")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:i.value,"onUpdate:modelValue":b[5]||(b[5]=h=>i.value=h)},{default:c(()=>[I(_(e(v)("preferences.widget.lockScreen")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:r.value,"onUpdate:modelValue":b[6]||(b[6]=h=>r.value=h)},{default:c(()=>[I(_(e(v)("preferences.widget.sidebarToggle")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:f.value,"onUpdate:modelValue":b[7]||(b[7]=h=>f.value=h)},{default:c(()=>[I(_(e(v)("preferences.widget.refresh")),1)]),_:1},8,["modelValue"]),p(It,{modelValue:d.value,"onUpdate:modelValue":b[8]||(b[8]=h=>d.value=h),items:m.value},{default:c(()=>[I(_(e(v)("preferences.position.title")),1)]),_:1},8,["modelValue","items"])],64))}}),md=L({name:"PreferenceGeneralConfig",__name:"global",props:{shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysLogout:{type:Boolean},shortcutKeysLogoutModifiers:{},shortcutKeysLockScreen:{type:Boolean},shortcutKeysLockScreenModifiers:{}},emits:["update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysLogout","update:shortcutKeysLockScreen"],setup(a){const l=y(a,"shortcutKeysEnable"),t=y(a,"shortcutKeysGlobalSearch"),o=y(a,"shortcutKeysLogout"),s=y(a,"shortcutKeysLockScreen"),n=w(()=>bt()?"Alt":"⌥");return(r,i)=>(u(),x(Z,null,[p(Q,{modelValue:l.value,"onUpdate:modelValue":i[0]||(i[0]=d=>l.value=d)},{default:c(()=>[I(_(e(v)("preferences.shortcutKeys.title")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:t.value,"onUpdate:modelValue":i[1]||(i[1]=d=>t.value=d),disabled:!l.value},{shortcut:c(()=>[I(_(e(bt)()?"Ctrl":"⌘")+" ",1),i[4]||(i[4]=$("kbd",null," K ",-1))]),default:c(()=>[I(_(e(v)("preferences.shortcutKeys.search"))+" ",1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:o.value,"onUpdate:modelValue":i[2]||(i[2]=d=>o.value=d),disabled:!l.value},{shortcut:c(()=>[I(_(n.value)+" Q ",1)]),default:c(()=>[I(_(e(v)("preferences.shortcutKeys.logout"))+" ",1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:s.value,"onUpdate:modelValue":i[3]||(i[3]=d=>s.value=d),disabled:!l.value},{shortcut:c(()=>[I(_(n.value)+" L ",1)]),default:c(()=>[I(_(e(v)("ui.widgets.lockScreen.title"))+" ",1)]),_:1},8,["modelValue","disabled"])],64))}}),hd={class:"flex w-full flex-wrap justify-between"},bd=["onClick"],vd={class:"flex-center relative size-5 rounded-sm"},gd=["value"],yd={class:"text-muted-foreground my-2 text-center text-xs"},wd=L({name:"PreferenceBuiltinTheme",__name:"builtin",props:be({isDark:{type:Boolean}},{modelValue:{default:"default"},modelModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{}}),emits:["update:modelValue","update:themeColorPrimary"],setup(a){const l=a,t=X(),o=y(a,"modelValue"),s=y(a,"themeColorPrimary"),n=Lt(b=>{s.value=b},300,!0,!0),r=w(()=>new dn(s.value||"").toHexString()),i=w(()=>[...un]);function d(b){switch(b){case"custom":return v("preferences.theme.builtin.custom");case"deep-blue":return v("preferences.theme.builtin.deepBlue");case"deep-green":return v("preferences.theme.builtin.deepGreen");case"default":return v("preferences.theme.builtin.default");case"gray":return v("preferences.theme.builtin.gray");case"green":return v("preferences.theme.builtin.green");case"neutral":return v("preferences.theme.builtin.neutral");case"orange":return v("preferences.theme.builtin.orange");case"pink":return v("preferences.theme.builtin.pink");case"rose":return v("preferences.theme.builtin.rose");case"sky-blue":return v("preferences.theme.builtin.skyBlue");case"slate":return v("preferences.theme.builtin.slate");case"violet":return v("preferences.theme.builtin.violet");case"yellow":return v("preferences.theme.builtin.yellow");case"zinc":return v("preferences.theme.builtin.zinc")}}function f(b){o.value=b.type}function m(b){const h=b.target;n(cn(h.value))}function g(){var b,h,T;(T=(h=(b=t.value)==null?void 0:b[0])==null?void 0:h.click)==null||T.call(h)}return de(()=>[o.value,l.isDark],([b,h])=>{const T=i.value.find(H=>H.type===b);if(T){const H=h&&T.darkPrimaryColor||T.primaryColor;s.value=H||T.color}}),(b,h)=>(u(),x("div",hd,[(u(!0),x(Z,null,oe(i.value,T=>(u(),x("div",{key:T.type,class:"flex cursor-pointer flex-col",onClick:H=>f(T)},[$("div",{class:F([{"outline-box-active":T.type===o.value},"outline-box flex-center group cursor-pointer"])},[T.type!=="custom"?(u(),x("div",{key:0,style:se({backgroundColor:T.color}),class:"mx-10 my-2 size-5 rounded-md"},null,4)):(u(),x("div",{key:1,class:"size-full px-10 py-2",onClick:Se(g,["stop"])},[$("div",vd,[p(e(Qn),{class:"absolute z-10 size-5 opacity-60 group-hover:opacity-100"}),$("input",{ref_for:!0,ref_key:"colorInput",ref:t,value:r.value,class:"absolute inset-0 opacity-0",type:"color",onInput:m},null,40,gd)])]))],2),$("div",yd,_(d(T.type)),1)],8,bd))),128))]))}}),xd=L({name:"PreferenceColorMode",__name:"color-mode",props:{appColorWeakMode:{type:Boolean,default:!1},appColorWeakModeModifiers:{},appColorGrayMode:{type:Boolean,default:!1},appColorGrayModeModifiers:{}},emits:["update:appColorWeakMode","update:appColorGrayMode"],setup(a){const l=y(a,"appColorWeakMode"),t=y(a,"appColorGrayMode");return(o,s)=>(u(),x(Z,null,[p(Q,{modelValue:l.value,"onUpdate:modelValue":s[0]||(s[0]=n=>l.value=n)},{default:c(()=>[I(_(e(v)("preferences.theme.weakMode")),1)]),_:1},8,["modelValue"]),p(Q,{modelValue:t.value,"onUpdate:modelValue":s[1]||(s[1]=n=>t.value=n)},{default:c(()=>[I(_(e(v)("preferences.theme.grayMode")),1)]),_:1},8,["modelValue"])],64))}}),kd=L({name:"PreferenceColorMode",__name:"radius",props:{themeRadius:{default:"0.5"},themeRadiusModifiers:{}},emits:["update:themeRadius"],setup(a){const l=y(a,"themeRadius"),t=[{label:"0",value:"0"},{label:"0.25",value:"0.25"},{label:"0.5",value:"0.5"},{label:"0.75",value:"0.75"},{label:"1",value:"1"}];return(o,s)=>(u(),k(e(Ja),{modelValue:l.value,"onUpdate:modelValue":s[0]||(s[0]=n=>l.value=n),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(u(),x(Z,null,oe(t,n=>p(e(el),{key:n.value,value:n.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 w-16 rounded-sm"},{default:c(()=>[I(_(n.label),1)]),_:2},1032,["value"])),64))]),_:1},8,["modelValue"]))}}),Cd={class:"flex w-full flex-wrap justify-between"},Sd=["onClick"],Td={class:"text-muted-foreground mt-2 text-center text-xs"},Md=L({name:"PreferenceTheme",__name:"theme",props:{modelValue:{default:"auto"},modelModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{}},emits:["update:modelValue","update:themeSemiDarkSidebar","update:themeSemiDarkHeader"],setup(a){const l=y(a,"modelValue"),t=y(a,"themeSemiDarkSidebar"),o=y(a,"themeSemiDarkHeader"),s=[{icon:Cn,name:"light"},{icon:Sn,name:"dark"},{icon:Tn,name:"auto"}];function n(i){return i===l.value?["outline-box-active"]:[]}function r(i){switch(i){case"auto":return v("preferences.followSystem");case"dark":return v("preferences.theme.dark");case"light":return v("preferences.theme.light")}}return(i,d)=>(u(),x("div",Cd,[(u(),x(Z,null,oe(s,f=>$("div",{key:f.name,class:"flex cursor-pointer flex-col",onClick:m=>l.value=f.name},[$("div",{class:F([n(f.name),"outline-box flex-center py-4"])},[(u(),k(Le(f.icon),{class:"mx-9 size-5"}))],2),$("div",Td,_(r(f.name)),1)],8,Sd)),64)),p(Q,{modelValue:t.value,"onUpdate:modelValue":d[0]||(d[0]=f=>t.value=f),disabled:l.value==="dark",class:"mt-6"},{default:c(()=>[I(_(e(v)("preferences.theme.darkSidebar")),1)]),_:1},8,["modelValue","disabled"]),p(Q,{modelValue:o.value,"onUpdate:modelValue":d[1]||(d[1]=f=>o.value=f),disabled:l.value==="dark"},{default:c(()=>[I(_(e(v)("preferences.theme.darkHeader")),1)]),_:1},8,["modelValue","disabled"])]))}}),_d={class:"flex items-center"},$d={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},Bd={class:"p-1"},Vd=L({__name:"preferences-drawer",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appLayout:{},appLayoutModifiers:{},appColorGrayMode:{type:Boolean},appColorGrayModeModifiers:{},appColorWeakMode:{type:Boolean},appColorWeakModeModifiers:{},appContentCompact:{},appContentCompactModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},transitionProgress:{type:Boolean},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{},themeBuiltinType:{},themeBuiltinTypeModifiers:{},themeMode:{},themeModeModifiers:{},themeRadius:{},themeRadiusModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{},sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},sidebarAutoActivateChild:{type:Boolean},sidebarAutoActivateChildModifiers:{},sidebarExpandOnHover:{type:Boolean},sidebarExpandOnHoverModifiers:{},sidebarCollapsedButton:{type:Boolean},sidebarCollapsedButtonModifiers:{},sidebarFixedButton:{type:Boolean},sidebarFixedButtonModifiers:{},headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{},headerMenuAlign:{},headerMenuAlignModifiers:{},breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{},tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarWheelable:{type:Boolean},tabbarWheelableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},tabbarMaxCount:{},tabbarMaxCountModifiers:{},tabbarMiddleClickToClose:{type:Boolean},tabbarMiddleClickToCloseModifiers:{},navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{},footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{},copyrightSettingShow:{type:Boolean},copyrightSettingShowModifiers:{},copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysGlobalLogout:{type:Boolean},shortcutKeysGlobalLogoutModifiers:{},shortcutKeysGlobalLockScreen:{type:Boolean},shortcutKeysGlobalLockScreenModifiers:{},widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:be(["clearPreferencesAndLogout"],["update:appLocale","update:appDynamicTitle","update:appLayout","update:appColorGrayMode","update:appColorWeakMode","update:appContentCompact","update:appWatermark","update:appEnableCheckUpdates","update:appPreferencesButtonPosition","update:transitionProgress","update:transitionName","update:transitionLoading","update:transitionEnable","update:themeColorPrimary","update:themeBuiltinType","update:themeMode","update:themeRadius","update:themeSemiDarkSidebar","update:themeSemiDarkHeader","update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsed","update:sidebarCollapsedShowTitle","update:sidebarAutoActivateChild","update:sidebarExpandOnHover","update:sidebarCollapsedButton","update:sidebarFixedButton","update:headerEnable","update:headerMode","update:headerMenuAlign","update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbShowHome","update:breadcrumbStyleType","update:breadcrumbHideOnlyOne","update:tabbarEnable","update:tabbarShowIcon","update:tabbarShowMore","update:tabbarShowMaximize","update:tabbarPersist","update:tabbarDraggable","update:tabbarWheelable","update:tabbarStyleType","update:tabbarMaxCount","update:tabbarMiddleClickToClose","update:navigationStyleType","update:navigationSplit","update:navigationAccordion","update:footerEnable","update:footerFixed","update:copyrightSettingShow","update:copyrightEnable","update:copyrightCompanyName","update:copyrightCompanySiteLink","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysGlobalLogout","update:shortcutKeysGlobalLockScreen","update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:widgetRefresh"]),setup(a,{emit:l}){const t=l,o=zo.getMessage(),s=y(a,"appLocale"),n=y(a,"appDynamicTitle"),r=y(a,"appLayout"),i=y(a,"appColorGrayMode"),d=y(a,"appColorWeakMode"),f=y(a,"appContentCompact"),m=y(a,"appWatermark"),g=y(a,"appEnableCheckUpdates"),b=y(a,"appPreferencesButtonPosition"),h=y(a,"transitionProgress"),T=y(a,"transitionName"),H=y(a,"transitionLoading"),O=y(a,"transitionEnable"),M=y(a,"themeColorPrimary"),D=y(a,"themeBuiltinType"),R=y(a,"themeMode"),A=y(a,"themeRadius"),V=y(a,"themeSemiDarkSidebar"),K=y(a,"themeSemiDarkHeader"),E=y(a,"sidebarEnable"),B=y(a,"sidebarWidth"),N=y(a,"sidebarCollapsed"),q=y(a,"sidebarCollapsedShowTitle"),te=y(a,"sidebarAutoActivateChild"),ue=y(a,"sidebarExpandOnHover"),ve=y(a,"sidebarCollapsedButton"),j=y(a,"sidebarFixedButton"),J=y(a,"headerEnable"),ge=y(a,"headerMode"),xe=y(a,"headerMenuAlign"),Ve=y(a,"breadcrumbEnable"),Oe=y(a,"breadcrumbShowIcon"),_e=y(a,"breadcrumbShowHome"),G=y(a,"breadcrumbStyleType"),ae=y(a,"breadcrumbHideOnlyOne"),le=y(a,"tabbarEnable"),ye=y(a,"tabbarShowIcon"),$e=y(a,"tabbarShowMore"),ze=y(a,"tabbarShowMaximize"),ie=y(a,"tabbarPersist"),he=y(a,"tabbarDraggable"),fe=y(a,"tabbarWheelable"),Je=y(a,"tabbarStyleType"),et=y(a,"tabbarMaxCount"),De=y(a,"tabbarMiddleClickToClose"),W=y(a,"navigationStyleType"),ee=y(a,"navigationSplit"),ce=y(a,"navigationAccordion"),Pe=y(a,"footerEnable"),Ie=y(a,"footerFixed"),Tl=y(a,"copyrightSettingShow"),ea=y(a,"copyrightEnable"),ta=y(a,"copyrightCompanyName"),aa=y(a,"copyrightCompanySiteLink"),la=y(a,"copyrightDate"),oa=y(a,"copyrightIcp"),na=y(a,"copyrightIcpLink"),sa=y(a,"shortcutKeysEnable"),ra=y(a,"shortcutKeysGlobalSearch"),ia=y(a,"shortcutKeysGlobalLogout"),da=y(a,"shortcutKeysGlobalLockScreen"),ua=y(a,"widgetGlobalSearch"),ca=y(a,"widgetFullscreen"),pa=y(a,"widgetLanguageToggle"),fa=y(a,"widgetNotification"),ma=y(a,"widgetThemeToggle"),ha=y(a,"widgetSidebarToggle"),ba=y(a,"widgetLockScreen"),va=y(a,"widgetRefresh"),{diffPreference:it,isDark:Ml,isFullContent:At,isHeaderNav:_l,isHeaderSidebarNav:$l,isMixedNav:ga,isSideMixedNav:Bl,isSideMode:Vl,isSideNav:Ll}=lt(),{copy:El}=pn({legacy:!0}),[zl]=tl(),ya=X("appearance"),Pl=w(()=>[{label:v("preferences.appearance"),value:"appearance"},{label:v("preferences.layout"),value:"layout"},{label:v("preferences.shortcutKeys.title"),value:"shortcutKey"},{label:v("preferences.general"),value:"general"}]),Il=w(()=>!At.value&&!ga.value&&!_l.value&&z.header.enable);function Al(){return Y(this,null,function*(){var Ut;yield El(JSON.stringify(it.value,null,2)),(Ut=o.copyPreferencesSuccess)==null||Ut.call(o,v("preferences.copyPreferencesSuccessTitle"),v("preferences.copyPreferencesSuccess"))})}function Ul(){return Y(this,null,function*(){Ca(),fn(),t("clearPreferencesAndLogout")})}function Hl(){return Y(this,null,function*(){it.value&&(Ca(),yield Na(z.app.locale))})}return(Ut,C)=>(u(),x("div",null,[p(e(zl),{description:e(v)("preferences.subtitle"),title:e(v)("preferences.title"),class:"sm:max-w-sm"},{extra:c(()=>[$("div",_d,[p(e(tt),{disabled:!e(it),tooltip:e(v)("preferences.resetTip"),class:"relative"},{default:c(()=>[e(it)?(u(),x("span",$d)):U("",!0),p(e(Yt),{class:"size-4",onClick:Hl})]),_:1},8,["disabled","tooltip"])])]),footer:c(()=>[p(e(Ae),{disabled:!e(it),class:"mx-4 w-full",size:"sm",variant:"default",onClick:Al},{default:c(()=>[p(e(Rn),{class:"mr-2 size-3"}),I(" "+_(e(v)("preferences.copyPreferences")),1)]),_:1},8,["disabled"]),p(e(Ae),{disabled:!e(it),class:"mr-4 w-full",size:"sm",variant:"ghost",onClick:Ul},{default:c(()=>[I(_(e(v)("preferences.clearAndLogout")),1)]),_:1},8,["disabled"])]),default:c(()=>[$("div",Bd,[p(e(er),{modelValue:ya.value,"onUpdate:modelValue":C[68]||(C[68]=S=>ya.value=S),tabs:Pl.value},{general:c(()=>[p(e(Ce),{title:e(v)("preferences.general")},{default:c(()=>[p(e(mi),{"app-dynamic-title":n.value,"onUpdate:appDynamicTitle":C[0]||(C[0]=S=>n.value=S),"app-enable-check-updates":g.value,"onUpdate:appEnableCheckUpdates":C[1]||(C[1]=S=>g.value=S),"app-locale":s.value,"onUpdate:appLocale":C[2]||(C[2]=S=>s.value=S),"app-watermark":m.value,"onUpdate:appWatermark":C[3]||(C[3]=S=>m.value=S)},null,8,["app-dynamic-title","app-enable-check-updates","app-locale","app-watermark"])]),_:1},8,["title"]),p(e(Ce),{title:e(v)("preferences.animation.title")},{default:c(()=>[p(e(pi),{"transition-enable":O.value,"onUpdate:transitionEnable":C[4]||(C[4]=S=>O.value=S),"transition-loading":H.value,"onUpdate:transitionLoading":C[5]||(C[5]=S=>H.value=S),"transition-name":T.value,"onUpdate:transitionName":C[6]||(C[6]=S=>T.value=S),"transition-progress":h.value,"onUpdate:transitionProgress":C[7]||(C[7]=S=>h.value=S)},null,8,["transition-enable","transition-loading","transition-name","transition-progress"])]),_:1},8,["title"])]),appearance:c(()=>[p(e(Ce),{title:e(v)("preferences.theme.title")},{default:c(()=>[p(e(Md),{modelValue:R.value,"onUpdate:modelValue":C[8]||(C[8]=S=>R.value=S),"theme-semi-dark-header":K.value,"onUpdate:themeSemiDarkHeader":C[9]||(C[9]=S=>K.value=S),"theme-semi-dark-sidebar":V.value,"onUpdate:themeSemiDarkSidebar":C[10]||(C[10]=S=>V.value=S)},null,8,["modelValue","theme-semi-dark-header","theme-semi-dark-sidebar"])]),_:1},8,["title"]),p(e(Ce),{title:e(v)("preferences.theme.builtin.title")},{default:c(()=>[p(e(wd),{modelValue:D.value,"onUpdate:modelValue":C[11]||(C[11]=S=>D.value=S),"theme-color-primary":M.value,"onUpdate:themeColorPrimary":C[12]||(C[12]=S=>M.value=S),"is-dark":e(Ml)},null,8,["modelValue","theme-color-primary","is-dark"])]),_:1},8,["title"]),p(e(Ce),{title:e(v)("preferences.theme.radius")},{default:c(()=>[p(e(kd),{modelValue:A.value,"onUpdate:modelValue":C[13]||(C[13]=S=>A.value=S)},null,8,["modelValue"])]),_:1},8,["title"]),p(e(Ce),{title:e(v)("preferences.other")},{default:c(()=>[p(e(xd),{"app-color-gray-mode":i.value,"onUpdate:appColorGrayMode":C[14]||(C[14]=S=>i.value=S),"app-color-weak-mode":d.value,"onUpdate:appColorWeakMode":C[15]||(C[15]=S=>d.value=S)},null,8,["app-color-gray-mode","app-color-weak-mode"])]),_:1},8,["title"])]),layout:c(()=>[p(e(Ce),{title:e(v)("preferences.layout")},{default:c(()=>[p(e(sd),{modelValue:r.value,"onUpdate:modelValue":C[16]||(C[16]=S=>r.value=S)},null,8,["modelValue"])]),_:1},8,["title"]),p(e(Ce),{title:e(v)("preferences.content")},{default:c(()=>[p(e(Qi),{modelValue:f.value,"onUpdate:modelValue":C[17]||(C[17]=S=>f.value=S)},null,8,["modelValue"])]),_:1},8,["title"]),p(e(Ce),{title:e(v)("preferences.sidebar.title")},{default:c(()=>[p(e(cd),{"sidebar-auto-activate-child":te.value,"onUpdate:sidebarAutoActivateChild":C[18]||(C[18]=S=>te.value=S),"sidebar-collapsed":N.value,"onUpdate:sidebarCollapsed":C[19]||(C[19]=S=>N.value=S),"sidebar-collapsed-show-title":q.value,"onUpdate:sidebarCollapsedShowTitle":C[20]||(C[20]=S=>q.value=S),"sidebar-enable":E.value,"onUpdate:sidebarEnable":C[21]||(C[21]=S=>E.value=S),"sidebar-expand-on-hover":ue.value,"onUpdate:sidebarExpandOnHover":C[22]||(C[22]=S=>ue.value=S),"sidebar-width":B.value,"onUpdate:sidebarWidth":C[23]||(C[23]=S=>B.value=S),"sidebar-collapsed-button":ve.value,"onUpdate:sidebarCollapsedButton":C[24]||(C[24]=S=>ve.value=S),"sidebar-fixed-button":j.value,"onUpdate:sidebarFixedButton":C[25]||(C[25]=S=>j.value=S),"current-layout":r.value,disabled:!e(Vl)},null,8,["sidebar-auto-activate-child","sidebar-collapsed","sidebar-collapsed-show-title","sidebar-enable","sidebar-expand-on-hover","sidebar-width","sidebar-collapsed-button","sidebar-fixed-button","current-layout","disabled"])]),_:1},8,["title"]),p(e(Ce),{title:e(v)("preferences.header.title")},{default:c(()=>[p(e(ad),{"header-enable":J.value,"onUpdate:headerEnable":C[26]||(C[26]=S=>J.value=S),"header-menu-align":xe.value,"onUpdate:headerMenuAlign":C[27]||(C[27]=S=>xe.value=S),"header-mode":ge.value,"onUpdate:headerMode":C[28]||(C[28]=S=>ge.value=S),disabled:e(At)},null,8,["header-enable","header-menu-align","header-mode","disabled"])]),_:1},8,["title"]),p(e(Ce),{title:e(v)("preferences.navigationMenu.title")},{default:c(()=>[p(e(rd),{"navigation-accordion":ce.value,"onUpdate:navigationAccordion":C[29]||(C[29]=S=>ce.value=S),"navigation-split":ee.value,"onUpdate:navigationSplit":C[30]||(C[30]=S=>ee.value=S),"navigation-style-type":W.value,"onUpdate:navigationStyleType":C[31]||(C[31]=S=>W.value=S),disabled:e(At),"disabled-navigation-split":!e(ga)},null,8,["navigation-accordion","navigation-split","navigation-style-type","disabled","disabled-navigation-split"])]),_:1},8,["title"]),p(e(Ce),{title:e(v)("preferences.breadcrumb.title")},{default:c(()=>[p(e(bi),{"breadcrumb-enable":Ve.value,"onUpdate:breadcrumbEnable":C[32]||(C[32]=S=>Ve.value=S),"breadcrumb-hide-only-one":ae.value,"onUpdate:breadcrumbHideOnlyOne":C[33]||(C[33]=S=>ae.value=S),"breadcrumb-show-home":_e.value,"onUpdate:breadcrumbShowHome":C[34]||(C[34]=S=>_e.value=S),"breadcrumb-show-icon":Oe.value,"onUpdate:breadcrumbShowIcon":C[35]||(C[35]=S=>Oe.value=S),"breadcrumb-style-type":G.value,"onUpdate:breadcrumbStyleType":C[36]||(C[36]=S=>G.value=S),disabled:!Il.value||!(e(Ll)||e(Bl)||e($l))},null,8,["breadcrumb-enable","breadcrumb-hide-only-one","breadcrumb-show-home","breadcrumb-show-icon","breadcrumb-style-type","disabled"])]),_:1},8,["title"]),p(e(Ce),{title:e(v)("preferences.tabbar.title")},{default:c(()=>[p(e(pd),{"tabbar-draggable":he.value,"onUpdate:tabbarDraggable":C[37]||(C[37]=S=>he.value=S),"tabbar-enable":le.value,"onUpdate:tabbarEnable":C[38]||(C[38]=S=>le.value=S),"tabbar-persist":ie.value,"onUpdate:tabbarPersist":C[39]||(C[39]=S=>ie.value=S),"tabbar-show-icon":ye.value,"onUpdate:tabbarShowIcon":C[40]||(C[40]=S=>ye.value=S),"tabbar-show-maximize":ze.value,"onUpdate:tabbarShowMaximize":C[41]||(C[41]=S=>ze.value=S),"tabbar-show-more":$e.value,"onUpdate:tabbarShowMore":C[42]||(C[42]=S=>$e.value=S),"tabbar-style-type":Je.value,"onUpdate:tabbarStyleType":C[43]||(C[43]=S=>Je.value=S),"tabbar-wheelable":fe.value,"onUpdate:tabbarWheelable":C[44]||(C[44]=S=>fe.value=S),"tabbar-max-count":et.value,"onUpdate:tabbarMaxCount":C[45]||(C[45]=S=>et.value=S),"tabbar-middle-click-to-close":De.value,"onUpdate:tabbarMiddleClickToClose":C[46]||(C[46]=S=>De.value=S)},null,8,["tabbar-draggable","tabbar-enable","tabbar-persist","tabbar-show-icon","tabbar-show-maximize","tabbar-show-more","tabbar-style-type","tabbar-wheelable","tabbar-max-count","tabbar-middle-click-to-close"])]),_:1},8,["title"]),p(e(Ce),{title:e(v)("preferences.widget.title")},{default:c(()=>[p(e(fd),{"app-preferences-button-position":b.value,"onUpdate:appPreferencesButtonPosition":C[47]||(C[47]=S=>b.value=S),"widget-fullscreen":ca.value,"onUpdate:widgetFullscreen":C[48]||(C[48]=S=>ca.value=S),"widget-global-search":ua.value,"onUpdate:widgetGlobalSearch":C[49]||(C[49]=S=>ua.value=S),"widget-language-toggle":pa.value,"onUpdate:widgetLanguageToggle":C[50]||(C[50]=S=>pa.value=S),"widget-lock-screen":ba.value,"onUpdate:widgetLockScreen":C[51]||(C[51]=S=>ba.value=S),"widget-notification":fa.value,"onUpdate:widgetNotification":C[52]||(C[52]=S=>fa.value=S),"widget-refresh":va.value,"onUpdate:widgetRefresh":C[53]||(C[53]=S=>va.value=S),"widget-sidebar-toggle":ha.value,"onUpdate:widgetSidebarToggle":C[54]||(C[54]=S=>ha.value=S),"widget-theme-toggle":ma.value,"onUpdate:widgetThemeToggle":C[55]||(C[55]=S=>ma.value=S)},null,8,["app-preferences-button-position","widget-fullscreen","widget-global-search","widget-language-toggle","widget-lock-screen","widget-notification","widget-refresh","widget-sidebar-toggle","widget-theme-toggle"])]),_:1},8,["title"]),p(e(Ce),{title:e(v)("preferences.footer.title")},{default:c(()=>[p(e(td),{"footer-enable":Pe.value,"onUpdate:footerEnable":C[56]||(C[56]=S=>Pe.value=S),"footer-fixed":Ie.value,"onUpdate:footerFixed":C[57]||(C[57]=S=>Ie.value=S)},null,8,["footer-enable","footer-fixed"])]),_:1},8,["title"]),Tl.value?(u(),k(e(Ce),{key:0,title:e(v)("preferences.copyright.title")},{default:c(()=>[p(e(ed),{"copyright-company-name":ta.value,"onUpdate:copyrightCompanyName":C[58]||(C[58]=S=>ta.value=S),"copyright-company-site-link":aa.value,"onUpdate:copyrightCompanySiteLink":C[59]||(C[59]=S=>aa.value=S),"copyright-date":la.value,"onUpdate:copyrightDate":C[60]||(C[60]=S=>la.value=S),"copyright-enable":ea.value,"onUpdate:copyrightEnable":C[61]||(C[61]=S=>ea.value=S),"copyright-icp":oa.value,"onUpdate:copyrightIcp":C[62]||(C[62]=S=>oa.value=S),"copyright-icp-link":na.value,"onUpdate:copyrightIcpLink":C[63]||(C[63]=S=>na.value=S),disabled:!Pe.value},null,8,["copyright-company-name","copyright-company-site-link","copyright-date","copyright-enable","copyright-icp","copyright-icp-link","disabled"])]),_:1},8,["title"])):U("",!0)]),shortcutKey:c(()=>[p(e(Ce),{title:e(v)("preferences.shortcutKeys.global")},{default:c(()=>[p(e(md),{"shortcut-keys-enable":sa.value,"onUpdate:shortcutKeysEnable":C[64]||(C[64]=S=>sa.value=S),"shortcut-keys-global-search":ra.value,"onUpdate:shortcutKeysGlobalSearch":C[65]||(C[65]=S=>ra.value=S),"shortcut-keys-lock-screen":da.value,"onUpdate:shortcutKeysLockScreen":C[66]||(C[66]=S=>da.value=S),"shortcut-keys-logout":ia.value,"onUpdate:shortcutKeysLogout":C[67]||(C[67]=S=>ia.value=S)},null,8,["shortcut-keys-enable","shortcut-keys-global-search","shortcut-keys-lock-screen","shortcut-keys-logout"])]),_:1},8,["title"])]),_:1},8,["modelValue","tabs"])])]),_:1},8,["description","title"])]))}}),ml=L({__name:"preferences",setup(a){const[l,t]=tl({connectedComponent:Vd}),o=w(()=>{const n={};for(const[r,i]of Object.entries(z))for(const[d,f]of Object.entries(i))n[`${r}${Sa(d)}`]=f;return n}),s=w(()=>{const n={};for(const[r,i]of Object.entries(z))if(typeof i=="object")for(const d of Object.keys(i))n[`update:${r}${Sa(d)}`]=f=>{Ye({[r]:{[d]:f}}),r==="app"&&d==="locale"&&Na(f)};else n[r]=i;return n});return(n,r)=>(u(),x("div",null,[p(e(l),pe(ne(ne({},n.$attrs),o.value),ja(s.value)),null,16),$("div",{onClick:r[0]||(r[0]=()=>e(t).open())},[P(n.$slots,"default",{},()=>[p(e(Ae),{title:e(v)("preferences.title"),class:"bg-primary flex-col-center size-10 cursor-pointer rounded-l-lg rounded-r-none border-none"},{default:c(()=>[p(e(sl),{class:"size-5"})]),_:1},8,["title"])])])]))}}),Ld=L({__name:"preferences-button",emits:["clearPreferencesAndLogout"],setup(a,{emit:l}){const t=l;function o(){t("clearPreferencesAndLogout")}return(s,n)=>(u(),k(ml,{onClearPreferencesAndLogout:o},{default:c(()=>[p(e(tt),null,{default:c(()=>[p(e(sl),{class:"text-foreground size-4"})]),_:1})]),_:1}))}}),Ed={class:"hover:bg-accent ml-1 mr-2 cursor-pointer rounded-full p-1.5"},zd={class:"hover:text-accent-foreground flex-center"},Pd={class:"ml-2 w-full"},Id={key:0,class:"text-foreground mb-1 flex items-center text-sm font-medium"},Ad={class:"text-muted-foreground text-xs font-normal"},tc=L({name:"UserDropdown",__name:"user-dropdown",props:{avatar:{default:""},description:{default:""},enableShortcutKey:{type:Boolean,default:!0},menus:{default:()=>[]},tagText:{default:""},text:{default:""},trigger:{default:"click"},hoverDelay:{default:500}},emits:["logout"],setup(a,{emit:l}){const t=a,o=l,{globalLockScreenShortcutKey:s,globalLogoutShortcutKey:n}=lt(),r=nt(),[i,d]=gt({connectedComponent:Vr}),[f,m]=gt({onConfirm(){K()}}),g=Ta("refTrigger"),b=Ta("refContent"),[h,T]=tr([g,b],()=>t.hoverDelay);de(()=>t.trigger==="hover"||t.trigger==="both",E=>{E?T.enable():T.disable()},{immediate:!0});const H=w(()=>bt()?"Alt":"⌥"),O=w(()=>t.enableShortcutKey&&n.value),M=w(()=>t.enableShortcutKey&&s.value),D=w(()=>t.enableShortcutKey&&z.shortcutKeys.enable);function R(){d.open()}function A(E){d.close(),r.lockScreen(E)}function V(){m.open(),h.value=!1}function K(){o("logout"),m.close()}if(D.value){const E=Ga();Bt(E["Alt+KeyQ"],()=>{O.value&&V()}),Bt(E["Alt+KeyL"],()=>{M.value&&R()})}return(E,B)=>(u(),x(Z,null,[e(z).widget.lockScreen?(u(),k(e(i),{key:0,avatar:E.avatar,text:E.text,onSubmit:A},null,8,["avatar","text"])):U("",!0),p(e(f),{"cancel-text":e(v)("common.cancel"),"confirm-text":e(v)("common.confirm"),"fullscreen-button":!1,title:e(v)("common.prompt"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[I(_(e(v)("ui.widgets.logoutTip")),1)]),_:1},8,["cancel-text","confirm-text","title"]),p(e(Gt),{open:e(h),"onUpdate:open":B[0]||(B[0]=N=>Kt(h)?h.value=N:null)},{default:c(()=>[p(e(jt),{ref_key:"refTrigger",ref:g,disabled:t.trigger==="hover"},{default:c(()=>[$("div",Ed,[$("div",zd,[p(e(vt),{alt:E.text,src:E.avatar,class:"size-8",dot:""},null,8,["alt","src"])])])]),_:1},8,["disabled"]),p(e(qt),{class:"mr-2 min-w-[240px] p-0 pb-1"},{default:c(()=>{var N;return[$("div",{ref_key:"refContent",ref:b},[p(e(rs),{class:"flex items-center p-3"},{default:c(()=>[p(e(vt),{alt:E.text,src:E.avatar,class:"size-12",dot:"","dot-class":"bottom-0 right-1 border-2 size-4 bg-green-500"},null,8,["alt","src"]),$("div",Pd,[E.tagText||E.text||E.$slots.tagText?(u(),x("div",Id,[I(_(E.text)+" ",1),P(E.$slots,"tagText",{},()=>[E.tagText?(u(),k(e(es),{key:0,class:"ml-2 text-green-400"},{default:c(()=>[I(_(E.tagText),1)]),_:1})):U("",!0)])])):U("",!0),$("div",Ad,_(E.description),1)])]),_:3}),(N=E.menus)!=null&&N.length?(u(),k(e(Mt),{key:0})):U("",!0),(u(!0),x(Z,null,oe(E.menus,q=>(u(),k(e(mt),{key:q.text,class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:q.handler},{default:c(()=>[p(e(Ee),{icon:q.icon,class:"mr-2 size-4"},null,8,["icon"]),I(" "+_(q.text),1)]),_:2},1032,["onClick"]))),128)),p(e(Mt)),e(z).widget.lockScreen?(u(),k(e(mt),{key:1,class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:R},{default:c(()=>[p(e(ll),{class:"mr-2 size-4"}),I(" "+_(e(v)("ui.widgets.lockScreen.title"))+" ",1),M.value?(u(),k(e(Va),{key:0},{default:c(()=>[I(_(H.value)+" L ",1)]),_:1})):U("",!0)]),_:1})):U("",!0),e(z).widget.lockScreen?(u(),k(e(Mt),{key:2})):U("",!0),p(e(mt),{class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:V},{default:c(()=>[p(e(jn),{class:"mr-2 size-4"}),I(" "+_(e(v)("common.logout"))+" ",1),O.value?(u(),k(e(Va),{key:0},{default:c(()=>[I(_(H.value)+" Q ",1)]),_:1})):U("",!0)]),_:1})],512)]}),_:3})]),_:3},8,["open"])],64))}}),Ud=L({__name:"layout-content",props:{contentCompact:{},contentCompactWidth:{},padding:{},paddingBottom:{},paddingLeft:{},paddingRight:{},paddingTop:{}},setup(a){const l=a,{contentElement:t,overlayStyle:o}=Po(),s=w(()=>{const{contentCompact:n,padding:r,paddingBottom:i,paddingLeft:d,paddingRight:f,paddingTop:m}=l,g=n==="compact"?{margin:"0 auto",width:`${l.contentCompactWidth}px`}:{};return ke(ne({},g),{flex:1,padding:`${r}px`,paddingBottom:`${i}px`,paddingLeft:`${d}px`,paddingRight:`${f}px`,paddingTop:`${m}px`})});return(n,r)=>(u(),x("main",{ref_key:"contentElement",ref:t,style:se(s.value),class:"bg-background-deep relative"},[p(e(Io),{style:se(e(o))},{default:c(()=>[P(n.$slots,"overlay")]),_:3},8,["style"]),P(n.$slots,"default")],4))}}),Hd=L({__name:"layout-footer",props:{fixed:{type:Boolean},height:{},show:{type:Boolean,default:!0},width:{},zIndex:{}},setup(a){const l=a,t=w(()=>{const{fixed:o,height:s,show:n,width:r,zIndex:i}=l;return{height:`${s}px`,marginBottom:n?"0":`-${s}px`,position:o?"fixed":"static",width:r,zIndex:i}});return(o,s)=>(u(),x("footer",{style:se(t.value),class:"bg-background-deep bottom-0 w-full transition-all duration-200"},[P(o.$slots,"default")],4))}}),Wd=L({__name:"layout-header",props:{fullWidth:{type:Boolean},height:{},isMobile:{type:Boolean},show:{type:Boolean},sidebarWidth:{},theme:{},width:{},zIndex:{}},setup(a){const l=a,t=We(),o=w(()=>{const{fullWidth:n,height:r,show:i}=l,d=!i||!n?void 0:0;return{height:`${r}px`,marginTop:i?0:`-${r}px`,right:d}}),s=w(()=>({minWidth:`${l.isMobile?40:l.sidebarWidth}px`}));return(n,r)=>(u(),x("header",{class:F([n.theme,"border-border bg-header top-0 flex w-full flex-[0_0_auto] items-center border-b pl-2 transition-[margin-top] duration-200"]),style:se(o.value)},[e(t).logo?(u(),x("div",{key:0,style:se(s.value)},[P(n.$slots,"logo")],4)):U("",!0),P(n.$slots,"toggle-button"),P(n.$slots,"default")],6))}}),za=L({__name:"sidebar-collapse-button",props:{collapsed:{type:Boolean},collapsedModifiers:{}},emits:["update:collapsed"],setup(a){const l=y(a,"collapsed");function t(){l.value=!l.value}return(o,s)=>(u(),x("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 left-3 z-10 cursor-pointer rounded-sm p-1",onClick:Se(t,["stop"])},[l.value?(u(),k(e(Ao),{key:0,class:"size-4"})):(u(),k(e(Uo),{key:1,class:"size-4"}))]))}}),Pa=L({__name:"sidebar-fixed-button",props:{expandOnHover:{type:Boolean},expandOnHoverModifiers:{}},emits:["update:expandOnHover"],setup(a){const l=y(a,"expandOnHover");function t(){l.value=!l.value}return(o,s)=>(u(),x("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 right-3 z-10 cursor-pointer rounded-sm p-[5px] transition-all duration-300",onClick:t},[l.value?(u(),k(e(Pt),{key:1,class:"size-3.5"})):(u(),k(e(nl),{key:0,class:"size-3.5"}))]))}}),Od=L({__name:"layout-sidebar",props:be({collapseHeight:{default:42},collapseWidth:{default:48},domVisible:{type:Boolean,default:!0},extraWidth:{},fixedExtra:{type:Boolean,default:!1},headerHeight:{},isSidebarMixed:{type:Boolean,default:!1},marginTop:{default:0},mixedWidth:{default:70},paddingTop:{default:0},show:{type:Boolean,default:!0},showCollapseButton:{type:Boolean,default:!0},showFixedButton:{type:Boolean,default:!0},theme:{},width:{},zIndex:{default:0}},{collapse:{type:Boolean},collapseModifiers:{},extraCollapse:{type:Boolean},extraCollapseModifiers:{},expandOnHovering:{type:Boolean},expandOnHoveringModifiers:{},expandOnHover:{type:Boolean},expandOnHoverModifiers:{},extraVisible:{type:Boolean},extraVisibleModifiers:{}}),emits:be(["leave"],["update:collapse","update:extraCollapse","update:expandOnHovering","update:expandOnHover","update:extraVisible"]),setup(a,{emit:l}){const t=a,o=l,s=y(a,"collapse"),n=y(a,"extraCollapse"),r=y(a,"expandOnHovering"),i=y(a,"expandOnHover"),d=y(a,"extraVisible"),f=mn(document.body),m=We(),g=$t(),b=w(()=>V(!0)),h=w(()=>{const{isSidebarMixed:B,marginTop:N,paddingTop:q,zIndex:te}=t;return ne(ke(ne({"--scroll-shadow":"var(--sidebar)"},V(!1)),{height:`calc(100% - ${N}px)`,marginTop:`${N}px`,paddingTop:`${q}px`,zIndex:te}),B&&d.value?{transition:"none"}:{})}),T=w(()=>{const{extraWidth:B,show:N,width:q,zIndex:te}=t;return{left:`${q}px`,width:d.value&&N?`${B}px`:0,zIndex:te}}),H=w(()=>{const{headerHeight:B}=t;return{height:`${B-1}px`}}),O=w(()=>{const{collapseWidth:B,fixedExtra:N,isSidebarMixed:q,mixedWidth:te}=t;return q&&N?{width:`${s.value?B:te}px`}:{}}),M=w(()=>{const{collapseHeight:B,headerHeight:N}=t;return ne({height:`calc(100% - ${N+B}px)`,paddingTop:"8px"},O.value)}),D=w(()=>{const{headerHeight:B,isSidebarMixed:N}=t;return ne(ke(ne({},N?{display:"flex",justifyContent:"center"}:{}),{height:`${B-1}px`}),O.value)}),R=w(()=>{const{collapseHeight:B,headerHeight:N}=t;return{height:`calc(100% - ${N+B}px)`}}),A=w(()=>({height:`${t.collapseHeight}px`}));qa(()=>{d.value=t.fixedExtra?!0:d.value});function V(B){const{extraWidth:N,fixedExtra:q,isSidebarMixed:te,show:ue,width:ve}=t;let j=ve===0?"0px":`${ve+(te&&q&&d.value?N:0)}px`;const{collapseWidth:J}=t;return B&&r.value&&!i.value&&(j=`${J}px`),ke(ne({},j==="0px"?{overflow:"hidden"}:{}),{flex:`0 0 ${j}`,marginLeft:ue?0:`-${j}`,maxWidth:j,minWidth:j,width:j})}function K(B){(B==null?void 0:B.offsetX)<10||i.value||(r.value||(s.value=!1),t.isSidebarMixed&&(f.value=!0),r.value=!0)}function E(){o("leave"),t.isSidebarMixed&&(f.value=!1),!i.value&&(r.value=!1,s.value=!0,d.value=!1)}return(B,N)=>(u(),x(Z,null,[B.domVisible?(u(),x("div",{key:0,class:F([B.theme,"h-full transition-all duration-150"]),style:se(b.value)},null,6)):U("",!0),$("aside",{class:F([[B.theme,{"bg-sidebar-deep":B.isSidebarMixed,"bg-sidebar border-border border-r":!B.isSidebarMixed}],"fixed left-0 top-0 h-full transition-all duration-150"]),style:se(h.value),onMouseenter:K,onMouseleave:E},[!s.value&&!B.isSidebarMixed&&B.showFixedButton?(u(),k(e(Pa),{key:0,"expand-on-hover":i.value,"onUpdate:expandOnHover":N[0]||(N[0]=q=>i.value=q)},null,8,["expand-on-hover"])):U("",!0),e(m).logo?(u(),x("div",{key:1,style:se(D.value)},[P(B.$slots,"logo")],4)):U("",!0),p(e(yt),{style:se(M.value),shadow:"","shadow-border":""},{default:c(()=>[P(B.$slots,"default")]),_:3},8,["style"]),$("div",{style:se(A.value)},null,4),B.showCollapseButton&&!B.isSidebarMixed?(u(),k(e(za),{key:2,collapsed:s.value,"onUpdate:collapsed":N[1]||(N[1]=q=>s.value=q)},null,8,["collapsed"])):U("",!0),B.isSidebarMixed?(u(),x("div",{key:3,ref_key:"asideRef",ref:g,class:F([{"border-l":d.value},"border-border bg-sidebar fixed top-0 h-full overflow-hidden border-r transition-all duration-200"]),style:se(T.value)},[B.isSidebarMixed&&i.value?(u(),k(e(za),{key:0,collapsed:n.value,"onUpdate:collapsed":N[2]||(N[2]=q=>n.value=q)},null,8,["collapsed"])):U("",!0),n.value?U("",!0):(u(),k(e(Pa),{key:1,"expand-on-hover":i.value,"onUpdate:expandOnHover":N[3]||(N[3]=q=>i.value=q)},null,8,["expand-on-hover"])),n.value?U("",!0):(u(),x("div",{key:2,style:se(H.value),class:"pl-2"},[P(B.$slots,"extra-title")],4)),p(e(yt),{style:se(R.value),class:"border-border py-2",shadow:"","shadow-border":""},{default:c(()=>[P(B.$slots,"extra")]),_:3},8,["style"])],6)):U("",!0)],38)],64))}}),Dd=L({__name:"layout-tabbar",props:{height:{}},setup(a){const l=a,t=w(()=>{const{height:o}=l;return{height:`${o}px`}});return(o,s)=>(u(),x("section",{style:se(t.value),class:"border-border bg-background flex w-full border-b transition-all"},[P(o.$slots,"default")],4))}});function Nd(a){const l=w(()=>a.isMobile?"sidebar-nav":a.layout),t=w(()=>l.value==="full-content"),o=w(()=>l.value==="sidebar-mixed-nav"),s=w(()=>l.value==="header-nav"),n=w(()=>l.value==="mixed-nav"||l.value==="header-sidebar-nav"),r=w(()=>l.value==="header-mixed-nav");return{currentLayout:l,isFullContent:t,isHeaderMixedNav:r,isHeaderNav:s,isMixedNav:n,isSidebarMixedNav:o}}const Rd={class:"relative flex min-h-full w-full"},Fd=L({name:"VbenLayout",__name:"vben-layout",props:be({contentCompact:{default:"wide"},contentCompactWidth:{default:1200},contentPadding:{default:0},contentPaddingBottom:{default:0},contentPaddingLeft:{default:0},contentPaddingRight:{default:0},contentPaddingTop:{default:0},footerEnable:{type:Boolean,default:!1},footerFixed:{type:Boolean,default:!0},footerHeight:{default:32},headerHeight:{default:50},headerHidden:{type:Boolean,default:!1},headerMode:{default:"fixed"},headerTheme:{},headerToggleSidebarButton:{type:Boolean,default:!0},headerVisible:{type:Boolean,default:!0},isMobile:{type:Boolean,default:!1},layout:{default:"sidebar-nav"},sidebarCollapse:{type:Boolean},sidebarCollapsedButton:{type:Boolean,default:!0},sidebarCollapseShowTitle:{type:Boolean,default:!1},sidebarEnable:{type:Boolean},sidebarExtraCollapsedWidth:{default:60},sidebarFixedButton:{type:Boolean,default:!0},sidebarHidden:{type:Boolean,default:!1},sidebarMixedWidth:{default:80},sidebarTheme:{default:"dark"},sidebarWidth:{default:180},sideCollapseWidth:{default:60},tabbarEnable:{type:Boolean,default:!0},tabbarHeight:{default:40},zIndex:{default:200}},{sidebarCollapse:{type:Boolean,default:!1},sidebarCollapseModifiers:{},sidebarExtraVisible:{type:Boolean},sidebarExtraVisibleModifiers:{},sidebarExtraCollapse:{type:Boolean,default:!1},sidebarExtraCollapseModifiers:{},sidebarExpandOnHover:{type:Boolean,default:!1},sidebarExpandOnHoverModifiers:{},sidebarEnable:{type:Boolean,default:!0},sidebarEnableModifiers:{}}),emits:be(["sideMouseLeave","toggleSidebar"],["update:sidebarCollapse","update:sidebarExtraVisible","update:sidebarExtraCollapse","update:sidebarExpandOnHover","update:sidebarEnable"]),setup(a,{emit:l}){const t=a,o=l,s=y(a,"sidebarCollapse"),n=y(a,"sidebarExtraVisible"),r=y(a,"sidebarExtraCollapse"),i=y(a,"sidebarExpandOnHover"),d=y(a,"sidebarEnable"),f=X(!1),m=X(!1),g=X(),{arrivedState:b,directions:h,isScrolling:T,y:H}=hn(document),{setLayoutHeaderHeight:O}=Ho(),{setLayoutFooterHeight:M}=Wo(),{y:D}=bn({target:g,type:"client"}),{currentLayout:R,isFullContent:A,isHeaderMixedNav:V,isHeaderNav:K,isMixedNav:E,isSidebarMixedNav:B}=Nd(t),N=w(()=>t.headerMode==="auto"),q=w(()=>{let W=0;return t.headerVisible&&!t.headerHidden&&(W+=t.headerHeight),t.tabbarEnable&&(W+=t.tabbarHeight),W}),te=w(()=>{const{sidebarCollapseShowTitle:W,sidebarMixedWidth:ee,sideCollapseWidth:ce}=t;return W||B.value||V.value?ee:ce}),ue=w(()=>!K.value&&d.value),ve=w(()=>{const{headerHeight:W,isMobile:ee}=t;return E.value&&!ee?W:0}),j=w(()=>{const{isMobile:W,sidebarHidden:ee,sidebarMixedWidth:ce,sidebarWidth:Pe}=t;let Ie=0;return ee||!ue.value||ee&&!B.value&&!E.value&&!V.value||((V.value||B.value)&&!W?Ie=ce:s.value?Ie=W?0:te.value:Ie=Pe),Ie}),J=w(()=>{const{sidebarExtraCollapsedWidth:W,sidebarWidth:ee}=t;return r.value?W:ee}),ge=w(()=>R.value==="mixed-nav"||R.value==="sidebar-mixed-nav"||R.value==="sidebar-nav"||R.value==="header-mixed-nav"||R.value==="header-sidebar-nav"),xe=w(()=>{const{headerMode:W}=t;return E.value||W==="fixed"||W==="auto-scroll"||W==="auto"}),Ve=w(()=>ge.value&&d.value&&!t.sidebarHidden),Oe=w(()=>!s.value&&t.isMobile),_e=w(()=>{let W="100%",ee="unset";if(xe.value&&R.value!=="header-nav"&&R.value!=="mixed-nav"&&R.value!=="header-sidebar-nav"&&Ve.value&&!t.isMobile)if((B.value||V.value)&&i.value&&n.value){const Pe=s.value?te.value:t.sidebarMixedWidth,Ie=r.value?t.sidebarExtraCollapsedWidth:t.sidebarWidth;ee=`${Pe+Ie}px`,W=`calc(100% - ${ee})`}else ee=f.value&&!i.value?`${te.value}px`:`${j.value}px`,W=`calc(100% - ${ee})`;return{sidebarAndExtraWidth:ee,width:W}}),G=w(()=>{let W="",ee=0;if(!E.value||t.sidebarHidden)W="100%";else if(d.value){const ce=i.value?t.sidebarWidth:te.value;ee=s.value?te.value:ce,W=`calc(100% - ${s.value?j.value:ce}px)`}else W="100%";return{marginLeft:`${ee}px`,width:W}}),ae=w(()=>{const W=xe.value,{footerEnable:ee,footerFixed:ce,footerHeight:Pe}=t;return{marginTop:W&&!A.value&&!m.value&&(!N.value||H.value<q.value)?`${q.value}px`:0,paddingBottom:`${ee&&ce?Pe:0}px`}}),le=w(()=>{const{zIndex:W}=t,ee=E.value?1:0;return W+ee}),ye=w(()=>{const W=xe.value;return{height:A.value?"0":`${q.value}px`,left:E.value?0:_e.value.sidebarAndExtraWidth,position:W?"fixed":"static",top:m.value||A.value?`-${q.value}px`:0,width:_e.value.width,"z-index":le.value}}),$e=w(()=>{const{isMobile:W,zIndex:ee}=t;let ce=W||ge.value?1:-1;return E.value&&(ce+=1),ee+ce}),ze=w(()=>t.footerFixed?_e.value.width:"100%"),ie=w(()=>({zIndex:t.zIndex})),he=w(()=>t.isMobile||t.headerToggleSidebarButton&&ge.value&&!B.value&&!E.value&&!t.isMobile),fe=w(()=>!ge.value||E.value||t.isMobile);de(()=>t.isMobile,W=>{W&&(s.value=!0)},{immediate:!0}),de([()=>q.value,()=>A.value],([W])=>{O(A.value?0:W)},{immediate:!0}),de(()=>t.footerHeight,W=>{M(W)},{immediate:!0});{const W=()=>{D.value>q.value?m.value=!0:m.value=!1};de([()=>t.headerMode,()=>D.value],()=>{if(!N.value||E.value||A.value){t.headerMode!=="auto-scroll"&&(m.value=!1);return}m.value=!0,W()},{immediate:!0})}{const W=Lt((ee,ce,Pe)=>{if(H.value<q.value){m.value=!1;return}if(Pe){m.value=!1;return}ee?m.value=!1:ce&&(m.value=!0)},300);de(()=>H.value,()=>{t.headerMode!=="auto-scroll"||E.value||A.value||T.value&&W(h.top,h.bottom,b.top)})}function Je(){s.value=!0}function et(){t.isMobile?s.value=!1:o("toggleSidebar")}const De=No;return(W,ee)=>(u(),x("div",Rd,[ue.value?(u(),k(e(Od),{key:0,collapse:s.value,"onUpdate:collapse":ee[0]||(ee[0]=ce=>s.value=ce),"expand-on-hover":i.value,"onUpdate:expandOnHover":ee[1]||(ee[1]=ce=>i.value=ce),"expand-on-hovering":f.value,"onUpdate:expandOnHovering":ee[2]||(ee[2]=ce=>f.value=ce),"extra-collapse":r.value,"onUpdate:extraCollapse":ee[3]||(ee[3]=ce=>r.value=ce),"extra-visible":n.value,"onUpdate:extraVisible":ee[4]||(ee[4]=ce=>n.value=ce),"show-collapse-button":W.sidebarCollapsedButton,"show-fixed-button":W.sidebarFixedButton,"collapse-width":te.value,"dom-visible":!W.isMobile,"extra-width":J.value,"fixed-extra":i.value,"header-height":e(E)?0:W.headerHeight,"is-sidebar-mixed":e(B)||e(V),"margin-top":ve.value,"mixed-width":W.sidebarMixedWidth,show:Ve.value,theme:W.sidebarTheme,width:j.value,"z-index":$e.value,onLeave:ee[5]||(ee[5]=()=>o("sideMouseLeave"))},dt({extra:c(()=>[P(W.$slots,"side-extra")]),"extra-title":c(()=>[P(W.$slots,"side-extra-title")]),default:c(()=>[e(B)||e(V)?P(W.$slots,"mixed-menu",{key:0}):P(W.$slots,"menu",{key:1})]),_:2},[ge.value&&!e(E)?{name:"logo",fn:c(()=>[P(W.$slots,"logo")]),key:"0"}:void 0]),1032,["collapse","expand-on-hover","expand-on-hovering","extra-collapse","extra-visible","show-collapse-button","show-fixed-button","collapse-width","dom-visible","extra-width","fixed-extra","header-height","is-sidebar-mixed","margin-top","mixed-width","show","theme","width","z-index"])):U("",!0),$("div",{ref_key:"contentRef",ref:g,class:"flex flex-1 flex-col overflow-hidden transition-all duration-300 ease-in"},[$("div",{class:F([[{"shadow-[0_16px_24px_hsl(var(--background))]":e(H)>20},e(Oo)],"overflow-hidden transition-all duration-200"]),style:se(ye.value)},[W.headerVisible?(u(),k(e(Wd),{key:0,"full-width":!ge.value,height:W.headerHeight,"is-mobile":W.isMobile,show:!e(A)&&!W.headerHidden,"sidebar-width":W.sidebarWidth,theme:W.headerTheme,width:_e.value.width,"z-index":le.value},dt({"toggle-button":c(()=>[he.value?(u(),k(e(tt),{key:0,class:"my-0 mr-1 rounded-md",onClick:et},{default:c(()=>[p(e(Do),{class:"size-4"})]),_:1})):U("",!0)]),default:c(()=>[P(W.$slots,"header")]),_:2},[fe.value?{name:"logo",fn:c(()=>[P(W.$slots,"logo")]),key:"0"}:void 0]),1032,["full-width","height","is-mobile","show","sidebar-width","theme","width","z-index"])):U("",!0),W.tabbarEnable?(u(),k(e(Dd),{key:1,height:W.tabbarHeight,style:se(G.value)},{default:c(()=>[P(W.$slots,"tabbar")]),_:3},8,["height","style"])):U("",!0)],6),p(e(Ud),{id:e(De),"content-compact":W.contentCompact,"content-compact-width":W.contentCompactWidth,padding:W.contentPadding,"padding-bottom":W.contentPaddingBottom,"padding-left":W.contentPaddingLeft,"padding-right":W.contentPaddingRight,"padding-top":W.contentPaddingTop,style:se(ae.value),class:"transition-[margin-top] duration-200"},{overlay:c(()=>[P(W.$slots,"content-overlay")]),default:c(()=>[P(W.$slots,"content")]),_:3},8,["id","content-compact","content-compact-width","padding","padding-bottom","padding-left","padding-right","padding-top","style"]),W.footerEnable?(u(),k(e(Hd),{key:0,fixed:W.footerFixed,height:W.footerHeight,show:!e(A),width:ze.value,"z-index":W.zIndex},{default:c(()=>[P(W.$slots,"footer")]),_:3},8,["fixed","height","show","width","z-index"])):U("",!0)],512),P(W.$slots,"extra"),Oe.value?(u(),x("div",{key:1,style:se(ie.value),class:"bg-overlay fixed left-0 top-0 h-full w-full transition-[background-color] duration-200",onClick:Je},null,4)):U("",!0)]))}});function Kd(){const a=X(!1),l=X(0),t=ot(),o=500,s=w(()=>z.transition.loading),n=()=>{if(!s.value)return;const r=performance.now()-l.value;r<o?setTimeout(()=>{a.value=!1},o-r):a.value=!1};return t.beforeEach(r=>(r.meta.loaded||!s.value||r.meta.iframeSrc||(l.value=performance.now(),a.value=!0),!0)),t.afterEach(r=>(r.meta.loaded||!s.value||r.meta.iframeSrc||n(),!0)),{spinning:a}}const Gd=L({name:"LayoutContentSpinner",__name:"content-spinner",setup(a){const{spinning:l}=Kd();return(t,o)=>(u(),k(e(Ra),{spinning:e(l)},null,8,["spinning"]))}}),jd={key:0,class:"relative size-full"},qd=["src","onLoad"],Yd=L({name:"IFrameRouterView",__name:"iframe-router-view",setup(a){const l=X([]),t=pt(),o=je(),s=w(()=>z.tabbar.enable),n=w(()=>s.value?t.getTabs.filter(b=>{var h;return!!((h=b.meta)!=null&&h.iframeSrc)}):o.meta.iframeSrc?[o]:[]),r=w(()=>new Set(n.value.map(b=>b.name))),i=w(()=>n.value.length>0);function d(b){return b.name===o.name}function f(b){const{meta:h,name:T}=b;return!T||!t.renderRouteView?!1:s.value?!(h!=null&&h.keepAlive)&&r.value.has(T)&&T!==o.name?!1:t.getTabs.some(H=>H.name===T):d(b)}function m(b){l.value[b]=!1}function g(b){const h=l.value[b];return h===void 0?!0:h}return(b,h)=>i.value?(u(!0),x(Z,{key:0},oe(n.value,(T,H)=>(u(),x(Z,{key:T.fullPath},[f(T)?Me((u(),x("div",jd,[p(e(Ra),{spinning:g(H)},null,8,["spinning"]),$("iframe",{src:T.meta.iframeSrc,class:"size-full",onLoad:O=>m(H)},null,40,qd)],512)),[[Be,d(T)]]):U("",!0)],64))),128)):U("",!0)}}),Xd={class:"relative h-full"},Zd=L({name:"LayoutContent",__name:"content",setup(a){const l=pt(),{keepAlive:t}=lt(),{getCachedTabs:o,getExcludeCachedTabs:s,renderRouteView:n}=Da(l),r=w(()=>{const{transition:f}=z;return f.name&&f.enable});function i(f){const{tabbar:m,transition:g}=z,b=g.name;if(!(!b||!g.enable))return!m.enable||!t,b}function d(f,m){var h;if(!f){console.error("Component view not found，please check the route configuration");return}const g=m.name;if(!g)return f;const b=(h=f==null?void 0:f.type)==null?void 0:h.name;return b||b===g||(f.type||(f.type={}),f.type.name=g),f}return(f,m)=>(u(),x("div",Xd,[p(e(Yd)),p(e(Ro),null,{default:c(({Component:g,route:b})=>[r.value?(u(),k(ut,{key:0,name:i(b),appear:"",mode:"out-in"},{default:c(()=>[e(t)?(u(),k(Ma,{key:0,exclude:e(s),include:e(o)},[e(n)?Me((u(),k(Le(d(g,b)),{key:e(Re)(b)})),[[Be,!b.meta.iframeSrc]]):U("",!0)],1032,["exclude","include"])):e(n)?(u(),k(Le(g),{key:e(Re)(b)})):U("",!0)]),_:2},1032,["name"])):(u(),x(Z,{key:1},[e(t)?(u(),k(Ma,{key:0,exclude:e(s),include:e(o)},[e(n)?Me((u(),k(Le(d(g,b)),{key:e(Re)(b)})),[[Be,!b.meta.iframeSrc]]):U("",!0)],1032,["exclude","include"])):e(n)?(u(),k(Le(g),{key:e(Re)(b)})):U("",!0)],64))]),_:1})]))}}),Qd={class:"flex-center text-muted-foreground relative h-full w-full text-xs"},Jd=L({name:"LayoutFooter",__name:"footer",setup(a){return(l,t)=>(u(),x("div",Qd,[P(l.$slots,"default")]))}}),eu={class:"flex-center hidden lg:block"},tu={class:"flex h-full min-w-0 flex-shrink-0 items-center"},Ge=50,au=L({name:"LayoutHeader",__name:"header",props:{theme:{default:"light"}},emits:["clearPreferencesAndLogout"],setup(a,{emit:l}){const t=l,o=nt(),{globalSearchShortcutKey:s,preferencesButtonPosition:n}=lt(),r=We(),{refresh:i}=ul(),d=w(()=>{const g=[{index:Ge+100,name:"user-dropdown"}];return z.widget.globalSearch&&g.push({index:Ge,name:"global-search"}),n.value.header&&g.push({index:Ge+10,name:"preferences"}),z.widget.themeToggle&&g.push({index:Ge+20,name:"theme-toggle"}),z.widget.languageToggle&&g.push({index:Ge+30,name:"language-toggle"}),z.widget.fullscreen&&g.push({index:Ge+40,name:"fullscreen"}),z.widget.notification&&g.push({index:Ge+50,name:"notification"}),Object.keys(r).forEach(b=>{const h=b.split("-");b.startsWith("header-right")&&g.push({index:Number(h[2]),name:b})}),g.sort((b,h)=>b.index-h.index)}),f=w(()=>{const g=[];return z.widget.refresh&&g.push({index:0,name:"refresh"}),Object.keys(r).forEach(b=>{const h=b.split("-");b.startsWith("header-left")&&g.push({index:Number(h[2]),name:b})}),g.sort((b,h)=>b.index-h.index)});function m(){t("clearPreferencesAndLogout")}return(g,b)=>(u(),x(Z,null,[(u(!0),x(Z,null,oe(f.value.filter(h=>h.index<Ge),h=>P(g.$slots,h.name,{key:h.name},()=>[h.name==="refresh"?(u(),k(e(tt),{key:0,class:"my-0 mr-1 rounded-md",onClick:e(i)},{default:c(()=>[p(e(Yt),{class:"size-4"})]),_:1},8,["onClick"])):U("",!0)],!0)),128)),$("div",eu,[P(g.$slots,"breadcrumb",{},void 0,!0)]),(u(!0),x(Z,null,oe(f.value.filter(h=>h.index>Ge),h=>P(g.$slots,h.name,{key:h.name},void 0,!0)),128)),$("div",{class:F([`menu-align-${e(z).header.menuAlign}`,"flex h-full min-w-0 flex-1 items-center"])},[P(g.$slots,"menu",{},void 0,!0)],2),$("div",tu,[(u(!0),x(Z,null,oe(d.value,h=>P(g.$slots,h.name,{key:h.name},()=>[h.name==="global-search"?(u(),k(e(Tr),{key:0,"enable-shortcut-key":e(s),menus:e(o).accessMenus,class:"mr-1 sm:mr-4"},null,8,["enable-shortcut-key","menus"])):h.name==="preferences"?(u(),k(e(Ld),{key:1,class:"mr-1",onClearPreferencesAndLogout:m})):h.name==="theme-toggle"?(u(),k(e(Mn),{key:2,class:"mr-1 mt-[2px]"})):h.name==="language-toggle"?(u(),k(e(_n),{key:3,class:"mr-1"})):h.name==="fullscreen"?(u(),k(e(Gs),{key:4,class:"mr-1"})):U("",!0)],!0)),128))])],64))}}),lu=Te(au,[["__scopeId","data-v-7670467e"]]),ou={class:"relative mr-1 flex size-1.5"},nu=L({__name:"menu-badge-dot",props:{dotClass:{default:""},dotStyle:{default:()=>({})}},setup(a){return(l,t)=>(u(),x("span",ou,[$("span",{class:F([l.dotClass,"absolute inline-flex h-full w-full animate-ping rounded-full opacity-75"]),style:se(l.dotStyle)},null,6),$("span",{class:F([l.dotClass,"relative inline-flex size-1.5 rounded-full"]),style:se(l.dotStyle)},null,6)]))}}),hl=L({__name:"menu-badge",props:{hasChildren:{type:Boolean},badge:{},badgeType:{},badgeVariants:{}},setup(a){const l=a,t={default:"bg-green-500",destructive:"bg-destructive",primary:"bg-primary",success:"bg-green-500",warning:"bg-yellow-500"},o=w(()=>l.badgeType==="dot"),s=w(()=>{const{badgeVariants:r}=l;return r?t[r]||r:t.default}),n=w(()=>s.value&&vn(s.value)?{backgroundColor:s.value}:{});return(r,i)=>o.value||r.badge?(u(),x("span",{key:0,class:F([r.$attrs.class,"absolute"])},[o.value?(u(),k(nu,{key:0,"dot-class":s.value,"dot-style":n.value},null,8,["dot-class","dot-style"])):(u(),x("div",{key:1,class:F([s.value,"text-primary-foreground flex-center rounded-xl px-1.5 py-0.5 text-[10px]"]),style:se(n.value)},_(r.badge),7))],2)):U("",!0)}}),su=["onClick","onMouseenter"],ru=L({name:"NormalMenu",__name:"normal-menu",props:{activePath:{default:""},collapse:{type:Boolean,default:!1},menus:{default:()=>[]},rounded:{type:Boolean},theme:{default:"dark"}},emits:["enter","select"],setup(a,{emit:l}){const t=a,o=l,{b:s,e:n,is:r}=Ze("normal-menu");function i(d){return t.activePath===d.path&&d.activeIcon||d.icon}return(d,f)=>(u(),x("ul",{class:F([[d.theme,e(s)(),e(r)("collapse",d.collapse),e(r)(d.theme,!0),e(r)("rounded",d.rounded)],"relative"])},[(u(!0),x(Z,null,oe(d.menus,m=>(u(),x("li",{key:m.path,class:F([e(n)("item"),e(r)("active",d.activePath===m.path)]),onClick:()=>o("select",m),onMouseenter:()=>o("enter",m)},[p(e(Ee),{class:F(e(n)("icon")),icon:i(m),fallback:""},null,8,["class","icon"]),$("span",{class:F([e(n)("name"),"truncate"])},_(m.name),3)],42,su))),128))],2))}}),iu=Te(ru,[["__scopeId","data-v-3ebda870"]]);function bl(a,l){var o,s;let t=a.parent;for(;t&&!l.includes((s=(o=t==null?void 0:t.type)==null?void 0:o.name)!=null?s:"");)t=t.parent;return t}const _t=a=>{const l=Array.isArray(a)?a:[a],t=[];return l.forEach(o=>{var s;Array.isArray(o)?t.push(..._t(o)):_a(o)&&Array.isArray(o.children)?t.push(..._t(o.children)):(t.push(o),_a(o)&&((s=o.component)!=null&&s.subTree)&&t.push(..._t(o.component.subTree)))}),t};function vl(){const a=Et();if(!a)throw new Error("instance is required");const l=w(()=>{var n;let o=a.parent;const s=[a.props.path];for(;(o==null?void 0:o.type.name)!=="Menu";)o!=null&&o.props.path&&s.unshift(o.props.path),o=(n=o==null?void 0:o.parent)!=null?n:null;return s});return{parentMenu:w(()=>bl(a,["Menu","SubMenu"])),parentPaths:l}}function gl(a){return w(()=>{var t;return{"--menu-level":a?(t=a==null?void 0:a.level)!=null?t:1:0}})}const yl=Symbol("menuContext");function du(a){Ya(yl,a)}function wl(a){const l=Et();Ya(`subMenu:${l==null?void 0:l.uid}`,a)}function Qt(){if(!Et())throw new Error("instance is required");return Xa(yl)}function xl(){const a=Et();if(!a)throw new Error("instance is required");const l=bl(a,["Menu","SubMenu"]);return Xa(`subMenu:${l==null?void 0:l.uid}`)}const uu=L({name:"MenuItem",__name:"menu-item",props:{activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},emits:["click"],setup(a,{emit:l}){const t=a,o=l,s=We(),{b:n,e:r,is:i}=Ze("menu-item"),d=Ze("menu"),f=Qt(),m=xl(),{parentMenu:g,parentPaths:b}=vl(),h=w(()=>t.path===(f==null?void 0:f.activePath)),T=w(()=>h.value&&t.activeIcon||t.icon),H=w(()=>{var A;return((A=g.value)==null?void 0:A.type.name)==="Menu"}),O=w(()=>{var A;return((A=f.props)==null?void 0:A.collapseShowTitle)&&H.value&&f.props.collapse}),M=w(()=>{var A;return f.props.mode==="vertical"&&H.value&&((A=f.props)==null?void 0:A.collapse)&&s.title}),D=xt({active:h,parentPaths:b.value,path:t.path||""});function R(){var A;t.disabled||((A=f==null?void 0:f.handleMenuItemClick)==null||A.call(f,{parentPaths:b.value,path:t.path}),o("click",D))}return qe(()=>{var A,V;(A=m==null?void 0:m.addSubMenu)==null||A.call(m,D),(V=f==null?void 0:f.addMenuItem)==null||V.call(f,D)}),Za(()=>{var A,V;(A=m==null?void 0:m.removeSubMenu)==null||A.call(m,D),(V=f==null?void 0:f.removeMenuItem)==null||V.call(f,D)}),(A,V)=>(u(),x("li",{class:F([e(f).theme,e(n)(),e(i)("active",h.value),e(i)("disabled",A.disabled),e(i)("collapse-show-title",O.value)]),role:"menuitem",onClick:Se(R,["stop"])},[M.value?(u(),k(e(st),{key:0,"content-class":[e(f).theme],side:"right"},{trigger:c(()=>[$("div",{class:F([e(d).be("tooltip","trigger")])},[p(e(Ee),{class:F(e(d).e("icon")),icon:T.value,fallback:""},null,8,["class","icon"]),P(A.$slots,"default"),O.value?(u(),x("span",{key:0,class:F(e(d).e("name"))},[P(A.$slots,"title")],2)):U("",!0)],2)]),default:c(()=>[P(A.$slots,"title")]),_:3},8,["content-class"])):U("",!0),Me($("div",{class:F([e(r)("content")])},[e(f).props.mode!=="horizontal"?(u(),k(e(hl),pe({key:0,class:"right-2"},t),null,16)):U("",!0),p(e(Ee),{class:F(e(d).e("icon")),icon:T.value},null,8,["class","icon"]),P(A.$slots,"default"),P(A.$slots,"title")],2),[[Be,!M.value]])],2))}});function cu(a,l={}){const{enable:t=!0,delay:o=320}=l;function s(){if(!(typeof t=="boolean"?t:t.value))return;const i=document.querySelector("aside li[role=menuitem].is-active");i&&i.scrollIntoView({behavior:"smooth",block:"center",inline:"center"})}const n=Nt(s,o);return de(a,()=>{(typeof t=="boolean"?t:t.value)&&n()}),{scrollToActiveItem:s}}const pu=L({name:"CollapseTransition",__name:"collapse-transition",setup(a){const l=o=>{o.style.maxHeight="",o.style.overflow=o.dataset.oldOverflow,o.style.paddingTop=o.dataset.oldPaddingTop,o.style.paddingBottom=o.dataset.oldPaddingBottom},t={afterEnter(o){o.style.maxHeight="",o.style.overflow=o.dataset.oldOverflow},afterLeave(o){l(o)},beforeEnter(o){o.dataset||(o.dataset={}),o.dataset.oldPaddingTop=o.style.paddingTop,o.dataset.oldMarginTop=o.style.marginTop,o.dataset.oldPaddingBottom=o.style.paddingBottom,o.dataset.oldMarginBottom=o.style.marginBottom,o.style.height&&(o.dataset.elExistsHeight=o.style.height),o.style.maxHeight=0,o.style.paddingTop=0,o.style.marginTop=0,o.style.paddingBottom=0,o.style.marginBottom=0},beforeLeave(o){o.dataset||(o.dataset={}),o.dataset.oldPaddingTop=o.style.paddingTop,o.dataset.oldMarginTop=o.style.marginTop,o.dataset.oldPaddingBottom=o.style.paddingBottom,o.dataset.oldMarginBottom=o.style.marginBottom,o.dataset.oldOverflow=o.style.overflow,o.style.maxHeight=`${o.scrollHeight}px`,o.style.overflow="hidden"},enter(o){requestAnimationFrame(()=>{o.dataset.oldOverflow=o.style.overflow,o.dataset.elExistsHeight?o.style.maxHeight=o.dataset.elExistsHeight:o.scrollHeight===0?o.style.maxHeight=0:o.style.maxHeight=`${o.scrollHeight}px`,o.style.paddingTop=o.dataset.oldPaddingTop,o.style.paddingBottom=o.dataset.oldPaddingBottom,o.style.marginTop=o.dataset.oldMarginTop,o.style.marginBottom=o.dataset.oldMarginBottom,o.style.overflow="hidden"})},enterCancelled(o){l(o)},leave(o){o.scrollHeight!==0&&(o.style.maxHeight=0,o.style.paddingTop=0,o.style.paddingBottom=0,o.style.marginTop=0,o.style.marginBottom=0)},leaveCancelled(o){l(o)}};return(o,s)=>(u(),k(ut,pe({name:"collapse-transition"},ja(t)),{default:c(()=>[P(o.$slots,"default")]),_:3},16))}}),Ia=L({name:"SubMenuContent",__name:"sub-menu-content",props:{isMenuMore:{type:Boolean,default:!1},isTopLevelMenuSubmenu:{type:Boolean},level:{default:0},activeIcon:{},disabled:{type:Boolean},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(a){const l=a,t=Qt(),{b:o,e:s,is:n}=Ze("sub-menu-content"),r=Ze("menu"),i=w(()=>t==null?void 0:t.openedMenus.includes(l.path)),d=w(()=>t.props.collapse),f=w(()=>l.level===1),m=w(()=>t.props.collapseShowTitle&&f.value&&d.value),g=w(()=>t==null?void 0:t.props.mode),b=w(()=>g.value==="horizontal"||!(f.value&&d.value)),h=w(()=>g.value==="vertical"&&f.value&&d.value&&!m.value),T=w(()=>g.value==="horizontal"&&!f.value||g.value==="vertical"&&d.value?Rt:Ft),H=w(()=>i.value?{transform:"rotate(180deg)"}:{});return(O,M)=>(u(),x("div",{class:F([e(o)(),e(n)("collapse-show-title",m.value),e(n)("more",O.isMenuMore)])},[P(O.$slots,"default"),O.isMenuMore?U("",!0):(u(),k(e(Ee),{key:0,class:F(e(r).e("icon")),icon:O.icon,fallback:""},null,8,["class","icon"])),h.value?U("",!0):(u(),x("div",{key:1,class:F([e(s)("title")])},[P(O.$slots,"title")],2)),O.isMenuMore?U("",!0):Me((u(),k(Le(T.value),{key:2,class:F([[e(s)("icon-arrow")],"size-4"]),style:se(H.value)},null,8,["class","style"])),[[Be,b.value]])],2))}}),kl=L({name:"SubMenu",__name:"sub-menu",props:{isSubMenuMore:{type:Boolean,default:!1},activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(a){var ve;const l=a,{parentMenu:t,parentPaths:o}=vl(),{b:s,is:n}=Ze("sub-menu"),r=Ze("menu"),i=Qt(),d=xl(),f=gl(d),m=X(!1),g=X({}),b=X({}),h=X(null);wl({addSubMenu:K,handleMouseleave:q,level:((ve=d==null?void 0:d.level)!=null?ve:0)+1,mouseInChild:m,removeSubMenu:E});const T=w(()=>i==null?void 0:i.openedMenus.includes(l.path)),H=w(()=>{var j;return((j=t.value)==null?void 0:j.type.name)==="Menu"}),O=w(()=>{var j;return(j=i==null?void 0:i.props.mode)!=null?j:"vertical"}),M=w(()=>i==null?void 0:i.props.rounded),D=w(()=>{var j;return(j=d==null?void 0:d.level)!=null?j:0}),R=w(()=>D.value===1),A=w(()=>{const j=O.value==="horizontal",J=j&&R.value?"bottom":"right";return{collisionPadding:{top:20},side:J,sideOffset:j?5:10}}),V=w(()=>{let j=!1;return Object.values(g.value).forEach(J=>{J.active&&(j=!0)}),Object.values(b.value).forEach(J=>{J.active&&(j=!0)}),j});function K(j){b.value[j.path]=j}function E(j){Reflect.deleteProperty(b.value,j.path)}function B(){const j=i==null?void 0:i.props.mode;l.disabled||i!=null&&i.props.collapse&&j==="vertical"||j==="horizontal"||i==null||i.handleSubMenuClick({active:V.value,parentPaths:o.value,path:l.path})}function N(j,J=300){var ge,xe;if(j.type!=="focus"){if(!(i!=null&&i.props.collapse)&&(i==null?void 0:i.props.mode)==="vertical"||l.disabled){d&&(d.mouseInChild.value=!0);return}d&&(d.mouseInChild.value=!0),h.value&&window.clearTimeout(h.value),h.value=setTimeout(()=>{i==null||i.openMenu(l.path,o.value)},J),(xe=(ge=t.value)==null?void 0:ge.vnode.el)==null||xe.dispatchEvent(new MouseEvent("mouseenter"))}}function q(j=!1){var J;if(!(i!=null&&i.props.collapse)&&(i==null?void 0:i.props.mode)==="vertical"&&d){d.mouseInChild.value=!1;return}h.value&&window.clearTimeout(h.value),d&&(d.mouseInChild.value=!1),h.value=setTimeout(()=>{!m.value&&(i==null||i.closeMenu(l.path,o.value))},300),j&&((J=d==null?void 0:d.handleMouseleave)==null||J.call(d,!0))}const te=w(()=>V.value&&l.activeIcon||l.icon),ue=xt({active:V,parentPaths:o,path:l.path});return qe(()=>{var j,J;(j=d==null?void 0:d.addSubMenu)==null||j.call(d,ue),(J=i==null?void 0:i.addSubMenu)==null||J.call(i,ue)}),Za(()=>{var j,J;(j=d==null?void 0:d.removeSubMenu)==null||j.call(d,ue),(J=i==null?void 0:i.removeSubMenu)==null||J.call(i,ue)}),(j,J)=>(u(),x("li",{class:F([e(s)(),e(n)("opened",T.value),e(n)("active",V.value),e(n)("disabled",j.disabled)]),onFocus:N,onMouseenter:N,onMouseleave:J[3]||(J[3]=()=>q())},[e(i).isMenuPopup?(u(),k(e(qs),{key:0,"content-class":[e(i).theme,e(r).e("popup-container"),e(n)(e(i).theme,!0),T.value?"":"hidden","overflow-auto","max-h-[calc(var(--radix-hover-card-content-available-height)-20px)]"],"content-props":A.value,open:!0,"open-delay":0},{trigger:c(()=>[p(Ia,{class:F(e(n)("active",V.value)),icon:te.value,"is-menu-more":j.isSubMenuMore,"is-top-level-menu-submenu":H.value,level:D.value,path:j.path,onClick:Se(B,["stop"])},{title:c(()=>[P(j.$slots,"title")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"])]),default:c(()=>[$("div",{class:F([e(r).is(O.value,!0),e(r).e("popup")]),onFocus:J[0]||(J[0]=ge=>N(ge,100)),onMouseenter:J[1]||(J[1]=ge=>N(ge,100)),onMouseleave:J[2]||(J[2]=()=>q(!0))},[$("ul",{class:F([e(r).b(),e(n)("rounded",M.value)]),style:se(e(f))},[P(j.$slots,"default")],6)],34)]),_:3},8,["content-class","content-props"])):(u(),x(Z,{key:1},[p(Ia,{class:F(e(n)("active",V.value)),icon:te.value,"is-menu-more":j.isSubMenuMore,"is-top-level-menu-submenu":H.value,level:D.value,path:j.path,onClick:Se(B,["stop"])},{title:c(()=>[P(j.$slots,"title")]),default:c(()=>[P(j.$slots,"content")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"]),p(pu,null,{default:c(()=>[Me($("ul",{class:F([e(r).b(),e(n)("rounded",M.value)]),style:se(e(f))},[P(j.$slots,"default")],6),[[Be,T.value]])]),_:3})],64))],34))}}),fu=L({name:"Menu",__name:"menu",props:{accordion:{type:Boolean,default:!0},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{default:"vertical"},rounded:{type:Boolean,default:!0},scrollToActive:{type:Boolean,default:!1},theme:{default:"dark"}},emits:["close","open","select"],setup(a,{emit:l}){const t=a,o=l,{b:s,is:n}=Ze("menu"),r=gl(),i=We(),d=X(),f=X(-1),m=X(t.defaultOpeneds&&!t.collapse?[...t.defaultOpeneds]:[]),g=X(t.defaultActive),b=X({}),h=X({}),T=X(!1),H=w(()=>t.mode==="horizontal"||t.mode==="vertical"&&t.collapse),O=w(()=>{var $e,ze;const G=(ze=($e=i.default)==null?void 0:$e.call(i))!=null?ze:[],ae=_t(G),le=f.value===-1?ae:ae.slice(0,f.value),ye=f.value===-1?[]:ae.slice(f.value);return{showSlotMore:ye.length>0,slotDefault:le,slotMore:ye}});de(()=>t.collapse,G=>{G&&(m.value=[])}),de(b.value,N),de(()=>t.defaultActive,(G="")=>{b.value[G]||(g.value=""),q(G)});let M;qa(()=>{t.mode==="horizontal"?M=yn(d,K).stop:M==null||M()}),du(xt({activePath:g,addMenuItem:ge,addSubMenu:xe,closeMenu:j,handleMenuItemClick:te,handleSubMenuClick:ue,isMenuPopup:H,openedMenus:m,openMenu:J,props:t,removeMenuItem:Oe,removeSubMenu:Ve,subMenus:h,theme:gn(t,"theme"),items:b})),wl({addSubMenu:xe,level:1,mouseInChild:T,removeSubMenu:Ve});function D(G){const ae=getComputedStyle(G),le=Number.parseInt(ae.marginLeft,10),ye=Number.parseInt(ae.marginRight,10);return G.offsetWidth+le+ye||0}function R(){var fe,Je,et;if(!d.value)return-1;const G=[...(Je=(fe=d.value)==null?void 0:fe.childNodes)!=null?Je:[]].filter(De=>De.nodeName!=="#comment"&&(De.nodeName!=="#text"||De.nodeValue)),ae=46,le=getComputedStyle(d==null?void 0:d.value),ye=Number.parseInt(le.paddingLeft,10),$e=Number.parseInt(le.paddingRight,10),ze=((et=d.value)==null?void 0:et.clientWidth)-ye-$e;let ie=0,he=0;return G.forEach((De,W)=>{ie+=D(De),ie<=ze-ae&&(he=W+1)}),he===G.length?-1:he}function A(G,ae=33.34){let le;return()=>{le&&clearTimeout(le),le=setTimeout(()=>{G()},ae)}}let V=!0;function K(){if(f.value===R())return;const G=()=>{f.value=-1,Xe(()=>{f.value=R()})};G(),V?G():A(G)(),V=!1}const E=w(()=>t.scrollToActive&&t.mode==="vertical"&&!t.collapse),{scrollToActiveItem:B}=cu(g,{enable:E,delay:320});de(g,()=>{B()});function N(){_e().forEach(ae=>{const le=h.value[ae];le&&J(ae,le.parentPaths)})}function q(G){const ae=b.value,le=ae[G]||g.value&&ae[g.value]||ae[t.defaultActive||""];g.value=le?le.path:G}function te(G){const{collapse:ae,mode:le}=t;(le==="horizontal"||ae)&&(m.value=[]);const{parentPaths:ye,path:$e}=G;!$e||!ye||o("select",$e,ye)}function ue({parentPaths:G,path:ae}){m.value.includes(ae)?j(ae,G):J(ae,G)}function ve(G){const ae=m.value.indexOf(G);ae!==-1&&m.value.splice(ae,1)}function j(G,ae){var le,ye;t.accordion&&(m.value=(ye=(le=h.value[G])==null?void 0:le.parentPaths)!=null?ye:[]),ve(G),o("close",G,ae)}function J(G,ae){if(!m.value.includes(G)){if(t.accordion){const le=_e();le.includes(G)&&(ae=le),m.value=m.value.filter(ye=>ae.includes(ye))}m.value.push(G),o("open",G,ae)}}function ge(G){b.value[G.path]=G}function xe(G){h.value[G.path]=G}function Ve(G){Reflect.deleteProperty(h.value,G.path)}function Oe(G){Reflect.deleteProperty(b.value,G.path)}function _e(){const G=g.value&&b.value[g.value];return!G||t.mode==="horizontal"||t.collapse?[]:G.parentPaths}return(G,ae)=>(u(),x("ul",{ref_key:"menu",ref:d,class:F([G.theme,e(s)(),e(n)(G.mode,!0),e(n)(G.theme,!0),e(n)("rounded",G.rounded),e(n)("collapse",G.collapse),e(n)("menu-align",G.mode==="horizontal")]),style:se(e(r)),role:"menu"},[G.mode==="horizontal"&&O.value.showSlotMore?(u(),x(Z,{key:0},[(u(!0),x(Z,null,oe(O.value.slotDefault,le=>(u(),k(Le(le),{key:le.key}))),128)),p(kl,{"is-sub-menu-more":"",path:"sub-menu-more"},{title:c(()=>[p(e(Fo),{class:"size-4"})]),default:c(()=>[(u(!0),x(Z,null,oe(O.value.slotMore,le=>(u(),k(Le(le),{key:le.key}))),128))]),_:1})],64)):P(G.$slots,"default",{key:1})],6))}}),Cl=L({name:"SubMenuUi",__name:"sub-menu",props:{menu:{}},setup(a){const l=a,t=w(()=>{const{menu:o}=l;return Reflect.has(o,"children")&&!!o.children&&o.children.length>0});return(o,s)=>t.value?(u(),k(e(kl),{key:`${o.menu.path}_sub`,"active-icon":o.menu.activeIcon,icon:o.menu.icon,path:o.menu.path},{content:c(()=>[p(e(hl),{badge:o.menu.badge,"badge-type":o.menu.badgeType,"badge-variants":o.menu.badgeVariants,class:"right-6"},null,8,["badge","badge-type","badge-variants"])]),title:c(()=>[$("span",null,_(o.menu.name),1)]),default:c(()=>[(u(!0),x(Z,null,oe(o.menu.children||[],n=>(u(),k(Cl,{key:n.path,menu:n},null,8,["menu"]))),128))]),_:1},8,["active-icon","icon","path"])):(u(),k(e(uu),{key:o.menu.path,"active-icon":o.menu.activeIcon,badge:o.menu.badge,"badge-type":o.menu.badgeType,"badge-variants":o.menu.badgeVariants,icon:o.menu.icon,path:o.menu.path},{title:c(()=>[$("span",null,_(o.menu.name),1)]),_:1},8,["active-icon","badge","badge-type","badge-variants","icon","path"]))}}),Sl=L({name:"MenuView",__name:"menu",props:{menus:{},accordion:{type:Boolean},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},setup(a){const t=at(a);return(o,s)=>(u(),k(e(fu),Ue(Qe(e(t))),{default:c(()=>[(u(!0),x(Z,null,oe(o.menus,n=>(u(),k(Cl,{key:n.path,menu:n},null,8,["menu"]))),128))]),_:1},16))}});function Jt(){const a=ot(),l=new Map,t=()=>{a.getRoutes().forEach(i=>{l.set(i.path,i)})};t(),a.afterEach(()=>{t()});const o=r=>{var d,f;if(Dt(r))return!0;const i=l.get(r);return(f=(d=i==null?void 0:i.meta)==null?void 0:d.openInNewWindow)!=null?f:!1};return{navigation:r=>Y(null,null,function*(){var i;try{const d=l.get(r),{openInNewWindow:f=!1,query:m={}}=(i=d==null?void 0:d.meta)!=null?i:{};Dt(r)?wn(r,{target:"_blank"}):f?Fa(r):yield a.push({path:r,query:m})}catch(d){throw console.error("Navigation failed:",d),d}}),willOpenedByWindow:r=>o(r)}}const mu=L({__name:"extra-menu",props:{collapse:{type:Boolean},menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},setup(a){const l=je(),{navigation:t}=Jt();function o(s){return Y(this,null,function*(){yield t(s)})}return(s,n)=>{var r;return u(),k(e(Sl),{accordion:s.accordion,collapse:s.collapse,"default-active":((r=e(l).meta)==null?void 0:r.activePath)||e(l).path,menus:s.menus,rounded:s.rounded,theme:s.theme,mode:"vertical",onSelect:o},null,8,["accordion","collapse","default-active","menus","rounded","theme"])}}}),Aa=L({__name:"menu",props:{menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapse:{type:Boolean},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},emits:["open","select"],setup(a,{emit:l}){const t=a,o=l;function s(r){o("select",r,t.mode)}function n(r,i){o("open",r,i)}return(r,i)=>(u(),k(e(Sl),{accordion:r.accordion,collapse:r.collapse,"collapse-show-title":r.collapseShowTitle,"default-active":r.defaultActive,menus:r.menus,mode:r.mode,rounded:r.rounded,"scroll-to-active":"",theme:r.theme,onOpen:n,onSelect:s},null,8,["accordion","collapse","collapse-show-title","default-active","menus","mode","rounded","theme"]))}}),hu=L({__name:"mixed-menu",props:{activePath:{},collapse:{type:Boolean},menus:{},rounded:{type:Boolean},theme:{}},emits:["defaultSelect","enter","select"],setup(a,{emit:l}){const t=a,o=l,s=je();return Qa(()=>{const n=Xt(t.menus||[],s.path);if(n){const r=(t.menus||[]).find(i=>{var d;return i.path===((d=n.parents)==null?void 0:d[0])});o("defaultSelect",n,r)}}),(n,r)=>(u(),k(e(iu),{"active-path":n.activePath,collapse:n.collapse,menus:n.menus,rounded:n.rounded,theme:n.theme,onEnter:r[0]||(r[0]=i=>o("enter",i)),onSelect:r[1]||(r[1]=i=>o("select",i))},null,8,["active-path","collapse","menus","rounded","theme"]))}});function bu(a){const l=nt(),{navigation:t,willOpenedByWindow:o}=Jt(),s=w(()=>{var M;return(M=a==null?void 0:a.value)!=null?M:l.accessMenus}),n=new Map,r=X([]),i=je(),d=X([]),f=X(!1),m=X(""),g=w(()=>z.app.layout==="header-mixed-nav"?1:0),b=M=>Y(null,null,function*(){var A,V,K;const D=(A=M==null?void 0:M.children)!=null?A:[],R=D.length>0;o(M.path)||(d.value=D!=null?D:[],m.value=(K=(V=M.parents)==null?void 0:V[g.value])!=null?K:M.path,f.value=R),R?z.sidebar.autoActivateChild&&(yield t(n.has(M.path)?n.get(M.path):M.path)):yield t(M.path)}),h=(M,D)=>Y(null,null,function*(){var R,A,V,K;d.value=(A=(R=D==null?void 0:D.children)!=null?R:r.value)!=null?A:[],m.value=(K=(V=M.parents)==null?void 0:V[g.value])!=null?K:M.path,z.sidebar.expandOnHover&&(f.value=d.value.length>0)}),T=()=>{var A,V;if(z.sidebar.expandOnHover)return;const{findMenu:M,rootMenu:D,rootMenuPath:R}=ht(s.value,i.path);m.value=(A=R!=null?R:M==null?void 0:M.path)!=null?A:"",d.value=(V=D==null?void 0:D.children)!=null?V:[]},H=M=>{var D,R,A;if(!z.sidebar.expandOnHover){const{findMenu:V}=ht(s.value,M.path);d.value=(D=V==null?void 0:V.children)!=null?D:[],m.value=(A=(R=M.parents)==null?void 0:R[g.value])!=null?A:M.path,f.value=d.value.length>0}};function O(M){var K,E,B,N;const D=((K=i.meta)==null?void 0:K.activePath)||M,{findMenu:R,rootMenu:A,rootMenuPath:V}=ht(s.value,D,g.value);r.value=(E=A==null?void 0:A.children)!=null?E:[],V&&n.set(V,D),m.value=(B=V!=null?V:R==null?void 0:R.path)!=null?B:"",d.value=(N=A==null?void 0:A.children)!=null?N:[],z.sidebar.expandOnHover&&(f.value=d.value.length>0)}return de(()=>[i.path,z.app.layout],([M])=>{O(M||"")},{immediate:!0}),{extraActiveMenu:m,extraMenus:d,handleDefaultSelect:h,handleMenuMouseEnter:H,handleMixedMenuSelect:b,handleSideMouseLeave:T,sidebarExtraVisible:f}}function vu(){const{navigation:a,willOpenedByWindow:l}=Jt(),t=nt(),o=je(),s=X([]),n=X(""),r=X(""),i=X([]),d=new Map,{isMixedNav:f,isHeaderMixedNav:m}=lt(),g=w(()=>z.navigation.split&&f.value||m.value),b=w(()=>{const K=z.sidebar.enable;return g.value?K&&s.value.length>0:K}),h=w(()=>t.accessMenus),T=w(()=>g.value?h.value.map(K=>ke(ne({},K),{children:[]})):h.value),H=w(()=>g.value?s.value:h.value),O=w(()=>m.value?H.value:T.value),M=w(()=>{var K,E;return(E=(K=o==null?void 0:o.meta)==null?void 0:K.activePath)!=null?E:o.path}),D=w(()=>{var K,E;return g.value?n.value:(E=(K=o.meta)==null?void 0:K.activePath)!=null?E:o.path}),R=(K,E)=>{var q,te;if(!g.value||E==="vertical"){a(K);return}const B=h.value.find(ue=>ue.path===K),N=(q=B==null?void 0:B.children)!=null?q:[];l(K)||(n.value=(te=B==null?void 0:B.path)!=null?te:"",s.value=N),N.length===0?a(K):B&&z.sidebar.autoActivateChild&&a(d.has(B.path)?d.get(B.path):B.path)},A=(K,E)=>{E.length<=1&&z.sidebar.autoActivateChild&&a(d.has(K)?d.get(K):K)};function V(K=o.path){var N,q,te,ue,ve;let{rootMenu:E}=ht(h.value,K);E||(E=h.value.find(j=>j.path===K));const B=ht((E==null?void 0:E.children)||[],K,1);r.value=(N=B.rootMenuPath)!=null?N:"",i.value=(te=(q=B.rootMenu)==null?void 0:q.children)!=null?te:[],n.value=(ue=E==null?void 0:E.path)!=null?ue:"",s.value=(ve=E==null?void 0:E.children)!=null?ve:[]}return de(()=>o.path,K=>{var B,N,q,te;const E=(te=(q=(B=o==null?void 0:o.meta)==null?void 0:B.activePath)!=null?q:(N=o==null?void 0:o.meta)==null?void 0:N.link)!=null?te:K;l(E)||(V(E),n.value&&d.set(n.value,E))},{immediate:!0}),Qa(()=>{var K;V(((K=o.meta)==null?void 0:K.activePath)||o.path)}),{handleMenuSelect:R,handleMenuOpen:A,headerActive:D,headerMenus:T,sidebarActive:M,sidebarMenus:H,mixHeaderMenus:O,mixExtraMenus:i,sidebarVisible:b}}const gu={class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold"},yu=L({__name:"tool-more",props:{menus:{}},setup(a){return(l,t)=>(u(),k(e(Ks),{menus:l.menus,modal:!1},{default:c(()=>[$("div",gu,[p(e(Ft),{class:"size-4"})])]),_:1},8,["menus"]))}}),wu=L({__name:"tool-screen",props:{screen:{type:Boolean},screenModifiers:{}},emits:["update:screen"],setup(a){const l=y(a,"screen");function t(){l.value=!l.value}return(o,s)=>(u(),x("div",{class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold",onClick:t},[l.value?(u(),k(e(ol),{key:0,class:"size-4"})):(u(),k(e(al),{key:1,class:"size-4"}))]))}}),xu=["data-active-tab","data-index","onClick","onMousedown"],ku={class:"relative size-full px-1"},Cu={key:0,class:"tabs-chrome__divider bg-border absolute left-[var(--gap)] top-1/2 z-0 h-4 w-[1px] translate-y-[-50%] transition-all"},Su={class:"tabs-chrome__extra absolute right-[var(--gap)] top-1/2 z-[3] size-4 translate-y-[-50%]"},Tu={class:"tabs-chrome__item-main group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground text-accent-foreground z-[2] mx-[calc(var(--gap)*2)] my-0 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pl-2 pr-4 duration-150"},Mu={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},_u=L({name:"VbenTabsChrome",inheritAttrs:!1,__name:"tabs",props:be({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{default:7},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]},wheelable:{type:Boolean}},{active:{},activeModifiers:{}}),emits:be(["close","unpin"],["update:active"]),setup(a,{emit:l}){const t=a,o=l,s=y(a,"active"),n=X(),r=X(),i=w(()=>{const{gap:m}=t;return{"--gap":`${m}px`}}),d=w(()=>t.tabs.map(m=>{const{fullPath:g,meta:b,name:h,path:T,key:H}=m||{},{affixTab:O,icon:M,newTabTitle:D,tabClosable:R,title:A}=b||{};return{affixTab:!!O,closable:Reflect.has(b,"tabClosable")?!!R:!0,fullPath:g,icon:M,key:H,meta:b,name:h,path:T,title:D||A||h}}));function f(m,g){m.button===1&&g.closable&&!g.affixTab&&d.value.length>1&&t.middleClickToClose&&(m.preventDefault(),m.stopPropagation(),o("close",g.key))}return(m,g)=>(u(),x("div",{ref_key:"contentRef",ref:n,class:F([m.contentClass,"tabs-chrome !flex h-full w-max overflow-y-hidden pr-6"]),style:se(i.value)},[p(Vt,{name:"slide-left"},{default:c(()=>[(u(!0),x(Z,null,oe(d.value,(b,h)=>(u(),x("div",{key:b.key,ref_for:!0,ref_key:"tabRef",ref:r,class:F([[{"is-active":b.key===s.value,draggable:!b.affixTab,"affix-tab":b.affixTab}],"tabs-chrome__item draggable translate-all group relative -mr-3 flex h-full select-none items-center"]),"data-active-tab":s.value,"data-index":h,"data-tab-item":"true",onClick:T=>s.value=b.key,onMousedown:T=>f(T,b)},[p(e(il),{"handler-data":b,menus:m.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[$("div",ku,[h!==0&&b.key!==s.value?(u(),x("div",Cu)):U("",!0),g[0]||(g[0]=$("div",{class:"tabs-chrome__background absolute z-[-1] size-full px-[calc(var(--gap)-1px)] py-0 transition-opacity duration-150"},[$("div",{class:"tabs-chrome__background-content group-[.is-active]:bg-primary/15 dark:group-[.is-active]:bg-accent h-full rounded-tl-[var(--gap)] rounded-tr-[var(--gap)] duration-150"}),$("svg",{class:"tabs-chrome__background-before group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 left-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[$("path",{d:"M 0 7 A 7 7 0 0 0 7 0 L 7 7 Z"})]),$("svg",{class:"tabs-chrome__background-after group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 right-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[$("path",{d:"M 0 0 A 7 7 0 0 0 7 7 L 0 7 Z"})])],-1)),$("div",Su,[Me(p(e(zt),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[2px] size-3 cursor-pointer rounded-full transition-all",onClick:Se(()=>o("close",b.key),["stop"])},null,8,["onClick"]),[[Be,!b.affixTab&&d.value.length>1&&b.closable]]),Me(p(e(Pt),{class:"hover:text-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:Se(()=>o("unpin",b),["stop"])},null,8,["onClick"]),[[Be,b.affixTab&&d.value.length>1&&b.closable]])]),$("div",Tu,[m.showIcon?(u(),k(e(Ee),{key:0,icon:b.icon,class:"mr-1 flex size-4 items-center overflow-hidden"},null,8,["icon"])):U("",!0),$("span",Mu,_(b.title),1)])])]),_:2},1032,["handler-data","menus"])],42,xu))),128))]),_:1})],6))}}),$u=Te(_u,[["__scopeId","data-v-08fc8c7f"]]),Bu=["data-index","onClick","onMousedown"],Vu={class:"relative flex size-full items-center"},Lu={class:"absolute right-1.5 top-1/2 z-[3] translate-y-[-50%] overflow-hidden"},Eu={class:"text-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mx-3 mr-4 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pr-3 transition-all duration-300"},zu={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},Pu=L({name:"VbenTabs",inheritAttrs:!1,__name:"tabs",props:be({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]},wheelable:{type:Boolean}},{active:{},activeModifiers:{}}),emits:be(["close","unpin"],["update:active"]),setup(a,{emit:l}){const t=a,o=l,s=y(a,"active"),n=w(()=>({brisk:{content:"h-full after:content-['']  after:absolute after:bottom-0 after:left-0 after:w-full after:h-[1.5px] after:bg-primary after:scale-x-0 after:transition-[transform] after:ease-out after:duration-300 hover:after:scale-x-100 after:origin-left [&.is-active]:after:scale-x-100 [&:not(:first-child)]:border-l last:border-r last:border-r border-border"},card:{content:"h-[calc(100%-6px)] rounded-md ml-2 border border-border  transition-all"},plain:{content:"h-full [&:not(:first-child)]:border-l last:border-r border-border"}})[t.styleType||"plain"]||{content:""}),r=w(()=>t.tabs.map(d=>{const{fullPath:f,meta:m,name:g,path:b,key:h}=d||{},{affixTab:T,icon:H,newTabTitle:O,tabClosable:M,title:D}=m||{};return{affixTab:!!T,closable:Reflect.has(m,"tabClosable")?!!M:!0,fullPath:f,icon:H,key:h,meta:m,name:g,path:b,title:O||D||g}}));function i(d,f){d.button===1&&f.closable&&!f.affixTab&&r.value.length>1&&t.middleClickToClose&&(d.preventDefault(),d.stopPropagation(),o("close",f.key))}return(d,f)=>(u(),x("div",{class:F([d.contentClass,"relative !flex h-full w-max items-center overflow-hidden pr-6"])},[p(Vt,{name:"slide-left"},{default:c(()=>[(u(!0),x(Z,null,oe(r.value,(m,g)=>(u(),x("div",{key:m.key,class:F([[{"is-active dark:bg-accent bg-primary/15":m.key===s.value,draggable:!m.affixTab,"affix-tab":m.affixTab},n.value.content],"tab-item [&:not(.is-active)]:hover:bg-accent translate-all group relative flex cursor-pointer select-none"]),"data-index":g,"data-tab-item":"true",onClick:b=>s.value=m.key,onMousedown:b=>i(b,m)},[p(e(il),{"handler-data":m,menus:d.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[$("div",Vu,[$("div",Lu,[Me(p(e(zt),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground dark:group-[.is-active]:text-accent-foreground group-[.is-active]:text-primary size-3 cursor-pointer rounded-full transition-all",onClick:Se(()=>o("close",m.key),["stop"])},null,8,["onClick"]),[[Be,!m.affixTab&&r.value.length>1&&m.closable]]),Me(p(e(Pt),{class:"hover:bg-accent hover:stroke-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:Se(()=>o("unpin",m),["stop"])},null,8,["onClick"]),[[Be,m.affixTab&&r.value.length>1&&m.closable]])]),$("div",Eu,[d.showIcon?(u(),k(e(Ee),{key:0,icon:m.icon,class:"mr-2 flex size-4 items-center overflow-hidden",fallback:""},null,8,["icon"])):U("",!0),$("span",zu,_(m.title),1)])])]),_:2},1032,["handler-data","menus"])],42,Bu))),128))]),_:1})],2))}});function Ot(a){const l="group";return a.classList.contains(l)?a:a.closest(`.${l}`)}function Iu(a,l){const t=X(null);function o(){return Y(this,null,function*(){var d;yield Xe();const n=(d=document.querySelectorAll(`.${a.contentClass}`))==null?void 0:d[0];if(!n){console.warn("Element not found for sortable initialization");return}const r=()=>Y(null,null,function*(){var f;n.style.cursor="default",(f=n.querySelector(".draggable"))==null||f.classList.remove("dragging")}),{initializeSortable:i}=Go(n,{filter:(f,m)=>{const g=Ot(m);return!(g==null?void 0:g.classList.contains("draggable"))||!a.draggable},onEnd(f){const{newIndex:m,oldIndex:g}=f,{srcElement:b}=f.originalEvent;if(!b){r();return}const h=Ot(b);if(!h){r();return}if(!h.classList.contains("draggable")){r();return}g!==void 0&&m!==void 0&&!Number.isNaN(g)&&!Number.isNaN(m)&&g!==m&&l("sortTabs",g,m),r()},onMove(f){const m=Ot(f.related);if(m!=null&&m.classList.contains("draggable")&&a.draggable){const g=f.dragged.classList.contains("affix-tab"),b=f.related.classList.contains("affix-tab");return g===b}else return!1},onStart:()=>{var f;n.style.cursor="grabbing",(f=n.querySelector(".draggable"))==null||f.classList.add("dragging")}});t.value=yield i()})}function s(){return Y(this,null,function*(){const{isMobile:n}=Ko();n.value||(yield Xe(),o())})}qe(s),de(()=>a.styleType,()=>{var n;(n=t.value)==null||n.destroy(),s()}),wt(()=>{var n;(n=t.value)==null||n.destroy()})}function Au(a){let l=null,t=null,o=0;const s=X(null),n=X(null),r=X(!1),i=X(!0),d=X(!1);function f(){var R;const O=(R=s.value)==null?void 0:R.$el;if(!O||!n.value)return{};const M=O.clientWidth,D=n.value.clientWidth;return{scrollbarWidth:M,scrollViewWidth:D}}function m(O,M=150){var A;const{scrollbarWidth:D,scrollViewWidth:R}=f();!D||!R||D>R||(A=n.value)==null||A.scrollBy({behavior:"smooth",left:O==="left"?-(D-M):+(D-M)})}function g(){return Y(this,null,function*(){var D,R;yield Xe();const O=(D=s.value)==null?void 0:D.$el;if(!O)return;const M=O==null?void 0:O.querySelector("div[data-radix-scroll-area-viewport]");n.value=M,h(),yield Xe(),b(),l==null||l.disconnect(),l=new ResizeObserver(Nt(A=>{h(),b()},100)),l.observe(M),o=((R=a.tabs)==null?void 0:R.length)||0,t==null||t.disconnect(),t=new MutationObserver(()=>{const A=M.querySelectorAll('div[data-tab-item="true"]').length;A>o&&b(),A!==o&&(h(),o=A)}),t.observe(M,{attributes:!1,childList:!0,subtree:!0})})}function b(){return Y(this,null,function*(){if(!n.value)return;yield Xe();const O=n.value,{scrollbarWidth:M}=f(),{scrollWidth:D}=O;M>=D||requestAnimationFrame(()=>{const R=O==null?void 0:O.querySelector(".is-active");R==null||R.scrollIntoView({behavior:"smooth",inline:"start"})})})}function h(){return Y(this,null,function*(){if(!n.value)return;const{scrollbarWidth:O}=f();r.value=n.value.scrollWidth>O})}const T=Nt(({left:O,right:M})=>{i.value=O,d.value=M},100);function H({deltaY:O}){var M;(M=n.value)==null||M.scrollBy({left:O*3})}return de(()=>a.active,()=>Y(null,null,function*(){b()}),{flush:"post"}),de(()=>a.styleType,()=>{g()}),qe(g),wt(()=>{l==null||l.disconnect(),t==null||t.disconnect(),l=null,t=null}),{handleScrollAt:T,handleWheel:H,initScrollbar:g,scrollbarRef:s,scrollDirection:m,scrollIsAtLeft:i,scrollIsAtRight:d,showScrollButton:r}}const Uu={class:"flex h-full flex-1 overflow-hidden"},Hu=L({name:"TabsView",__name:"tabs-view",props:{active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{},draggable:{type:Boolean,default:!0},gap:{},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{default:"chrome"},tabs:{},wheelable:{type:Boolean,default:!0}},emits:["close","sortTabs","unpin"],setup(a,{emit:l}){const t=a,o=l,s=He(t,o),{handleScrollAt:n,handleWheel:r,scrollbarRef:i,scrollDirection:d,scrollIsAtLeft:f,scrollIsAtRight:m,showScrollButton:g}=Au(t);function b(h){t.wheelable&&(r(h),h.stopPropagation(),h.preventDefault())}return Iu(t,o),(h,T)=>(u(),x("div",Uu,[Me($("span",{class:F([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(f),"pointer-events-none opacity-30":e(f)},"border-r px-2"]),onClick:T[0]||(T[0]=H=>e(d)("left"))},[p(e(jo),{class:"size-4 h-full"})],2),[[Be,e(g)]]),$("div",{class:F([{"pt-[3px]":h.styleType==="chrome"},"size-full flex-1 overflow-hidden"])},[p(e(yt),{ref_key:"scrollbarRef",ref:i,"shadow-bottom":!1,"shadow-top":!1,class:"h-full",horizontal:"","scroll-bar-class":"z-10 hidden ",shadow:"","shadow-left":"","shadow-right":"",onScrollAt:e(n),onWheel:b},{default:c(()=>[h.styleType==="chrome"?(u(),k(e($u),Ue(pe({key:0},ne(ne(ne({},e(s)),h.$attrs),h.$props))),null,16)):(u(),k(e(Pu),Ue(pe({key:1},ne(ne(ne({},e(s)),h.$attrs),h.$props))),null,16))]),_:1},8,["onScrollAt"])],2),Me($("span",{class:F([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(m),"pointer-events-none opacity-30":e(m)},"hover:bg-muted text-muted-foreground cursor-pointer border-l px-2"]),onClick:T[1]||(T[1]=H=>e(d)("right"))},[p(e(Rt),{class:"size-4 h-full"})],2),[[Be,e(g)]])]))}});function Wu(){const a=ot(),l=je(),t=nt(),o=pt(),{contentIsMaximize:s,toggleMaximize:n}=dl(),{closeAllTabs:r,closeCurrentTab:i,closeLeftTabs:d,closeOtherTabs:f,closeRightTabs:m,closeTabByKey:g,getTabDisableState:b,openTabInNewWindow:h,refreshTab:T,toggleTabPin:H}=cl(),O=w(()=>Re(l)),{locale:M}=Oa(),D=X();de([()=>o.getTabs,()=>o.updateTime,()=>M.value],([B])=>{D.value=B.map(N=>K(N))});const R=()=>{const B=xn(a.getRoutes(),N=>{var q;return!!((q=N.meta)!=null&&q.affixTab)});o.setAffixTabs(B)},A=B=>{const{fullPath:N,path:q}=o.getTabByKey(B);a.push(N||q)},V=B=>Y(null,null,function*(){yield g(B)});function K(B){var N;return ke(ne({},B),{meta:ke(ne({},B==null?void 0:B.meta),{title:v((N=B==null?void 0:B.meta)==null?void 0:N.title)})})}return de(()=>t.accessMenus,()=>{R()},{immediate:!0}),de(()=>l.fullPath,()=>{var N,q;const B=(q=(N=l.matched)==null?void 0:N[l.matched.length-1])==null?void 0:q.meta;o.addTab(ke(ne({},l),{meta:B||l.meta}))},{immediate:!0}),{createContextMenus:B=>{var xe,Ve;const{disabledCloseAll:N,disabledCloseCurrent:q,disabledCloseLeft:te,disabledCloseOther:ue,disabledCloseRight:ve,disabledRefresh:j}=b(B),J=(Ve=(xe=B==null?void 0:B.meta)==null?void 0:xe.affixTab)!=null?Ve:!1;return[{disabled:q,handler:()=>Y(null,null,function*(){yield i(B)}),icon:zt,key:"close",text:v("preferences.tabbar.contextMenu.close")},{handler:()=>Y(null,null,function*(){yield H(B)}),icon:J?nl:Pt,key:"affix",text:J?v("preferences.tabbar.contextMenu.unpin"):v("preferences.tabbar.contextMenu.pin")},{handler:()=>Y(null,null,function*(){s.value||(yield a.push(B.fullPath)),n()}),icon:s.value?ol:al,key:s.value?"restore-maximize":"maximize",text:s.value?v("preferences.tabbar.contextMenu.restoreMaximize"):v("preferences.tabbar.contextMenu.maximize")},{disabled:j,handler:()=>T(),icon:Yt,key:"reload",text:v("preferences.tabbar.contextMenu.reload")},{handler:()=>Y(null,null,function*(){yield h(B)}),icon:Kn,key:"open-in-new-window",separator:!0,text:v("preferences.tabbar.contextMenu.openInNewWindow")},{disabled:te,handler:()=>Y(null,null,function*(){yield d(B)}),icon:In,key:"close-left",text:v("preferences.tabbar.contextMenu.closeLeft")},{disabled:ve,handler:()=>Y(null,null,function*(){yield m(B)}),icon:Un,key:"close-right",separator:!0,text:v("preferences.tabbar.contextMenu.closeRight")},{disabled:ue,handler:()=>Y(null,null,function*(){yield f(B)}),icon:Gn,key:"close-other",text:v("preferences.tabbar.contextMenu.closeOther")},{disabled:N,handler:r,icon:An,key:"close-all",text:v("preferences.tabbar.contextMenu.closeAll")}].filter(Oe=>o.getMenuList.includes(Oe.key))},currentActive:O,currentTabs:D,handleClick:A,handleClose:V}}const Ou={class:"flex-center h-full"},Du=L({name:"LayoutTabbar",__name:"tabbar",props:{showIcon:{type:Boolean},theme:{}},setup(a){const l=je(),t=pt(),{contentIsMaximize:o,toggleMaximize:s}=dl(),{unpinTab:n}=cl(),{createContextMenus:r,currentActive:i,currentTabs:d,handleClick:f,handleClose:m}=Wu(),g=w(()=>{const b=t.getTabByKey(i.value);return r(b).map(T=>ke(ne({},T),{label:T.text,value:T.key}))});return z.tabbar.persist||t.closeOtherTabs(l),(b,h)=>(u(),x(Z,null,[p(e(Hu),{active:e(i),class:F(b.theme),"context-menus":e(r),draggable:e(z).tabbar.draggable,"show-icon":b.showIcon,"style-type":e(z).tabbar.styleType,tabs:e(d),wheelable:e(z).tabbar.wheelable,"middle-click-to-close":e(z).tabbar.middleClickToClose,onClose:e(m),onSortTabs:e(t).sortTabs,onUnpin:e(n),"onUpdate:active":e(f)},null,8,["active","class","context-menus","draggable","show-icon","style-type","tabs","wheelable","middle-click-to-close","onClose","onSortTabs","onUnpin","onUpdate:active"]),$("div",Ou,[e(z).tabbar.showMore?(u(),k(e(yu),{key:0,menus:g.value},null,8,["menus"])):U("",!0),e(z).tabbar.showMaximize?(u(),k(e(wu),{key:1,screen:e(o),onChange:e(s),"onUpdate:screen":e(s)},null,8,["screen","onChange","onUpdate:screen"])):U("",!0)])],64))}}),ac=L({name:"BasicLayout",__name:"layout",emits:["clearPreferencesAndLogout","clickLogo"],setup(a,{emit:l}){const t=l,{isDark:o,isHeaderNav:s,isMixedNav:n,isMobile:r,isSideMixedNav:i,isHeaderMixedNav:d,isHeaderSidebarNav:f,layout:m,preferencesButtonPosition:g,sidebarCollapsed:b,theme:h}=lt(),T=nt(),{refresh:H}=ul(),O=w(()=>o.value||z.theme.semiDarkSidebar?"dark":"light"),M=w(()=>o.value||z.theme.semiDarkHeader?"dark":"light"),D=w(()=>{const{collapsedShowTitle:ie}=z.sidebar,he=[];return ie&&b.value&&!n.value&&he.push("mx-auto"),i.value&&he.push("flex-center"),he.join(" ")}),R=w(()=>z.navigation.styleType==="rounded"),A=w(()=>r.value&&b.value?!0:s.value||n.value||f.value?!1:b.value||i.value||d.value),V=w(()=>!r.value&&(s.value||n.value||d.value)),{handleMenuSelect:K,handleMenuOpen:E,headerActive:B,headerMenus:N,sidebarActive:q,sidebarMenus:te,mixHeaderMenus:ue,sidebarVisible:ve}=vu(),{extraActiveMenu:j,extraMenus:J,handleDefaultSelect:ge,handleMenuMouseEnter:xe,handleMixedMenuSelect:Ve,handleSideMouseLeave:Oe,sidebarExtraVisible:_e}=bu(ue);function G(ie,he=!0){return he?Ka(ie,fe=>ke(ne({},$a(fe)),{name:v(fe.name)})):ie.map(fe=>ke(ne({},$a(fe)),{name:v(fe.name)}))}function ae(){Ye({sidebar:{hidden:!z.sidebar.hidden}})}function le(){t("clearPreferencesAndLogout")}function ye(){t("clickLogo")}de(()=>z.app.layout,ie=>Y(null,null,function*(){ie==="sidebar-mixed-nav"&&z.sidebar.hidden&&Ye({sidebar:{hidden:!1}})})),de(qo.global.locale,H,{flush:"post"});const $e=We(),ze=w(()=>Object.keys($e).filter(ie=>ie.startsWith("header-")));return(ie,he)=>(u(),k(e(Fd),{"sidebar-extra-visible":e(_e),"onUpdate:sidebarExtraVisible":he[0]||(he[0]=fe=>Kt(_e)?_e.value=fe:null),"content-compact":e(z).app.contentCompact,"content-compact-width":e(z).app.contentCompactWidth,"content-padding":e(z).app.contentPadding,"content-padding-bottom":e(z).app.contentPaddingBottom,"content-padding-left":e(z).app.contentPaddingLeft,"content-padding-right":e(z).app.contentPaddingRight,"content-padding-top":e(z).app.contentPaddingTop,"footer-enable":e(z).footer.enable,"footer-fixed":e(z).footer.fixed,"footer-height":e(z).footer.height,"header-height":e(z).header.height,"header-hidden":e(z).header.hidden,"header-mode":e(z).header.mode,"header-theme":M.value,"header-toggle-sidebar-button":e(z).widget.sidebarToggle,"header-visible":e(z).header.enable,"is-mobile":e(z).app.isMobile,layout:e(m),"sidebar-collapse":e(z).sidebar.collapsed,"sidebar-collapse-show-title":e(z).sidebar.collapsedShowTitle,"sidebar-enable":e(ve),"sidebar-collapsed-button":e(z).sidebar.collapsedButton,"sidebar-fixed-button":e(z).sidebar.fixedButton,"sidebar-expand-on-hover":e(z).sidebar.expandOnHover,"sidebar-extra-collapse":e(z).sidebar.extraCollapse,"sidebar-extra-collapsed-width":e(z).sidebar.extraCollapsedWidth,"sidebar-hidden":e(z).sidebar.hidden,"sidebar-mixed-width":e(z).sidebar.mixedWidth,"sidebar-theme":O.value,"sidebar-width":e(z).sidebar.width,"side-collapse-width":e(z).sidebar.collapseWidth,"tabbar-enable":e(z).tabbar.enable,"tabbar-height":e(z).tabbar.height,"z-index":e(z).app.zIndex,onSideMouseLeave:e(Oe),onToggleSidebar:ae,"onUpdate:sidebarCollapse":he[1]||(he[1]=fe=>e(Ye)({sidebar:{collapsed:fe}})),"onUpdate:sidebarEnable":he[2]||(he[2]=fe=>e(Ye)({sidebar:{enable:fe}})),"onUpdate:sidebarExpandOnHover":he[3]||(he[3]=fe=>e(Ye)({sidebar:{expandOnHover:fe}})),"onUpdate:sidebarExtraCollapse":he[4]||(he[4]=fe=>e(Ye)({sidebar:{extraCollapse:fe}}))},dt({logo:c(()=>[e(z).logo.enable?(u(),k(e(La),{key:0,fit:e(z).logo.fit,class:F(D.value),collapsed:A.value,src:e(z).logo.source,text:e(z).app.name,theme:V.value?M.value:e(h),onClick:ye},dt({_:2},[ie.$slots["logo-text"]?{name:"text",fn:c(()=>[P(ie.$slots,"logo-text")]),key:"0"}:void 0]),1032,["fit","class","collapsed","src","text","theme"])):U("",!0)]),header:c(()=>[p(e(lu),{theme:e(h),onClearPreferencesAndLogout:le},dt({"user-dropdown":c(()=>[P(ie.$slots,"user-dropdown")]),notification:c(()=>[P(ie.$slots,"notification")]),_:2},[!V.value&&e(z).breadcrumb.enable?{name:"breadcrumb",fn:c(()=>[p(e(ar),{"hide-when-only-one":e(z).breadcrumb.hideOnlyOne,"show-home":e(z).breadcrumb.showHome,"show-icon":e(z).breadcrumb.showIcon,type:e(z).breadcrumb.styleType},null,8,["hide-when-only-one","show-home","show-icon","type"])]),key:"0"}:void 0,V.value?{name:"menu",fn:c(()=>[p(e(Aa),{"default-active":e(B),menus:G(e(N)),rounded:R.value,theme:M.value,class:"w-full",mode:"horizontal",onSelect:e(K)},null,8,["default-active","menus","rounded","theme","onSelect"])]),key:"1"}:void 0,oe(ze.value,fe=>({name:fe,fn:c(()=>[P(ie.$slots,fe)])}))]),1032,["theme"])]),menu:c(()=>[p(e(Aa),{accordion:e(z).navigation.accordion,collapse:e(z).sidebar.collapsed,"collapse-show-title":e(z).sidebar.collapsedShowTitle,"default-active":e(q),menus:G(e(te)),rounded:R.value,theme:O.value,mode:"vertical",onOpen:e(E),onSelect:e(K)},null,8,["accordion","collapse","collapse-show-title","default-active","menus","rounded","theme","onOpen","onSelect"])]),"mixed-menu":c(()=>[p(e(hu),{"active-path":e(j),menus:G(e(ue),!1),rounded:R.value,theme:O.value,onDefaultSelect:e(ge),onEnter:e(xe),onSelect:e(Ve)},null,8,["active-path","menus","rounded","theme","onDefaultSelect","onEnter","onSelect"])]),"side-extra":c(()=>[p(e(mu),{accordion:e(z).navigation.accordion,collapse:e(z).sidebar.extraCollapse,menus:G(e(J)),rounded:R.value,theme:O.value},null,8,["accordion","collapse","menus","rounded","theme"])]),"side-extra-title":c(()=>[e(z).logo.enable?(u(),k(e(La),{key:0,fit:e(z).logo.fit,text:e(z).app.name,theme:e(h)},dt({_:2},[ie.$slots["logo-text"]?{name:"text",fn:c(()=>[P(ie.$slots,"logo-text")]),key:"0"}:void 0]),1032,["fit","text","theme"])):U("",!0)]),tabbar:c(()=>[e(z).tabbar.enable?(u(),k(e(Du),{key:0,"show-icon":e(z).tabbar.showIcon,theme:e(h)},null,8,["show-icon","theme"])):U("",!0)]),content:c(()=>[p(e(Zd))]),extra:c(()=>[P(ie.$slots,"extra"),e(z).app.enableCheckUpdates?(u(),k(e(lr),{key:0,"check-updates-interval":e(z).app.checkUpdatesInterval},null,8,["check-updates-interval"])):U("",!0),e(z).widget.lockScreen?(u(),k(ut,{key:1,name:"slide-up"},{default:c(()=>[e(T).isLockScreen?P(ie.$slots,"lock-screen",{key:0}):U("",!0)]),_:3})):U("",!0),e(g).fixed?(u(),k(e(ml),{key:2,class:"z-100 fixed bottom-20 right-0",onClearPreferencesAndLogout:le})):U("",!0),p(e(_s))]),_:2},[e(z).transition.loading?{name:"content-overlay",fn:c(()=>[p(e(Gd))]),key:"0"}:void 0,e(z).footer.enable?{name:"footer",fn:c(()=>[p(e(Jd),null,{default:c(()=>[e(z).copyright.enable?(u(),k(e($n),Ue(pe({key:0},e(z).copyright)),null,16)):U("",!0)]),_:1})]),key:"1"}:void 0]),1032,["sidebar-extra-visible","content-compact","content-compact-width","content-padding","content-padding-bottom","content-padding-left","content-padding-right","content-padding-top","footer-enable","footer-fixed","footer-height","header-height","header-hidden","header-mode","header-theme","header-toggle-sidebar-button","header-visible","is-mobile","layout","sidebar-collapse","sidebar-collapse-show-title","sidebar-enable","sidebar-collapsed-button","sidebar-fixed-button","sidebar-expand-on-hover","sidebar-extra-collapse","sidebar-extra-collapsed-width","sidebar-hidden","sidebar-mixed-width","sidebar-theme","sidebar-width","side-collapse-width","tabbar-enable","tabbar-height","z-index","onSideMouseLeave"]))}});export{ec as N,Ju as _,tc as a,ac as b,Yd as c,ar as d,lr as e,Tr as f,Vr as g,Ld as h,ml as i};
