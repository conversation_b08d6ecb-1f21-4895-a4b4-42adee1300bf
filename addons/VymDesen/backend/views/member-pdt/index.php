<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\LinkPager;
use addons\VymDesen\common\components\DataHelper;
$this->title = '控制台管理-用户产品管理';
error_reporting(0);
?>
<?php $this->beginBlock('content') ?>
<?php $node = \Yii::$app->session['auth']['node'];?>
<div class="row">
	<div class="clearfix">
		<div class="pull-right tableTools-container"></div>
	</div>
</div>
<div class="row" style="margin-bottom: 10px;">
	<form action="">
		<input type="hidden" name="r" value="member-pdt">
		<div class="col-xs-12">	
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='query_mode' id="form-field-select-1" >
					<option value="exact_query" <?php if (Html::encode(Yii::$app->controller->get('query_mode')) == "exact_query" ): ?> selected='selected' <?php endif ?>>IP地址精确查询</option>
					<option value="vague_query" <?php if (Html::encode(Yii::$app->controller->get('query_mode')) == "vague_query" ): ?> selected='selected' <?php endif ?>>IP地址模糊查询</option>
				</select>
			</div>
			<div style="float: left;margin-right: 6px;">   
				<input type="text" name="ip" value='<?php echo Html::encode(Yii::$app->controller->get('ip')) ?>'  placeholder="IP地址">
			</div>			
			<div style="float: left;margin-right: 6px;">   
				<input type="text" name="ipmi_ip" value='<?php echo Html::encode(Yii::$app->controller->get('ipmi_ip')) ?>'  placeholder="IPMI地址">
			</div>
			<div style="float: left;margin-right: 6px;">   
				<input type="text" name="unionid" value='<?php echo Html::encode(Yii::$app->controller->get('unionid')) ?>'  placeholder="业务ID">
			</div>
			<div style="float: left;margin-right: 6px;">   
				<input type="text" name="user_name" value='<?php echo Html::encode(Yii::$app->controller->get('user_name')) ?>'  placeholder="所属用户">
			</div>
			<div style="float: left;margin-right: 6px;">   
				<input type="text" name="user_nickname" value='<?php echo Html::encode(Yii::$app->controller->get('user_nickname')) ?>'  placeholder="用户昵称">
			</div>
			<div style="float: left;margin-right: 6px;">   
				<input type="text" name="start_time" id="start_time" value='<?php echo Html::encode(Yii::$app->controller->get('start_time')) ?>'  placeholder="开始时间">
			</div>
			<div style="float: left;margin-right: 6px;">   
				<input type="text" name="end_time" id="end_time" value='<?php echo Html::encode(Yii::$app->controller->get('end_time')) ?>'  placeholder="到期时间">
			</div>
			<br><br>
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='server_type_id'>
					<option value="">服务器分类</option>
					<?php foreach ($PdtManageTypeList as $val): ?>
					<option value="<?php echo $val['type_id'] ?>" rel="<?php echo $val['type_id'] ?>" <?php if ($val['type_id'] == Html::encode(Yii::$app->controller->get('server_type_id')) ): ?>
					 selected='selected'<?php endif ?>><?php echo $val['type_name'] ?></option>
					<?php endforeach ?>
				</select>
			</div>
			
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='pdt_id'>
					<option value="">产品配置类别</option>
					<?php foreach ($pdtRes as $value): ?>
					<option value="<?php echo $value['id'] ?>" rel="<?php echo $value['name'] ?>"   
					<?php if ($value['id'] == Html::encode(Yii::$app->controller->get('pdt_id')) ): ?>
					 selected='selected'
					 <?php endif ?>	
					><?php echo $value['name'] ?></option>
					<?php endforeach ?>
				</select>
			</div>
			
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='room_id'>
					<option value="">所属机房</option>
					<?php foreach ($roomRes as $value): ?>
					<option value="<?php echo $value['id'] ?>" rel="<?php echo $value['id'] ?>"   
					<?php if ($value['id'] == Html::encode(Yii::$app->controller->get('room_id')) ): ?>
					 selected='selected'
					 <?php endif ?>	
					><?php echo $value['name'] ?></option>
					<?php endforeach ?>
				</select>
			</div>
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='cabinet_id'>
					<option value="">所属机柜</option>
					<?php foreach ($cabinetRes as $value): ?>
					<option value="<?php echo $value['id'] ?>" rel="<?php echo $value['name'] ?>"   
					<?php if ($value['id'] == Html::encode(Yii::$app->controller->get('cabinet_id')) ): ?>
					 selected='selected'
					 <?php endif ?>	
					><?php echo $value['name'] ?></option>
					<?php endforeach ?>
				</select>
			</div>
			
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='admin_id'>
					<option value="">所属销售/客服</option>
					<?php foreach ($UserAdminRes as $value): ?>
					<option value="<?php echo $value['admin_id'] ?>" rel="<?php echo $value['rename'] ?>"   
					<?php if ($value['admin_id'] == Html::encode(Yii::$app->controller->get('admin_id')) ): ?>
					 selected='selected'
					 <?php endif ?>
					><?php echo $value['rename'] ?></option>
					<?php endforeach ?>
				</select>
			</div>
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='attribute_id'style='margin-left:10px'>
					<option value="" >服务器状态属性</option> 
					 <?php foreach ($ServerAttributeRes as $value): ?>
					<option value="<?php echo $value['id'] ?>" rel="<?php echo $value['name'] ?>"   
					<?php if ($value['id'] == Html::encode(Yii::$app->controller->get('attribute_id')) ): ?>
					 selected='selected'
					 <?php endif ?>
					><?php echo $value['name'] ?></option>
					<?php endforeach ?>
				</select>
			</div>
			<div style="float: left;margin-right: 6px;">   
				<select class="form-control" name='status'style='margin-left:10px'>
					<option value="" >使用状态</option>				
					<option value="1" <?php if (Html::encode(Yii::$app->controller->get('status')) == 1): ?>selected='selected'<?php endif ?>>正常使用</option>
					<option value="2" <?php if (Html::encode(Yii::$app->controller->get('status')) == 2): ?>selected='selected'<?php endif ?>>变更配置中</option>
					<option value="3" <?php if (Html::encode(Yii::$app->controller->get('status')) == 3): ?>selected='selected'<?php endif ?>>机器更换中</option>
					<option value="4" <?php if (Html::encode(Yii::$app->controller->get('status')) == 4): ?>selected='selected'<?php endif ?>>IP变更中</option>
					<option value="-2" <?php if (Html::encode(Yii::$app->controller->get('status')) == -2): ?>selected='selected'<?php endif ?>>退款中</option>
				</select>
			</div>
			<!--<div style="float: left;margin-right: 6px;">  
				<select class="form-control" name='audit_status'style='margin-left:10px'>
					<option value="">主管是否审核</option>				
					<option value="0" <?php if (Html::encode(Yii::$app->controller->get('audit_status')) == '0'): ?>selected='selected'<?php endif ?>>未审核</option>
					<option value="1" <?php if (Html::encode(Yii::$app->controller->get('audit_status')) == 1): ?>selected='selected'<?php endif ?>>已审核</option>
				</select>
			</div>-->
		<div>
			<button type="submit" class="btn btn-white btn-primary btn-bold" style="margin-left:15px">
				<span class="ace-icon fa fa-search icon-on-right bigger-110"></span>
				搜索
			</button>
			<button type="button" class="btn btn-white btn-success btn-bold" style="margin-left:10px;" onclick="javascript:location.href='<?php echo Url::to(['member-pdt/index']) ?>'"
				<span class="ace-icon fa  fa-refresh"></span>
				刷新
			</button>
			<?php if(in_array("member-pdt/export", $node) || $this->params['is_administrator_user']):?>
			<button type="button" class="btn btn-white btn-success btn-bold" style="margin-left:10px;" 
			onclick="javascript:location.href='<?php echo Url::to(['export',
				'unionid'=>Html::encode(Yii::$app->controller->get('unionid')),
				'user_name'=>Html::encode(Yii::$app->controller->get('user_name')),
				'ip'=>Html::encode(Yii::$app->controller->get('ip')),
				'ipmi_ip'=>Html::encode(Yii::$app->controller->get('ipmi_ip')),
				'end_time'=>Html::encode(Yii::$app->controller->get('end_time')),
				'server_type_id'=>Html::encode(Yii::$app->controller->get('server_type_id')),
				'room_id'=>Html::encode(Yii::$app->controller->get('room_id')),
				'cabinet_id'=>Html::encode(Yii::$app->controller->get('cabinet_id')),
				'pdt_id'=>Html::encode(Yii::$app->controller->get('pdt_id')),
				'admin_id'=>Html::encode(Yii::$app->controller->get('admin_id')),
				'status'=>Html::encode(Yii::$app->controller->get('status')),
				'attribute_id'=>Html::encode(Yii::$app->controller->get('attribute_id')),
			]) ?>'">
				<span class="ace-icon fa  fa-refresh"></span>
				导出
			</button>
			<?php endif;?>
	  </div>
	</div>							
	</form>
	</div>
	<form action="" id="checkbox">
	<table id="simple-table" width="100%" class="table table-striped table-bordered table-hover">
		<thead>
			<tr role="row">
                <th class="sorting_disabled" >操作</th>
                <th>IP地址</th>
                <th>IPMI地址</th>
                <th>服务器分类</th>
				<th>机 房</th>
				<th>产品配置类别</th>
				<th>售价(元)</th>
				<th>所属用户</th>
				<th>用户昵称</th>
				<th>起租时间</th>
				<th>到期时间</th>
				<th>机器状态</th>
				<th>租用状态</th>
			</tr>
		</thead>
		<tbody>
		   <?php foreach ($arrRes as $value):?>
		   <tr role="row" class="odd">
               <td>
                   <div class="">
                       <?php if(in_array("member-pdt/config-item", $node) || $this->params['is_administrator_user']):?>
                           <a class="blue notranslate" href="<?php echo Url::to(['config-item', 'unionid'=>$value['unionid']]) ?>" title="查看配置">
                               查看配置
                           </a>
                       <?php endif;?>
                       <a href="javascript:void(0)" class='notranslate label label-success copy_data' data-clipboard-text="<?=$value['copy_text']?>">一键复制</a>
                   </div>
               </td>
               <td>
                   <?php if ($value['ip'] == null):?>
                   <?php else: ?>
                       <?php $ipRes =  json_decode(@$value['ip'],true)?>
                       <?php echo $ipRes['0']?> &nbsp; <?php if ( count($ipRes) > 1):?><a class="notranslate" href="<?php echo Url::to(['config-item', 'unionid'=>$value['unionid']]) ?>">更多</a><?php endif;?>
                   <?php endif;?>
               </td>
               <td><?php echo $value['ipmi_ip']?></td>
				<td><?php echo $value['servertype']['0']['type_name'] ?></td> 
				<td><?php echo $value['pdtroom'][0]['name']?></td>
				<td><?php echo $value['pdtmanage'][0]['name']?> <?php if(!empty($value['note'])): ?><br/><a href="javascript:void(0)" rel="<?=$value['note']?>" class="open-event label label-info">配置改动</a><?php endif;?></td>
				
				<?php if($is_allow):?>
					<td><?php echo $value['sell_price']?></td>
					<td><?php echo (empty($value['user_name']) ?
                            (empty($value["usermember"][0]["mobile"]) ? $value["usermember"][0]["username"] : $value["usermember"][0]["mobile"])
                            : $value['user_name'])?></td>
					<td><?php echo $value['usermember'][0]['uname']?></td>
				<?php else:?>
					<?php if($value['admin_id'] == $admin_id):?>
						<td><?php echo $value['sell_price']?></td>
						<td><?php echo $value['user_name'];?></td>
						<td><?php echo $value['usermember'][0]['uname']?></td>
					<?php else:?>
						<td>---</td>
						<td>---</td>
						<td>---</td>
					<?php endif;?>
				<?php endif;?>
				
				<td>
					<?php if (!empty($value['start_time'])): ?>
						<?php echo date('Y-m-d', $value['start_time']) ?>
					<?php else: ?>
						--
					<?php endif ?>								
				</td>	
				<td>
					<?php if (!empty($value['end_time'])): ?>
						<?php echo date('Y-m-d', $value['end_time']) ?>
					<?php else: ?>
						--
					<?php endif ?>
				</td>
				<td>
					<?php 
						$hasPreOff = Yii::$app->db->createCommand("select * from member_pdt_appoint_off where appoint_unionid = '".$value["unionid"]."'")->queryOne();
					?>
					
					<?php if($hasPreOff):?>
						<span class='label label-primary'><?php echo '预约下架 '.date("m-d", $hasPreOff['appoint_time'])?></span>
					<?php else:?>
						<?php if (@$value['status'] == 1): ?>
							<span class='label label-success'><?php echo '正常' ?></span>
						<?php elseif(@$value['status'] == -1): ?>
							<span class='label label-danger'><?php echo '已删除' ?></span>
						<?php elseif(@$value['status'] == 0): ?>
							<span class='label label-warning'><?php echo '未开通' ?></span>
						<?php elseif(@$value['status'] == -2): ?>
							<span class='label label-warning'><?php echo '退款中' ?></span>
						<?php elseif(@$value['status'] == 2): ?>
							<span class='label label-warning'><?php echo '变配中' ?></span>
						<?php elseif(@$value['status'] == 3): ?>
							<span class='label label-warning'><?php echo '换机中' ?></span>
						<?php elseif(@$value['status'] == 4): ?>
							<span class='label label-warning'><?php echo '换IP中' ?></span>
						<?php elseif(@$value['status'] == 5): ?>
							<span class='label label-warning'><?php echo '过户中' ?></span>
						<?php elseif(@$value['status'] == 6): ?>
							<span class='label label-warning'><?php echo '关机下架中' ?></span>
						<?php elseif(@$value['status'] == 99): ?>
							<span class='label label-warning'><?php echo '工单处理中' ?></span>
						<?php endif;?>
					<?php endif;?>
				</td>
				<td class="" style="">
					<?php if (strtotime(date("Y-m-d", $value['end_time'])) < strtotime(date("Y-m-d", time()))):?>
						<span class='label label-danger'><?php echo '已 到 期' ?></span>
					<?php elseif(strtotime(date("Y-m-d", $value['end_time'])) == strtotime(date("Y-m-d", time()))):?>
						<span class='label label-warning'><?php echo '今日到期' ?></span>
					<?php elseif(strtotime(date("Y-m-d", $value['end_time'])) >= strtotime(date("Y-m-d", time()))+86400 && strtotime(date("Y-m-d", $value['end_time'])) <= strtotime(date("Y-m-d", time()))+86400*3):?>
						<span class='label label-pink'><?php echo '即将到期'?></span>
					<?php else:?>
						<span class='label label-success'><?php echo '正常使用' ?></span>
					<?php endif;?>
				</td>
			</tr>
			<?php endforeach;?>
		</tbody>
	</table>
	<div class="row">
		<div class="col-xs-6">
			<div class="dataTables_info" id="dynamic-table_info" role="status" aria-live="polite">每页<?php echo $pageSize;?>条记录，共有<?php echo $iCount;?>条记录</div>
		</div>
		<div class="col-xs-6 pagination1" style="text-align: right;">
			<?php 
				echo LinkPager::widget([
					'pagination' => $page,
					'firstPageLabel'=>"首页",
					'prevPageLabel'=>'上一页',
					'nextPageLabel'=>'下一页',
					'lastPageLabel'=>'末页',
				]);
			?>
		</div>
    </div>
</form>
<style type="text/css">
.form-horizontal {padding-left: 30px;}
.font-size-12 {font-size: 12px;}
.ny-form .form-group {margin-bottom: 12px;line-height: 30px;}
.form-horizontal .form-group {margin-right: -15px;margin-left: -15px;}
.ny-form .ny-control-label {float: left;}
.ny-control-label {width: 135px;padding-right: 0;text-align: right;color: #808080;}
.ny-form-control {display: inline-block;white-space: nowrap;color: #555;background-color: #fff;background-image: none;outline: none;}    
.ny-number-container {float: left;line-height: 1;}
.number-input-box {width: 131px;}
.number-input-box {float: left;position: relative;width: 100px;border-radius: 2px;}
.alert-warn, .alert-error, .alert-success {padding: 7px 22px 5px 37px;background: url(img/uc/tip_icon_warn_16.png) no-repeat 10px 10px #fff3e5;
border: 1px solid #ff8800;border-radius: 2px;color: #ff8800;font-size: 12px;line-height: 2em;}
.margin-bottom-20 {margin-bottom: 20px;}
</style>
<!-- 审核end -->
<div class="bootbox modal fade bootbox-prompt in" id="reviewed_page" tabindex="-1" role="dialog" style="padding-right: 17px;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
                    <font style="vertical-align: inherit;">
                        <font style="vertical-align: inherit;">×</font>
                    </font>
                </button>
                <h4 class="modal-title">
                <font style="vertical-align: inherit;"><font style="vertical-align: inherit;">主管审核</font></font></h4>
            </div>
            <div class="modal-body">
                <div class="bootbox-body">
					<div class="alert-warn margin-bottom-20">
						* 成本价为当前产品在付款周期内的所有基础成本<br/>
						* 配件成本价为当前产品在付款周期内的所有额外配件成本<br/> 
					</div>					
                    <form id="reviewedForm" action="" class="bootbox-form form-horizontal ny-form ny-tab-container font-size-12 tab-group-undefined"> 
						<div class="form-group">
							<div class="ny-control-label">CPU：</div><div class="col-xs-8 ny-form-control product_config_cpu"></div>
							<div class="ny-control-label">内存：</div><div class="col-xs-8 ny-form-control product_config_ram"></div>
							<div class="ny-control-label">硬盘：</div><div class="col-xs-8 ny-form-control product_config_hdd"></div>
							<div class="ny-control-label">带宽：</div><div class="col-xs-8 ny-form-control product_config_configbandwidth"></div>
							<div class="ny-control-label">可用IP：</div><div class="col-xs-8 ny-form-control product_config_ipnumber"></div>
							<div class="ny-control-label">防御：</div><div class="col-xs-8 ny-form-control product_config_defense"></div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">IP：</div>
							<div class="col-xs-8 ny-form-control product_config_ip"></div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">产品销售价格：</div>
							<div class="col-xs-8 ny-form-control sell_price"></div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">产品付款周期：</div>
							<div class="col-xs-8 ny-form-control payment_cycle"></div>
						</div>
            			<div class="form-group">
            				<div class="ny-control-label">成本价：</div>
            				<div class="col-xs-8 ny-form-control">
            					<div class="ny-number-container">
            					   <span class="number-input-box"><input type="text" name="cost_price" value = "" class="ny-input-reset ny-number-input"></span>
            					</div>
            				</div>
            			</div>            			
            			<div class="form-group">
            				<div class="ny-control-label">配件成本价：</div>
            				<div class="col-xs-8 ny-form-control">
            					<div class="ny-number-container">
            					   <span class="number-input-box"><input type="text" name="upgrade_cost_price" value="" class="ny-input-reset ny-number-input"></span></div>
            				</div>
            			</div>
						<div class="form-group">
            				<div class="ny-control-label">成本价货币类型：</div>
            				<div class="col-xs-8 ny-form-control">
            					<div class="ny-number-container">
            					   <span class="number-input-box">
									<select name="currency_type" id="currency_type"></select>
								   </span>
								</div>
            				</div>
            			</div>
            			<input type="hidden" name="id" value=""> 
            		</form>            		
                </div>
            </div>
            <div class="modal-footer">
                <button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
                    <font style="vertical-align: inherit;">
                        <font style="vertical-align: inherit;">取消</font></font>
                </button>
                <button data-bb-handler="confirm" data-last="Finish" type="button" class="btn btn-primary" id="reviewed">
                    <font style="vertical-align: inherit;"><font style="vertical-align: inherit;">确定</font></font>
                </button>
            </div>
        </div>
    </div>
</div>
<textarea id="copy" style="opacity:0" data-clipboard-action="copy"></textarea>
<!--审核end -->

<!-- inline scripts related to this page -->
<script>
	var clipboard = new ClipboardJS('.copy_data');
	clipboard.on('success', function (e) {
		console.info('Action:', e.action);
		console.info('Text:', e.text);
		console.info('Trigger:', e.trigger);

		e.clearSelection();
		layer.alert("复制成功", {icon: 1});
	});

	clipboard.on('error', function (e) {
		console.error('Action:', e.action);
		console.error('Trigger:', e.trigger);
		layer.alert("复制失败", {icon: 2});
	});
    function copy(dataText) {
        $("#copy").val(dataText);
        $("#copy").focus();
        $("#copy").select();
        if(copyinfo()) {
            layer.alert("复制完成", {icon:1});
        } else {
            layer.alert("复制失败，可能由于浏览器不支持<br/>请使用：chrome浏览器或360浏览器(极速模式)", {icon:7});
        }
    }

	//弹出一个tips层
    $('.open-event').on('click', function(){
		var str = $(this).attr('rel');
		layer.tips(str, $(this), {
		  tips: [1, '#82af6f'],
		  time: 3000
		});
	});
	var start = {
		elem: '#start_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas){
			 end.min = datas; //开始日选好后，重置结束日的最小日期
			 end.start = datas //将结束日的初始值设定为开始日
		}
	};
	laydate(start);
	laydate.skin('molv');
	
	var end = {
		elem: '#end_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas){
			 end.min = datas; //开始日选好后，重置结束日的最小日期
			 end.start = datas //将结束日的初始值设定为开始日
		}
	};
	laydate(end);
	laydate.skin('molv');
</script>
<script type="text/javascript">
	//主管审核页
	$('.reviewed_show').click(function(){
		var url = "<?php echo Url::to(['ajax-getmemberpdt']) ?>";
		var id = $(this).attr('rel');
		var load = layer.load(2);
		$.post(url, {id:id}, function(data){
			var res = data['data'];
			var config = JSON.parse(res['info']['config']);
			var ip = JSON.parse(res['info']['ip']);
			var ips = jsonObjToStr(ip);
			$('.product_config_cpu').html(config['cpu']);  
			$('.product_config_ram').html(config['ram']);  
			$('.product_config_hdd').html(config['hdd']);  
			$('.product_config_ipnumber').html(config['ipnumber']);  
			$('.product_config_configbandwidth').html(config['configbandwidth']);  
			$('.product_config_defense').html(config['defense']); 
			$('.product_config_ip').html(ips); 
			$('.payment_cycle').html(res['info']['payment_cycle']+' 个月');  
			$('.sell_price').html(res['info']['sell_price']+' 元'); 
			$("[name='id']").val(res['info']['id']);
			
			$("[name='currency_type']").empty();
			$("[name='currency_type']").append('<option value="" rel="" selected="selected">请选择货币类型</option>');
			$.each(res.data, function(key, val){
				if(val == "RMB") {
					$("[name='currency_type']").append('<option value="'+key+'" rel="'+val+'">'+val+'</option>');
				} else {
					$("[name='currency_type']").append('<option value="'+key+'" rel="'+val+'">'+val+'</option>');
				}
			});	
			$('#reviewed_page').modal('show');
			layer.closeAll('loading');
		},'json');		
	});
	
	
	function jsonObjToStr(data) {
		var newdata = "";
		$.each(data, function(i, n) {
			newdata += n + ", ";
		});
		newdata = newdata.substr(0, newdata.length - 1);
		return newdata;
	}
	
	$("[name='room_id']").change(function(){
		var url = "<?php echo Url::to(['ajax-roomchange']) ?>";
		var id = $(this).find('option:selected').attr('rel');
		var load = layer.load(2);
		$.post(url, {id:id}, function(data){    		
			$("[name='cabinet_id']").empty();			
			$("[name='cabinet_id']").append('<option value="">所属机柜</option>');  			
			$.each(data['data']['Cabinet'], function(key, val){
				$("[name='cabinet_id']").append('<option value="'+val['id']+'" rel="'+val['id']+'">'+val['name']+'</option>');
			});				
			layer.closeAll('loading');
		},'json');
	});
	//服务器分类改变
	$("[name='server_type_id']").change(function(){
		var url = "<?php echo Url::to(['ajax-servertypechange']) ?>";
		var id = $(this).find('option:selected').attr('rel');
		var load = layer.load(2);
		$.post(url, {id:id}, function(data){
			$("[name='pdt_id']").empty();
			$("[name='pdt_id']").append('<option value="">产品配置类别</option>');
			$.each(data['data']['pdtmanage'], function(key, val) {
				$("[name='pdt_id']").append('<option value="'+val['id']+'" rel="'+val['id']+'">'+val['name']+'</option>');
			});
			layer.closeAll('loading');
		},'json');
	});
	//审核操作
	$('#reviewed').click(function(){
		var _this = $(this);		
		layer.confirm('确定要审核通过该服务器产品信息吗？',{icon:3,
			btn: ['确定','取消'] //按钮
		}, function(){
			var currency_type = $('#currency_type option:selected') .val();
			if(!currency_type) {
				layer.alert('未选择货币类型', {icon:7});
				return false;
			}
			var url = "<?php echo Url::to(['reviewed']) ?>";
			layer.msg('操作中...', {icon:16, time:0});
			$.post(url, $('#reviewedForm').serialize(), function(data){
			if ( data['data']['status'] ) {
				layer.alert(data['data']['info'], {icon:1}, function(index){				   		  
					layer.msg('正在更新数据...', {icon:16, time:0});
					location.reload();
				}); 
			} else {
				layer.alert(data['data']['info'], {icon:7});
			}
		   },'json');
		}, function(){
		   
		});
		return false;
	});	
	//单选删除操作
	$('.del').click(function(){
		var _this = $(this);
		layer.confirm('确定要删除该服务器产品吗？',{icon:3,
			btn: ['确定','取消'] //按钮
		}, function(){
			var url = "<?php echo Url::to(['del']) ?>";
			layer.msg('操作中...', {icon:16, time:0});
		   $.post(url, {id:_this.attr('rel')}, function(data){
			if ( data['data']['status'] ) {
				layer.alert(data['data']['info'], {icon:1}, function(index){				   		  
					layer.msg('正在更新数据...', {icon:16, time:0});
					location.reload();
				}); 
			}else{
				layer.alert(data['data']['info'], {icon:7});
			}
		   },'json');
		}, function(){
		   
		});
		return false;
	});
</script>
<?php $this->endBlock(); ?>
