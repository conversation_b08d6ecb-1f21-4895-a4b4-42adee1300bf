<?php

use addons\VymServer\common\enums\OrderEnum;
use common\enums\AppEnum;
use common\helpers\ArrayHelper;
use common\helpers\Html;
use common\helpers\MerchantHelper;
use common\helpers\Url;
use kartik\date\DatePicker;
use yii\grid\ActionColumn;
use yii\grid\GridView;
use yii\widgets\ActiveForm;

/** @var yii\web\View $this */
/** @var yii\data\ActiveDataProvider $dataProvider */
/** @var common\models\base\SearchModel $searchModel */

$this->title                   = '业务订单';
$this->params['breadcrumbs'][] = $this->title;
?>
<!-- Main content -->
<div class="container-fluid">
    <!-- 搜索筛选区域 -->
    <div class="card search-card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-search mr-1"></i>
                筛选条件
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <?php $form = Html::commonFilterForm()?>
            <!-- 商户筛选 -->
            <?php if (Yii::$app->id == AppEnum::BACKEND): ?>
                <?= MerchantHelper::merchantSelect($form, $searchModel) ?>
            <?php endif; ?>

            <?= $form->field($searchModel, 'original_no')->textInput(['placeholder' => '主订单号',])->label('主订单号') ?>
            <?= $form->field($searchModel, 'general_no')->textInput(['placeholder' => '付款单号',])->label('付款单号') ?>
            <?= $form->field($searchModel, 'payment_no')->textInput(['placeholder' => '支付单号',])->label('支付单号') ?>
            <?= $form->field($searchModel, 'uid')->textInput(['placeholder' => '用户ID',])->label('用户ID') ?>
            <?= $form->field($searchModel, 'goods_id')->textInput(['placeholder' => '商品ID',])->label('商品ID') ?>
            <?= $form->field($searchModel, 'product_id')->textInput(['placeholder' => '产品ID',])->label('产品ID') ?>

            <?= $form->field($searchModel, 'order_status')->dropDownList(OrderEnum::getMap(), ['prompt' => '选择订单状态'])->label('订单状态') ?>
            <?= $form->field($searchModel, 'order_pay_status')->dropDownList(OrderEnum::getPaymentStatusMap(), ['prompt' => '选择支付状态'])->label('支付状态') ?>


            <?= $form->field($searchModel, 'created_at_start', [
                'template' => "{input}{error}",
                'options'  => ['class' => 'col-sm-12 col-md-6 col-lg-3 col-lg-2 mb-2'],
            ])->widget(DatePicker::class, [
                'options'       => ['placeholder' => '创建时间开始', 'class' => 'form-control'],
                'pluginOptions' => [
                    'autoclose' => true,
                    'format'    => 'yyyy/mm/dd',
                ],
            ])->label('创建时间开始') ?>
            <?= $form->field($searchModel, 'created_at_end', [
                'template' => "{input}{error}",
                'options'  => ['class' => 'col-sm-12 col-md-6 col-lg-3 col-lg-2 mb-2'],
            ])->widget(DatePicker::class, [
                'options'       => ['placeholder' => '创建时间结束', 'class' => 'form-control'],
                'pluginOptions' => [
                    'autoclose' => true,
                    'format'    => 'yyyy/mm/dd',
                ],
            ])->label('创建时间结束') ?>
            <div class="col-sm-12 col-md-6 col-lg-3">
                <?= Html::submitButton('<i class="fas fa-search"></i> 搜索', ['class' => 'btn btn-primary btn-md mr-2',]) ?>
                <?= Html::a('<i class="fas fa-redo"></i> 重置', ['index'], ['class' => 'btn btn-outline-secondary btn-md',]) ?>
            </div>
            <?php ActiveForm::end(); ?>
        </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-shopping-cart mr-1"></i>
                <?= Html::encode($this->title) ?>
            </h3>
            <div class="card-tools">
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <?= GridView::widget([
                    'dataProvider' => $dataProvider,
                    'layout'       => "{items}\n<div class='row d-flex align-items-center'><div class='col-xl-6 col-lg-3 col-md-12'>{summary}</div><div class='col-xl-6 col-lg-9 col-md-12'>{pager}</div></div>",
                    'tableOptions' => [
                        'class' => 'table table-hover rf-table mb-0',
                    ],
                    'columns'      => [
                        [
                            'attribute'      => 'id',
                            'headerOptions'  => ['class' => 'text-left'],
                            'contentOptions' => ['class' => 'text-left'],
                        ],
                        [
                            'label'          => '所属商户',
                            'headerOptions'  => ['style' => 'width: 120px;'],
                            'contentOptions' => ['class' => 'text-left'],
                            'format'         => 'raw',
                            'visible'        => Yii::$app->id == AppEnum::BACKEND, // 只在总后台显示
                            'value'         => function ($model) {
                                if ($model->merchant && $mId = $model->merchant->id) {
                                    if ($mTitle = $model->merchant->title) {
                                        return Html::tag('span', $mTitle, ['class' => 'badge badge-primary']);
                                    }
                                    return Html::tag('span', "ID:{$mId}", ['class' => 'badge badge-primary']);
                                }
                                return Html::tag('span', '平台直属', ['class' => 'badge badge-secondary']);
                            },
                        ],
                        [
                            'attribute'      => 'uid',
                            'label'          => '用户信息',
                            'headerOptions'  => ['style' => 'width: 120px;'],
                            'contentOptions' => ['class' => 'text-left'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $uid       = $model->uid;
                                $userName  = '';
                                $userEmail = '';

                                // 从 paymentFlow 中获取用户信息
                                if ($model->paymentFlow) {
                                    $userName  = $model->paymentFlow->user_name ?? '';
                                    $userEmail = $model->paymentFlow->user_email ?? '';
                                }

                                $userInfo = "UID: {$uid}";
                                if ($userName) {
                                    $userInfo .= "<br/>用户: {$userName}";
                                }
                                if ($userEmail) {
                                    $userInfo .= "<br/>邮箱: {$userEmail}";
                                }
                                return $userInfo;
                            },
                        ],
                        [
                            'header'        => "订单号",
                            'headerOptions' => ['style' => 'width: 150px;'],
                            'format'        => 'raw',
                            'value'         => function ($model) {
                                $configStr = "主订单号：{$model->original_no} <br/>";
                                $configStr .= "支付单号：{$model->general_no} <br/>";
                                $configStr .= "付款单号：{$model->payment_no}";
                                return $configStr;
                            },
                        ],
                        [
                            'header'        => "产品信息",
                            'headerOptions' => ['class' => 'col-md-2'],
                            'filter'        => false,
                            'format'        => 'raw',
                            'value'         => function ($model) {
                                $cartData     = $model->cart_data;
                                $cpu          = ArrayHelper::getValue($cartData, 'cart_config.config.cpu');
                                $ram          = ArrayHelper::getValue($cartData, 'cart_config.config.ram');
                                $hdd          = ArrayHelper::getValue($cartData, 'cart_config.config.hdd');
                                $bandwidth    = ArrayHelper::getValue($cartData, 'cart_config.config.configbandwidth');
                                $ipnumber     = ArrayHelper::getValue($cartData, 'cart_config.config.ipnumber');
                                $operatsystem = ArrayHelper::getValue($cartData, 'cart_config.config.operatsystem');
                                $defense      = ArrayHelper::getValue($cartData, 'cart_config.config.defense');
                                $card         = ArrayHelper::getValue($cartData, 'cart_config.config.card');

                                //硬件配置
                                $hardware = ['cpu' => $cpu, 'ram' => $ram, 'hdd' => $hdd, 'card' => $card,];
                                //网络配置
                                $network = ['bandwidth' => $bandwidth, 'ipnumber' => $ipnumber, 'defense' => $defense,];
                                //移除空值或NULL
                                $hardware    = array_filter($hardware, function ($value) {
                                    return $value !== '' && $value !== null;
                                });
                                $hardwareStr = implode(' / ', $hardware);
                                $network     = array_filter($network, function ($value) {
                                    return $value !== '' && $value !== null;
                                });
                                $networkStr  = implode(' / ', $network);

                                $configStr = "硬件配置：{$hardwareStr} <br/>";
                                $configStr .= "网络配置：{$networkStr} <br/>";
                                $configStr .= "操作系统：{$operatsystem}";
                                return $configStr;
                            },
                        ],
                        [
                            'header'        => "订单金额",
                            'headerOptions' => ['style' => 'width: 120px;'],
                            'format'        => 'raw',
                            'value'         => function ($model) {
                                if (!$model->original) return '-';
                                $amount = "金额: ¥" . number_format($model->original->order_original_price, 2) . "<br/>";
                                if ($model->original->order_finish_pay > 0) {
                                    $amount .= "实付: ¥" . number_format($model->original->order_finish_pay, 2);
                                }
                                return $amount;
                            },
                        ],
                        [
                            'attribute'      => 'pay_status',
                            'label'          => '支付状态',
                            'headerOptions'  => ['style' => 'width: 100px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                if (!$model->original) return '-';
                                $payStatus = $model->original->order_pay_status;

                                // 直接使用状态文本获取颜色
                                $color = OrderEnum::getStatusColor($payStatus, 'payment');

                                return Html::tag('span', $payStatus, ['class' => "badge badge-{$color}"]);
                            },
                        ],
                        [
                            'attribute'      => 'order_status',
                            'label'          => '订单状态',
                            'headerOptions'  => ['style' => 'width: 100px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                if (!$model->original) return '-';
                                $status = $model->original->order_status;

                                // 直接使用状态文本获取颜色
                                $color = OrderEnum::getStatusColor($status, 'order');

                                return Html::tag('span', $status, ['class' => "badge badge-{$color}"]);
                            },
                        ],
                        [
                            'label'          => '订单时间',
                            'headerOptions'  => ['style' => 'width: 130px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $created_at   = $model->created_at;
                                $payment_time = $model->paymentFlow->payment_successtime;
                                $timeStr      = "创建时间: {$created_at}";
                                if ($payment_time) {
                                    $payment_at = date('Y-m-d H:i:s', $payment_time);
                                    $timeStr    .= "<br/>支付时间: {$payment_at}";
                                }
                                return $timeStr;
                            },
                        ],
                        [
                            'header'         => '操作',
                            'headerOptions'  => ['style' => 'width: 120px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $buttons   = [];
                                $buttons[] = Html::a('<i class="fas fa-eye"></i> 详情', ['view', 'id' => $model->id], [
                                    'class' => 'btn btn-sm btn-outline-info',
                                    'title' => '查看详情',
                                ]);
                                return implode(' ', $buttons);
                            },
                        ],
                    ],
                ]); ?>
            </div>
        </div>
    </div>

</div>
