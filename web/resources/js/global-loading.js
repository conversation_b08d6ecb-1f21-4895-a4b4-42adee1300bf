/**
 * 全局 Loading 提示管理器
 * 基于 layer.js 实现页面跳转时的加载提示
 */
(function() {
    'use strict';
    
    // 全局 Loading 管理器
    window.GlobalLoading = {
        // 获取顶层窗口（处理iframe情况）
        getTopWindow: function() {
            try {
                // 尝试访问顶层窗口
                if (window.top !== window && window.top.layer) {
                    return window.top;
                }
            } catch (e) {
                // 跨域情况下无法访问top，使用当前窗口
            }
            return window;
        },
        
        // 显示 loading
        show: function() {
            var topWindow = this.getTopWindow();
            var topLayer = topWindow.layer || window.layer;
            
            if (topLayer) {
                var loading = topLayer.load(2, {
                    time: 10 * 1000, // 10秒超时自动关闭
                    shade: [0.3, '#000'] // 添加半透明遮罩
                });
                // 同时在当前窗口和顶层窗口存储loading ID
                sessionStorage.setItem('globalLoading', loading);
                if (topWindow !== window) {
                    try {
                        topWindow.sessionStorage.setItem('globalLoading', loading);
                    } catch (e) {
                        // 跨域情况下无法访问top.sessionStorage
                    }
                }
                return loading;
            }
            return null;
        },
        
        // 关闭 loading
        close: function() {
            var topWindow = this.getTopWindow();
            var topLayer = topWindow.layer || window.layer;
            
            if (topLayer) {
                // 尝试从多个地方获取loading ID
                var loading = sessionStorage.getItem('globalLoading');
                if (!loading && topWindow !== window) {
                    try {
                        loading = topWindow.sessionStorage.getItem('globalLoading');
                    } catch (e) {
                        // 跨域情况下无法访问
                    }
                }
                
                if (loading) {
                    topLayer.close(loading);
                    sessionStorage.removeItem('globalLoading');
                    if (topWindow !== window) {
                        try {
                            topWindow.sessionStorage.removeItem('globalLoading');
                        } catch (e) {
                            // 跨域情况下无法访问
                        }
                    }
                }
            }
        },
        
        // 判断是否需要显示 loading
        shouldShowLoading: function($link, href) {
            // 排除空链接和锚点链接
            if (!href || href === '#' || href === 'javascript:void(0)' || href.startsWith('javascript:')) {
                return false;
            }
            
            // 排除外部链接
            if (href.startsWith('http') && !href.includes(window.location.hostname)) {
                return false;
            }
            
            // 排除下载链接
            if ($link.attr('download') !== undefined) {
                return false;
            }
            
            // 排除新窗口打开的链接
            if ($link.attr('target') === '_blank') {
                return false;
            }
            
            // 排除带有 no-loading class 的链接
            if ($link.hasClass('no-loading')) {
                return false;
            }
            
            // 排除 AJAX 请求相关的链接
            if ($link.hasClass('ajax-link') || $link.data('method') || $link.data('confirm')) {
                return false;
            }
            
            return true;
        },
        
        // 判断按钮是否需要显示 loading
        shouldShowLoadingForButton: function($button, onclick) {
            if (!onclick) {
                return false;
            }
            
            // 排除带有 no-loading class 的按钮
            if ($button.hasClass('no-loading')) {
                return false;
            }
            
            // 检查onclick是否包含页面跳转的关键词
            var jumpPatterns = [
                /location\.href\s*=/i,           // location.href=
                /window\.location\s*=/i,         // window.location=
                /location\.replace\(/i,          // location.replace()
                /window\.open\(/i,               // window.open()
                /document\.location\s*=/i       // document.location=
            ];
            
            for (var i = 0; i < jumpPatterns.length; i++) {
                if (jumpPatterns[i].test(onclick)) {
                    // 进一步检查是否是外部链接
                    var urlMatch = onclick.match(/['"`]([^'"`]+)['"`]/);
                    if (urlMatch && urlMatch[1]) {
                        var url = urlMatch[1];
                        // 排除外部链接和javascript:协议
                        if (url.startsWith('http') && !url.includes(window.location.hostname)) {
                            return false;
                        }
                        if (url.startsWith('javascript:')) {
                            return false;
                        }
                    }
                    return true;
                }
            }
            
            return false;
        },
        
        // 初始化
        init: function() {
            var self = this;
            
            // 页面加载完成时关闭可能存在的 loading
            self.close();
            
            // 监听所有链接点击事件
            $(document).on('click', 'a[href]', function(e) {
                var $link = $(this);
                var href = $link.attr('href');
                
                // 判断是否需要显示 loading
                if (self.shouldShowLoading($link, href)) {
                    // 延迟一点点显示，避免快速点击造成闪烁
                    setTimeout(function() {
                        self.show();
                    }, 100);
                }
            });
            
            // 监听按钮点击事件（处理onclick跳转的按钮）
            $(document).on('click', 'button[onclick], input[onclick]', function(e) {
                var $button = $(this);
                var onclick = $button.attr('onclick');
                
                // 检查onclick是否包含页面跳转
                if (self.shouldShowLoadingForButton($button, onclick)) {
                    setTimeout(function() {
                        self.show();
                    }, 100);
                }
            });
            
            // 监听表单提交事件（可选）
            $(document).on('submit', 'form', function(e) {
                var $form = $(this);
                
                // 排除 AJAX 表单
                if (!$form.hasClass('no-loading') && !$form.hasClass('ajax-form')) {
                    setTimeout(function() {
                        self.show();
                    }, 100);
                }
            });
            
            // 页面 beforeunload 事件时也关闭 loading（清理）
            $(window).on('beforeunload', function() {
                self.close();
            });
        }
    };
    
    // 页面就绪时自动初始化
    $(document).ready(function() {
        // 确保 layer 已加载
        if (typeof layer !== 'undefined') {
            GlobalLoading.init();
        } else {
            // 如果 layer 还没加载，等待一下
            setTimeout(function() {
                if (typeof layer !== 'undefined') {
                    GlobalLoading.init();
                }
            }, 500);
        }
    });
    
})();