<?php

namespace addons\VymCloud\common\models;

use common\behaviors\MerchantStoreBehavior;
use common\models\merchant\Merchant;
use Yii;

/**
 * This is the model class for table "{{%addon_cloud_system}}".
 *
 * @property int         $id            ID
 * @property int         $pid           父节点ID
 * @property int|null    $merchant_id   商户id
 * @property int|null    $store_id      店铺id
 * @property string      $title         系统名称
 * @property string      $name          文件名称
 * @property string      $path          文件路径
 * @property string|null $cluster_ids   绑定集群
 * @property string|null $mark          系统标识
 * @property string|null $type          系统类型
 * @property int|null    $state         状态
 * @property int|null    $sort          排序
 * @property string|null $created_at
 * @property string|null $updated_at
 */
class CloudSystem extends \yii\db\ActiveRecord
{
    use MerchantStoreBehavior;

    const STATUS_ENABLED  = 1;
    const STATUS_DISABLED = 0;
    const OS_TYPE_WINDOWS = 'windows';
    const OS_TYPE_CENTOS  = 'centos';
    const OS_TYPE_UBUNTU  = 'ubuntu';
    const OS_TYPE_DEBIAN  = 'debian';
    const OS_TYPE_FEDORA  = 'fedora';


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%addon_cloud_system}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['created_at', 'updated_at', 'cluster_ids'], 'default', 'value' => null],
            [['pid'], 'default', 'value' => 0],
            [['merchant_id', 'store_id'], 'default', 'value' => 0],
            [['state'], 'default', 'value' => 1],
            [['pid', 'sort', 'state'], 'integer'],
            [['name', 'title', 'state', 'pid', 'type', 'mark'], 'required'],
            [['created_at', 'updated_at'], 'safe'],
            [['name'], 'string', 'max' => 100],
            [['mark', 'type'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'          => 'ID',
            'pid'         => '父节点ID',
            'merchant_id' => '商户ID',
            'store_id'    => '店铺ID',
            'title'       => '系统名称',
            'name'        => '文件名称',
            'path'        => '文件路径',
            'cluster_ids' => '绑定集群',
            'mark'        => '系统标识',
            'type'        => '系统类型',
            'state'       => '状态',
            'sort'        => '排序',
            'created_at'  => 'Created At',
            'updated_at'  => 'Updated At',
        ];
    }


    /**
     * 获取操作系统类型列表
     *
     * @return array|string
     */
    public static function getOsTypeMap($osType = null)
    {
        $osTypeArr = [
            self::OS_TYPE_WINDOWS => 'Windows',
            self::OS_TYPE_CENTOS  => 'Centos',
            self::OS_TYPE_UBUNTU  => 'Ubuntu',
            self::OS_TYPE_DEBIAN  => 'Debian',
            self::OS_TYPE_FEDORA  => 'Fedora',
        ];
        if ($osType === null) {
            return $osTypeArr;
        }
        return $osTypeArr[$osType] ?? '未知';
    }

    /**
     * 关联商户
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }
}
