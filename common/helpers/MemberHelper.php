<?php

namespace common\helpers;

use common\enums\MemberTypeEnum;

/**
 * Class MemberHelper
 * @package common\helpers
 * <AUTHOR> <<EMAIL>>
 */
class MemberHelper
{
    const ONE = 'one';
    const TWO = 'two';

    /**
     * @param $member
     * @return string
     */
    public static function html($member, $style = self::ONE)
    {
        if (empty($member)) {
            return '';
        }

        $url = Url::toRoute(['/member/member/view', 'id' => $member->id]);
        switch ($member['type']) {
            case MemberTypeEnum::MEMBER :
                $name = '昵称: '.Html::encode($member['nickname']);
                $hideName = Html::encode(StringHelper::textNewLine($member->nickname, 6, 1)[0]);
                break;
            default;
                $name = '账号: '.Html::encode($member['username']);
                $hideName = Html::encode(StringHelper::textNewLine($member->username, 6, 1)[0]);
                break;
        }

        switch ($style) {
            default :
                $head_portrait = Html::img(ImageHelper::defaultHeaderPortrait($member->head_portrait), [
                    'class' => 'img-circle elevation-1',
                    'width' => '35',
                    'height' => '35',
                ]);

                $toggle = [];
                $toggle[] = 'ID: '.$member->id;
                $toggle[] = $name;
                $toggle[] = '手机: '.(!empty($member['mobile']) ? StringHelper::hideStr($member['mobile'], 3, 4) : '-');
                $toggle = "<div class='text-left'>".implode('<br>', $toggle)."</div>";

                return '<div class="text-center openIframeView" href="'.$url.'">'.$head_portrait.'<a class="users-list-name pt-1" data-toggle="tooltip" data-placement="bottom" data-html="true" title="'.$toggle.'" href="javascript: void(0)">'.$hideName.'</a></div>';
            case self::TWO :
                $array = [];
                $array[] = 'ID: '.$member['id'];
                $array[] = $name;
                $array[] = '手机: '.(!empty($member['mobile']) ? StringHelper::hideStr($member['mobile'], 3, 4) : '-');

                return implode('<br>', $array);
        }
    }

    /**
     * @param $searchModel
     * @param $model
     * @return array
     */
    public static function gridView(
        $searchModel,
        $label = '用户',
        $attribute = 'member_id',
        $relevancy = 'member',
        $default = '游客',
        $style = 'one'
    ) {
        return [
            'label' => $label,
            'attribute' => $attribute,
            'headerOptions' => ['class' => 'col-md-1 text-align-center'],
            'contentOptions' => ['class' => 'text-align-center'],
            'filter' => Html::activeTextInput($searchModel, $attribute, [
                    'class' => 'form-control',
                    'placeholder' => '用户 ID',
                ]
            ),
            'value' => function ($model) use ($relevancy, $default, $style) {
                if (empty($model->$relevancy)) {
                    return $default;
                }

                return self::html($model->$relevancy, $style);
            },
            'format' => 'raw',
        ];
    }

    public static function userInfo($userMember, $member = null){
        $username = $userMember->username??$userMember->uname;
        $email = $userMember->email;
        if(!$username && $member){
            $username = $member->username;
        }
        if(!$email && $member){
            $email = $member->email;
        }
        return [$username, $email];
    }

    public static function userAccount($user, $nullUseUname = true){
        $userEmail = ArrayHelper::getValue($user, 'email');
        if(!empty($userEmail)){
            return $userEmail;
        }
        $username = ArrayHelper::getValue($user, 'username');
        $mobile = ArrayHelper::getValue($user, 'mobile');
        if(!ArrayHelper::keyExists('member', $user) || ArrayHelper::getValue($user, 'member') === null){
            if(!empty($mobile)){
                return $mobile;
            }
            if(!empty($username) && $nullUseUname){
                return $username;
            }
            return null;
        }
        $email = ArrayHelper::getValue($user, 'member.email');
        if(!empty($email)){
            return $email;
        }
        $username = ArrayHelper::getValue($user, 'member.username');
        if($username){
            return $username;
        }
        return ArrayHelper::getValue($user, 'member.mobile');
    }

    public static function userNick($user){
        $uname = ArrayHelper::getValue($user, 'uname');
        if(!empty($uname)){
            return $uname;
        }
        $username = ArrayHelper::getValue($user, 'username');
        if(!empty($username)){
            return $username;
        }
        if(!ArrayHelper::keyExists('member', $user)){
            return null;
        }
        return ArrayHelper::getValue($user, 'member.username');
    }
}
