<?php

namespace addons\VymCloud\merchant\controllers;

use addons\VymCloud\common\models\CloudCluster;
use common\models\base\SearchModel;
use Yii;
use yii\db\Exception;
use yii\db\StaleObjectException;
use yii\web\Response;

/**
 * ClusterController implements the CRUD actions for CloudCluster model.
 */
class ClusterController extends BaseController
{

    /**
     * @var CloudCluster
     */
    public $modelClass = CloudCluster::class;


    /**
     * 首页
     *
     * @return string
     * @throws \yii\web\NotFoundHttpException
     */
    public function actionIndex()
    {
        $searchModel = new SearchModel([
            'model'                  => $this->modelClass,
            'scenario'               => 'default',
            'partialMatchAttributes' => ['name'], // 模糊查询
            'pageSize'               => $this->pageSize,
            'defaultOrder'           => [
                'sort' => SORT_ASC,
                'id'   => SORT_DESC,
            ],
        ]);

        $dataProvider = $searchModel->search($this->request->queryParams);
        // $dataProvider->query->andWhere(['status' => StatusEnum::ENABLED]);
        $this->addQueryParam($dataProvider);
        return $this->render($this->action->id, [
            'dataProvider' => $dataProvider,
            'searchModel'  => $searchModel,
            'areaList'     => Yii::$app->vymCloudService->area->getMapList(),
        ]);
    }


    /**
     * 新增/编辑
     *
     * @param int|null $id 套餐ID
     *
     * @return string|Response
     * @throws Exception
     */
    public function actionEdit(int $id = null)
    {
        $model = $this->findModel($id);
        if ($this->request->isPost && $model->load($this->request->post())) {
            return $model->save()
                ? $this->message('操作成功', $this->redirect($this->request->referrer))
                : $this->message($this->getError($model), $this->redirect($this->request->referrer), 'error');
        }

        return $this->render($this->action->id, [
            'model'    => $model,
            'areaList' => Yii::$app->vymCloudService->area->getDropDownForEdit(),
        ]);
    }


    /**
     * 删除
     *
     * @param $id
     *
     * @return mixed
     * @throws StaleObjectException|\Throwable
     */
    public function actionDelete($id)
    {
        if ($this->findModel($id)->delete()) {
            return $this->message("删除成功", $this->redirect($this->request->referrer));
        }

        return $this->message("删除失败", $this->redirect($this->request->referrer), 'error');
    }
}
