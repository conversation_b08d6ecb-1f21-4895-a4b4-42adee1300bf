<?php

namespace addons\VymDesen\common\components;

use Yii;
use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\models\Member\MemberPdt;
use addons\VymDesen\common\models\NewTrade\TradeMain;
use addons\VymDesen\common\models\NewTrade\TradeDetail;
use addons\VymDesen\backend\models\UserAdmin\UserAdmin;
use addons\VymDesen\common\models\UserMember\UserMember;
use addons\VymDesen\backend\models\WorkFlow\WorkFlow;
use addons\VymDesen\backend\models\WorkFlow\WorkFlowRole;
use addons\VymDesen\backend\models\WorkFlow\WorkFlowDetail;
use addons\VymDesen\backend\models\PaymentAccount;
use addons\VymDesen\backend\models\PdtHaveuselog;
use addons\VymDesen\backend\models\PdtProvideruselog;
use addons\VymDesen\backend\models\IdlePdt;
use addons\VymDesen\backend\models\Provider;
use addons\VymDesen\backend\models\PdtRefund;
use addons\VymDesen\backend\models\TestServer;
use addons\VymDesen\common\models\Member\InitialAccount;
use addons\VymDesen\backend\models\Cost;
use addons\VymDesen\common\models\Pdt\PdtIp;
use addons\VymDesen\common\components\FinanceStatment;
use addons\VymDesen\common\components\PrePay;
use addons\VymDesen\common\components\RevenueCount;
use addons\VymDesen\common\components\FundFlowRecords;
use addons\VymDesen\common\components\RevenueNotes;
use addons\VymDesen\common\components\OutPut;
use addons\VymDesen\common\models\Member\BusinessDeleteRecord;
use addons\VymDesen\backend\models\IpManagement\SupplierIp;
use addons\VymDesen\common\models\Pdt\PdtManage;
use addons\VymDesen\common\models\Pdt\PdtManageType;

use addons\VymDesen\backend\models\BillManage;
use addons\VymDesen\backend\models\BillManageMain;

use addons\VymDesen\common\models\Pdt\SwitchLine;

error_reporting(E_ALL);

class WorkFlowHandle {
	
	/**
	* 	$flow_id 流程号  $status 流程状态
	**/
	#新购处理模块
	public static function ModelPurchase($flow_id, $status) {
		if(!$flow_id || !$status) {
			$data = ['status' => 0, 'info' => '缺少必要参数', 'data' => ''];
			return $data;
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$IdlePdtModel = new IdlePdt();
		$MemberPdtModel = new MemberPdt();
		$UserMemberModel = new UserMember();
		$TestServerModel = new TestServer();
		$SupplierIpModel = new SupplierIp();
		$PdtManageTypeModel = new PdtManageType();
		$PdtManageModel = new PdtManage();
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->all();
		
		$time = time();
			
		#查询用户信息
		$UserInfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
		
		#开启事务
		$transaction = Yii::$app->db->beginTransaction();
		
		
		if($status == '处理完成') 
		{
			$costTemp = [
				'unionid' => null,
				'basic_money' => null,
				'two_money' => null,
				'currency_type' => null,
				'change_time' => null,
				'admin_id' => null,
			]; 
			
			$accountTemp = [
				'unionid' => '',
				'name' => '',
				'pwd' => '',
				'port' => '',
				'create_time' => '',
			];
			
			$insertCost = [];
			$insertAccount = [];
			
			foreach($WorkFlowDetailRes as $key => $val) {
				$frontconfig = json_decode($val['flow_front_config'], true);
				$afterconfig = json_decode($val['flow_after_config'], true);
				
				#自有：新增memberpdt，修改Idle, 新增cost， 新增initial_account
				#供应商：新增memberpdt， 新增cost，新增initial_account
				
				$cost = $costTemp;
				$account = $accountTemp;
				
				#修改机器状态为 1
				$updateMemberPdtRes = Yii::$app->db->createCommand("update member_pdt set status = '1' where unionid= '".$val['flow_unionid']."'")->execute();
				
				if(!$updateMemberPdtRes) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '更新业务机器缓存出现异常', 'data' => ''];
					return $data;
				}
				
				#如果是测试机，把测试机状态改为正式服务器
				if(isset($afterconfig['test_id']) && $afterconfig['test_id'] != '') 
				{	
					$TestServerQuery = $TestServerModel->find()->where(['id' => $afterconfig['test_id']])->one();
					$TestServerQuery->status = '1';
					$TestServerQuery->end_time = time();
					$updateTestServerRes = $TestServerQuery->update();
					#$updateTestServerRes = Yii::$app->db->createCommand("update test_server set status = '1' where id= '".$afterconfig['test_id']."'")->execute();
					if(!$updateTestServerRes) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '更新测试机出现异常', 'data' => ''];
						return $data;
					}
					#如果测试机为自有
					if($TestServerQuery->servicerprovider == 0) 
					{
						$IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $TestServerQuery->idle_id])->one();
						
						$IdlePdtQuery->attribute_id = 2;
						$IdlePdtQuery->status = 1;
						$idle_config = json_decode($IdlePdtQuery->config,true);
						$idle_config['requirement_bandwidth'] = $afterconfig['config']['requirement_bandwidth'];
						$IdlePdtQuery->config = json_encode($idle_config, JSON_UNESCAPED_UNICODE);
						$updateIdlePdtRes = $IdlePdtQuery->update();
						if(!$updateIdlePdtRes) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '更新自有机器出现异常', 'data' => ''];
							return $data;
						}
					} else {
						#如果不是自有，为供应商机器，将供应商的IP状态更新
						$updateip_res = $SupplierIpModel->updateAll(['status' =>1,'update_time' => time()],['ip' => $afterconfig['ip2']]);
					}
				} else {
					#如果不是测试机,且为自有的
					if(isset($afterconfig['idle_id']) && $afterconfig['idle_id'] != '') 
					{
						$IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $afterconfig['idle_id']])->one();
						$IdlePdtQuery->attribute_id = 2;
						$IdlePdtQuery->status = 1;
						$idle_config = json_decode($IdlePdtQuery->config,true);
						$idle_config['requirement_bandwidth'] = $afterconfig['config']['requirement_bandwidth'];
						$IdlePdtQuery->config = json_encode($idle_config, JSON_UNESCAPED_UNICODE);
						$updateIdlePdtRes = $IdlePdtQuery->update();
						if(!$updateIdlePdtRes) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '更新自有机器出现异常', 'data' => ''];
							return $data;
						}
					} else {
						#如果不是自有，为供应商机器，将供应商的IP状态更新
						$updateip_res = $SupplierIpModel->updateAll(['status' =>1,'update_time' => time()],['ip' => $afterconfig['ip2']]);
					}
				}
				
				#成本
				$cost['unionid'] = $val['flow_unionid'];
				$cost['basic_money'] = $val['flow_cost_basic'];
				$cost['two_money'] = $val['flow_cost_parts'];
				$cost['currency_type'] = $val['flow_cost_current'];
				$cost['change_time'] = $time;
				$cost['admin_id'] = $val['flow_cost_settor_id'];
				
				$insertCost[] = array_values($cost);
				
				#账户
			
				$account['unionid'] = $val['flow_unionid'];
				$account['name'] = $afterconfig['install_account'];
				$account['pwd'] = $afterconfig['install_pass'];
				$account['port'] = $afterconfig['install_port'];
				$account['create_time'] = $time;
				
				$insertAccount[] = array_values($account);
				
			}
			
			#cost
			$cost_title = array_keys($costTemp);
			$insertCostRes = Yii::$app->db->createCommand()->batchInsert(Cost::tableName(), $cost_title, $insertCost)->execute();
			
			#account
			$account_title = array_keys($accountTemp);
			$insertAccountRes = Yii::$app->db->createCommand()->batchInsert(InitialAccount::tableName(), $account_title, $insertAccount)->execute();
			
			if($WorkFlowRes['flow_name'] == '新购-线下打款') {
				$moneyInfo = json_decode($WorkFlowRes['flow_money_info'], true);
				
				#如果打款金额大于补款金额，要多余金额预存到用户余额
				if($WorkFlowRes['flow_total_money'] < $moneyInfo['money_num']) {
					$updateUserRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->one();
					#获取退款前的用户余额数
					$before_transaction_money = $updateUserRes->balance;
					$payment_amount = $moneyInfo['money_num'] - $WorkFlowRes['flow_total_money'];
					#
					$updateUserRes->balance = $updateUserRes->balance + $moneyInfo['money_num'] - $WorkFlowRes['flow_total_money'];
					if(!$updateUserRes->save()) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '更新用户余额出现异常', 'data' => ''];
						return $data;
					}
					
					#新增资金流记录
					$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
					$Paymentinfo = [
						'transaction_mode' => '余额加款',
						'transaction_channel' => '用户余额'
					];
					$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $payment_amount, $before_transaction_money, $Paymentinfo, $userinfo);
					if( !$addReFundFlowRecords['status']) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
						return $data;
					}						
				}
				
				#因为在线支付和余额支付在订单支付时就已经增加记录了，所以这里只判断增加线下付款的
				#新增用户资金流动记录
				$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
				#获取退款前的用户余额数
				$before_transaction_money = $userinfo['balance'];
				$Paymentinfo = [
					'transaction_mode' => '线下打款',
					'transaction_channel' => $moneyInfo['money_platform']
				];
				$addReFundFlowRecords = FundFlowRecords::WorkFlow_Expenditure_Amount_Record($flow_id, $WorkFlowRes['flow_total_money'], $before_transaction_money, $Paymentinfo, $userinfo);
				if( !$addReFundFlowRecords['status']) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
					return $data;
				}
			}
			
			#账单开始
			$BillRes = self::NewBill($flow_id);
			if( $BillRes['status'] != 1)
			{
				$transaction->rollBack();
				$data = ['status' => 0, 'info' => $BillRes['info'], 'data' => ''];
				return $data;
			}
			#账单end
			
			
			
			#财务流水记录，新购一定会有记录
			$FinanceRes = FinanceStatment::Report_Simple($flow_id);
			#用户资金流记录
			
			if($insertCostRes && $insertAccountRes && $FinanceRes['status'] == 1) {

				$prepay = PrePay::PrePay_Purchase_Renew($flow_id);
				$revenue = RevenueCount::RevenueRecord($flow_id);
				$note = RevenueNotes::NoteFinish($flow_id);
				$output = OutPut::OrderPayPut($flow_id);
				
				if($prepay['status'] && $revenue['status'] && $note['status'] && $output['status']) {
					$transaction->commit();
					$data = ['status' => 1, 'info' => '更新数据完成', 'data' => ''];
					return $data;
				} else {
					$transaction->rollBack();
					$returnInfo = $prepay['status'] == 0 ? $prepay['info'] : $revenue['info'];
					$data = ['status' => 0, 'info' => '预付款、收支或数据明细出现异常', 'data' => ''];
					return $data;
				}
				
			} else {
				$transaction->rollBack();
				$data = ['status' => 0, 'info' => '更新数据出现异常', 'data' => ''];
				return $data;
			}
		} else {
			#中止流程完成，处理驳回

			#判断金额：如果为通过，那么则要退款，如果金额审核驳回，那么没有收到款项则不退款
			#只限于在线支付，余额支付退款
			if( in_array($WorkFlowRes['flow_name'], ['新购-在线支付','新购-余额支付']) ) {
			#start
				if($WorkFlowRes['flow_money_audit_status'] == '通过') {
					#查看退款
					$moneyInfo = json_decode($WorkFlowRes['flow_money_info'], true);
					if(!isset($moneyInfo['stop_refund'])) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '缺少退款信息', 'data' => ''];
						return $data;
					}
					
					#退款到用户账户余额
					if($moneyInfo['stop_refund']['type'] == 'balance') {
						$userUpdateRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->one();
						#退款金额
						$payment_amount = $moneyInfo['stop_refund']['money'];
						$before_transaction_money = $userUpdateRes->balance;
						#更新
						$userUpdateRes->balance += $moneyInfo['stop_refund']['money'];						
						if(!$userUpdateRes->save()) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '更新退款信息异常', 'data' => ''];
							return $data;
						}
						
						#新增资金流记录
						$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();						
						$Paymentinfo = [
							'transaction_type' => '新购取消',
							'transaction_mode' => '余额加款',
							'transaction_channel' => '用户余额'
						];
						$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $payment_amount, $before_transaction_money, $Paymentinfo, $userinfo);
						if( !$addReFundFlowRecords['status']) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
							return $data;
						}						
					} else {
						#这是退款到用户卡或者其他方式 #新增资金流记录						
						$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
						$before_transaction_money = $userinfo['balance'];
						$payment_amount = $moneyInfo['stop_refund']['money'];
						$Paymentinfo = [
							'transaction_type' => '新购取消',
							'transaction_mode' => '线下打款',
							'transaction_channel' => $moneyInfo['stop_refund']['platform']
						];
						$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $payment_amount, $before_transaction_money, $Paymentinfo, $userinfo);
						if( !$addReFundFlowRecords['status']) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
							return $data;
						}
					}
					
				}
				
				if($WorkFlowRes['flow_money_audit_status'] == '无需审核') {
					#查看退款
					$moneyInfo = json_decode($WorkFlowRes['flow_money_info'], true);
					if(isset($moneyInfo['stop_refund'])) {
						#退款到用户账户余额
						if($moneyInfo['stop_refund']['type'] == 'balance') {
							$userUpdateRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->one();
							#退款前的金额和退款金额
							$payment_amount = $moneyInfo['stop_refund']['money'];
							$before_transaction_money = $userUpdateRes->balance;
							#更新
							$userUpdateRes->balance += $moneyInfo['stop_refund']['money'];
							if(!$userUpdateRes->save()) {
								$transaction->rollBack();
								$data = ['status' => 0, 'info' => '更新退款信息异常', 'data' => ''];
								return $data;
							}
							
							#新增资金流记录
							$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();						
							$Paymentinfo = [
								'transaction_type' => '新购取消',
								'transaction_mode' => '余额加款',
								'transaction_channel' => '用户余额'
							];
							$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $payment_amount, $before_transaction_money, $Paymentinfo, $userinfo);
							if( !$addReFundFlowRecords['status']) {
								$transaction->rollBack();
								$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
								return $data;
							}		
						}
					}
				}
			#end
			}
			
			
			#中止流程完成 和 处理驳回 两种状态，要把机器回档
			
			foreach($WorkFlowDetailRes as $key => $val) {
				
				$afterconfig = json_decode($val['flow_after_config'], true);
				
				#删除member_pdt缓存记录
				$deleteMemberPdtRes = Yii::$app->db->createCommand("delete from member_pdt where unionid = '".$val['flow_unionid']."'")->execute();
				if(!$deleteMemberPdtRes) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '更新测试机出现异常', 'data' => ''];
					return $data;
				}
				
				
				#判断是不是测试机
				if(isset($afterconfig['test_id']) && $afterconfig['test_id'] != '') {
					#测试机，直接改变测试机状态
					$updateTestServerRes = Yii::$app->db->createCommand("update test_server set status = '0' where id= '".$afterconfig['test_id']."'")->execute();
					if(!$updateTestServerRes) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '更新测试机出现异常', 'data' => ''];
						return $data;
					}
				} else {
					#不是测试机，判断自有 还是供应商
					if(isset($afterconfig['idle_id']) && $afterconfig['idle_id'] != '') {
						#自有服务器
						$IdleRes = $IdlePdtModel->find()->where(['id' => $afterconfig['idle_id']])->one();
						#修改idle_pdt表
						$IdleRes->attribute_id = 1;
						$IdleRes->status = 0;
						if(!$IdleRes->save()) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '更新自有库数据出现异常', 'data' => ''];
							return $data;
						}
					} else {
						#不是测试机且为供应商机器，需要将IP从供应商IP库中删除掉
						if( !empty($afterconfig['ip2'])) {
							$deleteip_res = $SupplierIpModel->deleteAll(['ip' => $afterconfig['ip2']]);
						}
						
					}
				}
			}
			
			#如果为后付款。取消订单
			#同时如果为后付款 。将订单取消
			if( $WorkFlowRes['flow_name'] == '新购-后付款') {
				$TradeMainModel = new TradeMain();
				$TradeMainQuery = $TradeMainModel->find()->where(['trade_orderid' => $WorkFlowRes['flow_orderid']])->one();
				$TradeMainQuery->trade_status = '已取消';
				$updateTrade = $TradeMainQuery->update(false);
			} else {
				$updateTrade = true;
			}
			if( $updateTrade ) {
				$note = RevenueNotes::NoteFinish($flow_id);
				if($note['status']) {
					$transaction->commit();
					$data = ['status' => 1, 'info' => '更新数据完成', 'data' => ''];
					return $data;
				} else {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '更新订单失败', 'data' => ''];
					return $data;
				}
				
			} else {
				$transaction->rollBack();
				$data = ['status' => 0, 'info' => '更新订单失败', 'data' => ''];
				return $data;
			}
			
		}
	}
	
	#续费流程
	public static function ModelRenew($flow_id, $status) {
		if(!$flow_id) {
			$data = ['status' => 0, 'info' => '缺少必要参数', 'data' => ''];
			return $data;
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$IdlePdtModel = new IdlePdt();
		$MemberPdtModel = new MemberPdt();
		$UserMemberModel = new UserMember();
		$PdtManageModel = new PdtManage();
		$PdtManageTypeModel = new PdtManageType();
		
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->all();
		
		$time = time();
			
		#查询用户信息
		$UserInfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
		
		$moneyInfo = json_decode($WorkFlowRes['flow_money_info'], true);
		
		if( $status == '处理完成') {
			if($WorkFlowRes['flow_name'] == '续费-余额支付') 
			{
				$isAllSuccess = true;
				if( isset($moneyInfo['is_paylater']) && $moneyInfo['is_paylater']  == 1 ) {
					$FinanceSuccess = 1;
				} else {
					foreach($WorkFlowDetailRes as $key => $val) {
						
						$updateTime = $WorkFlowDetailModel->find()->where(['id' => $val['id']])->one();
						
						$frontConfig = json_decode($val['flow_front_config'], true);
						$afterConfig = json_decode($val['flow_after_config'], true);
						$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $val['flow_unionid']])->andWhere('status != -1')->one();
						if( empty($MemberPdtRes)) {
							$data = ['status' => 0, 'info' => '业务ID：'.$val['flow_unionid'].'的用户机器不存在或已被删除', 'data' => ''];
							return $data;
							break;
						}
						$frontConfig['report_old_time'] = $MemberPdtRes->end_time;
						#获取续费的总约束（续费周期乘以续费数量）
						$month_num = $afterConfig['payment_cycle'] * $afterConfig['renew_num'];
						$MemberPdtRes->end_time = DataHelper::getRenewEndtime($MemberPdtRes->end_time, $month_num);
						
						$afterConfig['report_new_time'] = $MemberPdtRes->end_time;
						$updateTime->flow_front_config = json_encode($frontConfig, JSON_UNESCAPED_UNICODE);
						$updateTime->flow_after_config = json_encode($afterConfig, JSON_UNESCAPED_UNICODE);
						
						if(!$MemberPdtRes->save() || !$updateTime->save()) {
							$isAllSuccess = false;
							break;
						}
					}
					
					#财务流水记录，新购一定会有记录
					$FinanceRes = FinanceStatment::Report_Simple($flow_id);
					$FinanceSuccess = $FinanceRes['status'];
				}
				
				$note = RevenueNotes::NoteFinish($flow_id);
				$prepay = PrePay::PrePay_Purchase_Renew($flow_id);
				$output = OutPut::OrderPayPut($flow_id);
				if($isAllSuccess && $FinanceSuccess && $note['status'] && $prepay['status'] && $output['status']) {
					$data = ['status' => 1, 'info' => '更新数据完成', 'data' => ''];
					return $data;
				} else {
					$data = ['status' => 0, 'info' => '更新数据异常', 'data' => ''];
					return $data;
				}
				
			} else {
				
				#开启事务
				$transaction = Yii::$app->db->beginTransaction();
				#
				if($WorkFlowRes['flow_name'] == '续费-线下打款') {
					if($WorkFlowRes['flow_total_money'] < $moneyInfo['money_num']) {
						$updateUserRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->one();
						#获取退款前的用户余额数
						$before_transaction_money = $updateUserRes->balance;
						#加款金额
						$payment_amount = $moneyInfo['money_num'] - $WorkFlowRes['flow_total_money'];
						#更新用户金额
						$updateUserRes->balance = $updateUserRes->balance + $moneyInfo['money_num'] - $WorkFlowRes['flow_total_money'];
						if(!$updateUserRes->save()) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '更新用户余额出现异常', 'data' => ''];
							return $data;
						}						
						#退款完毕，新增用户资金流动记录						
						$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();						
						$Paymentinfo = [
							'transaction_type' => '预存款项',
							'transaction_mode' => '线下打款',
							'transaction_channel' => $moneyInfo['money_platform']
						];
						$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $payment_amount, $before_transaction_money, $Paymentinfo, $userinfo);
						if( !$addReFundFlowRecords['status']) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
							return $data;
						}						
					}
					
					#因为在线支付和余额支付在订单支付时就已经增加记录了，所以这里只判断增加线下付款的
					#新增用户资金流动记录
					$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
					#获取退款前的用户余额数
					$before_transaction_money = $userinfo['balance'];
					$Paymentinfo = [
						'transaction_mode' => '线下打款',
						'transaction_channel' => $moneyInfo['money_platform']
					];
					$addReFundFlowRecords = FundFlowRecords::WorkFlow_Expenditure_Amount_Record($flow_id, $WorkFlowRes['flow_total_money'], $before_transaction_money, $Paymentinfo, $userinfo);
					if( !$addReFundFlowRecords['status']) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
						return $data;
					}
				}
				
				$isAllSuccess = true;
				#判断是否为后付款
				if( isset($moneyInfo['is_paylater']) && $moneyInfo['is_paylater']  == 1 ) {
					$FinanceSuccess = 1;
				} else {
					foreach($WorkFlowDetailRes as $key => $val) {
						$updateTime = $WorkFlowDetailModel->find()->where(['id' => $val['id']])->one();
						
						$frontConfig = json_decode($val['flow_front_config'], true);
						$afterConfig = json_decode($val['flow_after_config'], true);
						$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $val['flow_unionid']])->andWhere('status != -1')->one();
						if( empty($MemberPdtRes)) {
							$data = ['status' => 0, 'info' => '业务ID：'.$val['flow_unionid'].'的用户机器不存在或已被删除', 'data' => ''];
							return $data;
							break;
						}
						
						$frontConfig['report_old_time'] = $MemberPdtRes->end_time;
						#获取续费的总约束（续费周期乘以续费数量）
						$month_num = $afterConfig['payment_cycle'] * $afterConfig['renew_num'];				
						$MemberPdtRes->end_time = DataHelper::getRenewEndtime($MemberPdtRes->end_time, $month_num);
						
						$afterConfig['report_new_time'] = $MemberPdtRes->end_time;
						$updateTime->flow_front_config = json_encode($frontConfig, JSON_UNESCAPED_UNICODE);
						$updateTime->flow_after_config = json_encode($afterConfig, JSON_UNESCAPED_UNICODE);
						
						if(!$MemberPdtRes->save() || !$updateTime->save()) {
							$isAllSuccess = false;
							break;
						}
					}
					
					#财务流水记录，新购一定会有记录
					$FinanceRes = FinanceStatment::Report_Simple($flow_id);
					$FinanceSuccess = $FinanceRes['status'];
				}
				#账单开始
				$BillRes = self::NewBill($flow_id);
				if( $BillRes['status'] != 1)
				{
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => $BillRes['info'], 'data' => ''];
					return $data;
				}
				#账单end
				
				if($isAllSuccess && $FinanceSuccess) {
					$prepay = PrePay::PrePay_Purchase_Renew($flow_id);
					$revenue = RevenueCount::RevenueRecord($flow_id);
					$note = RevenueNotes::NoteFinish($flow_id);
					$output = OutPut::OrderPayPut($flow_id);
					if($prepay['status'] && $revenue['status'] && $note['status'] && $output['status']) {
						
						$transaction->commit();
						$data = ['status' => 1, 'info' => '更新数据完成', 'data' => ''];
						return $data;
					} else {						
						$transaction->rollBack();
						//return ['status' => 0, 'info' => '出现异常', 'data' => ''];
						$returnInfo = $prepay['status'] == 0 ? $prepay['info'] : $revenue['info'];
						$data = ['status' => 0, 'info' => $returnInfo, 'data' => ''];
						return $data;
					}
				} else {
					
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '更新数据异常', 'data' => ''];
					return $data;
				}
			}
			
		} else {
			#驳回或者中止
			#续费  在线支付和余额支付都为直接完成，所有没有中止
			#线下打款和后付款 中止不退款
			#开启事务
			$transaction = Yii::$app->db->beginTransaction();
			
			/*#判断金额：如果为通过，那么则要退款，如果金额审核驳回，那么没有收到款项则不退款
			if( in_array($WorkFlowRes['flow_money_audit_status'], ['通过', '无需审核'])) {
				#查看退款
				$moneyInfo = json_decode($WorkFlowRes['flow_money_info'], true);
				if(!isset($moneyInfo['stop_refund'])) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '缺少退款信息', 'data' => ''];
					return $data;
				}
				
				#退款到用户账户余额
				if($moneyInfo['stop_refund']['type'] == 'balance') {
					$userUpdateRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->one();
					$userUpdateRes->balance += $moneyInfo['stop_refund']['money'];
					if(!$userUpdateRes->save()) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '更新退款信息异常', 'data' => ''];
						return $data;
					}
				}
			}*/
			
			
			#如果 is_paylater 为 1 代表这个曾经是后付款，因为后付款是加了时间的，所以这里要将加的时间减掉
			$isAllSuccess = true;
			
			if( isset($moneyInfo['is_paylater']) && $moneyInfo['is_paylater'] == 1 ) {
				foreach($WorkFlowDetailRes as $key => $val) {
					$afterConfig = json_decode($val['flow_after_config'], true);
					$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $val['flow_unionid']])->andWhere('status != -1')->one();
					if( empty($MemberPdtRes)) {
						$data = ['status' => 0, 'info' => '业务ID：'.$val['flow_unionid'].'的用户机器不存在或已被删除', 'data' => ''];
						return $data;
						break;
					}
					#$MemberPdtRes->end_time = $afterConfig['new_end_time'];
					#获取续费的总月数（续费周期乘以续费数量）
					$month_num = $afterConfig['payment_cycle'] * $afterConfig['renew_num'];				
					$MemberPdtRes->end_time = DataHelper::getRecoveryEndtime($MemberPdtRes->end_time, $month_num);
					
					if(!$MemberPdtRes->save()) {
						$isAllSuccess = false;
						break;
					}
				}
			} else {
				
			}
			
			#同时如果为后付款 。将订单取消
			if( $WorkFlowRes['flow_name'] == '续费-后付款') {
				$TradeMainModel = new TradeMain();
				$TradeMainQuery = $TradeMainModel->find()->where(['trade_orderid' => $WorkFlowRes['flow_orderid']])->one();
				$TradeMainQuery->trade_status = '已取消';
				$updateTrade = $TradeMainQuery->update(false);
			} else {
				$updateTrade = true;
			}
			
			$note = RevenueNotes::NoteFinish($flow_id);
			
			if($isAllSuccess && $updateTrade && $note['status']) {
				$transaction->commit();
				$data = ['status' => 1, 'info' => '更新数据完成', 'data' => ''];				
			} else {
				$transaction->rollBack();
				$data = ['status' => 0, 'info' => '更新数据异常', 'data' => ''];
			}
			return $data;
		}	
		
	}
	
	#更换机器
	public static function ModelReplaceMachine($flow_id, $status) {
		if(!$flow_id) {
			$data = ['status' => 0, 'info' => '缺少必要参数', 'data' => ''];
			return $data;
		}
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$IdlePdtModel = new IdlePdt();
		$MemberPdtModel = new MemberPdt();
		$UserMemberModel = new UserMember();
		$TestServerModel = new TestServer();
		$PdtIpModel = new PdtIp();
		$SupplierIpModel = new SupplierIp();
		#开启事务
		$transaction = Yii::$app->db->beginTransaction();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one(); #print_r($WorkFlowDetailRes);exit;
		
		$after_config = json_decode($WorkFlowDetailRes['flow_after_config'], true); #变更后
		$front_config = json_decode($WorkFlowDetailRes['flow_front_config'], true); #变更前
		#print_r($after_config);print_r($front_config);exit;
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->asArray()->one(); 
		
		if( isset($after_config['testid']) ) {
			$testid = $after_config['testid'];
		} else {
			$testid = '';
		}
		$provider = $after_config['provider'];
		$unionid = $WorkFlowDetailRes['flow_unionid'];
		#获取用户产品
		$MemberPdtQuery = $MemberPdtModel->find()->where(['unionid' => $WorkFlowDetailRes['flow_unionid']])->one();
		
		if( empty($MemberPdtQuery) ) {
			$transaction->rollBack();
			$data = ['status' => 0, 'info' => '未知的用户产品或已被删除', 'data' => ''];
			return $data;
		}
		#用户对象信息
		$UserMemberQuery = $UserMemberModel->find()->where(['u_id' => $MemberPdtQuery->user_id])->one();

		if( $status == '处理完成' ) 
		{
			$account['name'] = $after_config['install_account'];
			$account['pwd'] = $after_config['install_pass'];
			$account['port'] = $after_config['install_port'];
			
			#如果更换机器选择的为测试机器
			if( isset($testid) && $testid ) 
			{
				$TestServerQuery = $TestServerModel->find()->where(['id' => $after_config['testid']])->one();
				#将测试机器的状态改为正式机器，更新结束时间
				$TestServerQuery->status = 1;
				$TestServerQuery->end_time = time();
				#更新
				if( !$TestServerQuery->update(false) ) { 
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '测试服务器更新失败', 'data' => ''];
					return $data;
				}

				#如果测试服务器 为自有的 或者供应商的。#如果自有的，更新自有机器为使用中，同时将配置同步更新下				
				if( $TestServerQuery->idle_id != "" ) 
				{
					$IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $TestServerQuery->idle_id])->one();
					#将更换的自有机器改为使用状态
					$IdlePdtQuery->status = 1;
					$IdlePdtQuery->attribute_id = 2;
					$IdlePdtQuery->start_time = time();
					$IdlePdtQuery->config = json_encode($after_config['config'], JSON_UNESCAPED_UNICODE);
					
					#如果使用原IP地址
					if( $after_config['use_original_ip'] == 1) {
						#同时更换前机器为自有机器，将更换后自有机器的IP地址绑定到更换前的自有机器上,这里保存IP 等会赋值
						if( $MemberPdtQuery->idle_id != '' && $MemberPdtQuery->servicerprovider == 0) {							
							$set_Old_IdlePdt_ip = $IdlePdtQuery->ip;
							$set_Old_IdlePdt_ip2 = $IdlePdtQuery->ip2;
						}
						
						if( isset($after_config['is_exchangedip']) && $after_config['is_exchangedip'] == 1) { 
							#代表着之前已经对换过IP 就不再对换了
						} else {
							$IdlePdtQuery->ip = json_encode($after_config['ip']);
							$IdlePdtQuery->ip2 = json_encode($after_config['ip2']);
						}
					}
					#同时同步机器登录账户密码
					$IdlePdtQuery->re_username = $account['name'];
					$IdlePdtQuery->re_password = $account['pwd'];
					$IdlePdtQuery->re_port = $account['port'];
					#执行更新
					if( !$IdlePdtQuery->update(false) ) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '自有机器信息更新失败', 'data' => ''];
						return $data;
					}
					#获取即将加入的产品信息
					$afterdata = $IdlePdtQuery->toArray();
					$afterdata['idle_id'] = $IdlePdtQuery->id;
					$afterdata['real_bandwidth'] = $after_config['config']['configbandwidth'];
					$afterdata['bandwidth'] = $after_config['config']['requirement_bandwidth'];
					
					unset($afterdata['id']);
					unset($afterdata['status']);
					unset($afterdata['remarks']);
					unset($afterdata['update_time']);
					unset($afterdata['start_time']);
					unset($afterdata['sn_code']);
					unset($afterdata['port_config']);
					unset($afterdata['test_server_admin']);
					unset($afterdata['test_server_remarks']);
					unset($afterdata['power_status']);
					
				} else {
					#如果是供应商的，无需做操作（）
					$afterdata = $TestServerQuery->toArray();
					$afterdata['config'] = json_encode($after_config['config'], JSON_UNESCAPED_UNICODE);
					$afterdata['real_bandwidth'] = $after_config['config']['configbandwidth'];
					$afterdata['bandwidth'] = $after_config['config']['requirement_bandwidth'];
					
					
					#如果使用原IP地址
					if( $after_config['use_original_ip'] == 1) 
					{
						#将测试机中的供应商IP从库中删除掉
						$deleteip_res = $SupplierIpModel->deleteAll(['ip' => json_decode($TestServerQuery->ip2, true)]);
						#赋值
						$afterdata['ip'] = json_encode($after_config['ip']);
						$afterdata['ip2'] = json_encode($after_config['ip2']);
					} else {
						#如果不使用原IP。将测试机中的供应商IP 状态改为使用
						$updateip_res = $SupplierIpModel->updateAll(['status' =>1,'update_time' => time()],['ip' => json_decode($TestServerQuery->ip2, true)]);
						#同时如果之前产品为供应商机器，将产品上的IP从供应商IP库中删除
						if($MemberPdtQuery->servicerprovider == 1) 
						{
							$deleteip_res = $SupplierIpModel->deleteAll(['ip' => json_decode($MemberPdtQuery->ip2, true)]);
						}
					}
					unset($afterdata['id']);
					unset($afterdata['status']);
					unset($afterdata['server_remarks']);
					unset($afterdata['create_time']);
					unset($afterdata['end_time']);
					unset($afterdata['sales_id']);
					unset($afterdata['user_contact']);
					unset($afterdata['founder_id']);
					unset($afterdata['founder_name']);
				}				
				#print_r($afterdata); print_r($after_config);exit;				
			} else {
				#如果选择的不是测试机器。
				#且选择为自有的
				if( $provider == 0 ) 
				{					
					#将更换的自有机器改为使用状态
					$IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $after_config['idle_id']])->one();
					
					$IdlePdtQuery->status = 1;
					$IdlePdtQuery->attribute_id = 2;
					$IdlePdtQuery->start_time = time();
					$IdlePdtQuery->config = json_encode($after_config['config'], JSON_UNESCAPED_UNICODE);
					
					#如果使用原IP地址
					if( $after_config['use_original_ip'] == 1) {						
						#同时更换前机器为自有机器，将更换后自有机器的IP地址绑定到更换前的自有机器上
						if( $MemberPdtQuery->idle_id != "" ) {
							$set_Old_IdlePdt_ip = $IdlePdtQuery->ip;
							$set_Old_IdlePdt_ip2 = $IdlePdtQuery->ip2;
						}						
						if( isset($after_config['is_exchangedip']) && $after_config['is_exchangedip'] == 1) { 
							#代表着之前已经对换过IP 就不再对换了
						} else {
							$IdlePdtQuery->ip = json_encode($after_config['ip']);
							$IdlePdtQuery->ip2 = json_encode($after_config['ip2']);
						}
					}
					#同时同步机器登录账户密码
					$IdlePdtQuery->re_username = $account['name'];
					$IdlePdtQuery->re_password = $account['pwd'];
					$IdlePdtQuery->re_port = $account['port'];
					#更新
					if( !$IdlePdtQuery->update(false) ) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '自有机器信息更新失败', 'data' => ''];
						return $data;
					}					
					#获取到更换后机器的信息
					$afterdata = $IdlePdtQuery->toArray();					
					$afterdata['idle_id'] = $afterdata['id'];
					$afterdata['real_bandwidth'] = $after_config['config']['configbandwidth'];
					$afterdata['bandwidth'] = $after_config['config']['requirement_bandwidth'];
					
					unset($afterdata['id']);
					unset($afterdata['status']);
					unset($afterdata['remarks']);
					unset($afterdata['update_time']);
					unset($afterdata['start_time']);
					unset($afterdata['sn_code']);
					unset($afterdata['port_config']);
					unset($afterdata['test_server_admin']);
					unset($afterdata['test_server_remarks']);
					unset($afterdata['power_status']);
					#同时如果之前产品为供应商机器，将产品上的IP从供应商IP库中删除
					if($MemberPdtQuery->servicerprovider == 1)
					{
						$deleteip_res = $SupplierIpModel->deleteAll(['ip' => json_decode($MemberPdtQuery->ip2, true)]);
					}
				} else {
					#如果是供应商的
					$afterdata['servicerprovider'] = 1;
					$afterdata['server_type_id'] = $after_config['server_typeid'];
					$afterdata['provider_id'] = $after_config['provider_id'];
					$afterdata['room_id'] = $after_config['room_id'];
					$afterdata['cabinet_id'] = '';					
					$afterdata['pdt_id'] = $after_config['pdt_id'];					
					##[property] => 1
					##[type_id] => 1
					##[attribute_id] => 2
					$afterdata['switch_location'] = '';
					$afterdata['switch_port'] = '';
					$afterdata['occupies_position'] = '';
					$afterdata['ipmi_ip'] = '';
					$afterdata['ipmi_name'] = '';
					$afterdata['ipmi_pwd'] = '';
					
					$afterdata['config'] = json_encode($after_config['config'], JSON_UNESCAPED_UNICODE);
					$afterdata['ip'] = json_encode($after_config['ip']);
					$afterdata['ip2'] = json_encode($after_config['ip2']);

					$afterdata['bandwidth'] = $after_config['config']['requirement_bandwidth'];
					$afterdata['real_bandwidth'] = $after_config['config']['configbandwidth'];					
					$afterdata['idle_id'] = '';
					
					#如果不使用原IP地址
					if( $after_config['use_original_ip'] != 1) 
					{
						$oldIP = json_decode($MemberPdtQuery->ip2, true);						
						$bigIPArray = array_unique(array_merge($after_config['ip2'], $oldIP));
						$return_insert = array_diff($bigIPArray, $oldIP);
						$return_delete = array_diff($bigIPArray, $after_config['ip2']);
						##如果之前是供应商的
						if($MemberPdtQuery->servicerprovider == 1) 
						{
							if( !empty($return_delete) ) 
							{
								#将更换机器前产品中的供应商IP从库中删除掉
								$deleteip_res = $SupplierIpModel->deleteAll(['ip' => $return_delete]);
							}								
						} else {
							#如果之前是自有的
						}
						
						if( !empty($return_insert) ) 
						{
							#同时更新供应商IP状态
							$updateip_res = $SupplierIpModel->updateAll(['status' =>1,'update_time' => time()],['ip' => $return_insert]);
						}
					} else {
						#使用原IP，不会新入库供应商IP
					}
					
					
				}
			}

			#只要选择了有差价，那么对应的基础成本价格、机器续费价格就可能存在变化
			if( $after_config['have_price_difference'] != 0 ) {
				
				$afterdata['sell_price'] = $after_config['sell_price'];
				$afterdata['cost_price'] = $after_config['cost_price'];
				$afterdata['upgrade_cost_price'] = 0.00;
				
				#选择了有差价。不管基本成本变没变都更新一下
				#查询产品在成本表在当天是否有记录
				$CostModel = new Cost;
				$start = strtotime(date('Y-m-d 00:00:00'));
				$end = strtotime(date('Y-m-d H:i:s'));
				$CostRes = $CostModel->find()->where(['unionid'=>$unionid])->andwhere(['>','change_time',$start])->andWhere(['<','change_time',$end])->asArray()->one();				
				#货币类型
				$currency_typeRes = Yii::$app->params['currency_type']; #print_r($currency_typeRes);				
				if( empty($CostRes) ) {
					
					$CostRes = $CostModel->find()->where(['unionid'=>$unionid])->asArray()->one(); #print_r($CostRes);exit;
					if( $CostRes ) {
						$currency_type = $CostRes['currency_type'];
					} else {
						$currency_type = $currency_typeRes[0];
					}
				
					$CostModel->unionid = $unionid;
					$CostModel->basic_money = $after_config['cost_price'];
					$CostModel->two_money = 0.00;
					$CostModel->currency_type = $currency_type;					
					$CostModel->change_time = time();
					$CostModel->admin_id = $UserMemberQuery->admin_id;

					if (!$CostModel->insert(false)) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '新增成本信息失败', 'data' => ''];
						return $data;
					}
				} else {
					$CostModelQuery = $CostModel->findone(['id'=>$CostRes['id']]); 
					$CostModelQuery->basic_money = $after_config['cost_price'];
					$CostModelQuery->two_money = 0.00;
					$CostModelQuery->change_time = time();
					$CostModelQuery->admin_id = $UserMemberQuery->admin_id;
					if (!$CostModelQuery->update(false)) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '更新成本信息失败', 'data' => ''];
						return $data;
					}
				}				
			} else {
				$afterdata['sell_price'] = $MemberPdtQuery->sell_price;
				$afterdata['cost_price'] = $MemberPdtQuery->cost_price;
				$afterdata['upgrade_cost_price'] = $MemberPdtQuery->upgrade_cost_price;
			}
			
			#判断用户产品之前所属产品为供应商还是自有。分别为之前的机器新增记录			
			if( $MemberPdtQuery->idle_id != "" && $MemberPdtQuery->idle_id != null ) {
				
				$OldIdlePdtQuery = $IdlePdtModel->find()->where(['id' => $MemberPdtQuery->idle_id])->one();
				#添加记录  自有
				$PdtHaveuselogModel = new PdtHaveuselog();
				$addUseLogRes = $PdtHaveuselogModel->doadd($OldIdlePdtQuery,$MemberPdtQuery);
				
				#将之前的自有机器状态更新为闲置
				$OldIdlePdtQuery->attribute_id = 1;
				$OldIdlePdtQuery->status = 0;
				
				#如果用户选择使用之前的IP地址。将更换后自有机器的IP地址绑定到更换前的自有机器上
				#必须保证更换后的机器也是自有的
				if( $after_config['use_original_ip'] == 1) {
					#如果更换机器选择的为测试机器
					if( isset($testid) && $testid ) {						
						if( $TestServerQuery->idle_id != "" ) {
							if( isset($after_config['is_exchangedip']) && $after_config['is_exchangedip'] == 1) { 
								#代表着之前已经对换过IP 就不再对换了
							} else {
								$OldIdlePdtQuery->ip = $set_Old_IdlePdt_ip;
								$OldIdlePdtQuery->ip2 = $set_Old_IdlePdt_ip2;
							}
						}
					} else {
						if( $provider == 0 ) {							
							if( isset($after_config['is_exchangedip']) && $after_config['is_exchangedip'] == 1) { 
								#代表着之前已经对换过IP 就不再对换了
							} else {
								$OldIdlePdtQuery->ip = $set_Old_IdlePdt_ip;
								$OldIdlePdtQuery->ip2 = $set_Old_IdlePdt_ip2;
							}							
						}
					}
				}
					
				$OldIdlePdtUpdateRes = $OldIdlePdtQuery->update();
				if ( !$OldIdlePdtUpdateRes ) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '原自有机器信息更新失败', 'data' => ''];
					return $data;
				}				
			} else {
				#添加供应商记录
				$PdtProvideruselogModel = new PdtProvideruselog();
				$ProviderModel = new Provider();
			   
				$sql = "(select end_time from pdt_haveuselog where unionid = '".$MemberPdtQuery->unionid."') UNION ALL (select end_time from pdt_provideruselog where unionid = '".$MemberPdtQuery->unionid."') ORDER BY end_time DESC LIMIT 1;";
				
				$UselogRes = Yii::$app->db->createCommand($sql)->queryScalar();  #echo $UselogRes;die;
			
				$ProviderRes = $ProviderModel->find()->where(['id'=>$MemberPdtQuery->provider_id])->asArray()->one();

				if( $UselogRes) {
					$ProviderRes['start_time'] = $UselogRes;
				} else {
					$ProviderRes['start_time'] = $MemberPdtQuery->start_time;
				}				
				
				$addUseLogRes = $PdtProvideruselogModel->doadd($ProviderRes,$MemberPdtQuery->toArray());				
				
			}
			
			if($WorkFlowRes['flow_name'] == '更换机器-线下打款') {
				$moneyInfo = json_decode($WorkFlowRes['flow_money_info'], true);
				if($WorkFlowRes['flow_total_money'] < $moneyInfo['money_num']) {
					$updateUserRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->one();
					#获取预存前的金额和预存金额
					$before_transaction_money = $updateUserRes->balance;
					$payment_amount = $moneyInfo['money_num'] - $WorkFlowRes['flow_total_money'];
					#更新
					$updateUserRes->balance = $updateUserRes->balance + $moneyInfo['money_num'] - $WorkFlowRes['flow_total_money'];
					if(!$updateUserRes->save()) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '更新用户余额出现异常', 'data' => ''];
						return $data;
					}
					
					#用户余额预存，新增资金流记录			
					$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();						
					$Paymentinfo = [
						'transaction_type' => '预存款项',
						'transaction_mode' => '线下打款',
						'transaction_channel' => $moneyInfo['money_platform']
					];
					$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $payment_amount, $before_transaction_money, $Paymentinfo, $userinfo);
					if( !$addReFundFlowRecords['status']) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
						return $data;
					}			
					
				}

				#因为在线支付和余额支付在订单支付时就已经增加记录了，所以这里只判断增加线下付款的
				#新增用户资金流动记录
				$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
				#获取退款前的用户余额数
				$before_transaction_money = $userinfo['balance'];
				$Paymentinfo = [
					'transaction_mode' => '线下打款',
					'transaction_channel' => $moneyInfo['money_platform']
				];
				$addReFundFlowRecords = FundFlowRecords::WorkFlow_Expenditure_Amount_Record($flow_id, $WorkFlowRes['flow_total_money'], $before_transaction_money, $Paymentinfo, $userinfo);
				if( !$addReFundFlowRecords['status']) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
					return $data;
				}
			}
			
			if($WorkFlowRes['flow_name'] == '更换机器-余额退款') {
				$moneyInfo = json_decode($WorkFlowRes['flow_money_info'], true);
				$updateUserRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->one();
				#退款前的金额和退款金额
				$before_transaction_money = $updateUserRes->balance;
				$payment_amount = $moneyInfo['money_num'];
				#更新
				$updateUserRes->balance = $updateUserRes->balance + $moneyInfo['money_num'];
				if(!$updateUserRes->save()) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '更新用户余额出现异常', 'data' => ''];
					return $data;
				}
				#增加记录
				#用户余额预存，新增资金流记录			
				$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();						
				$Paymentinfo = [					
					'transaction_mode' => '余额加款',
					'transaction_channel' => '用户余额'
				];
				$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $payment_amount, $before_transaction_money, $Paymentinfo, $userinfo);
				if( !$addReFundFlowRecords['status']) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
					return $data;
				}
				
			}
			
			#更新机器账户密码
			$InitialAccountModel = new InitialAccount();	
			$AccountQuery = $InitialAccountModel->find()->where(['unionid' => $unionid])->one();			
			$account['create_time'] = time();
			$AccountQuery->attributes = $account;			
			$AccountUpdateRes = $AccountQuery->update(false);
			
			if( !$AccountUpdateRes ) {
				$transaction->rollBack();
				$data = ['status' => 0, 'info' => '机器账户信息更新失败', 'data' => ''];
				return $data;
			}
			
			$MemberPdtQuery->attributes = $afterdata; #print_r($afterdata);print_r($MemberPdtQuery);exit;
			$MemberPdtQuery->update_time = time();
			$MemberPdtQuery->status = 1;
			$UpdateRes = $MemberPdtQuery->update();
			
			#财务流水记录
			$FinanceRes = FinanceStatment::Report_Simple($flow_id);
			$FinanceSuccess = $FinanceRes['status'];
			
			#账单开始
			$BillRes = self::NewBill($flow_id);
			if( $BillRes['status'] != 1)
			{
				$transaction->rollBack();
				$data = ['status' => 0, 'info' => $BillRes['info'], 'data' => ''];
				return $data;
			}
			#账单end
			
			if( !$UpdateRes || !$FinanceSuccess) {
				$transaction->rollBack();
				$data = ['status' => 0, 'info' => '用户产品更新失败', 'data' => ''];				
			} else {
				$prepay = PrePay::PrePay_Change_Replace($flow_id);
				$revenue = RevenueCount::RevenueRecord($flow_id);
				$note = RevenueNotes::NoteFinish($flow_id);
				$output = OutPut::OrderPayPut($flow_id);
				if($prepay['status'] && $revenue['status'] && $note['status'] && $output['status']) {
					$transaction->commit();
					$data = ['status' => 1, 'info' => '用户更换机器操作完成', 'data' => ''];
				} else {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '预付款、收支或数据明细出现异常', 'data' => ''];
				}				
			}			
			return $data;
			
		} else {
			#中止流程完成，处理驳回			
			#判断金额：如果为通过，那么则要退款，如果金额审核驳回，那么没有收到款项则不退款
			if( in_array($WorkFlowRes['flow_money_audit_status'], ['通过', '无需审核'])) {
				if(!in_array($WorkFlowRes['flow_name'], ['更换机器-无金额', '更换机器-余额退款', '更换机器-线下打款', '更换机器-后付款'])) {
					#查看退款
					$moneyInfo = json_decode($WorkFlowRes['flow_money_info'], true);
					if(!isset($moneyInfo['stop_refund'])) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '缺少退款信息', 'data' => ''];
						return $data;
					}
					#退款成功，新增资金流记录		
					#退款到用户账户余额或者平台
					if($moneyInfo['stop_refund']['type'] == 'balance') {
						$userUpdateRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->one();
						#退款前的金额和退款金额
						$before_transaction_money = $userUpdateRes->balance;
						$payment_amount = $moneyInfo['stop_refund']['money'];
						#更新
						$userUpdateRes->balance += $moneyInfo['stop_refund']['money'];
						if(!$userUpdateRes->save()) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '更新退款信息异常', 'data' => ''];
							return $data;
						}
						#新增资金流记录
						$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();						
						$Paymentinfo = [
							'transaction_type' => '更换机器取消',
							'transaction_mode' => '余额加款',
							'transaction_channel' => '用户余额'
						];
						$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $payment_amount, $before_transaction_money, $Paymentinfo, $userinfo);
						if( !$addReFundFlowRecords['status']) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
							return $data;
						}
					} else {
						#这是退款到用户卡或者其他方式
						#新增资金流记录
						$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
						$before_transaction_money = $userinfo['balance'];
						$payment_amount = $moneyInfo['stop_refund']['money'];
						$Paymentinfo = [
							'transaction_type' => '更换机器取消',
							'transaction_mode' => '线下打款',
							'transaction_channel' => $moneyInfo['stop_refund']['platform']
						];
						$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $payment_amount, $before_transaction_money, $Paymentinfo, $userinfo);
						if( !$addReFundFlowRecords['status']) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
							return $data;
						}
					}					
				}				
			}
			
			#如果选择的是测试机，
			if( isset($testid) && $testid ) {				
				$TestServerQuery = $TestServerModel->find()->where(['id' => $testid])->one();
				#将测试机器的状态改为测试服务器
				$TestServerQuery->status = 0;
				#更新
				if( !$TestServerQuery->update(false) ) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '测试机状态更新失败', 'data' => ''];
					return $data;
				}
			} else {
				#如果不是选择的测试机。那么选择不是自有机器就是供应商机器。如果选择的自有机器，将自有机器改为闲置状态
				if( $provider == 0 ) {
					#将更换的自有机器改为闲置状态
					$IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $after_config['idle_id']])->one();
					$IdlePdtQuery->status = 0;
					#更新
					if( !$IdlePdtQuery->update(false) ) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '自有机器状态更新失败', 'data' => ''];
						return $data;
					}
				} else {
					#如果选择的供应商机器
					#将入库的供应商IP删除掉（如果使用原IP就没有入库）。
					if( $after_config['use_original_ip'] != 1) 
					{
						$oldIP = json_decode($MemberPdtQuery->ip2, true);						
						$bigIPArray = array_unique(array_merge($after_config['ip2'], $oldIP));
						$return_insert = array_diff($bigIPArray, $oldIP);
						$return_delete = array_diff($bigIPArray, $after_config['ip2']);						
						#将之前入库的供应商IP从库中删除掉
						if( !empty($return_insert) )
						{
							$deleteip_res = $SupplierIpModel->deleteAll(['ip' => $return_insert]);
						}						
					}
					
				}
			}
			
			#提供选择使用原IP，并且已经对换过IP（自有才修改）
			if( $after_config['use_original_ip'] == 1) {
				if( $after_config['is_exchangedip'] == 1 && isset($after_config['is_exchangedip']) ) {
					
					$after_IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $after_config['idle_id']])->one();#更换后的自有机器信息对象
					$front_IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $front_config['idle_id']])->one();#更换前的自有机器信息对象
					
					$after_IdlePdtQuery->ip = $front_IdlePdtQuery->ip;
					$after_IdlePdtQuery->ip2 = $front_IdlePdtQuery->ip2;
					$after_IdlePdtQuery->vlan = '';
					
					$front_IdlePdtQuery->ip = $MemberPdtQuery->ip;
					$front_IdlePdtQuery->ip2 =  $MemberPdtQuery->ip2;	
					$front_IdlePdtQuery->vlan = '';
					if( !$front_IdlePdtQuery->update() || !$after_IdlePdtQuery->update() ) {	
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '恢复对换IP失败', 'data' => ''];
						return $data;
					}
				}
			}
			
			
			#如果为后付款。取消订单
			if( $WorkFlowRes['flow_name'] == '更换机器-后付款') {
				$TradeMainModel = new TradeMain();
				$TradeMainQuery = $TradeMainModel->find()->where(['trade_orderid' => $WorkFlowRes['flow_orderid']])->one();
				$TradeMainQuery->trade_status = '已取消';
				$updateTrade = $TradeMainQuery->update(false);
			} else {
				$updateTrade = true;
			}
			
			#中止流程完成 和 处理驳回 两种状态 将用户产品更新状态为使用
			$MemberPdtQuery->status = 1;
			
			$note = RevenueNotes::NoteFinish($flow_id);
			if( $MemberPdtQuery->update() && $updateTrade && $note['status']) {
				$transaction->commit();
				$data = ['status' => 1, 'info' => '用户产品状态恢复成功', 'data' => ''];

			} else {
				$transaction->rollBack();
				$data = ['status' => 0, 'info' => '用户产品状态恢复失败', 'data' => ''];					
			}
			return $data;
		}
	}

	#变更配置模块
	public static function ModelChangeConfig($flow_id, $status) {
		if(!$flow_id) {
			$data = ['status' => 0, 'info' => '缺少必要参数', 'data' => ''];
			return $data;
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$IdlePdtModel = new IdlePdt();
		$MemberPdtModel = new MemberPdt();
		$UserMemberModel = new UserMember();
		$SupplierIpModel = new SupplierIp();
		$PdtIpModel = new PdtIp();
		$SwitchLineModel = new SwitchLine();
		
		#开启事务
		$transaction = Yii::$app->db->beginTransaction();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		
		$after_config = json_decode($WorkFlowDetailRes['flow_after_config'], true); #变更后
		$front_config = json_decode($WorkFlowDetailRes['flow_front_config'], true); #变更前
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		
		#获取用户产品
		$MemberPdtQuery = $MemberPdtModel->find()->where(['unionid' => $WorkFlowDetailRes['flow_unionid']])->one();
		
		if( empty($MemberPdtQuery) ) {
			$transaction->rollBack();
			$data = ['status' => 0, 'info' => '未知的用户产品或已被删除', 'data' => ''];
			return $data;
		}
		
		if( $status == '处理完成' ) 
		{			
			$MemberPdtQuery->pdt_id = $after_config['pdt_id'];#新增的更换类别
			$MemberPdtQuery->config = json_encode($after_config['config'], JSON_UNESCAPED_UNICODE);
			$MemberPdtQuery->bandwidth = $after_config['config']['requirement_bandwidth'];
			$MemberPdtQuery->real_bandwidth = $after_config['config']['configbandwidth'];
			
			$MemberPdtQuery->sell_price = $after_config['normal_sell_price'];
			$MemberPdtQuery->upgrade_cost_price = $after_config['normal_upgrade_cost_price'];
			
			#如果选择变更配置同步更换IP
			if( isset($after_config['isneed_replaceip']) && $after_config['isneed_replaceip'] == 1) {
				$MemberPdtQuery->ip = json_encode($after_config['ip'], JSON_UNESCAPED_UNICODE);
				$MemberPdtQuery->ip2 = json_encode($after_config['ip2'], JSON_UNESCAPED_UNICODE);				
			}
			$updateMemberPdtRes = $MemberPdtQuery->update();
			
			#如果机器为自有机器。将配置同步更新到自有机器
			if( $MemberPdtQuery->servicerprovider == 0 && $MemberPdtQuery->idle_id != '') 
			{
				#获取用户产品所属的自有机器库信息
				$IdlePdtRes = $IdlePdtModel->findOne($MemberPdtQuery->idle_id);
				$IdlePdtRes->pdt_id = $after_config['pdt_id'];
				$IdlePdtRes->config = json_encode($after_config['config'], JSON_UNESCAPED_UNICODE);
				$IdlePdtRes->update_time = time();
				if( !$IdlePdtRes->update() ) {					
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '所属自有机器配置同步失败', 'data' => ''];
					return $data;  
				}
				#更新IP状态
				if( isset($after_config['isneed_replaceip']) && $after_config['isneed_replaceip'] == 1) {					
					$bgIPArray = array_unique(array_merge($after_config['ip2'],$front_config['ip2']));							
					$result_bangding = array_diff($bgIPArray,$front_config['ip2']); 	#需要改为使用中的				
					$result_jiechu = array_diff($bgIPArray,$after_config['ip2']); 	#需要改为闲置的
										
					if( !empty($result_bangding) ) {
						$res1 = $PdtIpModel->updateAll(['status' => 1],['ip'=>$result_bangding]);
						if( $res1 != count($result_bangding) ) {
							$transaction->rollBack();
							$data = [ 'status' => 0, 'info' => '需绑定的IP状态更新失败', 'data' => ''];
							return $data;
						}
					}
					if( !empty($result_jiechu) ) {
						$res2 = $PdtIpModel->updateAll(['status' => 0],['ip'=>$result_jiechu]);	
						if( $res2 != count($result_jiechu) ) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '需闲置的IP状态更新失败', 'data' => ''];
							return $data;
						}
					}		
				}				
			} else {
				#如果为供应商机器，并且有更换IP				
				if( isset($after_config['isneed_replaceip']) && $after_config['isneed_replaceip'] == 1) 
				{			
					$bgIPArray = array_unique(array_merge($after_config['ip2'],$front_config['ip2']));							
					$result_bangding = array_diff($bgIPArray,$front_config['ip2']); 	#新增的IP，需要改为使用中的				
					$result_jiechu = array_diff($bgIPArray,$after_config['ip2']); 		#解绑需要删除的
					
					if( !empty($result_bangding) ) {
						$res1 = $SupplierIpModel->updateAll(['status' => 1,'update_time'=> time()],['ip'=>$result_bangding]);
						if( $res1 != count($result_bangding) ) 
						{
							$transaction->rollBack();
							$data = [ 'status' => 0, 'info' => '供应商IP状态更新失败', 'data' => ''];
							return $data;
						}
					}
					if( !empty($result_jiechu) ) 
					{
						$res2 = $SupplierIpModel->deleteAll(['ip'=>$result_jiechu]);	
						if( $res2 != count($result_jiechu) ) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '供应商IP删除处理失败', 'data' => ''];
							return $data;
						}
					}
				}				
			}
			
			#同时如果是 变更机器-余额退款，还需要将款项退回用户账户余额
			if( $WorkFlowRes['flow_name'] == '变更配置-余额退款' ) {
				$UserMemberQuery = $UserMemberModel->findone($MemberPdtQuery->user_id);
				#退款前的金额和退款金额
				$before_transaction_money = $UserMemberQuery->balance;
				$payment_amount = $after_config['price'];
				
				$UserMemberQuery->balance += $after_config['price'];
				if( !$UserMemberQuery->update() ) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '退款更新用户余额失败', 'data' => ''];
					return $data;
				}
				
				#新增资金流记录
				$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
				$Paymentinfo = [
					'transaction_mode' => '余额加款',
					'transaction_channel' => '用户余额'
				];
				$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $payment_amount, $before_transaction_money, $Paymentinfo, $userinfo);
				if( !$addReFundFlowRecords['status']) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
					return $data;
				}
			}
			
			if($WorkFlowRes['flow_name'] == '变更配置-线下打款') {
				$moneyInfo = json_decode($WorkFlowRes['flow_money_info'], true);
				#如果打款对于补款金额，将多余金额预存到余额
				if($WorkFlowRes['flow_total_money'] < $moneyInfo['money_num']) {
					$updateUserRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->one();
					#获取预存前的金额和预存金额
					$before_transaction_money = $updateUserRes->balance;
					$payment_amount = $moneyInfo['money_num'] - $WorkFlowRes['flow_total_money'];
					#更新
					$updateUserRes->balance = $updateUserRes->balance + $moneyInfo['money_num'] - $WorkFlowRes['flow_total_money'];
					if(!$updateUserRes->save()) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '更新用户余额出现异常', 'data' => ''];
						return $data;
					}
					
					#新增资金流记录
					$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
					$Paymentinfo = [
						'transaction_mode' => '余额加款',
						'transaction_channel' => '用户余额'
					];
					$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $payment_amount, $before_transaction_money, $Paymentinfo, $userinfo);
					if( !$addReFundFlowRecords['status']) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
						return $data;
					}
				}
				
				#因为在线支付和余额支付在订单支付时就已经增加记录了，所以这里只判断增加线下付款的
				#新增用户资金流动记录
				$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
				$before_transaction_money = $userinfo['balance'];
				$Paymentinfo = [
					'transaction_mode' => '线下打款',
					'transaction_channel' => $moneyInfo['money_platform']
				];
				$addReFundFlowRecords = FundFlowRecords::WorkFlow_Expenditure_Amount_Record($flow_id, $WorkFlowRes['flow_total_money'], $before_transaction_money, $Paymentinfo, $userinfo);
				if( !$addReFundFlowRecords['status']) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
					return $data;
				}
			}			
			
			#判断成本  是否更新或者新增成本表
			$CostModel = new Cost();
			$start = strtotime(date('Y-m-d 00:00:00'));
			$end = strtotime(date('Y-m-d H:i:s'));
			$CostRes = $CostModel->find()->where(['unionid'=>$MemberPdtQuery->unionid])->andwhere(['>','change_time',$start])->andWhere(['<','change_time',$end])->asArray()->all();	
			#如果当天有记录  就更新。如果没有 就新建一条成本信息
			if( empty($CostRes) ) {
				$CostModel->unionid = $MemberPdtQuery->unionid;
				$CostModel->basic_money = $MemberPdtQuery->cost_price;
				$CostModel->two_money = $after_config['normal_upgrade_cost_price'];
				$CostModel->currency_type = $after_config['currency_type'];
				$CostModel->change_time = time();
				$CostModel->admin_id = $MemberPdtQuery->admin_id;
				if( !$CostModel->insert(false) ) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '新增成本记录失败', 'data' => ''];
					return $data;
				}
			} else {
				$CostModelQuery = $CostModel->find()->where(['unionid'=>$MemberPdtQuery->unionid])->andwhere(['>','change_time',$start])->andWhere(['<','change_time',$end])->one();

				$CostModelQuery->two_money = $after_config['normal_upgrade_cost_price'];
				$CostModelQuery->currency_type = $after_config['currency_type'];
				$CostModelQuery->change_time = time();
				if( !$CostModelQuery->update(false) ) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '更新成本记录失败', 'data' => ''];
					return $data;
				}
			}
			#成本end
			#更新用户产品状态
			$MemberPdtQuery->status = 1;
			
			#财务流水记录
			$FinanceRes = FinanceStatment::Report_Simple($flow_id);
			$FinanceSuccess = $FinanceRes['status'];
			
			#账单开始
			$BillRes = self::NewBill($flow_id);
			if( $BillRes['status'] != 1)
			{
				$transaction->rollBack();
				$data = ['status' => 0, 'info' => $BillRes['info'], 'data' => ''];
				return $data;
			}
			#账单end
			
			if( $MemberPdtQuery->update() && $FinanceSuccess ) {
				
				$prepay = PrePay::PrePay_Change_Replace($flow_id);
				$revenue = RevenueCount::RevenueRecord($flow_id);
				$note = RevenueNotes::NoteFinish($flow_id);
				$output = OutPut::OrderPayPut($flow_id);
				if($prepay['status'] && $revenue['status'] && $note['status'] && $output['status']) {
					$transaction->commit();
					$data = ['status' => 1, 'info' => '用户产品变更配置成功', 'data' => ''];
					return $data;
				} else {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '预付款、收支或数据明细出现异常', 'data' => ''];
					return $data;
				}

			} else {
				$transaction->rollBack();
				$data = ['status' => 0, 'info' => '用户产品变更配置失败', 'data' => ''];					
			}
			
			return $data;
			
		} else {
			#中止流程完成，处理驳回			
			#判断金额：如果为通过，那么则要退款，如果金额审核驳回，那么没有收到款项则不退款
			if( in_array($WorkFlowRes['flow_money_audit_status'], ['通过', '无需审核'])) {
				if(!in_array($WorkFlowRes['flow_name'], ['变更配置-无金额', '变更配置-余额退款', '变更配置-线下打款','变更配置-后付款'])) {
					#查看退款
					$moneyInfo = json_decode($WorkFlowRes['flow_money_info'], true);
					if(!isset($moneyInfo['stop_refund'])) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '缺少退款信息', 'data' => ''];
						return $data;
					}
					
					#退款到用户账户余额
					if($moneyInfo['stop_refund']['type'] == 'balance') {
						$userUpdateRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->one();
						#退款前的金额和退款金额
						$before_transaction_money = $userUpdateRes->balance;
						$payment_amount = $moneyInfo['stop_refund']['money'];
						#更新
						$userUpdateRes->balance += $moneyInfo['stop_refund']['money'];
						if(!$userUpdateRes->save()) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '更新退款信息异常', 'data' => ''];
							return $data;
						}
						
						#新增资金流记录
						$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
						$Paymentinfo = [
							'transaction_mode' => '余额加款',
							'transaction_channel' => '用户余额'
						];
						$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $payment_amount, $before_transaction_money, $Paymentinfo, $userinfo);
						if( !$addReFundFlowRecords['status']) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
							return $data;
						}
						
					} else {
						#这是退款到用户卡或者其他方式
						#新增资金流记录
						$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
						$before_transaction_money = $userinfo['balance'];
						$payment_amount = $moneyInfo['stop_refund']['money'];
						$Paymentinfo = [
							'transaction_type' => '变更配置取消',
							'transaction_mode' => '线下打款',
							'transaction_channel' => $moneyInfo['stop_refund']['platform']
						];
						$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $payment_amount, $before_transaction_money, $Paymentinfo, $userinfo);
						if( !$addReFundFlowRecords['status']) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
							return $data;
						}
					}
				}
			}
			
			#同时如果为后付款 。将订单取消
			if( $WorkFlowRes['flow_name'] == '变更配置-后付款') {
				$TradeMainModel = new TradeMain();
				$TradeMainQuery = $TradeMainModel->find()->where(['trade_orderid' => $WorkFlowRes['flow_orderid']])->one();
				$TradeMainQuery->trade_status = '已取消';
				$updateTrade = $TradeMainQuery->update(false);
			} else {
				$updateTrade = true;
			}
			#如果需要同时更换IP
			if( isset($after_config['isneed_replaceip']) && $after_config['isneed_replaceip'] == 1) 
			{
				#为空代表还未确定IP
				if( !empty($after_config['ip']) ) 
				{
					if( $MemberPdtQuery->servicerprovider == 0 && $MemberPdtQuery->idle_id != '') 
					{
						#获取用户产品所属的自有机器库信息,并将之前临时更改的IP修改回去
						$IdlePdtRes = $IdlePdtModel->findOne($MemberPdtQuery->idle_id);
						
						#Start IP线路验证
						$PdtIpRes = $PdtIpModel->find()->select('room_id, line_type_id, vlan')->where(['ip' => $front_config['ip2'][0]])->asArray()->one();
						#获取机器对应交换机的线路信息
						$SwitchLineRes = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtRes->switch_location, 'line_type_id' => $PdtIpRes['line_type_id']])->asArray()->one();
						if( empty($SwitchLineRes) ) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '机器对应的交换机线路中未有IP的线路类型', 'data' => ''];
							return $data; 							
						}
						#End IP线路验证
			
						$IdlePdtRes->ip = json_encode($front_config['ip'], JSON_UNESCAPED_UNICODE);
						$IdlePdtRes->ip2 = json_encode($front_config['ip2'], JSON_UNESCAPED_UNICODE);
						$IdlePdtRes->vlan = $SwitchLineRes['switch_vlan'];
						$IdlePdtRes->update_time = time();
						if( !$IdlePdtRes->update() ) {
							$transaction->rollBack();
							$data = ['status' => 0, 'info' => '恢复自有机器配置IP失败', 'data' => ''];
							return $data;  
						}
						#恢复IP状态
						if( isset($after_config['isneed_replaceip']) && $after_config['isneed_replaceip'] == 1) {						
							$bgIPArray = array_unique(array_merge($after_config['ip2'],$front_config['ip2']));							
							$result_bangding = array_diff($bgIPArray,$front_config['ip2']); 	#需要改为使用中的				
							$result_jiechu = array_diff($bgIPArray,$after_config['ip2']); 	#需要改为闲置的
							
							$PdtIpModel = new PdtIp();
							
							if( !empty($result_bangding) ) {
								$res1 = $PdtIpModel->updateAll(['status' => 0],['ip'=>$result_bangding]);
								if( $res1 != count($result_bangding) ) {
									$transaction->rollBack();
									$data = ['status' => 0,'info' => 'IP状态恢复失败', 'data' => ''];
									return $data;
								}
							}
							if( !empty($result_jiechu) ) {
								$res2 = $PdtIpModel->updateAll(['status' => 1],['ip'=>$result_jiechu]);	
								if( $res2 != count($result_jiechu) ) {
									$transaction->rollBack();
									$data = ['status' => 0, 'info' => 'IP状态恢复失败', 'data' => ''];
									return $data;
								}
							}		
						}
						#End
					} else {
						#如果为供应商机器，并且有更换IP				
						if( isset($after_config['isneed_replaceip']) && $after_config['isneed_replaceip'] == 1) 
						{			
							$bgIPArray = array_unique(array_merge($after_config['ip2'],$front_config['ip2']));							
							$result_bangding = array_diff($bgIPArray,$front_config['ip2']); 	#新增的IP，需要改为使用中的				
							$result_jiechu = array_diff($bgIPArray,$after_config['ip2']); 		#解绑需要删除的
							
							if( !empty($result_bangding) ) #之前临时入库的IP要删除掉
							{
								$res2 = $SupplierIpModel->deleteAll(['ip'=>$result_bangding]);	
								if( $res2 != count($result_bangding) ) {
									$transaction->rollBack();
									$data = ['status' => 0, 'info' => '供应商IP删除处理失败', 'data' => ''];
									return $data;
								}
							}
							if( !empty($result_jiechu) ) #之前要删除的IP 改回原来的状态
							{
								$res1 = $SupplierIpModel->updateAll(['status' => 1,'update_time'=> time()],['ip'=>$result_jiechu]);
								if( $res1 != count($result_jiechu) ) 
								{
									$transaction->rollBack();
									$data = [ 'status' => 0, 'info' => '供应商IP状态更新失败', 'data' => ''];
									return $data;
								}
							}
							
						}
					}
				}
			}
			
			#中止流程完成 和 处理驳回 两种状态 将用户产品更新状态为使用
			$MemberPdtQuery->status = 1;
			$note = RevenueNotes::NoteFinish($flow_id);
			if( $MemberPdtQuery->update() && $updateTrade && $note['status']) {
				$transaction->commit();
				$data = ['status' => 1, 'info' => '用户产品状态恢复成功', 'data' => ''];

			} else {
				$transaction->rollBack();
				$data = ['status' => 0, 'info' => '用户产品状态恢复失败', 'data' => ''];					
			}
			return $data;
		}
		
	}
	
	#用户业务变更IP（暂时无用）
	public function ModelAmountReplaceIp($flow_id, $status) {
		exit;
		if(!$flow_id) {
			$data = ['status' => 0, 'info' => '缺少必要参数', 'data' => ''];
			return $data;
		}
		
		$MemberPdtModel = new MemberPdt();
		$WorkFlowRoleModel = new WorkFlowRole();
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$UserMemberModel = new UserMember();
		$PdtIpModel = new PdtIp();
		$IdlePdtModel = new IdlePdt();
		
		#开启事务
		$transaction = Yii::$app->db->beginTransaction();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		$after_config = json_decode($WorkFlowDetailRes['flow_after_config'], true);
		
		$ip = $after_config['ip'];
		$ip2 = $after_config['ip2'];
		
		#获取用户产品
		$MemberPdtQuery = $MemberPdtModel->find()->where(['unionid' => $WorkFlowDetailRes['flow_unionid']])->andWhere('status != -1')->one();
		if( empty($MemberPdtQuery) ) {
			return false;
		}
		
		#原用户产品IP
		$oldIP = json_decode($MemberPdtQuery->ip2,true);
		$result_bangding = [];
		$result_jiechu = [];

		#如果产品是自有库产品，将新选择的IP 状态改为使用 （方法：作比较，获取2个IP数组的差集  先将2个数组合并并且去重）
		if( $MemberPdtQuery->servicerprovider == 0 && $MemberPdtQuery->idle_id !="") {
			
			$bgIPArray = array_unique(array_merge($ip2,$oldIP));
			
			$result_bangding = array_diff($bgIPArray,$oldIP); 	#需要改为使用中的				
			$result_jiechu = array_diff($bgIPArray,$ip2); 	#需要改为闲置的
		}

		if( $yes_or_no == 'Y') {
			
			$updateIdleRes = true;
			#新的ip需要绑定的 改为使用
			if( !empty($result_bangding) ) {
				#$res1 = $PdtIpModel->modifyIPStatus($result_bangding,"1");
				$res1 = $PdtIpModel->updateAll(['status' => 1,'update_time' => time()],['ip'=>$result_bangding]);
			}			
			#需要解除绑定的IP  改为闲置
			if( !empty($result_jiechu) ) {
				$res2 = $PdtIpModel->updateAll(['status' => 0,'update_time' => time()],['ip'=>$result_jiechu]);			
			}
			#更新用户产品新的IP
			$MemberPdtQuery->ip = json_encode($ip);
			$MemberPdtQuery->ip2 = json_encode($ip2);
			#如果是自有机器，将同步更新IP
			if( $MemberPdtQuery->servicerprovider == 0 && $MemberPdtQuery->idle_id !="" ) {
				$IdlePdtQuery = $IdlePdtModel->findone($MemberPdtQuery->idle_id);
				$IdlePdtQuery->ip = json_encode($ip);
				$IdlePdtQuery->ip2 = json_encode($ip2);
				$IdlePdtQuery->update_time = time();
				$updateIdleRes = $IdlePdtQuery->update(false);
			}
			
			$updateFlowRes = true;
		} else {
			$updateIdleRes = true;
			#新的ip需要绑定的  之前为待定改为闲置
			if( !empty($result_bangding) ) {
				$res1 = $PdtIpModel->updateAll(['status' => 0,'update_time' => time()],['ip'=>$result_bangding]);
			}
			
			#需要解除绑定的IP  之前为待定改为使用
			if( !empty($result_jiechu) ) {
				$res2 = $PdtIpModel->updateAll(['status' => 1,'update_time' => time()],['ip'=>$result_jiechu]);			
			}	
			$WorkFlowQuery = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
			$WorkFlowQuery->flow_end_time = time();
			$updateFlowRes = $WorkFlowQuery->update();
		}
		
		
		#同时更新状态为正常
		$MemberPdtQuery->status = 1;
		
		if( !$MemberPdtQuery->update() || !$updateFlowRes || !$updateIdleRes) {
			$transaction->rollBack();
			return false;
		} else {
			$transaction->commit();
			return true;
		}		
	}
	
	#用户打款、扣款
	public static function ModelAmount($flow_id, $status) {
		if(!$flow_id || !$status) {
			$data = ['status' => 0, 'info' => '缺少必要参数', 'data' => ''];
			return $data;
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();

		$UserMemberModel = new UserMember();
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		$UserInfoRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes->flow_account])->one();
		
		#开启事务
		$transaction = Yii::$app->db->beginTransaction();
		
		$moneyInfo = json_decode($WorkFlowRes['flow_money_info'], true);
		
		if($WorkFlowRes->flow_name == '用户金额变动-打款') {
			#驳回，中止事务，均不进行操作
			if($WorkFlowRes->flow_status == '处理完成') 
			{
				#处理完成，上账
				#获取退款前的用户余额数
				$before_transaction_money = $UserInfoRes->balance;
				
				$UserInfoRes->balance = $UserInfoRes->balance + $WorkFlowRes->flow_total_money;
				if(!$UserInfoRes->save()) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '更新用户余额出现异常', 'data' => ''];
					return $data;
				}
				
				#新增用户资金流动记录
				$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
				$Paymentinfo = [
					'transaction_mode' => '线下打款',
					'transaction_channel' => $moneyInfo['money_platform']
				];
				$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $WorkFlowRes['flow_total_money'], $before_transaction_money, $Paymentinfo, $userinfo);
				if( !$addReFundFlowRecords['status']) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
					return $data;
				}

				#账单开始
				$BillRes = self::NewBill($flow_id);
				if( $BillRes['status'] != 1)
				{
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => $BillRes['info'], 'data' => ''];
					return $data;
				}
				#账单end
			}
			
		} else {
			#驳回，中止事务，均不进行操作
			if($WorkFlowRes->flow_status == '处理完成') {
				#处理完成，扣掉账户款项
				if($UserInfoRes->balance < $WorkFlowRes->flow_total_money) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '用户余额不足', 'data' => ''];
					return $data;
				}
				#获取退款前的用户余额数
				$before_transaction_money = $UserInfoRes->balance;
				
				$UserInfoRes->balance = $UserInfoRes->balance - $WorkFlowRes->flow_total_money;
				if(!$UserInfoRes->save()) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '更新用户余额出现异常', 'data' => ''];
					return $data;
				}
				
				#新增用户资金流动记录
				$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();
				$Paymentinfo = [
					'transaction_mode' => '余额扣款',
					'transaction_channel' => '用户余额'
				];
				$addReFundFlowRecords = FundFlowRecords::WorkFlow_Expenditure_Amount_Record($flow_id, $WorkFlowRes['flow_total_money'], $before_transaction_money, $Paymentinfo, $userinfo);
				if( !$addReFundFlowRecords['status']) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
					return $data;
				}
			}
		}
		
		#财务流水记录
		$FinanceRes = FinanceStatment::Report_Draw($flow_id);
		$FinanceSuccess = $FinanceRes['status'];
		
		if($FinanceSuccess == 1) {
			$transaction->commit();
			$data = ['status' => 1, 'info' => '更新数据完成', 'data' => ''];
			return $data;
		} else {
			$transaction->rollBack();
			$data = ['status' => 0, 'info' => $FinanceRes['info'], 'data' => ''];
			return $data;
		}
		
		
	}
	
	#业务退款
	public static function ModelRefund($flow_id, $status) {
		if(!$flow_id || !$status) {
			$data = ['status' => 0, 'info' => '缺少必要参数', 'data' => ''];
			return $data;
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$IdlePdtModel = new IdlePdt();
		$MemberPdtModel = new MemberPdt();
		$UserMemberModel = new UserMember();
		$SupplierIpModel = new SupplierIp();
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->one();
		
		$UserInfoRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes->flow_account])->one();
		
		$MemberPdtQuery = $MemberPdtModel->find()->where(['unionid' => $WorkFlowDetailRes->flow_unionid])->andWhere('status != -1')->one();
		if( empty($MemberPdtQuery) ) {
			$data = ['status' => 0, 'info' => '未知的用户产品或已被删除', 'data' => ''];
			return $data;
		}
		
		#开启事务
		$transaction = Yii::$app->db->beginTransaction();	
		
		if($status == '处理完成') {

			if( $MemberPdtQuery->idle_id && $MemberPdtQuery->servicerprovider == 0 ) {
				#自有机器
				
				$IdlePdtQuery = $IdlePdtModel->findOne(['id'=>$MemberPdtQuery->idle_id]); 
				#将此记录保存在分配表中
				$PdtUselogModel = new PdtHaveuselog();                
				$addUseLogRes = $PdtUselogModel->doadd($IdlePdtQuery, $MemberPdtQuery->toArray());
				
				#同时将客户要求带宽清空
				$config = json_decode($IdlePdtQuery->config, true);
				$config['requirement_bandwidth'] = '';
				#更新 同时将自有产品库状态数据更新
				$IdlePdtQuery->config = json_encode($config, JSON_UNESCAPED_UNICODE);				
				$IdlePdtQuery->attribute_id = 1;
				$IdlePdtQuery->status = 0;
				
				$IdlePdtUpdateRes = $IdlePdtQuery->save();
				
				
				
			} else {
				#供应商
			   
				$PdtProvideruselogModel = new PdtProvideruselog();
				$ProviderModel = new Provider();
			   
				$sql = "(select end_time from pdt_haveuselog where unionid = '".$MemberPdtQuery->unionid."') UNION ALL (select end_time from pdt_provideruselog where unionid = '".$MemberPdtQuery->unionid."') ORDER BY end_time DESC LIMIT 1;";
				
				$UselogRes = Yii::$app->db->createCommand($sql)->queryScalar();
			
				$ProviderRes = $ProviderModel->find()->where(['id'=>$MemberPdtQuery->provider_id])->asArray()->one();
			
				if( $UselogRes) {
					$ProviderRes['start_time'] = $UselogRes;
				} else {
					$ProviderRes['start_time'] = $MemberPdtQuery->start_time;
				}
				 
				$addUseLogRes = $PdtProvideruselogModel->doadd($ProviderRes, $MemberPdtQuery->toArray());
				$IdlePdtUpdateRes = true;
				#将供应商IP从库中删除
				$ipArr = json_decode($MemberPdtQuery->ip2, true);
				if( !empty($ipArr) )
				{
					$deleteip_res = $SupplierIpModel->deleteAll(['ip' => $ipArr]);
				}				
			}
			
			#获取退款前的用户余额数
			$before_transaction_money = $UserInfoRes->balance;
			#修改用户余额
			if($WorkFlowRes->flow_total_money > 0) {
				$UserInfoRes->balance = $UserInfoRes->balance + $WorkFlowRes->flow_total_money;
				$updateUserInfo = $UserInfoRes->save();
				
				#新增用户资金流动记录
				$userinfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes['flow_account']])->asArray()->one();			
				$Paymentinfo = [
					'transaction_mode' => '余额加款',
					'transaction_channel' => '用户余额'
				];			
				$addReFundFlowRecords = FundFlowRecords::WorkFlow_Income_Amount_Record($flow_id, $WorkFlowRes['flow_total_money'], $before_transaction_money, $Paymentinfo, $userinfo);
				if( !$addReFundFlowRecords['status']) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '新增用户资金流动记录失败', 'data' => ''];
					return $data;
				}
				
			} else {
				$updateUserInfo = true;
			}
			

			
				
			#修改业务状态
			$MemberPdtQuery->status = -1;
			$updatePdt = $MemberPdtQuery->save();
			
			#财务流水记录
			$FinanceRes = FinanceStatment::Report_Simple($flow_id);
			$FinanceSuccess = $FinanceRes['status'];
			
			#增加业务删除记录
			$UserAdminModel = new UserAdmin();
			$UserAdminRes = $UserAdminModel->find()->where(['admin_id' => $MemberPdtQuery->admin_id])->asArray()->one();
				
			$DeleteRecordModel = new BusinessDeleteRecord();			
			$DeleteRecordModel->unionid = $MemberPdtQuery->unionid;
			$DeleteRecordModel->user_id = $MemberPdtQuery->user_id;
			$DeleteRecordModel->user_name = $MemberPdtQuery->user_name;
			$DeleteRecordModel->admin_id = $MemberPdtQuery->admin_id;
			$DeleteRecordModel->admin_name = $UserAdminRes['uname'];
			$DeleteRecordModel->ip = $MemberPdtQuery->ip;
			$DeleteRecordModel->ip2 = $MemberPdtQuery->ip2;
			$DeleteRecordModel->ipmi_ip = $MemberPdtQuery->ipmi_ip;
			$DeleteRecordModel->operator_name = $WorkFlowRes->flow_promter;
			$DeleteRecordModel->delete_time = time();
			$DeleteRecordModel->delete_reason = '业务退款手动删除';
			if( !$DeleteRecordModel->insert())
			{
				$transaction->rollBack();
				$data = ['status' => 0, 'info' => '新增业务删除记录失败', 'data' => ''];
				return $data;
			}
			
			$revenue = RevenueCount::RevenueRecord($flow_id);
			$note = RevenueNotes::NoteFinish($flow_id);
			$output = OutPut::OrderPayPut($flow_id);
			
			if($addUseLogRes && $IdlePdtUpdateRes && $updateUserInfo && $updatePdt && $FinanceSuccess && $revenue['status'] && $note['status'] && $output['status']) {
				$transaction->commit();
				$data = ['status' => 1, 'info' => '更新业务退款信息完成', 'data' => ''];
				return $data;
			} else {
				$transaction->rollBack();
				$data = ['status' => 0, 'info' => '业务退款信息出现异常', 'data' => ''];
				return $data;
			}
		} else if(in_array($status, ['中止事务完成', '处理驳回'])) {
			
			#这个状态需要把机器的状态还原
			$MemberPdtQuery->status = 1;
			$updatePdt = $MemberPdtQuery->save();
			$note = RevenueNotes::NoteFinish($flow_id);
			if($updatePdt && $note['status']) {
				$transaction->commit();
				$data = ['status' => 1, 'info' => '更新业务退款信息完成', 'data' => ''];
				return $data;
			} else {
				$transaction->rollBack();
				$data = ['status' => 0, 'info' => '业务退款信息出现异常', 'data' => ''];
				return $data;
			}
		}
	}
		
	#修改产品时间
	public static function ModelUpdateTime($flow_id, $status) {
		if(!$flow_id || !$status) {
			$data = ['status' => 0, 'info' => '缺少必要参数', 'data' => ''];
			return $data;
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$IdlePdtModel = new IdlePdt();
		$MemberPdtModel = new MemberPdt();
		$UserMemberModel = new UserMember();
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->one();
		$UserInfoRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes->flow_account])->one();

		$MemberPdtQuery = $MemberPdtModel->find()->where(['unionid' => $WorkFlowDetailRes->flow_unionid])->andWhere('status != -1')->one();
		if( empty($MemberPdtQuery) ) {
			$data = ['status' => 0, 'info' => '未知的用户产品或已被删除', 'data' => ''];
			return $data;
		}
		
		$afterconfig = json_decode($WorkFlowDetailRes->flow_after_config, true);
		
		if($status == '处理完成') {
			$MemberPdtQuery->start_time = $afterconfig['start_time'];
			$MemberPdtQuery->end_time = $afterconfig['end_time'];
			
			if($MemberPdtQuery->save()) {
				$data = ['status' => 1, 'info' => '更新业务时间信息完成', 'data' => ''];
				return $data;
			} else {
				$data = ['status' => 0, 'info' => '更新业务时间信息出现异常', 'data' => ''];
				return $data;
			}
		} else {
			$data = ['status' => 1, 'info' => '无操作', 'data' => ''];
			return $data;
		}
	}
	
	#修改产品信息
	public static function ModelUpdateInfo($flow_id, $status) {
		if(!$flow_id || !$status) {
			$data = ['status' => 0, 'info' => '缺少必要参数', 'data' => ''];
			return $data;
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$IdlePdtModel = new IdlePdt();
		$MemberPdtModel = new MemberPdt();
		$UserMemberModel = new UserMember();
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->one();
		$UserInfoRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes->flow_account])->one();

		$MemberPdtQuery = $MemberPdtModel->find()->where(['unionid' => $WorkFlowDetailRes->flow_unionid])->andWhere('status != -1')->one();
		if( empty($MemberPdtQuery) ) {
			$data = ['status' => 0, 'info' => '未知的用户产品或已被删除', 'data' => ''];
			return $data;
		}
		
		$afterconfig = json_decode($WorkFlowDetailRes->flow_after_config, true);
		
		if($status == '处理完成') {
			if( $MemberPdtQuery->cost_price != $afterconfig['data']['cost_price'] || $MemberPdtQuery->upgrade_cost_price != $afterconfig['data']['upgrade_cost_price'] ) {
				//查询产品在成本表在当天是否有记录
				$CostModel = new Cost;
				$start = strtotime(date('Y-m-d 00:00:00'));
				$end = strtotime(date('Y-m-d H:i:s'));
				$CostRes = $CostModel->find()->where(['unionid'=>$WorkFlowDetailRes->flow_unionid])->andwhere(['>','change_time',$start])->andWhere(['<','change_time',$end])->asArray()->one();				
				
				if( empty($CostRes) ) {
					$CostModel->unionid = $WorkFlowDetailRes->flow_unionid;
					$CostModel->basic_money = $afterconfig['data']['cost_price'];
					$CostModel->two_money = $afterconfig['data']['upgrade_cost_price'];
					$CostModel->currency_type = $afterconfig['data']['currency_type'];					
					$CostModel->change_time = time();
					$CostModel->admin_id = $MemberPdtQuery->admin_id;

					if ( !$CostModel->insert(false) ) {
						$data = ['status' => 0, 'info' => '创建成本信息出现异常', 'data' => ''];
						return $data;
					}
				} else {
					$CostModelQuery = $CostModel->findone(['id'=>$CostRes['id']]); 
					$CostModelQuery->basic_money = $afterconfig['data']['cost_price'];
					$CostModelQuery->two_money = $afterconfig['data']['upgrade_cost_price'];
					$CostModelQuery->currency_type = $afterconfig['data']['currency_type'];
					$CostModelQuery->change_time = time();
					
					if (!$CostModelQuery->update(false)) {
						$data = ['status' => 0, 'info' => '更新成本信息出现异常', 'data' => ''];
						return $data;
					}
				}
			}
			
			$MemberPdtQuery->attributes = $afterconfig['data'];
			#数据验证
			if ( !$MemberPdtQuery->validate() ) {
				$data = ['status' => 0, 'info' => '更新业务表出现异常', 'data' => ''];
				return $data;
			}
			
			#更新
			if ( $MemberPdtQuery->update() ) {
				$data = ['status' => 1, 'info' => '更新业务信息完成', 'data' => ''];
				return $data;
			} else {
				$data = ['status' => 0, 'info' => '更新业务信息出现异常', 'data' => ''];
				return $data;
			}
		} else {
			$data = ['status' => 1, 'info' => '无操作', 'data' => ''];
			return $data;
		}
	}
	
	#用户机器过户操作
	public static function ModelTransfer($flow_id, $status) {
		if(!$flow_id || !$status) {
			$data = ['status' => 0, 'info' => '缺少必要参数', 'data' => ''];
			return $data;
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$IdlePdtModel = new IdlePdt();
		$MemberPdtModel = new MemberPdt();
		$UserMemberModel = new UserMember();
		$UserAdminModel = new UserAdmin();
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->one();
		$UserInfoRes = $UserMemberModel->find()->where(['email' => $WorkFlowRes->flow_account])->one();

		$MemberPdtQuery = $MemberPdtModel->find()->where(['unionid' => $WorkFlowDetailRes->flow_unionid])->andWhere('status != -1')->one();
		if( empty($MemberPdtQuery) ) {
			$data = ['status' => 0, 'info' => '未知的用户产品或已被删除', 'data' => ''];
			return $data;
		}
		
		$afterconfig = json_decode($WorkFlowDetailRes->flow_after_config, true);
		if($status == '处理完成') {
			$newAdmin = $UserAdminModel->find()->where(['admin_id' => $afterconfig['admin_id']])->asArray()->one();
			$newUser = $UserMemberModel->find()->where(['u_id' => $afterconfig['user_id']])->asArray()->one();
			
			$MemberPdtQuery->user_id = $afterconfig['user_id'];
			$MemberPdtQuery->admin_id = $afterconfig['admin_id'];
			$MemberPdtQuery->user_name = $afterconfig['user_name'];
			$MemberPdtQuery->status = 1;
			
			if($MemberPdtQuery->save()) {
				$data = ['status' => 1, 'info' => '更新过户信息完成', 'data' => ''];
				return $data;
			} else {
				$data = ['status' => 0, 'info' => '更新过户信息出现异常', 'data' => ''];
				return $data;
			}
			
		} else {
			
			$MemberPdtQuery->status = 1;
			
			if($MemberPdtQuery->save()) {
				$data = ['status' => 1, 'info' => '无操作', 'data' => ''];
				return $data;
			} else {
				$data = ['status' => 0, 'info' => '更新过户信息出现异常', 'data' => ''];
				return $data;
			}
		}
	}
	
	#自有机器更换IP操作
	public static function ModelIdleChangeIP($flow_id, $status){
		if(!$flow_id || !$status) {
			$data = ['status' => 0, 'info' => '缺少必要参数', 'data' => ''];
			return $data;
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$IdlePdtModel = new IdlePdt();
		$PdtIpModel = new PdtIp();
		$SwitchLineModel = new SwitchLine();
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->one();
		
		$afterconfig = json_decode($WorkFlowDetailRes->flow_after_config, true);
		$ip = $afterconfig['ip'];
		$ip2 = $afterconfig['ip2'];
		
		$IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $afterconfig['idle_id']])->andWhere('status != 1')->one();

		if( empty($IdlePdtQuery) ) {
			$data = ['status' => 0, 'info' => '未知的自有机器或已经被使用', 'data' => ''];
			return $data;
		}
		$oldIP = json_decode($IdlePdtQuery->ip2, true);
		if( empty($oldIP) || !$oldIP ) {
			 $oldIP = [];
		}
		$bgIPArray = array_unique(array_merge($ip2,$oldIP));
		$result_bangding = array_diff($bgIPArray,$oldIP);//需要改为使用中的
		$result_jiechu = array_diff($bgIPArray,$ip2);//需要改为闲置的					
				
		if($status == '处理完成')
		{								
			if( $IdlePdtQuery->servicerprovider == 0 ) {
				
				#获取机器交换机对应线路的Vlan
				if( !empty($ip2) ) {
					$PdtIpAll = $PdtIpModel->find()->select('room_id, line_type_id,vlan')->where(['in','ip',$ip2])->asArray()->all();
					$line_typeList = array_column($PdtIpAll, 'line_type_id'); 
					$line_typeList = array_unique($line_typeList);
					if( count($line_typeList) > 1 ) {
						$data = ['status' => 0, 'info' => '所选IP与其他IP的线路类型不匹配', 'data' => ''];
						return $data;
					}
					#获取机器对应交换机的线路信息
					$SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtQuery->switch_location, 'line_type_id' => $line_typeList[0]])->asArray()->one();
					if( empty($SwitchLineList) ) {
						$data = ['status' => 0, 'info' => '机器对应的交换机线路中未有IP的线路类型', 'data' => ''];
						return $data;
					}
				}
			
				#新的ip需要绑定的
				if( !empty($result_bangding) ) {
					$updatePdtIPRes = $PdtIpModel->updateAll(['status' => 1],['ip'=>$result_bangding]);							
				}				
				#需要解除绑定的IP
				if( !empty($result_jiechu) ) {
					$updatePdtIPRes = $PdtIpModel->updateAll(['status' => 0],['ip'=>$result_jiechu]);						
				}
			}
			
			$IdlePdtQuery->ip = json_encode($ip);
			$IdlePdtQuery->ip2 = json_encode($ip2);
			if( !empty($ip2) ) {
				if($SwitchLineList) {
					$IdlePdtQuery->vlan = $SwitchLineList['switch_vlan'];
				}
			} else {
				$IdlePdtQuery->vlan = 'N/A';
			}
			
			$IdlePdtQuery->status = 0;
			
			if($IdlePdtQuery->save()) {
				$data = ['status' => 1, 'info' => '自有机器更换IP完成', 'data' => ''];
			} else {
				$data = ['status' => 0, 'info' => '自有机器更换IP出现异常', 'data' => ''];
			}
			return $data;
		} else {
			if( $IdlePdtQuery->servicerprovider == 0 ) {
				#新的ip需要绑定的
				if( !empty($result_bangding) ) {
					$updatePdtIPRes = $PdtIpModel->updateAll(['status' => 0],['ip'=>$result_bangding]);							
				}
				#需要解除绑定的IP
				if( !empty($result_jiechu) ) {
					$updatePdtIPRes = $PdtIpModel->updateAll(['status' => 1],['ip'=>$result_jiechu]);						
				}
			}

			$IdlePdtQuery->status = 0;
			
			if($IdlePdtQuery->save()) {
				$data = ['status' => 1, 'info' => '自有机器IP与状态恢复完成', 'data' => ''];	
			} else {
				$data = ['status' => 0, 'info' => '自有机器IP与状态恢复出现异常', 'data' => ''];
			}
			return $data;			
		}		
	}
	
	#变更测试机配置操作
	public static function ModelTesterChangeConfig($flow_id, $status) {
		if(!$flow_id || !$status) {
			$data = ['status' => 0, 'info' => '缺少必要参数', 'data' => ''];
			return $data;
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$TestServerModel = new TestServer();
		$IdlePdtModel = new IdlePdt();
		$PdtIpModel = new PdtIp();
		$SwitchLineModel = new SwitchLine();
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->one();
		
		$afterConfig = json_decode($WorkFlowDetailRes->flow_after_config, true);
		$frontConfig = json_decode($WorkFlowDetailRes->flow_front_config, true);
		
		$ip = $afterConfig['ip'];
		$ip2 = $afterConfig['ip2'];
		
		$TestServerQuery = $TestServerModel->find()->where(['id' => $frontConfig['test_id']])->one();

		if( empty($TestServerQuery) ) {
			$data = ['status' => 0, 'info' => '未知的测试机', 'data' => ''];
			return $data;
		}
		
		$oldIP = json_decode($TestServerQuery->ip2, true);
		
		$bgIPArray = array_unique(array_merge($ip2,$oldIP));
		$result_bangding = array_diff($bgIPArray,$oldIP); 	#需要改为使用中的
		$result_jiechu = array_diff($bgIPArray,$ip2);  		#需要改为闲置的					
				
		if($status == '处理完成') 
		{	
			#如果为自有机器
			if( $TestServerQuery->servicerprovider == 0) 
			{				
				#新的ip需要绑定的
				if( !empty($result_bangding) ) {
					$updatePdtIPRes = $PdtIpModel->updateAll(['status' => 1],['ip'=>$result_bangding]);							
				}
				#需要解除绑定的IP
				if( !empty($result_jiechu) ) {
					$updatePdtIPRes = $PdtIpModel->updateAll(['status' => 0],['ip'=>$result_jiechu]);						
				}
				
				#更新自有机器
				$IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $TestServerQuery->idle_id])->one();
				
				#获取IP对应线路的Vlan
				$PdtIpAll = $PdtIpModel->find()->select('room_id, line_type_id,vlan')->where(['in','ip',$ip2])->asArray()->all();
				$line_typeList = array_column($PdtIpAll, 'line_type_id'); 
				$line_typeList = array_unique($line_typeList);
				if( count($line_typeList) > 1 ) {
					$data = ['status' => 0, 'info' => '所选IP与其他IP的线路类型不匹配', 'data' => ''];
					return $data;
				}
					
				#获取机器对应交换机的线路信息
				$SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtQuery->switch_location, 'line_type_id' => $line_typeList[0]])->asArray()->one();
				if( empty($SwitchLineList) ) {
					$data = ['status' => 0, 'info' => '机器对应的交换机线路中未有IP的线路类型', 'data' => ''];
					return $data;
				}
				
				$IdlePdtQuery->ip = json_encode($ip, JSON_UNESCAPED_UNICODE);
				$IdlePdtQuery->ip2 = json_encode($ip2, JSON_UNESCAPED_UNICODE);
				if($SwitchLineList) {
					$IdlePdtQuery->vlan = $SwitchLineList['switch_vlan'];
				}
				$IdlePdtQuery->config = json_encode($afterConfig['config'], JSON_UNESCAPED_UNICODE);
				if(!$IdlePdtQuery->update())
				{
					$data = ['status' => 0, 'info' => '自有机器信息更新失败', 'data' => ''];
					return $data;
				}
			} else  {
				#供应商机器
				#$TestServerQuery->account_name = $afterConfig['account_name'];
				#$TestServerQuery->account_pwd = $afterConfig['account_pwd'];
				#$TestServerQuery->account_port = $afterConfig['account_port'];
			}
			
			#更新测试机
			$TestServerQuery->bandwidth = $afterConfig['config']['requirement_bandwidth'];
			$TestServerQuery->real_bandwidth = $afterConfig['config']['configbandwidth'];
			$TestServerQuery->config = json_encode($afterConfig['config'], JSON_UNESCAPED_UNICODE);
			$TestServerQuery->ip = json_encode($ip, JSON_UNESCAPED_UNICODE);
			$TestServerQuery->ip2 = json_encode($ip2, JSON_UNESCAPED_UNICODE);
			$TestServerQuery->sales_id = $afterConfig['sales_id'];
			$TestServerQuery->user_contact = $afterConfig['user_contact'];
			$TestServerQuery->server_remarks = $afterConfig['server_remarks'];
			$TestServerQuery->status = '0';
			if($TestServerQuery->update()) {
				$data = ['status' => 1, 'info' => '变更测试机配置成功', 'data' => ''];					
			} else {
				$data = ['status' => 0, 'info' => '变更测试机配置失败', 'data' => ''];					
			}
			return $data;
			
		} else {
			#如果为自有机器
			if( $TestServerQuery->servicerprovider == 0) 
			{
				#新的ip需要绑定的
				if( !empty($result_bangding) ) {
					$updatePdtIPRes = $PdtIpModel->updateAll(['status' => 0],['ip'=>$result_bangding]);							
				}
				#需要解除绑定的IP
				if( !empty($result_jiechu) ) {
					$updatePdtIPRes = $PdtIpModel->updateAll(['status' => 1],['ip'=>$result_jiechu]);						
				}
			}

			$TestServerQuery->status = '0';
			
			if($TestServerQuery->save()) {
				$data = ['status' => 1, 'info' => '变更测试机配置操作拒绝成功', 'data' => ''];	
			} else {
				$data = ['status' => 0, 'info' => '变更测试机配置操作拒绝失败', 'data' => ''];
			}
			return $data;
			
		}
	}
	
	#新增账单的公共方法
	public static function NewBill($flow_id) {
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$TradeMainModel = new TradeMain();
		$TradeDetailModel = new TradeDetail();
		$PdtManageModel = new PdtManage();
		$PdtManageTypeModel = new PdtManageType();
		$MemberPdtModel = new MemberPdt();
		
		#设置确定收款时间的组合 
		$operation_arr = ['新购-线下打款','新购-在线支付','更换机器-线下打款', '更换机器-在线支付','变更配置-在线支付', '变更配置-线下打款'];#after
		$renew_arr = ['续费-线下打款', '续费-在线支付'];
		$Recharge_arr = ['用户金额变动-打款'];
		#线下打款的集合
		$underline_arr = ['新购-线下打款', '更换机器-线下打款','变更配置-线下打款', '续费-线下打款', '用户金额变动-打款'];
		#在线支付的集合
		$online_arr = ['新购-在线支付', '更换机器-在线支付','变更配置-在线支付', '续费-在线支付'];
		$BigArr = array_merge($operation_arr, $renew_arr, $Recharge_arr);
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		$WorkFlowDetailList = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->all();
		
		$moneyInfo = json_decode($WorkFlowRes['flow_money_info'], true);
		#如果是需要账单的
		if( in_array($WorkFlowRes['flow_name'], $BigArr) ) 
		{
			$PostData = [];
			#拆分
			$flowtype = explode('-', $WorkFlowRes['flow_name'])[0];
			
			#先获取信息
			$PostData['usermail'] = $WorkFlowRes['flow_account']; #用户账户
			$PostData['username'] = $WorkFlowRes['flow_username']; #用户账户
			if( in_array($WorkFlowRes['flow_name'], $Recharge_arr) ) #如果只是充值 是没有订单信息的 也没有机器信息 
			{
				$PostData['total_order_price'] = $WorkFlowRes['flow_total_money'];  #充值的金额
				$PostData['total_discount_price'] = 0; 								#优惠金额 = 订单应付金额 - 客户实际支付金额
				$PostData['total_fact_price'] = $moneyInfo['money_num'];  			#客户实际支付金额
				
				$PostData['bill_time'] = strtotime(date("Y-m-d", strtotime($moneyInfo['receivables_time'])));		#账单时间我们以财务确定金额的时间
			} else {
				#如果有订单，查询出主订单和订单详情
				if( $WorkFlowRes['flow_orderid'] != '' ) {
					$TradeMainRes = $TradeMainModel->find()->where(['trade_orderid' => $WorkFlowRes['flow_orderid']])->asArray()->one();
				}
				#先获取信息
				$PostData['usermail'] = $WorkFlowRes['flow_account']; #用户账户			
				$PostData['total_order_price'] = $TradeMainRes['trade_price_fact']; #账单总金额 以客户应付金额和计算  这里取的订单的应付金额
				$PostData['total_discount_price'] = $TradeMainRes['trade_price_fact'] - $moneyInfo['money_num']; #优惠金额 = 订单应付金额 - 客户实际支付金额
				$PostData['total_fact_price'] = $moneyInfo['money_num'];  #客户实际支付金额
				
				if( isset($moneyInfo['receivables_time']) ) {
					$PostData['bill_time'] = strtotime(date("Y-m-d", strtotime($moneyInfo['receivables_time'])));#账单时间我们以财务确定金额的时间
				} else {
					$PostData['bill_time'] = strtotime(date("Y-m-d", strtotime($moneyInfo['money_time'])));#账单时间我们以财务确定金额的时间
				}
				
			}
			#
			$PostData['bill_status'] = 1;
			$PostData['item'] = []; #子账单集合
			
			if( $flowtype == '新购') 
			{
				foreach($WorkFlowDetailList as $k => $value) 
				{
					$sub_details = [];
					$frontConfig = json_decode($value['flow_front_config'], true);
					$afterConfig = json_decode($value['flow_after_config'], true);
					#获取服务器分类
					$NewPdtManageTypeModel = clone $PdtManageTypeModel;
					$PdtManageTypeRes = $NewPdtManageTypeModel->find()->select('type_id,type_name')->where(['type_id' => $afterConfig['server_typeid']])->asArray()->one();
					$sub_details['server'] = $PdtManageTypeRes['type_name'];
					$sub_details['bill_type'] = '新购';
					$sub_details['ip'] = $afterConfig['ip'];
					$sub_details['start_time'] = $afterConfig['start_date']; #开始时间
					$sub_details['end_time'] = date('Y-m-d', DataHelper::getRenewEndtime(strtotime($afterConfig['start_date']), $frontConfig['payment_cycle'])); #到期时间
					$sub_details['pay_cycle'] = $frontConfig['payment_cycle']; #付费周期
					$sub_details['ground_time'] = $afterConfig['start_date']; #上架时间
					
					#查询出配置分类
					$NewPdtManageModel = clone $PdtManageModel;
					$PdtManageRes = $NewPdtManageModel->find()->select('id,name')->where(['id' => $afterConfig['pdt_id']])->asArray()->one();
					$sub_details['config_type'] = $PdtManageRes['name'];
					#服务器配置
					$sub_details['config_defense'] = $afterConfig['config']['defense'];
					if( isset($afterConfig['config']['requirement_bandwidth']) ) {
						$sub_details['config_bandwidth'] = $afterConfig['config']['requirement_bandwidth'];
					} else {
						$sub_details['config_bandwidth'] = $afterConfig['config']['configbandwidth'];
					}
					$sub_details['config_other'] = $afterConfig['config']['cpu'].'/'.$afterConfig['config']['ram'].'/'.$afterConfig['config']['hdd'].'/'.$afterConfig['config']['ipnumber'];
					#金额
					if(isset($frontConfig['renew_num'])) {
						$renewNum = $frontConfig['renew_num'];
					} else {
						$renewNum = 1;
					}			
					$sub_details['order_price'] = $value['flow_fact_money']; #flow_fact_money其实应该叫做应付金额   flow_normal_money为订单的标准价格。这里取应付金额
					$sub_details['fact_price'] = ($TradeMainRes['trade_pay_money'] / $TradeMainRes['trade_price_fact'] * $value['flow_fact_money']) * $renewNum;  #$value['flow_fact_money'];					
					$sub_details['discount_price'] = $sub_details['order_price'] - $sub_details['fact_price'];#优惠金额 = 应付金额 - 实际支付金额					
					#订单备注
					$sub_details['remark'] = $value['flow_order_remark'];
					
					$PostData['item'][] = $sub_details;
				}
			} else if( $flowtype == '更换机器') {
				foreach($WorkFlowDetailList as $k => $value) 
				{
					$sub_details = [];
					$frontConfig = json_decode($value['flow_front_config'], true);
					$afterConfig = json_decode($value['flow_after_config'], true);
					$NewMemberPdtModel = clone $MemberPdtModel;
					$MemberPdtRes = $NewMemberPdtModel->find()->select('start_time,end_time')->where(['unionid' => $afterConfig['unionid']])->asArray()->one();
					#获取服务器分类
					$NewPdtManageTypeModel = clone $PdtManageTypeModel;
					$PdtManageTypeRes = $NewPdtManageTypeModel->find()->select('type_id,type_name')->where(['type_id' => $afterConfig['server_typeid']])->asArray()->one();
					$sub_details['server'] = $PdtManageTypeRes['type_name'];
					$sub_details['bill_type'] = '更换机器';	
					$sub_details['ip'] = $afterConfig['ip'];
					$sub_details['start_time'] = date("Y-m-d",time()); 						#以当前时间
					$sub_details['end_time'] = date('Y-m-d', $MemberPdtRes['end_time']);	#以机器的到期时间
					$sub_details['pay_cycle'] = $afterConfig['payment_cycle']; 				#付费周期
					$sub_details['ground_time'] = date("Y-m-d",time());					    #以更换机器时间为上架
					
					#查询出配置分类
					$NewPdtManageModel = clone $PdtManageModel;
					$PdtManageRes = $NewPdtManageModel->find()->select('id,name')->where(['id' => $afterConfig['pdt_id']])->asArray()->one();
					$sub_details['config_type'] = $PdtManageRes['name'];
					#服务器配置
					$sub_details['config_defense'] = $afterConfig['config']['defense'];
					if( isset($afterConfig['config']['requirement_bandwidth']) ) {
						$sub_details['config_bandwidth'] = $afterConfig['config']['requirement_bandwidth'];
					} else {
						$sub_details['config_bandwidth'] = $afterConfig['config']['configbandwidth'];
					}
					$sub_details['config_other'] = $afterConfig['config']['cpu'].'/'.$afterConfig['config']['ram'].'/'.$afterConfig['config']['hdd'].'/'.$afterConfig['config']['ipnumber'];
					#金额
					if(isset($frontConfig['renew_num'])) {
						$renewNum = $frontConfig['renew_num'];
					} else {
						$renewNum = 1;
					}
					$sub_details['order_price'] = $value['flow_fact_money']; 					#flow_fact_money其实应该叫做应付金额   flow_normal_money为订单的标准价格。这里取应付金额
					$sub_details['fact_price'] = ($TradeMainRes['trade_pay_money'] / $TradeMainRes['trade_price_fact'] * $value['flow_fact_money']) * $renewNum;  #$value['flow_fact_money'];					
					$sub_details['discount_price'] = $sub_details['order_price'] - $sub_details['fact_price'];#优惠金额 = 应付金额 - 实际支付金额					
					#订单备注
					$sub_details['remark'] = $value['flow_order_remark'];
					
					$PostData['item'][] = $sub_details;
				}
				
			} else if($flowtype == '变更配置' ) {
				foreach($WorkFlowDetailList as $k => $value) 
				{
					$sub_details = [];
					$frontConfig = json_decode($value['flow_front_config'], true);
					$afterConfig = json_decode($value['flow_after_config'], true);
					$NewMemberPdtModel = clone $MemberPdtModel;
					$MemberPdtRes = $NewMemberPdtModel->find()->select('start_time,end_time')->where(['unionid' => $afterConfig['unionid']])->asArray()->one();
					#获取服务器分类
					$NewPdtManageTypeModel = clone $PdtManageTypeModel;
					$PdtManageTypeRes = $NewPdtManageTypeModel->find()->select('type_id,type_name')->where(['type_id' => $afterConfig['server_typeid']])->asArray()->one();
					$sub_details['server'] = $PdtManageTypeRes['type_name'];
					$sub_details['bill_type'] = '变更配置';	
					$sub_details['ip'] = $afterConfig['ip'];
					$sub_details['start_time'] = date("Y-m-d",time()); 						#以当前时间
					$sub_details['end_time'] = date('Y-m-d', $MemberPdtRes['end_time']);	#以机器的到期时间
					$sub_details['pay_cycle'] = $afterConfig['payment_cycle']; 				#付费周期
					$sub_details['ground_time'] = '';
					
					#查询出配置分类
					$NewPdtManageModel = clone $PdtManageModel;
					$PdtManageRes = $NewPdtManageModel->find()->select('id,name')->where(['id' => $afterConfig['pdt_id']])->asArray()->one();
					$sub_details['config_type'] = $PdtManageRes['name'];
					#服务器配置
					$sub_details['config_defense'] = $afterConfig['config']['defense'];
					if( isset($afterConfig['config']['requirement_bandwidth']) ) {
						$sub_details['config_bandwidth'] = $afterConfig['config']['requirement_bandwidth'];
					} else {
						$sub_details['config_bandwidth'] = $afterConfig['config']['configbandwidth'];
					}
					$sub_details['config_other'] = $afterConfig['config']['cpu'].'/'.$afterConfig['config']['ram'].'/'.$afterConfig['config']['hdd'].'/'.$afterConfig['config']['ipnumber'];
					#金额
					if(isset($frontConfig['renew_num'])) {
						$renewNum = $frontConfig['renew_num'];
					} else {
						$renewNum = 1;
					}
					$sub_details['order_price'] = $value['flow_fact_money']; 					#flow_fact_money其实应该叫做应付金额   flow_normal_money为订单的标准价格。这里取应付金额
					$sub_details['fact_price'] = ($TradeMainRes['trade_pay_money'] / $TradeMainRes['trade_price_fact'] * $value['flow_fact_money']) * $renewNum;  #$value['flow_fact_money'];					
					$sub_details['discount_price'] = $sub_details['order_price'] - $sub_details['fact_price'];#优惠金额 = 应付金额 - 实际支付金额					
					#订单备注
					$sub_details['remark'] = $value['flow_order_remark'];
					
					$PostData['item'][] = $sub_details;
				}
			} else if($flowtype == '续费' ) {
				foreach($WorkFlowDetailList as $k => $value) 
				{
					$sub_details = [];
					
					$frontConfig = json_decode($value['flow_front_config'], true);
					$afterConfig = json_decode($value['flow_after_config'], true);
					#获取服务器分类
					$NewPdtManageTypeModel = clone $PdtManageTypeModel;
					$PdtManageTypeRes = $NewPdtManageTypeModel->find()->select('type_id,type_name')->where(['type_id' => $afterConfig['server_typeid']])->asArray()->one();
					$sub_details['server'] = $PdtManageTypeRes['type_name'];
					$sub_details['bill_type'] = '续费';	
					$sub_details['ip'] = $afterConfig['ip'];
					$sub_details['start_time'] = date('Y-m-d', $afterConfig['old_end_time']); #续费前的到期时间
					$sub_details['end_time'] = date('Y-m-d', $afterConfig['new_end_time']);	  #续费后的到期时间
					$sub_details['pay_cycle'] = $afterConfig['payment_cycle'];  #续费周期
					$sub_details['ground_time'] = '';
					
					#查询出配置分类
					$NewPdtManageModel = clone $PdtManageModel;
					$PdtManageRes = $NewPdtManageModel->find()->select('id,name')->where(['id' => $afterConfig['pdt_id']])->asArray()->one();
					$sub_details['config_type'] = $PdtManageRes['name'];
					##服务器配置
					$sub_details['config_defense'] = $afterConfig['config']['defense'];
					if( isset($afterConfig['config']['requirement_bandwidth']) ) {
						$sub_details['config_bandwidth'] = $afterConfig['config']['requirement_bandwidth'];
					} else {
						$sub_details['config_bandwidth'] = $afterConfig['config']['configbandwidth'];
					}
					
					$sub_details['config_other'] = $afterConfig['config']['cpu'].'/'.$afterConfig['config']['ram'].'/'.$afterConfig['config']['hdd'].'/'.$afterConfig['config']['ipnumber'];
					#金额
					$renewNum = $frontConfig['renew_num'] ? $frontConfig['renew_num'] : 1;			
					$sub_details['order_price'] = $value['flow_fact_money']; #flow_fact_money其实应该叫做应付金额   flow_normal_money为订单的标准价格。这里取应付金额
					#用户支付金额 除以 实际支付金额 再乘以 实际出售价格 最后乘以续费数
					$sub_details['fact_price'] = ($TradeMainRes['trade_pay_money'] / $TradeMainRes['trade_price_fact'] * $value['flow_fact_money']) * $renewNum;  #$value['flow_fact_money'];					
					$sub_details['discount_price'] = $sub_details['order_price'] - $sub_details['fact_price'];	#优惠金额 = 应付金额 - 实际支付金额			
					#备注
					$sub_details['remark'] = $value['flow_order_remark'];
					
					$PostData['item'][] = $sub_details;
				}
				
			} else if($flowtype == '用户金额变动' ) {
				$sub_details = [];
				$sub_details['server'] = '';
				$sub_details['bill_type'] = '用户充值';				
				$sub_details['ip'] = [];
				$sub_details['start_time'] = '';
				$sub_details['end_time'] = '';
				$sub_details['pay_cycle'] = '';
				$sub_details['ground_time'] = '';
				
				$sub_details['config_type'] = '';
				$sub_details['config_defense'] = '';
				$sub_details['config_bandwidth'] = '';
				$sub_details['config_other'] = '';
				#金额
				$sub_details['order_price'] = $WorkFlowRes['flow_total_money']; 
				$sub_details['fact_price'] =  $WorkFlowRes['flow_total_money'];
				$sub_details['discount_price'] = $sub_details['order_price'] - $sub_details['fact_price'];
				#备注
				$sub_details['remark'] = '';
				
				$PostData['item'][] = $sub_details;
			}
			
			
			#线下打款一定需要账单
			if( in_array($WorkFlowRes['flow_name'], $underline_arr) && !empty($PostData) ) 
			{
				#根据选择线下支付方式，将账单发往
				if( isset($moneyInfo['money_platform']) && $moneyInfo['money_platform']  != '' ) 
				{
					if( in_array($moneyInfo['money_platform'], ['蓝调账单YedPay支付', '蓝调账单联动优势支付']) ) {
						#这里代表那边已经有账单了，无需再进行账单信息传输
					} else {
						$PaymentAccountModel = new PaymentAccount();
						$PaymentAccountRes = $PaymentAccountModel->find()->where(['account_name' => $moneyInfo['money_platform']])->asArray()->one();
						if( !empty($PaymentAccountRes)) 
						{
							$PostData['payment_type'] = $moneyInfo['money_platform']; #线下的支付方式
							$apply_site = explode(',', $PaymentAccountRes['apply_site']);
							if(in_array('蓝调', $apply_site)) 
							{
								$RequestRes = DataHelper::sendJsonRequest(Yii::$app->params['Bill_interface']['api_url'], json_encode($PostData));
								$RequestResArr = json_decode($RequestRes, true);
								if($RequestResArr['status'] != 1) 
								{
									$data = ['status' => 0, 'info' => $RequestResArr['msg'], 'data' => ''];											
								} else {
									$data = ['status' => 1, 'info' => '账单新增成功', 'data' => ''];
								}								
							} else if(in_array('未定', $apply_site)) {
								$RequestRes = self::AddBill($PostData);
								if( $RequestRes['status'] ) {
									$data = ['status' => 1, 'data' => ''];
								} else {
									$data = ['status' => 0, 'data' => ''];
								}
							} else {
								$data = ['status' => 1, 'data' => ''];
							}
							return $data;
							
						}
					}
				}
				$data = ['status' => 1, 'data' => ''];
				return $data;
			} else if( in_array($WorkFlowRes['flow_name'], $online_arr) && !empty($PostData) ) {
				$RequestRes = self::AddBill($PostData);
				if( $RequestRes['status'] ) {
					$data = ['status' => 1, 'data' => ''];
				} else {
					$data = ['status' => 0, 'data' => ''];
				}
				return $data;
			}
			
		} else { #否则直接返回true
			$data = ['status' => 1, 'data' => ''];
		}
		return $data;
		
	}
	
	#添加账单
	public static function AddBill($PostData) {
		
		if( empty($PostData) ) {
			$data = ['status' => 0, 'info' => '参数异常'];
			return $data;
		}
		
		$BillManageModel = new BillManage();
		$BillManageMainModel = new BillManageMain();
		
		if( !isset($PostData['batch_id']) || $PostData['batch_id'] == '' ) {
			$batch_id = 'B'.date("YmdHis", time()).rand(10000, 99999);
		} else {
			$batch_id = $PostData['batch_id'];
		}
		
		$BillManageMainModel->batch_id = $batch_id;
		$BillManageMainModel->usermail = $PostData['usermail'];
		$BillManageMainModel->username = $PostData['username'];
		$BillManageMainModel->total_order_price = $PostData['total_order_price'];
		$BillManageMainModel->total_discount_price = $PostData['total_discount_price'];
		$BillManageMainModel->total_fact_price = $PostData['total_fact_price'];
		$BillManageMainModel->bill_time = $PostData['bill_time'];
		$BillManageMainModel->bill_status = $PostData['bill_status'];
		
		$key_name = ['batch_id','bill_type','server','ip','start_time','end_time','pay_cycle','ground_time','config_type','config_defense','config_bandwidth','config_other','order_price','discount_price','fact_price','remark'];

		$ipArr = [];
		$data = [];
		
		$item = $PostData['item'];#print_r($item);exit;
		foreach($item as $key=>$val)
		{
			$post = [];
			$payment_cycle = '';
			if( isset($val['pay_cycle']) ) {
				if($val['pay_cycle'] == 1) {
					$payment_cycle = '月付';
				} else if($val['pay_cycle'] == 3) {
					$payment_cycle = '季付';
				} else if($val['pay_cycle'] == 6) {
					$payment_cycle = '半年付';
				} else if($val['pay_cycle'] == 12) {
					$payment_cycle = '年付';
				}
			} else {
				$payment_cycle = '';
			}
			
			
			$post['batch_id'] = $batch_id;
			$post['bill_type'] = $val['bill_type'];
			$post['server'] = $val['server'];
			
			if( empty($ipArr) ) {
				$ipArr = $val['ip'];
			} else {
				$ipArr = array_merge($val['ip'], $ipArr);
			}
			$post['ip'] = json_encode($val['ip']);
			
			$post['start_time'] = $val['start_time'];
			$post['end_time'] = $val['end_time'];
			$post['pay_cycle'] = $payment_cycle;
			$post['ground_time'] = $val['ground_time'];
			
			$post['config_type'] = $val['config_type'];
			$post['config_defense'] = $val['config_defense'];
			$post['config_bandwidth'] = $val['config_bandwidth'];
			
			$post['config_other'] = $val['config_other'];
			$post['order_price'] = $val['order_price'];
			$post['discount_price'] = $val['discount_price']; 
			$post['fact_price'] = $val['fact_price']; 
			$post['remark'] = $val['remark']; 
	
			$data[] = $post;
		}
		$BillManageMainModel->ip = json_encode($ipArr);
		#批量新增
		$inserRes = Yii::$app->db->createCommand()->batchInsert(BillManage::tableName(), $key_name, $data)->execute();
		
		#支付统计
		/*$BillStatisticsModel->batch_id = $batch_id;
		$BillStatisticsModel->payment_amount = $newReport['total_fact_price'];
		$BillStatisticsModel->payment_time = $newReport['bill_time'];
		$BillStatisticsModel->payment_type = $newReport['payment_type'];
		$BillStatisticsModel->usermail = $newReport['usermail'];
		$BillStatisticsModel->insert()  */
		
		if( $inserRes == count($item) && $BillManageMainModel->insert() ) 
		{
			$data = ['status' => 1, 'msg' => '新增账单成功'];
		} else {
			$data = ['status' => 0, 'msg' => '新增账单失败'];
		}
		return $data;
	}
	
}