<?php

namespace addons\VymGeneral\merchant\controllers;

use common\helpers\ArrayHelper;
use Yii;
use yii\web\NotFoundHttpException;
use common\enums\StatusEnum;
use common\models\base\SearchModel;
use common\helpers\ResultHelper;
use addons\VymGeneral\common\models\TicketCategory;

/**
 * 工单分类控制器
 *
 * Class TicketCategoryController
 * @package addons\VymGeneral\merchant\controllers
 */
class TicketCategoryController extends BaseController
{
    /**
     * 首页
     *
     * @return string
     * @throws \yii\web\NotFoundHttpException
     */
    public function actionIndex()
    {
        $searchModel = new SearchModel([
            'model' => TicketCategory::class,
            'scenario' => 'default',
            'partialMatchAttributes' => ['title'], // 模糊查询
            'defaultOrder' => [
                'sort' => SORT_ASC,
                'id' => SORT_ASC,
            ],
            'pageSize' => $this->pageSize,
        ]);

        $dataProvider = $searchModel
            ->search(Yii::$app->request->queryParams);
        $dataProvider->query
            ->andWhere(['>=', 'status', StatusEnum::DISABLED])
            ->andFilterWhere(['merchant_id' => $this->getMerchantId()]);

        return $this->render($this->action->id, [
            'dataProvider' => $dataProvider,
            'searchModel' => $searchModel,
            'dropDownList' => Yii::$app->vymGeneralService->ticketCategory->getDropDownList($this->getMerchantId()),
        ]);
    }

    /**
     * 创建
     *
     * @return string|\yii\web\Response
     */
    public function actionCreate()
    {
        $model = new TicketCategory();
        $model->status = StatusEnum::ENABLED;
        $model->sort = 50;
        
        if (Yii::$app->request->isPost) {
            $model->load(Yii::$app->request->post());
            if (Yii::$app->vymGeneralService->ticketCategory->create($model, $this->getMerchantId())) {
                return $this->redirect(['index']);
            }
        }
        
        return $this->render('edit', [
            'model' => $model,
            'dropDownList' => Yii::$app->vymGeneralService->ticketCategory->getDropDownList($this->getMerchantId()),
        ]);
    }

    /**
     * 编辑
     *
     * @param $id
     * @return string|\yii\web\Response
     * @throws NotFoundHttpException
     */
    public function actionEdit($id)
    {
        $model = $this->findModel($id);
        
        if (Yii::$app->request->isPost) {
            $model->load(Yii::$app->request->post());
            if (Yii::$app->vymGeneralService->ticketCategory->update($model)) {
                return $this->redirect(['index']);
            }
        }
        
        return $this->render('edit', [
            'model' => $model,
            'dropDownList' => Yii::$app->vymGeneralService->ticketCategory->getDropDownList($this->getMerchantId()),
        ]);
    }

    /**
     * 删除
     *
     * @param $id
     * @return mixed
     * @throws NotFoundHttpException
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        $model->status = StatusEnum::DELETE;
        
        if ($model->save()) {
            return $this->redirect(['index']);
        }
        
        return $this->redirect(['index']);
    }

    /**
     * 状态修改
     *
     * @param $id
     * @return mixed
     * @throws NotFoundHttpException
     */
    public function actionStatus($id)
    {
        $model = $this->findModel($id);
        $model->status = $model->status == StatusEnum::ENABLED ? StatusEnum::DISABLED : StatusEnum::ENABLED;
        
        if ($model->save()) {
            return $this->redirect(['index']);
        }
        
        return $this->redirect(['index']);
    }

    public function actionAjaxUpdate()
    {
        $id = Yii::$app->request->get('id');
        if (!($model = $this->findModel($id))) {
            return ResultHelper::json(404, '找不到数据');
        }

        $model->attributes = ArrayHelper::filter(Yii::$app->request->get(), ['sort', 'status']);
        if (!$model->save()) {
            return ResultHelper::json(422, $this->getError($model));
        }

        return ResultHelper::json(200, '修改成功');
    }

    /**
     * 排序
     *
     * @param $id
     * @param $sort
     * @return array
     * @throws NotFoundHttpException
     */
    public function actionSort($id, $sort)
    {
        $model = $this->findModel($id);
        $model->sort = $sort;
        
        if ($model->save()) {
            return ResultHelper::json(200, '排序修改成功');
        }
        
        return ResultHelper::json(422, '排序修改失败');
    }

    /**
     * 查找模型
     *
     * @param $id
     * @return TicketCategory
     * @throws NotFoundHttpException
     */
    protected function findModel($id)
    {
        if (empty($id) || empty(($model = TicketCategory::findOne(['id' => $id, 'merchant_id' => $this->getMerchantId()])))) {
            throw new NotFoundHttpException('找不到工单分类');
        }
        
        return $model;
    }
}
