<?php
namespace addons\VymDesen\backend\controllers;

use addons\VymDesen\backend\models\IdlePdt;
use addons\VymDesen\backend\models\IpManagement\PdtIpClass;
use addons\VymDesen\backend\models\IpManagement\PdtIpNetwork;
use addons\VymDesen\backend\models\IpManagement\SupplierIp;
use addons\VymDesen\backend\models\Provider;
use addons\VymDesen\backend\models\TestServer;
use addons\VymDesen\backend\models\UserAdmin\UserAdmin;
use addons\VymDesen\backend\models\WorkFlow\WorkFlow;
use addons\VymDesen\backend\models\WorkFlow\WorkFlowDetail;
use addons\VymDesen\backend\models\WorkFlow\WorkFlowRole;
use addons\VymDesen\backend\controllers\BaseController;
use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\components\RevenueNotes;
use addons\VymDesen\common\components\WorkFlowHandle;
use addons\VymDesen\common\models\Member\MemberPdt;
use addons\VymDesen\common\models\MoneyAccountRecord;
use addons\VymDesen\common\models\NewTrade\TradeDetail;
use addons\VymDesen\common\models\NewTrade\TradeMain;
use addons\VymDesen\common\models\Pdt\PdtIp;
use addons\VymDesen\common\models\Pdt\PdtManage;
use addons\VymDesen\common\models\Pdt\PdtManageType;
use addons\VymDesen\common\models\Pdt\PdtRoomMange;
use addons\VymDesen\common\models\Pdt\SwitchLine;
use addons\VymDesen\common\models\UserMember\UserMember;
use Yii;
use yii\db\Expression;

#导出扩展

class WorkFlowController extends BaseController {
	
	#所有流程列表
	public function actionIndex() {
		#验证账户可查看权限
		$adminRole = Yii::$app->session['admin']['roles'];
		$adminRoleArr = Yii::$app->session['auth']['roles'];#数组形式
		
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		
		$where[] = 'or';
		$i = 0;
		foreach($adminRoleArr as $v){
			$i++;
			$where[] = new Expression("FIND_IN_SET(:wr_canlook_group_$i, wr_canlook_group)",[":wr_canlook_group_$i" => $v]);
		}

		#基础QueryModel
		$WorkFlowQuery = $WorkFlowModel->find()->joinWith('flowrole')->where($where)->andwhere(['in', 'flow_status', ['处理完成', '处理驳回', '中止事务完成']])->andwhere(['not', ['flow_end_time' => null]]);

		#$WorkFlowQuery = $WorkFlowModel->find()->joinWith('flowrole')->where(new Expression('FIND_IN_SET(:wr_canlook_group, wr_canlook_group)', [':wr_canlook_group' => $adminRole]))->andwhere(['in', 'flow_status', ['处理完成', '处理驳回', '中止事务完成']])->andwhere(['not', ['flow_end_time' => null]]);
		
		#如果查询IP存在，查询附表
		if($this->get('flow_ip')) {
			$ipRes = $WorkFlowDetailModel->find()->filterWhere(['like', "flow_after_config", $this->get('flow_ip')])->orFilterWhere(['like', "flow_ip2", $this->get('flow_ip')])->asArray()->all();
			if($ipRes) {
				$flow_id_list = [];
				foreach($ipRes as $key => $val) {
					if(!in_array($val['flow_id'], $flow_id_list)) {
						$flow_id_list[] = $val['flow_id'];
					}
				}
				$WorkFlowQuery->andWhere(['in', 'flow_id', $flow_id_list]);
			}
		}
			
		$WorkFlowQuery->orderBy('flow_end_time desc');
			
		$WorkFlowModel->createSearchWhere($WorkFlowQuery, $this->get());

		$iCount = $WorkFlowQuery->count();
    	$oPage = DataHelper::getPage($iCount);
    	
    	$listRes = $WorkFlowQuery->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();
		
		#进度查询
		foreach($listRes as $key => $val) {
			#初始化
			$nowDetailRes = [];
			$nowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $val['flow_id']])->asArray()->all();
			
			$dataCount = [
				'allCount' => 0,
				'takeOver' => 0,
				'finish' => 0
			];
			
			if(!$nowDetailRes) {
				$listRes[$key]['process'] = $dataCount;
				continue;
			}
			
			$dataCount['allCount'] = count($nowDetailRes);

			foreach($nowDetailRes as $k => $v) {
				if($v['flow_fix_confirm_time']) {
					$dataCount['takeOver']++;
				}
				
				if($v['flow_fix_finish_time']) {
					$dataCount['finish']++;
				}
			}
			
			$listRes[$key]['process'] = $dataCount;
		}
		
		#查询销售
		$UserAdminModel = new UserAdmin();
        $adminlist_sales = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 4])->asArray()->all();
		$adminlist_saleleader = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 7])->asArray()->all();
		$UserAdminRes = array_merge($adminlist_sales, $adminlist_saleleader);
		
		#查询流程名称
		$WorkFlowRoleModel = new WorkFlowRole();
		$FlowRoles = $WorkFlowRoleModel->find()->asArray()->all();
		
		return $this->render('index', ['list'=>$listRes,'iCount'=>$iCount,'page'=>$oPage, 'adminlist' => $UserAdminRes, 'flow_list' => $FlowRoles]);
	}
	
	#后付款流程列表
	public function actionPaylaterFlow() {
		#验证账户可查看权限
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminRole = Yii::$app->session['admin']['roles']; #字符串形式  以逗号连接
		$adminRoleArr = Yii::$app->session['auth']['roles'];#数组形式
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		
		$where[] = 'or';
		$i = 0;
		foreach($adminRoleArr as $v){
			$i++;
			$where[] = new Expression("FIND_IN_SET(:wr_canlook_group_$i, wr_canlook_group)",[":wr_canlook_group_$i" => $v]);
		}
		#基础QueryModel
		$WorkFlowQuery = $WorkFlowModel->find()->joinWith('flowrole')->where($where)->andwhere(['in', 'flow_status', ['事务处理中', '中止事务中', '处理驳回']])->andwhere('locate(:flow_name, flow_name)', [':flow_name'=>'后付款'])->andwhere(['flow_end_time' => null]);
		
		#如果查询IP存在，查询附表
		if($this->get('flow_ip')) {
			$ipRes = $WorkFlowDetailModel->find()->filterWhere(['like', "flow_after_config", $this->get('flow_ip')])->orFilterWhere(['like', "flow_ip2", $this->get('flow_ip')])->asArray()->all();
			if($ipRes) {
				$flow_id_list = [];
				foreach($ipRes as $key => $val) {
					if(!in_array($val['flow_id'], $flow_id_list)) {
						$flow_id_list[] = $val['flow_id'];
					}
				}
				$WorkFlowQuery->andWhere(['in', 'flow_id', $flow_id_list]);
			}
		}

		#如果是技术人员进入，只查询技术未处理完成的流程
		if( $adminRole == 6) {
			$WorkFlowQuery->andWhere(['or', ['in','flow_operate_audit_status', ['等待审核', '无需审核']], ['in', 'flow_operate_isfinish', ['等待配置', '等待取消']]]);
		}	
		
		$WorkFlowQuery->orderBy('flow_start_time desc');
		
				
		$WorkFlowModel->createSearchWhere($WorkFlowQuery, $this->get());

		$iCount = $WorkFlowQuery->count();
    	$oPage = DataHelper::getPage($iCount);
    	
    	$listRes = $WorkFlowQuery->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();
		
		#进度查询
		foreach($listRes as $key => $val) {
			#初始化
			$nowDetailRes = [];
			$nowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $val['flow_id']])->asArray()->all();
			
			$dataCount = [
				'allCount' => 0,
				'takeOver' => 0,
				'finish' => 0
			];
			
			if(!$nowDetailRes) {
				$listRes[$key]['process'] = $dataCount;
				continue;
			}
			
			$dataCount['allCount'] = count($nowDetailRes);

			foreach($nowDetailRes as $k => $v) {
				if($v['flow_fix_confirm_time']) {
					$dataCount['takeOver']++;
				}
				
				if($v['flow_fix_finish_time']) {
					$dataCount['finish']++;
				}
			}
			
			$listRes[$key]['process'] = $dataCount;
		}
		

		#查询销售
		$UserAdminModel = new UserAdmin();
        $adminlist_sales = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 4])->asArray()->all();
		$adminlist_saleleader = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 7])->asArray()->all();
		$UserAdminRes = array_merge($adminlist_sales, $adminlist_saleleader);
		
		#查询流程名称
		$WorkFlowRoleModel = new WorkFlowRole();
		$FlowRoles = $WorkFlowRoleModel->find()->asArray()->all();
		
		return $this->render('paylater', ['list'=>$listRes,'iCount'=>$iCount,'page'=>$oPage, 'adminlist' => $UserAdminRes, 'flow_list' => $FlowRoles]);
	} 
	
	#我的流程
	public function actionMyFlow() {
		#验证账户可查看权限
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		
		#基础QueryModel
		$WorkFlowQuery = $WorkFlowModel->find()->joinWith('flowrole')->where(['or', ['flow_promter' => $adminName], ['flow_admin_id' => $adminId]]);
		
		#如果查询IP存在，查询附表
		if($this->get('flow_ip')) {
			$ipRes = $WorkFlowDetailModel->find()->filterWhere(['like', "flow_after_config", $this->get('flow_ip')])->orFilterWhere(['like', "flow_ip2", $this->get('flow_ip')])->asArray()->all();
			if($ipRes) {
				$flow_id_list = [];
				foreach($ipRes as $key => $val) {
					if(!in_array($val['flow_id'], $flow_id_list)) {
						$flow_id_list[] = $val['flow_id'];
					}
				}
				$WorkFlowQuery->andWhere(['in', 'flow_id', $flow_id_list]);
			}
		}
		
		$WorkFlowQuery->orderBy('flow_start_time desc');
			
		$WorkFlowModel->createSearchWhere($WorkFlowQuery, $this->get());

		$iCount = $WorkFlowQuery->count();
    	$oPage = DataHelper::getPage($iCount);
    	
    	$listRes = $WorkFlowQuery->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();

		#进度查询
		foreach($listRes as $key => $val) {
			#初始化
			$nowDetailRes = [];
			$nowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $val['flow_id']])->asArray()->all();
			
			$dataCount = [
				'allCount' => 0,
				'takeOver' => 0,
				'finish' => 0
			];
			
			if(!$nowDetailRes) {
				$listRes[$key]['process'] = $dataCount;
				continue;
			}
			
			$dataCount['allCount'] = count($nowDetailRes);

			foreach($nowDetailRes as $k => $v) {
				if($v['flow_fix_confirm_time']) {
					$dataCount['takeOver']++;
				}
				
				if($v['flow_fix_finish_time']) {
					$dataCount['finish']++;
				}
			}
			
			$listRes[$key]['process'] = $dataCount;
		}
		
		#查询销售
		$UserAdminModel = new UserAdmin();
        $adminlist_sales = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 4])->asArray()->all();
		$adminlist_saleleader = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 7])->asArray()->all();
		$UserAdminRes = array_merge($adminlist_sales, $adminlist_saleleader);
		
		#查询流程名称
		$WorkFlowRoleModel = new WorkFlowRole();
		$FlowRoles = $WorkFlowRoleModel->find()->asArray()->all();
		
		return $this->render('myflow', ['list'=>$listRes,'iCount'=>$iCount,'page'=>$oPage, 'adminlist' => $UserAdminRes, 'flow_list' => $FlowRoles]);
	}
	
	#我的处理
	public function actionMyHandle() {
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		$WorkFlowDetailModel = new WorkFlowDetail();
		
		#基础QueryModel
		$WorkFlowDetailQuery = $WorkFlowDetailModel->find()->where(['flow_fix_id' => $adminId]);
		if($this->get('flow_status')) {
			$WorkFlowDetailQuery->joinWith(["flow" => function($e) {
				$e->where(['flow_status' => $this->get('flow_status')]);
			}]);
		} else {
			$WorkFlowDetailQuery->joinWith('flow');
		}

		#如果查询IP存在，查询附表
		if($this->get('flow_ip')) {
			$WorkFlowDetailQuery->orFilterWhere(['like', "flow_after_config", $this->get('flow_ip')])->orFilterWhere(['like', "flow_ip2", $this->get('flow_ip')]);
		}
		
		$WorkFlowDetailModel->createSearchWhere($WorkFlowDetailQuery, $this->get());
		
		//var_dump($WorkFlowDetailQuery->asArray()->all());exit;
		
		$iCount = $WorkFlowDetailQuery->count();
    	$oPage = DataHelper::getPage($iCount);
    	$listRes = $WorkFlowDetailQuery->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();
		
		#查询流程名称
		$WorkFlowRoleModel = new WorkFlowRole();
		$FlowRoles = $WorkFlowRoleModel->find()->asArray()->all();
		
		return $this->render('myhandle', ['list'=>$listRes,'iCount'=>$iCount,'page'=>$oPage, 'flow_list' => $FlowRoles]);
	}
		
	#预配置列表
	public function actionPresetList() {
		
		#验证账户可查看权限
		$adminRole = Yii::$app->session['admin']['roles'];
		$adminRoleArr = Yii::$app->session['auth']['roles'];#数组形式
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		
		#基础QueryModel
		if(DataHelper::search_array($adminRoleArr, ['1','2'])) {
			$WorkFlowQuery = $WorkFlowModel->find()->joinWith('flowrole')->where(['flow_status' => '等待预配置']);			
		} else {
			$WorkFlowQuery = $WorkFlowModel->find()->joinWith('flowrole')->where(['flow_status' => '等待预配置', 'flow_admin_id' => $adminId]);
		}
		
		#如果查询IP存在，查询附表
		if($this->get('flow_ip')) {
			$ipRes = $WorkFlowDetailModel->find()->filterWhere(['like', "flow_after_config", $this->get('flow_ip')])->orFilterWhere(['like', "flow_ip2", $this->get('flow_ip')])->asArray()->all();
			if($ipRes) {
				$flow_id_list = [];
				foreach($ipRes as $key => $val) {
					if(!in_array($val['flow_id'], $flow_id_list)) {
						$flow_id_list[] = $val['flow_id'];
					}
				}
				$WorkFlowQuery->andWhere(['in', 'flow_id', $flow_id_list]);
			}
		}
		
		$WorkFlowQuery->orderBy('flow_start_time desc');
			
		$WorkFlowModel->createSearchWhere($WorkFlowQuery, $this->get());

		$iCount = $WorkFlowQuery->count();
    	$oPage = DataHelper::getPage($iCount);
    	
    	$listRes = $WorkFlowQuery->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();
		
		return $this->render('preset', ['list'=>$listRes,'iCount'=>$iCount,'page'=>$oPage]);
	}
	
	#待处理列表
	public function actionWaitingList() {
		#验证账户可查看权限
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminRole = Yii::$app->session['admin']['roles']; #字符串形式  以逗号连接
		$adminRoleArr = Yii::$app->session['auth']['roles'];#数组形式
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		
		$where[] = 'or';
		$i = 0;
		foreach($adminRoleArr as $v){
			$i++;
			$where[] = new Expression("FIND_IN_SET(:wr_canlook_group_$i, wr_canlook_group)",[":wr_canlook_group_$i" => $v]);
		}
		
		#基础QueryModel
		if(in_array($adminRole, [3,15,17])) {
			$WorkFlowQuery = $WorkFlowModel->find()->joinWith('flowrole')->where($where)->andwhere(['in', 'flow_status', ['事务处理中', '中止事务中', '处理驳回']])->andwhere(['not in', 'flow_name', ['新购-后付款', '续费-后付款', '变更配置-后付款', '更换机器-后付款']])->andwhere(['flow_end_time' => null]);
		} else {
			$WorkFlowQuery = $WorkFlowModel->find()->joinWith('flowrole')->where($where)->andwhere(['in', 'flow_status', ['事务处理中', '中止事务中', '处理驳回']])->andwhere(['flow_end_time' => null]);
		}

		#如果查询IP存在，查询附表
		if($this->get('flow_ip')) {
			$ipRes = $WorkFlowDetailModel->find()->filterWhere(['like', "flow_after_config", $this->get('flow_ip')])->orFilterWhere(['like', "flow_ip2", $this->get('flow_ip')])->asArray()->all();
			if($ipRes) {
				$flow_id_list = [];
				foreach($ipRes as $key => $val) {
					if(!in_array($val['flow_id'], $flow_id_list)) {
						$flow_id_list[] = $val['flow_id'];
					}
				}
				$WorkFlowQuery->andWhere(['in', 'flow_id', $flow_id_list]);
			}
		}

		#如果是技术人员进入，只查询技术未处理完成的流程
		if( in_array($adminRole, [5, 6, 19])) {
			$WorkFlowQuery->andWhere(['or', ['in','flow_operate_audit_status', ['等待审核', '无需审核']], ['in', 'flow_operate_isfinish', ['等待配置', '等待取消']]]);
		}	
		
		$WorkFlowQuery->orderBy('flow_start_time desc');
		
				
		$WorkFlowModel->createSearchWhere($WorkFlowQuery, $this->get());

		$iCount = $WorkFlowQuery->count();
    	$oPage = DataHelper::getPage($iCount);
    	
    	$listRes = $WorkFlowQuery->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();
		
		#进度查询
		foreach($listRes as $key => $val) {
			#初始化
			$nowDetailRes = [];
			$nowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $val['flow_id']])->asArray()->all();
			
			$dataCount = [
				'allCount' => 0,
				'takeOver' => 0,
				'finish' => 0
			];
			
			if(!$nowDetailRes) {
				$listRes[$key]['process'] = $dataCount;
				continue;
			}
			
			$dataCount['allCount'] = count($nowDetailRes);

			foreach($nowDetailRes as $k => $v) {
				if($v['flow_fix_confirm_time']) {
					$dataCount['takeOver']++;
				}
				
				if($v['flow_fix_finish_time']) {
					$dataCount['finish']++;
				}
			}
			
			$listRes[$key]['process'] = $dataCount;
		}
		

		#查询销售
		$UserAdminModel = new UserAdmin();
        $adminlist_sales = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 4])->asArray()->all();
		$adminlist_saleleader = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 7])->asArray()->all();
		$UserAdminRes = array_merge($adminlist_sales, $adminlist_saleleader);
		
		#查询流程名称
		$WorkFlowRoleModel = new WorkFlowRole();
		$FlowRoles = $WorkFlowRoleModel->find()->asArray()->all();
		
		return $this->render('waiting', ['list'=>$listRes,'iCount'=>$iCount,'page'=>$oPage, 'adminlist' => $UserAdminRes, 'flow_list' => $FlowRoles]);
		
	}
	
	#导出（只导出已经完成的）
	/**
     * 导出
     */
    public function actionExport(){
		
		#开启缓冲区
		ob_start();
		
		#无限循环不过期
		set_time_limit(0);
		
		#内存增大
		ini_set("memory_limit", "512M");
		
		$filename = md5(time()).'.csv';
		
		header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="'.$fileName.'.csv"');
        header('Cache-Control: max-age=0');
		
		#以写方式加入
		$handle = fopen("download/".$filename, 'a');
		#$handle = fopen("php://input", 'a');
		
		$dataHeadList = ['流程号', '流程名称', '用户名称', '用户账户', '销售名称', '机器业务号', '服务器分类', '配置类别', 'CPU', '内存大小','硬盘','带宽','客户要求带宽','IP数','防御流量','操作系统', '处理接收人', '处理接手时间', '处理完成时间','流程开始时间', '流程结束时间'];
		
		
		fputcsv($handle, $dataHeadList);
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$PdtManageModel = new PdtManage();
		$PdtManageTypeModel = new PdtManageType();
		
		$PdtManageList = $PdtManageModel->find()->asArray()->all();
		$PdtManageName_List = array_column($PdtManageList, 'name','id');
		
		$PdtManageTypeList = $PdtManageTypeModel->find()->asArray()->all();		
		$PdtManageTypeName_List = array_column($PdtManageTypeList, 'type_name','type_id');
		
		$time = strtotime('2019-10-1');
		$flowlist = $WorkFlowModel->find()->where(['flow_status' => '处理完成'])->andwhere('flow_start_time > '.$time)->orderBy('fid desc')->asArray()->all();
		
		$totalNum = count($flowlist);
		
		$flow_id_offset = 0;
		$flow_id_limit = 1000;
		
		$flow_id_muti = ceil($totalNum / $flow_id_limit);
		
		$dataGroup = [];
		for($i=0; $i<$flow_id_muti; $i++) {
			
			ob_flush();
			flush();
			
			$list = Yii::$app->db->createCommand("SELECT * FROM workflow_detail AS a LEFT JOIN workflow AS b ON b.flow_id = a.flow_id WHERE a.flow_id IN(SELECT t.flow_id FROM (SELECT flow_id FROM workflow WHERE flow_status = '处理完成' and flow_start_time > ".$time." order by fid desc LIMIT ".($flow_id_offset*$flow_id_limit).", ".$flow_id_limit.") AS t ) order by id desc")->queryAll();

			foreach($list as $key => $val) {				
				$after_config = json_decode($val['flow_after_config'], true);
				
				if( isset($after_config['server_typeid']) && $after_config['server_typeid'] != '' ) {
					$server_typeid = $PdtManageTypeName_List[$after_config['server_typeid']];
				} else {
					$server_typeid  = '';
				}
				if( isset($after_config['pdt_id']) && $after_config['pdt_id'] != '' ) {
					$pdt_id = $PdtManageName_List[$after_config['pdt_id']];
					
				} else {
					$pdt_id  = '';
				}
					
				if( $after_config['config'] && !empty($after_config['config']) && isset($after_config['config'])) {
					$config = $after_config['config'];
					$cpu = $config['cpu'];
					$ram = $config['ram'];
					$hdd = $config['hdd'];
					$configbandwidth = $config['configbandwidth'];
					if( isset($config['requirement_bandwidth']) ) {
						$requirement_bandwidth = $config['requirement_bandwidth'];
					} else {
						$requirement_bandwidth = '';
					}					
					$ipnumber = $config['ipnumber'];
					if( isset($config['defense']) ) {
						$defense = $config['defense'];
					} else {
						$defense = '';
					}
					$operatsystem = $config['operatsystem'];
				} else {
					$cpu = '';
					$ram = '';
					$hdd = '';
					$configbandwidth = '';
					$requirement_bandwidth = '';
					$ipnumber = '';
					$defense = '';
					$operatsystem = '';
				}
				
				if( isset($val['flow_fix_name']) && $val['flow_fix_name'] != null) {
					$flow_fix_name = $val['flow_fix_name'];
					$flow_fix_confirm_time = date('Y-m-d H:i:s', $val['flow_fix_confirm_time']);
					$flow_fix_finish_time = date('Y-m-d H:i:s', $val['flow_fix_finish_time']);
				} else {
					$flow_fix_name = '';
					$flow_fix_confirm_time = '';
					$flow_fix_finish_time = '';
				}	
				$data = [
					'flow_id' => $val['flow_id'],
					'flow_name' => $val['flow_name'],
					'flow_username' => $val['flow_username'],
					'flow_account' => $val['flow_account'],
					'flow_admin_name' => $val['flow_admin_name'],					
					'flow_unionid' => $val['flow_unionid'],
					'server_typeid' => $server_typeid,
					'pdt_id' => $pdt_id,					
					'cpu' => $cpu,
					'ram' => $ram,
					'hdd' => $hdd,
					'configbandwidth' => $configbandwidth,
					'requirement_bandwidth' => $requirement_bandwidth,
					'ipnumber' => $ipnumber,
					'defense' => $defense,
					'operatsystem' => $operatsystem,
					'flow_fix_name' => $flow_fix_name,
					'flow_fix_confirm_time' => $flow_fix_confirm_time,
					'flow_fix_finish_time' => $flow_fix_finish_time,
					'flow_start_time' => date('Y-m-d H:i:s', $val['flow_start_time']),
					'flow_end_time' => date('Y-m-d H:i:s', $val['flow_end_time'])
				];
				
				fputcsv($handle, $data);
			}
		}
		
		fclose($handle);
		
		header("Location:/download/".$filename);
		/*$flow_id_offset = 0;
		$flow_id_limit = 1000;
		$sql = "SELECT * FROM workflow_detail AS a LEFT JOIN workflow AS b ON b.flow_id = a.flow_id WHERE a.flow_id IN(SELECT t.flow_id FROM (SELECT flow_id FROM workflow WHERE flow_status = '处理完成' LIMIT 0, 5000) AS t )";
			
		$pdo = new\PDO( 'mysql:host=localhost;dbname=jiusu', '9sudba', 'GeM23HLdf3fTDF' );
		$pdo->setAttribute( \PDO::MYSQL_ATTR_USE_BUFFERED_QUERY, false);
		$rows = $pdo->query($sql);
		
		$filename = date('Ymd').'.csv';//设置文件名
		header('Content-Type:text/csv');
		header("Content-Disposition:attachment;filename={$filename}");
		$out = fopen('php://output','w');
		fputcsv(
			$out, [
				'id',
				'flow_id',
				'flow_unionid'
			]
		);
		foreach( $rows as $row ){
			$line=[
				$row['id'],
				$row['flow_id'],
				$row['flow_unionid']
			];

			fputcsv($out,$line);
		}

		fclose($out);
		$memory=round((memory_get_usage()-$startMemory)/1024/1024,3).'M'.PHP_EOL;
		file_put_contents('/tmp/test.txt',$memory,FILE_APPEND);
		exit;*/
		
        /*$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$PdtManageModel = new PdtManage();
		$PdtManageTypeModel = new PdtManageType();
		$time = strtotime('2019-11-1');
		
        $WorkFlowModelQuery = $WorkFlowModel->find();#->where("flow_name like '%续费%'");		
        $WorkFlowModel->createSearchWhere($WorkFlowModelQuery, $this->get());

        $arrRes = $WorkFlowModelQuery->where('flow_end_time > '.$time)->andWhere(['flow_status' => '处理完成'])->orderBy('fid desc')->asArray()->all(); #print_r($arrRes);exit;
		#$a = DataHelper::filter_by_value($arrRes, 'flow_id', 'FF183967095858');
		#print_r($a);exit;
		$Flowid_List = array_column($arrRes, 'flow_id'); #print_r($Flowid_List);exit;
		
		
		
		$DetailListQuery = $WorkFlowDetailModel->find()->where(['in', 'flow_id', $Flowid_List]);
		exit;
		$iCount = $DetailListQuery->count();
    	#$oPage = DataHelper::getPage($iCount, 100);    	
		$page = ceil($iCount/100);
		
		$DetailList = [];
		for( $i=0; $i< 2; $i++) 
		{
			$offset = ($i * 100);
			$limit = 100;
			
			$list = $DetailListQuery->offset($offset)->limit($limit)->asArray()->all();
			print_r($list);#exit;
			$DetailList = array_merge($DetailList, $list);
		}
		print_r($DetailList);exit;
    	
		
		
		
		foreach($DetailList->batch() as $A)
		{
			$DetailList1[] = $A;
		}
		print_r($DetailList1);exit;
		
		print_r($DetailList);exit;
		$PdtManageList = $PdtManageModel->find()->asArray()->all();
		$PdtManageTypeList = $PdtManageTypeModel->find()->asArray()->all();
		
		$data = [];
        foreach ($arrRes as $key=>$value)
		{
			#print_r($value);
			
			
			foreach($DetailList as $k=>$val) 
			{
				if( $val['flow_id'] == $value['flow_id']) 
				{
					$models= [];
					
					$models['flow_id'] = $value['flow_id'];			
					$models['flow_name'] = $value['flow_name'];
					$models['flow_username'] = $value['flow_username'];
					$models['flow_account'] = $value['flow_account'];
					$models['flow_admin_name'] = $value['flow_admin_name'];
					
					$flow_after_config = json_decode($val['flow_after_config'], true);
					if( isset($flow_after_config['server_typeid']) && $flow_after_config['server_typeid'] != '' ) {
						$key = array_search($flow_after_config['server_typeid'], $PdtManageTypeList);
						if($key) {
							$models['server_typeid'] = $PdtManageTypeList[$key]['type_name'];
						} else {
							$models['server_typeid'] = '';
						}
					} else {
						$models['server_typeid']  = '';
					}
					if( isset($flow_after_config['pdt_id']) && $flow_after_config['pdt_id'] != '' ) {
						$key = array_search($flow_after_config['pdt_id'], $PdtManageList);
						if($key) {
							$models['pdt_id'] = $PdtManageList[$key]['name'];
						} else {
							$models['pdt_id'] = '';
						}
					} else {
						$models['pdt_id']  = '';
					}
					
					$config = $flow_after_config['config'];				
					if(isset($config) && !empty($config) ) {
						$models['cpu'] = $config['cpu'];
						$models['ram'] = $config['ram'];
						$models['hdd'] = $config['hdd'];
						if( isset($config['configbandwidth']) ) {
							$models['configbandwidth'] = $config['configbandwidth'];
						} else {
							$models['requirement_bandwidth'] = '';
						}
						if( isset($config['requirement_bandwidth']) ) {
							$models['requirement_bandwidth'] = $config['requirement_bandwidth'];
						} else {
							$models['requirement_bandwidth'] = '';
						}
						if( isset($config['defense']) ) {
							$models['defense'] = $config['defense'];
						} else {
							$models['defense'] = '';
						}
						$models['operatsystem'] = $config['operatsystem'];
					
					} else {
						$models['config'] = '';
					}
					if( isset($val['flow_fix_name']) && $val['flow_fix_name'] != null) {
						$models['flow_fix_name'] = $val['flow_fix_name'];
						$models['flow_fix_confirm_time'] = date('Y-m-d H:i:s', $val['flow_fix_confirm_time']);
						$models['flow_fix_finish_time'] = date('Y-m-d H:i:s', $val['flow_fix_finish_time']);
					} else {
						$models['flow_fix_name'] = '';
						$models['flow_fix_confirm_time'] = '';
						$models['flow_fix_finish_time'] = '';
					}	
					$models['flow_start_time'] = date('Y-m-d H:i:s', $value['flow_start_time']);
					$models['flow_end_time'] = date('Y-m-d H:i:s', $value['flow_end_time']);
				}
				
				$data[] = $models;
				
			}            
        }
		
		print_r($data);exit;
		$newExcel = new Spreadsheet;
		$objSheet = $newExcel->getActiveSheet(0);  //获取当前操作sheet的对象
        $objSheet->setTitle('处理完成流程表');  //设置当前sheet的标题
		$newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
		#水平方向对齐
		$newExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
		#垂直方向对齐
		$newExcel->getDefaultStyle()->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
		
		#设置列宽
        $newExcel->getActiveSheet()->getColumnDimension('A')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('B')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('C')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('D')->setWidth(25);
        $newExcel->getActiveSheet()->getColumnDimension('E')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('F')->setWidth(40);
        $newExcel->getActiveSheet()->getColumnDimension('G')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('H')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('I')->setWidth(20);	
		
		$newExcel->getActiveSheet()->getColumnDimension('J')->setWidth(20);	
		$newExcel->getActiveSheet()->getColumnDimension('K')->setWidth(20);	
		
		#设置列名
		$objSheet->setCellValue('A1', '流程ID');
		$objSheet->setCellValue('B1', '流程类型');
		$objSheet->setCellValue('C1', '用户昵称');
		$objSheet->setCellValue('D1', '用户账户');
		$objSheet->setCellValue('E1', '销售/客服');
		$objSheet->setCellValue('F1', '产品配置');
		$objSheet->setCellValue('G1', '处理接手人');
		$objSheet->setCellValue('H1', '处理接手时间');
		$objSheet->setCellValue('I1', '处理完成时间');
		$objSheet->setCellValue('J1', '流程开始时间');
		$objSheet->setCellValue('K1', '流程完成时间');
		foreach($data as $key => $val) {
			
			$k = $key + 2;
			
			$objSheet->setCellValue('A' . $k, $val['flow_id']);
			$objSheet->setCellValue('B' . $k, $val['flow_name']);
			$objSheet->setCellValue('C' . $k, $val['flow_username']);
			$objSheet->setCellValue('D' . $k, $val['flow_account']);
			$objSheet->setCellValue('E' . $k, $val['flow_admin_name']);
			$objSheet->setCellValue('F' . $k, $val['config']);
			$objSheet->setCellValue('G' . $k, $val['flow_fix_name']);
			$objSheet->setCellValue('H' . $k, $val['flow_fix_confirm_time']);
			$objSheet->setCellValue('I' . $k, $val['flow_fix_finish_time']);
			$objSheet->setCellValue('J' . $k, $val['flow_start_time']);
			$objSheet->setCellValue('K' . $k, $val['flow_end_time']);			
		}
        //设置第一栏的标题
			
		header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
		header("Content-Disposition: attachment;filename=处理完成流程表_".date('Y-m-d', time()).'.'.strtolower("Xlsx"));
		header('Cache-Control: max-age=0');
		$objWriter = IOFactory::createWriter($newExcel, 'Xlsx');
		$objWriter->save('php://output');*/
		  
    }
	
	#待处理数
	public function actionWaitDoCount() {
		#验证账户可查看权限
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminRole = Yii::$app->session['admin']['roles'];
		$adminRoleArr = Yii::$app->session['auth']['roles'];#数组形式
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		
		$where[] = 'or';
		$i = 0;
		foreach($adminRoleArr as $v){
			$i++;
			$where[] = new Expression("FIND_IN_SET(:wr_canlook_group_$i, wr_canlook_group)",[":wr_canlook_group_$i" => $v]);
		}
		#基础QueryModel
		$WorkFlowQuery = $WorkFlowModel->find()->joinWith('flowrole')->where($where)->andwhere(['in', 'flow_status', ['事务处理中', '中止事务中', '处理驳回']])->andwhere(['flow_end_time' => null]);
		
		#如果是技术人员进入，只查询技术未处理完成的流程
		if($adminRole == 6) {
			$WorkFlowQuery->andWhere(['or', ['flow_operate_audit_status' => '等待审核'], ['in', 'flow_operate_isfinish', ['等待配置', '等待取消']]]);
		}
		
		$WorkFlowQuery->orderBy('flow_start_time desc');
				
		$WorkFlowModel->createSearchWhere($WorkFlowQuery, $this->get());

		$iCount = $WorkFlowQuery->count();
		
		$arrReturn = [
			'status'=>1,
			'info'=>'缺少必要参数',
			'data' => $iCount
		];
		return $this->renderJSON($arrReturn);
	}
	
	#预配置页详情
	public function actionPresetItem() {
		$flow_id = $this->get('flow_id');
		if(!$flow_id) {
			return $this->redirect(["/work-flow/preset-list"]);
		}
		
		$WorkFlowModel = new WorkFlow;
		$WorkFlowDetailModel = new WorkFlowDetail;
		$TradeMainModel = new TradeMain();
		$TradeDetailModel = new TradeDetail();
		
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
	
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->all();
		
		if(!$WorkFlowDetailRes) {
			return $this->redirect(["/work-flow/preset-list"]);
		}
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		
		#查询订单
		$tradeMainRes = $TradeMainModel->find()->where(['trade_orderid' => $WorkFlowRes['flow_orderid']])->asArray()->one();
		$tradeDetailRes = $TradeDetailModel->find()->where(['trade_orderid' => $WorkFlowRes['flow_orderid']])->asArray()->all();
		
		return $this->render('preset-item', ['FlowDetailRes'=>$WorkFlowDetailRes, 'FlowRes' => $WorkFlowRes]);
	}
	
	#预配置选择机器页面
	public function actionPresetItemSet() {
		$flow_id = $this->get('flow_id');
		$pdt_id = $this->get('pdt_id');
		$server_typeid = $this->get('server_typeid');
		$unionid = $this->get('unionid');
		
		if(!$flow_id || !$pdt_id || !$server_typeid || !$unionid) {
			return $this->redirect(['/work-flow/preset-list']);
		}
		
		#获取下单配置
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		
		$WorkFlowModel = new WorkFlow;
		$WorkFlowDetailModel = new WorkFlowDetail;
		$searchRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		
		$detailRes = $WorkFlowDetailModel->find()->where(['flow_unionid' => $unionid])->asArray()->one();
		
		#获取机房
		$roomRes = self::db('select * from pdt_room_manage')->queryAll();
		$nowprovider = 0;
		
		$PdtManageModel  = new PdtManage();
		$PdtManageList = $PdtManageModel->find()->where(new Expression('FIND_IN_SET(:pdt_type_id, pdt_type_id)'))->addParams([':pdt_type_id' => $server_typeid])->asArray()->all();
		
		#服务器分类
		$PdtManageTypeModel = new PdtManageType();
		$PdtManageTypeRes = $PdtManageTypeModel->find()->asArray()->all();
		
		
		return $this->render('preset-item-set', [
			'room_list' => $roomRes,
			'pdt_list' => $PdtManageList,
			'pdt_type_list' => $PdtManageTypeRes,
			'pdt_id' => $pdt_id,
			'server_typeid' => $server_typeid,
			'unionid' => $unionid,
			'flow_id' => $flow_id,
			'flowRes' => $searchRes,
			'nowprovider' => $nowprovider,
			'detail' => $detailRes,
		]);
	}
	
	#新购流程重新分配选择机器页面
	public function actionPresetItemReset() {
		$flow_id = $this->get('flow_id');
		$pdt_id = $this->get('pdt_id');
		$server_typeid = $this->get('server_typeid');
		$unionid = $this->get('unionid');
		
		if(!$flow_id || !$unionid) {
			return $this->redirect(['/work-flow/preset-list']);
		}

		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		
		$WorkFlowModel = new WorkFlow;
		$WorkFlowDetailModel = new WorkFlowDetail;
		$searchRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		#获取下单配置
		$detailRes = $WorkFlowDetailModel->find()->where(['flow_unionid' => $unionid])->asArray()->one(); #print_r($detailRes);exit;
		$flow_after_config = json_decode($detailRes['flow_after_config'], true);
		#获取机房
		$roomRes = self::db('select * from pdt_room_manage')->queryAll();
		$nowprovider = 0;
		
		$PdtManageModel  = new PdtManage();
		$PdtManageList = $PdtManageModel->find()->where(new Expression('FIND_IN_SET(:pdt_type_id, pdt_type_id)'))->addParams([':pdt_type_id' => $server_typeid])->asArray()->all();
		
		#服务器分类
		$PdtManageTypeModel = new PdtManageType();
		$PdtManageTypeRes = $PdtManageTypeModel->find()->asArray()->all();
		
		return $this->render('preset-item-reset', [
			'room_list' => $roomRes,
			'pdt_list' => $PdtManageList,
			'pdt_type_list' => $PdtManageTypeRes,
			'pdt_id' => $pdt_id,
			'server_typeid' => $server_typeid,
			'unionid' => $unionid,
			'flow_id' => $flow_id,
			'flowRes' => $searchRes,
			'nowprovider' => $nowprovider,
			'detail' => $detailRes,
			'flow_after_config' => $flow_after_config,
		]);
	}
	
	#预配置中止事务
	public function actionStopPreset() {
		Yii::$app->request->isAjax || die('error');
		
		$flow_id = $this->post('flow_id');
		if(!$flow_id) {
			$arrReturn = [
				'status'=>0,
				'info'=>'缺少必要参数，未知的流程'
			];
			return $this->renderJSON($arrReturn);
		}
		
		#1.还原自有机器属性
		#2.还原测试机状态属性
		#3.变更流程为中止事务完成
		
		$WorkFlowModel = new WorkFlow;
		$WorkFlowDetailModel = new WorkFlowDetail;
		$WorkFlowRoleModel = new WorkFlowRole;
		$TestServerModel = new TestServer;
		$IdlePdtModel = new IdlePdt;
		$TradeMainModel = new TradeMain();
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id, 'flow_status' => '等待预配置'])->one();
		
		if(!$WorkFlowRes) {
			$arrReturn = [
				'status'=>0,
				'info'=>'未知的流程'
			];
			return $this->renderJSON($arrReturn);
		}
		
		$flowRole = $WorkFlowRoleModel->find()->where(['wr_name' => $WorkFlowRes->flow_name])->one();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->all();
		
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		
		#开启事务
		$transaction = Yii::$app->db->beginTransaction();

		$WorkFlowRes->flow_operate_audit_status = '无需审核';
		$WorkFlowRes->flow_operate_isfinish = '取消完成';
		
		$WorkFlowRes->flow_money_isfinish = '无需审核成本';
		
		if(in_array($WorkFlowRes->flow_name, ['新购-余额支付', '新购-在线支付']) )
		{
			$WorkFlowRes->flow_money_audit_status = '等待审核';
			$WorkFlowRes->flow_status = '中止事务中';
		} else {
			if( $WorkFlowRes->flow_money_audit_status = '等待审核' ) 
			{
				$WorkFlowRes->flow_money_audit_status = '无需审核';				
			}
			$WorkFlowRes->flow_status = '中止事务完成';
			$WorkFlowRes->flow_end_time = time();
			
			#如果为后付款，将订单取消
			if( $WorkFlowRes->flow_name == '新购-后付款') 
			{
				$TradeMainQuery = $TradeMainModel->find()->where(['trade_orderid' => $WorkFlowRes->flow_orderid])->one();
				if(empty($TradeMainQuery) )
				{
					$transaction->rollBack();
					$arrReturn = [
						'status'=>0,
						'info' => '后付款订单异常'
					];
					return $this->renderJSON($arrReturn);
				}
				$TradeMainQuery->trade_status = '已取消';
				if( !$TradeMainQuery->update() ) 
				{
					$transaction->rollBack();
					$arrReturn = [
						'status'=>0,
						'info' => '后付款订单更新异常'
					];
					return $this->renderJSON($arrReturn);
				}
				
			}
		}
		
		$WorkFlowRes->flow_suspension_auditor_id = $adminId;
		$WorkFlowRes->flow_suspension_auditor_name = $adminName;
		$WorkFlowRes->flow_suspension_auditor_time = time();
		$WorkFlowRes->flow_suspension_auditor_reason = trim($this->post('suspension_reason'));

		#处理附表
		foreach($WorkFlowDetailRes as $key => $val) 
		{
			if($val['flow_after_config']) {
				#如果配置了机器
				$config = json_decode($val['flow_after_config'], true);
				
				#自有机器
				if(isset($config['idle_id']) && $config['idle_id'] != '') {
					if(isset($config['test_id']) && $config['test_id'] != '') {
						#使用的测试机
						$TestServerRes = $TestServerModel->find()->where(['id' => $config['test_id']])->one();
						$TestServerRes->status = 0;
						if(!$TestServerRes->save()) {
							$transaction->rollBack();
							$arrReturn = [
								'status'=>0,
								'info'=>'还原测试服务器状态出现异常'
							];
							return $this->renderJSON($arrReturn);
						}
					} else {
						$IdleRes = $IdlePdtModel->find()->where(['id' => $config['idle_id']])->one();
						$IdleRes->attribute_id = 1; #闲置服务器
						$IdleRes->status = 0;#闲置
						if(!$IdleRes->save()) {
							$transaction->rollBack();
							$arrReturn = [
								'status'=>0,
								'info'=>'还原自有机器属性出现异常'
							];
							return $this->renderJSON($arrReturn);
						}
					}
				} else {
					#供应商机器
					if(isset($config['test_id']) && $config['test_id'] != '') {
						#使用的测试机
						$TestServerRes = $TestServerModel->find()->where(['id' => $config['test_id']])->one();
						$TestServerRes->status = 0;
						if(!$TestServerRes->save()) {
							$transaction->rollBack();
							$arrReturn = [
								'status'=>0,
								'info'=>'还原测试服务器状态出现异常'
							];
							return $this->renderJSON($arrReturn);
						}
					}
				}
			}
		}
		
		#更新主表
		$updateWorkFlow = $WorkFlowRes->save();
		#更新附表
		
		$stop_preset = [
			'stop_admin' => $adminId,
			'stop_time' => time()
		];
		
		$updateDetail = $WorkFlowDetailModel->updateAll(['flow_after_config' => json_encode(['stop_preset' => $stop_preset], true), 'flow_ip' => null, 'flow_ip2' => null],['flow_id' => $flow_id]);
		if($updateWorkFlow && $updateDetail) {
			$transaction->commit();
			$arrReturn = [
				'status'=>1,
				'info'=>'中止事务操作完成，请前往待处理列表查看'
			];
			return $this->renderJSON($arrReturn);
		} else {
			$arrReturn = [
				'status'=>0,
				'info'=>'中止事务操作出现异常'
			];
			return $this->renderJSON($arrReturn);
		}
	}
	
	#用于iframe查找自有机器
    public function actionSelectIdle() {        
        $IdlePdtModel = new IdlePdt();
		$IdlePdtModelQuery = $IdlePdtModel->find()->select("*")->With('servertype')->With('pdtroom')->With('pdtcabinet')->
        With('pdtmanage')->With('pdttype')->With('serverattribute')->With('provider')->With('switch')->where(['in','attribute_id',[1]])->andwhere(['status'=>0])->orderBy('id desc');
		
    	#创建搜索条件
    	$IdlePdtModel->createSearchWhere($IdlePdtModelQuery, $this->get());
    	
    	$iCount = $IdlePdtModelQuery->count();//echo $iCount;
    	
    	$oPage = DataHelper::getPage($iCount);

    	$arrRes = $IdlePdtModelQuery->offset($oPage->offset)->limit($oPage->limit)->orderBy('id desc')->asArray()->all();   //die(print_r($arrRes));
    
    	//获取机房列表
    	$RoomManageModel = new PdtRoomMange();

		if( $this->get('servicerprovider') != "" &&  $this->get('servicerprovider') == 0 ){
			$roomRes = $RoomManageModel->getListAll(['provider'=>0]);
		} else {
			$roomRes = $RoomManageModel->getListAll();
		}
    	
		//获取服务器分类
		$PdtManageTypeModel = new PdtManageType();
		$PdtManageTypeRes = $PdtManageTypeModel->find()->AsArray()->All();
    	
        return $this->render('select-idle',['arrRes'=>$arrRes,'page'=>$oPage,'iCount'=>$iCount,'roomRes'=>$roomRes,'PdtManageTypeRes'=>$PdtManageTypeRes]);
    }
	
	#通过idleid查找信息
	public function actionSelectIdleInfo() {
		Yii::$app->request->isAjax || die('error');  
		$idleid = trim($this->post('id'));
		if(!$idleid) {
			$arrReturn = [
				'status'=>0,
				'info'=>'缺少必要参数'
			];
			return $this->renderJSON($arrReturn);
		}
		
		$IdlePdtModel = new IdlePdt();
		$data = [];
		$data['data'] = $IdlePdtModel->find()->where(['id' => $idleid, 'status' => '0'])->asArray()->one();
		if(!$data['data']) {
			$arrReturn = [
				'status'=>0,
				'info'=>'配置机器不存在或短时间内被占用，请前往自有列表验证后再试'
			];
			return $this->renderJSON($arrReturn);
		}
		
		$data['config'] = json_decode($data['data']['config'],true);
        $data['ip'] = json_decode($data['data']['ip'],true);
		
		return $this->renderJSON($data);
	}
	
	#查找测试服务器
	public function actionSelectTest() {
		
		$TestServerModel = new TestServer();
		
		$TestServerModelQuery = $TestServerModel->find()->select("*")->With('servertype')->With('pdtroom')->With('pdtcabinet')->
        With('pdtmanage')->With('provider')->With('useradmin')->where(['status'=> '0'])->orderBy('id desc');
		
		
		#创建搜索条件
    	$TestServerModel->createSearchWhere($TestServerModelQuery, $this->get());

		$iCount = $TestServerModelQuery->count();//echo $iCount;
    	$oPage = DataHelper::getPage($iCount);
    	$arrRes = $TestServerModelQuery->offset($oPage->offset)->limit($oPage->limit)->orderBy('id desc')->asArray()->all();
		
		$getRes = $this->get();
		unset($getRes['r']);
		return $this->render('select-test',['arrRes'=>$arrRes,'page'=>$oPage,'iCount'=>$iCount, 'getRes' => $getRes]);
	}
	
	#根据Testid查找测试服务器信息
	public function actionSelectTestInfo() {
		
		Yii::$app->request->isAjax || die('error');  
		$testid = trim($this->post('testid'));
		$provider = trim($this->post('provider'));
		if(!$testid || !in_array($provider, [0, 1])) {
			$arrReturn = [
				'status'=>0,
				'info'=>'缺少必要参数'
			];
			return $this->renderJSON($arrReturn);
		}
		
		$TestServerModel = new TestServer();
		if($provider == 1) {
			$TestServerRes = $TestServerModel->find()->where(['id' => $testid])->asArray()->one();
		} else {
			$TestServerRes = $TestServerModel->find()->with('idlepdt')->where(['id' => $testid])->asArray()->one();
		}
		
		if(!$TestServerRes) {
			$arrReturn = [
				'status'=>0,
				'info'=>'没有指定的测试服务器'
			];
			return $this->renderJSON($arrReturn);
		}
		
		$arrReturn = [
			'status'=>0,
			'info'=>'查找完成',
			'data'=> $TestServerRes
		];
		return $this->renderJSON($arrReturn);
		
	}
	
	#通过选择的服务器分类，选择产品配置类别
	public function actionServertypeChange() {
		Yii::$app->request->isAjax || die('error');
    
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageModel = new PdtManage();
        
        $server_type_id = $this->post('id');
		$PdtManageTypeRes = $PdtManageTypeModel->find($server_type_id);		
		
		$PdtManageRes = $PdtManageModel->find()->where(new Expression('FIND_IN_SET(:pdt_type_id, pdt_type_id)'))->addParams([':pdt_type_id' => $server_type_id])->asArray()->all();
        
        $data = $PdtManageRes;
        return $this->renderJSON($data);
	}
	
	#查找供应商机房和供应商列表
	public function actionGetSupplierInfo() {
		$PdtRoomMangeModel = new PdtRoomMange();
		$ProviderModel = new Provider();
		
		$data['PdtRoomList'] = $PdtRoomMangeModel->find()->where(['provider' => 1])->asArray()->all();
		$data['ProviderList'] = $ProviderModel->find()->asArray()->all();
		
		return $this->renderJSON($data);
	}
	
	#通过产品配置类别查找信息
	public function actionPdttypeInfo() {
		Yii::$app->request->isAjax || die('error');
		
        $pdt_id = $this->post('id');
		
        if(!$pdt_id)
		{
            $arrReturn = [
                'status'=>0,
                'info'=>'未选择产品配置类别'
            ];
            return $this->renderJSON($arrReturn);
        }
        
        $PdtManageModel = new PdtManage();
        #获取整个的产品配置
        $PdtManageRes = $PdtManageModel->getRowById($pdt_id);
		
        if ( empty($PdtManageRes) ) 
		{
            $pdtRes= "";
            return $this->renderJSON($pdtRes);
        }
        $cpuRes = json_decode($PdtManageRes['cpu'],true);   	
        $ramRes = json_decode($PdtManageRes['ram'],true);
        $hddRes = json_decode($PdtManageRes['hdd'],true);
        $bandwidthRes = json_decode($PdtManageRes['bandwidth'],true);
        $ipnumberRes = json_decode($PdtManageRes['ipnumber'],true);
        $defenseRes = json_decode($PdtManageRes['defense'],true);
        $systemRes = json_decode($PdtManageRes['operatsystem'],true);
        
		$defaultConfig = [];
		
		foreach($cpuRes['info'] as $key => $val) {
			if($val['id'] == $cpuRes['default']) {
				$defaultConfig['cpu'] = $val['name'];
				break;
			}
		}
		
		foreach($ramRes['info'] as $key => $val) {
			if($val['id'] == $ramRes['default']) {
				$defaultConfig['ram'] = $val['name'];
				break;
			}
		}
		
		foreach($hddRes['info'] as $key => $val) {
			if($val['id'] == $hddRes['default']) {
				$defaultConfig['hdd'] = $val['name'];
				break;
			}
		}
		
		foreach($bandwidthRes['info'] as $key => $val) {
			if($val['id'] == $bandwidthRes['default']) {
				$defaultConfig['bandwidth'] = $val['name'];
				break;
			}
		}
		
		foreach($ipnumberRes['info'] as $key => $val) {
			if($val['id'] == $ipnumberRes['default']) {
				$defaultConfig['ipnumber'] = $val['name'];
				break;
			}
		}
		
		if($defenseRes) {
			foreach($defenseRes['info'] as $key => $val) {
				if($val['id'] == $defenseRes['default']) {
					$defaultConfig['defense'] = $val['name'];
					break;
				}
			}
		} else {
			$defaultConfig['defense'] = 'N/A';
		}
		
		if($systemRes) {
			foreach($systemRes['info'] as $key => $val) {
				if($val['id'] == $systemRes['default']) {
					$defaultConfig['system'] = $val['name'];
					break;
				}
			}
		} else {
			$defaultConfig['system'] = 'N/A';
		}
		
        return $this->renderJSON($defaultConfig);
	}
	
	#查询流程的详细信息
	public function actionGetHandleInfo() {
		Yii::$app->request->isAjax || die('error');
		$flow_id = $this->post('flow_id');
		if(!$flow_id) {
			$arrReturn = [
                'status'=>0,
                'info'=>'参数异常'
            ];
            return $this->renderJSON($arrReturn);
		}
		
		$WorkFlowDetailModel = new WorkFlowDetail();
		
		$DetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->all();
		if(!$DetailRes) {
			$arrReturn = [
                'status'=>0,
                'info'=>'无效的流程'
            ];
            return $this->renderJSON($arrReturn);
		}
		
		$data = [];
		
		foreach($DetailRes as $key => $val) {
			
			$timeinfo = '';
			if($val['flow_fix_finish_time'] && $val['flow_fix_confirm_time']) {
				$timeformat = $val['flow_fix_finish_time'] - $val['flow_fix_confirm_time'];
				if($timeformat < 60) {
					$timeinfo = '处理耗时：'.$timeformat.'秒';
				} else if($timeformat < 3600) {
					$min = intval($timeformat / 60);
					$second = $timeformat - ($min * 60);
					$timeinfo = '处理耗时：'.$min.'分'.$second.'秒';
				} else {
					$hour = intval($timeformat / 3600);
					$min = intval(($timeformat - ($hour * 3600)) / 60);
					$second = intval($timeformat - ($hour * 3600) - ($min * 60));
					$timeinfo = '处理耗时：'.$hour.'小时'.$min.'分'.$second.'秒';
				}
			}
			
			$config = json_decode($val['flow_after_config'], true);
			
			$data[] = [
				'provider' => isset($config['idle_id']) && $config['idle_id'] != '' ? '自有服务器' : '供应商',
				'istest' => isset($config['test_id']) && $config['test_id'] ? 'Y' : 'N',
				'unionid' => $val['flow_unionid'],
				'ip' => implode('<br/>', json_decode($val['flow_ip'], true)),
				'handle_admin' => $val['flow_fix_name'] ? $val['flow_fix_name'] : '--',
				'handle_confirm_time' => $val['flow_fix_confirm_time'] ? date("Y-m-d H:i", $val['flow_fix_confirm_time']) : '--',
				'handle_finish_time' => $val['flow_fix_finish_time'] ? date("Y-m-d H:i", $val['flow_fix_finish_time']) : '--',
				'timeinfo' => $timeinfo,
			];
		}
		
		$arrReturn = [
			'status'=>1,
			'info'=>'查询完成',
			'data' => $data
		];
		return $this->renderJSON($arrReturn);
		
	}
	
	#预配置模块提交单台机器配置
	public function actionSubmitPreConfig() {
		Yii::$app->request->isAjax || die('error');
		
		$flow_id = $this->post('flow_id');
		$unionid = $this->post('unionid');
		$provider = $this->post('provider');
		$data = $this->post('data');
		
		if(!$data || !in_array($provider, [0, 1])) {
			$arrReturn = [
                'status'=>0,
                'info'=>'参数异常'
            ];
            return $this->renderJSON($arrReturn);
		}
		
		
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		#找到流程中的配置，并且更新进去
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$MemberPdtModel = new MemberPdt();
		$TestServerModel = new TestServer();
		$IdlePdtModel = new IdlePdt();
		$PdtIpModel = new PdtIp();
		$SupplierIpModel = new SupplierIp();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id, 'flow_unionid' => $unionid])->one();
		
		$afterConfig = [];
		$configRemark = $data['remark'];
		
		$oldFlowData = json_decode($WorkFlowDetailRes->flow_after_config, true);
		#判定前，先将之前分配入库的供应商IP删除掉(只有供应商的非测试机)
		if($oldFlowData['servicerprovider'] == 1 && !$oldFlowData['test_id'] )
		{
			if( !empty($oldFlowData['ip2']) ) {
				$delteRes = $SupplierIpModel->deleteAll(['ip' => $oldFlowData['ip2']]);
				if( $delteRes != count($oldFlowData['ip2']) ) {
					$transaction->rollBack();
					$arrReturn = [
						'status'=>0,
						'info'=>'供应商IP删除处理失败'
					];
					return $this->renderJSON($arrReturn);
				}
			}
		}
		
		if($provider == 0) {
			#自有服务器查询自有ID
			$IdleRes = $IdlePdtModel->find()->where(['id' => $data['idle_id']])->asArray()->one();

			$afterConfig['config'] = json_decode($IdleRes['config'], true);
			$afterConfig['config']['requirement_bandwidth'] = $data['idle_needband'];
			$afterConfig['test_id'] = $data['test_id'];
			$afterConfig['idle_id'] = $data['idle_id'];
			$afterConfig['room_id'] = $IdleRes['room_id'];
			$afterConfig['server_typeid'] = $data['server_typeid'];
			$afterConfig['pdt_id'] = $IdleRes['pdt_id'];
			$afterConfig['start_date'] = $data['start_date'];
			$afterConfig['ip'] = json_decode($IdleRes['ip'], true);
			$afterConfig['ip2'] = json_decode($IdleRes['ip2'], true);
			$afterConfig['addition'] = $data['addition'];
			$afterConfig['servicerprovider'] = 0;
			
		} else {
			#供应商服务器，所有数据均由前台传过来，直接赋值写入
			$afterConfig['config'] = $data['config'];
			$afterConfig['config']['requirement_bandwidth'] = $data['supplier_needband'];
			$afterConfig['test_id'] = $data['test_id'];
			$afterConfig['server_typeid'] = $data['server_typeid'];
			$afterConfig['pdt_id'] = $data['pdt_id'];
			$afterConfig['provider_id'] = $data['provider_id'];
			$afterConfig['room_id'] = $data['room_id'];
			$afterConfig['start_date'] = $data['start_date'];
			$afterConfig['addition'] = $data['addition'];
			$afterConfig['servicerprovider'] = 1;
			if(!$data['iplist']) 
			{
				$arrReturn = [
					'status' => 0,
					'info' => '供应商IP为必填选项'
				];
				return $this->renderJSON($arrReturn);
			}
			$afterConfig['ip'] = DataHelper::dotrim($data['iplist']);
			$afterConfig['ip2'] = [];
			
			#解析拆分IP段
			$Retuen_IPArray = DataHelper::splitIP($data['iplist']);
			if( !$Retuen_IPArray['status'] ) 
			{
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info'=> $Retuen_IPArray['info']
				];
				return $this->renderJSON($arrReturn);
			}					
			$afterConfig['ip2'] = $Retuen_IPArray['data'];
			
			
			#选择了测试机，供应商的IP就不用入库，因为已经入了一次
			if( isset($afterConfig['test_id']) && $afterConfig['test_id'] ) 
			{
				#如果为测试机，改动了不能改IP，所以这段注释掉
				/*$TestServerRes = $TestServerModel->find()->where(['id' => $afterConfig['test_id']])->asArray()->one();
				$ip = json_decode($TestServerRes['ip'],true);
				$ip2 = json_decode($TestServerRes['ip2'],true);
				$diffArray = array_diff($afterConfig['ip2'],$ip2); #print_r($diffArray);
				if( !empty($diffArray) ) {
					$CheckRes = DataHelper::CheckIP_isused($diffArray);
					if( $CheckRes['status'] == 0 ) {
						$transaction->rollBack();
						$arrReturn = [
						   'status' => 0,
						   'info' => $CheckRes['info']
						];
						return $this->renderJSON($arrReturn);
					}
				}*/			
			} else {
				#如果不是测试，需要先判断是否能用				
				$CheckRes = DataHelper::detect_supplierip($afterConfig['ip2']);
				if( $CheckRes['status'] == 0 ) {
					$transaction->rollBack();
					$arrReturn = [
					   'status' => 0,
					   'info' => $CheckRes['info']
					];
					return $this->renderJSON($arrReturn);
				}
				$insert_ip = $afterConfig['ip2'];
				
				#供应商IP入库
				$insertRes = $SupplierIpModel->add_peration($insert_ip, $afterConfig['provider_id'], $afterConfig['room_id'], '3');
				if($insertRes != count($insert_ip) )
				{
					$transaction->rollBack();
					$arrReturn = [
					   'status' => 0,
					   'info' => '供应商IP入库失败'
					];
					return $this->renderJSON($arrReturn);
				}
			}
			
		}
		
		#改变自有服务器状态，如果是测试服务器则不需要改
		$IdlePdtModel = new IdlePdt();
		$updateIdleOld = true;
		$updateIdleNew = true;
		
		#测试机改为占用
		$TestServerModel = new TestServer();
		$updateTestOld = true;
		$updateTestNew = true;
		
		#如果有数据
		if($afterConfig['test_id']) 
		{
			$TestServerRes = $TestServerModel->find()->where(['id' => $afterConfig['test_id']])->one();
			$TestServerRes->status = 2;
			$updateTestNew = $TestServerRes->save();
		} else {
			#如果新数据没有测试ID，并且是idle_id存在，那么则更新新机器的状态为待定
			if(isset($afterConfig['idle_id']) && $afterConfig['idle_id']) {
				$IdlePdtNewRes = $IdlePdtModel->find()->where(['id' => $afterConfig['idle_id']])->one();
				$IdlePdtNewRes->status = 2;
				$updateIdleNew = $IdlePdtNewRes->save();
			}
			
		}	
		#flow_after_config有数据表示之前已经分配过一次
		$oldFlowData = json_decode($WorkFlowDetailRes->flow_after_config, true);		
		if($WorkFlowDetailRes->flow_after_config) 
		{
			$oldIP = $oldFlowData['ip2'];
			if(isset($oldFlowData['test_id']) && $oldFlowData['test_id']) 
			{
				if($oldFlowData['test_id'] != $afterConfig['test_id']) 
				{
					$TestServerOldRes = $TestServerModel->find()->where(['id' => $oldFlowData['test_id']])->one();
					$TestServerOldRes->status = 0;
					$updateTestOld = $TestServerOldRes->save();
				}
			} else {
				#之前提交的是供应商的
				if( $oldFlowData['servicerprovider'] == 1) {
					
				}
			}
			
			#如果以前的配置是自有机器，并且不是测试机，那么则修改之前的机器状态为闲置
			if(isset($oldFlowData['idle_id']) && $oldFlowData['idle_id'] && !$oldFlowData['test_id']) 
			{
				$IdlePdtOldRes = $IdlePdtModel->find()->where(['id' => $oldFlowData['idle_id']])->one();
				$IdlePdtOldRes->status = 0;
				$updateIdleOld = $IdlePdtOldRes->save();
			}
		}
		
		#判断是否补录机器
		if($afterConfig['addition'] == 1) 
		{
			$WorkFlowDetailRes->flow_fix_id = $adminId;
			$WorkFlowDetailRes->flow_fix_name = $adminName;
			$WorkFlowDetailRes->flow_fix_confirm_time = time();
			$WorkFlowDetailRes->flow_fix_finish_time = time();
			
			if($provider == 0) {
				//自有服务器
				$IdleRes = $IdlePdtModel->find()->where(['id' => $data['idle_id']])->asArray()->one();
				$afterConfig['install_account'] = $IdleRes['re_username'];
				$afterConfig['install_pass'] = $IdleRes['re_password'];
				$afterConfig['install_port'] = $IdleRes['re_port'];
			} else {
				//供应商
				if(isset($afterConfig['test_id']) && $afterConfig['test_id'] != '') {
					$TestServerRes = $TestServerModel->find()->where(['id' => $afterConfig['test_id']])->asArray()->one();
					$afterConfig['install_account'] = $TestServerRes['account_name'];
					$afterConfig['install_pass'] = $TestServerRes['account_pwd'];
					$afterConfig['install_port'] = $TestServerRes['account_port'];
				} else {
					$afterConfig['install_account'] = $data['supplier_account'];
					$afterConfig['install_pass'] = $data['supplier_pass'];
					$afterConfig['install_port'] = $data['supplier_port'];
				}
			}
			
			$testAdditionFinish = $WorkFlowDetailModel->find()->where(['flow_id' => $WorkFlowDetailRes->flow_id])->asArray()->all();
			
			$hasFinish = true;
			
			foreach($testAdditionFinish as $key => $val) {
				if($val['id'] == $WorkFlowDetailRes->id) {
					continue;
				}
				
				if(!$val['id']) {
					$hasFinish = false;
					break;
				}
			}
			
			if($hasFinish) {
				#全都是补录
				$FlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
				$FlowRes->flow_operate_audit_status = '无需审核';
				$FlowRes->flow_operate_isfinish = '配置完成';
				$updateFlow = $FlowRes->save();
			} else {
				$updateFlow = true;
			}
		} else {
			$WorkFlowDetailRes->flow_fix_id = null;
			$WorkFlowDetailRes->flow_fix_name = null;
			$WorkFlowDetailRes->flow_fix_confirm_time = null;
			$WorkFlowDetailRes->flow_fix_finish_time = null;
			$updateFlow = true;
		}		
		
		#保存结果
		$WorkFlowDetailRes->flow_after_config = json_encode($afterConfig, JSON_UNESCAPED_UNICODE);
		$WorkFlowDetailRes->flow_config_remark = $configRemark;
		#更新详情
		$updateRes = $WorkFlowDetailRes->save();
		
		if($updateRes && $updateFlow && $updateTestNew && $updateTestOld) {
			$transaction->commit();
			$arrReturn = [
                'status'=>1,
                'info'=>'保存成功'
            ];
            return $this->renderJSON($arrReturn);
		} else {
			$transaction->rollBack();
			$arrReturn = [
                'status'=>0,
                'info'=>'保存配置时出现异常，请联系管理员'
            ];
            return $this->renderJSON($arrReturn);
		}		
	}
	
	#新购机器待处理流程中  重新分配机器提交
	public function actionResetPreConfig() {
		Yii::$app->request->isAjax || die('error');
		
		$flow_id = $this->post('flow_id');
		$unionid = $this->post('unionid');
		$provider = $this->post('provider');
		$data = $this->post('data');
		
		if(!$data || !in_array($provider, [0, 1])) {
			$arrReturn = [
                'status' => 0,
                'info' => '参数异常'
            ];
            return $this->renderJSON($arrReturn);
		}
		
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		#找到流程中的配置，并且更新进去
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$MemberPdtModel = new MemberPdt();
		$TestServerModel = new TestServer();
		$PdtIpModel = new PdtIp();
		$SupplierIpModel = new SupplierIp();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id, 'flow_unionid' => $unionid])->one();
		
		$afterConfig = [];
		$configRemark = $data['remark'];
		
		$oldFlowData = json_decode($WorkFlowDetailRes->flow_after_config, true);
		#判定前，先将之前分配入库的供应商IP删除掉(只有供应商的非测试机)
		if($oldFlowData['servicerprovider'] == 1 && !$oldFlowData['test_id'] ) 
		{
			$delteRes = $SupplierIpModel->deleteAll(['ip' => $oldFlowData['ip2']]);
			if( $delteRes != count($oldFlowData['ip2']) ) {
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info' => '供应商IP删除处理失败'
				];
				return $this->renderJSON($arrReturn);
			}
		}
		
		$TempMemberPdt = $MemberPdtModel->find()->where(['unionid' => $WorkFlowDetailRes['flow_unionid']])->one();
		
		if($provider == 0) 
		{
			#自有服务器查询自有ID
			$IdlePdtModel = new IdlePdt();
			$IdleRes = $IdlePdtModel->find()->where(['id' => $data['idle_id']])->asArray()->one();
			
			$config = json_decode($IdleRes['config'], true);
			$config['requirement_bandwidth'] = $data['idle_needband'];
			
			$TempMemberPdt->config = $config;
			$TempMemberPdt->idle_id = $data['idle_id'];
			$TempMemberPdt->server_type_id = $data['server_typeid'];
			$TempMemberPdt->pdt_id = $IdleRes['pdt_id'];
			$TempMemberPdt->ip = $IdleRes['ip'];
			$TempMemberPdt->ip2 = $IdleRes['ip2'];
			$TempMemberPdt->servicerprovider = 0;
					
			
			$afterConfig['config'] = $config;
			$afterConfig['test_id'] = $data['test_id'];
			$afterConfig['idle_id'] = $data['idle_id'];
			$afterConfig['server_typeid'] = $data['server_typeid'];
			$afterConfig['pdt_id'] = $IdleRes['pdt_id'];
			$afterConfig['start_date'] = $data['start_date'];
			$afterConfig['ip'] = json_decode($IdleRes['ip'], true);
			$afterConfig['ip2'] = json_decode($IdleRes['ip2'], true);
			$afterConfig['addition'] = $data['addition'];
			$afterConfig['servicerprovider'] = 0;
			
		} else {
			#供应商服务器，所有数据均由前台传过来，直接赋值写入
			
			
			$TempMemberPdt->config = $config;
			$TempMemberPdt->server_type_id = $data['server_typeid'];
			$TempMemberPdt->pdt_id = $IdleRes['pdt_id'];
			$TempMemberPdt->provider_id = $IdleRes['provider_id'];
			$TempMemberPdt->room_id = $data['room_id'];
			$TempMemberPdt->servicerprovider = 1;
			
			
			$afterConfig['config'] = $data['config'];
			$afterConfig['config']['requirement_bandwidth'] = $data['supplier_needband'];
			$afterConfig['test_id'] = $data['test_id'];
			$afterConfig['server_typeid'] = $data['server_typeid'];
			$afterConfig['pdt_id'] = $data['pdt_id'];
			$afterConfig['provider_id'] = $data['provider_id'];
			$afterConfig['room_id'] = $data['room_id'];
			$afterConfig['start_date'] = $data['start_date'];			
			$afterConfig['addition'] = $data['addition'];
			$afterConfig['servicerprovider'] = 1;			
			if(!$data['iplist']) {
				$arrReturn = [
					'status' => 0,
					'info' => '供应商IP为必填选项'
				];
				return $this->renderJSON($arrReturn);
			}
			$afterConfig['ip'] = array_filter(DataHelper::dotrim($data['iplist']));
			$afterConfig['ip2'] = [];
			
			#解析IP段
			$Retuen_IPArray = DataHelper::splitIP($data['iplist']);
			if( !$Retuen_IPArray['status'] ) {
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info'=> $Retuen_IPArray['info']
				];
				return $this->renderJSON($arrReturn);
			}
			$afterConfig['ip2'] = $Retuen_IPArray['data'];
			
			$TempMemberPdt->ip = json_encode($afterConfig['ip'], true);
			$TempMemberPdt->ip2 = json_encode($afterConfig['ip2'], true);
			
			#选择了测试机，供应商的IP就不用入库，因为已经入了一次
			if( isset($afterConfig['test_id']) && $afterConfig['test_id'] ) 
			{
				#如果为测试机，改动了不能改IP，所以这段注释掉
				/*$TestServerRes = $TestServerModel->find()->where(['id' => $afterConfig['test_id']])->asArray()->one();
				$ip = json_decode($TestServerRes['ip'],true);
				$ip2 = json_decode($TestServerRes['ip2'],true);
				$diffArray = array_diff($afterConfig['ip2'],$ip2); #print_r($diffArray);
				if( !empty($diffArray) ) {
					$CheckRes = DataHelper::CheckIP_isused($diffArray);
					if( $CheckRes['status'] == 0 ){
						$arrReturn = [
						   'status' => 0,
						   'info' => $CheckRes['info']
						];
						return $this->renderJSON($arrReturn);
					}
				}*/
				
			} else {
				#如果不是测试机,#检测新提交的IP地址					
				$CheckRes = DataHelper::detect_supplierip($afterConfig['ip2']);
				if( $CheckRes['status'] == 0 ) {
					$transaction->rollBack();
					$arrReturn = [
					   'status' => 0,
					   'info' => $CheckRes['info']
					];
					return $this->renderJSON($arrReturn);
				}
				
				$insert_ip = $afterConfig['ip2'];
				#供应商IP入库
				$insertRes = $SupplierIpModel->add_peration($insert_ip, $afterConfig['provider_id'], $afterConfig['room_id'], '3');
				if($insertRes != count($insert_ip) )
				{
					$transaction->rollBack();
					$arrReturn = [
					   'status' => 0,
					   'info' => '供应商IP入库失败'
					];
					return $this->renderJSON($arrReturn);
				}				
			}		
		}
		
		
		#更新member_pdt缓存
		if(!$TempMemberPdt->save()) {
			$transaction->rollBack();
			$arrReturn = [
			   'status' => 0,
			   'info' => '更新业务列表缓存出现异常'
			];
			return $this->renderJSON($arrReturn);
		}
		
		#改变自有服务器状态，如果是测试服务器则不需要改
		$IdlePdtModel = new IdlePdt();
		$updateIdleOld = true;
		$updateIdleNew = true;
		
		#测试机改为占用
		$TestServerModel = new TestServer();
		$updateTestOld = true;
		$updateTestNew = true;
		
		#如果提交的test_id有数据
		if($afterConfig['test_id']) {
			$TestServerRes = $TestServerModel->find()->where(['id' => $afterConfig['test_id']])->one();
			$TestServerRes->status = 2;
			$updateTestNew = $TestServerRes->save();
		} else {
			#如果新数据没有测试ID，并且是idle_id存在，那么则更新新机器的状态为待定
			if(isset($afterConfig['idle_id']) && $afterConfig['idle_id']) {
				$IdlePdtNewRes = $IdlePdtModel->find()->where(['id' => $afterConfig['idle_id']])->one();
				$IdlePdtNewRes->status = 2;
				$updateIdleNew = $IdlePdtNewRes->save();
			}
		}
		#flow_after_config有数据表示之前已经分配过一次
		$oldFlowData = json_decode($WorkFlowDetailRes->flow_after_config, true);
		if($WorkFlowDetailRes->flow_after_config) 
		{			
			$oldIP = $oldFlowData['ip2'];
			#如果之前是测试机
			if(isset($oldFlowData['test_id']) && $oldFlowData['test_id']) 
			{
				if($oldFlowData['test_id'] != $afterConfig['test_id']) {
					$TestServerOldRes = $TestServerModel->find()->where(['id' => $oldFlowData['test_id']])->one();
					$TestServerOldRes->status = 0;
					$updateTestOld = $TestServerOldRes->save();
				}
			} else {
				#之前提交的是供应商的
				if( $oldFlowData['servicerprovider'] == 1) {
					
				}
			}
			
			#如果以前的配置是自有机器，并且不是测试机，那么则修改之前的机器状态为闲置
			if(isset($oldFlowData['idle_id']) && $oldFlowData['idle_id'] && !$oldFlowData['test_id']) {
				$IdlePdtOldRes = $IdlePdtModel->find()->where(['id' => $oldFlowData['idle_id']])->one();
				$IdlePdtOldRes->status = 0;
				$updateIdleOld = $IdlePdtOldRes->save();
			}			
		}
		
		#判断是否为补录机器
		if($afterConfig['addition'] == 1) {
			
			$WorkFlowDetailRes->flow_fix_id = $adminId;
			$WorkFlowDetailRes->flow_fix_name = $adminName;
			$WorkFlowDetailRes->flow_fix_confirm_time = time();
			$WorkFlowDetailRes->flow_fix_finish_time = time();
			
			if($provider == 0) {
				//自有服务器
				$IdleRes = $IdlePdtModel->find()->where(['id' => $data['idle_id']])->asArray()->one();
				$afterConfig['install_account'] = $IdleRes['re_username'];
				$afterConfig['install_pass'] = $IdleRes['re_password'];
				$afterConfig['install_port'] = $IdleRes['re_port'];
			} else {
				//供应商
				if(isset($afterConfig['test_id']) && $afterConfig['test_id'] != '') {
					$TestServerRes = $TestServerModel->find()->where(['id' => $afterConfig['test_id']])->asArray()->one();
					$afterConfig['install_account'] = $TestServerRes['account_name'];
					$afterConfig['install_pass'] = $TestServerRes['account_pwd'];
					$afterConfig['install_port'] = $TestServerRes['account_port'];
				} else {
					$afterConfig['install_account'] = $data['supplier_account'];
					$afterConfig['install_pass'] = $data['supplier_pass'];
					$afterConfig['install_port'] = $data['supplier_port'];
				}
			}
			
			$testAdditionFinish = $WorkFlowDetailModel->find()->where(['flow_id' => $WorkFlowDetailRes->flow_id])->asArray()->all();
			
			$hasFinish = true;
			
			foreach($testAdditionFinish as $key => $val) {
				if($val['id'] == $WorkFlowDetailRes->id) {
					continue;
				}
				
				if(!$val['id']) {
					$hasFinish = false;
					break;
				}
			}
			
			if($hasFinish) {
				#全都是补录
				$FlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
				$FlowRes->flow_operate_audit_status = '无需审核';
				$FlowRes->flow_operate_isfinish = '无需操作';
				$updateFlow = $FlowRes->save();
			} else {
				$updateFlow = true;
			}
		} else {
			$WorkFlowDetailRes->flow_fix_id = null;
			$WorkFlowDetailRes->flow_fix_name = null;
			$WorkFlowDetailRes->flow_fix_confirm_time = null;
			$WorkFlowDetailRes->flow_fix_finish_time = null;
			$updateFlow = true;
		}

		#保存结果
		$WorkFlowDetailRes->flow_after_config = json_encode($afterConfig, JSON_UNESCAPED_UNICODE);
		$WorkFlowDetailRes->flow_config_remark = $configRemark;


		
		#更新详情
		$updateRes = $WorkFlowDetailRes->save();
		
		if($updateRes && $updateTestNew && $updateTestOld) {
			$transaction->commit();
			$arrReturn = [
                'status' => 1,
                'info' => '保存成功'
            ];
            return $this->renderJSON($arrReturn);
		} else {
			$transaction->rollBack();
			$arrReturn = [
                'status' => 0,
                'info' => '保存配置时出现异常，请联系管理员'
            ];
            return $this->renderJSON($arrReturn);
		}
		
	}
	
	#获取自动填充的机器账号密码
	public function actionGetFillAccount() {
		Yii::$app->request->isAjax || die('error');
		$detail_id = $this->post('detail_id');
		if(!$detail_id) {
			$arrReturn = [
                'status'=>0,
                'info'=>'缺少必要参数'
            ];
			return $this->renderJSON($arrReturn);
		}
		
		$WorkFlowDetailModel = new WorkFlowDetail();
		$IdlePdtModel = new IdlePdt();
		$TestServerModel = new TestServer();
		
		$DetailRes = $WorkFlowDetailModel->find()->where(['id' => $detail_id])->asArray()->one();
		$config = json_decode($DetailRes['flow_after_config'], true);
		
		$data = [
			'account' => '',
			'pwd' => '',
			'port' => '',
		];
		
		if(isset($config['test_id']) && $config['test_id'] != '') {
			//测试机
			$TestRes = $TestServerModel->find()->where(['id' => $config['test_id']])->asArray()->one();
			
			#优先测试机的账户账号密码
			if($TestRes['account_name'] && $TestRes['account_pwd'] && $TestRes['account_port']) {
				$data['account'] = $TestRes['account_name'];
				$data['pwd'] = $TestRes['account_pwd'];
				$data['port'] = $TestRes['account_port'];
				
			} else {
				if(isset($config['idle_id']) && $config['idle_id'] != '') {
					//自有服务器
					$IdleRes = $IdlePdtModel->find()->where(['id' => $config['idle_id']])->asArray()->one();
					if($IdleRes['re_username'] && $IdleRes['re_password'] && $IdleRes['re_port']) {
						$data['account'] = $IdleRes['re_username'];
						$data['pwd'] = $IdleRes['re_password'];
						$data['port'] = $IdleRes['re_port'];
					}
				}
				
				//供应商没有库记录
			}
		} else {
			//非测试机则只有自有服务器有账号密码
			if(isset($config['idle_id']) && $config['idle_id'] != '') {
				//自有服务器
				$IdleRes = $IdlePdtModel->find()->where(['id' => $config['idle_id']])->asArray()->one();
				if($IdleRes['re_username'] && $IdleRes['re_password'] && $IdleRes['re_port']) {
					$data['account'] = $IdleRes['re_username'];
					$data['pwd'] = $IdleRes['re_password'];
					$data['port'] = $IdleRes['re_port'];
				}
			}
			//供应商没有库记录
		}
		
		$arrReturn = [
			'status'=>1,
			'info'=>'获取完成',
			'data' => $data
		];
		return $this->renderJSON($arrReturn);
	}
	
	#发布流程到待处理列表
	public function actionPublishFlow() {
		Yii::$app->request->isAjax || die('error');
		
		$flow_id = $this->post('flow_id');
		if(!$flow_id) {
			$arrReturn = [
                'status'=>0,
                'info'=>'缺少必要参数'
            ];
			return $this->renderJSON($arrReturn);
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$IdlePdtModel = new IdlePdt();
		$UserMemberModel = new UserMember();
		
		$detailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->all();
		
		foreach($detailRes as $key => $val) {
			if(!$val['flow_after_config']) {
				$arrReturn = [
					'status'=>0,
					'info'=>'只有在全部机器预配置完成后，才能发布'
				];
				return $this->renderJSON($arrReturn);
			}
		}
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction();
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		$WorkFlowRes->flow_status = '事务处理中';
		$WorkFlowRes->flow_start_time = time();
		$publishResult = $WorkFlowRes->save();
		
		#查询用户信息
		$UserInfo = $UserMemberModel->find()->where(['email' => $WorkFlowRes->flow_account])->asArray()->one();
		
		
		$memberPdtTemp = [
			'server_type_id' => null,
			'room_id' => null,
			'cabinet_id' => null,
			'pdt_id' => null,
			'property' => 0,
			'type_id' => 1,
			'attribute_id' => 2,
			'admin_id' => null,
			'user_id' => null,
			'user_name' => null,
			'bandwidth' => null,
			'real_bandwidth' => null,
			'switch_location' => null,
			'switch_port' => null,
			'occupies_position' => null,
			'unionid' => null,
			'sell_price' => null,
			'cost_price' => null,
			'upgrade_cost_price' => null,
			'payment_cycle' => null,
			'start_time' => null,
			'end_time' => null,
			'remark' => null,
			'ipmi_ip' => null,
			'ipmi_name' => null,
			'ipmi_pwd' => null,
			'status' => 80,
			'config' => null,
			'ip' => null,
			'ip2' => null,
			'trade_no' => null,
			'reserved' => null,
			'servicerprovider' => null,
			'provider_id' => null,
			'note' => null,
			'idle_id' => null,
			'update_time' => null,
			'audit_status' => 1,
			'is_auto' => 'N',
		];
		
		$insertMemberPdt = [];
		
		foreach($detailRes as $key => $val) {
			$frontconfig = json_decode($val['flow_front_config'], true);
			$afterconfig = json_decode($val['flow_after_config'], true);
			$ip = json_encode($afterconfig['ip']);
			$ip2 = json_encode($afterconfig['ip2']);
			
			$res = Yii::$app->db->createCommand("update workflow_detail set flow_ip = '".$ip."', flow_ip2 = '".$ip2."' where id = '".$val['id']."'")->execute();
			
			$temp = $memberPdtTemp;
			
			#生成到member_pdt里面去
			if(isset($afterconfig['idle_id'])) {
				#自有机器
				$temp['servicerprovider'] = 0;
				$IdleRes = $IdlePdtModel->find()->where(['id' => $afterconfig['idle_id']])->one();
				$temp['server_type_id'] = $IdleRes->server_type_id;
				$temp['pdt_id'] = $IdleRes->pdt_id;
				$temp['room_id'] = $IdleRes->room_id;
				$temp['cabinet_id'] = $IdleRes->cabinet_id;
				$temp['property'] = $IdleRes->property;
				$temp['type_id'] = $IdleRes->type_id;
				$temp['switch_location'] = $IdleRes->switch_location;
				$temp['switch_port'] = $IdleRes->switch_port;
				$temp['occupies_position'] = $IdleRes->occupies_position;
				$temp['ipmi_ip'] = $IdleRes->ipmi_ip;
				$temp['ipmi_name'] = $IdleRes->ipmi_name;
				$temp['ipmi_pwd'] = $IdleRes->ipmi_pwd;
				$temp['config'] = $IdleRes->config;
				$temp['ip'] = $IdleRes->ip;
				$temp['ip2'] = $IdleRes->ip2;
				$temp['idle_id'] = $IdleRes['id'];
				$temp['admin_id'] = $WorkFlowRes->flow_admin_id;
				$temp['user_id'] = $UserInfo['u_id'];
				$temp['user_name'] = $WorkFlowRes->flow_account;
				$temp['bandwidth'] = $afterconfig['config']['requirement_bandwidth'];
				$temp['real_bandwidth'] = $afterconfig['config']['configbandwidth'];
				$temp['unionid'] = $val['flow_unionid'];
				$temp['sell_price'] = $val['flow_fact_money'];
				$temp['cost_price'] = $val['flow_cost_basic'];
				$temp['upgrade_cost_price'] = $val['flow_cost_parts'];
				$temp['payment_cycle'] = $frontconfig['payment_cycle'];
				$temp['start_time'] = strtotime($afterconfig['start_date']);
				$temp['end_time'] = DataHelper::getRenewEndtime($temp['start_time'], $temp['payment_cycle']);
				$temp['trade_no'] = $WorkFlowRes->flow_orderid;
				$temp['reserved'] = isset($afterconfig['cost_commission']) && $afterconfig['cost_commission'] != '' ? $afterconfig['cost_commission'] : 0.00;
				$insertMemberPdt[] = array_values($temp);
				
			} else {
				#供应商
				$temp['servicerprovider'] = 1;
				$temp['server_type_id'] = $afterconfig['server_typeid'];
				$temp['pdt_id'] = $afterconfig['pdt_id'];
				$temp['room_id'] = $afterconfig['room_id'];
				$temp['provider_id'] = $afterconfig['provider_id'];
				$temp['ip'] = json_encode($afterconfig['ip']);
				$temp['ip2'] = json_encode($afterconfig['ip2']);
				$temp['config'] = json_encode($afterconfig['config'], JSON_UNESCAPED_UNICODE);
				$temp['admin_id'] = $WorkFlowRes->flow_admin_id;
				$temp['user_id'] = $UserInfo['u_id'];
				$temp['user_name'] = $WorkFlowRes->flow_account;
				$temp['bandwidth'] = $afterconfig['config']['requirement_bandwidth'];
				$temp['real_bandwidth'] = $afterconfig['config']['configbandwidth'];
				$temp['unionid'] = $val['flow_unionid'];
				$temp['sell_price'] = $val['flow_fact_money'];
				$temp['cost_price'] = $val['flow_cost_basic'];
				$temp['upgrade_cost_price'] = $val['flow_cost_parts'];
				$temp['payment_cycle'] = $frontconfig['payment_cycle'];
				$temp['start_time'] = strtotime($afterconfig['start_date']);
				$temp['end_time'] = DataHelper::getRenewEndtime($temp['start_time'], $temp['payment_cycle']);
				$temp['trade_no'] = $WorkFlowRes->flow_orderid;
				$temp['reserved'] = isset($afterconfig['cost_commission']) && $afterconfig['cost_commission'] != '' ? $afterconfig['cost_commission'] : 0;
				
				$insertMemberPdt[] = array_values($temp);
			}
			
			if(!$res) {
				$transaction->rollBack();
				$arrReturn = [
					'status'=>0,
					'info'=>'更新信息出现异常'
				];
				return $this->renderJSON($arrReturn);
			}
			
		}
		
		#memberPdt
		$memberpdt_title = array_keys($memberPdtTemp);
		$insertMemberPdtRes = Yii::$app->db->createCommand()->batchInsert(MemberPdt::tableName(), $memberpdt_title, $insertMemberPdt)->execute();
		
		
		if($publishResult && $insertMemberPdtRes) {
			$transaction->commit();
			$arrReturn = [
				'status'=>1,
				'info'=>'流程已发布，可前往 <流程列表> 查看'
			];
			return $this->renderJSON($arrReturn);
		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'流程尝试发布，但失败了'
			];
			return $this->renderJSON($arrReturn);
		}
	}

	#查询流程的中止理由信息等
	public function actionGetSuspensionInfo() {
		Yii::$app->request->isAjax || die('error');
		$flow_id = $this->post('flow_id');
		if(!$flow_id) {
			$arrReturn = [
                'status' => 0,
                'info' => '参数异常'
            ];
            return $this->renderJSON($arrReturn);
		}
		
		$WorkFlowModel = new WorkFlow();
		
		$WorkFlowRes= $WorkFlowModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		if(!$WorkFlowRes) {
			$arrReturn = [
                'status' => 0,
                'info' => '无效的流程'
            ];
            return $this->renderJSON($arrReturn);
		}
		
		$data['auditor_id'] = $WorkFlowRes['flow_suspension_auditor_id'];
		$data['auditor_name'] = $WorkFlowRes['flow_suspension_auditor_name'];
		$data['auditor_time'] = date('Y-m-d H:i:s', $WorkFlowRes['flow_suspension_auditor_time']);
		$data['auditor_reason'] = $WorkFlowRes['flow_suspension_auditor_reason'];
		
		$arrReturn = [
			'status'=>1,
			'info'=>'查询完成',
			'data' => $data
		];
		return $this->renderJSON($arrReturn);
	}
	#----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------#
																									#分割线#
																								#以下为处理模块#
	#----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------#
	
	#用户金额变动模板，上账、扣款
	public function actionFlowItemChangeamount() {
		
		$flow_id = $this->get('flow_id');
		if(!$flow_id) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		$WorkFlowModel = new WorkFlow();
		
		#记录访客
		$countRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		if(!self::vistorRecord($flow_id)) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		if($countRes['flow_lookcount']) {
			$lookcount = json_decode($countRes['flow_lookcount'], true);
			$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
			$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		}
		
		$WorkFlowRes = $WorkFlowModel->find()->joinwith('flowrole')->where(['flow_id' => $flow_id])->asArray()->one();
		
		return $this->render('item-changeamount', ['FlowRes' => $WorkFlowRes]);
	}
	
	#测试机变更配置
	public function actionFlowItemTesterChangeconfig() {
		$flow_id = $this->get('flow_id');
		if(!$flow_id) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		
		#记录访客
		$countRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		if(!self::vistorRecord($flow_id)) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		if($countRes['flow_lookcount']) {
			$lookcount = json_decode($countRes['flow_lookcount'], true);
			$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
			$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		}
		
		$WorkFlowRes = $WorkFlowModel->find()->joinwith('flowrole')->where(['flow_id' => $flow_id])->asArray()->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		
		return $this->render('item-tester-changeconfig', ['FlowRes' => $WorkFlowRes, 'FlowDetail' => $WorkFlowDetailRes]);
	} 
	
	#自有闲置机器更换Ip
	public function actionFlowItemIdleChangeip() {
		$flow_id = $this->get('flow_id');
		if(!$flow_id) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		
		#记录访客
		$countRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		if(!self::vistorRecord($flow_id)) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		if($countRes['flow_lookcount']) {
			$lookcount = json_decode($countRes['flow_lookcount'], true);
			$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
			$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		}
		
		$WorkFlowRes = $WorkFlowModel->find()->joinwith('flowrole')->where(['flow_id' => $flow_id])->asArray()->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		
		return $this->render('item-idle-changeip', ['FlowRes' => $WorkFlowRes, 'FlowDetail' => $WorkFlowDetailRes]);
	}
	
	#用户退款
	public function actionFlowItemRefund() {
		$flow_id = $this->get('flow_id');
		if(!$flow_id) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$WorkFlowRes = $WorkFlowModel->find()->joinwith('flowrole')->where(['flow_id' => $flow_id])->asArray()->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		
		
		#记录访客
		$countRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		if(!self::vistorRecord($flow_id)) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		if($countRes['flow_lookcount']) {
			$lookcount = json_decode($countRes['flow_lookcount'], true);
			$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
			$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		}
		
		return $this->render('item-refund', ['FlowRes' => $WorkFlowRes, 'FlowDetailRes' => $WorkFlowDetailRes]);
	}
	
	#修改产品时间 详情页
	public function actionFlowItemUpdatetime() {
		$flow_id = $this->get('flow_id');
		if(!$flow_id) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$WorkFlowRes = $WorkFlowModel->find()->joinwith('flowrole')->where(['flow_id' => $flow_id])->asArray()->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		
		#记录访客
		$countRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		if(!self::vistorRecord($flow_id)) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		if($countRes['flow_lookcount']) {
			$lookcount = json_decode($countRes['flow_lookcount'], true);
			$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
			$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		}
		
		return $this->render('item-updatetime', ['FlowRes' => $WorkFlowRes, 'FlowDetailRes' => $WorkFlowDetailRes]);
	}
	
	#修改产品信息 详情页
	public function actionFlowItemUpdateinfo() {
		$flow_id = $this->get('flow_id');
		if(!$flow_id) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$WorkFlowRes = $WorkFlowModel->find()->joinwith('flowrole')->where(['flow_id' => $flow_id])->asArray()->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		
		#记录访客
		$countRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		if(!self::vistorRecord($flow_id)) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		if($countRes['flow_lookcount']) {
			$lookcount = json_decode($countRes['flow_lookcount'], true);
			$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
			$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		}
		
		return $this->render('item-updateinfo', ['FlowRes' => $WorkFlowRes, 'FlowDetailRes' => $WorkFlowDetailRes]);
	}
	
	#机器过户 详情页
	public function actionFlowItemTransfer() {
		$flow_id = $this->get('flow_id');
		if(!$flow_id) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$WorkFlowRes = $WorkFlowModel->find()->joinwith('flowrole')->where(['flow_id' => $flow_id])->asArray()->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		
		#记录访客
		$countRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		if(!self::vistorRecord($flow_id)) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		if($countRes['flow_lookcount']) {
			$lookcount = json_decode($countRes['flow_lookcount'], true);
			$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
			$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		}
		
		return $this->render('item-transfer', ['FlowRes' => $WorkFlowRes, 'FlowDetailRes' => $WorkFlowDetailRes]);
	}
	
	#更换机器 详情页
	public function actionFlowItemReplacemachine() {
		$flow_id = $this->get('flow_id');
		if(!$flow_id) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$TradeMainModel = new TradeMain();
		
		#记录访客
		$countRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		if(!self::vistorRecord($flow_id)) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		if($countRes['flow_lookcount']) {
			$lookcount = json_decode($countRes['flow_lookcount'], true);
			$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
			$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		}
		
		$WorkFlowRes = $WorkFlowModel->find()->joinwith('flowrole')->where(['flow_id' => $flow_id])->asArray()->one(); #print_r($WorkFlowRes);exit;
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one(); #print_r($WorkFlowDetailRes);exit;
		
		if( $WorkFlowRes['flow_orderid'] ) {
			$orderRes = $TradeMainModel->find()->where(['trade_orderid' => $WorkFlowRes['flow_orderid']])->asArray()->one();
		} else {
			$orderRes = [];
		}
		
		$MemberPdtModel = new MemberPdt();
		$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $WorkFlowDetailRes['flow_unionid']])->asArray()->one();
		
		return $this->render('item-replacemachine', ['FlowRes' => $WorkFlowRes, 'FlowDetail' => $WorkFlowDetailRes, 'MemberPdtRes' => $MemberPdtRes, 'OrderRes' => $orderRes]);
	}
	
	#更换机器流程 重新分配机器页
	public function actionReplacemachineItemReset() {
		$flow_id = $this->get('flow_id');
		$unionid = $this->get('unionid');
		
		if(!$flow_id || !$unionid) {
			return $this->redirect(['/work-flow/waiting-list']);
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$MemberPdtModel = new MemberPdt();
		$PdtManageTypeModel = new PdtManageType();
		$RoomManageModel = new PdtRoomMange();
		
		$MemberPdtRes = $MemberPdtModel->find()->select('*')->With('servertype')->With('pdtroom')->With('pdtmanage')->where(['unionid'=>$unionid])->asArray()->one();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		$Detail_afterconfig = json_decode($WorkFlowDetailRes['flow_after_config'],true); #print_r($Detail_afterconfig);exit;
		
		#当前机器的配置
		$config = json_decode($MemberPdtRes['config'],true); 
		#当前所属机器类别
		$pdtmanage = $MemberPdtRes['pdtmanage'][0];
		#当前服务器分类
		$servertype = $MemberPdtRes['servertype'][0];
		
		#获取机房
		$room_list = $RoomManageModel->find()->AsArray()->All();

		#获取服务器分类		
		$pdt_type_list = $PdtManageTypeModel->find()->AsArray()->All();
		#获取供应商
		$ProviderModel = new Provider();
		$ProviderList = $ProviderModel->find()->asArray()->all();
		$IPArr = json_decode($MemberPdtRes['ip'], true); #print_r($IPArr);exit;
		$IPstr = implode(',', $IPArr);
		return $this->render('item-replacemachine-reset',[
			'MemberPdtRes' => $MemberPdtRes,
			'FlowDetail' => $WorkFlowDetailRes,
			'Detail_afterconfig' => $Detail_afterconfig,
			'room_list' => $room_list,
			'nowprovider' => $MemberPdtRes['servicerprovider'],
			'pdt_type_list' => $pdt_type_list,
			'config' => $config,
			'pdtmanage' => $pdtmanage,
			'servertype' => $servertype,
			'IPstr' => $IPstr,
			'ProviderList' => $ProviderList,
		]);		
	}
	
	#更换机器流程 重新分配机器 提交处理
	public function actionReplacemachineDoReset() {
		Yii::$app->request->isAjax || die('error');

        $MemberPdtModel = new MemberPdt();
        $IdlePdtModel = new IdlePdt();
		$TestServerModel = new TestServer();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$WorkFlowModel = new WorkFlow();
		$SupplierIpModel = new SupplierIp();
		
		$post = DataHelper::dotrim($this->post()); #print_r($post);exit;
		$data = $post['data'];
		$unionid = $post['unionid'];
		$testid = $data['test_id'];
		$provider = $post['provider'];
		$flow_id = $post['flow_id'];
		#获取当前产品信息
		$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $unionid])->asArray()->one();
		
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		
		#开启事务
		$transaction = Yii::$app->db->beginTransaction();
		#获取子流程信息
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id, 'flow_unionid' => $unionid])->one();
		
		$afterConfig = [];
		$frontConfig = json_decode($WorkFlowDetailRes->flow_front_config, true);
		$oldFlowData_after = json_decode($WorkFlowDetailRes->flow_after_config, true);	
		
		$afterConfig['unionid'] = $oldFlowData_after['unionid'];
		$afterConfig['flow_promter'] = $oldFlowData_after['flow_promter'];
		$afterConfig['use_original_ip'] = $data['use_original_ip'];
		$afterConfig['addition'] = $data['addition'];
		
		$afterConfig['provider'] = $provider;
		
		
		$configRemark = $data['remark'];
		
		if( $provider == 0) {
			if( $data['use_original_ip'] == 1 && $data['addition'] == 1) {
				$arrReturn = [
					'status' => 0,
					'info' => '更换自有机器补录不能选择使用原IP'
				];
				return $this->renderJSON($arrReturn);
			}
		}
		
		#因为是重新分配，这里提前将之前入库的供应商IP删除掉（只限于之前分配的为供应商的非测试机器）
		if($oldFlowData_after['provider'] == 1 && !$oldFlowData_after['test_id']) {
			$bigIpArray = array_unique(array_merge($oldFlowData_after['ip2'],$frontConfig['ip2']));
			$return_insert = array_diff($bigIpArray, $frontConfig['ip2']);  #取出之前入库的供应商IP
			#$return_delete = array_diff($bigIpArray, $oldFlowData_after['ip2']);
			$delteRes = $SupplierIpModel->deleteAll(['ip' => $return_insert]);
			if( count($return_insert) != $delteRes)
			{
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info' => '供应商IP删除处理失败'
				];
				return $this->renderJSON($arrReturn);
			}
		}
		if( isset($testid) && $testid) {
			$afterConfig['testid'] = $data['test_id'];
		}
		if($provider == 0) 
		{
			#自有服务器查询自有ID
			$IdlePdtModel = new IdlePdt();
			$IdleRes = $IdlePdtModel->find()->where(['id' => $data['idle_id']])->asArray()->one();
			
			$afterConfig['servicerprovider'] = 0;
			$afterConfig['server_typeid'] = $data['server_typeid'];			
			$afterConfig['pdt_id'] = $IdleRes['pdt_id'];
			$afterConfig['room_id'] = $IdleRes['room_id'];			
			$afterConfig['idle_id'] = $data['idle_id'];
			$afterConfig['ipmi_ip'] = $IdleRes['ipmi_ip'];
			
			$afterConfig['config'] = json_decode($IdleRes['config'], true);
			$afterConfig['config']['requirement_bandwidth'] = $data['requirement_bandwidth'];
			
			$afterConfig['ip'] = json_decode($IdleRes['ip'], true);
			$afterConfig['ip2'] = json_decode($IdleRes['ip2'], true);
			#用于检测机房是否一致
			$check_room_id = $IdleRes['room_id'];
			
		} else {
			#供应商服务器，所有数据均由前台传过来，直接赋值写入
			$afterConfig['servicerprovider'] = 1;
			$afterConfig['server_typeid'] = $data['server_typeid'];
			$afterConfig['pdt_id'] = $data['pdt_id'];
			$afterConfig['provider_id'] = $data['provider_id'];
			$afterConfig['room_id'] = $data['room_id'];
			$afterConfig['ipmi_ip'] = '';
			
			$afterConfig['config'] = $data['config'];
			$afterConfig['config']['requirement_bandwidth'] = $data['requirement_bandwidth'];
			
			$check_provider_id = $data['provider_id'];
			$check_room_id = $data['room_id'];
					
			$afterConfig['ip'] = DataHelper::dotrim($data['iplist']);
			
			#如果使用原IP ，供应商可以不提交IP
			if( $afterConfig['use_original_ip'] != 1) {
				if( !$afterConfig['ip'] || empty($afterConfig['ip']) ) {
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => '供应商IP为必填选项'
					];
					return $this->renderJSON($arrReturn);
				}	
			}			
			$afterConfig['ip2'] = [];			
			#解析IP段
			$Retuen_IPArray = DataHelper::splitIP($data['iplist']);
			if( !$Retuen_IPArray['status'] ) {
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info'=> $Retuen_IPArray['info']
				];
				return $this->renderJSON($arrReturn);
			}
			$afterConfig['ip2'] = $Retuen_IPArray['data'];

			#如果是测试机
			if( isset($testid) && $testid ) 
			{
				#如果是测试机，不用验证IP
				/*$TestServerRes = $TestServerModel->find()->where(['id' => $testid])->asArray()->one();
				
				$ip2 = json_decode($TestServerRes['ip2'],true);
				$diffArray = array_diff($afterConfig['ip2'],$ip2); #print_r($diffArray);
				
				if( !empty($diffArray) ) {
					$CheckRes = DataHelper::CheckIP_isused($diffArray);
					if( $CheckRes['status'] == 0 ) {
						$transaction->rollBack();
						$arrReturn = [
						   'status' => 0,
						   'info' => $CheckRes['info']
						];
						return $this->renderJSON($arrReturn);
					}
				}*/
				
			} else {
				$bigIpArray = array_unique(array_merge($afterConfig['ip2'],$frontConfig['ip2']));
				##取出此次分配的IP（因为IP可能为正在用的，所以排除用的）
				$now_return_insert = array_diff($bigIpArray, $frontConfig['ip2']);
				
				#$CheckRes = DataHelper::CheckIP_isused($afterConfig['ip2']);
				/*$CheckRes = DataHelper::CheckIP_isused($now_return_insert);
				if( $CheckRes['status'] == 0 ){
					$transaction->rollBack();
					$arrReturn = [
					   'status' => 0,
					   'info' => $CheckRes['info']
					];
					return $this->renderJSON($arrReturn);
				}*/
				#检测新增的IP
				$CheckRes = DataHelper::detect_supplierip($now_return_insert);
				if( $CheckRes['status'] == 0 )
				{
					$transaction->rollBack();
					return $this->renderJSON([
						'status' => 0,
						'info' => $CheckRes['info']
					]);
				}		
			}
		}
		
		#改变自有服务器状态，如果是测试服务器则不需要改		
		$updateIdleOld = true;
		$updateIdleNew = true;
		
		#测试机改为占用
		$updateTestOld = true;
		$updateTestNew = true;
		
		#如果选择的为测试机 将测试机改为占用
		if($afterConfig['test_id'] && isset($afterConfig['test_id']) ) 
		{
			$TestServerRes = $TestServerModel->find()->where(['id' => $afterConfig['test_id']])->one();
			$TestServerRes->status = 2;
			$updateTestNew = $TestServerRes->save();
		} else {
			#如果新数据没有测试ID，并且是idle_id存在，那么则更新新机器的状态为待定
			if(isset($afterConfig['idle_id']) && $afterConfig['idle_id']) 
			{
				$IdlePdtNewRes = $IdlePdtModel->find()->where(['id' => $afterConfig['idle_id']])->one();
				$IdlePdtNewRes->status = 2;
				$updateIdleNew = $IdlePdtNewRes->save();
			} else {
				#且选择为供应商的。将IP入库下来  #不使用原IP的才入库				
				if( $afterConfig['use_original_ip'] != 1 ) 
				{
					#执行需要写入数据库的IP
					if( !empty($now_return_insert) ) 
					{
						$insertRes = $SupplierIpModel->add_peration($now_return_insert, $afterConfig['provider_id'], $afterConfig['room_id'], '3');
						if( $insertRes != count($now_return_insert)) {
							$transaction->rollBack();
							$arrReturn = [
								'status' => 0,
								'info' => '供应商IP入库失败'
							];
							return $this->renderJSON($arrReturn);
						}
					}
				}				
			}
		}
		
		#判断之前是选择的什么机器（如果是自有的。将自有的由待定改为闲置，如果为测试机，将由占用改为测试服务器）		
		if($WorkFlowDetailRes->flow_after_config) 
		{		
			if(isset($oldFlowData_after['test_id']) && $oldFlowData_after['test_id']) {
				$TestServerOldRes = $TestServerModel->find()->where(['id' => $oldFlowData_after['test_id']])->one();
				$TestServerOldRes->status = 0;
				$updateTestOld = $TestServerOldRes->save();
			} else {
				#如果不是测试机，并且以前的配置是自有机器，那么则修改之前的机器状态为闲置
				if(isset($oldFlowData_after['idle_id']) && $oldFlowData_after['idle_id']) {
					$IdlePdtOldRes = $IdlePdtModel->find()->where(['id' => $oldFlowData_after['idle_id']])->one();
					$IdlePdtOldRes->status = 0;
					$updateIdleOld = $IdlePdtOldRes->save();
				} else {
					#
				}
			}
		}
		
		#判断是否补录机器
		if($afterConfig['addition'] == 1) {
			$WorkFlowDetailRes->flow_fix_id = $adminId;
			$WorkFlowDetailRes->flow_fix_name = $adminName;
			$WorkFlowDetailRes->flow_fix_confirm_time = time();
			$WorkFlowDetailRes->flow_fix_finish_time = time();			
			if($provider == 0) {
				//自有服务器
				$IdleRes = $IdlePdtModel->find()->where(['id' => $data['idle_id']])->asArray()->one();
				$afterConfig['install_account'] = $IdleRes['re_username'];
				$afterConfig['install_pass'] = $IdleRes['re_password'];
				$afterConfig['install_port'] = $IdleRes['re_port'];
			} else {
				//供应商
				if(isset($afterConfig['test_id']) && $afterConfig['test_id'] != '') {
					$TestServerRes = $TestServerModel->find()->where(['id' => $afterConfig['test_id']])->asArray()->one();
					$afterConfig['install_account'] = $TestServerRes['account_name'];
					$afterConfig['install_pass'] = $TestServerRes['account_pwd'];
					$afterConfig['install_port'] = $TestServerRes['account_port'];
				} else {
					$afterConfig['install_account'] = $data['supplier_account'];
					$afterConfig['install_pass'] = $data['supplier_pass'];
					$afterConfig['install_port'] = $data['supplier_port'];
				}
			}

			#全都是补录
			$FlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
			$FlowRes->flow_operate_audit_status = '无需审核';
			$FlowRes->flow_operate_isfinish = '配置完成';
			$updateFlow = $FlowRes->save();
			
		} else {
			$WorkFlowDetailRes->flow_fix_id = null;
			$WorkFlowDetailRes->flow_fix_name = null;
			$WorkFlowDetailRes->flow_fix_confirm_time = null;
			$WorkFlowDetailRes->flow_fix_finish_time = null;
			$updateFlow = true;
		}
		
		#判断IP是否使用之前IP  #如果等于1 就是用之前的IP		
		if( $afterConfig['use_original_ip'] == 1) 
		{
			#如果提供商为一致的	
			if( $MemberPdtRes['servicerprovider'] == $provider) {
				if( $provider == 0) {
					#如果为自有的，判断机房是否一致
					if( $check_room_id != $MemberPdtRes['room_id'] ) {
						$transaction->rollBack();
						$arrReturn = [
							'status' => 0,
							'info' => '选择使用原IP，但更换机器前后机房不一致！'
						];
						return $this->renderJSON($arrReturn);
					}
				} else {
					#如果为供应商的。判断供应商和机房是否一致
					if( $check_provider_id != $MemberPdtRes['provider_id'] || $check_room_id != $MemberPdtRes['room_id']) {
						$transaction->rollBack();
						$arrReturn = [
							'status' => 0,
							'info' => '选择使用原IP，但更换机器前后机房或供应商不一致！'
						];
						return $this->renderJSON($arrReturn);
					}
				}				
				$afterConfig['should_ip'] = $afterConfig['ip']; #更换机器后本应的IP				
				$afterConfig['ip'] = json_decode($MemberPdtRes['ip'], true);
				$afterConfig['ip2'] = json_decode($MemberPdtRes['ip2'], true);
				
				$WorkFlowDetailRes->flow_ip = $MemberPdtRes['ip'];
				$WorkFlowDetailRes->flow_ip2 = $MemberPdtRes['ip2'];
			} else {
				#但如果提供商之前和现在不是一致的
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info' => '产品之前服务提供者与更换后不一致，不能选择使用之前IP！'
				];
				return $this->renderJSON($arrReturn);
			}			
		} else {
			$afterConfig['should_ip'] = [];		
			$WorkFlowDetailRes->flow_ip = json_encode($afterConfig['ip'], JSON_UNESCAPED_UNICODE);
			$WorkFlowDetailRes->flow_ip2 = json_encode($afterConfig['ip2'], JSON_UNESCAPED_UNICODE);
		}
		
		$after_config['is_exchangedip'] = 0;  #代表着未对换IP
		
		#同时 判断重新分配前，是否有进行IP对换 oldFlowData_after
		if( isset($oldFlowData_after['is_exchangedip']) && $oldFlowData_after['is_exchangedip'] == 1 ) {
			
			$last_after_IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $oldFlowData_after['idle_id']])->one();#上次更换后的自有机器信息对象
			$front_IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $frontConfig['idle_id']])->one();#用户产品更换前的自有机器信息对象
			
			$last_after_IdlePdtQuery->ip = $front_IdlePdtQuery->ip;
			$last_after_IdlePdtQuery->ip2 = $front_IdlePdtQuery->ip2;
			
			$front_IdlePdtQuery->ip = $MemberPdtRes['ip'];
			$front_IdlePdtQuery->ip2 = $MemberPdtRes['ip2'];
			
			if( !$front_IdlePdtQuery->update() || !$last_after_IdlePdtQuery->update() ) {
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info' => '恢复对换IP失败'
				];
				return $this->renderJSON($arrReturn);
			}
		}
		
		#是否有差价
		if( $data['have_price_difference'] == 1) {
			if( $data['cost_price'] == '' || $data['sell_price'] == '' ) {
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info' => '请输入正确的价格！'
				];
				return $this->renderJSON($arrReturn);
			}
			
			if( !is_numeric($data['cost_price']) || !is_numeric($data['sell_price']) ) {
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info' => '请输入正确的价格！'
				];
				return $this->renderJSON($arrReturn);
			}
			
			$afterConfig['have_price_difference'] = 1;
			$afterConfig['sell_price'] = $data['sell_price'];
			$afterConfig['cost_price'] = $data['cost_price'];
		} else {
			$afterConfig['have_price_difference'] = 0;
			$afterConfig['sell_price'] = $MemberPdtRes['sell_price'];
			$afterConfig['cost_price'] = $MemberPdtRes['cost_price'];
		}
		#将之前的补款价格保存
		$afterConfig['difference_price'] = $oldFlowData_after['difference_price'];
		$afterConfig['payment_cycle'] = $MemberPdtRes['payment_cycle'];

		#保存结果
		$WorkFlowDetailRes->flow_after_config = json_encode($afterConfig, JSON_UNESCAPED_UNICODE);
		$WorkFlowDetailRes->flow_config_remark = $configRemark;
		#同时考虑技术已经处理这个机器，这里将完成时间清空
		#$WorkFlowDetailRes->flow_fix_finish_time = null;
		#更新详情
		$updateRes = $WorkFlowDetailRes->save();
		
		if($updateRes && $updateTestNew && $updateTestOld && $updateIdleNew && $updateIdleOld && $updateFlow) {
			$transaction->commit();
			$arrReturn = [
                'status' => 1,
                'info' => '设置成功'
            ];            
		} else {
			$transaction->rollBack();
			$arrReturn = [
                'status' => 0,
                'info' => '保存配置时出现异常，请联系管理员'
            ];
		}
		return $this->renderJSON($arrReturn);
		
	}
	
	#详情页，新购相关，余额支付，线下打款，在线支付
	public function actionFlowItemPurchase() {
		$flow_id = $this->get('flow_id');
		if(!$flow_id) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}

		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$TradeMainModel = new TradeMain();
		
		#记录访客
		$countRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		if(!self::vistorRecord($flow_id)) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		if($countRes['flow_lookcount']) {
			$lookcount = json_decode($countRes['flow_lookcount'], true);
			$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
			$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		}
		
		
		$WorkFlowRes = $WorkFlowModel->find()->joinwith('flowrole')->where(['flow_id' => $flow_id])->asArray()->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->all();
		$orderRes = $TradeMainModel->find()->where(['trade_orderid' => $WorkFlowRes['flow_orderid']])->asArray()->one();
		
		return $this->render('item-purchase', ['FlowRes' => $WorkFlowRes, 'FlowDetail' => $WorkFlowDetailRes, 'OrderRes' => $orderRes]);
		
	}
	
	#详情页，续费相关，余额支付，线下打款，在线支付
	public function actionFlowItemRenew() {
		$flow_id = $this->get('flow_id');
		if(!$flow_id) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}

		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$TradeMainModel = new TradeMain();
		
		#记录访客
		$countRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		if(!self::vistorRecord($flow_id)) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		if($countRes['flow_lookcount']) {
			$lookcount = json_decode($countRes['flow_lookcount'], true);
			$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
			$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		}
		
		
		$WorkFlowRes = $WorkFlowModel->find()->joinwith('flowrole')->where(['flow_id' => $flow_id])->asArray()->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->all();
		$orderRes = $TradeMainModel->find()->where(['trade_orderid' => $WorkFlowRes['flow_orderid']])->asArray()->one();
		
		return $this->render('item-renew', ['FlowRes' => $WorkFlowRes, 'FlowDetail' => $WorkFlowDetailRes, 'OrderRes' => $orderRes]);
		
	}
	
	#详情页，更换IP，供应商，自有
	public function actionFlowItemReplaceip() {
		$flow_id = $this->get('flow_id');
		if(!$flow_id) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		
		#记录访客
		$countRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		if(!self::vistorRecord($flow_id)) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		if($countRes['flow_lookcount']) {
			$lookcount = json_decode($countRes['flow_lookcount'], true);
			$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
			$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		}
		
		$WorkFlowRes = $WorkFlowModel->find()->joinwith('flowrole')->where(['flow_id' => $flow_id])->asArray()->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		
		return $this->render('item-replaceip', ['FlowRes' => $WorkFlowRes, 'FlowDetail' => $WorkFlowDetailRes]);
	}
	
	#详情页，变更配置-余额支付，变更配置-在线支付，变更配置-线下打款  变更配置-无金额  变更配置-余额退款
	public function actionFlowItemChangeconfig() {
		$flow_id = $this->get('flow_id');
		if(!$flow_id) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$TradeMainModel = new TradeMain();
		
		#记录访客
		$countRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		if(!self::vistorRecord($flow_id)) {
			return $this->redirect(["/work-flow/waiting-list"]);
		}
		if($countRes['flow_lookcount']) {
			$lookcount = json_decode($countRes['flow_lookcount'], true);
			$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
			$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		}
		
		$WorkFlowRes = $WorkFlowModel->find()->joinwith('flowrole')->where(['flow_id' => $flow_id])->asArray()->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one(); #print_r($WorkFlowDetailRes);exit;
		
		if( $WorkFlowRes['flow_orderid'] ) {
			$orderRes = $TradeMainModel->find()->where(['trade_orderid' => $WorkFlowRes['flow_orderid']])->asArray()->one();
		} else {
			$orderRes = [];
		}
		
		$MemberPdtModel = new MemberPdt();
		$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $WorkFlowDetailRes['flow_unionid']])->asArray()->one();
		
		#获取IP分类
		$PdtIpClassModel = new PdtIpClass();
		$IpClassList = $PdtIpClassModel->find()->asArray()->all();
		#网段
		$PdtIpNetworkModel = new PdtIpNetwork();
		$IpNetworkList = $PdtIpNetworkModel->find()->where(['room_id' => $MemberPdtRes['room_id']])->asArray()->all();
		
		return $this->render('item-changeconfig', ['FlowRes' => $WorkFlowRes, 'FlowDetail' => $WorkFlowDetailRes, 
		
		'MemberPdtRes' => $MemberPdtRes, 'OrderRes' => $orderRes,
		'IpClassList' => $IpClassList, 'IpNetworkList' => $IpNetworkList
		]);
	}
	
	#技术完成流程处理 方法(更换IP)
	public function actionComplete() {
		Yii::$app->request->isAjax || die('error');
		$time = time();
		
		$post = $this->post();
		$detail_id = $this->post('detail_id');
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$WorkFlowRoleModel = new WorkFlowRole();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['id' => $detail_id])->one();
		if( empty($WorkFlowDetailRes) ) {
			$arrReturn = [
				'status' => 0,
				'info' => '不存在的流程分表信息'
			];
			return $this->renderJSON($arrReturn);
		}
		$afterConfig = json_decode($WorkFlowDetailRes->flow_after_config, true);
		
		$afterConfig['complete_remark'] = $post['remark'];
		$WorkFlowDetailRes->flow_after_config = json_encode($afterConfig, JSON_UNESCAPED_UNICODE);
		
		#更新分表 完成时间
		$WorkFlowDetailRes->flow_fix_finish_time = $time;
		
		$updateDetailRes = $WorkFlowDetailRes->update(); #print_r($WorkFlowDetailRes->errors);exit;
		
		#查询出主表
		$WorkFlowQuery = $WorkFlowModel->find()->where(['flow_id' => $WorkFlowDetailRes->flow_id])->one();
		
		$WorkFlowQuery->flow_status = '处理完成';
		
		if( $WorkFlowQuery->flow_end_time == '' || !isset($WorkFlowQuery->flow_end_time) ) {
			$WorkFlowQuery->flow_end_time = time();
		}
		
		#
		$WorkFlowRoleRes = $WorkFlowRoleModel->find()->where(['wr_name' => $WorkFlowQuery->flow_name])->asArray()->one();
		
		if( $WorkFlowRoleRes['wr_handle_isverify'] == 'Y') {
			$WorkFlowQuery->flow_operate_isfinish = '配置完成';			
		}
		
		$updateWorkFlowRes = $WorkFlowQuery->update();
		
		
		/*if( $WorkFlowRoleRes['wr_money_isverify'] == 'Y') {
			$WorkFlowQuery->flow_money_isfinish = '已完成';
		}*/

		
		#完成处理后续操作
		$CompleteRes = self::finishflow($WorkFlowQuery->flow_id,$WorkFlowQuery->flow_name);
		
		if( $updateWorkFlowRes && $CompleteRes && $updateDetailRes) {
			
			if(self::checkFlowIsFinish($WorkFlowQuery->flow_id)) {
				$data = self::checkFlowFinishAfter($WorkFlowQuery->flow_id);
				if($data['status'] == 1) {
					$transaction->commit();
					$arrReturn = [
						'status' => 1,
						'info' => '处理完成'
					];
				} else {
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => $data['info']
					];	
				}
			} else {
				$transaction->commit();
				$arrReturn = [
					'status' => 1,
					'info' => '处理完成'
				];
			}
			
			
		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status' => 0,
				'info' => '处理更新失败！请联系技术查看问题'
			];			
		}
		return $this->renderJSON($arrReturn);
		
	}
	
	#完成流程处理 方法（有关款项的  变更配置）
	public function actionCompletemoney() {
		Yii::$app->request->isAjax || die('error');
		$time = time();
		
		$post = $this->post();
		$detail_id = $this->post('detail_id');
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['id' => $detail_id])->one();
		if( empty($WorkFlowDetailRes) ) {
			$arrReturn = [
				'status' => 0,
				'info' => '不存在的流程分表信息'
			];
			return $this->renderJSON($arrReturn);
		}
		$afterConfig = json_decode($WorkFlowDetailRes->flow_after_config, true);
		
		$afterConfig['complete_remark'] = $post['remark'];		
		$WorkFlowDetailRes->flow_after_config = json_encode($afterConfig, JSON_UNESCAPED_UNICODE);
		$WorkFlowDetailRes->flow_fix_finish_time = time();
		#更新分表 		
		$updateDetailRes = $WorkFlowDetailRes->update(); #print_r($WorkFlowDetailRes->errors);exit;
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $WorkFlowDetailRes->flow_id])->one();
		$WorkFlowRes->flow_operate_isfinish = '配置完成';
		$updateWorkFlowRes = $WorkFlowRes->update();
		#完成处理后续操作
		$CompleteRes = self::finishflow($WorkFlowRes['flow_id'],$WorkFlowRes['flow_name']);
		#echo $CompleteRes.'<br/>';echo $updateDetailRes.'<br/>';echo $updateWorkFlowRes;exit;
		
		if( $CompleteRes && $updateDetailRes && $updateWorkFlowRes) {
			
			if(self::checkFlowIsFinish($WorkFlowRes['flow_id'])) { 
				$data = self::checkFlowFinishAfter($WorkFlowRes['flow_id']);
				if($data['status'] == 1) {
					$transaction->commit();
					$arrReturn = [
						'status' => 1,
						'info' => '处理完成'
					];
				} else {
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => $data['info']
					];
				}
			} else {
				$transaction->commit();
				$arrReturn = [
					'status' => 1,
					'info' => '处理完成'
				];
			}
		} else {

			$transaction->rollBack();
			$arrReturn = [
				'status' => 0,
				'info' => '变更配置处理更新失败！请联系技术查看问题'
			];
		}
		return $this->renderJSON($arrReturn);
		
	}
	
	#完成操作。根据不同的流程  进入不同的处理方法（这里只有变更IP  后期改到公共函数中）
	public function finishflow($flow_id, $flow_name) {
		$return = false;		
		$yes_or_no = 'Y';
		switch($flow_name) {
			case '变更IP-自有机器':
				$return = self::replaceip($flow_id, $yes_or_no);
				break;
			case '变更IP-供应商':
				$return = self::replaceip($flow_id, $yes_or_no);
				break;	
			case '变更配置-余额支付':
				$return = self::changeconfig($flow_id, $yes_or_no);
				break;
			case '变更配置-在线支付':
				$return = self::changeconfig($flow_id, $yes_or_no);
				break;
			case '变更配置-线下打款':
				$return = self::changeconfig($flow_id, $yes_or_no);
				break;
			case '变更配置-余额退款':
				$return = self::changeconfig($flow_id, $yes_or_no);
				break;
			case '变更配置-无金额':
				$return = self::changeconfig($flow_id, $yes_or_no);
				break;
			default:
			$return = true;
		}
		return $return;
	}
	
	#驳回中止操作。根据不同方法  进入不同的处理方法，将改动的更新到原来 （这里只有变更IP  后期改到公共函数中）
	public function rejectflow($flow_id, $flow_name) {
		$yes_or_no = 'N';
		switch($flow_name) {
			case '变更IP-自有机器':
				$return = self::replaceip($flow_id, $yes_or_no);
				break;
			case '变更IP-供应商':
				$return = self::replaceip($flow_id, $yes_or_no);
				break;
			case '变更配置-余额支付':
				$return = self::changeconfig($flow_id, $yes_or_no);
				break;
			case '变更配置-在线支付':
				$return = self::changeconfig($flow_id, $yes_or_no);
				break;
			case '变更配置-线下打款':
				$return = self::changeconfig($flow_id, $yes_or_no);
				break;
			case '变更配置-余额退款':
				$return = self::changeconfig($flow_id, $yes_or_no);
				break;
			case '变更配置-无金额':
				$return = self::changeconfig($flow_id, $yes_or_no);
				break;
			default:
			$return = true;
		}		
		return $return;
	}

	/**
	*	处理变更配置 方法
	*	flow_id 流程ID   yes_or_no 表示是完成 或者 驳回
	**/
	public function changeconfig($flow_id, $yes_or_no){
		
		$MemberPdtModel = new MemberPdt();
		$WorkFlowRoleModel = new WorkFlowRole();
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$UserMemberModel = new UserMember();
		$PdtIpModel = new PdtIp();
		$TradeMainModel = new TradeMain();
		$TradeDetailModel = new TradeDetail();
		$IdlePdtModel = new IdlePdt();
		
			
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->asArray()->one();

		#如果是完成
		if( $yes_or_no == 'Y') {					
			
			#这里为技术处理完成	
			$WorkFlowQuery = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
			#设置
			if( $WorkFlowQuery->flow_operate_isfinish == '配置完成') {
				$updateFlowRes = true;
			} else {
				$WorkFlowQuery->flow_operate_isfinish = '配置完成';
				$updateFlowRes = $WorkFlowQuery->update();
			}
			
			$WorkFlowDetailQuery = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->one();	
			if( $WorkFlowDetailQuery->flow_fix_finish_time != '' || !isset($WorkFlowDetailQuery->flow_fix_finish_time) ) {
				$updateFlowDetailRes = true;
			} else {
				$WorkFlowDetailQuery->flow_fix_finish_time = time();
				$updateFlowDetailRes = $WorkFlowDetailQuery->update(false);	
			}
			
		} else {
			#如果是驳回
			if( $WorkFlowRes['flow_name'] == '变更配置-无金额') {
				#无处理
			} else if( $WorkFlowRes['flow_name'] == '变更配置-余额支付') {
				
			} else if( $WorkFlowRes['flow_name'] == '变更配置-在线支付' ){
				
			} else if( $WorkFlowRes['flow_name'] == '变更配置-线下打款' ){
				
			} else if( $WorkFlowRes['flow_name'] == '变更配置-余额退款' ){
				
			}	
			#$WorkFlowQuery = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
			#$updateFlowRes = $WorkFlowQuery->update();
			$updateFlowRes = true;
			$updateFlowDetailRes = true;
		}
		#echo $updateFlowRes.'<br/>';echo $updateFlowDetailRes;exit;
		if( $updateFlowRes && $updateFlowDetailRes) {
			return true;
		} else {
			return false;
		}
		
		
		
	}
	/**
	* 	处理变更IP方法
	*	flow_id 流程ID   yes_or_no 表示是完成 或者 驳回
	**/
	public function replaceip($flow_id, $yes_or_no) {
		$MemberPdtModel = new MemberPdt();
		$WorkFlowRoleModel = new WorkFlowRole();
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$UserMemberModel = new UserMember();
		$PdtIpModel = new PdtIp();
		$IdlePdtModel = new IdlePdt();
		$SupplierIpModel = new SupplierIp();
		$SwitchLineModel = new SwitchLine();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		$after_config = json_decode($WorkFlowDetailRes['flow_after_config'], true);
		
		$ip = $after_config['ip'];
		$ip2 = $after_config['ip2'];
		
		#获取用户产品
		$MemberPdtQuery = $MemberPdtModel->find()->where(['unionid' => $WorkFlowDetailRes['flow_unionid']])->one();
		if( empty($MemberPdtQuery) ) {
			return false;
		}
		#开启事务
		$transaction = Yii::$app->db->beginTransaction();
		
		#原用户产品IP
		$oldIP = json_decode($MemberPdtQuery->ip2,true);
		$result_bangding = [];
		$result_jiechu = [];
		
		$bgIPArray = array_unique(array_merge($ip2,$oldIP));
		#如果产品是自有库产品，将新选择的IP 状态改为使用 （方法：作比较，获取2个IP数组的差集  先将2个数组合并并且去重）
		if( $MemberPdtQuery->servicerprovider == 0 && $MemberPdtQuery->idle_id !="") 
		{
			$result_bangding = array_diff($bgIPArray,$oldIP); 	#需要改为使用中的				
			$result_jiechu = array_diff($bgIPArray,$ip2); 	#需要改为闲置的
		} else {
			$result_bangding = array_diff($bgIPArray,$oldIP); 	#需要改为使用中的				
			$result_jiechu = array_diff($bgIPArray,$ip2); 	#需要删除的
		}

		if( $yes_or_no == 'Y') 
		{
			$updateIdleRes = true;
			#如果为自有机器，更新自有IP中的对应状态
			if( $MemberPdtQuery->servicerprovider == 0 && $MemberPdtQuery->idle_id !="") 
			{
				#新的ip需要绑定的 改为使用
				if( !empty($result_bangding) ) {
					$res1 = $PdtIpModel->updateAll(['status' => 1,'update_time' => time()],['ip'=>$result_bangding]);
				}			
				#需要解除绑定的IP  改为闲置
				if( !empty($result_jiechu) ) {
					$res2 = $PdtIpModel->updateAll(['status' => 0,'update_time' => time()],['ip'=>$result_jiechu]);			
				}
			} else {
				#如果为供应商机器，将新增的IP的状态改为使用，解除的IP从供应商IP库中删除
				#新的ip需要绑定的 改为使用
				if( !empty($result_bangding) ) {
					$res1 = $SupplierIpModel->updateAll(['status' => 1,'update_time' => time()],['ip'=>$result_bangding]);
				}		
				#需要解除绑定的IP  从库中删除
				if( !empty($result_jiechu) ) {
					$res2 = $SupplierIpModel->deleteAll(['ip'=>$result_jiechu]);			
				}
			}

			#更新用户产品新的IP
			$MemberPdtQuery->ip = json_encode($ip);
			$MemberPdtQuery->ip2 = json_encode($ip2);
			#如果是自有机器，将同步更新IP
			if( $MemberPdtQuery->servicerprovider == 0 && $MemberPdtQuery->idle_id !="" ) {
				
				$IdlePdtQuery = $IdlePdtModel->findone($MemberPdtQuery->idle_id);
				
				#获取IP对应线路的Vlan
				$PdtIpAll = $PdtIpModel->find()->select('room_id, line_type_id,vlan')->where(['in','ip',$ip2])->asArray()->all();
				$line_typeList = array_column($PdtIpAll, 'line_type_id'); 
				$line_typeList = array_unique($line_typeList);
				if( count($line_typeList) > 1 ) {
					$arrReturn = [
						'status' => 0,
						'info' => '所选IP与其他IP的线路类型不匹配'
					];
					return $this->renderJSON($arrReturn);
				}
					
				#获取机器对应交换机的线路信息
				$SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtQuery->switch_location, 'line_type_id' => $line_typeList[0]])->asArray()->one();
				if( empty($SwitchLineList) ) {
					$arrReturn = [
						'status' => 0,
						'info' => '机器对应的交换机线路中未有IP的线路类型'
					];
					return $this->renderJSON($arrReturn);
				}
				
				$IdlePdtQuery->ip = json_encode($ip);
				$IdlePdtQuery->ip2 = json_encode($ip2);
				
				if($SwitchLineList) {
					$IdlePdtQuery->vlan = $SwitchLineList['switch_vlan'];
				}
				$IdlePdtQuery->update_time = time();
				$updateIdleRes = $IdlePdtQuery->update(false);
			}
			
			$updateFlowRes = true;
		} else {
			$updateIdleRes = true;
			
			if( $MemberPdtQuery->servicerprovider == 0 && $MemberPdtQuery->idle_id !="") 
			{
				#新的ip需要绑定的  之前为待定改为闲置
				if( !empty($result_bangding) ) {
					$res1 = $PdtIpModel->updateAll(['status' => 0,'update_time' => time()],['ip'=>$result_bangding]);
				}				
				#需要解除绑定的IP  之前为待定改为使用
				if( !empty($result_jiechu) ) {
					$res2 = $PdtIpModel->updateAll(['status' => 1,'update_time' => time()],['ip'=>$result_jiechu]);			
				}
			} else {
				#供应商的（将本应新增的，从库中删除）
				if( !empty($result_bangding) ) {
					$res2 = $SupplierIpModel->deleteAll(['ip'=>$result_bangding]);			
				}
			}
				
			$WorkFlowQuery = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
			
			if( $WorkFlowQuery->flow_end_time == '' || !isset($WorkFlowQuery->flow_end_time) ) {
				$WorkFlowQuery->flow_end_time = time();
				$updateFlowRes = $WorkFlowQuery->update();
			} else {
				$updateFlowRes = true;
			}
			
		}
		
		
		#同时更新状态为正常
		$MemberPdtQuery->status = 1;
		#echo $updateFlowRes;echo "<pre>";echo $updateIdleRes;echo "<pre>";echo $MemberPdtQuery->update();exit;
		if( !$MemberPdtQuery->update() || !$updateFlowRes || !$updateIdleRes) {
			$transaction->rollBack();
			return false;
		} else {
			$transaction->commit();
			return true;
		}		
	}
	
	#中止事务 完成（更换IP）
	public function actionFinishStopflowReplaceip() {
		Yii::$app->request->isAjax || die('error');
		$flow_id = $this->post('flow_id');
		
		$time = time();
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$WorkFlowDetailModel = new WorkFlowDetail();
		$WorkFlowModel = new WorkFlow();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		
		$WorkFlowQuery = $WorkFlowModel->find()->where(['flow_id'=>$WorkFlowDetailRes['flow_id']])->one();
		
		$ReturnRes = self::rejectflow($WorkFlowQuery->flow_id, $WorkFlowQuery->flow_name);
		
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		
		$WorkFlowQuery->flow_status = '中止事务完成';
		$WorkFlowQuery->flow_suspension_auditor_id = $adminId;
		$WorkFlowQuery->flow_suspension_auditor_name = $adminName;
		$WorkFlowQuery->flow_suspension_auditor_time = time();
		$WorkFlowQuery->flow_suspension_auditor_reason = trim($this->post('suspension_reason'));
		
		if( $ReturnRes && $WorkFlowQuery->update() ) {
			$transaction->commit();
			$arrReturn = [
				'status' => 1,
				'info' => '事务中止完成'
			];
		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status' => 0,
				'info' => '事务中止完成异常'
			];
		}
		return $this->renderJSON($arrReturn);

	}
	
	#中止事务 完成（变更配置）（暂时无用）
	public function actionFinishStopflowChangeconfig() {
		Yii::$app->request->isAjax || die('error');
		$detail_id = $this->post('detail_id');
		if( !$detail_id ) {
			$arrReturn = [
				'status' => 0,
				'info' => '缺少必要参数'
			];
			return $this->renderJSON($arrReturn);
		}
		$time = time();
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$WorkFlowDetailModel = new WorkFlowDetail();
		$WorkFlowModel = new WorkFlow();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['id' => $detail_id])->asArray()->one();
		
		$WorkFlowQuery = $WorkFlowModel->find()->where(['flow_id'=>$WorkFlowDetailRes['flow_id']])->one();
		
		$ReturnRes = self::rejectflow($WorkFlowQuery->flow_id, $WorkFlowQuery->flow_name);
	
		if( $ReturnRes ) {
			if(self::checkFlowIsFinish($WorkFlowDetailRes['flow_id'])) {
				$data = self::checkFlowFinishAfter($WorkFlowDetailRes['flow_id']);
				if($data['status'] == 1) {
					$transaction->commit();
					$arrReturn = [
						'status' => 1,
						'info' => '事务中止完成'
					];
				} else {
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => $data['info']
					];	
				}
			} else {
				$transaction->commit();
				$arrReturn = [
					'status' => 1,
					'info' => '事务中止完成'
				];
			}
			
		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status' => 0,
				'info' => '事务中止完成异常'
			];
		}
		return $this->renderJSON($arrReturn);

	}
	
	#新购确认安装信息
	public function actionFixInstallFinish() {
		Yii::$app->request->isAjax || die('error');
		$detail_id = $this->post('detail_id');
		$install_account = $this->post('account');
		$install_pass = $this->post('pass');
		$install_port = $this->post('port');
		$install_remark = $this->post('remark');
		
		if(!$detail_id || !$install_account || !$install_pass || !$install_port) {
			$arrReturn = [
				'status'=>0,
				'info'=>'缺少必要参数'
			];
			return $this->renderJSON($arrReturn);
		}
		
		$time = time();
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$WorkFlowDetailModel = new WorkFlowDetail();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['id' => $detail_id])->one();
		$config = json_decode($WorkFlowDetailRes->flow_after_config, true);
		$config['install_account'] = $install_account;
		$config['install_pass'] = $install_pass;
		$config['install_port'] = $install_port;
		$config['install_remark'] = $install_remark;
		
		$WorkFlowDetailRes->flow_after_config = json_encode($config, JSON_UNESCAPED_UNICODE);
		$WorkFlowDetailRes->flow_fix_finish_time = $time;
		$updateRes = $WorkFlowDetailRes->save();
		
		#检查是否所有都完成了技术相关操作
		$WorkFlowAllDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $WorkFlowDetailRes->flow_id])->asArray()->all();
		$checkIsFinish = true;
		foreach($WorkFlowAllDetailRes as $key => $val) {
			if(!$val['flow_fix_finish_time']) {
				$checkIsFinish = false;
				break;
			}
		}
		
		if($checkIsFinish == true) {
			$WorkFlowModel = new WorkFlow();
			$MainFlow = $WorkFlowModel->find()->where(['flow_id' => $WorkFlowDetailRes->flow_id])->one();
			$MainFlow->flow_operate_isfinish = '配置完成';
			$updateMain = $MainFlow->save();
		} else {
			$updateMain = true;
		}
		
		if($updateRes && $updateMain) {
			
			if(self::checkFlowIsFinish($WorkFlowDetailRes->flow_id)) {
				$data = self::checkFlowFinishAfter($WorkFlowDetailRes->flow_id);
				if($data['status'] == 1) {
					$transaction->commit();
					$arrReturn = [
						'status' => 1,
						'info' => '处理完成'
					];
				} else {
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => $data['info']
					];	
				}
			} else {
				$transaction->commit();
				$arrReturn = [
					'status' => 1,
					'info' => '处理完成'
				];
			}
			
		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'更新配置信息出现异常'
			];			
		}
		return $this->renderJSON($arrReturn);
		
	}
	
	#新购填写成本
	public function actionFillCost() {
		Yii::$app->request->isAjax || die('error');
		$detail_id = $this->post('detail_id');
		
		$cost_base = $this->post('cost_base');
		$cost_part = $this->post('cost_part');
		$cost_type = $this->post('cost_type');
		$cost_commission = $this->post('cost_commission');
		
		if($cost_base == '' || $cost_part == '' || $cost_type == '' || $cost_commission == '') {
			$arrReturn = [
				'status'=>0,
				'info'=>'请正确填写成本各项'
			];
			return $this->renderJSON($arrReturn);
		}
		
		$time = time();
		$adminName = Yii::$app->session['admin']['rename'];
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$WorkFlowDetailModel = new WorkFlowDetail();
		$DetailRes = $WorkFlowDetailModel->find()->where(['id' => $detail_id])->one();
		$after_config = json_decode($DetailRes->flow_after_config, true);
		$after_config['cost_commission'] = $cost_commission;
		
		$DetailRes->flow_after_config = json_encode($after_config, JSON_UNESCAPED_UNICODE);
		$DetailRes->flow_cost_basic = $cost_base;
		$DetailRes->flow_cost_parts = $cost_part;
		$DetailRes->flow_cost_current = $cost_type;
		$DetailRes->flow_cost_settor_id = $adminId;
		$DetailRes->flow_cost_settor_name = $adminName;
		$updateCost = $DetailRes->save();
		
		#检查是否所有都完成了成本审核
		$WorkFlowAllDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $DetailRes->flow_id])->asArray()->all();
		$checkIsFinish = true;
		foreach($WorkFlowAllDetailRes as $key => $val) {
			if(!$val['flow_cost_settor_id']) {
				$checkIsFinish = false;
				break;
			}
		}
		
		if($checkIsFinish == true) {
			$WorkFlowModel = new WorkFlow();
			$MainFlow = $WorkFlowModel->find()->where(['flow_id' => $DetailRes->flow_id])->one();
			$MainFlow->flow_money_isfinish = '已完成';
			$updateMain = $MainFlow->save();
		} else {
			$updateMain = true;
		}
		
		if($updateCost && $updateMain) {		
			
			if(self::checkFlowIsFinish($DetailRes->flow_id)) {
				$data = self::checkFlowFinishAfter($DetailRes->flow_id);

				if($data['status'] == 1) {
					$transaction->commit();
					$arrReturn = [
						'status' => 1,
						'info' => '处理完成'
					];
				} else {
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => $data['info']
					];	
				}
			} else {
				$transaction->commit();
				$arrReturn = [
					'status' => 1,
					'info' => '处理完成'
				];
			}
			
		} else {
			
			$transaction->rollBack(); print_r($DetailRes->errors);exit;
			$arrReturn = [
				'status'=>0,
				'info'=>'更新成本信息出现异常'
			];			
		}
		return $this->renderJSON($arrReturn);
	}
	
	#中止流程 公共方法
	public function actionStopFlow() {
		Yii::$app->request->isAjax || die('error');
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$flow_id = $this->post('flow_id');
		if(!$flow_id) {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'缺少必要参数'
			];
			return $this->renderJSON($arrReturn);
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$FlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		$FlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->one();
		if(!$FlowRes) {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'未知的流程'
			];
			return $this->renderJSON($arrReturn);
		}
		
		if(in_array($FlowRes->flow_status, ['处理完成', '处理驳回', '中止事务中', '中止事务完成', '等待预配置'])) {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'当前流程状态不可中止'
			];
			return $this->renderJSON($arrReturn);
		}
		
		
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		
		$FlowRes->flow_status = '中止事务中';
		$FlowRes->flow_suspension_auditor_id = $adminId;
		$FlowRes->flow_suspension_auditor_name = $adminName;
		$FlowRes->flow_suspension_auditor_time = time();
		$FlowRes->flow_suspension_auditor_reason = trim($this->post('suspension_reason'));

		#
		$flow_nameArr = explode('-', $FlowRes->flow_name);
		$flow_mainname = $flow_nameArr[0];
		#判断操作信息
		
		$moneyFinish = true;
		$operateFinish = true;
		
		#这里为必须要进行退款的
		$refund_data = [
			'新购-余额支付','新购-在线支付','变更配置-余额支付','变更配置-在线支付','更换机器-余额支付','更换机器-在线支付'
		];
		
		if( $FlowRes->flow_money_audit_status == '等待审核') {
			$FlowRes->flow_money_audit_status = '无需审核';
		}
		
		if( in_array($FlowRes->flow_name, $refund_data) ) {
			$FlowRes->flow_status = '中止事务中';
			if(in_array($FlowRes->flow_operate_audit_status, ['通过', '无需审核'])) {
				
				#如果操作状态还没改变，则检查操作状态
				$afterconfig = json_decode($FlowDetailRes->flow_after_config, true);
						
				if($operateFinish) {
					if( in_array($flow_mainname,['新购', '更换机器']) ) {						
						if($FlowDetailRes->flow_fix_id && (!isset($afterconfig['is_close']) || $afterconfig['is_close'] == '')) {
							$operateFinish = false;
						}
					} else if( in_array($flow_mainname,['变更配置']) ){
						if($FlowDetailRes->flow_fix_id && (!isset($afterconfig['is_recovery']) || $afterconfig['is_recovery'] == '')) {
							$operateFinish = false;
						}
					} else {
						$operateFinish = true;
					}					
				}
				
				if($operateFinish) {
					$FlowRes->flow_operate_isfinish = '取消完成';
				} else {
					$FlowRes->flow_operate_isfinish = '等待取消';
				}
				
			} else if($FlowRes->flow_operate_audit_status == '驳回') {
				$FlowRes->flow_operate_isfinish = '取消完成';
			} else {
				#等待审核
				$FlowRes->flow_operate_isfinish = '取消完成';
				$FlowRes->flow_operate_audit_status = '无需审核';
			}
			
		} else {
			if(in_array($FlowRes->flow_operate_audit_status, ['通过', '无需审核'])) {
				#如果操作状态还没改变，则检查操作状态
				$afterconfig = json_decode($FlowDetailRes->flow_after_config, true);
						
				if($operateFinish) {
					if( in_array($flow_mainname,['新购', '更换机器']) ) {						
						if($FlowDetailRes->flow_fix_id && (!isset($afterconfig['is_close']) || $afterconfig['is_close'] == '')) {
							$operateFinish = false;
						}
					} else if( in_array($flow_mainname,['变更配置']) ){
						if($FlowDetailRes->flow_fix_id && (!isset($afterconfig['is_recovery']) || $afterconfig['is_recovery'] == '')) {
							$operateFinish = false;
						}
					} else {
						$operateFinish = true;
					}					
				}

				if($operateFinish) {
					$FlowRes->flow_operate_isfinish = '取消完成';
					$FlowRes->flow_status = '中止事务完成';
					
				} else {
					$FlowRes->flow_operate_isfinish = '等待取消';
				}
				
			} else if($FlowRes->flow_operate_audit_status == '驳回') {
				$FlowRes->flow_operate_isfinish = '取消完成';
				$FlowRes->flow_status = '中止事务完成';
			} else {
				#等待审核
				$FlowRes->flow_operate_isfinish = '取消完成';
				$FlowRes->flow_operate_audit_status = '无需审核';
				
				$FlowRes->flow_status = '中止事务完成';
			}
		}
		
		if(in_array($FlowRes->flow_name, ['修改产品信息', '业务退款', '用户机器过户'])) {
			$FlowRes->flow_money_audit_status = '无需审核';
		}
		
		$FlowRes->flow_money_isfinish = '已完成';		
		
		$updateRes = $FlowRes->save();
		
		#修改member_pdt缓存
		if( in_array($FlowRes->flow_name, ['新购-余额支付', '新购-在线支付', '新购-线下打款', '新购-后付款']) ) {
			$FlowDetailList = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->asArray()->all();
			
			foreach($FlowDetailList as $key => $val) {
				$updateRes = Yii::$app->db->createCommand("update member_pdt set status = '82' where unionid = '".$val['flow_unionid']."'")->execute();
				if(!$updateRes) {
					$transaction->rollBack();
					$data = ['status' => 0, 'info' => '更新业务列表机器缓存出现异常', 'data' => ''];
					return $data;
				}
			}
		}
		
		if($updateRes) {
			
			if(self::checkFlowIsFinish($FlowRes->flow_id)) {
				
				$data = self::checkFlowFinishAfter($FlowRes->flow_id); #print_r($data);exit;
				if($data['status'] == 1) {
					$arrReturn = [
						'status' => 1,
						'info' => '当前操作已直接中止完成，不需要后续操作'
					];
				} else {
					$arrReturn = [
						'status' => 1,
						'info' => '中止事务设定完成'
					];
				}
			} else {
				$arrReturn = [
					'status' => 1,
					'info' => '中止事务初始设定完成'
				];
			}
			$transaction->commit();
			
			return $this->renderJSON($arrReturn);
		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'中止事务设定出现异常，请联系管理员'
			];
			return $this->renderJSON($arrReturn);
		}
	}

	#中止或者驳回 恢复机器配置
	public function actionRestoreMachineConfiguration(){
		Yii::$app->request->isAjax || die('error');
		$detail_id = $this->post('detail_id');
		if(!$detail_id) {
			$arrReturn = [
				'status' => 0,
				'info' => '参数错误'
			];
			return $this->renderJSON($arrReturn);
		}
		
		$WorkFlowDetailModel = new WorkFlowDetail();
		$WorkFlowModel = new WorkFlow();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['id' => $detail_id])->one();
		
		$detailConfig = json_decode($WorkFlowDetailRes->flow_after_config, true);
		
		if( isset($detailConfig['is_recovery']) ) {
			$arrReturn = [
				'status' => 0,
				'info' => '已恢复到原配置'
			];
			return $this->renderJSON($arrReturn);
		}
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$detailConfig['is_recovery'] = 1;
		$WorkFlowDetailRes->flow_after_config = json_encode($detailConfig, JSON_UNESCAPED_UNICODE);
		
		$updateRes = $WorkFlowDetailRes->save();
		
		#设置为取消完成
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $WorkFlowDetailRes->flow_id])->one();

		$WorkFlowRes->flow_operate_isfinish = '取消完成';
		
		if($updateRes && $WorkFlowRes->save()) {
			
			if(self::checkFlowIsFinish($WorkFlowRes->flow_id)) {
				$data = self::checkFlowFinishAfter($WorkFlowRes->flow_id);
				if($data['status'] == 1) {
					$transaction->commit();
					$arrReturn = [
						'status' => 1,
						'info' => '恢复机器配置标记完成'
					];
				} else {
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => $data['info']
					];	
				}
			} else {
				$transaction->commit();
				$arrReturn = [
					'status' => 1,
					'info' => '恢复机器配置标记完成'
				];
			}

		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'恢复机器配置标记完成出现异常'
			];			
		}
		return $this->renderJSON($arrReturn);
	}
	
	#中止流程关闭机器
	public function actionStopClose() {
		Yii::$app->request->isAjax || die('error');
		$detail_id = $this->post('detail_id');
		if(!$detail_id) {
			$arrReturn = [
				'status'=>0,
				'info'=>'参数错误'
			];
			return $this->renderJSON($arrReturn);
		}
		
		$WorkFlowDetailModel = new WorkFlowDetail();
		$WorkFlowModel = new WorkFlow();
		
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['id' => $detail_id])->one();
		
		$detailConfig = json_decode($WorkFlowDetailRes->flow_after_config, true);
		
		if(isset($detailConfig['is_close'])) {
			$arrReturn = [
				'status'=>0,
				'info'=>'已标记过为关机状态'
			];
			return $this->renderJSON($arrReturn);
		}
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$detailConfig['is_close'] = 1;
		$WorkFlowDetailRes->flow_after_config = json_encode($detailConfig, JSON_UNESCAPED_UNICODE);
		$updateRes = $WorkFlowDetailRes->save();
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $WorkFlowDetailRes->flow_id])->one();
		
		#判断业务是否关机完成
		$detailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $WorkFlowDetailRes->flow_id])->asArray()->all();
		$isFinish = true;
		foreach($detailRes as $key => $val) {
			if(!$val['flow_fix_confirm_time']) {
				#没有确认则跳过
				continue;
			} else {
				#有确认时间
				$configAfter = json_decode($val['flow_after_config'], true);
				if(!isset($configAfter['is_close'])) {
					#没有设置关机属性
					$isFinish = false;
					break;
				} else {
					if($configAfter['is_close'] == 1) {
						continue;
					} else {
						$isFinish = false;
						break;
					}
				}
			}
		}
		
		if($isFinish) {
			$WorkFlowRes->flow_operate_isfinish = '取消完成';
		}
		
		$refund_data = [
			'新购-余额支付','新购-在线支付','变更配置-余额支付','变更配置-在线支付','更换机器-余额支付','更换机器-在线支付'
		];
		/*if( !in_array($WorkFlowRes->flow_name, $refund_data) ) {
			$WorkFlowRes->flow_status = '中止事务完成';
			$WorkFlowRes->flow_end_time = time();
		}*/

		if($updateRes && $WorkFlowRes->save()) {
			
			if(self::checkFlowIsFinish($WorkFlowRes->flow_id)) {
				$data = self::checkFlowFinishAfter($WorkFlowRes->flow_id);
				if($data['status'] == 1) {
					$transaction->commit();
					$arrReturn = [
						'status' => 1,
						'info' => '标记为关机状态完成'
					];
				} else {
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => $data['info']
					];	
				}
			} else {
				$transaction->commit();
				$arrReturn = [
					'status' => 1,
					'info' => '标记为关机状态完成'
				];
			}
			

		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'标记为关机状态出现异常'
			];			
		}
		return $this->renderJSON($arrReturn);
	}
	
	#驳回或中止流程退款 公共方法
	public function actionStopRefund() {
		Yii::$app->request->isAjax || die('error');
		
		$data = $this->post('data');
		if(!$data) {
			$arrReturn = [
				'status'=>0,
				'info'=>'请选择正确的渠道提交信息'
			];
			return $this->renderJSON($arrReturn);
		}
		
		if(!$this->post('flow_id')) {
			$arrReturn = [
				'status'=>0,
				'info'=>'未知的流程'
			];
			return $this->renderJSON($arrReturn);
		}
		
		if(!in_array($data['refund_type'], ['balance', 'platform'])) {
			$arrReturn = [
				'status'=>0,
				'info'=>'请选择正确退款方式'
			];
			return $this->renderJSON($arrReturn);
		}
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $this->post('flow_id')])->one();
		
		$commonRefundData = [
			'type' => $data['refund_type'],
			'platform' => '',
			'account' => '',
			'time' => '',
			'money' => '',
			'proof' => '',
			'remark' => '',
		];
		
		if($data['refund_type'] == 'balance') {
			$flow_money_info = json_decode($WorkFlowRes->flow_money_info, true);
			$commonRefundData['money'] = sprintf("%.2f", $data['refund_balance_money']);
			$commonRefundData['time'] = time();
			$commonRefundData['remark'] = $data['refund_balance_remark'];
			
		} else {
			$commonRefundData['platform'] = $data['refund_platform'];
			$commonRefundData['account'] = $data['refund_account'];
			$commonRefundData['name'] = $data['refund_name'];
			$commonRefundData['money'] = sprintf("%.2f", $data['refund_platform_money']);
			$commonRefundData['time'] = strtotime($data['refund_time']);
			$commonRefundData['proof'] = $data['refund_proof'];
			$commonRefundData['remark'] = $data['refund_platform_remark'];
		}
		
		$flow_money_info['stop_refund'] = $commonRefundData;
		
		$WorkFlowRes->flow_money_info = json_encode($flow_money_info, JSON_UNESCAPED_UNICODE);

		$refundRes = $WorkFlowRes->save();
		
		if($refundRes) {
			if(self::checkFlowIsFinish($WorkFlowRes->flow_id)) {
				$data = self::checkFlowFinishAfter($WorkFlowRes->flow_id);
				if($data['status'] == 1) {
					$transaction->commit();
					$arrReturn = [
						'status' => 1,
						'info' => '中止流程后退款操作完成'
					];
				} else {
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => $data['info']
					];	
				}
			} else {
				$transaction->commit();
				$arrReturn = [
					'status' => 1,
					'info' => '中止流程后退款操作完成'
				];
			}		
		} else {
			$transaction->rollBack();
			
			$arrReturn = [
				'status'=>0,
				'info'=>'中止流程后退款操作出现异常，请联系管理员'
			];			
		}
		return $this->renderJSON($arrReturn);
	}
	
	#记录访客 公共方法
	public function vistorRecord($flow_id) {
		$WorkFlowModel = new WorkFlow();
		$countRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		if(!$countRes) {
			return false;
		}
		
		$time = time();
		$adminName = Yii::$app->session['admin']['rename'];
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		
		if($countRes->flow_lookcount) {
			$lookcount = json_decode($countRes->flow_lookcount, true);
			$hasAdmin = false;
			
			foreach($lookcount as $key => $val) {
				if($val['admin_id'] == $adminId) {
					$hasAdmin = true;
					if($time - $val['admin_last_visittime'] > 10) {
						$lookcount[$key]['admin_count']++;
						$lookcount[$key]['admin_name'] = $adminName;
						$lookcount[$key]['admin_last_visittime'] = $time;
						$countRes->flow_lookcount = json_encode($lookcount, JSON_UNESCAPED_UNICODE);
						$countRes->save();
					}
					
					break;
				}
			}
			
			if(!$hasAdmin) {
				$lookcount[] = [
					'admin_name' => $adminName,
					'admin_id' => $adminId,
					'admin_count' => 1,
					'admin_last_visittime' => $time,
				];
				
				$countRes->flow_lookcount = json_encode($lookcount, JSON_UNESCAPED_UNICODE);
				$countRes->save();
			}
		} else {
			$lookcount[] = [
				'admin_name' => $adminName,
				'admin_id' => $adminId,
				'admin_count' => 1,
				'admin_last_visittime' => $time,
			];
			$countRes->flow_lookcount = json_encode($lookcount, JSON_UNESCAPED_UNICODE);
			$countRes->save();
		}
		
		return true;
	}
	
	#金额审核 公用方法
	public function actionVerifyMoneyResult() {
		Yii::$app->request->isAjax || die('error');
		
		$post = $this->post();
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		$time = time();
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$FlowRes = $WorkFlowModel->find()->where(['flow_id' => $post['flow_id']])->one();
		if(!$FlowRes) {
			$arrReturn = [
				'status'=>0,
				'info'=>'未知的流程编号'
			];
			return $this->renderJSON($arrReturn);
		}
		$MemberPdtModel = new MemberPdt();
		$userModel = new UserMember();
		$MoneyAccountRecordModel = new MoneyAccountRecord();
		$TradeMainModel = new TradeMain();
		$PdtRoomMangeModel = new PdtRoomMange();
		$PdtManageTypeModel = new PdtManageType();
		#开启事务
        $transaction = Yii::$app->db->beginTransaction();
		
		if($post['result'] == 'Y') {
			
			//审核通过
			$FlowRes->flow_money_audit_status = '通过';
			$FlowRes->flow_money_auditor_id = $adminId;
			$FlowRes->flow_money_auditor_name = $adminName;
			$FlowRes->flow_money_auditor_time = $time;
			
			$behaviorArr = explode('-',$FlowRes->flow_name);
			$behaviorname = $behaviorArr[0];
			$moneyinfo = json_decode($FlowRes->flow_money_info, true);
			$moneyinfo['receivables_time'] = $post['receivables_time'];
			
			
			$TradeMainRes = $TradeMainModel->find()->where(['trade_orderid' => $FlowRes->flow_orderid])->asArray()->one();#var_dump($TradeMainRes);exit;
			
			#设置确定收款时间的组合 
			$operation_arr = ['新购-线下打款','新购-在线支付','更换机器-线下打款', '更换机器-在线支付','变更配置-在线支付', '变更配置-线下打款'];#after
			$renew_arr = ['续费-线下打款', '续费-在线支付']; #库中未存room_id 没有判断是否自有的依据
			$Recharge_arr = ['用户金额变动-打款'];
			
			$BigArr = array_merge($operation_arr, $renew_arr, $Recharge_arr);
			if( in_array($FlowRes->flow_name, $BigArr) )
			{
				$FlowRes->flow_money_info = json_encode($moneyinfo, JSON_UNESCAPED_UNICODE);
				$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id'=> $FlowRes->flow_id])->asArray()->all();
				
				foreach($WorkFlowDetailRes as $key=>$val) 
				{
					$afterConfig = json_decode($val['flow_after_config'], true);
					$frontConfig = json_decode($val['flow_front_config'], true);
					$NewMoneyAccountRecordModel = clone $MoneyAccountRecordModel;
					
					$record_money_num = $moneyinfo['money_num'];
					$record_platform = $moneyinfo['money_platform'];
					$record_account_time = strtotime(date("Y-m-d", strtotime($post['receivables_time'])));
					
					$recordHistory = $NewMoneyAccountRecordModel->find()->where(['record_type' => '未确认收款', 'record_money' => $record_money_num, 'record_account_time' => $record_account_time, 'record_platform' => $record_platform])->one();
					
					if($recordHistory) {
						$recordHistory->record_type = $behaviorname;
						$recordHistory->record_pay_type = $behaviorArr[1];
						$recordHistory->record_flowid = $FlowRes->flow_id;
						$recordHistory->record_user_account = $FlowRes->flow_account;
						$recordHistory->record_user_nick = $FlowRes->flow_username;
						$recordHistory->record_sales = $FlowRes->flow_admin_name;
						
						if( in_array($FlowRes->flow_name, $Recharge_arr) ) {
							$recordHistory->record_type = '用户充值';
							$recordHistory->record_pay_type = '线下打款';
							
							$recordHistory->record_room_name = null;
							$recordHistory->record_server_type = null;
							$recordHistory->record_provider = null;
							$recordHistory->record_config = json_encode([]);
							$recordHistory->record_ip = json_encode([]);
							$recordHistory->record_ip2 = json_encode([]);
							
							$recordHistory->record_money = $moneyinfo['money_num'];
							$recordHistory->record_account_time = strtotime($post['receivables_time']);
							$recordHistory->record_platform = $moneyinfo['money_platform'];
							$recordHistory->record_stream_number = $moneyinfo['money_stream_number'];
							$recordHistory->record_remark = $moneyinfo['money_remark'];
							
						} else {
							if( isset($afterConfig['room_id']) && $afterConfig['room_id'] != '' ) {
								$RoomRes = $PdtRoomMangeModel->find()->select('name')->where(['id' => $afterConfig['room_id']])->asArray()->one();#机房
							} else {
								$MemberPdtRes = $MemberPdtModel->find()->With('pdtroom')->where(['unionid' => $val['flow_unionid']])->asArray()->one();
								$RoomRes['name'] = $MemberPdtRes['pdtroom'][0]['name'];#机房
							}
							$recordHistory->record_room_name = $RoomRes['name'];
							
							$ServerTypeRes = $PdtManageTypeModel->find()->select('type_name')->where(['type_id' => $afterConfig['server_typeid']])->asArray()->one();#服务器分类
							$recordHistory->record_server_type = $ServerTypeRes['type_name'];
							
							if( in_array($FlowRes->flow_name, $renew_arr) )
							{
								$MemberPdtRes = $MemberPdtModel->find()->select('servicerprovider')->where(['unionid' => $val['flow_unionid']])->asArray()->one();
								if( $MemberPdtRes['servicerprovider'] == 0 ) {
									$recordHistory->record_provider = '自有';
								} else {
									$recordHistory->record_provider = '供应商';
								}
							} else {
								if( isset($afterConfig['provider_id']) && $afterConfig['provider_id'] != '' ) {
									$recordHistory->record_provider = '供应商';
								} else {
									$recordHistory->record_provider = '自有';
								}
							}
							
							$recordHistory->record_config = json_encode($afterConfig['config']);
							$recordHistory->record_ip = json_encode($afterConfig['ip']);
							$recordHistory->record_ip2 = json_encode($afterConfig['ip2']);
							
							$renewNum = $frontConfig['renew_num'] ? $frontConfig['renew_num'] : 1;
							
							$recordHistory->record_money = ($TradeMainRes['trade_pay_money'] / $TradeMainRes['trade_price_fact'] * $val['flow_fact_money']) * $renewNum;
							$recordHistory->record_account_time = strtotime($post['receivables_time']);
							$recordHistory->record_platform = $moneyinfo['money_platform'];
							$recordHistory->record_stream_number = $moneyinfo['money_stream_number'];
							$recordHistory->record_remark = $moneyinfo['money_remark'];
						}
						
						if( !$recordHistory->save() ) {
							$transaction->rollBack();
							$arrReturn = [
								'status' => 0,
								'info' => '修改第三方收款明细失败'
							];
							return $this->renderJSON($arrReturn);
						}
						
					} else {
						$NewMoneyAccountRecordModel->record_type = $behaviorname;
						$NewMoneyAccountRecordModel->record_pay_type = $behaviorArr[1];
						$NewMoneyAccountRecordModel->record_flowid = $FlowRes->flow_id;
						$NewMoneyAccountRecordModel->record_user_account = $FlowRes->flow_account;
						$NewMoneyAccountRecordModel->record_user_nick = $FlowRes->flow_username;
						$NewMoneyAccountRecordModel->record_sales = $FlowRes->flow_admin_name;
							
						if( in_array($FlowRes->flow_name, $Recharge_arr) ) {
							$NewMoneyAccountRecordModel->record_type = '用户充值';
							$NewMoneyAccountRecordModel->record_pay_type = '线下打款';
							
							$NewMoneyAccountRecordModel->record_room_name = null;
							$NewMoneyAccountRecordModel->record_server_type = null;
							$NewMoneyAccountRecordModel->record_provider = null;
							$NewMoneyAccountRecordModel->record_config = json_encode([]);
							$NewMoneyAccountRecordModel->record_ip = json_encode([]);
							$NewMoneyAccountRecordModel->record_ip2 = json_encode([]);
							
							$NewMoneyAccountRecordModel->record_money = $moneyinfo['money_num'];
							$NewMoneyAccountRecordModel->record_account_time = strtotime($post['receivables_time']);
							$NewMoneyAccountRecordModel->record_platform = $moneyinfo['money_platform'];
							$NewMoneyAccountRecordModel->record_stream_number = $moneyinfo['money_stream_number'];
							$NewMoneyAccountRecordModel->record_remark = $moneyinfo['money_remark'];
							
						} else {
							if( isset($afterConfig['room_id']) && $afterConfig['room_id'] != '' ) {
								$RoomRes = $PdtRoomMangeModel->find()->select('name')->where(['id' => $afterConfig['room_id']])->asArray()->one();#机房
							} else {
								$MemberPdtRes = $MemberPdtModel->find()->With('pdtroom')->where(['unionid' => $val['flow_unionid']])->asArray()->one();
								$RoomRes['name'] = $MemberPdtRes['pdtroom'][0]['name'];#机房
							}
							$NewMoneyAccountRecordModel->record_room_name = $RoomRes['name'];
							
							$ServerTypeRes = $PdtManageTypeModel->find()->select('type_name')->where(['type_id' => $afterConfig['server_typeid']])->asArray()->one();#服务器分类
							$NewMoneyAccountRecordModel->record_server_type = $ServerTypeRes['type_name'];
							
							if( in_array($FlowRes->flow_name, $renew_arr) )
							{
								$MemberPdtRes = $MemberPdtModel->find()->select('servicerprovider')->where(['unionid' => $val['flow_unionid']])->asArray()->one();
								if( $MemberPdtRes['servicerprovider'] == 0 ) {
									$NewMoneyAccountRecordModel->record_provider = '自有';
								} else {
									$NewMoneyAccountRecordModel->record_provider = '供应商';
								}
							} else {
								if( isset($afterConfig['provider_id']) && $afterConfig['provider_id'] != '' ) {
									$NewMoneyAccountRecordModel->record_provider = '供应商';
								} else {
									$NewMoneyAccountRecordModel->record_provider = '自有';
								}
							}
							
							$NewMoneyAccountRecordModel->record_config = json_encode($afterConfig['config']);
							$NewMoneyAccountRecordModel->record_ip = json_encode($afterConfig['ip']);
							$NewMoneyAccountRecordModel->record_ip2 = json_encode($afterConfig['ip2']);
							
							$renewNum = $frontConfig['renew_num'] ? $frontConfig['renew_num'] : 1;
							
							$NewMoneyAccountRecordModel->record_money = ($TradeMainRes['trade_pay_money'] / $TradeMainRes['trade_price_fact'] * $val['flow_fact_money']) * $renewNum;
							$NewMoneyAccountRecordModel->record_account_time = strtotime($post['receivables_time']);
							$NewMoneyAccountRecordModel->record_platform = $moneyinfo['money_platform'];
							$NewMoneyAccountRecordModel->record_stream_number = $moneyinfo['money_stream_number'];
							$NewMoneyAccountRecordModel->record_remark = $moneyinfo['money_remark'];
						}
						
						if( !$NewMoneyAccountRecordModel->insert() ) {
							$transaction->rollBack();
							$arrReturn = [
								'status' => 0,
								'info' => '新增三方收款明细失败'
							];
							return $this->renderJSON($arrReturn);
						}
					}					
				}
				
				if($moneyinfo['money_num'] > $FlowRes->flow_total_money) {
					$insertBalanceMoney = $moneyinfo['money_num'] - $FlowRes->flow_total_money;
					
					$BalanceMoneyAccountRecordModel = clone $MoneyAccountRecordModel;
					
					$BalanceMoneyAccountRecordModel->record_type = '用户充值';
					$BalanceMoneyAccountRecordModel->record_pay_type = '线下打款';
					$BalanceMoneyAccountRecordModel->record_flowid = $FlowRes->flow_id;
					$BalanceMoneyAccountRecordModel->record_user_account = $FlowRes->flow_account;
					$BalanceMoneyAccountRecordModel->record_user_nick = $FlowRes->flow_username;
					$BalanceMoneyAccountRecordModel->record_sales = $FlowRes->flow_admin_name;
					
					$BalanceMoneyAccountRecordModel->record_room_name = null;
					$BalanceMoneyAccountRecordModel->record_server_type = null;
					$BalanceMoneyAccountRecordModel->record_provider = null;
					$BalanceMoneyAccountRecordModel->record_config = json_encode([]);
					$BalanceMoneyAccountRecordModel->record_ip = json_encode([]);
					$BalanceMoneyAccountRecordModel->record_ip2 = json_encode([]);
					
					$BalanceMoneyAccountRecordModel->record_money = $insertBalanceMoney;
					$BalanceMoneyAccountRecordModel->record_account_time = strtotime($post['receivables_time']);
					$BalanceMoneyAccountRecordModel->record_platform = $moneyinfo['money_platform'];
					$BalanceMoneyAccountRecordModel->record_stream_number = $moneyinfo['money_stream_number'];
					$BalanceMoneyAccountRecordModel->record_remark = $moneyinfo['money_remark'];
					
					if( !$BalanceMoneyAccountRecordModel->insert() ) {
						$transaction->rollBack();
						$arrReturn = [
							'status' => 0,
							'info' => '新增三方收款剩余余额明细失败'
						];
						return $this->renderJSON($arrReturn);
					}
				}
			}
			
			if($FlowRes->flow_status == '事务处理中') {

				if( in_array($FlowRes->flow_name, ['用户金额变动-扣款']) ) {

					$userinfo = $userModel->find()->where(['email' => $FlowRes->flow_account])->one();
					
					
					if( $moneyinfo['money_num'] > $userinfo->balance ) {
						$arrReturn = [
							'status' => 0,
							'info' => '用户余额不足'
						];
						return $this->renderJSON($arrReturn);
					}
				}
				#当为变更配置和更换机器时，成本和款项一起审核。所以这里把 flow_money_isfinish 改为已完成
				
				if( in_array($behaviorname, ['变更配置','更换机器']) ) {
					$WorkFlowRoleModel = new WorkFlowRole();
					$flow_role = $WorkFlowRoleModel->find()->where(['wr_name' => $FlowRes->flow_name])->asArray()->one();
					if( $flow_role['wr_money_isverify'] == 'Y' ) {
						$FlowRes->flow_money_isfinish = '已完成';
					}
				}
				
			} else if($FlowRes->flow_status == '中止事务完成' || $FlowRes->flow_status == '处理完成' || ($FlowRes->flow_status == '处理驳回' && $FlowRes->flow_end_time != null)) {
				//如果是这几个状态，是不允许进行审核的，这几个状态是完成状态
				$arrReturn = [
					'status' => 0,
					'info' => '当前流程不允许进行这种操作'
				];
				return $this->renderJSON($arrReturn);
			}
			
		} else {
			if($post['reason'] == '') {
				$arrReturn = [
					'status'=>0,
					'info'=>'审核驳回请填写理由'
				];
				return $this->renderJSON($arrReturn);
			}
			
			$FlowRes->flow_money_audit_status = '驳回';
			$FlowRes->flow_money_auditor_id = $adminId;
			$FlowRes->flow_money_auditor_name = $adminName;
			$FlowRes->flow_money_auditor_time = $time;
			$FlowRes->flow_money_auditor_reason = $post['reason'];
			$FlowRes->flow_status = '处理驳回';
			#如果技术并没有确认
			$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id'=> $FlowRes->flow_id])->asArray()->all();
			foreach( $WorkFlowDetailRes as $key => $val) {
				if( !$val['flow_fix_id'] ) {
					$FlowRes->flow_operate_isfinish = '取消完成';
				}
				
				if(in_array($FlowRes->flow_name, ['新购-余额支付', '新购-在线支付', '新购-线下打款', '新购-后付款'])) {
					$updateRes = Yii::$app->db->createCommand("update member_pdt set status = '81' where unionid = '".$val['flow_unionid']."'")->execute();
					if(!$updateRes) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '更新业务列表机器缓存出现异常', 'data' => ''];
						return $data;
					}
				}
			}
			
		}
		
		$updateResult = $FlowRes->save();
		if($updateResult) {
			if(in_array($FlowRes->flow_name, ['新购-余额支付', '新购-在线支付', '新购-线下打款', '新购-后付款', '续费-余额支付', '续费-在线支付', '续费-线下打款', '续费-后付款', '变更配置-余额支付', '变更配置-在线支付', '变更配置-线下打款', '变更配置-后付款', '变更配置-余额退款', '更换机器-余额支付', '更换机器-在线支付', '更换机器-线下打款', '更换机器-后付款', '更换机器-余额退款', '业务退款'])) {
				$NoteRes = RevenueNotes::FlowMoneyAudit($FlowRes->flow_id);
			} else {
				$NoteRes = ['status' => 1];
			}
			
			if($NoteRes['status']) {
				if(self::checkFlowIsFinish($FlowRes->flow_id)) {
					$data = self::checkFlowFinishAfter($FlowRes->flow_id);
					if($data['status'] == 1) {
						$transaction->commit();
						$arrReturn = [
							'status' => 1,
							'info' => '审核处理完成'
						];
					} else {
						$transaction->rollBack();
						$arrReturn = [
							'status' => 0,
							'info' => $data['info']
						];	
					}
				} else {
					$transaction->commit();
					$arrReturn = [
						'status' => 1,
						'info' => '审核处理完成'
					];
				}
			} else {
				$transaction->rollBack();
				$arrReturn = [
					'status'=>0,
					'info'=>$NoteRes['info']
				];
			}
				
		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'更新审核数据出现异常'
			];
		}
		return $this->renderJSON($arrReturn);
	}
	
	#操作审核 公用方法
	public function actionVerifyHandleResult() {
		Yii::$app->request->isAjax || die('error');
		
		$post = $this->post();
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		$time = time();
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$FlowRes = $WorkFlowModel->find()->where(['flow_id' => $post['flow_id']])->one();
		$FlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $post['flow_id']])->asArray()->all();
		if(!$FlowRes) {
			$arrReturn = [
				'status'=>0,
				'info'=>'未知的流程编号'
			];
			return $this->renderJSON($arrReturn);
		}
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction();
		
		if($post['result'] == 'Y') {
			//审核通过
			$FlowRes->flow_operate_audit_status = '通过';
			$FlowRes->flow_operate_auditor_id = $adminId;
			$FlowRes->flow_operate_auditor_name = $adminName;
			$FlowRes->flow_operate_auditor_time = $time;
			
			$returnRes = true;
			#$returnRes = self::finishflow($FlowRes->flow_id, $FlowRes->flow_name);
		} else {
			if($post['reason'] == '') {
				$arrReturn = [
					'status'=> 0,
					'info' => '审核驳回请填写理由'
				];
				return $this->renderJSON($arrReturn);
			}
			$FlowRes->flow_operate_audit_status = '驳回';
			$FlowRes->flow_operate_auditor_id = $adminId;
			$FlowRes->flow_operate_auditor_name = $adminName;
			$FlowRes->flow_operate_auditor_time = $time;
			$FlowRes->flow_operate_auditor_reason = $post['reason'];
			$FlowRes->flow_status = '处理驳回';
			
			$returnRes = self::rejectflow($FlowRes->flow_id, $FlowRes->flow_name);
			
			if(in_array($FlowRes->flow_name, ['新购-余额支付', '新购-在线支付', '新购-线下打款', '新购-后付款'])) {
				foreach($FlowDetailRes as $key => $val) {
					$updateRes = Yii::$app->db->createCommand("update member_pdt set status = '81' where unionid = '".$val['flow_unionid']."'")->execute();
					if(!$updateRes) {
						$transaction->rollBack();
						$data = ['status' => 0, 'info' => '更新业务列表机器缓存出现异常', 'data' => ''];
						return $data;
					}
				}
			}
		}
		
		$updateResult = $FlowRes->save();
		
		if($updateResult && $returnRes) {
			
			if(self::checkFlowIsFinish($FlowRes->flow_id)) {
				$data = self::checkFlowFinishAfter($FlowRes->flow_id);
				if($data['status'] == 1) {
					$transaction->commit();
					$arrReturn = [
						'status' => 1,
						'info' => '审核处理完成'
					];
				} else {
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => $data['info']
					];	
				}
			} else {
				$transaction->commit();
				$arrReturn = [
					'status' => 1,
					'info' => '审核处理完成'
				];
			}	
			
		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'更新审核数据出现异常'
			];
		}
		return $this->renderJSON($arrReturn);
	}
	
	#技术确认 公共方法
	public function actionFixConfirm() {
		Yii::$app->request->isAjax || die('error');
		$detail_id = $this->post('detail_id');
		if(!$detail_id) {
			$arrReturn = [
				'status'=>0,
				'info'=>'缺少必要参数'
			];
			return $this->renderJSON($arrReturn);
		}
		
		$adminName = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
		$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		$time = time();
		
		$WorkFlowDetailModel = new WorkFlowDetail();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['id' => $detail_id])->one();
		if(!$WorkFlowDetailRes) {
			$arrReturn = [
				'status'=>0,
				'info'=>'未知的信息'
			];
			return $this->renderJSON($arrReturn);
		}
		
		if($WorkFlowDetailRes->flow_fix_id || $WorkFlowDetailRes->flow_fix_name) {
			$arrReturn = [
				'status'=>0,
				'info'=>'当前业务已由 '.$WorkFlowDetailRes->flow_fix_name.' 在处理了'
			];
			return $this->renderJSON($arrReturn);
		}
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$WorkFlowDetailRes->flow_fix_id = $adminId;
		$WorkFlowDetailRes->flow_fix_name = $adminName;
		$WorkFlowDetailRes->flow_fix_confirm_time = $time;
		
		$updateRes = $WorkFlowDetailRes->save();
		
		$WorkFlowModel = new WorkFlow();
		$FlowMain = $WorkFlowModel->find()->where(['flow_id' => $WorkFlowDetailRes->flow_id])->one();
		
		if($FlowMain->flow_operate_isfinish == null) {
			$FlowMain->flow_operate_isfinish = '等待配置';
			$updateMain = $FlowMain->save();
		} else {
			$updateMain = true;
		}
		
		if($updateRes && $updateMain) {
			$transaction->commit();
			$arrReturn = [
				'status'=>1,
				'info'=>'已确认完成'
			];
		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'确认业务出现异常'
			];
		}
		return $this->renderJSON($arrReturn);
	}
	
	#检查流程是否完成
	public function checkFlowIsFinish($flow_id) {
		$WorkFlowModel = new WorkFlow();
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->asArray()->one();
		$WorksaveRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();

		if($WorkFlowRes['flow_status'] == '中止事务中') {
			
			#中止事务
			if(in_array($WorkFlowRes['flow_money_audit_status'], ['通过', '无需审核'])) {
				if(in_array($WorkFlowRes['flow_name'], ['新购-余额支付', '新购-在线支付', '变更配置-余额支付', '变更配置-在线支付', '更换机器-余额支付', '更换机器-在线支付'])) {
					$checkMoneyResult = self::checkFlowMoneyIsRefund($WorkFlowRes['flow_money_info']);				
					if(!$checkMoneyResult) {
						#通过了，没有退款，返回false
						return false;
					}
				}
				
			} else if(in_array($WorkFlowRes['flow_money_audit_status'], ['驳回'])) {
				#驳回则没有收到金额，不用判断
			} else {
				#等待审核，返回false
				return false;
			}
			
			#flow_money_isfinish 无论什么状态都不会影响中止事务
			
			if(in_array($WorkFlowRes['flow_operate_audit_status'], ['通过', '无需审核'])) {
				#这两个状态需要查看流程状态，如果是等待审核的状态则不用查看
				if(!in_array($WorkFlowRes['flow_operate_isfinish'], ['取消完成', '无需操作'])) {
					return false;
				}
			}

			$WorksaveRes->flow_status = '中止事务完成';
			
		} else if($WorkFlowRes['flow_status'] == '事务处理中') {
			
			if(!in_array($WorkFlowRes['flow_money_audit_status'], ['通过', '无需审核'])) {
				return false;
			}
			
			if(in_array(explode('-', $WorkFlowRes['flow_name'])[0], ['新购', '变更配置', '更换机器'])) {
				#这三种类型需要查看成本
				if($WorkFlowRes['flow_money_isfinish'] == '等待审核成本') {
					return false;
				}
			}
			
			if(!in_array($WorkFlowRes['flow_operate_audit_status'], ['通过', '无需审核'])) {
				return false;
			}
			
			if(!in_array($WorkFlowRes['flow_operate_isfinish'], ['配置完成', '无需操作'])) {
				return false;
			}
			$WorksaveRes->flow_status = '处理完成';
			
		} else if($WorkFlowRes['flow_status'] == '处理驳回') {
			if($WorkFlowRes['flow_money_audit_status'] == '驳回') {
				#财务驳回，不需要检查金额是否退款，因为根本就没收到款，查看技术是否关机完成
				if(in_array($WorkFlowRes['flow_operate_audit_status'], ['通过', '无需审核'])) {
					#这两个状态需要查看流程状态，如果是等待审核的状态则不用查看
					if(!in_array($WorkFlowRes['flow_operate_isfinish'], ['取消完成', '无需操作'])) {
						return false;
					}
				}
				
			} else {
				#技术驳回
				if(in_array($WorkFlowRes['flow_money_audit_status'], ['通过', '无需审核'])) {
					if(in_array($WorkFlowRes['flow_name'], ['新购-余额支付', '新购-在线支付', '变更配置-余额支付', '变更配置-在线支付', '更换机器-余额支付', '更换机器-在线支付'])) {
						if(!self::checkFlowMoneyIsRefund($WorkFlowRes['flow_money_info'])) {
							#通过了，没有退款，返回false
							return false;
						}
					}
				} else if(in_array($WorkFlowRes['flow_money_audit_status'], ['驳回'])) {
					#驳回则没有收到金额，不用判断
				} else {
					#等待审核，返回false
					return false;
				}
			}
			
			$WorksaveRes->flow_status = '处理驳回';
		}
		
		$WorksaveRes->flow_end_time = time();

		$updateFlow = $WorksaveRes->update(false);#var_dump($updateFlow);exit;
		if($updateFlow) {
			return true;
		} else {
			return false;
		}
	}
	
	#检查流程是否完成（辅助方法：查看金额是否退款）
	public function checkFlowMoneyIsRefund($moneyinfo) {
		$refundInfo = json_decode($moneyinfo, true);
		if(isset($refundInfo['stop_refund'])) {
			if(isset($refundInfo['stop_refund']['type']) && $refundInfo['stop_refund']['type'] != '') {
				return true;
			}
			return false;
 		}
		return false;
	}
		
	#检查中止流程是否完成
	public function checkStopFlowIsFinish($flow_id) {
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		$WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->all();
		
		if($WorkFlowRes->flow_status != '中止事务中') {
			return false;
		}
		
		#有金额项是否退款
		if(in_array($WorkFlowRes->flow_name, ['新购-余额支付', '新购-在线支付', '新购-线下打款'])) {
			$money_info = json_decode($WorkFlowRes->flow_money_info, true);
			if(!isset($money_info['stop_refund'])) {
				return false;
			}
		}
		

		#判断是否关机
		if(in_array($WorkFlowRes->flow_name, ['新购-余额支付', '新购-在线支付', '新购-线下打款'])) {
			foreach($WorkFlowDetailRes as $key => $val) {
				$config = json_decode($val->flow_after_config, true);
				if(!isset($config['is_close'])) {
					return false;
				}
			}	
		}
		
		$WorkFlowRes->flow_status = '中止事务完成';
		$updateFlow = $WorkFlowRes->save();
		
		if($updateFlow) {
			return true;
		} else {
			return false;
		}
	}
		
	#处理流程后续
	public function checkFlowFinishAfter($flow_id) {
		
		$WorkFlowModel = new WorkFlow();
		
		$WorkFlowRes = $WorkFlowModel->find()->where(['flow_id' => $flow_id])->one();
		$data = [];
		
		if(in_array($WorkFlowRes['flow_status'], ['处理驳回', '中止事务完成'])) {

			$MoneyAccountRecordModel = new MoneyAccountRecord;
			
			$RecordRes = $MoneyAccountRecordModel->find()->where(['record_flowid' => $WorkFlowRes['flow_id']])->asArray()->all();
			if($RecordRes) {
				$deleteIdlist = array_column($RecordRes, 'id');
				
				$deleteCount = $MoneyAccountRecordModel->deleteAll(['id' => $deleteIdlist]);
				
				if($deleteCount != count($deleteIdlist)) {
					$data = ['status' => 0, 'info' => '删除三方明细记录出现异常'];
					return $data;
				}
			}
		}
		
		
		#流程走完，处理后续
		if(in_array($WorkFlowRes['flow_name'], ['新购-余额支付', '新购-在线支付', '新购-线下打款', '新购-后付款'])) {
			#新购流程处理后续
			$data = WorkFlowHandle::ModelPurchase($WorkFlowRes['flow_id'], $WorkFlowRes['flow_status']);
		} else if(in_array($WorkFlowRes['flow_name'], ['变更配置-余额支付', '变更配置-在线支付', '变更配置-线下打款','变更配置-余额退款','变更配置-无金额', '变更配置-后付款'])) {
			#变更配置流程处理后续
			$data = WorkFlowHandle::ModelChangeConfig($WorkFlowRes['flow_id'], $WorkFlowRes['flow_status']);
		} else if(in_array($WorkFlowRes['flow_name'], ['用户金额变动-打款', '用户金额变动-扣款'])) {
			#用户打款扣款流程处理后续
			$data = WorkFlowHandle::ModelAmount($WorkFlowRes['flow_id'], $WorkFlowRes['flow_status']);
		} else if(in_array($WorkFlowRes['flow_name'], ['业务退款'])) {
			#业务退款流程后续处理
			$data = WorkFlowHandle::ModelRefund($WorkFlowRes['flow_id'], $WorkFlowRes['flow_status']);
		} else if(in_array($WorkFlowRes['flow_name'], ['修改产品时间'])) {
			#修改产品时间流程后续处理
			$data = WorkFlowHandle::ModelUpdateTime($WorkFlowRes['flow_id'], $WorkFlowRes['flow_status']);
		} else if(in_array($WorkFlowRes['flow_name'], ['修改产品信息'])) {
			#修改产品信息流程后续处理
			$data = WorkFlowHandle::ModelUpdateInfo($WorkFlowRes['flow_id'], $WorkFlowRes['flow_status']);
		} else if(in_array($WorkFlowRes['flow_name'], ['更换机器-余额支付', '更换机器-在线支付', '更换机器-线下打款','更换机器-余额退款','更换机器-无金额','更换机器-后付款'])) {
			#更换机器流程处理后续
			#echo $WorkFlowRes['flow_status'];exit;
			$data = WorkFlowHandle::ModelReplaceMachine($WorkFlowRes['flow_id'], $WorkFlowRes['flow_status']);
		} else if(in_array($WorkFlowRes['flow_name'], ['用户机器过户'])) {
			#用户机器过户流程后续处理
			$data = WorkFlowHandle::ModelTransfer($WorkFlowRes['flow_id'], $WorkFlowRes['flow_status']);
		} else if(in_array($WorkFlowRes['flow_name'], ['续费-余额支付','续费-在线支付', '续费-线下打款', '续费-后付款'])) {
			#续费流程后续处理
			$data = WorkFlowHandle::ModelRenew($WorkFlowRes['flow_id'], $WorkFlowRes['flow_status']);
		} else if(in_array($WorkFlowRes['flow_name'], ['更换自有闲置机器IP'])) {
			#自有机器更换IP流程后续处理
			$data = WorkFlowHandle::ModelIdleChangeIP($WorkFlowRes['flow_id'], $WorkFlowRes['flow_status']);
		} else if(in_array($WorkFlowRes['flow_name'], ['变更IP-自有机器', '变更IP-供应商'])) {
			#用户机器更换IP流程后续处理
			$data = self::replaceip($WorkFlowRes['flow_id'], $WorkFlowRes['flow_status']);
		}  else if(in_array($WorkFlowRes['flow_name'], ['变更测试机配置'])) {
			#变更测试机配置流程后续处理
			$data = WorkFlowHandle::ModelTesterChangeConfig($WorkFlowRes['flow_id'], $WorkFlowRes['flow_status']);
		}
		
		return $data;
	}
		
	#技术确定变更后的IP（变更配置时 同时更换IP设置 ）
	public function actionFlowSettingip() {
		Yii::$app->request->isAjax || die('error');
		
		$post = $this->post();
		$flow_id = $post['flow_id'];
		if(!$flow_id) {
			$arrReturn = [
				'status' => 0,
				'info' => '未知的流程号参数'
			];
			return $this->renderJSON($arrReturn);
		}
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$MemberPdtModel = new MemberPdt();
		$IdlePdtModel = new IdlePdt();
		$PdtIpModel = new PdtIp();
		$SupplierIpModel = new SupplierIp();
		$SwitchLineModel = new SwitchLine();
		
		$DetailQuery = $WorkFlowDetailModel->find()->where(['flow_id' => $flow_id])->one();
		if( empty($DetailQuery)) {
			$transaction->rollBack();
			$arrReturn = [
				'status' => 0,
				'info' => '未知的流程'
			];
			return $this->renderJSON($arrReturn);
		}
		$afterConfig = json_decode($DetailQuery->flow_after_config,true);
		$frontConfig = json_decode($DetailQuery->flow_front_config,true);
		$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $DetailQuery->flow_unionid])->asArray()->one();	
		
		#获取提交的IP
		$IpArr = $post['ipdata'];		
		$IpArr = DataHelper::dotrim($IpArr);
		if( empty($IpArr)) {
			return $this->renderJSON([
				'status'=>0,
				'info'=>'IP地址未选择或未填写'
			]);
		}
		#去重 去空
		$ipArray = array_unique($IpArr);
		$ipArray = array_filter($ipArray);
		
		$ip2 = $ipArray;		
		$Retuen_IPArray = DataHelper::splitIP($ip2);#拆分
		if( !$Retuen_IPArray['status'] ) {
			$transaction->rollBack();
			$arrReturn = [
				'status' => 0,
				'info'=> $Retuen_IPArray['info']
			];
			return $this->renderJSON($arrReturn);
		}
		$ipArray2 = $Retuen_IPArray['data'];
		
		#获取最原始的IP
		$Original_ip = $frontConfig['ip2'];
		
		#判断IP是否有变化 (用提交的IP和最原始的IP做对比)
		$hasChangeIP = false;
		foreach($ipArray2 as $k => $v) {
			if(!in_array($v, $Original_ip)) {
				$hasChangeIP = true;
				break;
			}				
		}
		foreach($Original_ip as $k => $v) {
			if(!in_array($v, $ipArray2)) {
				$hasChangeIP = true;
				break;
			}
		}
		if(!$hasChangeIP) {
			#如果没有更换IP
			$transaction->rollBack();
			$arrReturn = [
				'status' => 0,
				'info' => 'IP地址未做更换'
			];
			return $this->renderJSON($arrReturn);
		}
		
		#看是否之前还提交过
		if( !empty($afterConfig['ip']) )
		{
			#如果不为空，代表以及提交过一次IP。这里先将之前一次提交的IP恢复
			$last_ip = $afterConfig['ip2'];
			if( $MemberPdtRes['servicerprovider'] == 0) 
			{
				$bgIPArray = array_unique(array_merge($last_ip,$Original_ip));							
				$result_bangding = array_diff($bgIPArray,$Original_ip); 	#原需要改为使用中的				
				$result_jiechu = array_diff($bgIPArray,$last_ip); 	#原需要改为闲置的	
				#print_r($result_jiechu);print_r($result_bangding);exit;
				if( !empty($result_bangding) ) {
					$res1 = $PdtIpModel->updateAll(['status' => 0],['ip'=>$result_bangding]);#需要绑定的IP  改为闲置
				}
				if( !empty($result_jiechu) ) {
					$res2 = $PdtIpModel->updateAll(['status' => 1],['ip'=>$result_jiechu]);	#需要解除绑定的IP  改为使用
				}
			} else {
				#如果是供应商
				$bgIPArray = array_unique(array_merge($last_ip,$Original_ip));							
				$result_insert = array_diff($bgIPArray,$Original_ip); 	#上一次提交 入库的供应商IP
				$result_deltet = array_diff($bgIPArray,$last_ip); 	#上一次提交 最后需要删除，临时修改状态的供应商IP	
				#删除上次 入库的供应商IP
				if( !empty($result_insert) )
				{
					$delteRes = $SupplierIpModel->deleteAll(['ip' => $result_insert]);
					if( $delteRes != count($result_insert) ) {
						$transaction->rollBack();
						$arrReturn = [
						   'status' => 0,
						   'info' => '供应商IP删除处理失败'
						];
						return $this->renderJSON($arrReturn);
					}
				}
				#将上次提交修改状态的供应商IP状态改回
				if( !empty($result_deltet) ) {
					$updateRes = $SupplierIpModel->updateAll(['status' => 1,'update_time' => time()],['ip'=>$result_deltet]);
					if( $updateRes != count($result_deltet) ) {
						$transaction->rollBack();
						$arrReturn = [
						   'status' => 0,
						   'info' => '供应商IP状态更新失败'
						];
						return $this->renderJSON($arrReturn);
					}
				}
			}
			
			$oldIP = $Original_ip;
			
		} else {
			$oldIP = $Original_ip;
		}
		
		
		if( $MemberPdtRes['servicerprovider'] == 0) 
		{
			#自有的要更改状态
			$IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $MemberPdtRes['idle_id']])->one();
			#Start IP线路验证
			$PdtIpAll = $PdtIpModel->find()->select('room_id, line_type_id, vlan')->where(['in','ip',$ipArray2])->asArray()->all();
			$temp = array_column($PdtIpAll, 'room_id');					
			$temp = array_unique($temp);
			$temp =  array_values($temp);
			if(count($temp) > 1 ) {
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info' => 'IP中有属于其他机房的IP地址'
				];
				return $this->renderJSON($arrReturn);
			} else if( count($temp) == 1) {
				if($temp[0] != $IdlePdtQuery->room_id ){
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => 'IP所属机房与产品选择机房不一致'
					];
					return $this->renderJSON($arrReturn);
				}
			}					
			#
			$line_typeList = array_column($PdtIpAll, 'line_type_id');					
			$line_typeList = array_unique($line_typeList);
			if( count($line_typeList) > 1 ) {
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info' => '所选IP与其他IP的线路类型不匹配'
				];
				return $this->renderJSON($arrReturn);
			}
			#获取机器对应交换机的线路信息
			$SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtQuery->switch_location, 'line_type_id' => $line_typeList[0]])->asArray()->one();
			if( empty($SwitchLineList) ) {
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info' => '机器对应的交换机线路中未有IP的线路类型'
				];
				return $this->renderJSON($arrReturn);
			}
			#End IP线路验证
			
			$bgIPArray = array_unique(array_merge($ipArray2,$oldIP));							
			$result_bangding = array_diff($bgIPArray,$oldIP); 	#需要改为使用中的				
			$result_jiechu = array_diff($bgIPArray,$ipArray2); 	#需要改为闲置的
			#新的ip需要绑定的 改为待定
			if( !empty($result_bangding) ) {
				$res1 = $PdtIpModel->updateAll(['status' => 3],['ip'=>$result_bangding]);
				if( $res1 != count($result_bangding) ) {
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => '要绑定的IP状态更新失败'
					];
					return $this->renderJSON($arrReturn);
				}
			}
			#需要解除绑定的IP  改为待定
			if( !empty($result_jiechu) ) {
				$res2 = $PdtIpModel->updateAll(['status' => 3],['ip'=>$result_jiechu]);	
				if( $res2 != count($result_jiechu) ) {
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => '要解除的IP状态更新失败'
					];
					return $this->renderJSON($arrReturn);
				}
			}
			#然后将变更后的IP临时 更改到 自有机器上
			$IdlePdtQuery->ip = json_encode($ipArray);
			$IdlePdtQuery->ip2 = json_encode($ipArray2);
			$IdlePdtQuery->vlan = $SwitchLineList['switch_vlan'];
			if( !$IdlePdtQuery->update() ) {
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info' => '自有机器中IP信息更新失败'
				];
				return $this->renderJSON($arrReturn);
			}
			
		} else {
			#将供应商IP新增的IP入库，需要删除的IP临时做状态修改
			$bgIPArray = array_unique(array_merge($ipArray2,$oldIP));
			$result_insert = array_diff($bgIPArray,$oldIP); 	#需要入库的供应商IP
			$result_deltet = array_diff($bgIPArray,$ipArray2); 	#最后需要删除，临时修改状态的供应商IP	
			
			#入库供应商IP
			if( !empty($result_insert) )
			{
				#供应商的  要做判断，看是否可用			
				/*$data = DataHelper::CheckIP_isused($result_insert);
				if( $data['status'] == 0 ) {
					$transaction->rollBack();
					return $this->renderJSON([
						'status' => 0,
						'info' => $data['info']
					]);
				}*/
				#检测新增的IP
				$CheckRes = DataHelper::detect_supplierip($result_insert);
				if( $CheckRes['status'] == 0 ) 
				{
					$transaction->rollBack();
					return $this->renderJSON([
						'status' => 0,
						'info' => $CheckRes['info']
					]);
				}
				
				$insertRes = $SupplierIpModel->add_peration($result_insert, $MemberPdtRes['provider_id'], $MemberPdtRes['room_id'], '3');
				if(!$insertRes)
				{
					$transaction->rollBack();
					$arrReturn = [
					   'status' => 0,
					   'info' => '供应商IP删除处理失败'
					];
					return $this->renderJSON($arrReturn);
				}
			}
			#将需要删除的供应商IP，临时修改状态
			if( !empty($result_deltet) ) {
				$updateRes = $SupplierIpModel->updateAll(['status' => 3,'update_time' => time()],['ip'=>$result_deltet]);
				if( $updateRes != count($result_deltet) ) {
					$transaction->rollBack();
					$arrReturn = [
					   'status' => 0,
					   'info' => '供应商IP状态更新失败'
					];
					return $this->renderJSON($arrReturn);
				}
			}			
		}
		
		#将变更后的IP存到流程中
		$afterConfig = json_decode($DetailQuery->flow_after_config,true);
		$afterConfig['ip'] = $ipArray;
		$afterConfig['ip2'] = $ipArray2;
		$DetailQuery->flow_after_config = json_encode($afterConfig, JSON_UNESCAPED_UNICODE);
		
		if( $DetailQuery->update() ) {
			$transaction->commit();
			$arrReturn = [
				'status' => 1,
				'info' => '确定变更后IP设置成功'
			];
		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status' => 0,
				'info' => '确定变更后IP设置失败'
			];
		}		
		return $this->renderJSON($arrReturn);
	}
		
	#更换机器 对换机器IP
	public function actionFlowExchangeip() {
		Yii::$app->request->isAjax || die('error');
		
		$post = $this->post();
		$detail_id = $post['detail_id'];
		if(!$detail_id) {
			$arrReturn = [
				'status' => 0,
				'info' => '未知的参数'
			];
			return $this->renderJSON($arrReturn);
		}
		
		$WorkFlowModel = new WorkFlow();
		$WorkFlowDetailModel = new WorkFlowDetail();
		$MemberPdtModel = new MemberPdt();
		$IdlePdtModel = new IdlePdt();
		$PdtIpModel = new PdtIp();
		$SwitchLineModel = new SwitchLine();
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction();
		$DetailQuery = $WorkFlowDetailModel->find()->where(['id' => $detail_id])->one();
		if( empty($DetailQuery) ) {
			$arrReturn = [
				'status' => 0,
				'info' => '未知的流程信息'
			];
			return $this->renderJSON($arrReturn);
		}
		
		$afterConfig = json_decode($DetailQuery->flow_after_config,true);
		$frontConfig = json_decode($DetailQuery->flow_front_config,true);
		
		#必须更换前后都为自有，才会有这个操作
		if( $afterConfig['servicerprovider'] == $frontConfig['servicerprovider'] && $afterConfig['servicerprovider'] == 0) {
			if( isset($afterConfig['use_original_ip']) && $afterConfig['use_original_ip'] == 1) {
				if( !isset($afterConfig['idle_id']) ||  $afterConfig['idle_id'] == '' || $afterConfig['idle_id'] == null) {
					$arrReturn = [
						'status' => 0,
						'info' => '未知自有机器'
					];
					
					return $this->renderJSON($arrReturn);
				} else {
					$after_IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $afterConfig['idle_id']])->one();#更换后的自有机器信息对象
					$front_IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $frontConfig['idle_id']])->one();#更换前的自有机器信息对象
					
					#Start IP线路验证
					#验证更换后的机器配置的IP是否与之前的机器匹配
					if( !empty(json_decode($after_IdlePdtQuery->ip2, true)) ) {
						$PdtIpAll1 = $PdtIpModel->find()->select('room_id, line_type_id, vlan')->where(['in', 'ip', json_decode($after_IdlePdtQuery->ip2, true)])->asArray()->all();					
						$temp1 = array_column($PdtIpAll1, 'room_id');					
						$temp1 = array_unique($temp1);
						$temp1 =  array_values($temp1);
						if(count($temp1) > 1 ) {
							$transaction->rollBack();
							$arrReturn = [
								'status' => 0,
								'info' => '更换后的自有机器原绑定的IP存在多机房'
							];
							return $this->renderJSON($arrReturn);
						} else if( count($temp1) == 1) {
							if($temp1[0] != $front_IdlePdtQuery->room_id ){
								$transaction->rollBack();
								$arrReturn = [
									'status' => 0,
									'info' => '更换后的自有机器原绑定的IP所属机房与更换前的自有机器绑定IP所属机房不一致'
								];
								return $this->renderJSON($arrReturn);
							}
						}
						$line_typeList1 = array_column($PdtIpAll1, 'line_type_id');					
						$line_typeList1 = array_unique($line_typeList1);
						if( count($line_typeList1) > 1 ) {
							$transaction->rollBack();
							$arrReturn = [
								'status' => 0,
								'info' => '所选IP与其他IP的线路类型不匹配'
							];
							return $this->renderJSON($arrReturn);
						}
						#获取机器对应交换机的线路信息
						$SwitchLineList1 = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $front_IdlePdtQuery->switch_location, 'line_type_id' => $line_typeList1[0]])->asArray()->one();
						if( empty($SwitchLineList1) ) {
							$transaction->rollBack();
							$arrReturn = [
								'status' => 0,
								'info' => '更换前机器对应的交换机线路中未有更换后机器IP的线路类型'
							];
							return $this->renderJSON($arrReturn);
						}
					}
					
					#第二个验证
					if( !empty($afterConfig['ip2'])) {
						$PdtIpAll2 = $PdtIpModel->find()->select('room_id, line_type_id, vlan')->where(['in', 'ip', $afterConfig['ip2']])->asArray()->all();
						$temp2 = array_column($PdtIpAll2, 'room_id');
						$temp2 = array_unique($temp2);
						$temp2 =  array_values($temp2);
						if(count($temp2) > 1 ) {
							$transaction->rollBack();
							$arrReturn = [
								'status' => 0,
								'info' => '更换前的自有机器原绑定的IP存在多机房'
							];
							return $this->renderJSON($arrReturn);
						} else if( count($temp2) == 1) {
							if($temp2[0] != $after_IdlePdtQuery->room_id ){
								$transaction->rollBack();
								$arrReturn = [
									'status' => 0,
									'info' => '更换后的自有机器原绑定的IP所属机房与更换前的自有机器绑定IP所属机房不一致'
								];
								return $this->renderJSON($arrReturn);
							}
						}
						$line_typeList2 = array_column($PdtIpAll2, 'line_type_id');					
						$line_typeList2 = array_unique($line_typeList2);
						if( count($line_typeList2) > 1 ) {
							$transaction->rollBack();
							$arrReturn = [
								'status' => 0,
								'info' => '所选IP与其他IP的线路类型不匹配'
							];
							return $this->renderJSON($arrReturn);
						}
						#获取机器对应交换机的线路信息
						$SwitchLineList2 = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $after_IdlePdtQuery->switch_location, 'line_type_id' => $line_typeList2[0]])->asArray()->one();
						if( empty($SwitchLineList2) ) {
							$transaction->rollBack();
							$arrReturn = [
								'status' => 0,
								'info' => '更换后机器对应的交换机线路中未有更换前机器IP的线路类型'
							];
							return $this->renderJSON($arrReturn);
						}
					}					
					#End IP线路验证	
					
					$front_IdlePdtQuery->ip = $after_IdlePdtQuery->ip;
					$front_IdlePdtQuery->ip2 =  $after_IdlePdtQuery->ip2;
					if( !$SwitchLineList1) {
						$front_IdlePdtQuery->vlan = $SwitchLineList1['switch_vlan'];
					}
					
					$after_IdlePdtQuery->ip = json_encode($afterConfig['ip'], JSON_UNESCAPED_UNICODE);
					$after_IdlePdtQuery->ip2 = json_encode($afterConfig['ip2'], JSON_UNESCAPED_UNICODE);
					if( !$SwitchLineList2) {
						$after_IdlePdtQuery->vlan = $SwitchLineList2['switch_vlan'];
					}
					
					$afterConfig['is_exchangedip'] = 1;
					
					$DetailQuery->flow_after_config = json_encode($afterConfig, JSON_UNESCAPED_UNICODE);
					if( $front_IdlePdtQuery->update() && $after_IdlePdtQuery->update() && $DetailQuery->update() ) {
						$transaction->commit();
						$arrReturn = [
							'status' => 1,
							'info' => 'IP对换成功'
						];
					} else {
						$transaction->rollBack();
						$arrReturn = [
							'status' => 0,
							'info' => 'IP对换失败'
						];
					}
					return $this->renderJSON($arrReturn);
					
				}
			} else {
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info' => '当前不能执行这个操作'
				];
				return $this->renderJSON($arrReturn);
			}
			
		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status' => 0,
				'info' => '当前不能执行这个操作'
			];
		}
		return $this->renderJSON($arrReturn);
	}	
		
	public function db($sql) {
		return Yii::$app->db->createCommand($sql);
	}
}