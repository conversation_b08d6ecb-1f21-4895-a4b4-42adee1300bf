<?php

use addons\VymCloud\common\models\CloudSystem;
use yii\grid\GridView;
use common\helpers\Html;
use yii\widgets\ActiveForm;
use common\enums\StatusEnum;
use common\enums\AppEnum;
use common\helpers\ArrayHelper;
use ji<PERSON>an\treegrid\TreeGrid;

/** @var yii\web\View $this */
/** @var yii\data\ActiveDataProvider $dataProvider */
/** @var common\models\base\SearchModel $searchModel */
/** @var $clusterList */

$this->title                   = '操作系统';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="cloud-os-tpl-index">
    <div class="box">
        <div class="box-header with-border">
            <h3 class="box-title"><?= Html::encode($this->title) ?></h3>
            <div class="box-tools">
                <?= Html::create(['ajax-edit'], '创建', [
                    'data-toggle' => 'modal',
                    'data-target' => '#ajaxModal',
                    'class' => "btn btn-primary btn-sm no-loading"
                ]); ?>
            </div>
        </div>
        <div class="box-body table-responsive">
            <?= TreeGrid::widget([
                'dataProvider'     => $dataProvider,
                'keyColumnName'    => 'id',
                'parentColumnName' => 'pid',
                'parentRootValue'  => '0', //first parentId value
                'pluginOptions'    => [
                    'initialState' => 'collapsed',
                ],
                'options'          => ['class' => 'table table-hover rf-table mb-0', 'id' => 'grid'],
                'columns'          => [
                    ['attribute' => 'id', 'headerOptions' => ['class' => 'col-md-1'],],
                    [
                        'label'          => '所属商户',
                        'headerOptions'  => ['class' => 'col-md-1'],
                        'format'         => 'raw',
                        'visible'        => Yii::$app->id == AppEnum::BACKEND, // 只在总后台显示
                        'value'          => function ($model) {
                            if ($model->merchant && $mId = $model->merchant->id) {
                                if ($mTitle = $model->merchant->title) {
                                    return Html::tag('span', $mTitle, ['class' => 'badge badge-primary']);
                                }
                                return Html::tag('span', "ID:{$mId}", ['class' => 'badge badge-primary']);
                            }
                            return Html::tag('span', '平台直属', ['class' => 'badge badge-secondary']);
                        },
                    ],
                    [
                        'attribute' => 'title',
                        'format'    => 'raw',
                        'headerOptions' => ['class' => 'col-md-2'],
                        'value'     => function ($model, $key, $index, $column) {
                            $str = Html::tag('span', $model->title, [
                                'class' => 'm-l-sm',
                            ]);
                            $str .= Html::a(' <i class="iconfont iconplus-circle pointer"></i>',
                                ['ajax-edit', 'pid' => $model['id']], [
                                    'data-toggle' => 'modal',
                                    'data-target' => '#ajaxModal',
                                ]);
                            return $str;
                        },
                    ],
                    ['attribute' => 'name', 'headerOptions' => ['class' => 'col-md-2'],],
                    ['attribute' => 'mark', 'headerOptions' => ['class' => 'col-md-2'],],
                    [
                        'attribute'     => 'cluster_ids',
                        'headerOptions' => ['class' => 'col-md-2'],
                        'format'        => 'raw',
                        'value'         => function ($model) use ($clusterList) {
                            $value = '';
                            if ($model->cluster_ids) {
                                foreach (explode(',', $model->cluster_ids) as $clusterId) {
                                    $cluster = $clusterList[$clusterId] ?? "集群ID{$clusterId}";
                                    $value   .= Html::tag('span', $cluster, ['class' => 'label label-outline-default mr-1']) . ' ';
                                }
                            }else{
                                $value = '-';
                            }
                            return $value;
                        },
                    ],
                    [
                        'attribute'     => 'state',
                        'headerOptions' => ['class' => 'col-md-1'],
                        'format'        => 'raw',
                        'value'         => function ($model) {
                            $stateText  = $model->state == 1 ? '启用' : '禁用';
                            $stateClass = $model->state == 1 ? 'primary' : 'danger';
                            return Html::tag('span', $stateText, ['class' => 'label label-' . $stateClass]);
                        },
                    ],
                    [
                        'attribute'     => 'sort',
                        'format'        => 'raw',
                        'headerOptions' => ['class' => 'col-md-1'],
                        'value'         => function ($model, $key, $index, $column) {
                            return Html::sort($model->sort);
                        },
                    ],
                    [
                        'header'   => "操作",
                        'class'    => 'yii\grid\ActionColumn',
                        'headerOptions'  => ['style' => 'width: 120px;'],
                        'contentOptions' => ['style' => 'width: 120px;', 'class' => 'text-center'],
                        'template' => '{edit} {delete}',
                        'buttons'  => [
                            'edit'   => function ($url, $model, $key) {
                                return Html::edit(['ajax-edit', 'id' => $model->id], '编辑', [
                                    'data-toggle' => 'modal',
                                    'data-target' => '#ajaxModal',
                                ]);
                            },
                            'delete' => function ($url, $model, $key) {
                                return Html::delete(['delete', 'id' => $model->id]);
                            },
                        ],
                    ],
                ],
            ]); ?>
        </div>
    </div>
</div>
