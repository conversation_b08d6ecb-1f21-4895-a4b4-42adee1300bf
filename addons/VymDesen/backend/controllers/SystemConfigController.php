<?php

namespace addons\VymDesen\backend\controllers;

use addons\VymDesen\backend\models\UserAdmin\UserAdmin;
use addons\VymDesen\backend\controllers\BaseController;
use addons\VymDesen\common\models\AdminSystemConfig;
use addons\VymDesen\common\models\ConfigSystem;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;

class SystemConfigController extends BaseController
{

    #基础设置
    public function actionIndex()
    {
        $array =array(
            'recommended_config',
            'Commission_expiration_time',
            'commission_rules',
            'membership_level',
            'amount_integral_proportion',
            'membership_rules',
            'maximum_withdrawal_amount',
            'old_membership_level',
        );
        // array('1月'=>'0.1','3月'=>'0.2','1年'=>'0.5');
        // print_r(json_encode(array('1'=>'0.1','3'=>'0.2','6'=>'0.5','12'=>'0.5')));die;

        $SystemConfigModel = new AdminSystemConfig();
        $UserAdminModel    = new UserAdmin();

        $SystemConfigArr = $SystemConfigModel->find()
            ->where(['<>', 'config_name', 'protocol_template'])
            ->orderBy('config_sort asc')
            ->asArray()
            ->all();
        foreach ($SystemConfigArr as $key => $value){
            if (in_array($value['config_name'],$array)){
                ArrayHelper::remove($SystemConfigArr, $key);
            }
        }
        $adminlist = $UserAdminModel->find()->where(['status' => 1])->asArray()->all();
        return $this->render('index', ['ArrRes' => $SystemConfigArr, 'Adminlist' => $adminlist]);
    }
    public function actionUser(){
        $array =array(
            'recommended_config',
            'Commission_expiration_time',
            'commission_rules',
            'membership_level',
            'amount_integral_proportion',
            'membership_rules',
            'maximum_withdrawal_amount',
            'old_membership_level',
        );
        $SystemConfigModel = new AdminSystemConfig();
        $UserAdminModel    = new UserAdmin();

        $SystemConfigArr = $SystemConfigModel->find()
                                             ->where(['<>', 'config_name', 'protocol_template'])
                                             ->orderBy('config_sort asc')
                                             ->asArray()
                                             ->all();
        foreach ($SystemConfigArr as $key => $value){
            if (!in_array($value['config_name'],$array)){
                ArrayHelper::remove($SystemConfigArr, $key);
            }
        }
        $adminlist = $UserAdminModel->find()->where(['status' => 1])->asArray()->all();

        return $this->render('user', ['ArrRes' => $SystemConfigArr, 'Adminlist' => $adminlist]);
    }

    #协议模板设置
    public function actionTemplateSet()
    {

        $SystemConfigModel = new AdminSystemConfig();

        $SystemConfigArr = $SystemConfigModel->find()->where(['config_name' => 'protocol_template'])->asArray()->one();

        $Arr['protocol_template'] = [
            'alias'  => $SystemConfigArr['config_alias'],
            'value'  => $SystemConfigArr['config_value'],
            'remaek' => $SystemConfigArr['config_remark'],
        ];
        return $this->render('template-set', ['ArrRes' => $Arr]);
    }

    #修改设置
    public function actionUpdate()
    {
        $SystemConfigModel = new AdminSystemConfig();
        $post = $this->post();
        $recommended_config_key = ArrayHelper::getValue($post,'recommended_config_key');
        $recommended_config_val = ArrayHelper::getValue($post,'recommended_config_val');
        $coupon_discount_val = ArrayHelper::getValue($post,'coupon_discount_val');
        $coupon_discount_quota = ArrayHelper::getValue($post,'coupon_discount_quota');
        $coupon_discount_number = ArrayHelper::getValue($post,'coupon_discount_number');
        $coupon_minus_val = ArrayHelper::getValue($post,'coupon_minus_val');
        $coupon_minus_quota = ArrayHelper::getValue($post,'coupon_minus_quota');
        $coupon_minus_number = ArrayHelper::getValue($post,'coupon_minus_number');
        $old_user = ArrayHelper::getValue($post,'old_user');
        $array = array();
        if ($recommended_config_key && $recommended_config_val){
            foreach ($recommended_config_key as $item => $value){
                if ($value && $recommended_config_val[$item]){
                    $array[$value] = $recommended_config_val[$item];
                }else{
                    return $this->renderJSON(['status'=>0,'info'=> '参数不能为空']);
                }
            }
        }
        ArrayHelper::remove($post,'recommended_config_key');
        ArrayHelper::remove($post,'recommended_config_val');
        // ArrayHelper::setValue($post,'recommended_config',json_encode($array));
        $membership_level_key = ArrayHelper::getValue($post,'membership_level_key');
        $membership_level_val = ArrayHelper::getValue($post,'membership_level_val');
        $membership_desc      = ArrayHelper::getValue($post,'membership_desc');
        $arrayN         = array();
        $arrayDesc      = array();
        $couponArray    = array();
        if ($membership_level_key && $membership_level_val){
            foreach ($membership_level_key as $itemN => $valueN){
                if ($valueN && $membership_level_val[$itemN]){
                    $arrayN[$valueN] = $membership_level_val[$itemN];
                    #生成规则
                    $arrayDesc[$valueN] = $membership_desc[$itemN];
                }else{
                    return $this->renderJSON(['status'=>0,'info'=> '参数不能为空']);
                }
                $couponArray[$valueN]['discount']['val'] = $coupon_discount_val[$itemN];
                $couponArray[$valueN]['discount']['quota'] = $coupon_discount_quota[$itemN];
                $couponArray[$valueN]['discount']['number'] = $coupon_discount_number[$itemN];
                $couponArray[$valueN]['minus']['val'] = $coupon_minus_val[$itemN];
                $couponArray[$valueN]['minus']['quota'] = $coupon_minus_quota[$itemN];
                $couponArray[$valueN]['minus']['number'] = $coupon_minus_number[$itemN];
            }
        }
        // $couponArray = $this->array_filter_recursive($couponArray);
        ArrayHelper::remove($post,'membership_level_key');
        ArrayHelper::remove($post,'membership_level_val');
        ArrayHelper::remove($post,'membership_desc');
        ArrayHelper::remove($post,'coupon_discount_val');
        ArrayHelper::remove($post,'coupon_discount_quota');
        ArrayHelper::remove($post,'coupon_discount_number');
        ArrayHelper::remove($post,'coupon_minus_val');
        ArrayHelper::remove($post,'coupon_minus_quota');
        ArrayHelper::remove($post,'coupon_minus_number');
        ArrayHelper::setValue($post,'membership_level',json_encode($arrayN));
        ArrayHelper::remove($post,'old_user');
        ArrayHelper::setValue($post,'old_membership_level',json_encode($old_user));
        if (!empty($post)) {
            #开启事务
            $transaction = Yii::$app->db->beginTransaction();
            $status      = true;
            if (!empty($arrayDesc)){
                $jsonNew = json_encode(array(
                    'desc'=>'会员等级和对应的积分【积分A-B】',
                    'list'=>$arrayDesc,
                    'coupon'=>$couponArray
                ),true);
                $SystemConfigNew = $SystemConfigModel->find()->where(['config_name' => 'membership_level'])->one();
                $SystemConfigNew->config_remark = $jsonNew;
                $SystemConfigNew->update();
            }
            foreach ($post as $key => $val) {
                $SystemConfigQuery = $SystemConfigModel->find()->where(['config_name' => $key])->one();
                if (empty($SystemConfigQuery)) {
                    $transaction->rollback();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '未知的系统设置参数：' . $key
                    ];
                    return $this->renderJSON($arrReturn);
                } else {

                    if ($key == 'hkd_rate') {
                        if (!is_numeric($val)) {
                            $transaction->rollback();
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '汇率必须为数字'
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                    }
                    $SystemConfigQuery->config_value = $val;
                    if ($key == 'notice_config') {
                        $SystemConfigQuery->config_value = json_encode($val);
                    }
                    if (!empty($SystemConfigQuery->dirtyAttributes)) {
                        if ($SystemConfigQuery->update()) {

                            if ($key == 'hkd_rate') {

                                $files_path = dirname(Yii::$app->BasePath) . '/common/rate';

                                if (!file_put_contents($files_path, $val)) {
                                    $transaction->rollback();
                                    $arrReturn = [
                                        'status' => 0,
                                        'info'   => '汇率未写入成功'
                                    ];
                                    return $this->renderJSON($arrReturn);
                                }

                            }

                            continue;

                        } else {
                            $status = false;
                            break;
                        }
                    } else {
                        continue;
                    }
                }
            }

            if ($status == false) {
                $transaction->rollback();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '更新失败'
                ];
            } else {
                $transaction->commit();
                $arrReturn = [
                    'status' => 1,
                    'info'   => '保存成功'
                ];
            }
            return $this->renderJSON($arrReturn);

        } else {
            $arrReturn = [
                'status' => 0,
                'info'   => '提交异常'
            ];
            return $this->renderJSON($arrReturn);
        }

    }

    protected function array_filter_recursive(array &$arr){
        if (count($arr) <1) return [];

        foreach ($arr as &$arrItem) {
            if (is_array($arrItem)){
                $arrItem = $this->array_filter_recursive($arrItem);
            }
        }
        return array_filter($arr);
    }

    /**
     * APP版本更新
     *
     * @return string
     */
    public function actionAppUpdate()
    {
        $configSystem = ConfigSystem::findOne(['config_key' => 'app_update']);
        $oSession     = Yii::$app->getSession();
        if (Yii::$app->request->isPost) {
            $params                     = $this->post();
            $configSystem->config_value = json_encode($params, 320);
            $configSystem->save();
            $oSession->setFlash('return', ['status' => 1, 'info' => '更新成功']);
            return $this->redirect(Url::to(['system-config/app-update']));
        }
        $config_value = ArrayHelper::getValue($configSystem, 'config_value', '{}');
        return $this->render('app-update', ['config' => json_decode($config_value, true)]);
    }

    public function actionThirdapi() {
        if(!Yii::$app->request->isPost){
            $configSystem = ConfigSystem::findOne(['config_key' => 'thirdapi_config']);
            $config_value = ArrayHelper::getValue($configSystem, 'config_value', '[]');
            return $this->render('thirdapi-config', ['configs'=> json_decode($config_value, true)]);
        }
        return $this->render('thirdapi-config');
    }
}