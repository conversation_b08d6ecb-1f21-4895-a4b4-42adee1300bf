<?php
use yii\helpers\Url;
use yii\helpers\Html;

$this->title = '控制台管理-用户产品管理-分配机器';
?>
<?php $this->beginBlock('content') ?>
<style type="text/css">
 .disabled{display:none}
</style>
<link rel="stylesheet" href="/css/layui.css"  media="all">
<div class="row">
	<div class="col-xs-12">   
        <form id='form' class="form-horizontal" method='post' role="form">
        <div class="form-group">
            <label class="col-sm-3 control-label no-padding-right" >服务器提供商</label>
    		<div class="col-sm-2" style="margin-top: 5px;">    		    
    			<label>
    				<input name="servicerprovider" <?php if($arrRes['servicerprovider'] == 0 ):?>  checked="checked" <?php endif;?> value="0" type="radio" class="ace">
    				<span class="lbl"> 自有</span>
    			</label>
    			<label>
    				<input name="servicerprovider" <?php if($arrRes['servicerprovider'] == 1 ):?>  checked="checked" <?php endif;?>   value="1" type="radio" class="ace">
    				<span class="lbl"> 供应商提供</span>
    			</label>    			
    			<span class="middle" style="color:red;margin-left:5px"></span>
    		</div>
	    </div>
	    <div class="form-group">
	       <label class="col-sm-3 control-label no-padding-right" >所属机房</label>
    		<div class="col-sm-2">
    			<select name="room_id" id="room_id" class="col-xs-10">
    			    <option value="">请选择机房</option>			
    				<?php foreach ($roomRes as $value): ?>
					<option value="<?php echo $value['id'] ?>" rel="<?php echo $value['id'] ?>" <?php if ($value['id'] == $arrRes['room_id']): ?>
							selected='selected'
						<?php endif ?>
						><?php echo $value['name'] ?></option>
					<?php endforeach ?>
    			</select>
    			<span class="middle" style="color:red;margin-left:5px"></span>
    		</div>		    
    		<label class="col-sm-1 control-label no-padding-right <?php if($arrRes['servicerprovider'] == 1 ):?> disabled <?php endif;?>" id="cabinet_label">所属机柜</label>
    		<div class="col-sm-2 <?php if($arrRes['servicerprovider'] == 1 ):?> disabled <?php endif;?>" id="cabinet_div">
    			<select name="cabinet_id" id="cabinet_id" class="col-xs-10" >
    			     <option value="">请选择机柜</option>	
    				 <?php foreach ($cabinetRes as $value): ?>
    					<option value="<?php echo $value['id'] ?>" rel="<?php echo $value['name'] ?>"
    						<?php if ($value['id'] == $arrRes['cabinet_id']): ?>
    							selected='selected'
    						<?php endif ?>
    					><?php echo $value['name'] ?></option>
    					<?php endforeach ?>
    			</select>
    			<span class="middle" style="color:red;margin-left:5px"></span>
    		</div>
			<label id="provider_label" class="col-sm-1 control-label no-padding-right <?php if($arrRes['servicerprovider'] == 0 ):?> disabled <?php endif;?>"> 供应商</label>
        	<div class="col-sm-2 <?php if($arrRes['servicerprovider'] == 0 ):?> disabled <?php endif;?>" id="provider_div">        	   
        		<input type="text" readonly name='' placeholder="选择供应商" value='<?php if(!empty($arrRes['provider'])):?><?php echo $arrRes['provider'][0]['name']?><?php endif;?>' id="provider_name"  class="col-xs-10">
        		<input type="hidden" name='provider_id' value='<?php echo $arrRes['provider_id']?>' id="provider_id"  class="col-xs-10">
        		<span class="middle" style="color:red;margin-left:5px"></span>
        	</div>
	    </div>	    
    	<div class="form-group">
        <label class="col-sm-3 control-label no-padding-right" >产品库选择</label>
		<div class="col-sm-2">
		<?php if(!empty($arrRes['idlepdt'])):?>
			<input type="text" name='idle_name' placeholder="选择闲置产品" value='<?= $arrRes['idlepdt'][0]['idle_name']?>' id="idle_name" class="col-xs-10">
    		<input type="hidden" name='idle_id' value='<?php echo $arrRes['idle_id']?>' id="idle_id"  class="col-xs-10">
    		<?php else:?>
    		<input type="text" name='' placeholder="选择闲置产品" value='' readonly="readonly" id="idle_name" class="col-xs-10">
    		<input type="hidden" name='idle_id' value='' id="idle_id"  class="col-xs-10">
    		<?php endif;?>
			<span class="middle" style="color:red;margin-left:5px"></span>		
		</div>
		
        <label class="col-sm-1 control-label no-padding-right" >产品配置类别</label>
		<div class="col-sm-2">
			<select name="pdt_id" id="pdt_id" class="col-xs-10 col-sm-7">	
			  <option value="">请选择产品配置类别</option>
			 <?php foreach ($PdtManageList as $value): ?>
					<option value="<?php echo $value['id'] ?>" rel="<?php echo $value['id'] ?>"
						<?php if ($value['id'] == $arrRes['pdt_id']): ?>
							selected='selected'
						<?php endif ?>
					><?php echo $value['name'] ?></option>
					<?php endforeach ?>	
			</select>
			<span class="middle" style="color:red;margin-left:5px"></span>    			
			<span style="margin-left: 4px;display:none" class="btn btn-sm btn-success" data-rel="tooltip" href="#modal-eips" id="Detailedconfig">
				<i class="ace-icon fa fa-search-plus bigger-130"></i>配置详情
			</span>
		</div>
   </div>
   <!--配置详情 -->
   <div id="Detailedconfigshow" class="modal">
	<div class="modal-dialog">
		<div class="modal-content">
			<div id="modal-wizard-container">	
			<div class="modal-header">					
				<h4 class="widget-title">产品配置详情：</h4>					
			</div>			
				<div class="modal-body step-content">
					<div class="step-pane active" style="min-height:70px" data-step="1">							
						<div class="form-group">
							<label class="col-sm-3 control-label no-padding-right" > CPU： </label>								
							<div class="col-sm-5">
							    <input name="cpu" value="<?=$config['cpu']?>" class="form-control" id="cpu">        							
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-3 control-label no-padding-right" > 内存大小： </label>								
							<div class="col-sm-5">
    							<input name="ram" value="<?=$config['ram']?>" class="form-control" id="ram">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-3 control-label no-padding-right" > 硬盘： </label>								
							<div class="col-sm-5">
    							<input name="hdd" value="<?=$config['hdd']?>" class="form-control" id="hdd">  
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-3 control-label no-padding-right" > 带宽： </label>								
							<div class="col-sm-5">
    							<input name="configbandwidth" value="<?=$config['configbandwidth']?>" class="form-control" id="configbandwidth">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-3 control-label no-padding-right" > IP可用数： </label>								
							<div class="col-sm-5">
    							<input name="ipnumber" value="<?=$config['ipnumber']?>" class="form-control" id="ipnumber">
							</div>
						</div>
						<div class="form-group defense">
							<label class="col-sm-3 control-label no-padding-right" > 防御流量： </label>								
							<div class="col-sm-5">
    							<input name="defense" value="<?=@$config['defense']?>" class="form-control" id="defense">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-3 control-label no-padding-right" > 操作系统： </label>								
							<div class="col-sm-5">
    							<input name="operatsystem" value="<?=$config['operatsystem']?>" class="form-control" id="operatsystem">
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="modal-footer wizard-actions">				
			<button class="btn btn-success btn-sm btn-next" id="mount" data-dismiss="modal">确定
			    <i class="ace-icon fa fa-arrow-right icon-on-right"></i>
			</button>
			<button class="btn btn-danger btn-sm pull-left" data-dismiss="modal"><i class="ace-icon fa fa-times"></i>
				取消
			</button>
		</div>
       </div>
    </div>
    <!-- 配置详情end -->
    <div class="form-group <?php if ($arrRes['servicerprovider'] == 1):?> disabled <?php endif;?>" id="switch_div" >
    	<label class="col-sm-3 control-label no-padding-right"> 交换机</label>
    	<div class="col-sm-2">        		
    		<input type="text" readonly name='' placeholder="选择交换机" value='<?php if(!empty($arrRes['switch'])):?><?=$arrRes['switch'][0]['ip']?><?php endif;?>' id="switch_location_input"   class="col-xs-10">
    		<input type="hidden" name='switch_location' id="switch_location" value='<?php echo $arrRes['switch_location'] ?>' class="col-xs-10">
    		<span class="middle" style="color:red;margin-left:5px"></span>
    	</div>    	
    	<label class="col-sm-1 control-label no-padding-right" > 交换机端口</label>
    	<div class="col-sm-2">
    		<input type="text" name='switch_port' id="switch_port" value='<?php echo @$arrRes['switch_port'] ?>' class="col-xs-10">
    		<span class="middle" style="color:red;margin-left:5px"></span>
    	</div>        	
    </div>
    <div class="form-group <?php if($arrRes['servicerprovider'] == 1):?> disabled<?php endif;?>"  id="occupies_position_div">
    	<label class="col-sm-3 control-label no-padding-right" > 所占机位 (单位：U )</label>
    	<div class="col-sm-2">
    		<input type="text" name='occupies_position' id="occupies_position" value='<?=$arrRes['occupies_position']?>'  class="col-xs-10">
    		<span class="middle" style="color:red;margin-left:5px"></span>
    	</div>
    </div>
    <!-- 自有 -->
    <div class="form-group <?php if($arrRes['servicerprovider'] == 1):?> disabled<?php endif;?>" id= "ips_have_div">
	    <label class="col-sm-3 control-label no-padding-right" > IP地址</label>
    	<div class="col-sm-5" id="InputsWrapper1">
        	<div class="col-sm-4 control-label no-padding-right" >
        		<button type="button" id="IPlibraryselection" class="btn btn-xs btn-info btn-success" style="width: 210px;"><i class="ace-icon glyphicon glyphicon-plus">IP库选择</i></button>
        	</div>
        	<div id="iplistji1">
        	  <?php if($arrRes['servicerprovider'] == 1):?>
        	  <?php elseif($arrRes['servicerprovider'] == 0):?>
        	  <?php if(!empty(json_decode($arrRes['ip']))):?>
            	  <?php foreach (json_decode($arrRes['ip']) as $value):?>
            		<div class="col-sm-4 control-label no-padding-right"><input value="<?=$value?>" type="text" name="have_ips[]" readonly><button type="button" style="margin-left: 4px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button></div>
            	 <?php endforeach;?>
            	 <?php endif;?>
        	 <?php endif;?>
    	   </div>
        </div>
    </div>
    <!-- 供应商IP -->
    <div class="form-group <?php if($arrRes['servicerprovider'] == 0):?> disabled<?php endif;?>" id= "ips_provider_div">
	    <label class="col-sm-3 control-label no-padding-right" > IP地址</label>
    	<div class="col-sm-5" id="InputsWrapper">
        	<div class="col-sm-4 control-label no-padding-right" >
        		<input type="text" name='' readonly id="provider_ips" placeholder="IP段形式如：127.0.0.1-2" value=''>            		
        		<button type="button" id="AddMoreFileBox" class="btn btn-xs btn-info btn-success" ><i class="ace-icon glyphicon glyphicon-plus"></i></button>
        	</div>
        	<div id="iplistji">
        	<?php if($arrRes['servicerprovider'] == 1):?>
            	<?php if(!empty(json_decode($arrRes['ip']))):?>
                	<?php foreach (json_decode($arrRes['ip']) as $value):?>
                		<div class="col-sm-4 control-label no-padding-right"><input value="<?=$value?>" type="text" name="provider_ips[]"><button type="button" style="margin-left: 4px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button></div>
                	<?php endforeach;?>
            	<?php endif;?>
        	<?php elseif($arrRes['servicerprovider'] == 0):?>
        	<?php endif;?>
        	</div>
    	</div>
    </div>             
    <div class="form-group">
	    <label class="col-sm-3 control-label no-padding-right" > IPMI：IP地址</label>
    	<div class="col-sm-2">
    	     <input type="text" name='ipmi_ip' id="ipmi_ip" value='<?php echo @$arrRes['ipmi_ip'] ?>'  class="col-xs-10">
    		<span class="middle" style="color:red;margin-left:5px"></span>
    	</div>
    </div>
    <div class="form-group">
    	<label class="col-sm-3 control-label no-padding-right" > IPMI：用户名</label>
    	<div class="col-sm-2">
    		<input type="text" name='ipmi_name' id="ipmi_name" value='<?php echo @$arrRes['ipmi_name'] ?>' class="col-xs-10">
    		<span class="middle" style="color:red;margin-left:5px"></span>
    	</div>
    </div>
    <div class="form-group">
    	<label class="col-sm-3 control-label no-padding-right" > IPMI：密码</label>
    	<div class="col-sm-2">
    		<input type="text" name='ipmi_pwd' id="ipmi_pwd" value='<?php echo @$arrRes['ipmi_pwd'] ?>' class="col-xs-10">
    		<span class="middle" style="color:red;margin-left:5px"></span>
    	</div>
    </div>   
    <input type="hidden" name="id" value="<?php echo $arrRes['id']?>">
    <div class="clearfix form-actions">
        <div class="col-md-offset-3 col-md-9">            	
        	<button id="go" class="btn btn-info" type="submit">
        		<i class="ace-icon fa fa-check bigger-110"></i>
        		确定更换
        	</button>
        	&nbsp; &nbsp; &nbsp;
        	<button class="btn" type="reset">
        		<i class="ace-icon fa fa-undo bigger-110"></i>
        		还原内容
        	</button>
            &nbsp; &nbsp; &nbsp;
        	<button class="btn btn-danger" type="button" style="width:120px" onclick="window.history.back()">
        		<i class="ace-icon fa fa-reply icon-only"></i>
        		返回上一页
        	</button> 
        </div>
    </div>        
    <div class="hr hr-24"></div>        
 </form>
</div>
</div>
<!--IP库 Modal -->
<div id="IPlibraryselectionshow" class="modal">
<div class="modal-dialog" style="min-width: 1200px;">
	<div class="modal-content">
		<div id="modal-wizard-container">	
			<div class="modal-header">					
				<h4 class="widget-title">IP库列表：</h4>					
			</div>
			<div class="row" style="">
        		<div class="col-xs-12" style="margin-left:20px;margin-top: 10px;">    		     		
        			 <div style="float: left;margin-right: 6px;">   
        		         <input type="text" class="form-control search-query" name="ifame_select_ip" value='<?php echo Html::encode(Yii::$app->controller->get('ip')) ?>'  placeholder="IP">
        		     </div>
        		     <div>
            			<button type="submit" id="ifame_select_ip_submit" class="btn btn-white btn-primary" style="margin-left:15px">
            				<span class="fa fa-search"></span>
            				搜索
            			</button>        	 
        		     </div>
    		   </div>
        	</div>
			<div class="modal-body step-content">
				<div class="step-pane active" style="min-height:70px" data-step="1">
					<form action="" id='checkbox1'>
                	   <table class="layui-hide" id="iplist"></table>
                	</form>	
				</div>
			</div>
		</div>
	</div>
	<div class="modal-footer wizard-actions">			
		<button class="btn btn-success btn-sm btn-next" id="selectip" data-dismiss="modal">确定
		    <i class="ace-icon fa fa-arrow-right icon-on-right"></i>
		</button>
		<button class="btn btn-danger btn-sm pull-left" data-dismiss="modal"><i class="ace-icon fa fa-times"></i>
			取消
		</button>
	</div>
   </div>
</div>
<script type="text/html" id="statusTpl">
  {{#  if(d.status === '0'){ }}
    <span style="color: #d15b47;">闲置</span>
  {{# } }}
  {{#  if(d.status === '1'){ }}
    <span style="color: #82af6f;">使用中</span>
  {{#  } }}
  {{#  if(d.status === '2'){ }}
    <span style="color: #f89406;">预留</span>
  {{# } }}
    </script>
<!-- IP库end -->
<script src="/js/typeahead.jquery.js"></script>
<script src="/js/ace/elements.typeahead.js"></script>


<script src="/js/layui.js" charset="utf-8"></script>
<script>
    var start = {
    	    elem: '#start',
    	    format: 'YYYY-MM-DD hh:mm:ss',
    	    min: '1900-01-01 00:00:00', //设定最小日期为当前日期
    	    max: '2099-06-16 23:59:59', //最大日期
    	    istime: false,
    	    istoday: true,
    	    choose: function(datas){
    	         end.min = datas; //开始日选好后，重置结束日的最小日期
    	         end.start = datas //将结束日的初始值设定为开始日
    	    }
    	};
    
    	var end = {
    	    elem: '#end',
    	    format: 'YYYY-MM-DD hh:mm:ss',
    	    min: '1900-01-01 00:00:00', //设定最小日期为当前日期,
    	    max: '2099-06-16 23:59:59',
    	    istime: false,
    	    istoday: true,
    	    choose: function(datas){
    	        start.max = datas; //结束日选好后，重置开始日的最大日期
    	    }
    	};
    laydate(start);
    laydate(end);
    laydate.skin('molv');
</script>
<script type="text/javascript">
    $(document).ready(function() {
        var MaxInputs    = 1000; //maximum input boxes allowed
        var InputsWrapper  = $("#iplistji"); //Input boxes wrapper ID
        var AddButton    = $("#AddMoreFileBox"); //Add button ID
        var x = InputsWrapper.length; //initlal text box count
        var FieldCount=1; //to keep track of text box added
        $(AddButton).click(function (e) //on add input button click
        {
            if(x <= MaxInputs) //max input box allowed
            {
              FieldCount++; //text box added increment
              //add input box
              $(InputsWrapper).append('<div class="col-sm-4 control-label no-padding-right"><input value="" type="text" name="provider_ips[]"/><button type="button" style="margin-left: 4px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button><div>');
              x++; //text box increment
            }
            return false;
        });
        $("body").on("click",".removeclass", function(e){ //user click on remove text                
                $(this).parent('div').remove(); //remove text box                   
            return false;
        })
    });
</script> 
<script type="text/javascript">
//当选择服务器为自有或者供应商时
 $('input[type=radio][name=servicerprovider]').change(function() {
    if (this.value == '0') {
    	$('#provider_label').addClass('disabled');
    	$('#provider_div').addClass('disabled');
    	$('#cabinet_label').removeClass('disabled');
    	$('#cabinet_div').removeClass('disabled');
    	$('#property_label').removeClass('disabled');
    	$('#property_div').removeClass('disabled'); 

    	$('#occupies_position_div').removeClass('disabled');
    	$('#switch_div').removeClass('disabled');
    	$('#ips_have_div').removeClass('disabled');
    	$('#ips_provider_div').addClass('disabled');
    }
    else if (this.value == '1') {
    	$('#provider_label').removeClass('disabled');
    	$('#provider_div').removeClass('disabled');
    	$('#cabinet_label').addClass('disabled');
    	$('#cabinet_div').addClass('disabled');
    	$('#property_label').addClass('disabled');
    	$('#property_div').addClass('disabled');
    	
    	$('#occupies_position_div').addClass('disabled');
    	$('#switch_div').addClass('disabled');
    	$('#ips_have_div').addClass('disabled');
    	$('#ips_provider_div').removeClass('disabled');

    	$("[name='idle_id']").val("");
    	$("[name='idle_name']").val("");
    	$("[name='ipmi_ip']").val("");
    	$("[name='ipmi_name']").val("");
    	$("[name='ipmi_pwd']").val("");
    	
    }
      var url = "<?php echo Url::to(['ajax-getroom']) ?>";
      $.post(url, {servicerprovider:this.value}, function(data){    		
		$("[name='room_id']").empty();  		    		
		$("[name='room_id']").append('<option value="">请选择机房</option>');
		$.each(data['data']['Room'], function(key, val){
			$("[name='room_id']").append('<option value="'+val['id']+'" rel="'+val['id']+'">'+val['name']+'</option>');
		});	
		layer.closeAll('loading');
     },'json');
});
//供应商打开
$("#provider_name").click(function(){
	// 正常打开
	layer.open({
	    type: 2, 
	    area: ['1020px', '600px'],
	    title:"" || "选择供应商",
	    content: "<?php echo Url::to(['provider/select'])?>",
	    btn: ['确定', '关闭'],
	    yes: function(index, layero){ //或者使用btn1
			var iframeWin = layero.find('iframe')[0];//得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
            var body = $(iframeWin).contents();
            var radio = $(body).find(".i-checks:checked");
            $("#provider_id").val(radio.val());
       		$("#provider_name").val(radio.data("name"));
            layer.close(index);
		},
		cancel: function(index){ //或者使用btn2
	        //按钮【按钮二】的回调
	    }
	}); 
});
</script>
<script type="text/javascript">
$("#idle_name").click(function(){
	var room_id = $('#room_id option:selected').val(); 
	var cabinet_id = $('#cabinet_id option:selected').val();
	var servicerprovider = $("input[name='servicerprovider']:checked").val();
	var url = '<?php echo Url::to(['idle-pdt/select'])?>';
if( servicerprovider == 0 )
{
	if(room_id =="" ){
		layer.alert("请先选择机房", {icon:7});
		return ;
	}
	var idleurl = url+"?room_id="+room_id+"&servicerprovider="+servicerprovider;
}
if( servicerprovider == 1 )
{
	if(room_id ==""){
		layer.alert("请先选择机房", {icon:7});
		return ;
	}
	var idleurl = url+"?room_id="+room_id+"&servicerprovider="+servicerprovider;
}     
// 正常打开
layer.open({
    type: 2, 
    area: ['1020px', '600px'],
    title:"" || "选择闲置产品",
    content: idleurl,
    btn: ['确定', '关闭'],
    yes: function(index, layero){ //或者使用btn1            		    
		var iframeWin = layero.find('iframe')[0];//得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
        var body = $(iframeWin).contents();
        var radio = $(body).find(".i-checks:checked");
        $("#idle_id").val(radio.val());
   		$("#idle_name").val(radio.data("name"));
   		var id = radio.val();
   		var url = "<?php echo Url::to(['get-selectidle']) ?>";
       		$("#Detailedconfig").show();
        	$.post(url, {id:id}, function(data){  
                var res = data['data'];
                if( res['servicerprovider'] == 0)
                {                            	
                	document.getElementById('iplistji1').innerHTML = '';                            	
                	$.each(res['ip'], function(key, val){
            			$("#iplistji1").append('<div class="col-sm-4 control-label no-padding-right"><input value="'+val+'" type="text" readonly="readonly" name="have_ips[]"/><button type="button" style="margin-left: 4px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button><div>');
                    });	                            	
                   	$("#switch_location").val(res['switch']['id']);                    	 
                	$("#switch_location_input").val(res['switch']['ip']);
                	$("#switch_port").val(res['switch_port']);
                	$("#occupies_position").val(res['occupies_position']);
                	$("#property").find("option[value = '"+res['property']+"']").attr("selected","selected");
                }
                if( res['servicerprovider'] == 1)
                {
                	document.getElementById('iplistji').innerHTML = '';
                	$.each(res['ip'], function(key, val){
            			$("#iplistji").append('<div class="col-sm-4 control-label no-padding-right"><input value="'+val+'" type="text" readonly="readonly" name="provider_ips[]"/><button type="button" style="margin-left: 4px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button><div>');
                    });
                   	$("#provider_id").val(res['provider']['id']);                    	 
                	$("#provider_name").val(res['provider']['name']);
                }        
                $("#pdt_id").find("option[value = '"+res['pdt_id']+"']").attr("selected","selected");
                $("#cabinet_id").find("option[value = '"+res['cabinet_id']+"']").attr("selected","selected");
                //$("#attribute_id").find("option[value = '"+res['attribute_id']+"']").attr("selected","selected");
                $("#type_id").find("option[value = '"+res['type_id']+"']").attr("selected","selected");                    	
                $("#ipmi_ip").val(res['ipmi_ip']);
                $("#ipmi_name").val(res['ipmi_name']);
            	$("#ipmi_pwd").val(res['ipmi_pwd']);                    	 

            	$("#cpu").val(res['config']['cpu']);
            	$("#ram").val(res['config']['ram']);
            	$("#hdd").val(res['config']['hdd']);
            	$("#configbandwidth").val(res['config']['configbandwidth']);
            	$("#ipnumber").val(res['config']['ipnumber']);
            	$("#defense").val(res['config']['defense']);
            	$("#operatsystem").val(res['config']['operatsystem']);                    	 
        	},'json');    
        	
            layer.close(index);
		},
		cancel: function(index){ //或者使用btn2
	        //按钮【按钮二】的回调
	    }
	}); 
});
</script>
<script type="text/javascript">
	$("#switch_location_input").click(function(){
		var room_id = $('#room_id option:selected').val(); 
		var cabinet_id = $('#cabinet_id option:selected').val(); 
		if(room_id =="" || cabinet_id == ""){
			layer.alert("请先选择机房机柜", {icon:7});
			return ;
		}
		var url = "<?php echo Url::to(['switch-manage/select'])?>";
		// 正常打开
		layer.open({
		    type: 2, 
		    area: ['1020px', '600px'],
		    title:"" || "选择交换机",
		    content: url+"?room_id="+room_id+"&cabinet_id="+cabinet_id,
		    btn: ['确定', '关闭'],
		    yes: function(index, layero){ //或者使用btn1
				var iframeWin = layero.find('iframe')[0];//得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
	            var body = $(iframeWin).contents();
	            var radio = $(body).find(".i-checks:checked");
           		$("#switch_location_input").val(radio.data("name"));
           		$("#switch_location").val(radio.val());
	            layer.close(index);
			},
    		cancel: function(index){ //或者使用btn2
    	        //按钮【按钮二】的回调
    	    }
		}); 
	});
</script>
<script type="text/javascript">
$('input[type=radio][name=have_price_difference]').change(function() {
   if (this.value == '0') {
   	$('#difference_div').addClass('disabled');
   }
   else if (this.value == '1') {
   	$('#difference_div').removeClass('disabled');  
   }
});
$("#Detailedconfig").show();
//查看详细配置
$('#Detailedconfig').click(function(){
	$('#Detailedconfigshow').modal('show');
	return ;
});
$("[name='pdt_id']").change(function(){
	$("#Detailedconfig").show();
	var url = "<?php echo Url::to(['ajax-pdt']) ?>";
	var id = $(this).find('option:selected').attr('rel');
	var load = layer.load(2);
	$.post(url, {id:id}, function(data){    		
		$("[name='cpu']").empty();
		$("[name='ram']").empty();
		$("[name='hdd']").empty();
		$("[name='configbandwidth']").empty();
		$("[name='ipnumber']").empty();
		$("[name='defense']").empty();
		$("[name='operatsystem']").empty();
		if(data['data']['status'])
		{
			$.each(data['data']['cpu']['info'], function(key, val){
				if(data['data']['cpu']['default'] == val['id'])
        		{
					$("#cpu").val(val['name']);
            	}else
            	{
            		$("[name='cpu']").append('<option  value="'+val['id']+'" rel="'+val['name']+'">'+val['name']+'</option>');
                }    				
    		});
			$.each(data['data']['ram']['info'], function(key, val){
				if(data['data']['ram']['default'] == val['id'])
        		{
					$("#ram").val(val['name']);
					$("[name='ram']").append('<option selected="selected" value="'+val['id']+'" rel="'+val['name']+'">'+val['name']+'</option>');
            	}else
            	{
            		$("[name='ram']").append('<option  value="'+val['id']+'" rel="'+val['name']+'">'+val['name']+'</option>');
                }    				
    		});
			$.each(data['data']['hdd']['info'], function(key, val){
				if(data['data']['hdd']['default'] == val['id'])
        		{
					$("#hdd").val(val['name']);
					$("[name='hdd']").append('<option selected="selected" value="'+val['id']+'" rel="'+val['name']+'">'+val['name']+'</option>');
            	}else
            	{
            		$("[name='hdd']").append('<option  value="'+val['id']+'" rel="'+val['name']+'">'+val['name']+'</option>');
                }    				
    		});
			$.each(data['data']['bandwidth']['info'], function(key, val){
				if(data['data']['bandwidth']['default'] == val['id'])
        		{
					$("#configbandwidth").val(val['name']);
					$("[name='configbandwidth']").append('<option selected="selected" value="'+val['id']+'" rel="'+val['name']+'">'+val['name']+'</option>');
            	}else
            	{
            		$("[name='configbandwidth']").append('<option  value="'+val['id']+'" rel="'+val['name']+'">'+val['name']+'</option>');
                }    				
    		});
			$.each(data['data']['ipnumber']['info'], function(key, val){
				if(data['data']['ipnumber']['default'] == val['id'])
        		{
					$("#ipnumber").val(val['name']);
					$("[name='ipnumber']").append('<option selected="selected" value="'+val['id']+'" rel="'+val['name']+'">'+val['name']+'</option>');
            	}else
            	{
            		$("[name='ipnumber']").append('<option  value="'+val['id']+'" rel="'+val['name']+'">'+val['name']+'</option>');
                }    				
    		});
    		<?php if(!empty($data['data']['defense'])):?>
			$.each(data['data']['defense']['info'], function(key, val){
				if(data['data']['defense']['default'] == val['id'])
        		{
					$("#defense").val(val['name']);
					$("[name='defense']").append('<option selected="selected" value="'+val['id']+'" rel="'+val['name']+'">'+val['name']+'</option>');
            	}else
            	{
            		$("[name='defense']").append('<option  value="'+val['id']+'" rel="'+val['name']+'">'+val['name']+'</option>');
                }    				
    		});
    		<?php else:?>
    		//$(".defense").hide();
    		<?php endif;?>
			$.each(data['data']['system']['info'], function(key, val){
				if(data['data']['system']['default'] == val['id'])
        		{
					$("#operatsystem").val(val['name']);
					$("[name='operatsystem']").append('<option selected="selected" value="'+val['id']+'" rel="'+val['name']+'">'+val['name']+'</option>');
            	}else
            	{
            		$("[name='operatsystem']").append('<option  value="'+val['id']+'" rel="'+val['name']+'">'+val['name']+'</option>');
                }    				
    		});
    		layer.closeAll('loading');
    	}else
    	{
    		layer.alert(data['data']['info'], {icon:7});
    		layer.closeAll('loading');
        }
	},'json');
});
$('#go').click(function(){
    var url = '<?php echo Url::to(["assigned"]) ?>';
    layer.msg('正在提交...', {icon:16, time:0});
    $(".middle").empty();
    $.post(url, $('#form').serialize(), function(data){
    	if ( data['data']['status'] )
    	{
    		layer.alert(data['data']['info'], {icon:1}, function(index){				   		  
				window.location.href='<?php echo Url::to(["member-pdt/index"])?>';
		   	});
    	}
    	else
    	{
    		if ( typeof data['data']['info'] === 'string' )
    		{
    			layer.alert(data['data']['info'], {icon:7});
    			return ;
    		}        
    		$.each(data['data']['info'], function(key, val){			
    			layer.alert('用户产品信息填写有误', {icon:7});			
    			$("[name='"+key+"']").next('span').html('<i class="ace-icon fa fa-exclamation-circle bigger-110">'+val[0]+'</i>');    			
    		});
    	}
    
    }, 'json');
    
    return false;

});
//显示IP库
$('#IPlibraryselection').click(function(){ 
	var room_id = $('#room_id option:selected').val(); 
	layui.use('table', function(){
		  var table = layui.table;	  
		  table.render({
			elem: '#iplist',
			url:'<?php echo Url::to(['member-pdt/ajax-iplist'])?>',
		    where: {room_id:room_id},
		    limit:"15",
		    cols: [[
		      {type:'checkbox'}
		      ,{field:'id', width:'100',title: 'ID', sort: true}
		      ,{field:'ip', width:'150', title: 'IP地址'}
		      ,{field:'vlan', width:'80',title: 'Vlan', sort: true}
		      ,{field:'mask', width:'150', title: '掩码'}    		      
		      ,{field:'gateway', width:'150', title: '网关'} 
		      ,{field:'status',width:'100', title: '状态', sort: true,templet: '#statusTpl'}   	
		      ,{field:'remarks', title: '备注'}       		      
		    ]],
		    page: true,		   
		  });
	});
	$('#IPlibraryselectionshow').modal('show');
	return ;
});
//根据IP查询
$('#ifame_select_ip_submit').click(function(){ 
	var room_id = $('#room_id option:selected').val(); 
	var selip = $("[name='ifame_select_ip']").val();
	layui.use('table', function(){
		  var table = layui.table;	  
		  table.render({
			elem: '#iplist',
			url:'<?php echo Url::to(['member-pdt/ajax-iplist'])?>',
		    where: {room_id:room_id,ip:selip},
		    limit:"15",    		   
		    cols: [[
		      {type:'checkbox'}
		      ,{field:'id', width:'100',title: 'ID', sort: true}
		      ,{field:'ip', width:'150', title: 'IP地址'}
		      ,{field:'vlan', width:'80',title: 'Vlan', sort: true}
		      ,{field:'mask', width:'150', title: '掩码'}    		      
		      ,{field:'gateway', width:'150', title: '网关'} 
		      ,{field:'status', width:'100', title: '状态', sort: true,templet: '#statusTpl'}   	
		      ,{field:'remarks', title: '备注'}      		      
		    ]],
		    page: true,		   
		 });
	});  	
	//$('#IPlibraryselectionshow').modal('show');
	return ;
});
//选择好IP
$('#selectip').click(function(){
    var table = layui.table;
    var checkStatus = table.checkStatus('iplist');
    data = checkStatus.data;
    //layer.alert(JSON.stringify(data));
    var url = "<?php echo Url::to(['get-selectip']) ?>";
    var load = layer.load(2);
    //var oldIP= document.getElementById("have_ips").value;
    //var oldIP = $('input[name="havp_ip"]').value();
	$.post(url, {data:JSON.stringify(data),ips:""}, function(data){           	
		//document.getElementById("ips").value = data['data'];
		$.each(data['data'], function(key, val){
			$("#iplistji1").append('<div class="col-sm-4 control-label no-padding-right"><input value="'+val+'" type="text" readonly="readonly" name="have_ips[]"/><button type="button" style="margin-left: 4px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button><div>');
            
		});	        		
		layer.closeAll('loading');        	
	},'json');    
});

$("[name='room_id']").change(function(){
	var url = "<?php echo Url::to(['ajax-roomchange']) ?>";
	var id = $(this).find('option:selected').attr('rel');
	var servicerprovider = $("input[name='servicerprovider']:checked").val();
	var load = layer.load(2);
	$.post(url, {id:id,provider:servicerprovider}, function(data){    		
		$("[name='cabinet_id']").empty();
		$("[name='pdt_id']").empty();
		$("[name='cabinet_id']").append('<option value="">请选择机柜</option>');  			
		$.each(data['data']['Cabinet'], function(key, val){
			$("[name='cabinet_id']").append('<option value="'+val['id']+'" rel="'+val['id']+'">'+val['name']+'</option>');
		});	
		$("[name='pdt_id']").append('<option value="">请选择产品配置类别</option>');
		$.each(data['data']['Pdt'], function(key, val){
			$("[name='pdt_id']").append('<option value="'+val['id']+'" rel="'+val['id']+'">'+val['name']+'</option>');
		});	
		layer.closeAll('loading');
	},'json');
});
    
</script>    

<?php $this->endBlock(); ?>
