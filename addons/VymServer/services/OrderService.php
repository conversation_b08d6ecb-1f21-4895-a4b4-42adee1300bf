<?php

namespace addons\VymServer\services;

use addons\VymDesen\backend\models\Cart\TradeCartSupport;
use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\components\NewPipe\PipeLine;
use addons\VymDesen\common\models\PayOrder\PayorderGeneral;
use addons\VymDesen\common\models\PipeLine\PipelineTypes;
use addons\VymDesen\common\models\UserMember\UserMember;
use addons\VymServer\common\enums\OrderEnum;
use addons\VymServer\common\models\DsPaymentFlow;
use addons\VymServer\common\models\DsPayOrderDetail;
use addons\VymServer\common\models\DsPayOrderGeneral;
use addons\VymServer\common\models\DsPayOrderOriginal;
use addons\VymServer\common\models\DsPdtManage;
use addons\VymServer\common\models\Server;
use addons\VymServer\common\models\ServerOrder;
use addons\VymServer\common\models\ServerProduct;
use common\helpers\ArrayHelper;
use common\helpers\EchantsHelper;
use common\helpers\MemberHelper;
use common\helpers\StringHelper;
use ErrorException;
use Exception;
use Throwable;
use Yii;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * Class OrderService
 * 1. 业务订单的创建订单，获取支付链接
 * 2. 业务订单的完成支付，修改订单状态，并创建业务实例
 * 3. 业务订单的支付失败或超时，修改订单状态
 * 4. 业务订单的列表、查看、删除、退款，人工修改订单状态等基本功能
 */
class OrderService
{
    protected $time; // 添加时间属性

    /**
     *  生成物理服务器订单
     *
     * @param int $userId
     * @param     $params
     * @param     $extraData
     *
     * @return array
     * @throws \yii\db\Exception
     */
    public function createOrder(int $userId, $params, $extraData = [], $member = null): array
    {
        try {
            #开启事务
            $transaction = Yii::$app->db->beginTransaction();

            // 添加购物车
            $cartRe = Yii::$app->vymServerService->cart->add($userId, $params);
            if (!ArrayHelper::getValue($cartRe, 'status')) {
                throw new Exception(ArrayHelper::getValue($cartRe, 'msg'));
            }
            $cartData = ArrayHelper::getValue($cartRe, 'data');
            $cartId   = ArrayHelper::getValue($cartData, 'cart_id');

            //获取用户信息
            $user = self::getUserInfoByMid($userId);
            [$userName, $email] = MemberHelper::userInfo($user, $member);
            // 生成订单号和其他必要的ID
            $originalNo = DataHelper::createTradeno();
            $generalNo  = DataHelper::createPayorderGeneralID();
            $paymentNo  = DataHelper::createPaymentFlowNO();
            $time       = time();

            // 获取流程类型
            $PipelineTypesRes = PipelineTypes::findOne(['line_type_name' => '新购业务']);

            // 计算价格
            $orderPrice     = ArrayHelper::getValue($cartData, 'unit_price');            // 原价
            $totalMoney     = ArrayHelper::getValue($cartData, 'cart_price_total');      // 实付金额
            $discount_price = ArrayHelper::getValue($cartData, 'unit_price');            // 已优惠金额
            $use_price      = ArrayHelper::getValue($cartData, 'discount_price');        // 优惠金额、抹零金额

            // todo 处理优惠券（如果有）

            #创建主订单
            $dsOrderOriginalId = DsPayOrderOriginal::create($originalData = [
                'order_id'             => $originalNo,
                'order_type'           => '新购',
                'order_user_id'        => $user->u_id,
                'order_user_nickname'  => $userName,
                'order_user_email'     => $email,
                'order_admin_id'       => $user->admin_id,
                'order_admin_name'     => $user->admin_name,
                'order_original_price' => $orderPrice,
                'order_update_price'   => $totalMoney,
                'order_amount_money'   => $use_price,
                'order_update_remark'  => null,
                'order_update_status'  => '未锁定',
                'order_finish_pay'     => 0,
                'order_pay_status'     => '未支付',
                'order_remark'         => ArrayHelper::getValue($params, 'order_remark'),
                'order_status'         => $PipelineTypesRes && $PipelineTypesRes->line_first_trial == 'Y' ? '待审核' : '进行中',
                'order_time_create'    => $time,
            ]);
            if (!$dsOrderOriginalId) {
                throw new ErrorException('创建主订单失败');
            }

            #创建支付单
            $dsOrderGeneralId = DsPayOrderGeneral::create($generalData = [
                'general_payorder_title'       => '新购',
                'general_payorder_number'      => $paymentNo,
                'general_payorder'             => $generalNo,
                'general_original_order'       => $originalNo,
                'general_pay_money'            => $totalMoney,
                'general_pay_platform'         => null,
                'general_pay_channel'          => null,
                'general_pay_time'             => null,
                'general_pay_lock'             => '等待支付',
                'general_payinfo_review'       => '未审核',
                'general_payorder_type'        => 'original',
                'general_callback_stream'      => null,
                'general_command_adminid_list' => $user->admin_id,
                'general_payment_userid'       => $user->u_id,
                'general_create_time'          => $time,
            ]);
            if (!$dsOrderGeneralId) {
                throw new ErrorException('创建支付单失败');
            }

            // 准备订单详情数据（根据购物车商品创建）
            $dsOrderDetailIds = $detailData = [];
            for ($i = 0; $i < ArrayHelper::getValue($cartData, 'cart_num'); $i++) {
                $unionId    = DataHelper::createUnionid();
                $cartConfig = ArrayHelper::getValue($cartData, 'cart_config', []);
                // 赋值unionid
                ArrayHelper::setValue($cartConfig, 'unionid', $unionId);

                //创建订单详情
                $dsOrderDetailIds[$unionId] = DsPayOrderDetail::create($detailData[$unionId] = [
                    'detail_original_order' => $originalNo,
                    'detail_title'          => '新购产品',
                    'detail_price'          => ArrayHelper::getValue($cartData, 'cart_price_single'),
                    'detail_remark'         => ArrayHelper::getValue($cartData, 'cart_remark'),
                    'detail_content'        => json_encode($cartConfig, 320),
                    'params'                => json_encode(ArrayHelper::getValue($cartData, 'params'), 320),
                ]);
                if (!$dsOrderDetailIds[$unionId]) {
                    throw new ErrorException('创建订单详情失败');
                }

                // 把实例添加到Server
                $serverResult = Server::create([
                    'unionid' => $unionId, // 业务单号,
                    'user_id' => $user->u_id, // 会员ID,
                    'pdt_id'  => '', // Desen产品ID,
                    'mpdt_id' => '', // Desen业务ID,
                    'kf_id'   => '', // 客服ID,
                ]);
                if (!$serverResult) {
                    throw new ErrorException('创建服务器实例记录失败');
                }
            }

            // 准备付款单数据
            $sign = md5($originalNo . $time . uniqid());
            #新增付款单
            $dsPaymentFlowId = DsPaymentFlow::create($paymentFlowData = [
                'payment_flow_type'         => '订单支付',
                'payment_mode'              => '在线支付',
                'payment_flow_no'           => $paymentNo,
                'payment_payorders'         => $generalNo,
                'payable_amount'            => $orderPrice,
                'payment_amount'            => $totalMoney,
                'payment_credit_record_id'  => null,
                'payment_platform'          => '',
                'payment_method'            => '',
                'payment_trans_no'          => '',
                'payment_creattime'         => $time,
                'payment_settlement_status' => '未支付',
                'payment_successtime'       => null,
                'payment_callback_content'  => '',
                'payment_callbacktime'      => null,
                'admin_id'                  => $user->admin_id,
                'admin_name'                => $user->admin_name,
                'user_id'                   => $user->u_id,
                'user_name'                 => $userName,
                'user_email'                => $email,
                'link_effective_time'       => $time,
                'link_sign'                 => $sign,
            ]);
            if (!$dsPaymentFlowId) {
                throw new ErrorException('创建付款单失败');
            }

            // 创建商户订单 - 使用关联数据模式，通过关联关系动态获取完整数据
            $serverOrderId = ServerOrder::create($serverOrderData = [
                'uid'         => $user->u_id,
                'merchant_id' => Yii::$app->services->merchant->getId(),
                'store_id'    => Yii::$app->services->store->getId(),
                'goods_id'    => ArrayHelper::getValue($params, 'goods_id', 0),
                'product_id'  => ArrayHelper::getValue($params, 'product_id', 0),
                'remark'      => ArrayHelper::getValue($params, 'remark'),
                'original_id' => $dsOrderOriginalId,
                'original_no' => $originalNo,
                'general_id'  => $dsOrderGeneralId,
                'general_no'  => $generalNo,
                'payment_id'  => $dsPaymentFlowId,
                'payment_no'  => $paymentNo,
                'cart_data'   => $cartData,
            ]);
            if (!$serverOrderId) {
                throw new ErrorException('创建商户订单记录失败');
            }

            //创建工作任务
            $createPipe = PipeLine::Line_NewTask($originalNo);
            if (!ArrayHelper::getValue($createPipe, 'status')) {
                throw new ErrorException('创建工作任务失败：' . ArrayHelper::getValue($createPipe, 'info'));
            }
            $transaction->commit();
            //清除购物车
            TradeCartSupport::deleteAll(['cart_id' => $cartId]);
            return [
                'status'   => 1,
                'msg'      => '订单初始化完成',
                'order_sn' => $originalNo,
                'original' => ['params' => $originalData, 'result' => $dsOrderOriginalId],
                'general'  => ['params' => $generalData, 'result' => $dsOrderGeneralId],
                'detail'   => ['params' => $detailData, 'result' => $dsOrderDetailIds],
                'order'    => ['params' => $serverOrderData, 'result' => $serverOrderId],
                'payment'  => ['params' => $paymentFlowData, 'result' => $dsPaymentFlowId],
            ];
        } catch (Exception|Throwable $e) {
            isset($transaction) && $transaction->rollBack();
            return [
                'status' => 0,
                'msg'    => $e->getMessage(),
                'trace'  => $e->getTraceAsString(),
            ];
        }
    }


    /**
     * 续费物理服务器订单
     *
     * @param      $unionId
     * @param int  $renewNum
     * @param bool $flow 是否添加付款单
     *
     * @return array
     * @throws \yii\db\Exception
     */
    public function renewOrder($unionId, int $renewNum = 1, bool $flow = true): array
    {
        try {

            $ids_str     = implode('_', $unionId);
            $_this       = new self();
            $_this->time = time();

            $MemberPdtList = \addons\VymDesen\common\models\Member\MemberPdt::find()->select('*')->With('usermember')->With('useradmin')->where(['in', 'unionid', $unionId])->all();

            #验证是否可以续费
            $finishSubmitIP = [];
            foreach ($MemberPdtList as $key => $val) {
                $hasHistory = \addons\VymDesen\common\models\PayOrder\PayorderDetail::find()->joinwith('original')->where(['order_status' => '进行中'])->andWhere('JSON_CONTAINS(detail_content, JSON_OBJECT("unionid", "' . $val->unionid . '"))')->one();
                if ($hasHistory) {
                    #还有未完成订单
                    $detailConfig     = json_decode($hasHistory->detail_content, true);
                    $finishSubmitIP[] = ArrayHelper::getValue($detailConfig, 'ip.0');
                }
            }
            if ($finishSubmitIP) {
                return ["status" => 0, 'info' => '当前所选业务中包含(' . count($finishSubmitIP) . ")条记录已下单或正在进行其他业务中(变更配置, 更换机器)，需要等待之前订单彻底完成之后才可以发起后续订单\n\n若需要增加续费数请前往指定订单详情增加\n\n主IP分别为：\n" . implode("\n", $finishSubmitIP)];
            }

            $userMemberRenewArray = [];
            $unionidList          = [];
            #筛选出用户分组以及机器分组
            foreach ($MemberPdtList as $key => $val) {
                $hasRecord                     = false;
                $userPdtArray                  = [];
                $userPdtArray['config']        = json_decode($val['config'], true);
                $userPdtArray['ip']            = json_decode($val['ip'], true);
                $userPdtArray['unionid']       = $unionidList[] = $val['unionid'];
                $userPdtArray['payment_cycle'] = $val['payment_cycle'];
                $userPdtArray['sell_price']    = $val['sell_price'] * $renewNum;
                $userPdtArray['renew_num']     = $renewNum;
                $userPdtArray['end_time']      = $val['end_time'];

                foreach ($userMemberRenewArray as $k => $v) {
                    if ($val['user_id'] == $v['user_id']) {
                        $hasRecord                              = true;
                        $userMemberRenewArray[$k]['user_pdt'][] = $userPdtArray;
                    } else {
                        continue;
                    }
                }

                if (!$hasRecord) {
                    #新生成一个在用户数组里
                    $userMemberRenewArray[] = [
                        'user_id'    => ArrayHelper::getValue($val, 'user_id'),
                        'user_mail'  => ArrayHelper::getValue($val, 'user_name'),
                        'user_nick'  => ArrayHelper::getValue($val, 'usermember.0.uname'),
                        'admin_id'   => ArrayHelper::getValue($val, 'usermember.0.admin_id'),
                        'admin_name' => ArrayHelper::getValue($val, 'usermember.0.admin_name'),
                        'user_pdt'   => [
                            $userPdtArray,
                        ],
                    ];
                }
            }

            $originalData       = [];
            $detailData         = [];
            $generalData        = [];
            $flowData           = [];
            $PipelineTypesModel = new PipelineTypes();
            $PipelineTypesRes   = $PipelineTypesModel->find()->where(['line_type_name' => '续费业务'])->one();

            $orderList = [];

            foreach ($userMemberRenewArray as $key => $val) {

                $orderList[] = $mainOrder = DataHelper::createTradeno();
                $generalNo   = DataHelper::createPayorderGeneralID();
                $paymentNo   = DataHelper::createPaymentFlowNO();
                $totalMoney  = 0;

                foreach ($val['user_pdt'] as $k => $v) {
                    $totalMoney   += $v['sell_price'];
                    $detailData[] = [
                        'detail_original_order' => $mainOrder,
                        'detail_title'          => '产品续费',
                        'detail_content'        => json_encode($v, JSON_UNESCAPED_UNICODE),
                        'detail_price'          => $v['sell_price'],
                        'detail_remark'         => null,
                    ];
                }

                $originalData[] = [
                    'order_id'             => $mainOrder,
                    'order_type'           => '续费',
                    'order_user_id'        => $val['user_id'],
                    'order_user_nickname'  => $val['user_nick'],
                    'order_user_email'     => $val['user_mail'],
                    'order_admin_id'       => $val['admin_id'],
                    'order_admin_name'     => $val['admin_name'],
                    'order_original_price' => $totalMoney,
                    'order_update_price'   => $totalMoney,
                    'order_update_remark'  => null,
                    'order_update_status'  => '未锁定',
                    'order_amount_money'   => 0,
                    'order_finish_pay'     => 0,
                    'order_pay_status'     => '未支付',
                    'order_remark'         => null,
                    'order_status'         => $PipelineTypesRes->line_first_trial == 'Y' ? '待审核' : '进行中',
                    'order_time_create'    => $_this->time,
                ];

                $generalData[] = [
                    'general_payorder_title'       => '续费',
                    'general_payorder_number'      => $flow ? $paymentNo : '',
                    'general_payorder'             => $generalNo,
                    'general_original_order'       => $mainOrder,
                    'general_pay_money'            => $totalMoney,
                    'general_pay_platform'         => null,
                    'general_pay_channel'          => null,
                    'general_pay_time'             => null,
                    'general_pay_lock'             => '等待支付',
                    'general_payinfo_review'       => '未审核',
                    'general_payorder_type'        => 'original',
                    'general_callback_stream'      => null,
                    'general_command_adminid_list' => ArrayHelper::getValue($val, 'admin_id'),
                    'general_payment_userid'       => ArrayHelper::getValue($val, 'user_id'),
                    'general_create_time'          => $_this->time,
                ];


                #新增付款单
                if ($flow) {
                    $sign       = md5($mainOrder . $_this->time . uniqid());
                    $flowData[] = [
                        'payment_flow_type'         => '订单支付',
                        'payment_mode'              => '在线支付',
                        'payment_flow_no'           => $paymentNo, #这里其实未提交，但也还是生成一个
                        'payment_payorders'         => $generalNo,       #付款单集合
                        'payment_credit_record_id'  => $mainOrder,      #挂账单集合
                        'payable_amount'            => $totalMoney,     #应付金额
                        'payment_amount'            => $totalMoney,     #金额
                        'payment_platform'          => '',              #支付平台
                        'payment_method'            => '',              #支付方式
                        'payment_trans_no'          => '',              #三方交易号
                        'payment_creattime'         => $_this->time,    #创建时间
                        'payment_settlement_status' => '未支付',
                        'payment_successtime'       => null, #支付成功时间
                        'payment_callback_content'  => '',  #三方回调内容
                        'payment_callbacktime'      => null, #回调时间
                        'admin_id'                  => ArrayHelper::getValue($val, 'admin_id'),
                        'admin_name'                => ArrayHelper::getValue($val, 'admin_name'),
                        'user_id'                   => ArrayHelper::getValue($val, 'user_id'),
                        'user_name'                 => ArrayHelper::getValue($val, 'user_nick'),
                        'user_email'                => ArrayHelper::getValue($val, 'user_mail'),
                        'link_effective_time'       => $_this->time,
                        'link_sign'                 => $sign,
                    ];
                }
            }

            #开启事务
            $transaction    = Yii::$app->db->beginTransaction();
            $insertOriginal = Yii::$app->db->createCommand()->batchInsert(\addons\VymDesen\common\models\PayOrder\PayorderOriginal::tableName(), array_keys($originalData[0]), array_values($originalData))->execute();
            $insertDetail   = Yii::$app->db->createCommand()->batchInsert(\addons\VymDesen\common\models\PayOrder\PayorderDetail::tableName(), array_keys($detailData[0]), array_values($detailData))->execute();
            $insertGeneral  = Yii::$app->db->createCommand()->batchInsert(\addons\VymDesen\common\models\PayOrder\PayorderGeneral::tableName(), array_keys($generalData[0]), array_values($generalData))->execute();
            if ($flow)
                $insertFlow = Yii::$app->db->createCommand()->batchInsert(\addons\VymDesen\common\models\PayOrder\PaymentFlow::tableName(), array_keys($flowData[0]), array_values($flowData))->execute();

            if (count($unionidList) > 0) {
                $MemberPdtAppointOffModel = new \addons\VymDesen\common\models\Member\MemberPdtAppointOff();
                $offRecord                = $MemberPdtAppointOffModel->find()->where(["in", "appoint_unionid", $unionidList])->asArray()->all();
                $memberpdt_off_record     = $MemberPdtAppointOffModel->deleteAll(["appoint_unionid" => $unionidList]);

                if (count($offRecord) != $memberpdt_off_record) {
                    $transaction->rollBack();
                    return ["status" => 0, 'info' => '取消预约关机下架出现异常'];
                }
            }

            //获取是不是后台续费的操作
            $isCp = \addons\VymDesen\common\components\FuncHelper::GetCache("order_placing_method_is_cp_{$ids_str}");
            foreach ($orderList as $key => $val) {
                //后台下单存入cache,方便判断是否TG通知
                if ($isCp) {
                    \addons\VymDesen\common\components\FuncHelper::SetCache("order_placing_method_is_cp_{$val}", $val, 600);
                }
                $createPipe = PipeLine::Line_Renew($val);
                if (!$createPipe['status']) {
                    $transaction->rollBack();
                    return ["status" => 0, 'info' => $createPipe['info'], 'createPipe' => $createPipe];
                    //return ["status" => 0, 'info' => '加入工作任务出现异常,请联系管理员', 'createPipe' => $createPipe];
                }
            }

            if ($insertOriginal && $insertDetail && $insertGeneral) {
                $transaction->commit();
                return [
                    "status" => 1,
                    'info'   => '已完成下单，默认续费数为一个续费周期，若有其他需要请前往订单页修改',
                    'data'   => $flow ? (count($orderList) > 1 ? $orderList : $orderList[0]) : '',
                ];
            } else {
                $transaction->rollBack();
                return ["status" => 0, 'info' => '下单出现异常,请联系管理员'];
            }
        } catch (ErrorException $e) {
            return ["status" => 0, 'info' => $e->getMessage()];
        }
    }


    /**
     * 获取用户信息
     *
     * @param $userId
     *
     * @return UserMember
     * @throws ErrorException
     */
    public static function getUserInfo($userId)
    {
        $user = UserMember::findOne($userId);
        if (!$user)
            throw new ErrorException('用户不存在');
        if (!$user->admin_id || !$user->admin_name)
            throw new ErrorException('您暂未分配客服，请先联系客服');

        return $user;
    }

    /**
     * 通过mid获取用户信息
     *
     * @param mixed $u_id //usermember表的id
     */
    public static function getUserInfoByMid($u_id)
    {
        $user = UserMember::findOne(['u_id' => $u_id]);
        if (!$user)
            throw new ErrorException('用户不存在');
        if (!$user->admin_id || !$user->admin_name)
            throw new ErrorException('您暂未分配客服，请先联系客服');

        return $user;
    }

    /**
     * 购物车结算
     *
     * @param $cartId
     *
     * @return array
     * @throws ErrorException
     */
    public static function getTradeCartSupport($cartId)
    {
        $cartGoods = TradeCartSupport::findAll(['cart_id' => $cartId]);
        if (!$cartGoods)
            throw new ErrorException('错误的订单号');

        $orderPrice     = 0; #原价
        $totalMoney     = 0; #实付金额
        $discount_price = 0; #已优惠金额
        foreach ($cartGoods as $key => $val) {
            $orderPrice     += $val['unit_price'];
            $totalMoney     += $val['cart_price_total'];
            $discount_price += $val['discount_price'];
        }

        $total     = $totalMoney; #应付金额
        $use_price = 0;           #优惠金额、抹零金额
        return [
            'order_price' => $orderPrice,
            'total_money' => $totalMoney,
            'discount'    => $discount_price,
            'use_price'   => $use_price,
        ];
    }

    /**
     * 更新订单状态
     *
     * @param     $params
     *
     * @return array
     */
    public function updateStatus($params)
    {
        try {
            $order_no   = ArrayHelper::getValue($params, 'order_no');
            $status     = ArrayHelper::getValue($params, 'status');
            $payment_no = ArrayHelper::getValue($params, 'payment_no');
            $amount     = ArrayHelper::getValue($params, 'amount'); //商户返回的金额

            $order = $this->findByOrderNo($order_no);
            // 判断订单是否存在
            if (!$order) {
                throw new Exception('订单不存在');
            }
            // 判断订单金额是否匹配，如果金额不匹配，则需要人工介入
            if ($amount != $order->amount_pay) {
                throw new Exception('订单金额不匹配');
            }
            // 判断订单状态是否允许修改, 只有待支付和支付失败状态的订单才能修改，失败可能需要重新处理
            if (!in_array($order->order_st, [OrderEnum::ORDER_STATUS_PENDING, OrderEnum::ORDER_STATUS_FAILED])) {
                throw new Exception('订单当前状态不允许修改');
            }
            // 判断商户订单是否存在
            $checkOrder = $this->findByWhere(['payment_no' => $payment_no]);
            if ($checkOrder && $checkOrder->id != $order->id) {
                throw new Exception('支付订单与订单不匹配，请联系客服');
            }

            if ($status !== OrderEnum::PAYMENT_STATUS_PAID) {
                $order->order_st   = OrderEnum::ORDER_STATUS_FAILED;   // 失败
                $order->payment_st = OrderEnum::PAYMENT_STATUS_FAILED; // 失败
                $order->payment_at = StringHelper::intToDate(time());
                if (!$order->save()) {
                    throw new Exception('订单状态更新失败');
                }
                throw new Exception('订单支付失败，状态更新成功', 1);
            }

            $order->order_st   = OrderEnum::ORDER_STATUS_PAID; // 已支付
            $order->payment_st = OrderEnum::PAYMENT_STATUS_PAID;
            $order->payment_at = StringHelper::intToDate(time());
            $order->payment_no = $payment_no;
            if (!$order->save()) {
                throw new Exception('订单状态更新失败');
            }
            // todo 创建实例
            $serverRe = Yii::$app->vymServerService->server->createServer($order);
            if (ArrayHelper::getValue($serverRe, 'code') != 1) {
                throw new Exception('创建实例失败');
            }

            return ['code' => 1, 'msg' => '订单支付成功，创建实例中', 'data' => $order];
        } catch (Exception $e) {
            return ['code' => $e->getCode(), 'msg' => $e->getMessage()];
        }
    }

    /**
     * 删除订单
     *
     * @param int $id
     *
     * @return array
     */
    public function deleteOrder($id, $userMemberId)
    {
        try {
            /** @var DsPayOrderOriginal $order */
            $order = DsPayOrderOriginal::findOne(['original_id' => $id, 'order_user_id' => $userMemberId]);


            // 判断订单是否存在
            if (!$order) {
                throw new Exception('订单不存在');
            }

            return DsPayOrderOriginal::cancelOrderByOrderId($order->order_id);


        } catch (Exception $e) {
            return ['code' => $e->getCode(), 'msg' => $e->getMessage()];
        }
    }

    /**
     * 成功的订单，退款订单，用户余额
     *
     * @param int $id
     *
     * @return ServerOrder
     */
    public function refundOrder($id)
    {
        try {
            $order = $this->findById($id);
            // 判断订单是否存在
            if (!$order) {
                throw new Exception('订单不存在');
            }
            // 判断订单状态是否允许退款，只有已支付和已完成状态的订单才能退款
            if (!in_array($order->order_st, [OrderEnum::ORDER_STATUS_PAID, OrderEnum::ORDER_STATUS_COMPLETED])) {
                throw new Exception('订单当前状态不允许退款');
            }

            // 判断订单是否已退款
            if ($order->order_st == OrderEnum::ORDER_STATUS_REFUND) {
                throw new Exception('订单已退款，不允许再退款');
            }

            // 退款
            $order->order_st = OrderEnum::ORDER_STATUS_REFUND;
            if (!$order->save()) {
                throw new Exception('订单退款失败');
            }

            // todo 退款成功，更新用户余额
            $user = \addons\VymDesen\common\models\UserMember\UserMember::findOne(['u_id' => $order->uid]);
            if ($user) {
                $user->balance += $order->amount_pay;
                if (!$user->save()) {
                    throw new Exception('用户余额更新失败');
                }
            }

            return ['code' => 1, 'msg' => '订单退款成功', 'data' => $order];
        } catch (Exception $e) {
            return ['code' => $e->getCode(), 'msg' => $e->getMessage()];
        }
    }

    /**
     * 根据订单ID查询订单
     *
     * @param mixed $id
     */
    public function getFlowOrderNoById($id)
    {
        $order = DsPayOrderOriginal::find()->where(['original_id' => $id])->with('general')->one();
        if (!$order) {
            return null;
        }
        $general = $order->general;
        return $general->general_payorder_number;
    }

    public function getOrderDetail($orderNo)
    {
        try {
            date_default_timezone_set('PRC');
            //支付单
            $generalOrder  = DsPayOrderGeneral::find()->where(['general_payorder_number' => $orderNo])->asArray()->one();
            $originalNo    = ArrayHelper::getValue($generalOrder, 'general_original_order');  //主单号
            $generalNo     = ArrayHelper::getValue($generalOrder, 'general_payorder');        //支付单号
            $paymentFlowNo = ArrayHelper::getValue($generalOrder, 'general_payorder_number'); //付款单号
            if (!$generalOrder)
                throw new Exception('订单不存在(001)');

            //主订单
            $originalOrder = DsPayOrderOriginal::find()->where(['order_id' => $originalNo])
                                               ->with([
                                                   'serverOrder' => function ($query) {
                                                       $query->with(['product']);
                                                   },
                                               ])
                                               ->asArray()->one();
            if (!$originalOrder)
                throw new Exception('订单不存在(002)');
            $productName       = ArrayHelper::getValue($originalOrder, 'serverOrder.product.name');
            $generalDetailList = DsPayOrderDetail::find()->where(['detail_original_order' => $originalNo])->asArray()->all();
            if (!$generalDetailList)
                throw new Exception('订单不存在(003)');
            // 处理JSON字段
            $generalDetailList = ArrayHelper::fieldJsonToArray($generalDetailList, ['detail_content', 'params',]);

            //付款单
            $payOrder         = DsPaymentFlow::find()->where(['payment_flow_no' => $orderNo])->asArray()->one();
            $paySt            = ArrayHelper::getValue($payOrder, 'payment_settlement_status');   //订单状态
            $payAmount        = ArrayHelper::getValue($payOrder, 'payable_amount');              //支付金额
            $payEffectiveTime = ArrayHelper::getValue($payOrder, 'link_effective_time');         //可支付时间
            if (!$payOrder)
                throw new Exception('订单不存在(004)');
            //如果已经支付
            if ($paySt == '已支付') {
                throw new Exception('订单已支付，请勿重复支付(005)', 2);
            }
            //如果已经取消
            if ($paySt == '取消支付') {
                throw new Exception('订单已取消，请重新下单(006)');
            }
            //判断支付链接是否在有效期内
            // if ($payEffectiveTime < time()) {
            //     throw new Exception('订单已过期(007)');
            // }
            $dataRe = [
                'code'              => 1,
                'msg'               => '请在有效期内支付！',
                // 'productName' => ArrayHelper::getValue($originalOrder, 'order_type') . '产品服务',
                'productName'       => $productName,
                'originalNo'        => $originalNo,
                'generalNo'         => $generalNo,
                'paymentFlowNo'     => $paymentFlowNo,
                'payAmount'         => $payAmount,
                'payOrder'          => $payOrder,
                'originalOrder'     => $originalOrder,
                'generalOrder'      => $generalOrder,
                'generalDetailList' => $generalDetailList,
            ];
        } catch (Exception $e) {
            $dataRe = [
                'code' => $e->getCode(),
                'msg'  => $e->getMessage(),
            ];
        }
        return $dataRe;
    }

    /**
     * 根据ID获取订单
     *
     * @param $id
     * @param $is_array
     *
     * @return array|ActiveRecord|null
     */
    public function findById($id, $is_array = false)
    {
        $query = ServerOrder::find()->where(['id' => $id]);
        if ($is_array) {
            return $query->asArray()->one();
        }
        return $query->one();
    }


    /**
     * 根据订单号获取订单
     *
     * @param $order_no
     * @param $is_array
     *
     * @return array|ActiveRecord|null
     */
    public function findByOrderNo($order_no, $is_array = false)
    {
        $query = ServerOrder::find()->where(['order_no' => $order_no]);
        if ($is_array) {
            return $query->asArray()->one();
        }
        return $query->one();
    }

    /**
     * 根据条件获取订单
     *
     * @param $where
     * @param $is_array
     *
     * @return array|ActiveRecord|null
     */
    public function findByWhere($where, $is_array = false)
    {
        $query = ServerOrder::find()->where($where);
        if ($is_array) {
            return $query->asArray()->one();
        }
        return $query->one();
    }

    /**
     * 获取所有订单 - 使用关联数据模式
     *
     * @param array  $where
     * @param mixed  $select
     * @param string $orderBy
     * @param int    $limit
     * @param int    $offset
     *
     * @return ActiveQuery
     */
    public function findAll($where = [], $select = '*', $orderBy = 'original_id desc', $limit = 10, $offset = 0, $with = [])
    {

        return DsPayOrderOriginal::find()
                                 ->with(array_merge(['detail', 'serverOrder', 'general'], $with))
                                 ->where($where)
                                 ->select($select)
                                 ->orderBy($orderBy)
                                 ->limit($limit)
                                 ->offset($offset);

        //        return ServerOrder::find()
        //                          ->with([
        //                              'original',    // 主订单关联
        //                              'general',     // 付款单关联
        //                              'goods',     // 商户产品关联
        //                              'product',   // DS产品关联
        //                              'merchant',     // 商户关联
        //                          ])
        //                          ->select($select)
        //                          ->where($where)
        //                          ->orderBy($orderBy)
        //                          ->limit($limit)
        //                          ->offset($offset)
        //                          ->asArray()
        //                          ->all();
    }

    public function count($where)
    {
        return DsPayOrderOriginal::find()->where($where)->count();
    }


    /**
     * @param $id
     *
     * @return ServerOrder|array|ActiveRecord|\yii\db\T|null
     */
    public function getOriginalOrder($where, $is_array = true, $with = [])
    {
        $query = DsPayOrderOriginal::find()->with(array_merge(['detail', 'serverOrder', 'general'], $with))->where($where);

        if ($is_array) {
            return $query->asArray()->one();
        }
        return $query->one();

    }

    /**
     * 获取订单详情 - 使用关联数据模式
     *
     * @param int  $id       订单ID
     * @param bool $is_array 是否返回数组格式
     *
     * @return array|ServerOrder|null
     */
    public function getOrderDetailById($id, $is_array = true)
    {
        $query = ServerOrder::find()
                            ->with([
                                'original',    // 主订单
                                'general',     // 付款单
                                'goods',     // 商户产品
                                'product',   // DS产品
                                'merchant',     // 商户信息
                            ])
                            ->where(['id' => $id]);

        if ($is_array) {
            return $query->asArray()->one();
        }
        return $query->one();
    }

    /**
     * @param DsPayOrderOriginal|array $order
     *
     * @return array
     */
    public function getOrderInfo($order, $isRemoveWith = true)
    {
        $result = $order;
        if (is_object($result)) {
            $result = $result->toArray();
        }
        $detail = ArrayHelper::getValue($result, 'detail');

        $content = ArrayHelper::getValue($detail, 'detail_content');
        if (!is_array($content)) {
            $content                  = json_decode($content, true);
            $result['detail_content'] = $content;
        }

        $result['config_names'] = Yii::$app->vymServerService->order->getConfig(ArrayHelper::getValue($content, 'config', []));

        //新单有serverOrder可取出
        $serverOrder = ArrayHelper::getValue($result, 'serverOrder');
        if ($serverOrder) {
            $cartData = ArrayHelper::getValue($serverOrder, 'cart_data', []);
            if (!is_array($cartData)) {
                $cartData            = json_decode($cartData, true);
                $result['cart_data'] = $cartData;
            }
        }

        $goods_id = 0;
        $params   = ArrayHelper::getValue($detail, 'params');
        if (!is_array($params)) {
            $params           = json_decode($params, true);
            $product_id       = ArrayHelper::getValue($params, 'product_id');
            $goods_id         = ArrayHelper::getValue($params, 'goods_id');
            $result['params'] = $params;
        }

        $goods = null;

        if ($goods_id) {
            $goods = ServerProduct::find()->where(['id' => $goods_id])->asArray()->one();
        }
        if ($goods) {
            $result['goods_info'] = [
                'price_month'   => ArrayHelper::getValue($goods, 'price_month'),
                'price_quarter' => ArrayHelper::getValue($goods, 'price_quarter'),
                'price_half'    => ArrayHelper::getValue($goods, 'price_half'),
                'price_year'    => ArrayHelper::getValue($goods, 'price_year'),
            ];
        }

        // 从付款单获取支付信息
        $generalOrder = ArrayHelper::getValue($order, 'general', []);
        if ($generalOrder) {
            $result['payment_info'] = [
                'general_payorder_number' => ArrayHelper::getValue($generalOrder, 'general_payorder_number'),
                'general_pay_money'       => ArrayHelper::getValue($generalOrder, 'general_pay_money'),
                'general_pay_lock'        => ArrayHelper::getValue($generalOrder, 'general_pay_lock'),
                'general_payinfo_review'  => ArrayHelper::getValue($generalOrder, 'general_payinfo_review'),
                'general_pay_time'        => ArrayHelper::getValue($generalOrder, 'general_pay_time'),
            ];
        }

        if ($isRemoveWith) {
            unset($result['original'], $result['general'], $result['detail']);
        }
        return $result;
    }

    /**
     * 获取订单的完整信息 - 动态计算价格和配置
     *
     * @param ServerOrder|array $order        订单对象或数组
     * @param bool              $isRemoveWith 处理后移除关联数据
     *
     * @return array
     * @throws Exception
     */
    public function getOrderFullInfo($order, $isRemoveWith = false)
    {
        $result = $order;
        if (is_object($result)) {
            $result = $result->toArray();
        }

        $cartData = ArrayHelper::getValue($result, 'cart_data', []);
        if (!is_array($cartData)) {
            $cartData            = json_decode($cartData, true);
            $result['cart_data'] = $cartData;
        }
        $result['config_names'] = Yii::$app->vymServerService->order->getConfigDisplayNames($cartData);

        // 从关联的DS产品获取最新产品信息
        $product = ArrayHelper::getValue($order, 'product', []);
        if ($product) {
            if (is_object($product)) {
                $product = $product->toArray();
            }


            // 处理JSON字段
            $jsonFields = ['cpu', 'ram', 'hdd', 'bandwidth', 'ipnumber', 'defense', 'operatsystem', 'card'];
            $product    = ArrayHelper::fieldJsonToArray($product, $jsonFields);
            // 返回指定的字段
            $result['product_info'] = [
                'name'         => ArrayHelper::getValue($product, 'name'),
                'cpu'          => ArrayHelper::getValue($product, 'cpu'),
                'ram'          => ArrayHelper::getValue($product, 'ram'),
                'hdd'          => ArrayHelper::getValue($product, 'hdd'),
                'bandwidth'    => ArrayHelper::getValue($product, 'bandwidth'),
                'ipnumber'     => ArrayHelper::getValue($product, 'ipnumber'),
                'operatsystem' => ArrayHelper::getValue($product, 'operatsystem'),
                'defense'      => ArrayHelper::getValue($product, 'defense'),
                'card'         => ArrayHelper::getValue($product, 'card'),
            ];
        }

        // 从关联的商户产品获取价格信息
        $merchantProduct = ArrayHelper::getValue($order, 'goods', []);
        if ($merchantProduct) {
            if (is_object($merchantProduct)) {
                $merchantProduct = $merchantProduct->toArray();
            }

            $result['goods_info'] = [
                'price_month'   => ArrayHelper::getValue($merchantProduct, 'price_month'),
                'price_quarter' => ArrayHelper::getValue($merchantProduct, 'price_quarter'),
                'price_half'    => ArrayHelper::getValue($merchantProduct, 'price_half'),
                'price_year'    => ArrayHelper::getValue($merchantProduct, 'price_year'),
            ];
        }

        // 从主订单获取订单状态和金额信息
        $originalOrder = ArrayHelper::getValue($order, 'original', []);
        if ($originalOrder) {
            if (is_object($originalOrder)) {
                $originalOrder = $originalOrder->toArray();
            }

            $result['order_info'] = [
                'order_id'             => ArrayHelper::getValue($originalOrder, 'order_id'),
                'order_type'           => ArrayHelper::getValue($originalOrder, 'order_type'),
                'order_status'         => ArrayHelper::getValue($originalOrder, 'order_status'),
                'order_pay_status'     => ArrayHelper::getValue($originalOrder, 'order_pay_status'),
                'order_original_price' => ArrayHelper::getValue($originalOrder, 'order_original_price'),
                'order_update_price'   => ArrayHelper::getValue($originalOrder, 'order_update_price'),
                'order_amount_money'   => ArrayHelper::getValue($originalOrder, 'order_amount_money'),
                'order_time_create'    => ArrayHelper::getValue($originalOrder, 'order_time_create'),
            ];
        }

        // 从付款单获取支付信息
        $generalOrder = ArrayHelper::getValue($order, 'general', []);
        if ($generalOrder) {
            if (is_object($generalOrder)) {
                $generalOrder = $generalOrder->toArray();
            }

            $result['payment_info'] = [
                'general_payorder_number' => ArrayHelper::getValue($generalOrder, 'general_payorder_number'),
                'general_pay_money'       => ArrayHelper::getValue($generalOrder, 'general_pay_money'),
                'general_pay_lock'        => ArrayHelper::getValue($generalOrder, 'general_pay_lock'),
                'general_payinfo_review'  => ArrayHelper::getValue($generalOrder, 'general_payinfo_review'),
                'general_pay_time'        => ArrayHelper::getValue($generalOrder, 'general_pay_time'),
            ];
        }

        if ($isRemoveWith) {
            unset($result['original'], $result['general'], $result['goods'], $result['product'], $result['merchant']);
        }
        return $result;
    }

    /**
     * 根据购物车数据获取配置显示名称
     *
     * @param array $cartData 购物车数据
     *
     * @return array
     * @throws Exception
     */
    public function getConfigDisplayNames(&$cartData)
    {
        //        $configNames = [];
        if (!is_array($cartData)) {
            $cartData = json_decode($cartData, true);
        }

        return $this->getConfig(ArrayHelper::getValue($cartData, 'cart_config.config'));
        //  $configFields = ['cpu', 'ram', 'hdd', 'bandwidth', 'ipnumber', 'operatsystem', 'defense', 'card'];

        //        foreach ($configFields as $field) {
        //            $configNames[$field] = ArrayHelper::getValue($cartData, 'cart_config.config.' . $field);
        //        }
        //        return $configNames;
    }

    public function getConfig($config)
    {
        $configNames  = [];
        $configFields = ['cpu', 'ram', 'hdd', 'bandwidth', 'ipnumber', 'operatsystem', 'defense', 'card'];

        foreach ($configFields as $field) {
            $configNames[$field] = ArrayHelper::getValue($config, $field);
        }
        return $configNames;
    }

    /**
     * 根据id获取原始订单
     *
     * @param mixed $id
     */
    public function getOriginalOrderById($id)
    {
        $result = ServerOrder::find()->where(['id' => $id])->with(['original'])->asArray()->one();
        return ArrayHelper::getValue($result, 'original');
    }

    public function getStatistics($type)
    {
        $fields = [
            'count' => '下单笔数',
            'totalAmount' => '下单金额(万)',
            'payCount' => '支付笔数',
            'payTotalAmount' => '支付金额(万)',
        ];
        list($time, $format) = EchantsHelper::getFormatTime($type);
        return EchantsHelper::lineOrBarInTime(function ($start_time, $end_time, $formatting) {
            $allOrders = ServerOrder::find()->select(['count(id) as count', 'round(sum(general_pay_money)/10000,4)  as totalAmount', "DATE_FORMAT(created_at, '$formatting') as time"])
                ->andWhere(['between', 'created_at', date('Y-m-d H:i:s', $start_time), date('Y-m-d H:i:s', $end_time)])
                ->andWhere(['merchant_id' => Yii::$app->services->merchant->getNotNullId()])
                ->groupBy(['time'])
                ->innerJoinWith("general")->asArray()->all();

            $paidOrders = ServerOrder::find()->select(['count(id) as count', 'round(sum(general_pay_money)/10000,4) as totalAmount', "DATE_FORMAT(created_at, '$formatting') as time"])
                ->andWhere(['between', 'created_at', date('Y-m-d H:i:s', $start_time), date('Y-m-d H:i:s', $end_time)])
                ->andWhere(['merchant_id' => Yii::$app->services->merchant->getNotNullId()])
                ->andWhere(['general_pay_lock' => "支付完成"])
                ->groupBy(['time'])
                ->innerJoinWith("general")->asArray()->all();

            // 将支付完成的订单数据合并到所有订单数据中
            $paidOrdersMap = [];
            foreach ($paidOrders as $paidOrder) {
                $paidOrdersMap[$paidOrder['time']] = [
                    'payCount' => $paidOrder['count'],
                    'payTotalAmount' => $paidOrder['totalAmount'],
                ];
            }

            foreach ($allOrders as &$order) {
                if (isset($paidOrdersMap[$order['time']])) {
                    $order['payCount'] = $paidOrdersMap[$order['time']]['payCount'];
                    $order['payTotalAmount'] = $paidOrdersMap[$order['time']]['payTotalAmount'];
                } else {
                    $order['payCount'] = 0;
                    $order['payTotalAmount'] = 0;
                }
            }

            return $allOrders;


        }, $fields, $time, $format);
    }
}
