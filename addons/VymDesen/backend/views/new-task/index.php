<?php

$this->title = '控制台管理-新购业务下单';
use yii\helpers\Url;
use yii\helpers\Html;
use addons\VymDesen\common\components\DataHelper;
?>
<?php $node = \Yii::$app->session['auth']['node'];?>
<?php $this->beginBlock('content') ?>

<style type="text/css">
	*{margin:0;padding:0;outline:none;}
	ul,li{list-style:none;margin:0;padding:0;}
	a{text-decoration:none;}
	a:hover{text-decoration:none;}
	.clearfix{clear:both;}
	
	.info-title{height:40px;width:100%;line-height:50px;font-weight:bold;border-bottom:2px solid rgba(232,233,234,1);margin-bottom:10px;}
	.info-box{margin:40px 0px;padding:0 20px;margin-top:30px;}
	.info-box .customertable{border:none;width:700px;}
	.info-box .customertable tr{border:none;}
	.info-box .customertable tr td{border:none;padding:4px;}
	.info-box .customertable tr td:nth-child(odd){width:100px;text-align:right;}
	
	.table tr td input{height:30px;line-height:30px;}
	.table tr td .fact_price{width:80px;}
	.table tr td .trade_num{width:40px;}
	
	.updatebox{margin-bottom:10px;}
	.updatebox .total-box{float:left;display:inline-block;margin-right:30px;}
	.updatebox .total-box div{float:left;height:35px;line-height:35px;display:block;}
	.updatebox .total-box input{float:left;height:35px;line-height:35px;}
	.updatebox .total-box .tips{color:#f00;width:100%;}
	.main_price_total, .main_price_fact, .main_pay_money, .main_pay_remark{margin-right:20px;font-size:20px;line-height:30px!important;}
	.main_price_total{color:#2b7dbc;}
	.main_price_fact{color:rgba(221, 55, 81, 1)}
	.main_price_fact button{margin-left:10px;}
	
	.float_table{border:none;}
	.float_table tr td{height:40px;line-height:40px!important;}
	.float_table tr td:first-child{text-align:right;width:150px;}
	.float_table tr td input,textarea{width:180px;}
	.float_table tr td select{width:300px;}
	.float_table tr td textarea{line-height:24px;}
	
	.dobox{float:left;padding:8px 15px;color:#fff;cursor:pointer;margin-right:20px;}
	.do-paylater{background:#428bca;}
	.do-payunderline{background:#82af6f;}
	
	.editbox a{float:right;margin-bottom:10px;}
	

	.changeConfigBox{width:400px;height:30px;line-height:30px;}
	.changeConfigBox .title{width:70px;float:left;text-align:center;height:30px;line-height:30px;border-right:1px #ccc solid;}
	.changeConfigBox .content{width:320px;float:left;text-align:left;height:30px;line-height:30px;}
	.changeConfigBox .content .front_content{width:140px;height:30px;line-height:30px;float:left;padding:0 5px;border-right:1px #ccc solid;}
	.changeConfigBox .content .after_content{width:140px;height:30px;line-height:30px;float:left;padding:0 5px;}
	.changeConfigBox .content .small{width:100px!important;}
	.changeConfigBox .smallcontent{width:240px!important;}
	.smallconfigbox{width:320px!important}
	.renewBox{float:left;height:30px;width:30px;display:inline-block;text-align:center;line-height:30px;}
	.minusRenew{border:1px #ccc solid;cursor:pointer;font-size:30px;line-height:28px;}
	.plusRenew{border:1px #ccc solid;cursor:pointer;font-size:26px;}
	
	/* 下单部分样式 */
	
	#trade-content{min-height:500px;}
	.top1{background:#343434!important;}
	.top2{background:#000!important;}
	.tk{height:140px;}
	.content-box{background:#fff;border:1px #ddd solid;width:100%;margin-top:20px;border-radius:6px;-webkit-border-radius:6px;-moz-border-radius:6px;box-shadow:0 5px 5px #ebecec;-webkit-box-shadow:0 5px 5px #ebecec;-moz-box-shadow:0 5px 5px #ebecec;padding:20px 20px;}
	.content-innerbox{width:100%;margin-bottom:20px;display:inline-block;}
	.content-box .content-title{width:100px;padding:0 20px;float:left;display:inline-block;height:34px;line-height:34px;font-size:14px;text-align:left;}
	.content-box .content-content ul{margin-left:20px;max-width:1000px;display:inline-block;width:100%;}
	.content-box .content-content ul li{display:inline-block;min-width:100px;height:34px;text-align:center;line-height:34px;margin-right:5px;margin-bottom:10px;padding:0 5px;}
	.content-box .content-content ul li a{display:block;width:100%;height:100%;text-align:center;line-height:34px;border:1px #ccc solid;padding:0 5px;font-size:12px;color:#333;}
	.content-box .content-content ul li a:hover{border:1px #00a0e9 solid; color:#00a0e9;}
	.content-box .content-content ul .active a{color:#fff;background:#00a0e9;border:1px #00a0e9 solid;}
	.content-box .content-content ul .active a:hover{color:#fff;background:#00a0e9;border:1px #00a0e9 solid;}
	.content-box .content-select{display:inline-block;float:left;border:1px #ccc solid;padding:0 10px;font-family:"Microsoft Yahei"}
	.paybox{height:34px;width:100%;}
	.settimetitle{height:34px;line-height:34px;padding:0 10px;float:left;display:inline-block;}
	.setselect{height:34px;line-height:34px;text-align:center;padding-left:20px; padding-right:13px;border:1px #ccc solid;font-family:"Microsoft Yahei";float:left;display:inline-block;border-color:#ff8a00;}
	.autopay{height:34px;line-height:34px;padding-left:20px;display:inline-block;float:right;margin-right:70px;}
	.autopay input[type=checkbox]{width:16px;height:16px;background:none;border:none;float:left;display:inline-block;margin-top:10px;}
	#paybtn, #addCart{padding:0 40px;line-height:40px;text-align:center;background:#00a0e9;color:#fff;cursor:pointer;float:right;display:inline-block;}
	#addCart{margin-right:20px;background:#ff8a00;}
	.paymoney{float:left;display:inline-block;padding:0 20px;text-align:center;line-height:34px;height:34px;color:#ff8a00;font-size:16px;margin-left:30px;}
	.paymoney .moneybox{font-size:24px;}
	.paymain{position:fixed;bottom:0px;left:0px;background:#fff;z-index:99997;width:100%;padding-left:0px;padding-right:0px;border-top:2px #e5e5e5 solid;}
	.content-width{width:1050px;height:auto;margin:0 auto;}
	.content-width-float{width:1240px!important;}
	
	.buyNum{float:left;height:34px;line-height:34px;text-align:center;}
	.buyNum div{float:left;height:34px;}
	.buyNum .numtxt{margin-left:20px;}
	.buyNum .plusNum{width:34px;color:#fff;background:#ff8a00;font-size:24px;cursor:pointer;margin-right:5px;}
	.buyNum .buyNum_input input{float:left;width:34px;height:34px;display:block;text-align:center;margin-right:5px;border-color:#ff8a00;}
	.buyNum .minuesNum{width:34px;color:#fff;background:#ff8a00;font-size:24px;cursor:pointer;}
	.buyNum .minuesNum a, .buyNum .plusNum a{color:#fff;display:block;width:100%;height:100%;}

	.userinfo{margin-bottom:20px;height:34px;line-height:34px;}
	
	/* 购物车样式 */
	#cartBoxSideBar{position:fixed;right:0px;top:300px;z-index:99999;cursor:pointer;border:1px #ff8a00 solid;width:25px;height:160px;font-size:16px;padding:20px 10px;text-align:center;line-height:25px;border-top-left-radius:20px;border-bottom-left-radius:20px;background:#FF6633;color:#fff;}
	#cartBoxSideBar .carticon{display:block;width:28px;height:28px;float:left;margin-bottom:5px;margin-top:10px;}
	#cartBoxSideBar .carticon img{width:100%;display:block;margin-left:-1px;}
	#cartBoxSideBar .cart_num{width:25px;height:25px;float:left;margin-top:5px;border-radius:50%;background:#fff;color:#FF6633;text-align:center;line-height:25px;margin-left:2px;}
	#cartBoxSideBar .cart_text{float:left;margin-left:2px;margin-top:5px;margin-bottom:5px;}
	
	#shadeBox{width:100%;height:100%;position:absolute;top:0px;left:0px;z-index:99998;background:#000;opacity:0;display:none;}
	#cartViewBox{width:1200px;height:800px;background:#fff;z-index:999999;opacity:0;display:none;position:absolute;top:-900px;left:200px;}
	#cartViewBox .viewbox_title{margin:0 60px;height:60px;border-bottom:2px solid rgba(232,233,234,1);font-size:24px;font-weight:bold;margin-top:40px;}
	#cartViewBox .viewbox_title div{float:left;}
	#cartViewBox .viewbox_title .viewbox_title_txt{float:left;margin-top:24px;}
	#cartViewBox .viewbox_title .closeCartView{float:right;width:60px;height:60px;text-align:center;line-height:80px;font-size:32px;}
	#cartViewBox .viewbox_title .closeCartView a{display:block;width:100%;height:100%;}
	#cartViewBox .viewbox_title .closeCartView a img{width:100%;}
	
	#cartViewBox .viewbox_content{height:500px;overflow-y:scroll;}
	#cartViewBox .viewbox_content .cartlistbox{border-bottom:1px solid rgba(232,233,234,1);margin:0 60px;padding:0 30px;margin-top:30px;position:relative;}
	#cartViewBox .viewbox_content .cartlistbox:last-child{border-bottom:none;}
	#cartViewBox .viewbox_content .cartlistbox .deleteFromCart{position:absolute;right:0px;bottom:5px;width:20px;float:left;}
	#cartViewBox .viewbox_content .cartlistbox .deleteFromCart img{vertical-align:middle;width:20px;display:block;}
	#cartViewBox .viewbox_content .cartlistbox .selectCartPro{position:absolute;left:0px;top:70px;width:20px;height:20px;float:left;}
	#cartViewBox .viewbox_content .cartlistbox .selectCartPro input{width:20px;height:20px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistrow{height:24px;line-height:24px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistrow div{float:left;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistrow .cartlist-rowtitle{text-align:right;padding:0 10px;min-width:90px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistrow .cartlist-rowcontent{text-align:left;padding:0 10px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistedit{position:absolute;left:550px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistedit .cartlistedit-title{float:left;height:30px;line-height:30px;margin-right:20px;text-align:right;width:60px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistedit .cartlistedit-content{float:left;height:30px;line-height:30px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistcycle{top:0px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistcycle select{border:1px #ff8a00 solid;height:30px;padding:0 20px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistnum{top:0px;left:800px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistnum .cartlistedit-content div{float:left;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistnum .cartlistedit-content .cartnumPlus{width:30px;height:30px;line-height:30px;text-align:center;background:#ff8a00;margin-right:10px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistnum .cartlistedit-content .cartnumPlus a{display:block;width:100%;height:100%;color:#fff;font-size:18px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistnum .cartlistedit-content .cartnum input{width:30px;height:30px;text-align:center;line-height:30px;margin-right:10px;padding:0px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistnum .cartlistedit-content .cartnumMinues{width:30px;height:30px;line-height:30px;text-align:center;background:#ff8a00;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistnum .cartlistedit-content .cartnumMinues a{display:block;width:100%;height:100%;color:#fff;font-size:18px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlistprice{top:50px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlisttotalprice{top:100px;}
	#cartViewBox .viewbox_content .cartlistbox .cartlisttotalprice .cartlistedit-content{font-size:20px;color:#EE4000;font-weight:bold;}
	
	#cartViewBox .setAccountsBox{margin:0 60px;margin-top:20px;border-top:2px solid rgba(232,233,234,1);padding-top:20px;}
	#cartViewBox .setAccountsBox .selectAllCart{float:left;height:40px;}
	#cartViewBox .setAccountsBox .selectAllCart input{width:20px;height:20px;display:block;float:left;margin-right:10px;margin-top:10px;}
	#cartViewBox .setAccountsBox .selectAllCart span{font-size:16px;float:left;height:40px;line-height:40px;margin-right:20px;}
	#cartViewBox .setAccountsBox .selectAllCart .deleteSelect{padding:0 15px;height:40px;display:block;float:left;line-height:40px;text-align:center;background:#CC3300;color:#fff;}
	#cartViewBox .setAccountsBox .setAccount{float:right;height:40px;font-size:16px;}
	#cartViewBox .setAccountsBox .setAccount .allPrice{float:left;height:40px;margin-right:20px;line-height:40px;}
	#cartViewBox .setAccountsBox .setAccount .allPrice span{color:#EE4000;font-size:24px!important;}
	#cartViewBox .setAccountsBox .setAccount .allPrice span:first-child{margin-left:10px;}
	#cartViewBox .setAccountsBox .setAccount .allPrice span:last-child{font-weight:bold;}
	#cartViewBox .setAccountsBox .setAccount .gotoPay{float:left;height:40px;}
	#cartViewBox .setAccountsBox .setAccount .gotoPay a{display:block;width:100%;height:100%;background:#EE4000;color:#fff;text-align:center;line-height:40px;padding:0 10px;}
	
</style>

<div id="shadeBox"></div>
<div id="cartViewBox">
	<div class="viewbox_title">
		<div class="viewbox_title_txt">购物车清单 / Shopping Cart List</div>
		<div class="closeCartView"><a href="javascript:;"><img src="/cart/closeCart.png"></a></div>
	</div>
	<div class="viewbox_content">
		
	</div>
	<div class="setAccountsBox">
		<div class="selectAllCart">
			<input type="checkbox" name="allcart" id="chooseAll" value=""><span>全选</span>
			<?php if(in_array("new-task/del-cart", $node) || $this->params['is_administrator_user']):?>
			<a href="javascript:;" class="deleteSelect">删除选中项</a>
			<?php endif;?>
		</div>
		<div class="setAccount">
			<div class="allPrice">总计金额<span>￥</span><span class="allpricenumber">0.00</span></div>
			
			<?php if(in_array("new-task/create-order", $node) || $this->params['is_administrator_user']):?>
			<div class="gotoPay"><a href="javascript:;" id="payfor">去结算</a></div>
			<?php endif;?>
			
		</div>
	</div>
</div>

<div id="mainContainer">
	<div class="info-title">新购业务下单</div>
	<div class="info-box">
		<div class="userinfo">
			<?php if($user_id):?>
				<?php 
					$usermail = \common\helpers\MemberHelper::userAccount($userinfo);
					$usernick = $userinfo['uname'] ? $userinfo['uname'] : '-';
					$userbalance = $userinfo['balance'];
					
					echo '账户：'.$usermail.'&nbsp;&nbsp;&nbsp;&nbsp;昵称：'.$usernick.'&nbsp;&nbsp;&nbsp;&nbsp;余额：'.$userbalance;
				?>
				<input type="hidden" name="toUserID" id="toUserID" value="<?php echo $userinfo['u_id']?>" class="col-xs-5"> 
				<input type="hidden" name="toAdminID" id="toAdminID" value="<?php echo $userinfo['admin_id']?>" class="col-xs-5"> 
			<?php endif;?>
			<button type="button" style="margin-left:20px;height:30px;" class="btn btn-white btn-default" id="userselector"> 选择会员 </button>
			
			<?php if(in_array("new-task/select-cart", $node) || $this->params['is_administrator_user']):?>
			<button type="button" style="margin-left:20px;height:30px;" class="btn btn-white btn-warning" id="lookcart"> <i class="fa fa-shopping-cart"></i> 查看购物车 <span class="notranslate" id="cartNum"><?php echo $cartNum;?></span> </button>
			<?php endif;?>
			
			<div class="clearfix"></div>
		</div>
		<div id="trade-content">
			<div class="public-container clear">
				<section class="content-box">
					<div class="content-innerbox">
						<div class="content-title">选择地域</div>
						<div class="content-content">
							<ul>
								<?php foreach($arealist as $k1 => $v1):?>
									<?php if($areaid && $areaid == $v1['id']):?>
										<li class="active items" rels="areaid" reldata="<?php echo $v1['id']?>"><a href="<?php echo Url::to(['new-task/index', 'id'=>$id, 'areaid' => $v1['id'], 'typeid' => $typeid, 'user_id' => $user_id])?>"><?php echo $v1['regional_name']?></a></li>
									<?php else:?>
										<li class="items" rels="areaid" reldata="<?php echo $v1['id']?>"><a href="<?php echo Url::to(['new-task/index', 'id'=>$id, 'areaid' => $v1['id'], 'typeid' => $typeid, 'user_id' => $user_id])?>"><?php echo $v1['regional_name']?></a></li>
									<?php endif;?>
								<?php endforeach;?>
							</ul>
						</div>
					</div>
					<div class="content-innerbox">
						<div class="content-title">分类类型</div>
						<div class="content-content">
							<ul>
								<?php foreach($typelist as $k2 => $v2):?>
									<?php if($typeid && $typeid == $v2['type_id']):?>
										<li class="active items" rels="server_type_id" reldata="<?php echo $v2['type_id']?>"><a href="<?php echo Url::to(['new-task/index', 'id'=>$id, 'areaid' => $areaid, 'typeid' => $v2['type_id'], 'user_id' => $user_id])?>"><?php echo $v2['type_name']?></a></li>
									<?php else:?>
										<li class="items" rels="server_type_id" reldata="<?php echo $v2['type_id']?>"><a href="<?php echo Url::to(['new-task/index', 'id'=>$id, 'areaid' => $areaid, 'typeid' => $v2['type_id'], 'user_id' => $user_id])?>"><?php echo $v2['type_name']?></a></li>
									<?php endif;?>
								<?php endforeach;?>
							</ul>
						</div>
					</div>
					<div class="content-innerbox">
						<div class="content-title">选择配置</div>
						<div class="content-content">
							<ul>
								<?php foreach($setlist as $k3 => $v3):?>
									<?php if($id && $id == $v3['id']):?>
										<li class="active items" rels="id" reldata="<?php echo $v3['id']?>"><a href="<?php echo Url::to(['new-task/index', 'id'=>$v3['id'], 'areaid' => $areaid, 'typeid' => $typeid, 'user_id' => $user_id])?>"><?php echo $v3['name']?></a></li>
									<?php else:?>
										<li class="items" rels="id" reldata="<?php echo $v3['id']?>"><a href="<?php echo Url::to(['new-task/index', 'id'=>$v3['id'], 'areaid' => $areaid, 'typeid' => $typeid, 'user_id' => $user_id])?>"><?php echo $v3['name']?></a></li>
									<?php endif;?>
								<?php endforeach;?>
							</ul>
						</div>
					</div>
				</section>
				<?php if($setinfo):?>
				<section class="content-box">			
					<div class="content-innerbox">
						<div class="content-title">CPU</div>
						<div class="content-content">
							<ul>
								<?php $cpu = json_decode($setinfo['cpu'], true);?>
								<?php foreach($cpu['info'] as $k => $v):?>
									<?php if($v['id'] == $cpu['default']):?>
										<li class="active" rels="cpu" reldata="<?php echo $v['id']?>"><a href="javascript:;" price="0"><?php echo $v['name'];?></a></li>
									<?php endif;?>
								<?php endforeach;?>
							</ul>
						</div>
					</div>
					
					<div class="content-innerbox">
						<div class="content-title">内存</div>
						<div class="content-content">
							<ul>
								<?php $ram = json_decode($setinfo['ram'], true);?>
								<?php foreach($ram['info'] as $k => $v):?>
									<?php if($v['id'] == $ram['default']):?>
										<li class="active" rels="ram" reldata="<?php echo $v['id']?>"><a href="javascript:;" price="0"><?php echo $v['name'];?></a></li>
									<?php else:?>
										<li rels="ram" reldata="<?php echo $v['id']?>"><a href="javascript:;" price="<?php echo $v['price'];?>"><?php echo $v['name'];?></a></li>
									<?php endif;?>
								<?php endforeach;?>
							</ul>
						</div>
					</div>
					
					<div class="content-innerbox">
						<div class="content-title">硬盘</div>
						<div class="content-content">
							<ul>
								<?php $hdd = json_decode($setinfo['hdd'], true);?>
								<?php foreach($hdd['info'] as $k => $v):?>
									<?php if($v['id'] == $hdd['default']):?>
										<li class="active" rels="hdd" reldata="<?php echo $v['id']?>"><a href="javascript:;" price="0"><?php echo $v['name'];?></a></li>
									<?php else:?>
										<li rels="hdd" reldata="<?php echo $v['id']?>"><a href="javascript:;" price="<?php echo $v['price'];?>"><?php echo $v['name'];?></a></li>
									<?php endif;?>
								<?php endforeach;?>
							</ul>
						</div>
					</div>
					
					<div class="content-innerbox">
						<div class="content-title">带宽</div>
						<div class="content-content">
							<ul>
								<?php $bandwidth = json_decode($setinfo['bandwidth'], true);?>
								<?php foreach($bandwidth['info'] as $k => $v):?>
									<?php if($v['id'] == $bandwidth['default']):?>
										<li class="active" rels="bandwidth" reldata="<?php echo $v['id']?>"><a href="javascript:;" price="0"><?php echo $v['name'];?></a></li>
									<?php else:?>
										<li rels="bandwidth" reldata="<?php echo $v['id']?>"><a href="javascript:;" price="<?php echo $v['price'];?>"><?php echo $v['name'];?></a></li>
									<?php endif;?>
								<?php endforeach;?>
							</ul>
						</div>
					</div>
					
					<div class="content-innerbox">
						<div class="content-title">IP数量</div>
						<div class="content-content">
							<ul>
								<?php $ipnumber = json_decode($setinfo['ipnumber'], true);?>
								<?php foreach($ipnumber['info'] as $k => $v):?>
									<?php if($v['id'] == $ipnumber['default']):?>
										<li class="active" rels="ipnumber" reldata="<?php echo $v['id']?>"><a href="javascript:;" price="0"><?php echo $v['name'];?></a></li>
									<?php else:?>
										<li rels="ipnumber" reldata="<?php echo $v['id']?>"><a href="javascript:;" price="<?php echo $v['price'];?>"><?php echo $v['name'];?></a></li>
									<?php endif;?>
								<?php endforeach;?>
							</ul>
						</div>
					</div>
					
					<div class="content-innerbox">
						<div class="content-title">防御流量</div>
						<div class="content-content">
							<ul>						
								<?php $defense = json_decode($setinfo['defense'], true);?>
								<?php if($defense):?>
									<?php foreach($defense['info'] as $k => $v):?>
										<?php if($v['id'] == $defense['default']):?>
											<li class="active" rels="defense" reldata="<?php echo $v['id']?>"><a href="javascript:;" price="0"><?php echo $v['name'];?></a></li>
										<?php else:?>
											<li rels="defense" reldata="<?php echo $v['id']?>"><a href="javascript:;" price="<?php echo $v['price'];?>"><?php echo $v['name'];?></a></li>
										<?php endif;?>
									<?php endforeach;?>
								<?php else:?>
									<li class="active" rels="defense" reldata=""><a href="javascript:;">没有选项</a></li>
								<?php endif;?>
							</ul>
						</div>
					</div>
					
					<div class="content-innerbox">
						<div class="content-title">操作系统</div>
						<div class="content-content">
							<ul>
								<?php $operatsystem = json_decode($setinfo['operatsystem'], true);?>
								<?php foreach($operatsystem['info'] as $k => $v):?>
									<?php if($v['id'] == $operatsystem['default']):?>
										<li class="active" rels="operatsystem" reldata="<?php echo $v['id']?>"><a href="javascript:;" price="0"><?php echo $v['name'];?></a></li>
									<?php else:?>
										<li rels="operatsystem" reldata="<?php echo $v['id']?>"><a href="javascript:;" price="<?php echo $v['price'];?>"><?php echo $v['name'];?></a></li>
									<?php endif;?>
								<?php endforeach;?>
							</ul>
						</div>
					</div>
					<div class="content-innerbox">
						<div class="content-title">备注</div>
						<div class="content-content">
							<ul>
								<div class="clearfix">
									<textarea class="form-control textarea" name="order_remark" id="order_remark" cols="80" rows="3" style="margin: 0px;resize: none; "></textarea>
								</div>
							</ul>
						</div>
					</div>
				</section>
				
				<section class="content-box payscroll paymain">
					<div class="content-width">
						<div class="content-title">配置费用</div>
						<div class="content-content">
							<div class="paybox">
								<div class="settimetitle">付费周期：</div>
								<div class="settimecontent">
									<select name="expire" class="setselect">
										<option value="1" selected money="<?php echo sprintf("%.2f", $setinfo['sell_price'])?>" >1 个月</option>
										<option value="3" money="<?php echo sprintf("%.2f", $setinfo['price_quarter'])?>">3 个月</option>
										<option value="6" money="<?php echo sprintf("%.2f", $setinfo['price_half'])?>">半 年</option>
										<option value="12" money="<?php echo sprintf("%.2f", $setinfo['price_year'])?>">1 年</option>
									</select>
									<div class="buyNum">
										<div class="numtxt">购买数量：</div>
										<div class="plusNum"><a href="javascript:;">+</a></div>
										<div class="buyNum_input"><input type="text" name="buynum" value="1" readonly /></div>
										<div class="minuesNum"><a href="javascript:;">-</a></div>
									</div>
									<div class="paymoney">配置费用：<span class="moneybox">￥<?php echo sprintf("%.2f", $setinfo['sell_price'])?></span></div>
									
									<?php if(isset($userRole) && is_array($userRole)):?>
										<?php if(in_array('pay', $userRole) || in_array('all', $userRole)):?>
										
											<?php if(in_array("new-task/create-order", $node) || $this->params['is_administrator_user']):?>
												<div id="paybtn">立即下单</div>
											<?php endif;?>
											
											<?php if(in_array("new-task/add-cart", $node) || $this->params['is_administrator_user']):?>
												<div id="addCart">加入购物车</div>
											<?php endif;?>
											
										<?php endif;?>
									<?php else:?>
										<?php if(in_array("new-task/create-order", $node) || $this->params['is_administrator_user']):?>
											<div id="paybtn">立即下单</div>
										<?php endif;?>
										
										<?php if(in_array("new-task/add-cart", $node) || $this->params['is_administrator_user']):?>
											<div id="addCart">加入购物车</div>
										<?php endif;?>
										
									<?php endif;?>
								</div>
							</div>
						</div>
					</div>
				</section>
				<?php endif;?>
			</div>
		</div>
		<input type="hidden" id="money" value="0">
		<input type="hidden" id="addmoney" value="0">
	</div>
</div>


<script type="text/javascript">
$(function() {
	
	$("#shadeBox").height($(this).height() - 70);
	
	$("#cartViewBox").css("left", ($("#shadeBox").width() - 800) / 4);
	
	/* 全选 反选 */
	$("#chooseAll").click(function() {
		if($(this).prop('checked')) {
			$('.selectPro').prop("checked", true);
		} else {
			$('.selectPro').prop("checked", false);
		}
		countTotalMoney();
	});
	
	/* 批量删除 */
	$(".deleteSelect").click(function() {
		var index = layer.confirm("确定要删除所选项吗?", {
			btn:['是的', '点错了']
		}, function() {
			var totalDoms = $(".cartlistbox").length;
			var cartIdList = '';
			for(var i=0; i<totalDoms; i++) {
				if($(".cartlistbox:eq("+i+")").find('.selectPro').prop("checked") == true) {
					cartIdList += $(".cartlistbox:eq("+i+")").find('.cart_id').html() + ',';
					$(".cartlistbox:eq("+i+")").addClass('readyToRemove');
				}
			}
			
			cartIdList = cartIdList.substr(0, cartIdList.length - 1);
			$(".readyToRemove").fadeOut(400, function() {
				$(this).remove();
			});
			$.post("<?php echo Url::to(['new-task/del-cart'])?>", {"cart_id":cartIdList}, function(e) {
				$(".allpricenumber").html(decimalTwo(0));
				$("#chooseAll").prop("checked", false);
				layer.close(index);
				flashCartInfo();
				if(e.data.status == 0) {
					layer.alert(e.data.info, {icon:7});
				}
			}, "json");
			
			
		});
	});
	
	/* 购物车 */
	$("#lookcart").click(function() {
		if($(this).hasClass("isOpenCart")) {
			closeCartList();
		} else {
			viewCartList();	
			if(!$("#chooseAll").prop('checked')) {
				$("#chooseAll").click();
			}
		}
	});
	
	/* 购物车关闭按钮 */
	$(".closeCartView a").click(function() {
		closeCartList();
	});
	
	
	/* 购物车结算生成订单 */
	$("#payfor").click(function() {
		var totalDoms = $(".cartlistbox").length;
		var cartIdList = '';
		
		var user_id = $("#toUserID").val();
		
		for(var i=0; i<totalDoms; i++) {
			
			if($(".cartlistbox:eq("+i+")").find('.selectPro').prop("checked") == true) {
				
				cartIdList += $(".cartlistbox:eq("+i+")").find('.cart_id').html() + ',';
				$(".cartlistbox:eq("+i+")").addClass('readyToRemove');
			}
		}
		cartIdList = cartIdList.substr(0, cartIdList.length - 1);
		$.post("<?php echo Url::to(['new-task/create-order'])?>", {"cart_id":cartIdList, "user_id":user_id}, function(e) {
			if(e.data.status == 0) {
				layer.alert(e.data.info, {icon:7});
				return false;
			} else if(e.data.status == 1 || e.data.status == -1) {
				window.location.href=e.data.url;
			} else if(e.data.status == -2) {
				//未分配客服
				var html = '<div style="padding:0 40px;height:500px;">';
				$.each(e.data.data, function(i, n) {
					html += '<div class="selectSaler" dataRole="'+n.admin_id+'" style="font-size:12px;border:1px #ccc solid;padding:5px 8px;display:inline-block;cursor:pointer;margin-right:10px;margin-top:10px;">'+(n.nickname ? n.nickname : n.rename)+'<br/>QQ:'+n.qq+'</div>';
				});
				html += '<div class="chooseSaler" style="position:fixed;bottom:20px;left:40px;padding:5px 15px;display:inline-block;cursor:pointer;border:1px #ccc solid;color:#fff;background:#ccc;">确认选择</div></div>';
					
				var closeIndex = layer.open({
				  type: 1, //Page层类型
				  area: ['450px', 'auto'],
				  title: '请您选择为您服务的客服代表',
				  shade: 0.6, //遮罩透明度
				  maxmin: false, //允许全屏最小化
				  anim: 2, //0-6的动画形式，-1不开启
				  content: html,
				});
				
				$(".selectSaler").bind("click", function() {
					$(".selectSaler").css({"color":"#000", "background":"#fff", "border":"1px #ccc solid"}).removeClass("activeSelect");
					$(this).css({"color":"#fff", "background":"#00a0e9", "border":"1px #00a0e9 solid"}).addClass("activeSelect");
					$(".chooseSaler").css({"color":"#fff", "background":"#00a0e9", "border":"1px #00a0e9 solid"}).addClass("activeSelect");
				});
				
				$(".chooseSaler").bind("click", function() {
					var adminid = $(".activeSelect").attr("dataRole");
					if(!adminid) {
						return false;
					}
					
					$.get("<?php echo Url::to(['new-task/select-saler'])?>", {"admin_id":adminid}, function(e) {
						if(e.data.status == 1) {
							layer.close(closeIndex);
							layer.alert(e.data.info, {icon:1});
						} else {
							layer.close(closeIndex);
							layer.alert(e.data.info, {icon:7});
						}
					}, "json");
				});

				return false;
			}
		}, "json");
	});
	
	
	
	/* 计算总金额 */
	function countTotalMoney() {
		var totalPriceDom = $(".cartlistbox").length;
		var moneyTotal = 0;
		for(var i=0; i<totalPriceDom; i++) {
			if($(".cartlistbox:eq("+i+")").find('.selectPro').prop("checked") == true) {
				moneyTotal = Number(moneyTotal) + Number($(".cartlistbox:eq("+i+")").find('.cartlisttotalprice > .cartlistedit-content > span').html());
			}
		}
		$(".allpricenumber").html(decimalTwo(moneyTotal));
	}
	
	function viewCartList() {
		$("#shadeBox").show();
		$("#cartViewBox").show();
		
		flashCartInfo();
		
		$("#shadeBox").animate({
			"opacity":"0.7"
		}, 300);
		
		$("#cartViewBox").animate({
			"top":"40px",
			"opacity":"1"
		}, 300, function() {
			$("#lookcart").addClass("isOpenCart");
		});
	}
	
	
	
	function closeCartList() {
		$("#shadeBox").animate({
			"opacity":"0"
		}, 300, function() {
			$("#shadeBox").hide();
		});
		
		$("#cartViewBox").animate({
			"top":"-900px",
			"opacity":"0"
		}, 300, function() {
			$("#cartViewBox").hide();
			$("#lookcart").removeClass("isOpenCart");
		});
	}
	
	//选择用户
	$("#userselector").click(function(){
		// 正常打开
		layer.open({
			type: 2, 
			area: ['1020px', '600px'],
			title:"" || "选择用户",
			content: "<?php echo Url::to(['user-member/select'])?>",
			btn: ['确定', '关闭'],
			yes: function(index, layero){ //或者使用btn1
				var iframeWin = layero.find('iframe')[0];//得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
				var body = $(iframeWin).contents();
				var radio = $(body).find(".i-checks:checked");
				layer.close(index);
				
				window.location.href="<?php echo Url::to(['new-task/index'])?>"+"?user_id="+radio.val();
			},
			cancel: function(index){ //或者使用btn2
				//按钮【按钮二】的回调
			}
		}); 
	});
	
	$(".setselect").change(function() {
		var month = $(this).val();
		innerMoney();
		var money = $("#money").val();
		var addmoney = $("#addmoney").val();
		var buynum = $(".buyNum_input input").val();
		var viewmoney = decimalTwo((Number(money) + (Number(addmoney) * month)) * buynum);
		$(".moneybox").html("￥"+viewmoney);
	});
	
	$(".plusNum").click(function() {
		var oldnum = $(".buyNum_input input").val();
		var newnum = parseInt(oldnum) + 1;
		var month = $(".setselect").val();
		var money = $("#money").val();
		var addmoney = $("#addmoney").val();
		var viewmoney = decimalTwo((Number(money) + (Number(addmoney) * month)) * newnum);

		$(".buyNum_input input").val(newnum);
		$(".moneybox").html("￥"+viewmoney);
	});
	
	$(".minuesNum").click(function() {
		var oldnum = $(".buyNum_input input").val();
		if(oldnum > 1) {
			var newnum = parseInt(oldnum) - 1;
			var month = $(".setselect").val();
			var money = $("#money").val();
			var addmoney = $("#addmoney").val();
			var viewmoney = decimalTwo((Number(money) + (Number(addmoney) * month)) * newnum);
			
			$(".buyNum_input input").val(newnum);
			$(".moneybox").html("￥"+viewmoney);
		}
	});
	
	innerMoney();
	function innerMoney() {
		var nowcheck = $(".setselect option:selected").attr('money');
		$("#money").val(nowcheck);
	}
	
	$(".content-content ul li").click(function() {
		var month = $(".setselect").val();
		if(!$(this).hasClass('items')) {
			if(!$(this).hasClass('active')) {
				var money = decimalTwo($("#money").val());
				var addmoney = decimalTwo($("#addmoney").val());
				var front = decimalTwo($(this).siblings('.active').children('a').attr('price'));
				var after = decimalTwo($(this).children('a').attr('price'));
				addmoney = addmoney - front + parseFloat(after);
				//console.log(money);
				$("#addmoney").val(addmoney);
				var buynum = $(".buyNum_input input").val();
				var viewmoney = decimalTwo((Number(money) + Number(addmoney) * month) * buynum);
				$(".moneybox").html("￥"+viewmoney);
			}
			
		}
		$(this).siblings().removeClass('active');
		$(this).addClass('active');
	});
	
	function decimalTwo(num) {
			var result = parseFloat(num);
		if (isNaN(result)) {
			return false;
		}
			result = Math.round(num * 100) / 100;
		var s_x = result.toString();
		var pos_decimal = s_x.indexOf('.');
		if (pos_decimal < 0) {
			pos_decimal = s_x.length;
			s_x += '.';
		}
		while (s_x.length <= pos_decimal + 2) {
			s_x += '0';
		}
		return s_x;
	}
	
	$("#addCart").click(function() {
        var loading = layer.load(2);
		var data = {};
		for(var i=0; i<$(".active").length; i++) {
			var key = $(".active:eq("+i+")").attr("rels");
			var value = $(".active:eq("+i+")").attr("reldata");
			data[key] = value;
		}
		data['order_remark'] = $("#order_remark").val();
		data["expire"] = $(".setselect option:selected").val();
		data["buynum"] = $(".buyNum_input input").val();
		
		var url = "<?php echo Url::to(['new-task/add-cart']);?>";
		var user_id = $("#toUserID").val();
		data['user_id'] = user_id;
		
		
		$.post(url, data, function(e) {
			if(e.data.status == -1) {
				window.location.href=e.data.info;
				return false;
			} else if(e.data.status == 0) {
                layer.close(loading);
				layer.alert(e.data.info, {icon:7});
				return false;
			} else if(e.data.status == -2) {
                layer.close(loading);
				//未分配客服
				var html = '<div style="padding:0 40px;height:500px;">';
				$.each(e.data.data, function(i, n) {
					html += '<div class="selectSaler" dataRole="'+n.admin_id+'" style="font-size:12px;border:1px #ccc solid;padding:5px 8px;display:inline-block;cursor:pointer;margin-right:10px;margin-top:10px;">'+(n.nickname ? n.nickname : n.rename)+'<br/>QQ:'+n.qq+'</div>';
				});
				html += '<div class="chooseSaler" style="position:fixed;bottom:20px;left:40px;padding:5px 15px;display:inline-block;cursor:pointer;border:1px #ccc solid;color:#fff;background:#ccc;">确认选择</div></div>';
					
				var closeIndex = layer.open({
				  type: 1, //Page层类型
				  area: ['450px', 'auto'],
				  title: '请您选择为您服务的客服代表',
				  shade: 0.6, //遮罩透明度
				  maxmin: false, //允许全屏最小化
				  anim: 2, //0-6的动画形式，-1不开启
				  content: html,
				});
				
				$(".selectSaler").bind("click", function() {
					$(".selectSaler").css({"color":"#000", "background":"#fff", "border":"1px #ccc solid"}).removeClass("activeSelect");
					$(this).css({"color":"#fff", "background":"#00a0e9", "border":"1px #00a0e9 solid"}).addClass("activeSelect");
					$(".chooseSaler").css({"color":"#fff", "background":"#00a0e9", "border":"1px #00a0e9 solid"}).addClass("activeSelect");
				});
				
				$(".chooseSaler").bind("click", function() {
					var adminid = $(".activeSelect").attr("dataRole");
					if(!adminid) {
						return false;
					}
					
					$.get("<?php echo Url::to(['new-task/select-saler'])?>", {"admin_id":adminid}, function(e) {
						if(e.data.status == 1) {
							layer.close(closeIndex);
							layer.alert(e.data.info, {icon:1}, function() {
								window.location.reload();
							});
						} else {
							layer.close(closeIndex);
							layer.alert(e.data.info, {icon:7});
						}
					}, "json");
				});
				
				
				return false;
			} else {
				flashCartInfo();
                layer.close(loading);
				layer.alert(e.data.info,{icon:1});
				$("#cartNum").html(e.data.data);
				return false;
			}
		}, "json");
	});
	
	$("#paybtn").click(function() {
        var loading = layer.load(2);
		
		var user_id = $("#toUserID").val();
		
		var data = {};
		for(var i=0; i<$(".active").length; i++) {
			var key = $(".active:eq("+i+")").attr("rels");
			var value = $(".active:eq("+i+")").attr("reldata");
			data[key] = value;
		}
		data['order_remark'] = $("#order_remark").val();
		data["expire"] = $(".setselect option:selected").val();
		data["buynum"] = $(".buyNum_input input").val();
		data['user_id'] = user_id;
		
		var url = "<?php echo Url::to(['new-task/add-cart']);?>";
		$.post(url, data, function(e) {
			if(e.data.status == -1) {
				window.location.href=e.data.info;
				return false;
			} else if(e.data.status == 0) {
                layer.close(loading);
				layer.alert(e.data.info, {icon:7});
				return false;
			} else if(e.data.status == -2) {
                layer.close(loading);
				//未分配客服
				var html = '<div style="padding:0 40px;height:500px;">';
				$.each(e.data.data, function(i, n) {
					html += '<div class="selectSaler" dataRole="'+n.admin_id+'" style="font-size:12px;border:1px #ccc solid;padding:5px 8px;display:inline-block;cursor:pointer;margin-right:10px;margin-top:10px;">'+(n.nickname ? n.nickname : n.rename)+'<br/>QQ:'+n.qq+'</div>';
				});
				html += '<div class="chooseSaler" style="position:fixed;bottom:20px;left:40px;padding:5px 15px;display:inline-block;cursor:pointer;border:1px #ccc solid;color:#fff;background:#ccc;">确认选择</div></div>';
					
				var closeIndex = layer.open({
				  type: 1, //Page层类型
				  area: ['450px', 'auto'],
				  title: '请您选择为您服务的客服代表',
				  shade: 0.6, //遮罩透明度
				  maxmin: false, //允许全屏最小化
				  anim: 2, //0-6的动画形式，-1不开启
				  content: html,
				});
				
				$(".selectSaler").bind("click", function() {
					$(".selectSaler").css({"color":"#000", "background":"#fff", "border":"1px #ccc solid"}).removeClass("activeSelect");
					$(this).css({"color":"#fff", "background":"#00a0e9", "border":"1px #00a0e9 solid"}).addClass("activeSelect");
					$(".chooseSaler").css({"color":"#fff", "background":"#00a0e9", "border":"1px #00a0e9 solid"}).addClass("activeSelect");
				});
				
				$(".chooseSaler").bind("click", function() {
					var adminid = $(".activeSelect").attr("dataRole");
					if(!adminid) {
						return false;
					}
					
					$.get("<?php echo Url::to(['new-task/select-saler'])?>", {"admin_id":adminid}, function(e) {
						if(e.data.status == 1) {
							layer.close(closeIndex);
							layer.alert(e.data.info, {icon:1}, function() {
								window.location.reload();
							});
						} else {
							layer.close(closeIndex);
							layer.alert(e.data.info, {icon:7});
						}
					}, "json");
				});
				
				
				return false;
			} else {
				
				$.post("<?php echo Url::to(['new-task/create-order'])?>", {"cart_id":e.data.cart_id, "user_id":user_id}, function(o) {
					if(o.data.status == 0) {
                        layer.close(loading);
						layer.alert(o.data.info, {icon:7});
						return false;
					} else if(o.data.status == 1 || o.data.status == -1) {
						window.location.href=o.data.url;
					} else if(o.data.status == -2) {
                        layer.close(loading);
						//未分配客服
						var html = '<div style="padding:0 40px;height:500px;">';
						$.each(o.data.data, function(i, n) {
							html += '<div class="selectSaler" dataRole="'+n.admin_id+'" style="font-size:12px;border:1px #ccc solid;padding:5px 8px;display:inline-block;cursor:pointer;margin-right:10px;margin-top:10px;">'+(n.nickname ? n.nickname : n.rename)+'<br/>QQ:'+n.qq+'</div>';
						});
						html += '<div class="chooseSaler" style="position:fixed;bottom:20px;left:40px;padding:5px 15px;display:inline-block;cursor:pointer;border:1px #ccc solid;color:#fff;background:#ccc;">确认选择</div></div>';
							
						var closeIndex = layer.open({
						  type: 1, //Page层类型
						  area: ['450px', 'auto'],
						  title: '请您选择为您服务的客服代表',
						  shade: 0.6, //遮罩透明度
						  maxmin: false, //允许全屏最小化
						  anim: 2, //0-6的动画形式，-1不开启
						  content: html,
						});
						
						$(".selectSaler").bind("click", function() {
							$(".selectSaler").css({"color":"#000", "background":"#fff", "border":"1px #ccc solid"}).removeClass("activeSelect");
							$(this).css({"color":"#fff", "background":"#00a0e9", "border":"1px #00a0e9 solid"}).addClass("activeSelect");
							$(".chooseSaler").css({"color":"#fff", "background":"#00a0e9", "border":"1px #00a0e9 solid"}).addClass("activeSelect");
						});
						
						$(".chooseSaler").bind("click", function() {
							var adminid = $(".activeSelect").attr("dataRole");
							if(!adminid) {
								return false;
							}
							
							$.get("<?php echo Url::to(['new-task/select-saler'])?>", {"admin_id":adminid}, function(e) {
								if(o.data.status == 1) {
									layer.close(closeIndex);
									layer.alert(o.data.info, {icon:1});
								} else {
									layer.close(closeIndex);
									layer.alert(o.data.info, {icon:7});
								}
							}, "json");
						});

						return false;
					}
				}, "json");
			}
		}, "json");
	});
	
	function flashCartInfo() {
		
		var user_id = $("#toUserID").val();
		
		$.post("<?php echo Url::to(['new-task/select-cart'])?>", {"user_id":user_id}, function(e) {
			var cartnumInit = e.data.datanum ? e.data.datanum : 0;
			$("#cartNum").html(cartnumInit);
			if(e.data.datanum > 0) {
				//如果购物车有产品
				var cartHtml = '';
				$.each(e.data.data, function(i, n) {
					cartHtml += '<div class="cartlistbox"><a href="javascript:;" class="deleteFromCart"><img src="/cart/deleteCart.png"/></a><div class="selectCartPro"><input type="checkbox" name="selectPro[]" class="selectPro" value=""></div>';
					for(var k in n) {
						if(k == 'cart_id') {
							cartHtml += '<div class="cartlistrow"><div class="cartlist-rowtitle">产品编号</div><div class="cartlist-rowcontent cart_id notranslate">'+n[k]+'</div></div>';
						}
						
						if(k == 'viewservername') {
							cartHtml += '<div class="cartlistrow"><div class="cartlist-rowtitle">服务器类型</div><div class="cartlist-rowcontent">'+n[k]+'</div></div>';
						}
						
						if(k == 'viewpdtname') {
							cartHtml += '<div class="cartlistrow"><div class="cartlist-rowtitle">配置名称</div><div class="cartlist-rowcontent">'+n[k]+'</div></div>';
						}
						
						if(k == 'viewconfig') {
							cartHtml += '<div class="cartlistrow"><div class="cartlist-rowtitle">配置详情</div><div class="cartlist-rowcontent">'+n[k]+'</div></div>';
						}
						
						if(k == 'viewdefense') {
							cartHtml += '<div class="cartlistrow"><div class="cartlist-rowtitle">配置防御</div><div class="cartlist-rowcontent">'+n[k]+'</div></div>';
						}
						
						if(k == 'viewsystem') {
							cartHtml += '<div class="cartlistrow"><div class="cartlist-rowtitle">配置系统</div><div class="cartlist-rowcontent">'+n[k]+'</div></div>';
						}
						
						if(k == 'cart_remark') {
							cartHtml += '<div class="cartlistrow"><div class="cartlist-rowtitle">配置备注</div><div class="cartlist-rowcontent">'+n[k]+'</div></div>';
						}
						
						if(k == 'viewcycle') {
							if(n[k] == 1) {
								n[k] = '1个月';
							} else if(n[k] == 3) {
								n[k] = '3个月';
							} else if(n[k] == 6) {
								n[k] = '半年';
							} else if(n[k] == 12) {
								n[k] = '1年';
							}
							cartHtml += '<div class="cartlistedit cartlistcycle"><div class="cartlistedit-title">付费周期</div><div class="cartlistedit-content">'+n[k]+'</div></div>';
						}
						
						if(k == 'cart_num') {
							cartHtml += '<div class="cartlistedit cartlistnum"><div class="cartlistedit-title">下单数量</div><div class="cartlistedit-content"><div class="cartnumPlus"><a href="javascript:;">+</a></div><div class="cartnum"><input type="text" name="cartnum" value="'+n[k]+'" readonly /></div><div class="cartnumMinues"><a href="javascript:;">-</a></div></div></div>';
						}
						
						if(k == 'cart_price_single') {
							cartHtml += '<div class="cartlistedit cartlistprice"><div class="cartlistedit-title notranslate">产品单价</div><div class="cartlistedit-content">￥<span class="notranslate">'+n[k]+'</span></div></div>';
						}
						
						if(k == 'cart_price_total') {
							cartHtml += '<div class="cartlistedit cartlisttotalprice"><div class="cartlistedit-title notranslate">产品总价</div><div class="cartlistedit-content">￥<span class="notranslate">'+n[k]+'</span></div></div>';
						}
						
					}
					
					cartHtml += '</div>';
				});
				
				$(".viewbox_content").html(cartHtml);
				
				/*
					------------ 绑定事件 -----------
				*/
				
				/* 从购物车删除 */
				$(".deleteFromCart").click(function() {
					var cart_id = $(this).parent(".cartlistbox").find(".cart_id").html();
					var _boxobj = $(this).parent(".cartlistbox");
					_boxobj.fadeOut(400, function() {
						_boxobj.remove();
						$.post("<?php echo Url::to(['new-task/del-cart'])?>", {"cart_id":cart_id}, function(e) {
							countTotalMoney();
							if(e.data.status == 0) {
								layer.alert(e.data.info, {icon:7});
							}
						}, "json");
					});
				});
				
				/* 产品数量 */
				$(".cartnumPlus a").click(function() {
					var oldnum = $(this).parents('.cartlistnum').find(".cartnum > input").val();
					var newnum = parseInt(oldnum) + 1;
					var cart_id = $(this).parents('.cartlistbox').find('.cart_id').html();
					var _this = $(this);
					$.post("<?php echo Url::to(['new-task/upd-cartnum'])?>", {"cart_id":cart_id, "number":newnum}, function(e) {
						if(e.data.status == 1) {
							_this.parents('.cartlistnum').find(".cartnum > input").val(newnum);
							var singlePrice = _this.parents('.cartlistbox').find('.cartlistprice').find('.cartlistedit-content > span').html();
							var newMoney = decimalTwo(Number(singlePrice) * Number(newnum));
							
							_this.parents('.cartlistbox').find('.cartlisttotalprice').find('.cartlistedit-content > span').html(newMoney);
							countTotalMoney();
						}
					}, "json");
					
				});
				
				$(".cartnumMinues a").click(function() {
					var oldnum = $(this).parents('.cartlistnum').find(".cartnum > input").val();
					var newnum = parseInt(oldnum) - 1;
					var cart_id = $(this).parents('.cartlistbox').find('.cart_id').html();
					var _this = $(this);
					if(oldnum > 1) {
						$.post("<?php echo Url::to(['new-task/upd-cartnum'])?>", {"cart_id":cart_id, "number":newnum}, function(e) {
							if(e.data.status == 1) {
								_this.parents('.cartlistnum').find(".cartnum > input").val(newnum);
								var singlePrice = _this.parents('.cartlistbox').find('.cartlistprice').find('.cartlistedit-content > span').html();
								var newMoney = decimalTwo(Number(singlePrice) * Number(newnum));
								_this.parents('.cartlistbox').find('.cartlisttotalprice').find('.cartlistedit-content > span').html(newMoney);
								countTotalMoney();
							}
						}, "json");
					}
					
				});
				
				$('.selectPro').click(function() {
					if($('.selectPro:checked').length == $('.selectPro').length) {
						$("#chooseAll").prop("checked", true);
					} else {
						$("#chooseAll").prop("checked", false);
					}
					countTotalMoney();
				});
			}
		}, "json");
	}
	
});
</script>
<?php $this->endBlock(); ?>