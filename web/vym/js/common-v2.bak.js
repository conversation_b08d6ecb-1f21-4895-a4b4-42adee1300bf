(function () {
  document.addEventListener("DOMContentLoaded", function () {
    async function _0x260791(_0x38500a) {
      {
        const _0x59e163 = await (await fetch(_0x38500a)).arrayBuffer(),
          _0x23a208 = _0x2baec6(_0x59e163);
        return _0x23a208;
      }
    }
    function _0x5ad4c3(_0xdb28a3, _0x4b2d95 = 200) {
      const _0xe419d5 = document.createElement("canvas");
      _0xe419d5.width = _0x4b2d95;
      _0xe419d5.height = 100;
      const _0xa82083 = _0xe419d5.getContext("2d");
      _0xa82083.fillStyle = "#FF0000";
      _0xa82083.fillRect(0, 0, 200, 100);
      _0xa82083.fillStyle = "#FFFFFF";
      _0xa82083.font = "18px Arial";
      _0xa82083.fillText(_0xdb28a3, 10, 50);
      const _0x29fe57 = _0xe419d5.toDataURL();
      return _0x29fe57;
    }
    function _0x2baec6(_0xdb9367) {
      const _0x17c9e2 = new Uint8Array(_0xdb9367),
        _0x5d9330 = [..._0x17c9e2].map(_0x584815 => _0x584815.toString(16).padStart(2, "0")).join("");
      return _0x5d9330;
    }
    async function _0x256342() {
      !_0x3a8ef6() && _0x38fc40("环境异常不支持的浏览器内核,请更换浏览器访问！");
      const _0xa48574 = "环境异常-2！请刷新后再试",
        _0x10a63e = "Error",
        _0x11f151 = window.location.host,
        _0x51bdd6 = _0x5ad4c3(_0x11f151);
      let _0x5ab852 = await _0x260791(_0x51bdd6);
      const _0x459042 = await _0x5ad4c3(_0x51bdd6, 152);
      let _0x2c6efc = await _0x260791(_0x459042);
      try {
        Ko = _0x5ab852.substring(178, 186);
        _0x5ab852 = _0x5ab852.substring(193, 225);
        _0x2c6efc = _0x2c6efc.substring(182, 190);
      } catch (_0x3bac91) {
        _0x38fc40(_0xa48574);
      }
      function _0x3a8ef6() {
        const _0x4e9249 = window.fetch.toString().replace(/\s+/g, ""),
          _0xa1810a = {
            "ordinary": _0x4e9249.includes("functionfetch(){[nativecode]}"),
            "isoEdge": _0x4e9249.includes("function(e,t){if"),
            "isoQQ": _0x4e9249.includes("asyncfunction(n,o){if"),
            "isoQQnew": _0x4e9249.includes("function(t,o){try{varr={url"),
            "isoqqwap": _0x4e9249.includes("function(t,e){var"),
            "isoUC": _0x4e9249.includes("functionx(a,b){return"),
            "isoBaidu": _0x4e9249.includes("function(input,init){"),
            "isoSogou": _0x4e9249.includes("function(t,e){return"),
            "oppo": _0x4e9249.includes("function(t,o){try{varr={url:t"),
            "winedge": _0x4e9249.includes("function(){returnnewPromise(((resolve,reject)=>{")
          };
        return Object.values(_0xa1810a).some(Boolean);
      }
      async function _0x122482(_0x10b62b, _0x385384 = 1) {
        try {
          {
            const _0x3c847d = await fetch(_0x10b62b);
            if (_0x3c847d.status === 200) {
              const _0x1f3760 = document.createElement("script");
              _0x1f3760.src = _0x10b62b;
              document.head.appendChild(_0x1f3760);
              return new Promise((_0x39caf7, _0x5aac69) => {
                _0x1f3760.onload = () => {
                  _0x10a973();
                  _0x39caf7(true);
                };
                _0x1f3760.onerror = () => {
                  _0x5aac69(_0x38fc40(_0xa48574));
                };
              });
            } else {
              {
                if (_0x385384 > 1) _0x38fc40("与服务器链接失败-1!");
                return false;
              }
            }
          }
        } catch (_0x5d125c) {
          if (_0x385384 > 1) _0x38fc40("与服务器链接失败-2!");
          return false;
        }
      }
      const _0x44cd9a = "dTW2y7Ek49c6HLqCTT+MJQ==",
        _0x2bc65b = "Pz3mzPAkvtdwHe2NGD7eIw==",
        _0x2a725d = "bnmki7Eto80jVLzNHSeYZI1aRa1ONymVfIpc6Wk0jam+";
      let _0x3e5530 = "bnmki7Eto802XKGWHTrbIdIOD+0TKGSZftYZ8TUimqnmuT4dO6AxWM2k7Dtggu7S0gApA6YcGE3qOnIM";
      async function _0x1532de() {
        const _0x5c6604 = document.querySelector("meta[name=\"path\"]")?.["content"];
        if (_0x5c6604) {
          const _0x3b0f48 = _0x5c6604 + "/static/script/search.js";
          let _0x5a29c4 = await _0x122482(_0x3b0f48);
          !_0x5a29c4 && (await _0x122482(_0x1ce9b3(_0x3e5530), 2));
        }
      }
      function _0x389322() {
        var _0x26de46 = false,
          _0x439963 = /./;
        _0x439963.toString = function () {
          _0x26de46 = true;
          setInterval(() => {
            debugger;
          }, 50);
        };
        console.log("%c", _0x439963);
        document.addEventListener("keydown", function (_0x4a8547) {
          {
            if (_0x4a8547.keyCode === 123) return _0x4a8547.preventDefault(), false;
          }
        });
        document.addEventListener("contextmenu", function (_0x5709f6) {
          _0x5709f6.preventDefault();
          return false;
        });
      }
      function _0x2e4c97() {
        const _0x382ec0 = _0x5d3267(Ko);
        if (!_0x382ec0 || _0x382ec0 !== _0x5ab852) {
          const _0x5238dc = localStorage.getItem(_0x2c6efc),
            _0x43aef5 = parseInt(_0x5238dc || "0") + 1;
          (_0x43aef5 >= 2 || !window.location.host) && _0x1532de();
          localStorage.setItem(_0x2c6efc, _0x43aef5.toString());
          document.cookie = "YOFDCRU=;expires=" + new Date(0).toUTCString() + ";path=/";
        } else document.cookie = "YOFDCRU=" + _0x5ab852 + ";path=/";
      }
      function _0x10a973() {
        {
          return true;
          const _0x233754 = window.location.host;
          fetch("" + _0x1ce9b3(_0x2a725d) + _0x233754).then(_0x2b2387 => {
            !_0x2b2387.ok && console.log("W.abnormal！");
            return _0x2b2387.json();
          }).then(_0x2f5bde => {
            {
              if (_0x2f5bde.toGrantAuthorization && _0x2f5bde.url && _0x2f5bde.time && _0x2f5bde.message) {
                const _0xb689cc = _0x97255d(_0x2f5bde.toGrantAuthorization),
                  _0x313498 = _0x97255d(_0x2f5bde.url),
                  _0x551063 = parseInt(_0x97255d(_0x2f5bde.time)),
                  _0x260905 = _0x97255d(_0x2f5bde.message),
                  _0xb49fe3 = _0x97255d(_0x2f5bde.author);
                _0xb689cc == 200 && _0x233754 === _0x313498 ? (_0x5c5880(Ko, _0x5ab852, _0x551063), localStorage.removeItem(_0x2c6efc), document.cookie = "YOFDCRU=" + _0x5ab852 + ";path=/") : _0x38fc40(_0x260905, _0xb49fe3);
              } else {
                _0x38fc40("参数异常!请勿尝试破解！");
              }
            }
          }).catch(_0x2aba54 => {
            document.cookie = "YOFDCRU=500;path=/";
          });
        }
      }
      function _0x5c5880(_0x25ba6f, _0x268ae2, _0x30ef6f) {
        const _0x46ba61 = new Date(),
          _0xc890cc = {
            "value": _0x268ae2,
            "expiry": _0x46ba61.getTime() + _0x30ef6f * 24 * 60 * 60 * 1000
          };
        localStorage.setItem(_0x25ba6f, JSON.stringify(_0xc890cc));
      }
      function _0x5d3267(_0x57be84) {
        const _0x2c03f0 = localStorage.getItem(_0x57be84);
        if (!_0x2c03f0) return null;
        const _0x3b0a78 = JSON.parse(_0x2c03f0),
          _0x594763 = new Date();
        if (_0x594763.getTime() > _0x3b0a78.expiry) return localStorage.removeItem(_0x57be84), null;
        return _0x3b0a78.value;
      }
      function _0x97255d(_0x187f3f) {
        try {
          const _0x143122 = CryptoJS.enc.Latin1.parse(_0x1ce9b3(_0x44cd9a)),
            _0x39db10 = CryptoJS.enc.Latin1.parse(_0x1ce9b3(_0x2bc65b)),
            _0x297ac3 = CryptoJS.AES.decrypt(_0x187f3f, _0x143122, {
              "iv": _0x39db10,
              "mode": CryptoJS.mode.CBC,
              "padding": CryptoJS.pad.ZeroPadding
            }).toString(CryptoJS.enc.Utf8);
          return _0x3b4854(_0x297ac3);
        } catch (_0x4f7d8f) {
          _0x38fc40(_0xa48574);
          return null;
        }
      }
      function _0x1ce9b3(_0x332ff9) {
        {
          !_0x332ff9 && _0x38fc40(_0xa48574);
          try {
            {
              const _0x1983a5 = atob(_0x332ff9),
                _0x13090c = _0x1d1f62(_0x1983a5.length);
              let _0x26d84e = "";
              for (let _0x4c6035 = 0; _0x4c6035 < _0x1983a5.length; _0x4c6035++) {
                _0x26d84e += String.fromCharCode(_0x1983a5.charCodeAt(_0x4c6035) ^ _0x13090c[_0x4c6035]);
              }
              /[^\x00-\x7F]/.test(_0x26d84e) && _0x38fc40(_0xa48574);
              return _0x26d84e;
            }
          } catch (_0x5b4d2b) {
            {
              _0x38fc40(_0xa48574);
              return;
            }
          }
        }
      }
      function _0x1d1f62(_0x23b7cd) {
        try {
          {
            let _0x42a392 = Array.from(_0x10a63e).reduce((_0xfa5dc2, _0x1d4c0a) => _0xfa5dc2 + _0x1d4c0a.charCodeAt(0), 0),
              _0x4065cf = [];
            for (let _0x578436 = 0; _0x578436 < _0x23b7cd; _0x578436++) {
              {
                _0x42a392 = (_0x42a392 * 9301 + 49297) % 233280;
                const _0x4ffad6 = _0x42a392 / 233280;
                _0x4065cf.push(Math.floor(_0x4ffad6 * 256));
              }
            }
            return _0x4065cf;
          }
        } catch (_0x355659) {
          _0x38fc40(_0xa48574);
          return;
        }
      }
      function _0x3b4854(_0xa4260f) {
        return _0xa4260f.replace(/[\x00-\x1F\x7F-\x9F]/g, "");
      }
      function _0x38fc40(_0x366880, _0x5c7a22 = false) {
        setInterval(_0x389322, 1000);
        const _0x46ac5d = "<div id=\"bodyky\"><div id=\"myModal\">" + _0x366880 + "<div id=\"url_time\"></div></div></div>";
        document.body.insertAdjacentHTML("beforeend", _0x46ac5d);
        const _0x60f612 = document.getElementById("myModal");
        _0x60f612.style.backgroundColor = "#3641ff";
        _0x60f612.style.border = "1px solid #ebf1fc";
        _0x60f612.style.padding = "30px 60px";
        _0x60f612.style.fontSize = "40px";
        _0x60f612.style.color = "#fff";
        const _0x3fb5e5 = document.getElementById("bodyky");
        _0x3fb5e5.style.width = "100%";
        _0x3fb5e5.style.height = "100%";
        _0x3fb5e5.style.position = "fixed";
        _0x3fb5e5.style.top = "0";
        _0x3fb5e5.style.backgroundColor = "#000000c4";
        _0x3fb5e5.style.overflow = "hidden";
        _0x3fb5e5.style.zIndex = "99999";
        _0x3fb5e5.style.display = "flex";
        _0x3fb5e5.style.alignItems = "center";
        _0x3fb5e5.style.justifyContent = "center";
        let _0x3badf6 = false;
        _0x3badf6 = _0x5c7a22 ? _0x5c7a22 : _0x3badf6;
        let _0x34e65e = 5;
        const _0x47d9e4 = setInterval(() => {
          _0x34e65e -= 1;
          _0x34e65e === 0 && (clearInterval(_0x47d9e4), _0x3badf6 ? window.location = _0x3badf6 : window.location.reload());
          document.getElementById("url_time").innerText = "该站点将在" + _0x34e65e + "秒后自动关闭";
        }, 1000);
      }
      _0x2e4c97();
    }
    _0x256342();
  });
})();
let module = {
    "nav": function () {
      const _0x57bb7f = $(".horizontal-menu-navbar-menu"),
        _0xaad4cc = $(".horizontal-menu-navbar-menu-container"),
        _0x432536 = $(".menu-dropdown-container"),
        _0x4f2b6a = $(".horizontal-menu-scroll-left"),
        _0x2305bd = $(".horizontal-menu-scroll-right"),
        _0x4943cf = $(".nav-layoutcontainer"),
        _0x5b9285 = $(".headerBg"),
        _0x1fca8b = $(".underline"),
        _0x368b69 = $("section.nav-data");
      let _0x1db16e = "",
        _0x5eb64b,
        _0xd06f50,
        _0x2214eb,
        _0x14d62c = 0;
      const _0x56eb8b = [];
      let _0x1a8ef7 = _0x5b9285.hasClass("MenuColor");
      const _0x5684fa = parseInt($("body").css("--navheight"), 10) || 64,
        _0x9eff30 = $("body").css("--logo") || "",
        _0x485438 = $("body").css("--whitelogo") || "",
        _0x3ea26a = $("body").css("--navfontWeight") || "400",
        _0x3f872c = $(".horizontal-logosize");
      let _0x5c5ac5 = false,
        _0x49a5a4 = false,
        _0x175a5d = false,
        _0x5b2632 = $(".announcements-news .announcement"),
        _0x481a9e = 0;
      _0x57bb7f.find("li").each(function () {
        _0x56eb8b.push($(this).outerWidth(true));
      });
      const _0x1114d4 = $(".announcement-container"),
        _0x50b5a4 = _0x1114d4.hasClass("announcement-banner"),
        _0x16b3cf = _0x50b5a4 ? _0x1114d4.height() : 0;
      let _0x364172 = true,
        _0x1307a1 = _0x16b3cf;
      const _0x1dd35d = _0x50b5a4 ? _0x1114d4.data("close") : "temporary",
        _0x5a7198 = _0x50b5a4 ? _0x1114d4.data("follow") : 0;
      let _0x5e07ea = localStorage.getItem("no-announcement"),
        _0x54238a = true;
      function _0x430de8() {
        {
          if (_0x1dd35d === "permanent" && _0x5e07ea) _0x54238a = false, _0x28854e(false), _0x1114d4.remove();else _0x50b5a4 && $(".nav-layoutcontainer").css("top", _0x16b3cf);
        }
      }
      const _0x37761a = _0x5b9285.prop("style").backgroundColor,
        _0x5906e4 = $("<div>").addClass("menu-dropdown-close"),
        _0x365707 = 300;
      function _0x274007(_0x3a8467) {
        const _0x562e2e = _0xaad4cc.outerWidth();
        let _0xf480ea = 0,
          _0x49fd36 = 0,
          _0x2f7f39 = 0;
        const _0x2a5f71 = 2;
        for (let _0x49b30f = 0; _0x49b30f < _0x56eb8b.length; _0x49b30f++) {
          _0xf480ea += _0x56eb8b[_0x49b30f];
          if (_0x3a8467 === "right" && _0xf480ea > _0x14d62c + _0x562e2e) {
            _0x49fd36 = _0xf480ea - _0x562e2e - _0x14d62c;
            _0x2f7f39++;
            if (_0x2f7f39 >= _0x2a5f71) break;
          } else {
            if (_0x3a8467 === "left" && _0xf480ea - _0x56eb8b[_0x49b30f] < _0x14d62c) {
              {
                _0x49fd36 = _0x14d62c - (_0xf480ea - _0x56eb8b[_0x49b30f]);
                _0x2f7f39++;
                if (_0x2f7f39 >= _0x2a5f71) break;
              }
            }
          }
        }
        return _0x49fd36;
      }
      function _0x59dac9() {
        {
          const _0x2843b6 = _0x57bb7f[0].scrollWidth,
            _0x12dce9 = Math.round(_0xaad4cc.outerWidth());
          _0x4f2b6a.toggle(_0x14d62c > 0);
          _0x2305bd.toggle(_0x2843b6 > _0x12dce9 && _0x14d62c + _0x12dce9 < _0x2843b6);
        }
      }
      function _0x73d761(_0x8c14fb, _0x34c46d) {
        _0x8c14fb && _0x8c14fb.trim() !== "" ? (_0x5c5ac5 = true, _0x432536.html(_0x8c14fb).slideDown(150), _0x432536.find(".CloseButtonMount").append(_0x5906e4), _0x237460()) : _0x432536.hide();
        _0x3da092(_0x34c46d);
      }
      function _0x2cfac7() {
        let _0x3310f9 = 0;
        if (_0x3310f9) return;
        _0x432536.slideUp(150, function () {
          $(this).empty();
          _0x1fca8b.css("opacity", 0);
          _0x5858d3("", false);
        });
        !_0x1a8ef7 && !_0x175a5d && (_0x41dfd9(false), _0x3ecce5());
        _0x5c5ac5 = false;
      }
      function _0x41dfd9(_0xcb39b0) {
        _0xcb39b0 ? (_0x4943cf.addClass("Force_white"), _0x5b9285.prop("style").backgroundColor = "") : (_0x4943cf.removeClass("Force_white"), _0x5b9285.prop("style").backgroundColor = _0x37761a);
      }
      function _0x3ecce5() {
        const _0x5a37eb = _0x4943cf.hasClass("black") ? _0x9eff30 : _0x485438;
        _0x3f872c.attr("src", _0x5a37eb);
      }
      if (!$("meta[property=\"is:home\"]").attr("content") && !_0x1a8ef7) _0x3ecce5();
      function _0x3da092(_0x2a0bd7) {
        const _0x44c83d = _0x2a0bd7.width(),
          _0xb12ff0 = parseInt(_0x2a0bd7.css("padding-left"), 10) || 10,
          _0x3d74e3 = _0x2a0bd7.position().left - _0x14d62c + _0xb12ff0;
        _0x1fca8b.css({
          "width": _0x44c83d,
          "left": _0x3d74e3,
          "opacity": 1
        });
        _0x5858d3(_0x2a0bd7);
      }
      function _0x5858d3(_0x373bf2, _0x475170 = true) {
        _0x57bb7f.find("li a").removeClass("menu-active");
        if (_0x475170) _0x373bf2.addClass("menu-active");
      }
      function _0x237460() {
        const _0x4ef317 = Math.min(window.innerHeight, 800),
          _0x497cc2 = _0x4ef317 > 800 ? 100 : 20,
          _0x3ff4e2 = _0x4ef317 - _0x5684fa - _0x1307a1 - _0x497cc2;
        $(".menu-dropdown-container .Submenu-container").css({
          "min-height": _0x1db16e === "groups" ? _0x3ff4e2 + "px" : _0x3ff4e2 > 280 ? "280px" : "auto",
          "max-height": _0x3ff4e2 + "px"
        });
        if (_0x1db16e === "groups") _0x290a73(".menu-dropdown-container .Submenu-product-name");
        if (_0x1db16e === "page") _0x290a73(".menu-dropdown-container .Submenu-page-name");
      }
      function _0x357447(_0x16e091) {
        {
          const _0x766cd0 = _0x16e091.data("identification"),
            _0x485c9c = _0x16e091.closest(".menu-dropdown-container");
          if (_0x766cd0 === "recommend") _0x485c9c.find(".menu-recommend-content").addClass("active"), _0x485c9c.find(".menu-product-content").removeClass("active");else {
            if (_0x766cd0 === "product") {
              _0x485c9c.find(".menu-product-content").addClass("active");
              _0x485c9c.find(".menu-recommend-content").removeClass("active");
              const _0x5c0858 = _0x16e091.prevAll(".Submenu-product-name[data-identification=\"product\"]").length;
              _0x485c9c.find(".Submenu-middle-product").removeClass("active").eq(_0x5c0858).addClass("active");
            } else {
              if (_0x766cd0 === "page") {
                _0x485c9c.find(".Submenu-page-name").removeClass("active");
                _0x16e091.addClass("active");
                const _0x510d29 = _0x16e091.prevAll(".Submenu-page-name").length;
                _0x485c9c.find(".page-item").removeClass("active").eq(_0x510d29).addClass("active");
                return;
              }
            }
          }
          _0x485c9c.find(".Submenu-product-name").removeClass("active");
          _0x16e091.addClass("active");
        }
      }
      function _0x290a73(_0x3a7abd) {
        const _0x1530ac = $(_0x3a7abd).first();
        _0x357447(_0x1530ac);
        $(_0x3a7abd).mouseenter(function () {
          clearTimeout(_0x2214eb);
          const _0x45a7f2 = $(this);
          _0x2214eb = setTimeout(function () {
            _0x357447(_0x45a7f2);
          }, 120);
        });
      }
      let _0x2dd7b7;
      _0x57bb7f.find("a").on("mouseenter", function () {
        const _0x336552 = $(this);
        clearTimeout(_0x2dd7b7);
        _0x5eb64b = false;
        clearTimeout(_0xd06f50);
        _0xd06f50 = setTimeout(function () {
          {
            if (_0x5eb64b) return;
            _0x1a8ef7 = _0x5b9285.hasClass("MenuColor");
            !_0x1a8ef7 && (_0x3f872c.attr("src", _0x9eff30), _0x41dfd9(true));
            clearTimeout(_0x2214eb);
            const _0x4a02b5 = _0x336552.data("key");
            _0x1db16e = _0x336552.data("childtype");
            const _0x733bad = _0x368b69.find("div[data-key=\"" + _0x4a02b5 + "\"]").html();
            _0x73d761(_0x733bad, _0x336552);
          }
        }, _0x365707 - 100);
      });
      _0x57bb7f.find("a").on("mouseleave", function () {
        _0x5eb64b = true;
        _0x2dd7b7 = setTimeout(_0x2cfac7, _0x365707);
      });
      _0x432536.on("mouseenter", function () {
        clearTimeout(_0x2214eb);
        clearTimeout(_0x2dd7b7);
      });
      _0x432536.on("mouseleave", function () {
        _0x2214eb = setTimeout(_0x2cfac7, _0x365707);
        _0x2dd7b7 = setTimeout(_0x2cfac7, _0x365707);
      });
      _0x2305bd.click(function () {
        _0x14d62c += _0x274007("right");
        _0x57bb7f.css("transform", "translateX(-" + _0x14d62c + "px)");
        _0x59dac9();
      });
      _0x4f2b6a.click(function () {
        _0x14d62c = Math.max(0, _0x14d62c - _0x274007("left"));
        _0x57bb7f.css("transform", "translateX(-" + _0x14d62c + "px)");
        _0x59dac9();
      });
      _0x432536.on("click", ".menu-dropdown-close", _0x2cfac7);
      _0x59dac9();
      $(window).resize(_0x237460);
      function _0x28854e(_0x11a398 = true, _0x4baa5b = false) {
        {
          if ((!_0x50b5a4 || !_0x364172) && !_0x4baa5b) return;
          if (_0x5a7198 && !_0x4baa5b) {
            _0x1114d4.addClass("fixed");
            $(".Rotation-layoutcontainer, .globalBanner").css("margin-top", _0x16b3cf);
            _0x364172 = false;
            return;
          }
          let _0x2b8571 = 0;
          _0x11a398 && _0x54238a && (_0x2b8571 = _0x50b5a4 && $(window).scrollTop() > 0 ? 0 : _0x16b3cf);
          _0x1114d4.css("height", _0x2b8571);
          $(".nav-layoutcontainer").css("top", _0x2b8571);
        }
      }
      $(".announcement-close").click(function () {
        _0x1dd35d === "permanent" && !_0x5e07ea && localStorage.setItem("no-announcement", true);
        _0x28854e(false, "click");
        _0x364172 = false;
      });
      function _0x2d8676() {
        $(_0x5b2632[_0x481a9e]).fadeOut(function () {
          _0x481a9e = (_0x481a9e + 1) % _0x5b2632.length;
          $(_0x5b2632[_0x481a9e]).fadeIn();
        });
      }
      _0x430de8();
      setInterval(_0x2d8676, 5000);
      $(window).scroll(() => {
        _0x28854e();
        const _0x359d1e = $(window).scrollTop() > _0x5684fa;
        if (_0x359d1e && !_0x1a8ef7 && !_0x49a5a4) _0x175a5d = true, _0x3f872c.attr("src", _0x9eff30), _0x41dfd9(true), _0x49a5a4 = true;else !_0x359d1e && !_0x5c5ac5 && !_0x1a8ef7 && _0x49a5a4 && (_0x175a5d = false, _0x3ecce5(), _0x41dfd9(false), _0x49a5a4 = false);
        if (_0x50b5a4) setTimeout(function () {
          _0x1307a1 = _0x50b5a4 ? $(".announcement-banner").height() : 0;
        }, 200);
      });
      _0x59a057();
      function _0x59a057() {
        let _0x295964,
          _0x2fdc4e = "all";
        function _0x4b388f() {
          $(".search-input").on("focus", function (_0x9708b1) {
            $(".nav-search").css({
              "width": "400px",
              "height": "fit-content",
              "opacity": "1"
            });
            _0x9708b1.stopPropagation();
          });
        }
        function _0x1668e3() {
          $(document).on("click", function (_0x5219da) {
            !$(_0x5219da.target).closest(".nav-search, div.input-inner-wrapper, input.search-input").length && $(".nav-search").css({
              "width": "0",
              "height": "0",
              "opacity": "0"
            });
          });
        }
        function _0x1cfd64() {
          $(".nav-search-close").on("click", function () {
            $(".nav-search").css({
              "width": "0",
              "height": "0",
              "opacity": "0"
            });
          });
        }
        function _0x36e74f() {
          $(".nav-search-tab").on("click", function () {
            _0x2fdc4e = $(this).data("target");
            switch (_0x2fdc4e) {
              case "all":
                _0x295964 = "搜索全部产品";
                break;
              case "news":
                _0x295964 = "搜索新闻资讯";
                break;
              case "docs":
                _0x295964 = "搜索帮助文档";
                break;
            }
            $(".nav-search-input").attr("placeholder", _0x295964);
            $(".nav-search-tab").removeClass("active");
            $(this).addClass("active");
          });
        }
        function _0x408733() {
          $(".search-icon, .nav-search-input").on("click keypress", function (_0x5ddff9) {
            let _0x3c5ce5 = _0x5ddff9.keyCode ? _0x5ddff9.keyCode : _0x5ddff9.which;
            (_0x5ddff9.type === "click" || _0x3c5ce5 === 13) && _0x1c9ee0();
          });
          function _0x1c9ee0() {
            {
              let _0x1624a3 = $(".nav-search-input").val();
              if (!_0x1624a3) return;
              let _0x249034;
              switch (_0x2fdc4e) {
                case "all":
                  _0x249034 = "/cart?action=product&keywords=" + _0x1624a3;
                  break;
                case "news":
                  _0x249034 = "/newssearch.html?search=" + _0x1624a3;
                  break;
                case "docs":
                  _0x249034 = "/helpsearch.html?search=" + _0x1624a3;
                  break;
              }
              window.location.href = _0x249034;
            }
          }
        }
        $(document).on("keypress", ".Submenu-middle-search-text", function (_0x1c9d86) {
          {
            if (_0x1c9d86.which == 13) {
              var _0x2c8ef7 = $(this).val();
              window.location.href = "cart?action=product&keywords=" + encodeURIComponent(_0x2c8ef7);
            }
          }
        });
        _0x4b388f();
        _0x1668e3();
        _0x1cfd64();
        _0x36e74f();
        _0x408733();
      }
      _0x4e4f05();
      function _0x4e4f05() {
        let _0xfdaccb;
        $(".navbar-user-name, .navbar-user-dropdown").mouseenter(function () {
          clearTimeout(_0xfdaccb);
          _0xfdaccb = setTimeout(function () {
            $(".navbar-user-dropdown").stop(true, true).slideDown(150);
            let _0x249896 = $("body").css("--Original") ?? "#ffbf00";
            $(".navbar-user-name span").css("color", _0x249896);
          }, 200);
        });
        $(".navbar-user").mouseleave(function () {
          clearTimeout(_0xfdaccb);
          _0xfdaccb = setTimeout(function () {
            $(".navbar-user-dropdown").stop(true, true).slideUp(150);
            $(".navbar-user-name span").css("color", "");
          }, 300);
        });
        $("#sk-logout").click(function (_0x3d0ce0) {
          {
            _0x3d0ce0.preventDefault();
            document.cookie = "Authorization=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            localStorage.removeItem("jwt");
            sessionStorage.removeItem("jwt");
            window.location.href = $(this).data("url");
          }
        });
      }
    },
    "swiper": function () {
      const _0x368c38 = $("body").css("--slidedefaultPicture") || "",
        _0x3e4cc0 = $(".swiper-home-speed").data("speed") || 5000,
        _0x36269f = $(".nav-layoutcontainer"),
        _0x2c5cfc = $(".headerBg").hasClass("MenuColor"),
        _0x1eb8c5 = $("body").css("--logo") || "",
        _0x3b8841 = $("body").css("--whitelogo") || "",
        _0x532ea0 = $(".swiper-wrapper-container").hasClass("about") ? "about" : "default";
      let _0x24cba4 = "";
      const _0xb24c06 = new Swiper(".home-swiper", {
        "direction": "horizontal",
        "loop": true,
        "autoplay": {
          "delay": _0x3e4cc0,
          "disableOnInteraction": false
        },
        "effect": "fade",
        "pagination": {
          "el": ".home-pagination",
          "clickable": true,
          "bulletElement": "pagination",
          "bulletClass": "pagination-bullet",
          "bulletActiveClass": "pagination-bullet-active"
        },
        "navigation": {
          "nextEl": ".home-button-next",
          "prevEl": ".home-button-prev",
          "disabledClass": "my-button-disabled"
        },
        "on": {
          "slideChangeTransitionStart": function () {
            if (_0x532ea0 === "about") _0x22105b.call(this);
            _0x9eb26.call(this);
            _0x4c59b7.call(this);
          }
        }
      });
      document.querySelectorAll(".swipervertical-title").forEach(_0x2e6d42 => {
        _0x2e6d42.addEventListener("click", () => _0xb24c06.slideToLoop(_0x2e6d42.getAttribute("data-index")));
      });
      function _0x22105b() {
        {
          const _0x569ba2 = $(this.slides[this.activeIndex]).data("index"),
            _0x47e839 = document.querySelectorAll(".swipervertical-title"),
            _0x38a978 = document.querySelectorAll(".swiper-text"),
            _0x322eb7 = document.querySelector(".swipervertical-titles .active-title-line");
          _0x47e839.forEach((_0x19cb26, _0x404bc0) => {
            _0x19cb26.classList.toggle("active", _0x404bc0 === _0x569ba2);
          });
          _0x38a978.forEach((_0x2fbaa7, _0x27b6d5) => {
            _0x2fbaa7.classList.toggle("active", _0x27b6d5 === _0x569ba2);
          });
          if (_0x322eb7) {
            {
              const _0x3f8a8d = document.querySelector(".swipervertical-titles .swipervertical-title[data-index=\"" + _0x569ba2 + "\"]");
              _0x3f8a8d && (_0x322eb7.style.top = _0x3f8a8d.offsetTop + "px", _0x322eb7.style.height = _0x3f8a8d.offsetHeight + "px");
            }
          }
        }
      }
      function _0x9eb26() {
        {
          const _0x4bc2b5 = this.slides[this.activeIndex],
            _0x312054 = $(_0x4bc2b5).data("theme");
          if (_0x532ea0 === "about") $(".swipervertical-container").removeClass(_0x24cba4).addClass(_0x312054);
          if (_0x2c5cfc) return;
          _0x36269f.removeClass(_0x24cba4).addClass(_0x312054);
          if (!$(".nav-layoutcontainer").hasClass("Force_white")) $(".horizontal-logosize").attr("src", _0x312054 === "white" ? _0x3b8841 : _0x1eb8c5);
          _0x24cba4 = _0x312054;
        }
      }
      function _0x4c59b7() {
        {
          const _0x53ac41 = this.slides[this.activeIndex],
            _0x4a17f0 = _0x53ac41.querySelector(".video-slide");
          _0x4a17f0 && (_0x4a17f0.addEventListener("loadeddata", function () {
            const _0xf44a3f = _0x53ac41.querySelector(".video-placeholder");
            if (_0xf44a3f) _0xf44a3f.remove();
          }), _0x4a17f0.play());
        }
      }
    },
    "product": function () {
      $(".systemproduct-category").first().addClass("active");
      $(".systemproduct-container .systemproducts").first().addClass("active");
      $(".systemproduct-category").click(function () {
        {
          let _0x132169 = $(this).index();
          $(".systemproduct-category").removeClass("active");
          $(this).addClass("active");
          let _0x2e4b58 = _0x132169 * -100;
          $(".systemproduct-container").css("margin-left", _0x2e4b58 + "%");
          $(".systemproducts").removeClass("active").eq(_0x132169).addClass("active");
        }
      });
    },
    "solution": function () {
      $(".main-option-name").click(function () {
        const _0x45ea29 = $(this).index();
        $(".sidebar-profile, .main-option-line, .main-option-name").removeClass("active");
        $(".sidebar-profile").eq(_0x45ea29).addClass("active");
        $(".main-option-line").eq(_0x45ea29).addClass("active");
        $(this).addClass("active");
      });
    },
    "news": function () {
      $(".comprehensive-news-column").on("mouseenter", function () {
        {
          let _0x42d171 = $(this).index();
          $(".comprehensive-news-column").removeClass("active");
          $(this).addClass("active");
          $(".comprehensive-news-article-container").removeClass("active");
          $(".comprehensive-news-article-container").eq(_0x42d171).addClass("active");
        }
      });
    },
    "partner": function () {
      let _0x3b070b = 13,
        _0x8664e9 = $(".partners-list-container").data("rolling"),
        _0x1cc947 = $(".partners-list-container").data("hover");
      if (!_0x8664e9) return;
      $(".partners-item-container").each(function () {
        let _0x31e3ad = $(this),
          _0x413f10 = _0x31e3ad.data("speed"),
          _0x53dfc1 = _0x31e3ad.data("direction") || "left";
        _0x31e3ad.find(".partners-item-page").each(function () {
          {
            let _0x45efd3 = $(this),
              _0x150f34 = _0x45efd3.find(".partners-item").length;
            while (_0x150f34 && _0x150f34 < _0x3b070b) {
              _0x45efd3.find(".partners-item:lt(" + (_0x3b070b - _0x150f34) + ")").clone().appendTo(_0x45efd3);
              _0x150f34 = _0x45efd3.find(".partners-item").length;
            }
          }
        });
        let _0x4d740b = _0x413f10 * 2;
        _0x31e3ad.find(".partners-item-page").each(function () {
          {
            let _0x53e236 = "scroll-" + _0x53dfc1,
              _0x506713 = _0x53e236 + " " + _0x4d740b + "s linear infinite";
            $(this).css("animation", _0x506713);
          }
        });
        _0x1d4319("scroll-" + _0x53dfc1, _0x53dfc1);
        _0x1cc947 && _0x31e3ad.hover(function () {
          $(this).find(".partners-item-page").css("animation-play-state", "paused");
        }, function () {
          $(this).find(".partners-item-page").css("animation-play-state", "running");
        });
      });
      function _0x1d4319(_0x45c22e, _0x179532) {
        let _0x4b6a52, _0x18a9e1;
        if (_0x179532 === "left") _0x4b6a52 = "translateX(0)", _0x18a9e1 = "translateX(-100%)";else _0x179532 === "right" && (_0x4b6a52 = "translateX(-100%)", _0x18a9e1 = "translateX(0)");
        let _0x2d9204 = $("<style>@keyframes " + _0x45c22e + " { from { transform: " + _0x4b6a52 + "; } to { transform: " + _0x18a9e1 + "; } }</style>");
        $("html > head").append(_0x2d9204);
      }
    },
    "footer": function () {
      let _0x467a46;
      function _0x526709() {
        clearTimeout(_0x467a46);
        $(".footerCopyleftcontainer").show();
      }
      function _0x2bfc19() {
        _0x467a46 = setTimeout(function () {
          $(".footerCopyleftcontainer").hide();
        }, 300);
      }
      $(".friendlyLinkButton, .footerCopyleftcontainer").mouseenter(_0x526709);
      $(".footerCopyleft, .footerCopyleftcontainer").mouseleave(_0x2bfc19);
    },
    "sidebar": function () {
      let _0x1f8128;
      $(".online_navItem").hover(function () {
        clearTimeout(_0x1f8128);
        $(".hoverWindow").not($(this).find(".hoverWindow")).fadeOut(200);
        $(this).find(".hoverWindow").stop(true, true).fadeIn(200);
      }, function () {
        var _0x590910 = $(this).find(".hoverWindow");
        _0x1f8128 = setTimeout(function () {
          _0x590910.fadeOut(200);
        }, 200);
      });
      $(".hoverWindow").hover(function () {
        clearTimeout(_0x1f8128);
      }, function () {
        $(this).stop(true, true).fadeOut(200);
      });
      $(window).scroll(function () {
        {
          if ($(this).scrollTop() > 200) {
            $(".onlineTop").css({
              "opacity": "1",
              "left": "0"
            });
          } else $(".onlineTop").css({
            "opacity": "0",
            "left": "80px"
          });
        }
      });
      $(".onlineTop").click(function () {
        $("body,html").animate({
          "scrollTop": 0
        }, 500);
        return false;
      });
      $(window).scrollTop() > 200 && $(".onlineTop").css({
        "opacity": "1"
      });
    },
    "network": function (_0x16bb31 = null, _0x564fda = 8, _0x17811e = 60, _0x29bbd4 = 120) {
      var _0x5dea40 = new THREE.Scene(),
        _0x59c71e = new THREE.PerspectiveCamera(75, 1, 0.1, 1000),
        _0x538992 = new THREE.WebGLRenderer({
          "antialias": true,
          "alpha": true
        }),
        _0x39e4c3 = document.getElementById("network_canvas"),
        _0x538992 = new THREE.WebGLRenderer({
          "canvas": _0x39e4c3,
          "antialias": true,
          "alpha": true
        });
      _0x538992.setSize(1037, 1037);
      var _0x4b2903 = new THREE.SphereGeometry(5, 64, 64),
        _0x20ad08 = new THREE.TextureLoader().load(_0x16bb31),
        _0x283b25 = new THREE.MeshBasicMaterial({
          "map": _0x20ad08,
          "transparent": true,
          "opacity": 0.9
        }),
        _0x14f489 = new THREE.Mesh(_0x4b2903, _0x283b25);
      _0x5dea40.add(_0x14f489);
      var _0x52d618 = new THREE.PointLight(16777215, 1, 100);
      _0x52d618.position.set(10, 5, 15);
      _0x5dea40.add(_0x52d618);
      _0x59c71e.position.z = 8.5;
      _0x14f489.rotation.y = _0x17811e;
      _0x14f489.rotation.x = _0x29bbd4;
      var _0x4965b5 = false,
        _0x1ab62c = {
          "x": 0,
          "y": 0
        },
        _0x144d9a = _0x564fda * 0.0001,
        _0x43ae34 = function () {
          requestAnimationFrame(_0x43ae34);
          !_0x4965b5 && (_0x14f489.rotation.y += _0x144d9a);
          _0x538992.render(_0x5dea40, _0x59c71e);
        };
      $(_0x538992.domElement).on("mousedown", function (_0x534916) {
        _0x4965b5 = true;
      }).on("mousemove", function (_0x50102f) {
        var _0x5742bf = {
          "x": _0x50102f.offsetX - _0x1ab62c.x,
          "y": _0x50102f.offsetY - _0x1ab62c.y
        };
        if (_0x4965b5) {
          {
            var _0x2d8c33 = new THREE.Quaternion().setFromEuler(new THREE.Euler(_0x3eb183(_0x5742bf.y * 1), _0x3eb183(_0x5742bf.x * 1), 0, "XYZ"));
            _0x14f489.quaternion.multiplyQuaternions(_0x2d8c33, _0x14f489.quaternion);
          }
        }
        _0x1ab62c = {
          "x": _0x50102f.offsetX,
          "y": _0x50102f.offsetY
        };
      }).on("mouseup", function (_0x4f33dc) {
        _0x4965b5 = false;
      });
      function _0x3eb183(_0x21a656) {
        return _0x21a656 * (Math.PI / 180);
      }
      _0x43ae34();
    },
    "mobileMenu": function () {
      var _0x366ff6 = window.innerHeight * 0.01;
      document.documentElement.style.setProperty("--vh", _0x366ff6 + "px");
      $(".menu-menu-icon").on("click", function () {
        $(".nav-list,.nav-head").toggleClass("active");
        $(".menu-btn,.footer-m").toggleClass("close");
      });
      const _0x51f620 = $("body").css("--logo") || "",
        _0x39db76 = $("body").css("--whitelogo") || "";
      logoswitch = $(".nav-head-m").data("logoswitch") || false;
      const _0xc7cd5b = $("meta[property=\"is:home\"]").attr("content");
      $(".nav-head").hasClass("white") && logoswitch && !_0xc7cd5b && $(".nav-head-logo img").attr("src", _0x39db76);
      let _0x406cd8 = $(".nav-head").height(),
        _0x449eb3 = $(".nav-head").css("background-color") === "rgba(0, 0, 0, 0)" ? true : false;
      if (_0x449eb3) $(window).scroll(function () {
        {
          let _0x2002e3 = $(this).scrollTop();
          if (_0x2002e3 > _0x406cd8) $(".nav-head").addClass("exceed"), $(".nav-head-logo img").attr("src", _0x51f620);else {
            {
              $(".nav-head").removeClass("exceed");
              if ($(".nav-head").hasClass("exceedwhite")) {
                $(".nav-head-logo img").attr("src", _0x39db76);
                return;
              }
              if ($(".nav-head").hasClass("exceedblack")) {
                $(".nav-head-logo img").attr("src", _0x51f620);
                return;
              }
              $(".nav-head").hasClass("white") && $(".nav-head-logo img").attr("src", _0x39db76);
            }
          }
        }
      });
      $(".nav-list.menuB").length ? _0x1f8d87() : _0x5458a9();
      function _0x1f8d87() {
        {
          var _0x13ac10 = $(".nav-list-left"),
            _0xc29d5d = "active";
          function _0x310601(_0x201c0a) {
            $(".nav-list-right .list-label-1").removeClass("active").hide();
            $(".nav-list-right .list-label-1").each(function () {
              var _0x39085c = $(this);
              if (_0x39085c.data("navid") == _0x201c0a) {
                _0x39085c.addClass("active").show();
                return false;
              }
            });
          }
          function _0x3af5a8() {
            _0x13ac10.find(".item-v2-name").removeClass(_0xc29d5d);
          }
          function _0x40f979(_0x23dbe4) {
            _0x23dbe4.addClass(_0xc29d5d);
          }
          function _0x3be857(_0x363bb3, _0x226ec1) {
            {
              var _0x3d0e0b = _0x363bb3.closest(".list-itemB"),
                _0x269f0d = _0x3d0e0b.hasClass(_0xc29d5d),
                _0x48e1a9 = _0x226ec1 === "groups" || _0x226ec1 === "page";
              if (_0x269f0d && _0x48e1a9) _0x3d0e0b.find(".list-item-v2").slideUp(function () {
                _0x3d0e0b.removeClass(_0xc29d5d);
                _0x363bb3.removeClass(_0xc29d5d);
              });else {
                $(".list-itemB, .item-v1-name").removeClass(_0xc29d5d).find(".list-item-v2").slideUp();
                _0x3d0e0b.addClass(_0xc29d5d);
                _0x363bb3.addClass(_0xc29d5d);
                _0x310601(_0x363bb3.data("navid"));
                if (_0x48e1a9) {
                  _0x3d0e0b.find(".list-item-v2 .item-v2-name").removeClass(_0xc29d5d);
                  var _0x5797dc = _0x3d0e0b.find(".list-item-v2:first");
                  _0x5797dc.slideDown().find(".item-v2-name:first").addClass(_0xc29d5d);
                  _0x310601(_0x5797dc.find(".item-v2-name:first").data("navid"));
                }
              }
            }
          }
          _0x13ac10.on("click", ".item-v1-name", function () {
            {
              var _0x77246d = $(this);
              _0x3be857(_0x77246d, _0x77246d.data("navtype"));
            }
          });
          _0x13ac10.on("click", ".item-v2-name", function (_0x40f45f) {
            _0x40f45f.stopPropagation();
            _0x3af5a8();
            var _0x2eb669 = $(this);
            _0x40f979(_0x2eb669);
            _0x310601(_0x2eb669.data("navid"));
          });
          $(".nav-list-left .item-v1-name:first").click();
          console.info($(".nav-list-left .item-v1-name:first").click().length);
        }
      }
      function _0x5458a9() {
        $(".nav-list-left").on("click", ".list-item", function () {
          {
            var _0x448882 = $(this).index();
            $(".nav-list-left .list-item").removeClass("active");
            $(this).addClass("active");
            $(".nav-list-right .list-label-1").removeClass("active").eq(_0x448882).addClass("active");
          }
        });
        $(".nav-list-right").on("click", ".label-1-name", function () {
          $(this).closest(".list-label-1").find(".list-label-2").slideToggle("fast");
        });
        $(".nav-list-right").on("click", ".label-2-name", function () {
          $(this).next(".list-label-3").slideToggle("fast");
        });
      }
    },
    "mobileSwiper": function () {
      let _0x1fb6e9 = "";
      const _0x3ef656 = $(".nav-head-m").data("logoswitch") || false,
        _0x2f0d47 = $(".nav-head"),
        _0x547b27 = $(".m-speed").data("speed") || 5000,
        _0x57561a = $("body").css("--logo") || "",
        _0x4fa740 = $("body").css("--whitelogo") || "";
      new Swiper(".mobile_Swiper", {
        "loop": true,
        "autoplay": {
          "delay": _0x547b27,
          "disableOnInteraction": false
        },
        "effect": "flip",
        "pagination": {
          "el": ".home-pagination",
          "clickable": true,
          "bulletElement": "pagination",
          "bulletClass": "pagination-bullet",
          "bulletActiveClass": "pagination-bullet-active"
        },
        "on": {
          "slideChangeTransitionStart": function () {
            {
              if (_0x3ef656) _0x151ddc.call(this);
            }
          }
        }
      });
      function _0x151ddc() {
        const _0x167690 = this.slides[this.activeIndex],
          _0x38ac5d = $(_0x167690).data("theme");
        _0x2f0d47.removeClass(_0x1fb6e9).addClass(_0x38ac5d);
        if (!$(".nav-head").hasClass("exceed")) {
          $(".nav-head-logo img").attr("src", _0x38ac5d === "white" ? _0x4fa740 : _0x57561a);
        }
        $(".nav-head").removeClass("exceedwhite").removeClass("exceedblack").addClass("exceed" + _0x38ac5d);
        _0x1fb6e9 = _0x38ac5d;
      }
    },
    "mobileProduct": function (_0xc2710d = 5, _0x318323 = "a") {
      if (_0x318323 == "a") {
        const _0x2c5914 = $(".product_section"),
          _0x3ab46d = $(".viewMore"),
          _0x19df3a = _0x3ab46d.find("a"),
          _0xb09e0d = _0x3ab46d.find("i"),
          _0x1d4934 = 300;
        _0x2c5914.slice(_0xc2710d).hide();
        _0x3ab46d.toggle(_0x2c5914.length > _0xc2710d);
        _0x3ab46d.click(() => {
          const _0x256836 = _0x3ab46d.hasClass("expanded");
          _0x2c5914.slice(_0xc2710d).slideToggle(_0x1d4934);
          _0x3ab46d.toggleClass("expanded", !_0x256836);
          _0x19df3a.text(_0x256836 ? "查看全部" : "收起");
          _0xb09e0d.toggleClass("arrow-up", !_0x256836).toggleClass("arrow-down", _0x256836);
        });
        _0x2c5914.click(function () {
          $(this).toggleClass("active").siblings().removeClass("active").find(".product_section_list").slideUp(_0x1d4934);
          $(this).find(".product_section_list").stop(true, true).slideToggle(_0x1d4934);
        });
      }
      if (_0x318323 == "b") {
        const _0x3fd166 = $(".pb_tab_butt"),
          _0x524e5a = $(".pb_tab_row .pb_tab_itme"),
          _0x47e683 = $(".pb_tab_list ul li"),
          _0x5849dc = $(".pb_tab_list"),
          _0x596ec9 = $(".product_section_list"),
          _0x6c76ec = $(".pb_tab_row"),
          _0x5db12e = $(".pb_tab_row_cn"),
          _0x30a5ba = $(".viewMore"),
          _0x5e7eb4 = _0xc2710d;
        function _0x20e333(_0x2403c4 = false) {
          _0x596ec9.each(function (_0x3c2b92) {
            const _0x46203b = $(this).find(".product_item");
            _0x46203b.length > _0x5e7eb4 && _0x46203b.hide().slice(0, _0x5e7eb4).show();
            _0x2403c4 && _0x3c2b92 === 0 && _0x36fe78(_0x46203b.length > _0x5e7eb4);
          });
        }
        function _0x36fe78(_0x1a2292) {
          {
            _0x20e333();
            if (_0x1a2292) {
              _0x30a5ba.show().find("a").text("查看全部");
            } else _0x30a5ba.hide();
          }
        }
        $(".viewMore").click(function () {
          {
            const _0xb05621 = $(this).find("a"),
              _0x247bed = _0x596ec9.filter(".active"),
              _0x3e060e = _0x247bed.find(".product_item");
            _0xb05621.text() === "查看全部" ? (_0x3e060e.show(), _0xb05621.text("收起")) : (_0x3e060e.hide().slice(0, _0x5e7eb4).show(), _0xb05621.text("查看全部"));
            _0x30a5ba.toggleClass("active").find("i").toggleClass("arrow-up");
          }
        });
        let _0x574724,
          _0x7738cc,
          _0x3b52a2,
          _0x378a5c,
          _0x68e8aa = 0,
          _0x5a0263,
          _0xb938b;
        _0x5db12e.on("touchstart", function (_0x1b7f7b) {
          let _0x2393ac = $(this).parent().width(),
            _0x43a3fe = $(this).width();
          _0x5a0263 = _0x2393ac - _0x43a3fe - 26;
          _0xb938b = 0;
          _0x574724 = _0x1b7f7b.originalEvent.touches[0].pageX;
          _0x7738cc = _0x1b7f7b.originalEvent.touches[0].pageY;
        });
        _0x5db12e.on("touchmove", function (_0x4aed45) {
          _0x3b52a2 = _0x4aed45.originalEvent.touches[0].pageX - _0x574724;
          _0x378a5c = _0x4aed45.originalEvent.touches[0].pageY - _0x7738cc;
          Math.abs(_0x3b52a2) > Math.abs(_0x378a5c) && _0x4aed45.preventDefault();
          let _0x4bbb60 = _0x68e8aa + _0x3b52a2;
          _0x4bbb60 = Math.max(Math.min(_0x4bbb60, _0xb938b), _0x5a0263);
          $(this).css("transform", "translateX(" + _0x4bbb60 + "px)");
        });
        _0x5db12e.on("touchend", function (_0x27ee7f) {
          _0x68e8aa += _0x3b52a2;
          _0x68e8aa = Math.max(Math.min(_0x68e8aa, _0xb938b), _0x5a0263);
        });
        function _0x13fbc8() {
          const _0x3bd63e = _0x5db12e.css("transform"),
            _0x3fc571 = _0x3bd63e.match(/matrix.*\((.+)\)/);
          if (_0x3fc571 && _0x3fc571.length > 1) {
            {
              const _0x255733 = _0x3fc571[1].split(", ");
              _0x68e8aa = parseFloat(_0x255733[4]);
            }
          } else {
            _0x68e8aa = 0;
          }
        }
        _0x3fd166.add(_0x524e5a).add(_0x47e683).click(function () {
          let _0x23c910 = $(this).hasClass("pb_tab_butt"),
            _0x18b403 = _0x23c910 ? _0x524e5a.filter(".active").index() : $(this).index();
          if (_0x23c910) _0x3fd166.add(_0x5849dc).toggleClass("active");else {
            _0x5849dc.add(_0x3fd166).add(_0x524e5a).add(_0x596ec9).add(_0x47e683).removeClass("active");
            $(this).addClass("active");
            _0x524e5a.eq(_0x18b403).addClass("active");
            _0x47e683.eq(_0x18b403).addClass("active");
            _0x596ec9.eq(_0x18b403).addClass("active");
            _0x1498f4(_0x18b403);
            let _0x56081d = _0x596ec9.eq(_0x18b403).find(".product_item");
            _0x36fe78(_0x56081d.length > _0x5e7eb4);
          }
          _0x13fbc8();
        });
        function _0x1498f4(_0x21ec8c) {
          const _0x1ac78a = _0x524e5a.eq(_0x21ec8c),
            _0x596855 = _0x6c76ec.width(),
            _0xc74bc9 = _0x524e5a.toArray().reduce((_0x51f026, _0x3cd731) => _0x51f026 + $(_0x3cd731).outerWidth(true), 0),
            _0xbdc404 = _0x1ac78a.position().left;
          let _0x3a4e9b = Math.max(0, Math.min(_0xbdc404, _0xc74bc9 - _0x596855));
          (_0x21ec8c === 0 || _0xbdc404 < _0x596855) && (_0x3a4e9b = 0);
          _0x3a4e9b && (_0x3a4e9b += 26);
          _0x5db12e.css("transform", "translateX(-" + _0x3a4e9b + "px)");
        }
        _0x20e333(true);
      }
    }
  },
  activity = {
    "initializeNavigation": function () {
      const _0x408581 = $(".nav-layoutcontainer").height();
      function _0x450f0d() {
        var _0x4aab0d = $(window).scrollTop() + _0x408581;
        $(".activityContent_bg").each(function () {
          var _0x8115fa = $(this),
            _0x53120e = _0x8115fa.offset().top,
            _0x84d07b = _0x53120e + _0x8115fa.height();
          if (_0x4aab0d >= _0x53120e && _0x4aab0d < _0x84d07b) {
            {
              var _0x58bdd2 = _0x8115fa.attr("id");
              $(".navigation-slied-item").removeClass("active");
              $(".navigation-slied-item[data-navigation=\"#" + _0x58bdd2 + "\"]").addClass("active");
            }
          }
        });
      }
      var _0x18b757 = false,
        _0x4a4bf5,
        _0x2b1a6f = $(".pageNavigation"),
        _0x38822d = $(".navigation-slied");
      _0x2b1a6f.hover(function () {
        clearTimeout(_0x4a4bf5);
        _0x18b757 = true;
        _0x38822d.addClass("active");
        $(this).css("opacity", "0");
      }, function () {
        _0x4a4bf5 = setTimeout(function () {
          !_0x18b757 && (_0x38822d.removeClass("active"), _0x2b1a6f.css("opacity", "1"), _0x450f0d());
        }, 300);
      });
      _0x38822d.hover(function () {
        clearTimeout(_0x4a4bf5);
        _0x18b757 = true;
      }, function () {
        _0x18b757 = false;
        _0x4a4bf5 = setTimeout(function () {
          !_0x18b757 && (_0x38822d.removeClass("active"), _0x2b1a6f.css("opacity", "1"), _0x450f0d());
        }, 300);
      });
      $(".navigation-slied-item").click(function () {
        {
          $("html, body").stop(true);
          var _0xa4977f = $($(this).data("navigation"));
          $("html, body").animate({
            "scrollTop": _0xa4977f.offset().top - _0x408581 + 10
          }, 500);
          $(".navigation-slied-item").removeClass("active");
          $(this).addClass("active");
        }
      });
      $(window).on("scroll", _0x450f0d);
    },
    "initializeTypeLayout": function (_0x4a3e71) {
      $(_0x4a3e71).parent().each(function () {
        var _0x1e56ff = $(this),
          _0x82b087 = false;
        _0x1e56ff.find(".act-radio-option, .act-checkbox-option").each(function () {
          if ($(this).outerWidth() > 200) {
            _0x82b087 = true;
          }
        });
        _0x82b087 ? _0x1e56ff.css({
          "display": "flex",
          "flex-direction": "column",
          "gap": "0"
        }) : _0x1e56ff.css({
          "display": "flex",
          "flex-direction": "row",
          "flex-wrap": "wrap",
          "gap": "10px"
        });
      });
    },
    "initializePriceCalculation": function () {
      $(".act-dropdown").each(function () {
        const _0x4178ac = $(this).find(".act-dropdown-option:first").addClass("selected");
        $(this).find(".act-dropdown-selected").text(_0x4178ac.text());
      });
      $(document).on("click", ".act-dropdown-selected", function () {
        {
          const _0x4b60f2 = $(this).closest(".act-dropdown");
          _0x4b60f2.find(".act-dropdown-options").toggle();
          _0x4b60f2.toggleClass("active");
        }
      }).on("click", ".act-dropdown-option", function (_0x500319) {
        _0x500319.stopPropagation();
        const _0x185b57 = $(this);
        _0x185b57.addClass("selected").siblings().removeClass("selected");
        const _0x376cc0 = _0x185b57.closest(".act-dropdown");
        _0x376cc0.find(".act-dropdown-selected").text(_0x185b57.text());
        _0x376cc0.removeClass("active").find(".act-dropdown-options").hide();
      }).on("click", function (_0x67343d) {
        {
          if (!$(_0x67343d.target).closest(".act-dropdown").length) {
            $(".act-dropdown-options").hide();
            $(".act-dropdown").removeClass("active");
          }
        }
      });
      $(".act-radio-group").each(function () {
        $(this).find(".act-radio-option:first").addClass("selected");
      }).on("click", ".act-radio-option", function () {
        {
          const _0x3a9dec = $(this);
          _0x3a9dec.addClass("selected").siblings().removeClass("selected");
        }
      });
      $(".act-checkbox-option").each(function () {
        if ($(this).data("new") == 0) {
          $(this).addClass("selected");
        }
      });
      $(".act-checkbox-option").click(function () {
        $(this).toggleClass("selected");
      });
      let _0x2e6fa1 = {},
        _0x3225a2 = {},
        _0x559c82 = {};
      const _0x8c115 = [2, 3, 4];
      function _0x34f39a(_0x77855d) {
        _0x2e6fa1[_0x77855d] = {};
        _0x3225a2 = {
          "used": 1,
          "new": 1
        };
        _0x559c82 = {
          "current": 0,
          "old": 0
        };
        $("[data-id=\"" + _0x77855d + "\"]").find("[data-key]").each(function () {
          {
            let _0x51f31f = $(this).data("key"),
              _0x5763ff = $(this).find("[data-type]").data("type");
            _0x2e6fa1[_0x77855d][_0x51f31f] = {
              "totalUsed": 0,
              "totalNew": 0,
              "values": []
            };
            let _0x527ee6;
            if (_0x8c115.includes(_0x5763ff)) {
              _0x527ee6 = $(this).find(".selected");
              _0x3508f2(_0x527ee6, _0x51f31f, _0x77855d, false, _0x5763ff);
            }
            _0x5763ff === 5 && (_0x527ee6 = $(this).find(".act-checkbox-option.selected"), _0x3508f2(_0x527ee6, _0x51f31f, _0x77855d, true));
          }
        });
        let _0x1a553b = 0,
          _0x5647af = 0;
        for (let _0x53dbbb in _0x2e6fa1[_0x77855d]) {
          _0x1a553b += _0x2e6fa1[_0x77855d][_0x53dbbb].totalUsed;
          _0x5647af += _0x2e6fa1[_0x77855d][_0x53dbbb].totalNew;
        }
        _0x559c82.current = Number($("[data-id=\"" + _0x77855d + "\"]").find("#current").data("current"));
        _0x559c82.old = Number($("[data-id=\"" + _0x77855d + "\"]").find("#old").data("old"));
        let _0x2fefa3 = ((_0x559c82.current + Number(_0x5647af)) * Number(_0x3225a2.new)).toFixed(2),
          _0xc2b31b = ((_0x559c82.old + Number(_0x1a553b)) * Number(_0x3225a2.used)).toFixed(2);
        if (isNaN(_0x2fefa3) || isNaN(_0xc2b31b) || Number(_0xc2b31b) === 0) {
          formdiscount = "无折扣";
        } else {
          let _0x52bf40 = Number(_0x2fefa3) / Number(_0xc2b31b) * 10;
          !isFinite(_0x52bf40) ? formdiscount = "无折扣" : formdiscount = _0x52bf40.toFixed(1) + "折";
        }
        $("[data-id=\"" + _0x77855d + "\"]").find("#current").text(_0x2fefa3);
        $("[data-id=\"" + _0x77855d + "\"]").find("#discount").text(formdiscount);
        $("[data-id=\"" + _0x77855d + "\"]").find("#old").text(_0xc2b31b);
      }
      function _0x3508f2(_0x15eadf, _0x26c30d, _0x1e3b41, _0x5b54f3 = false, _0x2c1a89 = false) {
        _0x5b54f3 && (_0x2e6fa1[_0x1e3b41][_0x26c30d].values = []);
        if (_0x2c1a89 == 3) {
          {
            _0x3225a2.used = Number(_0x15eadf.data("used"));
            _0x3225a2.new = Number(_0x15eadf.data("new"));
            return;
          }
        }
        _0x15eadf.each(function () {
          let _0x480b00 = Number($(this).data("used")),
            _0x3ebedd = Number($(this).data("new"));
          _0x2e6fa1[_0x1e3b41][_0x26c30d].totalUsed += _0x480b00;
          _0x2e6fa1[_0x1e3b41][_0x26c30d].totalNew += _0x3ebedd;
          _0x2e6fa1[_0x1e3b41][_0x26c30d].values.push({
            "used": _0x480b00,
            "new": _0x3ebedd
          });
        });
      }
      $(document).on("click", ".act-dropdown-option, .act-radio-option, .act-checkbox-option", function () {
        {
          let _0x2a8bdf = $(this).closest("[data-id]").data("id");
          _0x34f39a(_0x2a8bdf);
        }
      });
    }
  };
  
function handleMediaLoadError(_0xd4a814) {
  const _0x29b7c8 = _0xd4a814.target;
  console.log("handleMediaLoadError", _0x29b7c8);
  if (_0x29b7c8.dataset.defaultLoaded) {
    return;
  }
  if (_0x29b7c8.tagName === "VIDEO") {
    const _0x45112e = document.createElement("img");
    _0x45112e.src = defaultImageUrl;
    _0x45112e.dataset.defaultLoaded = "true";
    const _0x334c66 = _0x29b7c8.closest(".comp_swiper_video");
    _0x334c66.innerHTML = "";
    _0x334c66.classList.replace("comp_swiper_video", "comp_swiper_img");
    _0x334c66.appendChild(_0x45112e);
  } else {
    if (_0x29b7c8.tagName === "IMG") {
      _0x29b7c8.src = defaultImageUrl;
      _0x29b7c8.dataset.defaultLoaded = "true";
    }
  }
}
function setupNavigationTabs() {
  if (!$(".et-hero-tabs").length) return;
  const _0x302e0c = $(".et-hero-tabs"),
    _0xaf57d4 = $(".nav-layoutcontainer"),
    _0x1f0d6b = $(".announcement-container").length ? $(".announcement-container").outerHeight() : 0,
    _0x2a2653 = _0x302e0c.offset().top,
    _0x20e0f7 = $(".et-main .et-slide"),
    _0x1809f1 = $(".et-hero-tabs-container"),
    _0xf75074 = $(".et-hero-tab-slider"),
    _0x3bb6df = $(".et-container");
  $(".et-hero-tab a").click(function (_0x914ebf) {
    _0x914ebf.preventDefault();
    let _0x19d7cd = $(this).parent().index(),
      _0x3d483c = _0x20e0f7.eq(_0x19d7cd);
    $("html, body").animate({
      "scrollTop": _0x3d483c.offset().top - _0x302e0c.outerHeight() - 10
    }, 500);
  });
  function _0x1dae55() {
    let _0x31279a = _0xaf57d4.outerHeight() + _0x1f0d6b;
    $(window).scrollTop() >= _0x2a2653 - _0x31279a ? (_0x3bb6df.addClass("navFix"), _0xaf57d4.hide()) : (_0x3bb6df.removeClass("navFix"), _0xaf57d4.show());
    _0x20e0f7.each(function (_0x9684c6) {
      if ($(this).offset().top <= $(window).scrollTop() + _0x302e0c.outerHeight() + 20) {
        _0x1809f1.find("li").removeClass("active");
        _0x1809f1.find("li").eq(_0x9684c6).addClass("active");
        const _0x591cdd = _0x1809f1.find("li.active"),
          _0x492f1b = _0x591cdd.position().left,
          _0x20e3b0 = parseInt(_0x591cdd.css("margin-left"), 10),
          _0x10f7c9 = _0x492f1b + _0x20e3b0;
        _0xf75074.css({
          "left": _0x10f7c9,
          "width": _0x1809f1.find("li.active").width()
        });
      }
    });
  }
  $(window).scroll(_0x1dae55);
  _0x1dae55();
}
function m_setupNavigationTabs() {
  if (!$(".et-hero-tabs").length) return;
  const _0x10b15e = $(".et-container .overflow_tab"),
    _0x1dd64f = _0x10b15e.width(),
    _0x2acb59 = $(".et-container").offset().top,
    _0x1e5306 = $(".nav-head-m").height(),
    _0xdccfa0 = $(".et-container").height();
  let _0x2850b5 = 0;
  _0x10b15e.find(".et-hero-tab").each(function () {
    _0x2850b5 += $(this).outerWidth(true);
  });
  if (_0x2850b5 > _0x1dd64f) {
    const _0x4f6a1b = $("<div class=\"dropdown-content\"></div>").hide(),
      _0x16832d = $("<div class=\"dropdown-navbtn\"></div>").insertAfter(_0x10b15e).after(_0x4f6a1b);
    _0x10b15e.find(".et-hero-tab").toArray().reduce((_0x40edf9, _0x5751dc) => {
      const _0x2a02f = _0x40edf9 + $(_0x5751dc).outerWidth(true);
      if (_0x2a02f > _0x1dd64f) $(_0x5751dc).appendTo(_0x4f6a1b);
      return _0x2a02f;
    }, 0);
    $(document).on("click", ".dropdown-navbtn", () => _0x4f6a1b.toggle());
  }
  $(".et-container .et-hero-tab").last().addClass("last-tab");
  $(window).on("scroll", function () {
    {
      $(window).scrollTop() > _0x2acb59 - _0x1e5306 ? $(".et-container").addClass("et-container-fixed") : ($(".et-container").removeClass("et-container-fixed"), $(".dropdown-content").hide());
      let _0x2a6504 = false;
      $(".et-slide").each(function (_0x297239) {
        if (_0x2a6504) return;
        const _0x552cc4 = $(this),
          _0x3ae992 = _0x552cc4.offset().top,
          _0x4b348e = _0x552cc4.outerHeight(),
          _0x38bfe5 = $(window).scrollTop() + _0x1e5306 + _0xdccfa0;
        if (_0x38bfe5 >= _0x3ae992 && _0x38bfe5 < _0x3ae992 + _0x4b348e) {
          $(".et-hero-tab, .dropdown-content .et-hero-tab").removeClass("active");
          const _0x26ae0e = $(".et-hero-tabs-container .et-hero-tab").length;
          _0x297239 < _0x26ae0e ? $(".et-hero-tabs-container .et-hero-tab").eq(_0x297239).addClass("active") : $(".dropdown-content .et-hero-tab").eq(_0x297239 - _0x26ae0e).addClass("active");
          _0x2a6504 = true;
        }
      });
    }
  });
  $(document).on("click", ".et-hero-tab", function () {
    const _0x4fa03e = $(this),
      _0x333238 = _0x4fa03e.closest(".dropdown-content").length ? _0x4fa03e.index() + $(".et-hero-tabs-container .et-hero-tab").length : _0x4fa03e.index(),
      _0x18307f = $(".et-slide").eq(_0x333238);
    $(".dropdown-content").hide();
    $("html, body").animate({
      "scrollTop": _0x18307f.offset().top - _0x1e5306 - _0xdccfa0
    }, 500);
  });
}
function initializeLightGallery(_0xb59158, _0x413da2) {
  if (!$(_0xb59158).length) return;
  $(_0xb59158).lightGallery({
    "selector": _0x413da2
  });
}
function initializeActivity() {
  $(".cloud-tab-button").click(function () {
    $(".cloud-tab-button").removeClass("selected");
    $(this).addClass("selected");
    const _0x292e2b = $(this).data("tab");
    $(".activity-item").each(function () {
      if (_0x292e2b === "all" || $(this).data("status") === _0x292e2b) $(this).show();else {
        $(this).hide();
      }
    });
  });
}
function openPage(_0x1d21de = "", _0xfa6c9 = false) {
  _0xfa6c9 == "1" ? window.open(_0x1d21de, "_blank") : window.location.href = _0x1d21de;
}