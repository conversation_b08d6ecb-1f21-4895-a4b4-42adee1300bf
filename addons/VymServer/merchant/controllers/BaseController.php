<?php

namespace addons\VymServer\merchant\controllers;

use addons\VymServer\common\models\DefaultModel;
use Yii;
use common\controllers\AddonsController;
use common\traits\MerchantTrait;

/**
 * 默认控制器
 *
 * Class DefaultController
 * @package addons\VymServer\merchant\controllers
 */
class BaseController extends AddonsController
{
    use MerchantTrait;
    /**
     * @var string
     */
    public $layout = "@addons/VymServer/merchant/views/layouts/main";
    // public $layout = "@merchant/views/layouts/main";


    /**
     * @var DefaultModel
     */
    public $modelClass = DefaultModel::class;

    /**
     * 返回模型
     *
     * @param $id
     *
     * @return \yii\db\ActiveRecord
     */
    protected function findModel($id)
    {
        //获取模型ID
        $primaryKey = $this->modelClass::primaryKey()[0];
        /* @var $model \yii\db\ActiveRecord */
        if (empty($id) || empty($model = $this->modelClass::find()->where([$primaryKey => $id])->one())) {
            $model = new $this->modelClass;
            return $model->loadDefaultValues();
        }

        return $model;
    }

}
