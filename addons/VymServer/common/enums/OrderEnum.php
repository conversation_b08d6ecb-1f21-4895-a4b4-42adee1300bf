<?php

namespace addons\VymServer\common\enums;

use common\enums\BaseEnum;

/**
 * 订单状态枚举
 *
 * Class OrderStatusEnum
 * @package addons\VymServer\common\enums
 */
class OrderEnum extends BaseEnum
{
    /**
     * 获取订单状态颜色映射
     *
     * @return array
     */
    public static function getOrderStatusColors(): array
    {
        return [
            '进行中'   => 'info',
            '已支付'   => 'success',
            '已完成'   => 'success',
            '待支付'   => 'warning',
            '部分支付' => 'warning',
            '已退款'   => 'secondary',
            '失败'     => 'danger',
            '已取消'   => 'secondary',
        ];
    }

    /**
     * 获取支付状态颜色映射
     *
     * @return array
     */
    public static function getPaymentStatusColors(): array
    {
        return [
            '已支付'   => 'success',
            '待支付'   => 'warning',
            '部分支付' => 'warning',
            '支付失败' => 'danger',
            '已退款'   => 'secondary',
        ];
    }

    /**
     * 获取支付锁定状态颜色映射
     *
     * @return array
     */
    public static function getPaymentLockColors(): array
    {
        return [
            '支付完成' => 'success',
            '待支付'   => 'warning',
            '支付中'   => 'info',
            '支付失败' => 'danger',
        ];
    }

    /**
     * 获取审核状态颜色映射
     *
     * @return array
     */
    public static function getReviewStatusColors(): array
    {
        return [
            '已审核'   => 'success',
            '未审核'   => 'warning',
            '审核中'   => 'info',
            '审核失败' => 'danger',
        ];
    }

    /**
     * 获取结算状态颜色映射
     *
     * @return array
     */
    public static function getSettlementStatusColors(): array
    {
        return [
            '已支付'   => 'success',
            '待支付'   => 'warning',
            '结算中'   => 'info',
            '结算失败' => 'danger',
        ];
    }

    /**
     * 获取状态对应的颜色
     *
     * @param string $status 状态文本
     * @param string $type   状态类型 (order|payment|lock|review|settlement)
     *
     * @return string
     */
    public static function getStatusColor(string $status, string $type = 'order'): string
    {
        switch ($type) {
            case 'payment':
                $colors = self::getPaymentStatusColors();
                break;
            case 'lock':
                $colors = self::getPaymentLockColors();
                break;
            case 'review':
                $colors = self::getReviewStatusColors();
                break;
            case 'settlement':
                $colors = self::getSettlementStatusColors();
                break;
            case 'order':
            default:
                $colors = self::getOrderStatusColors();
                break;
        }

        return $colors[$status] ?? 'secondary';
    }

    /**
     * 实现BaseEnum要求的getMap方法
     * 返回订单状态映射
     *
     * @return array
     */
    public static function getMap(): array
    {
        return [
            '进行中'   => '进行中',
            '已支付'   => '已支付',
            '已完成'   => '已完成',
            '待支付'   => '待支付',
            '部分支付' => '部分支付',
            '已退款'   => '已退款',
            '失败'     => '失败',
            '已取消'   => '已取消',
        ];
    }

    /**
     * 获取支付状态映射（用于下拉筛选）
     *
     * @return array
     */
    public static function getPaymentStatusMap(): array
    {
        return [
            '已支付'   => '已支付',
            '待支付'   => '待支付',
            '部分支付' => '部分支付',
            '支付失败' => '支付失败',
            '已退款'   => '已退款',
        ];
    }

}
