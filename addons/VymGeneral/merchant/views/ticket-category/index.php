<?php

use common\enums\AppEnum;
use common\helpers\ArrayHelper;
use jianyan\treegrid\TreeGrid;
use yii\grid\GridView;
use common\helpers\Html;
use common\helpers\Url;
use common\enums\StatusEnum;

$this->title = '工单分类管理';
$this->params['breadcrumbs'][] = $this->title;

?>

<div class="row">
    <div class="col-12">
        <div class="box">
            <div class="box-header">
                <h3 class="box-title"><?= $this->title; ?></h3>
                <div class="box-tools">
                    <?= Html::create(['create'], '创建'); ?>
                </div>
            </div>
            <div class="box-body table-responsive">
                <?= TreeGrid::widget([
                    'dataProvider' => $dataProvider,
//                    'filterModel' => $searchModel,
                    'keyColumnName' => 'id',
                    'parentColumnName' => 'pid',
                    'parentRootValue' => '0',

                    'options' => ['class' => 'table table-hover'],
                    'columns' => [
                        'id',
                        [
                            'label'          => '所属商户',
                            'headerOptions'  => ['style' => 'width: 120px;'],
                            'contentOptions' => ['class' => 'text-left'],
                            'format'         => 'raw',
                            'visible'        => Yii::$app->id == AppEnum::BACKEND, // 只在总后台显示
                            'value'          => function ($model) {
                                $merchantId    = ArrayHelper::getValue($model->merchant,"id");
                                $merchantTitle =  ArrayHelper::getValue($model->merchant,"title","未设置"); //$model->merchant->title ?? '未设置';
                                $str           = "未关联商户";
                                if ($merchantId) {
                                    $str = "ID: {$merchantId}";
                                    if ($merchantTitle) {
                                        $str .= "<br/>名称: {$merchantTitle}";
                                    }
                                }
                                return $str;
                            },
                        ],
                        [
                            'attribute' => 'title',
                            'format' => 'raw',
                            'value' => function ($model) use ($dropDownList) {
                                $prefix = '';
                                for ($i = 1; $i < $model->level; $i++) {
                                    $prefix .= '　';
                                }
                                return $prefix . $model->title;
                            },
                        ],
                        'description',
                        [
                            'attribute' => 'sort',
                            'format' => 'raw',
                            'headerOptions' => ['class' => 'col-md-1'],
                            'value' => function ($model) {
                                return Html::sort($model->sort);
                            },
                        ],
//                        [
//                            'attribute' => 'status',
//                            'format' => 'raw',
//                            'headerOptions' => ['class' => 'col-md-1'],
//                            'value' => function ($model) {
//                                return Html::status($model->status);
//                            },
//                            'filter' => Html::activeDropDownList($searchModel, 'status', [
//                                StatusEnum::ENABLED => '启用',
//                                StatusEnum::DISABLED => '禁用',
//                            ], [
//                                'prompt' => '全部',
//                                'class' => 'form-control',
//                            ]),
//                        ],
                        [
                            'attribute' => 'created_at',
                            'format' => ['date', 'php:Y-m-d H:i:s'],
                        ],
                        [
                            'header' => "操作",
                            'class' => 'yii\grid\ActionColumn',
                            'template' => '{edit} {status} {delete}',
                            'buttons' => [
                                'edit' => function ($url, $model, $key) {
                                    return Html::edit(['edit', 'id' => $model->id]);
                                },
                                'status' => function ($url, $model, $key) {
                                    return Html::status($model->status);
                                },
                                'delete' => function ($url, $model, $key) {
                                    return Html::delete(['delete', 'id' => $model->id]);
                                },
                            ],
                        ],
                    ],
                ]); ?>
            </div>
        </div>
    </div>
</div>
