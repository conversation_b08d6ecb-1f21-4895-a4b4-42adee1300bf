<?php

namespace addons\VymCloud\common\models;

use common\behaviors\MerchantStoreBehavior;
use common\helpers\StringHelper;
use common\models\member\Member;
use common\models\merchant\Merchant;
use Yii;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "{{%addon_cloud_server}}".
 *
 *
 * @property int         $id
 * @property int         $uid              会员ID
 * @property int|null    $merchant_id      商户id
 * @property int|null    $store_id         店铺id
 * @property string|null $name             主机别名
 * @property string|null $title            主机标题
 * @property int         $cluster_id       集群ID
 * @property float|null  $renew_amount     续费金额
 * @property int         $renew_mod        付款周期（月）
 * @property int         $renew_auto       自动续费
 * @property string      $node             集群节点
 * @property string      $vmid             虚拟机VMID
 * @property int         $system_id        系统镜像
 * @property string|null $ip               主IP
 * @property string|null $mac              MAC地址
 * @property array       $detail           配置详情
 * @property array       $extend           其他详情
 * @property int|null    $tasks_id         当前任务ID
 * @property int|null    $status_server    服务器状态
 * @property int|null    $status_power     电源状态
 * @property int|null    $status_network   断网状态
 * @property string|null $remark           备注
 * @property int|null    $tips_time        到期提醒时间
 * @property int|null    $reinstall_count  重装系统次数/当天
 * @property int|null    $reinstall_time   重装系统时间
 * @property int|null    $begin_at         开始时间
 * @property int|null    $end_at           到期时间
 * @property string|null $created_at       创建时间
 * @property string|null $updated_at       更新时间
 * @property string|null $deleted_at       删除时间
 */
class CloudServer extends \yii\db\ActiveRecord
{
    use MerchantStoreBehavior;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%addon_cloud_server}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'node', 'mac', 'remark', 'created_at', 'updated_at', 'deleted_at'], 'default', 'value' => null],
            [['status_server', 'status_network'], 'default', 'value' => 0],
            [['merchant_id', 'store_id'], 'default', 'value' => 0],
            [['status_power'], 'default', 'value' => 'unknown'],
            [['uid', 'cluster_id', 'system_id', 'renew_mod', 'renew_auto', 'tasks_id', 'tips_time', 'reinstall_count',], 'integer'],
            [['renew_amount'], 'number'],
            [['detail', 'extend', 'created_at', 'updated_at', 'deleted_at', 'begin_at', 'end_at',], 'safe'],
            [['remark'], 'string'],
            [['name', 'title'], 'string', 'max' => 255],
            [['ip', 'mac', 'node', 'vmid'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'              => 'ID',
            'uid'             => '会员ID',
            'merchant_id'     => '商户ID',
            'store_id'        => '店铺ID',
            'name'            => '主机别名',
            'title'           => '主机标题',
            'cluster_id'      => '集群ID',
            'renew_amount'    => '续费金额',
            'renew_mod'       => '付款周期（月）',
            'renew_auto'      => '自动续费',
            'node'            => '集群节点',
            'vmid'            => '虚拟机VMID',
            'system_id'       => '系统镜像',
            'ip'              => '主IP',
            'mac'             => 'MAC地址',
            'detail'          => '配置详情',
            'extend'          => '其他详情',
            'tasks_id'        => '当前任务ID',
            'status_server'   => '服务器状态',
            'status_power'    => '电源状态',
            'status_network'  => '断网状态',
            'remark'          => '备注',
            'tips_time'       => '到期提醒时间',
            'reinstall_count' => '重装系统次数/当天',
            'reinstall_time'  => '重装系统时间',
            'begin_at'        => '开始时间',
            'end_at'          => '到期时间',
            'created_at'      => '创建时间',
            'updated_at'      => '更新时间',
            'deleted_at'      => '删除时间',
        ];
    }

    /**
     * 获取集群
     *
     * @return ActiveQuery
     */
    public function getCluster()
    {
        return $this->hasOne(CloudCluster::class, ['id' => 'cluster_id']);
    }

    /**
     * 获取系统镜像
     *
     * @return ActiveQuery
     */
    public function getSystem()
    {
        return $this->hasOne(CloudSystem::class, ['id' => 'system_id']);
    }


    /**
     * 获取任务
     *
     * @return ActiveQuery
     */
    public function getTask()
    {
        return $this->hasOne(CloudServerTask::class, ['task_id' => 'tasks_id']);
    }

    /**
     * 获取IP地址
     *
     * @return ActiveQuery
     */
    public function getIps()
    {
        return $this->hasMany(CloudIps::class, ['server_id' => 'id']);
    }


    /**
     * 关联会员
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMember()
    {
        return $this->hasOne(Member::class, ['id' => 'uid']);
    }

    /**
     * 关联商户
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }


    /**
     * 定义序列化字段
     *
     * @return array
     */
    public function fields()
    {
        $fields = parent::fields();

        // 添加关联数据到序列化字段中
        $fields['cluster'] = 'cluster';
        $fields['system']  = 'system';
        $fields['task']    = 'task';
        $fields['ips']     = 'ips';

        return $fields;
    }


    /**
     * 定义序列化字段
     *
     * @return array
     */
    public function extraFields()
    {
        // 添加关联数据到序列化字段中
        return [
            'cluster',
            'ips',
            'system',
            'task',
        ];
    }

    /**
     * @param bool $insert
     *
     * @return bool
     */
    public function beforeSave($insert)
    {
        $this->created_at = StringHelper::intToDate($this->created_at);
        $this->updated_at = StringHelper::intToDate(time());

        return parent::beforeSave($insert);
    }
}
