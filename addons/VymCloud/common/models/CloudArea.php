<?php

namespace addons\VymCloud\common\models;

use common\behaviors\MerchantStoreBehavior;
use common\behaviors\SoftDeleteBehavior;
use common\helpers\StringHelper;
use common\models\merchant\Merchant;
use common\traits\Tree;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%addon_cloud_area}}".
 *
 * @property int         $id          主键ID
 * @property int|null    $merchant_id 商户id
 * @property int|null    $store_id    店铺id
 * @property string      $name        简体名称
 * @property string|null $name_hk     繁体名称
 * @property string|null $name_en     英文名称
 * @property int         $pid         父节点ID
 * @property string|null $icon        ICON
 * @property int|null    $sort        排序
 * @property int|null    $area_st     状态
 * @property string|null $content
 * @property string|null $created_at  创建时间
 * @property string|null $updated_at  更新时间
 */
class CloudArea extends \common\models\base\BaseModel
{

    use Tree;
    use MerchantStoreBehavior {
        behaviors as merchantStoreBehavior;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%addon_cloud_area}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name_hk', 'name_en', 'icon', 'content', 'created_at', 'updated_at'], 'default', 'value' => null],
            [['pid'], 'default', 'value' => 0],
            [['area_st', 'level'], 'default', 'value' => 1],
            [['merchant_id', 'store_id'], 'default', 'value' => 0],
            [['name'], 'required'],
            [['pid', 'sort', 'area_st'], 'integer'],
            [['content', 'tree'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['name'], 'string', 'max' => 100],
            [['name_hk', 'name_en', 'icon'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键ID',
            'merchant_id' => '商户ID',
            'store_id' => '店铺ID',
            'name' => '简体名称',
            'name_hk' => '繁体名称',
            'name_en' => '英文名称',
            'pid' => '父节点ID',
            'icon' => 'ICON',
            'sort' => '排序',
            'area_st' => '状态',
            'content' => '内容',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
        ];
    }

    public function behaviors()
    {
        // $before = parent::behaviors();
        $before = self::merchantStoreBehavior();
        $before[] =
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['updated_at'],
                ],
                'value' => function () {
                    return date('Y-m-d H:i:s'); // 自定义时间格式
                },
            ];
        $before[] = [
            'class' => SoftDeleteBehavior::class,
            'attribute' => 'status',
        ];
        return $before;
    }

    /**
     * 关联商户
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }
}
