<?php

use yii\helpers\ArrayHelper;
use yii\helpers\Url;

$this->title = '控制台管理-产品配置管理-产品配置信息修改';
?>
<?php $this->beginBlock('content') ?>
<?php $node = \Yii::$app->session['auth']['node'];?>
<div class="row">
	<div class="col-xs-12">
        <form id='form' class="form-horizontal" method='post' role="form">
        <div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" > 产品配置名称</label>
        	<div class="col-sm-9">
        		<input type="text" name='name' value='<?php echo $arrRes['name'] ?>'  class="col-xs-10 col-sm-5">
        		<span class="middle" style="color:red;margin-left:5px"></span>
        	</div>
        </div>
		<div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" > 产品配置繁体名称</label>
        	<div class="col-sm-9">
        		<input type="text" name='name_tw' value='<?php echo $arrRes['name_tw'] ?>'  class="col-xs-10 col-sm-5">
        		<span class="middle" style="color:red;margin-left:5px"></span>
        	</div>
        </div>
		<div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" > 产品配置英文名称</label>
        	<div class="col-sm-9">
        		<input type="text" name='name_en' value='<?php echo $arrRes['name_en'] ?>'  class="col-xs-10 col-sm-5">
        		<span class="middle" style="color:red;margin-left:5px"></span>
        	</div>
        </div>
		
        <div class="form-group">
		  <label class="col-sm-3 control-label no-padding-right" >产品配置分类</label>
		  <div class="col-sm-5">
    		  <div class="checkbox">
    		  <?php foreach ($PdtManageTypeRes as $value): ?>
    			 <label>
    				<input name="pdt_type_id[]" value="<?php echo $value['type_id'] ?>" <?php if (in_array($value['type_id'], $pdtTypeIdList)):?>checked <?php endif;?> class="ace ace-checkbox-2" type="checkbox">
    				<span class="lbl" style="min-width: 122px;min-height: 22px;"> <?php echo $value['type_name'] ?></span>
    			 </label>
    		  <?php endforeach ?>  			 
    		  </div>
		  </div>
	    </div>
	    <div class="form-group">
		  <label class="col-sm-3 control-label no-padding-right" >产品属性类别</label>
    		<div class="col-sm-9">
    			<select name="type_id" class="col-xs-10 col-sm-5">			
    				<?php foreach ($PdtTypeRes as $value): ?>
    					<option value="<?php echo $value['id'] ?>" rel="<?php echo $value['name'] ?>"
    						<?php if ($value['id'] == @$arrRes['type_id']): ?>
    							selected='selected'
    						<?php endif ?>
    					><?php echo $value['name'] ?></option>
    					<?php endforeach ?>
    			</select>
    			<span class="middle" style="color:red;margin-left:5px"></span>
    		</div>
	    </div>
	    <div class="form-group">
	       <label class="col-sm-3 control-label no-padding-right" >基本信息配置</label>
			<div class="col-sm-6">				
				<div id="accordion" class="accordion-style1 panel-group">
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseOne">
									<i class="ace-icon fa fa-angle-down bigger-110" data-icon-hide="ace-icon fa fa-angle-down" data-icon-show="ace-icon fa fa-angle-right"></i>
									&nbsp;CPU
								</a>
							</h4>
						</div>
						<div class="panel-collapse collapse" id="collapseOne">
							<div class="panel-body">
                        	<?php foreach ($PdtCpuRes as  &$value): ?>
                                 <div style="margin-bottom: 3px;">
                                 <input  type="hidden" name="config_Cpu_id[]" value="<?php echo $value['id']?>" style="display: none">
							     <input  type="hidden" name="config_Cpu_name[]" value="<?php echo $value['name']?>" style="display: none">
    				             <input class="pdefault" type="radio" <?php if ($cpu['default'] == $value['id']):?>checked="checked" <?php endif;?> name="config_Cpu_default" value="<?php echo $value['id']?>" >
    				             <label>默认</label>&nbsp;&nbsp;&nbsp;
    				             <input class="phidden" type="checkbox" <?php if ($value['openly'] == 1):?>checked="checked" <?php endif;?> name="config_Cpu_openly[]" value="<?php echo $value['id']?>">
    						     <label>公开</label>&nbsp;&nbsp;&nbsp;  
    						     <label>价格：</label>
    						     <input name="config_Cpu_price[]" type="text" value="<?php echo $value['price']?>" class="ui-spinner-input">
    						     &nbsp;&nbsp;&nbsp;	
    						     <label>cpu名称：<?php echo $value['name']?></label>
    					     </div>
    					     <?php endforeach;?>							
							</div>
						</div>
					</div>
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseTwo">
									<i class="ace-icon fa fa-angle-down bigger-110" data-icon-hide="ace-icon fa fa-angle-down" data-icon-show="ace-icon fa fa-angle-right"></i>
									&nbsp;内存大小
								</a>
							</h4>
						</div>
						<div class="panel-collapse collapse" id="collapseTwo">
							<div class="panel-body">
							    <?php foreach ($PdtRamRes as  &$value): ?>
                                 <div style="margin-bottom: 3px;">
                                 <input  type="hidden" name="config_Ram_id[]" value="<?php echo $value['id']?>" style="display: none">
							     <input  type="hidden" name="config_Ram_name[]" value="<?php echo $value['name']?>" style="display: none">
    				             <input class="pdefault" type="radio" <?php if ($ram['default'] == $value['id']):?>checked="checked" <?php endif;?> name="config_Ram_default" value="<?php echo $value['id']?>" >
    				             <label>默认</label>&nbsp;&nbsp;&nbsp;
    				             <input class="phidden" type="checkbox" <?php if ($value['openly'] == 1):?>checked="checked" <?php endif;?> name="config_Ram_openly[]" value="<?php echo $value['id']?>">
    						     <label>公开</label>&nbsp;&nbsp;&nbsp;
    						     <label>价格：</label>
    						     <input name="config_Ram_price[]" type="text" value="<?php echo $value['price']?>" class="ui-spinner-input">
    						     &nbsp;&nbsp;&nbsp;	
    						     <label>内存大小：<?php echo $value['name']?></label>
    					     </div>
    					     <?php endforeach;?>			
							</div>
						</div>
					</div>					
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseThree">
									<i class="ace-icon fa fa-angle-down bigger-110" data-icon-hide="ace-icon fa fa-angle-down" data-icon-show="ace-icon fa fa-angle-right"></i>
									&nbsp;硬盘
								</a>
							</h4>
						</div>
						<div class="panel-collapse collapse" id="collapseThree">
							<div class="panel-body">
							     <?php foreach ($PdtHddRes as  &$value): ?>
                                 <div style="margin-bottom: 3px;">
                                 <input  type="hidden" name="config_Hdd_id[]" value="<?php echo $value['id']?>" style="display: none">
							     <input  type="hidden" name="config_Hdd_name[]" value="<?php echo $value['name']?>" style="display: none">
    				             <input class="pdefault" type="radio" <?php if ($hdd['default'] == $value['id']):?>checked="checked" <?php endif;?> name="config_Hdd_default" value="<?php echo $value['id']?>" >
    				             <label>默认</label>&nbsp;&nbsp;&nbsp;
    				             <input class="phidden" type="checkbox" <?php if ($value['openly'] == 1):?>checked="checked" <?php endif;?> name="config_Hdd_openly[]" value="<?php echo $value['id']?>">
    						     <label>公开</label>&nbsp;&nbsp;&nbsp;  
    						     <label>价格：</label>
    						     <input name="config_Hdd_price[]" type="text" value="<?php echo $value['price']?>" class="ui-spinner-input">
    						     &nbsp;&nbsp;&nbsp;	
    						     <label>硬盘：<?php echo $value['name']?></label>
    					     </div>
    					     <?php endforeach;?>			
							</div>
						</div>
					</div>
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseFour">
									<i class="ace-icon fa fa-angle-down bigger-110" data-icon-hide="ace-icon fa fa-angle-down" data-icon-show="ace-icon fa fa-angle-right"></i>
									&nbsp;带宽
								</a>
							</h4>
						</div>
						<div class="panel-collapse collapse" id="collapseFour">
							<div class="panel-body">
							    <?php foreach ($PdtBandwidthRes as  &$value): ?>
                                 <div style="margin-bottom: 3px;">
                                 <input  type="hidden" name="config_Bandwidth_id[]" value="<?php echo $value['id']?>" style="display: none">
							     <input  type="hidden" name="config_Bandwidth_name[]" value="<?php echo $value['name']?>" style="display: none">
    				             <input class="pdefault" type="radio" <?php if ($bandwidth['default'] == $value['id']):?>checked="checked" <?php endif;?> name="config_Bandwidth_default" value="<?php echo $value['id']?>" >
    				             <label>默认</label>&nbsp;&nbsp;&nbsp;
    				             <input class="phidden" type="checkbox" <?php if ($value['openly'] == 1):?>checked="checked" <?php endif;?> name="config_Bandwidth_openly[]" value="<?php echo $value['id']?>">
    						     <label>公开</label>&nbsp;&nbsp;&nbsp;
    						     <label>价格：</label>
    						     <input name="config_Bandwidth_price[]" type="text" value="<?php echo $value['price']?>" class="ui-spinner-input">
    						     &nbsp;&nbsp;&nbsp;	
    						     <label>带宽：<?php echo $value['name']?></label>
    					     </div>
    					     <?php endforeach;?>			
							</div>
						</div>
					</div>
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseFive">
									<i class="ace-icon fa fa-angle-down bigger-110" data-icon-hide="ace-icon fa fa-angle-down" data-icon-show="ace-icon fa fa-angle-right"></i>
									&nbsp;IP数量
								</a>
							</h4>
						</div>
						<div class="panel-collapse collapse" id="collapseFive">
							<div class="panel-body">
							     <?php foreach ($PdtIpnumberRes as  &$value): ?>
                                 <div style="margin-bottom: 3px;">
                                 <input  type="hidden" name="config_Ipnumber_id[]" value="<?php echo $value['id']?>" style="display: none">
							     <input  type="hidden" name="config_Ipnumber_name[]" value="<?php echo $value['name']?>" style="display: none">
    				             <input class="pdefault" type="radio" <?php if ($ipnumber['default'] == $value['id']):?>checked="checked" <?php endif;?> name="config_Ipnumber_default" value="<?php echo $value['id']?>" >
    				             <label>默认</label>&nbsp;&nbsp;&nbsp;
    				             <input class="phidden" type="checkbox" <?php if ($value['openly'] == 1):?>checked="checked" <?php endif;?> name="config_Ipnumber_openly[]" value="<?php echo $value['id']?>">
    						     <label>公开</label>&nbsp;&nbsp;&nbsp;  
    						     <label>价格：</label>
    						     <input name="config_Ipnumber_price[]" type="text" value="<?php echo $value['price']?>" class="ui-spinner-input">
    						     &nbsp;&nbsp;&nbsp;	
    						     <label>IP数量：<?php echo $value['name']?></label>
    					     </div>
    					     <?php endforeach;?>			
							</div>
						</div>
					</div>
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseSix">
									<i class="ace-icon fa fa-angle-down bigger-110" data-icon-hide="ace-icon fa fa-angle-down" data-icon-show="ace-icon fa fa-angle-right"></i>
									&nbsp;防御流量
								</a>
							</h4>
						</div>
						<div class="panel-collapse collapse" id="collapseSix">
							<div class="panel-body">
							<?php if(empty($defense)):?>
    							 <?php foreach ($PdtDefenseRes as  &$value): ?>
                                     <div style="margin-bottom: 3px;">
                                     <input  type="hidden" name="config_Defense_id[]" value="<?php echo $value['id']?>" style="display: none">
    							     <input  type="hidden" name="config_Defense_name[]" value="<?php echo $value['name']?>" style="display: none">
        				             <input class="pdefault" type="radio" name="config_Defense_default" value="<?php echo $value['id']?>" >
        				             <label>默认</label>&nbsp;&nbsp;&nbsp;
        				             <input class="phidden" type="checkbox"  name="config_Defense_openly[]" value="<?php echo $value['id']?>">
        						     <label>公开</label>&nbsp;&nbsp;&nbsp;  
        						     <label>价格：</label>
        						     <input name="config_Defense_price[]" type="text" value="0" class="ui-spinner-input">
        						     &nbsp;&nbsp;&nbsp;	
        						     <label>防御流量：<?php echo $value['name']?></label>
        					     </div>
        					     <?php endforeach;?>		
							<?php else:?>
    							 <?php foreach ($PdtDefenseRes as  &$value): ?>
                                     <div style="margin-bottom: 3px;">
                                     <input  type="hidden" name="config_Defense_id[]" value="<?php echo $value['id']?>" style="display: none">
    							     <input  type="hidden" name="config_Defense_name[]" value="<?php echo $value['name']?>" style="display: none">
        				             <input class="pdefault" type="radio" <?php if ($defense['default'] == $value['id']):?>checked="checked" <?php endif;?> name="config_Defense_default" value="<?php echo $value['id']?>" >
        				             <label>默认</label>&nbsp;&nbsp;&nbsp;
        				             <input class="phidden" type="checkbox" <?php if ($value['openly'] == 1):?>checked="checked" <?php endif;?> name="config_Defense_openly[]" value="<?php echo $value['id']?>">
        						     <label>公开</label>&nbsp;&nbsp;&nbsp;  
        						     <label>价格：</label>
        						     <input name="config_Defense_price[]" type="text" value="<?php echo $value['price']?>" class="ui-spinner-input">
        						     &nbsp;&nbsp;&nbsp;	
        						     <label>防御流量：<?php echo $value['name']?></label>
        					     </div>
        					     <?php endforeach;?>		
							<?php endif;?>
							    	
							</div>
						</div>
					</div>
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseSeven">
									<i class="ace-icon fa fa-angle-down bigger-110" data-icon-hide="ace-icon fa fa-angle-down" data-icon-show="ace-icon fa fa-angle-right"></i>
									&nbsp;操作系统
								</a>
							</h4>
						</div>
						<div class="panel-collapse collapse" id="collapseSeven">
							<div class="panel-body">
							    <?php foreach ($PdtSystemRes as  &$value): ?>
                                 <div style="margin-bottom: 3px;">
                                 <input  type="hidden" name="config_System_id[]" value="<?php echo $value['id']?>" style="display: none">
							     <input  type="hidden" name="config_System_name[]" value="<?php echo $value['name']?>" style="display: none">
    				             <input class="pdefault" type="radio" <?php if ($operatsystem['default'] == $value['id']):?>checked="checked" <?php endif;?> name="config_System_default" value="<?php echo $value['id']?>" >
    				             <label>默认</label>&nbsp;&nbsp;&nbsp;
    				             <input class="phidden" type="checkbox" <?php if ($value['openly'] == 1):?>checked="checked" <?php endif;?> name="config_System_openly[]" value="<?php echo $value['id']?>">
    						     <label>公开</label>&nbsp;&nbsp;&nbsp;  
    						     <label>价格：</label>
    						     <input name="config_System_price[]" type="text" value="<?php echo $value['price']?>" class="ui-spinner-input">
    						     &nbsp;&nbsp;&nbsp;	
    						     <label>操作系统：<?php echo $value['name']?></label>
    					     </div>
    					     <?php endforeach;?>		
							</div>
						</div>
					</div>
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseEight">
									<i class="ace-icon fa fa-angle-down bigger-110" data-icon-hide="ace-icon fa fa-angle-down" data-icon-show="ace-icon fa fa-angle-right"></i>
									&nbsp;显卡
								</a>
							</h4>
						</div>
						<div class="panel-collapse collapse" id="collapseEight">
							<div class="panel-body">
							    <?php foreach ($PdtCardRes as  &$value): ?>
                                 <div style="margin-bottom: 3px;">
                                 <input  type="hidden" name="config_Card_id[]" value="<?php echo $value['id']?>" style="display: none">
							     <input  type="hidden" name="config_Card_name[]" value="<?php echo $value['name']?>" style="display: none">
    				             <input class="pdefault" type="radio" <?php if (ArrayHelper::getValue($card,'default') == $value['id']):?>checked="checked" <?php endif;?>name="config_Card_default" value="<?php echo $value['id']?>" >
    				             <label>默认</label>&nbsp;&nbsp;&nbsp;
    				             <input class="phidden" type="checkbox" <?php if (ArrayHelper::getValue($value,'openly') == 1):?>checked="checked" <?php endif;?> name="config_Card_openly[]" value="<?php echo $value['id']?>">
    						     <label>公开</label>&nbsp;&nbsp;&nbsp;
    						     <label>价格：</label>
    						     <input name="config_Card_price[]" type="text" value="<?php echo ArrayHelper::getValue($value,'price')?>" class="ui-spinner-input">
    						     &nbsp;&nbsp;&nbsp;
    						     <label>显卡：<?php echo $value['name']?></label>
    					     </div>
    					     <?php endforeach;?>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" > 付款周期</label>
        	<div class="col-sm-9">        	
        		<input class="pdefault" type="radio" <?php if ( $arrRes['payment_cycle'] == 1 ):?> checked="checked" <?php endif;?> name="payment_cycle" value="1" >
    			<label>年</label>&nbsp;&nbsp;&nbsp;    			
    			<input class="pdefault" type="radio" <?php if ( $arrRes['payment_cycle'] == 2 ):?> checked="checked" <?php endif;?> name="payment_cycle" value="2" >
    			<label>月</label>&nbsp;&nbsp;&nbsp;
    			<input class="pdefault" type="radio" <?php if ( $arrRes['payment_cycle'] == 3 ):?> checked="checked" <?php endif;?> name="payment_cycle" value="3" >
    			<label>天</label>&nbsp;&nbsp;&nbsp;    			
        	</div>
        </div>
		<div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" > 价格( 元/月 )</label>
        	<div class="col-sm-9">
        		<input type="text" name='sell_price' value='<?php echo $arrRes['sell_price']?>'  class="col-xs-10 col-sm-5">
        		<span class="middle" style="color:red;margin-left:5px"></span>
        	</div>
        </div>
        <div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" > 价格( 元/季度 )</label>
        	<div class="col-sm-9">
        		<input type="text" name='price_quarter' value='<?php echo $arrRes['price_quarter']?>'  class="col-xs-10 col-sm-5">
        		<span class="middle" style="color:red;margin-left:5px"></span>
        	</div>
        </div>
        <div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" > 价格( 元/半年 )</label>
        	<div class="col-sm-9">
        		<input type="text" name='price_half' value='<?php echo $arrRes['price_half']?>'  class="col-xs-10 col-sm-5">
        		<span class="middle" style="color:red;margin-left:5px"></span>
        	</div>
        </div>
        <div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" > 价格( 元/年 )</label>
        	<div class="col-sm-9">
        		<input type="text" name='price_year' value='<?php echo $arrRes['price_year']?>'  class="col-xs-10 col-sm-5">
        		<span class="middle" style="color:red;margin-left:5px"></span>
        	</div>
        </div>
	    <div class="form-group">
		  <label class="col-sm-3 control-label no-padding-right" >状态</label>
    		<div class="col-sm-9">
    			<select name="status" class="col-xs-10 col-sm-5">
    			<option value="1" <?php if ( $arrRes['status'] == 1): ?>selected='selected'<?php endif ?>>公开</option>			
    			 <option value="0" <?php if ( $arrRes['status'] == 0): ?>selected='selected'<?php endif ?>>隐藏</option>    			 
    			</select>
    			<span class="middle" style="color:red;margin-left:5px"></span>
    		</div>
	    </div>
		<div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" >是否推荐 </label>        
        	<div class="col-sm-9">
        		<div style="height:20px;margin-top:5px">
					<label>
						<input name="recommend" type="radio" value="Y" <?php if ($arrRes['recommend'] == "Y"): ?>checked="" <?php endif ?> class="ace">
						<span class="lbl"> 是</span>
					</label>
					<label>
						<input name="recommend" type="radio" value="N" <?php if ($arrRes['recommend'] == "N"): ?>checked="" <?php endif ?>  class="ace">
						<span class="lbl"> 否</span>
					</label>        		
        		<span class="lbl"></span>
        		</div>
        	</div>
        </div>
        <div class="form-group">
    	    <label class="col-sm-3 control-label no-padding-right" > 备注</label>
        	<div class="col-sm-9">
        		<textarea class="form-control col-xs-10 col-sm-5 cn" id="form-field-8" name='desc'><?php echo $arrRes['desc'] ?></textarea>
        		<span class="middle" style="color:red;margin-left:5px"></span>
        	</div>
        </div>
        <div class="clearfix form-actions">	
            <div class="col-md-offset-3 col-md-9">
                <?php if(in_array("pdt-manage/do-item", $node) || $this->params['is_administrator_user']):?>
					<input type="hidden" name='id' value="<?php echo @$arrRes['id'] ?>">
					<button id="go" class="btn btn-info" type="submit">
						<i class="ace-icon fa fa-check bigger-110"></i>
						数据提交
					</button>
					&nbsp; &nbsp; &nbsp;
            	<?php endif;?>
            	
            	<button class="btn" type="reset">
            		<i class="ace-icon fa fa-undo bigger-110"></i>
            		还原内容
            	</button>
                &nbsp; &nbsp; &nbsp;
            	<button class="btn btn-danger" type="button" style="width:120px" onclick="window.history.back()">
            		<i class="ace-icon fa fa-reply icon-only"></i>
            		返回上一页
            	</button> 
            </div>
        </div>        
        <div class="hr hr-24"></div>        
		</form>
	</div>
</div>
<script type="text/javascript">
	$('#go').click(function(){    
		var url = '<?php echo Url::to(["do-item"]) ?>';
		layer.msg('正在提交...', {icon:16, time:0});
		$('.middle').html("");
		$.post(url, $('#form').serialize(), function(data){
			if ( data['data']['status'] ) {
				layer.alert(data['data']['info'], {icon:1}, function(index){				   		  
					window.location.href='<?php echo Url::to(["pdt-manage/index"])?>';
				}); 
			} else {
				if ( typeof data['data']['info'] === 'string' ) {
					layer.alert(data['data']['info'], {icon:7});
					return ;
				}    
				$.each(data['data']['info'], function(key, val){			
					layer.alert('产品配置信息填写有误', {icon:7});			
					$("[name='"+key+"']").next('span').html('<i class="ace-icon fa fa-exclamation-circle bigger-110">'+val[0]+'</i>');
					
				});
			}		
		}, 'json');		
		return false;	
	});
</script>

<?php $this->endBlock(); ?>
