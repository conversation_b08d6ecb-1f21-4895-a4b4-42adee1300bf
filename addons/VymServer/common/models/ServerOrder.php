<?php

namespace addons\VymServer\common\models;

use common\behaviors\MerchantStoreBehavior;
use common\behaviors\TaggableBehavior;
use common\helpers\StringHelper;
use common\models\merchant\Merchant;
use Yii;
use yii\db\Exception;

/**
 * This is the model class for table "{{%addon_server_order}}".
 *
 * @property int         $id
 * @property int         $uid              会员ID
 * @property int|null    $merchant_id      商户id
 * @property int|null    $store_id         店铺id
 * @property int         $original_id      主订单 - payorder_original.original_id
 * @property string      $original_no      支付单号
 * @property int         $general_id       支付单ID - payorder_general.general_id
 * @property string      $general_no       支付单号
 * @property int|null    $payment_id       付款单ID - payment_flow.id
 * @property string|null $payment_no       付款单号
 * @property int         $goods_id         商户产品ID
 * @property int         $product_id       产品ID
 * @property array       $cart_data        购买详情
 * @property string|null $remark           备注
 * @property string|null $created_at       创建时间
 * @property string|null $updated_at       更新时间
 * @property string|null $deleted_at       软删除
 */
class ServerOrder extends \yii\db\ActiveRecord
{
    use MerchantStoreBehavior;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%addon_server_order}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['remark', 'created_at', 'updated_at'], 'default', 'value' => null],
            [['uid', 'product_id'], 'default', 'value' => 0],
            [['merchant_id', 'store_id'], 'default', 'value' => 0],
            [['uid', 'original_id', 'general_id', 'goods_id', 'product_id'], 'required'],
            [['uid', 'merchant_id', 'store_id', 'original_id', 'general_id', 'payment_id', 'goods_id', 'product_id'], 'integer'],
            [['cart_data'], 'safe'],
            [['original_no', 'general_no', 'payment_no', 'remark',], 'string'],
            [['created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'          => 'ID',
            'uid'         => '会员ID',
            'merchant_id' => '商户id',
            'store_id'    => '店铺id',
            'original_id' => '主订单ID',
            'original_no' => '主订单号',
            'general_id'  => '支付单ID',
            'general_no'  => '支付单号',
            'payment_id'  => '付款单ID',
            'payment_no'  => '付款单号',
            'goods_id'    => '商户产品ID',
            'product_id'  => '产品ID',
            'cart_data'   => '购买详情',
            'remark'      => '备注',
            'created_at'  => '创建时间',
            'updated_at'  => '更新时间',
        ];
    }

    /**
     * 主订单
     * @return \yii\db\ActiveQuery
     */
    public function getOriginal()
    {
        return $this->hasOne(DsPayOrderOriginal::class, ['original_id' => 'original_id']);
    }

    /**
     * 支付单
     * @return \yii\db\ActiveQuery
     */
    public function getGeneral()
    {
        return $this->hasOne(DsPayOrderGeneral::class, ['general_id' => 'general_id']);
    }

    /**
     * 付款单
     * @return Yii\db\ActiveQuery
     */
    public function getPaymentFlow()
    {
        return $this->hasOne(DsPaymentFlow::class, ['id' => 'payment_id']);
    }

    /**
     * 商户产品
     * @return \yii\db\ActiveQuery
     */
    public function getGoods()
    {
        return $this->hasOne(ServerProduct::class, ['id' => 'goods_id']);
    }

    /**
     * DS产品
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(DsPdtManage::class, ['id' => 'product_id']);
    }

    /**
     * 商户
     * @return \yii\db\ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }

    /**
     * @param $insert
     *
     * @return bool
     */
    public function beforeSave($insert)
    {
        $this->created_at = StringHelper::intToDate($this->created_at);
        $this->updated_at = StringHelper::intToDate(time());
        return parent::beforeSave($insert);
    }


    /**
     * 创建订单
     *
     * @param array $data
     *
     * @return int
     * @throws Exception
     */
    public static function create($data)
    {
        $model = new self();
        $model->setAttributes($data);
        if (!$model->save()) {
            dd($model->getFirstErrors());
            return false;
        }
        return $model->id;
    }

    /**
     * 更新订单
     *
     * @param array $data
     *
     * @return $this
     * @throws Exception
     */
    public function updateOrder($data)
    {
        $this->setAttributes($data);
        $this->save();
        return $this;
    }
}
