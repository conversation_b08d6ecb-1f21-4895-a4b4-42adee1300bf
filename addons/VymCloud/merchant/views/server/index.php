<?php

use addons\VymCloud\common\enums\ServerEnum;
use addons\VymCloud\common\models\CloudServer;
use common\enums\AppEnum;
use common\helpers\ArrayHelper;
use common\helpers\Html;
use common\helpers\Url;
use kartik\daterange\DateRangePicker;
use yii\grid\ActionColumn;
use yii\grid\GridView;
use yii\widgets\ActiveForm;

/** @var yii\web\View $this */
/** @var yii\data\ActiveDataProvider $dataProvider */
/** @var common\models\base\SearchModel $searchModel */
/** @var array $queryParams 搜索参数 */
/** @var array $clusterList 集群列表 */
/** @var string $beginTime 开始时间 */
/** @var string $endTime 结束时间 */

$this->title                   = '业务实例';
$this->params['breadcrumbs'][] = $this->title;

?>
<!-- Main content -->
<div class="container-fluid">

    <!-- 搜索筛选区域 -->
    <div class="card search-card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-search mr-1"></i>
                筛选条件
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <?php $form = ActiveForm::begin([
                'method'                 => 'get',
                'enableClientValidation' => false,
                'enableAjaxValidation'   => false,
                'options'                => ['class' => 'form-row'],
                'fieldConfig'            => [
                    'template'     => '<div class="input-group-prepend">{label}</div>{input}{error}',
                    'labelOptions' => ['class' => 'col-form-label-sm input-group-text'],
                    'options'      => ['class' => 'input-group col-sm-12 col-md-6 col-lg-3 col-lg-2 mb-2'],
                ],
            ]); ?>

            <?php if (Yii::$app->id == AppEnum::BACKEND): ?>
                <?= $form->field($searchModel, 'merchant_id')->dropDownList(
                    ArrayHelper::map(Yii::$app->services->merchant->findAllByCondition([]), 'id', 'title'),
                    ['prompt' => '选择商户']
                )->label('所属商户') ?>
            <?php endif; ?>

            <?= $form->field($searchModel, 'title')->textInput(['placeholder' => '输入实例名称',])->label('实例名称') ?>
            <?= $form->field($searchModel, 'uid')->textInput(['placeholder' => '输入用户ID',])->label('用户ID') ?>
            <?= $form->field($searchModel, 'ip')->textInput(['placeholder' => '输入IP地址',])->label('IP地址') ?>
            <?= $form->field($searchModel, 'cluster_id')->dropDownList($clusterList, ['prompt' => '选择集群',])->label('所在集群') ?>
            <?= $form->field($searchModel, 'status_server')->dropDownList(ServerEnum::getMap(), ['prompt' => '选择服务器状态',])->label('服务器状态') ?>
            <?= $form->field($searchModel, 'status_power')->dropDownList(ServerEnum::getPowerStatusMap(), ['prompt' => '选择电源状态',])->label('电源状态') ?>

            <div class="col-sm-12 col-md-6 col-lg-3">
                <?= Html::submitButton('<i class="fas fa-search"></i> 搜索', ['class' => 'btn btn-primary btn-md mr-2',]) ?>
                <?= Html::a('<i class="fas fa-redo"></i> 重置', ['index'], ['class' => 'btn btn-outline-secondary btn-md',]) ?>
            </div>
            <?php ActiveForm::end(); ?>
        </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-server mr-1"></i>
                <?= Html::encode($this->title) ?>
            </h3>
            <div class="card-tools">
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <?= GridView::widget([
                    'dataProvider' => $dataProvider,
                    'tableOptions' => [
                        'class' => 'table table-hover rf-table mb-0',
                    ],
                    'columns'      => [
                        [
                            'attribute'      => 'id',
                            'headerOptions'  => ['class' => 'text-left'],
                            'contentOptions' => ['class' => 'text-left'],
                        ],
                        [
                            'attribute'     => 'title',
                            'headerOptions' => ['style' => 'width: 150px;'],
                            'format'        => 'raw',
                            'value'         => function ($model) {
                                return Html::tag('span', Html::encode($model->title), [
                                    'class' => 'font-weight-medium',
                                ]);
                            },
                        ],
                        [
                            'label'          => '所属商户',
                            'headerOptions'  => ['style' => 'width: 120px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'visible'        => Yii::$app->id == AppEnum::BACKEND, // 只在总后台显示
                            'value'          => function ($model) {
                                if ($model->merchant && $mId = $model->merchant->id) {
                                    if ($mTitle = $model->merchant->title) {
                                        return Html::tag('span', $mTitle, ['class' => 'badge badge-primary']);
                                    }
                                    return Html::tag('span', "ID:{$mId}", ['class' => 'badge badge-primary']);
                                }
                                return Html::tag('span', '平台直属', ['class' => 'badge badge-secondary']);
                            },
                        ],
                        [
                            'label'          => '会员账号',
                            'headerOptions'  => ['style' => 'width: 130px;'],
                            'contentOptions' => ['class' => 'text-left'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $uid        = $model->uid;
                                $userName   = $model->member->username;
                                $userEmail  = $model->member->email;
                                $userMobile = $model->member->mobile;
                                $userInfo   = "UID: {$uid}";
                                if ($userName) {
                                    $userInfo .= "<br/>用户: {$userName}";
                                }
                                if ($userEmail) {
                                    $userInfo .= "<br/>邮箱: {$userEmail}";
                                }
                                if ($userMobile) {
                                    $userInfo .= "<br/>手机: {$userMobile}";
                                }
                                return $userInfo;
                            },
                        ],
                        [
                            'label'         => '所在集群',
                            'headerOptions' => ['style' => 'width: 150px;'],
                            'format'        => 'raw',
                            'value'         => function ($model) use ($clusterList) {
                                $cluster = ArrayHelper::getValue($clusterList, $model->cluster_id, '-');
                                $node    = $model->node;
                                $vmid    = $model->vmid;

                                $html = "集群：" . Html::tag('span', $cluster, ['class' => "badge badge-success"]) . "<br/>";
                                $html .= "节点：" . Html::tag('span', $node, ['class' => "badge badge-primary"]) . "<br/>";
                                $html .= "VMID：" . Html::tag('span', $vmid, ['class' => "badge badge-info"]);
                                return $html;
                            },
                        ],
                        [
                            'attribute' => 'detail',
                            'label'     => '服务器配置',
                            'format'    => 'raw',
                            'value'     => function ($model) {
                                $basic     = $model->detail;
                                $cpu       = ArrayHelper::getValue($basic, 'config.cpu');
                                $mem       = ArrayHelper::getValue($basic, 'config.mem');
                                $disk_os   = ArrayHelper::getValue($basic, 'config.disk_os');
                                $disk_data = ArrayHelper::getValue($basic, 'config.disk_data');
                                $ip_num    = ArrayHelper::getValue($basic, 'config.ip_num');
                                $bw        = ArrayHelper::getValue($basic, 'config.bw');
                                $def       = ArrayHelper::getValue($basic, 'config.def');

                                $html = "CPU内存：CPU:{$cpu}H / 内存: {$mem}GB<br/>";
                                $html .= "磁盘配置：系统盘:{$disk_os}GB";
                                if ($disk_data) {
                                    $html .= " / 数据盘:{$disk_data}GB<br/>";
                                } else {
                                    $html .= "<br/>";
                                }
                                $html .= "网络配置：IP数:{$ip_num}个 / 带宽:{$bw}Mbps";
                                $def && $html .= " / 防御:{$def}Gbps";

                                return $html;
                            },
                        ],
                        [
                            'label'         => '网络配置',
                            'headerOptions' => ['style' => 'width: 150px;'],
                            'format'        => 'raw',
                            'value'         => function ($model) {
                                $ip   = $model->ip;
                                $mac  = $model->mac;
                                $html = "主IP：{$ip} <br/>";
                                $mac && $html .= "MAC：{$mac}";
                                return $html;
                            },
                        ],
                        [
                            'attribute'      => 'status_server',
                            'label'          => '服务器状态',
                            'headerOptions'  => ['style' => 'width: 100px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $statusMap    = ServerEnum::getMap();
                                $statusColors = ServerEnum::getMapColors();
                                $use          = $statusMap[$model->status_server] ?? '未知';
                                $useClass     = $statusColors[$model->status_server] ?? 'secondary';
                                // 转换AdminLTE的label颜色到Bootstrap 4的badge颜色
                                $badgeColorMap = [
                                    'success' => 'success',
                                    'warning' => 'warning',
                                    'danger'  => 'danger',
                                    'info'    => 'info',
                                    'primary' => 'primary',
                                    'default' => 'secondary',
                                ];
                                $badgeColor    = $badgeColorMap[$useClass] ?? 'secondary';
                                return Html::tag('span', $use, ['class' => "badge badge-{$badgeColor}"]);
                            },
                        ],
                        [
                            'attribute'      => 'status_power',
                            'label'          => '电源状态',
                            'headerOptions'  => ['style' => 'width: 100px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $statusMap    = ServerEnum::getPowerStatusMap();
                                $statusColors = ServerEnum::getPowerStatusColors();
                                $use          = $statusMap[$model->status_power] ?? '未知';
                                $useClass     = $statusColors[$model->status_power] ?? 'secondary';
                                // 转换AdminLTE的label颜色到Bootstrap 4的badge颜色
                                $badgeColorMap = [
                                    'success' => 'success',
                                    'warning' => 'warning',
                                    'danger'  => 'danger',
                                    'info'    => 'info',
                                    'primary' => 'primary',
                                    'default' => 'secondary',
                                ];
                                $badgeColor    = $badgeColorMap[$useClass] ?? 'secondary';
                                return Html::tag('span', $use, ['class' => "badge badge-{$badgeColor}"]);
                            },
                        ],
                        [
                            'attribute'     => 'end_at',
                            'label'         => '到期信息',
                            'headerOptions' => ['style' => 'width: 150px;'],
                            'format'        => 'raw',
                            'value'         => function ($model) {
                                $renew_mod  = $model->renew_mod;
                                $renew_auto = $model->renew_auto;
                                $end_at     = $model->end_at;
                                $html       = "付款周期：{$renew_mod}月 <br/>";
                                $html       .= "自动续费：";
                                if ($renew_auto) {
                                    $html .= Html::tag('span', '是', ['class' => "badge badge-success"]);
                                } else {
                                    $html .= Html::tag('span', '否', ['class' => "badge badge-danger"]);
                                }
                                $html .= " <br/>到期时间：{$end_at}";
                                return $html;
                            },
                        ],
                        [
                            'header'         => '操作',
                            'headerOptions'  => ['style' => 'width: 120px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $buttons   = [];
                                $buttons[] = Html::a('<i class="fas fa-eye"></i> 详情', ['view', 'id' => $model->id], [
                                    'class' => 'btn btn-sm btn-outline-info',
                                    'title' => '查看详情',
                                ]);
                                return implode(' ', $buttons);
                            },
                        ],
                    ],
                ]); ?>
            </div>
        </div>
    </div>

</div>