<?php

namespace addons\VymCloud\common\models;

use common\behaviors\MerchantStoreBehavior;
use common\models\merchant\Merchant;
use Yii;

/**
 * This is the model class for table "{{%addon_cloud_logs}}".
 *
 * @property int         $log_id      日志ID
 * @property int         $user_id     用户ID
 * @property int|null    $merchant_id 商户id
 * @property int|null    $store_id    店铺id
 * @property int         $user_type   管理员/用户
 * @property string      $action      操作描述
 * @property string|null $ip_address  用户IP地址
 * @property string|null $method      操作方式
 * @property string      $request     请求参数
 * @property string      $result      操作结果
 * @property string      $created_at  操作时间
 */
class CloudLogs extends \yii\db\ActiveRecord
{

    use MerchantStoreBehavior;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%addon_cloud_logs}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['ip_address', 'method'], 'default', 'value' => null],
            [['merchant_id', 'store_id'], 'default', 'value' => 0],
            [['user_id', 'user_type', 'action', 'request', 'result'], 'required'],
            [['user_id', 'user_type'], 'integer'],
            [['request', 'result', 'created_at'], 'safe'],
            [['action'], 'string', 'max' => 255],
            [['ip_address'], 'string', 'max' => 45],
            [['method'], 'string', 'max' => 50],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'log_id'      => '日志ID',
            'user_id'     => '用户ID',
            'merchant_id' => '商户ID',
            'store_id'    => '店铺ID',
            'user_type'   => '管理员/用户',
            'action'      => '操作描述',
            'ip_address'  => '用户IP地址',
            'method'      => '操作方式',
            'request'     => '请求参数',
            'result'      => '操作结果',
            'created_at'  => '操作时间',
        ];
    }

    /**
     * 关联商户
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }
}
