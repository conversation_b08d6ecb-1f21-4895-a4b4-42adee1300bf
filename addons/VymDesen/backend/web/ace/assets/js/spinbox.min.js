/*
 * Fuel UX Spinbox
 * https://github.com/ExactTarget/fuelux
 *
 * Copyright (c) 2014 ExactTarget
 * Licensed under the BSD New license.
 */
!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):"object"==typeof exports?module.exports=a(require("jquery")):a(jQuery)}(function(a){var b=a.fn.spinbox,c=function(b,c){this.$element=a(b),this.$element.find(".btn").on("click",function(a){a.preventDefault()}),this.options=a.extend({},a.fn.spinbox.defaults,c),this.options.step=this.$element.data("step")||this.options.step,this.options.value<this.options.min?this.options.value=this.options.min:this.options.max<this.options.value&&(this.options.value=this.options.max),this.$input=this.$element.find(".spinbox-input"),this.$input.on("focusout.fu.spinbox",this.$input,a.proxy(this.change,this)),this.$element.on("keydown.fu.spinbox",this.$input,a.proxy(this.keydown,this)),this.$element.on("keyup.fu.spinbox",this.$input,a.proxy(this.keyup,this)),this.bindMousewheelListeners(),this.mousewheelTimeout={},this.options.hold?(this.$element.on("mousedown.fu.spinbox",".spinbox-up",a.proxy(function(){this.startSpin(!0)},this)),this.$element.on("mouseup.fu.spinbox",".spinbox-up, .spinbox-down",a.proxy(this.stopSpin,this)),this.$element.on("mouseout.fu.spinbox",".spinbox-up, .spinbox-down",a.proxy(this.stopSpin,this)),this.$element.on("mousedown.fu.spinbox",".spinbox-down",a.proxy(function(){this.startSpin(!1)},this))):(this.$element.on("click.fu.spinbox",".spinbox-up",a.proxy(function(){this.step(!0)},this)),this.$element.on("click.fu.spinbox",".spinbox-down",a.proxy(function(){this.step(!1)},this))),this.switches={count:1,enabled:!0},"medium"===this.options.speed?this.switches.speed=300:"fast"===this.options.speed?this.switches.speed=100:this.switches.speed=500,this.options.defaultUnit=e(this.options.defaultUnit,this.options.units)?this.options.defaultUnit:"",this.unit=this.options.defaultUnit,this.lastValue=this.options.value,this.render(),this.options.disabled&&this.disable()},d=function(a,b){return Math.round(a/b)*b},e=function(b,c){var d=!1,e=b.toLowerCase();return a.each(c,function(a,b){return b=b.toLowerCase(),e===b?(d=!0,!1):void 0}),d},f=function(a){return isNaN(parseFloat(a))?a:(a>this.options.max?a=this.options.cycle?this.options.min:this.options.max:a<this.options.min&&(a=this.options.cycle?this.options.max:this.options.min),this.options.limitToStep&&this.options.step&&(a=d(a,this.options.step),a>this.options.max?a-=this.options.step:a<this.options.min&&(a+=this.options.step)),a)};c.prototype={constructor:c,destroy:function(){return this.$element.remove(),this.$element.find("input").each(function(){a(this).attr("value",a(this).val())}),this.$element[0].outerHTML},render:function(){this.setValue(this.getDisplayValue())},change:function(){this.setValue(this.getDisplayValue()),this.triggerChangedEvent()},stopSpin:function(){void 0!==this.switches.timeout&&(clearTimeout(this.switches.timeout),this.switches.count=1,this.triggerChangedEvent())},triggerChangedEvent:function(){var a=this.getValue();a!==this.lastValue&&(this.lastValue=a,this.$element.trigger("changed.fu.spinbox",a))},startSpin:function(b){if(!this.options.disabled){var c=this.switches.count;1===c?(this.step(b),c=1):c=3>c?1.5:8>c?2.5:4,this.switches.timeout=setTimeout(a.proxy(function(){this.iterate(b)},this),this.switches.speed/c),this.switches.count++}},iterate:function(a){this.step(a),this.startSpin(a)},step:function(a){this.setValue(this.getDisplayValue());var b;b=a?this.options.value+this.options.step:this.options.value-this.options.step,b=b.toFixed(5),this.setValue(b+this.unit)},getDisplayValue:function(){var a=this.parseInput(this.$input.val()),b=a?a:this.options.value;return b},setDisplayValue:function(a){this.$input.val(a)},getValue:function(){var a=this.options.value;return"."!==this.options.decimalMark&&(a=(a+"").split(".").join(this.options.decimalMark)),a+this.unit},setValue:function(a){if("."!==this.options.decimalMark&&(a=this.parseInput(a)),"number"!=typeof a){var b=a.replace(/[0-9.-]/g,"");this.unit=e(b,this.options.units)?b:this.options.defaultUnit}var c=this.getIntValue(a);return isNaN(c)&&!isFinite(c)?this.setValue(this.options.value):(c=f.call(this,c),this.options.value=c,a=c+this.unit,"."!==this.options.decimalMark&&(a=(a+"").split(".").join(this.options.decimalMark)),this.setDisplayValue(a),this)},value:function(a){return a||0===a?this.setValue(a):this.getValue()},parseInput:function(a){return a=(a+"").split(this.options.decimalMark).join(".")},getIntValue:function(a){return a="undefined"==typeof a?this.getValue():a,"undefined"!=typeof a?("string"==typeof a&&(a=this.parseInput(a)),a=parseFloat(a,10)):void 0},disable:function(){this.options.disabled=!0,this.$element.addClass("disabled"),this.$input.attr("disabled",""),this.$element.find("button").addClass("disabled")},enable:function(){this.options.disabled=!1,this.$element.removeClass("disabled"),this.$input.removeAttr("disabled"),this.$element.find("button").removeClass("disabled")},keydown:function(a){var b=a.keyCode;38===b?this.step(!0):40===b?this.step(!1):13===b&&this.change()},keyup:function(a){var b=a.keyCode;38!==b&&40!==b||this.triggerChangedEvent()},bindMousewheelListeners:function(){var b=this.$input.get(0);b.addEventListener?(b.addEventListener("mousewheel",a.proxy(this.mousewheelHandler,this),!1),b.addEventListener("DOMMouseScroll",a.proxy(this.mousewheelHandler,this),!1)):b.attachEvent("onmousewheel",a.proxy(this.mousewheelHandler,this))},mousewheelHandler:function(a){if(!this.options.disabled){var b=window.event||a,c=Math.max(-1,Math.min(1,b.wheelDelta||-b.detail)),d=this;return clearTimeout(this.mousewheelTimeout),this.mousewheelTimeout=setTimeout(function(){d.triggerChangedEvent()},300),0>c?this.step(!0):this.step(!1),b.preventDefault?b.preventDefault():b.returnValue=!1,!1}}},a.fn.spinbox=function(b){var d,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.spinbox"),h="object"==typeof b&&b;g||f.data("fu.spinbox",g=new c(this,h)),"string"==typeof b&&(d=g[b].apply(g,e))});return void 0===d?f:d},a.fn.spinbox.defaults={value:0,min:0,max:999,step:1,hold:!0,speed:"medium",disabled:!1,cycle:!1,units:[],decimalMark:".",defaultUnit:"",limitToStep:!1},a.fn.spinbox.Constructor=c,a.fn.spinbox.noConflict=function(){return a.fn.spinbox=b,this},a(document).on("mousedown.fu.spinbox.data-api","[data-initialize=spinbox]",function(b){var c=a(b.target).closest(".spinbox");c.data("fu.spinbox")||c.spinbox(c.data())}),a(function(){a("[data-initialize=spinbox]").each(function(){var b=a(this);b.data("fu.spinbox")||b.spinbox(b.data())})})});