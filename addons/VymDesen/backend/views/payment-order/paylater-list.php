<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\LinkPager;

$this->title = '控制台管理-后付款单列表';
?>
<?php $this->beginBlock('content') ?>
<?php $node = \Yii::$app->session['auth']['node'];?>
<div class="row">    			
	<div class="clearfix">
		<div class="pull-right tableTools-container"></div>
	</div>  
</div>  		
<div class="row" style="margin-bottom: 10px;">
	<form action="">
		<input type="hidden" name="r" value="payment-order/paylater-list"/>
		<div class="col-xs-10">
			<div style="float: left;margin-right: 6px;">
				<input type="text" class="form-control search-query" name="payment_trans_number" value='<?php echo Html::encode(Yii::$app->controller->get('payment_trans_number')) ?>'  placeholder="支付号">
			</div>
			<div style="float: left;margin-right: 6px;">
				<input type="text" class="form-control search-query" name="user_email" value='<?php echo Html::encode(Yii::$app->controller->get('user_email')) ?>'  placeholder="用户账户">
			</div>
			<div style="float: left;margin-right: 6px;">
				<input type="text" class="form-control search-query" name="user_name" value='<?php echo Html::encode(Yii::$app->controller->get('user_name')) ?>'  placeholder="用户名称">
			</div>
			<div style="float: left;margin-right: 6px;">                        
				<select class="form-control" name='admin_id' id="form-field-select-1" >
					<option value="">销售名称</option>
					<?php foreach ($UserAdminRes as $value): ?>
					<option value="<?php echo $value['admin_id'] ?>" rel="<?php echo $value['uname'] ?>"   
					<?php if ($value['admin_id'] == Html::encode(Yii::$app->controller->get('admin_id')) ): ?>
					 selected='selected'
					<?php endif ?>
					>
					<?php echo $value['rename'] ?></option>
					<?php endforeach ?>
				</select>
			</div>
			<div style="float: left;margin-right: 6px;">
				 <input type="text" class="" name="start_time" id="start_time"  value='<?php echo Html::encode(Yii::$app->controller->get('start_time')) ?>'  placeholder="支付时间" readonly> 至
			</div>
			<div style="float: left;margin-right: 6px;">
				 <input type="text" class="" name="end_time" id="end_time" value='<?php echo Html::encode(Yii::$app->controller->get('end_time')) ?>'  placeholder="支付时间" readonly>
			</div>							    
        </div>
        <div class="col-xs-2">
			<div>
				<button type="submit" class="btn btn-white btn-primary btn-bold" style="margin-left:10px">
					<span class="ace-icon fa fa-search icon-on-right bigger-110"></span>
					搜索
				</button>				
				<button type="button" class="btn btn-white btn-success btn-bold" style="margin-left:10px;" onclick="javascript:location.href='<?php echo Url::to(['payment-order/paylater-list']) ?>'">
					<span class="ace-icon fa  fa-refresh"></span>
					刷新
				</button>
			</div>
		</div>							
	</form>
</div>
<form action="" id='checkbox'>
	<table id="simple-table" width="100%" class="table table-bordered table-hover">
		<thead>
			<tr><th colspan="12">
				<a  href="javascript:void(0)" id="go_pay" class="btn btn-white btn-success btn-bold" style="margin-left:10px;">
					<span class="ace-icon fa fa-share "></span>
					去付款
				</a>
			
			</th></tr>
			<tr>
				<th class="center">
				<label class="pos-rel">
					<input type="checkbox" class="ace" />
					<span class="lbl"></span>
				</label>
			</th> 
				<th>客户</th>
				<th>销售</th>
				<th>付款单号</th>
				<th>所属订单</th>
				<th>金额</th>
				<th>支付方式</th>
				<th>三方交易号</th>				
				<th>支付时间</th>
				<th>创建时间</th>
				<th>状态</th>
				<th class="hidden-480">操作</th>				
			</tr>
		</thead>
		<tbody>
			<?php foreach ($PayorderGeneralList as $key => $val): ?>
				<td class="center">
					<?php if(in_array($val['general_pay_lock'], ['等待支付', '后付款'])):?>
					<label class="pos-rel">
						<input type="checkbox" name="payorder_no[]" value="<?php echo $val['general_payorder'] ?>" class="ace">
						<span class="lbl"></span>
					</label>
					<?php endif;?>
				</td>
				<td><?php echo $val['original']['order_user_nickname']?></td>
				<td><?php echo $val['original']['order_admin_name']?></td>
				<td><?php echo $val['general_payorder']?></td>
				<td><?php echo $val['general_original_order']?></td>
				<td><?php echo $val['general_pay_money']?></td>
				<td><?php echo $val['general_pay_platform']?></td>
				<td><?php echo $val['general_callback_stream']?></td>				
				<td><?php echo $val['general_pay_time'] ? date('Y-m-d H:i:s', $val['general_pay_time']) : ''; ?></td>
				<td><?php echo date('Y-m-d H:i:s', $val['general_create_time']) ?></td>
				<td>
					<?php if( $val['general_pay_lock'] == '等待支付'):?>
							<span class='label label-warning'><?php echo $val['general_pay_lock']; ?></span>
					<?php elseif( $val['general_pay_lock'] == '后付款'):?>
							<span class='label label-pink'><?php echo $val['general_pay_lock']; ?></span>
					<?php elseif( $val['general_pay_lock'] == '已取消'):?>
							<span class='label label-danger'><?php echo $val['general_pay_lock']; ?></span>
					<?php elseif( $val['general_pay_lock'] == '支付完成'):?>
							<span class='label label-success'><?php echo $val['general_pay_lock']; ?></span>
					<?php endif;?>
				</td>
				<td class="hidden-480">					
					<a class="blue" href="<?php echo Url::to(['details', 'payorder_no' => $val['general_payorder']])?>">详情</a>
				</td>
			</tr>				
		<?php endforeach;?>
		</tbody>						  
	</table>	
	<div class="row" style="margin-top: 10px;">
		<div class="col-xs-6">
			<div class="dataTables_info" id="dynamic-table_info" role="status" aria-live="polite">共有<?php echo $iCount;?>条记录</div>
		</div>
		<div class="col-xs-6 pagination1" style="text-align: right;">
			<?php 
				echo LinkPager::widget([
					'pagination' => $page,
					'firstPageLabel'=>"首页",
					'prevPageLabel'=>'上一页',
					'nextPageLabel'=>'下一页',
					'lastPageLabel'=>'末页',
				]);
			?>
		</div>
	</div>
</form>	
<style type="text/css">
.form-horizontal {padding-left: 30px;}
.font-size-12 {font-size: 12px;}
.ny-form .form-group {margin-bottom: 12px;line-height: 30px;}
.form-horizontal .form-group {margin-right: -15px;margin-left: -15px;}
.ny-form .ny-control-label {float: left;}
.ny-control-label {width: 135px;padding-right: 0;text-align: right;color: #808080;}
.ny-form-control {display: inline-block;white-space: nowrap;color: #555;background-color: #fff;background-image: none;outline: none;}    
.ny-number-container {float: left;line-height: 1;}
.number-input-box {width: 131px;}
.number-input-box {float: left;position: relative;width: 100px;border-radius: 2px;}
.alert-warn, .alert-error, .alert-success {padding: 7px 22px 5px 37px;background: url(img/uc/tip_icon_warn_16.png) no-repeat 10px 10px #fff3e5;
border: 1px solid #ff8800;border-radius: 2px;color: #ff8800;font-size: 12px;line-height: 2em;}
.margin-bottom-20 {margin-bottom: 20px;}
</style>
<!-- 审核 start -->
<div class="bootbox modal fade bootbox-prompt in" id="payment_audit_modal" tabindex="-1" role="dialog" style="padding-right: 17px;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
                    <font style="vertical-align: inherit;">
                        <font style="vertical-align: inherit;">×</font>
                    </font>
                </button>
                <h4 class="modal-title">
                <font style="vertical-align: inherit;"><font style="vertical-align: inherit;">支付信息审核</font></font></h4>
            </div>
            <div class="modal-body">
                <div class="bootbox-body">
					<div class="alert-warn margin-bottom-20">
						* 请核对金额、信息是否正确
					</div>
                    <form id="payment_audit_form" action="" class="bootbox-form form-horizontal ny-form ny-tab-container font-size-12 tab-group-undefined">  
						<div class="form-group">
							<div class="ny-control-label">金额：</div>
							<div class="col-xs-8 ny-form-control payment_amount"></div>
						</div>
						<div class="form-group">
            				<div class="ny-control-label">是否准确：</div>
            				<div class="col-xs-8 ny-form-control">
            					<div class="ny-number-container">
								   <label>
										<input name="is_right" type="radio" value="Y" checked="" class="ace">
										<span class="lbl"> 信息正确</span>
									</label>
									&nbsp;&nbsp;&nbsp;
									<label>
										<input name="is_right" type="radio" value="N" class="ace">
										<span class="lbl"> 支付信息有误</span>
									</label>
            					</div>
            				</div>
            			</div>  
            			<div class="form-group">
            				<div class="ny-control-label">交易号：</div>
            				<div class="col-xs-8 ny-form-control">
            					<div class="ny-number-container">
            					   <span class="number-input-box" style="width: 400px;">
									<input type="text" name="payment_trans_no" value = "" class="ny-input-reset ny-number-input col-xs-8"></span>
            					</div>
            				</div>
            			</div>  
						<div class="form-group">
            				<div class="ny-control-label">收付款时间：</div>
            				<div class="col-xs-8 ny-form-control">
            					<div class="ny-number-container">
            					   <span class="number-input-box" style="width: 400px;">
								   <input type="text" name="payment_successtime" value = "" class="ny-input-reset ny-number-input col-xs-8"></span>
            					</div>
            				</div>
            			</div>
            			<div class="form-group">
            				<div class="ny-control-label">备注：</div>
            				<div class="col-xs-8 ny-form-control">
            					<div class="ny-number-container">
            					   <span class="number-input-box" style="width: 400px;">
								   <input type="text" name="reason_remark" value="" class="ny-input-reset ny-number-input col-xs-8"></span></div>
            				</div>
            			</div>
            			<input type="hidden" name="id" value=""> 
            		</form>            		
                </div>
            </div>
            <div class="modal-footer">
                <button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
                    <font style="vertical-align: inherit;">
                        <font style="vertical-align: inherit;">取消</font></font>
                </button>
                <button data-bb-handler="confirm" data-last="Finish" type="button" class="btn btn-primary" id="payment_audit_go">
                    <font style="vertical-align: inherit;"><font style="vertical-align: inherit;">确定</font></font>
                </button>
            </div>
        </div>
    </div>
</div>
<!--审核 end -->

<!-- page specific plugin scripts -->


<script type="text/javascript">
	var start = {
		elem: '#start_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas){
			 end.min = datas; //开始日选好后，重置结束日的最小日期
			 end.start = datas //将结束日的初始值设定为开始日
		}
	};
	var end = {
		elem: '#end_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas){
			 end.min = datas; //开始日选好后，重置结束日的最小日期
			 end.start = datas //将结束日的初始值设定为开始日
		}
	};
	laydate(start);
	laydate(end);
	laydate.skin('molv');
	
	
	$('#go_pay').click(function() {
		var _this = $(this);		
		if ( $("[name='payorder_no[]']:checked").length <= 0 ) {
			layer.alert('请至少选择一条付款单', {icon:7});
			return false;
		}
		var payorder_no =[];
		var num = $("[name='payorder_no[]']:checked").length;
		$('input[name="payorder_no[]"]:checked').each(function(){
			payorder_no.push($(this).val());
		});		
		var payorders = payorder_no.join([',']);//console.log(payorders);return false;
		layer.confirm('您一共选择了'+num+'条付款单', {icon:3,
			btn: ['确定','取消'] //按钮
		}, function(){
			window.location.href="<?php echo Url::to(['merge-payorderlist'])?>"+"?payorder_no="+payorders;
		}, function(){
		   
		});
		return false;
	});

	
	jQuery(function($) {			
		//And for the first simple table, which doesn't have TableTools or dataTables
		//select/deselect all rows according to table header checkbox
		var active_class = 'active';
		$('#simple-table > thead > tr > th input[type=checkbox]').eq(0).on('click', function(){
			var th_checked = this.checked;//checkbox inside "TH" table header				
			$(this).closest('table').find('tbody > tr').each(function(){
				var row = this;
				if(th_checked) $(row).addClass(active_class).find('input[type=checkbox]').eq(0).prop('checked', true);
				else $(row).removeClass(active_class).find('input[type=checkbox]').eq(0).prop('checked', false);
			});
		});				
		//select/deselect a row when the checkbox is checked/unchecked
		$('#simple-table').on('click', 'td input[type=checkbox]' , function(){
			var $row = $(this).closest('tr');
			if($row.is('.detail-row ')) return;
			if(this.checked) $row.addClass(active_class);
			else $row.removeClass(active_class);
		});			
		/***************/
		$('.show-details-btn').on('click', function(e) {
			e.preventDefault();
			$(this).closest('tr').next().toggleClass('open');
			$(this).find(ace.vars['.icon']).toggleClass('fa-angle-double-down').toggleClass('fa-angle-double-up');
		});
		
		function questionClick(obj) {
			var id = obj.attr('financeid');
			var point = obj.attr('financepoint');
			var _this = obj;
			
			var url = "<?php echo Url::to(['question'])?>";
			$.post(url, {"id":id, "point":point}, function(e) {
				if(point == 'Y') {
					_this.parents('tr:eq(0)').css("background","#FFFFFF");
					_this.attr('financepoint', 'N');
					_this.html('疑问标记');
					
				} else {
					_this.parents('tr:eq(0)').css("background","#FFF68F");
					_this.attr('financepoint', 'Y');
					_this.html('撤销标记');
				}
				/*
				$(".hasQuestion").click(function() {
					alert(123);
					questionClick($(this));
				});*/
				
			}, "json");
		}
		
		//疑问标记
		$(".hasQuestion").click(function() {		
			questionClick($(this));
		});		
	});	

</script>
<?php $this->endBlock(); ?>
