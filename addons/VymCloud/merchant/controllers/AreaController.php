<?php

namespace addons\VymCloud\merchant\controllers;

use addons\VymCloud\common\models\CloudArea;
use yii\data\ActiveDataProvider;
use addons\VymCloud\merchant\controllers\BaseController;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use common\models\base\SearchModel;
use common\traits\MerchantCurd;
use common\enums\StatusEnum;
use Yii;

/**
 * AreaController implements the CRUD actions for CloudArea model.
 */
class AreaController extends BaseController
{

    // use MerchantCurd;

    /**
     * @var CloudArea
     */
    public $modelClass = CloudArea::class;


    /**
     * 首页
     *
     * @return string
     * @throws \yii\web\NotFoundHttpException
     */
    public function actionIndex()
    {
        $searchModel = new SearchModel([
            'model'                  => $this->modelClass,
            'scenario'               => 'default',
            'partialMatchAttributes' => ['title'], // 模糊查询
            'defaultOrder'           => [
                'sort' => SORT_ASC,
                'id'   => SORT_DESC,
            ],
            'pageSize'               => $this->pageSize,
        ]);

        $dataProvider = $searchModel->search($this->request->queryParams);
        // $dataProvider->query->andWhere(['status' => StatusEnum::ENABLED]);
        $this->addQueryParam($dataProvider);
        return $this->render($this->action->id, [
            'dataProvider' => $dataProvider,
            'searchModel'  => $searchModel,
        ]);
    }

    /**
     * ajax编辑/创建
     *
     * @return mixed|string|\yii\web\Response
     * @throws \yii\base\ExitException
     */
    public function actionAjaxEdit()
    {
        $id         = $this->request->get('id');
        $model      = $this->findModel($id);
        $model->pid = $this->request->get('pid', null) ?? $model->pid; // 父id

        $merId = $this->getMerchantId();
        // ajax 校验
        $this->activeFormValidate($model);
        if ($model->load($this->request->post())) {
            return $model->save()
                ? $this->redirect($this->request->referrer)
                : $this->message($this->getError($model), $this->redirect($this->request->referrer), 'error');
        }

        return $this->renderAjax($this->action->id, [
            'model'        => $model,
            'dropDownList' => Yii::$app->vymCloudService->area->getDropDownForEdit($id),
        ]);
    }


    /**
     * 删除
     *
     * @param $id
     *
     * @return mixed
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    public function actionDelete($id)
    {
        if ($this->findModel($id)->delete()) {
            return $this->message("删除成功", $this->redirect($this->request->referrer));
        }

        return $this->message("删除失败", $this->redirect($this->request->referrer), 'error');
    }
}
