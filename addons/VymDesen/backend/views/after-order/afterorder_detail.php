<?php

$this->title = '控制台管理-售后工单-工单详情';

use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\helpers\Html;
use addons\VymDesen\common\components\DataHelper;
?>

<?php $node = \Yii::$app->session['auth']['node'];?>
<?php $this->beginBlock('content') ?>

<style type="text/css">
	*{margin:0;padding:0;}
	ul,li{list-style:none;margin:0;padding:0;}
	a{text-decoration:none;}
	.clearfix{clear:both;}
	
	.info-title{height:40px;width:100%;line-height:50px;font-weight:bold;border-bottom:2px solid rgba(232,233,234,1);margin-bottom:10px;}
	.info-box{margin:15px 0px;padding:0 20px;}
	.info-box .customertable{border:none;width:700px;}
	.info-box .customertable tr{border:none;}
	.info-box .customertable tr td{border:none;padding:4px;}
	.info-box .customertable tr td:nth-child(odd){width:100px;text-align:right;}
	
	.table tr td input{height:30px;line-height:30px;}
	.table tr td .fact_price{width:80px;}
	.table tr td .trade_num{width:40px;}
	
	.updatebox{margin-bottom:10px;}
	.updatebox .total-box{float:left;display:inline-block;margin-right:30px;}
	.updatebox .total-box div{float:left;height:35px;line-height:35px;display:block;}
	.updatebox .total-box input{float:left;height:35px;line-height:35px;}
	.updatebox .total-box .tips{color:#f00;width:100%;}
	.main_price_total, .main_price_fact, .main_pay_money, .main_pay_remark{margin-right:20px;font-size:20px;line-height:30px!important;}
	.main_price_total{color:#2b7dbc;}
	.main_price_fact{color:rgba(221, 55, 81, 1)}
	.main_price_fact button{margin-left:10px;}
	
	.float_table{border:none;}
	.float_table tr td{height:40px;line-height:40px!important;}
	.float_table tr td:first-child{text-align:right;width:150px;}
	.float_table tr td input,textarea{width:180px;}
	.float_table tr td select{width:300px;}
	.float_table tr td textarea{line-height:24px;}
	
	.dobox{float:left;padding:8px 15px;color:#fff;cursor:pointer;margin-right:20px;}
	.do-paylater{background:#428bca;}
	.do-payunderline{background:#82af6f;}
	
	.editbox a{float:right;margin-bottom:10px;}
	
	.tips{color:#f00;width:100%;}
	

	.changeConfigBox{width:400px;height:30px;line-height:30px;}
	.changeConfigBox .title{width:70px;float:left;text-align:center;height:30px;line-height:30px;border-right:1px #ccc solid;}
	.changeConfigBox .content{width:320px;float:left;text-align:left;height:30px;line-height:30px;}
	.changeConfigBox .content .front_content{width:140px;height:30px;line-height:30px;float:left;padding:0 5px;border-right:1px #ccc solid;}
	.changeConfigBox .content .after_content{width:140px;height:30px;line-height:30px;float:left;padding:0 5px;}
	.changeConfigBox .content .small{width:100px!important;}
	.changeConfigBox .smallcontent{width:240px!important;}
	.smallconfigbox{width:320px!important}
	.renewBox{float:left;height:30px;width:30px;display:inline-block;text-align:center;line-height:30px;}
	.minusRenew{border:1px #ccc solid;cursor:pointer;font-size:30px;line-height:28px;}
	.plusRenew{border:1px #ccc solid;cursor:pointer;font-size:26px;}
	
	.baseinfo-table{border:none;width:600px;display:inline-block;}
	.baseinfo-table tr td:first-child{width:200px;text-align:right;}
	.baseinfo-table tr td{border:none!important;}
	
	.configTable{margin-top:10px;}
	
	.trailList{height:40px;}
	.trailList p{float:left;height:26px;padding:0 10px;line-height:24px;text-align:center;margin-right:10px;margin-top:10px;border:1px #333 solid;border-radius:3px; -webkit-border-radius:3px; -moz-border-radius:3px;}
</style>
<div id="mainContainer">
	<div class="info-title">基础信息</div>
	<div class="info-box">
		<table class="table table-bordered">
			<tr>
				<td>工单号</td>
				<td>工作记录号</td>
				<td>工作记录类型</td>
				<td>用户昵称</td>
				<td>用户账户</td>
				<td>工单发起人</td>
				<td>发起时间</td>
			</tr>
			<tr>
				<td><?php echo $AfterOrderInfo['ao_id'];?></td>
				<td><?php echo $AfterOrderInfo['linelist']['line_id'];?>&nbsp;&nbsp;&nbsp;&nbsp;<a class="blue" href="<?php echo Url::to(['pipe-line/pipe-line-detail', 'line_id' => $AfterOrderInfo['linelist']['line_id']])?>">查看工作记录</a></td>
				<td><?php echo $AfterOrderInfo['line_type_info']['line_type_name'];?></td>
				<td><?php echo $AfterOrderInfo['linelist']['user_nickname'] ? $AfterOrderInfo['linelist']['user_nickname'] : '-';?></td>
				<td><?php echo $AfterOrderInfo['linelist']['user_account'] ? $AfterOrderInfo['linelist']['user_account'].'&nbsp;&nbsp;<a class="blue" href="'. Url::to(['user-member/info-item', 'u_id' => $AfterOrderInfo['linelist']['user_id']]) .'" >查看用户</a>' : '-';?></td>
				<td><?php echo $AfterOrderInfo['linelist']['request_admin_name'];?></td>
				<td><?php echo date("Y-m-d H:i:s", $AfterOrderInfo['linelist']['line_request_time']);?></td>
			</tr>
		</table>
	</div>
	<div class="info-title">工单要求</div>
	<div class="info-box">
		<?php 
			$allow_admin[] =  $AfterOrderInfo['ao_takeover_admin_id'];
			if( $AfterOrderInfo['ao_shiftduty_admin_id'] != '') {
				$allow_admin[] =  $AfterOrderInfo['ao_shiftduty_admin_id'];
			}
		?>
		
		<?php $content = json_decode($AfterOrderInfo['ao_request_content'], true);?>
	
		<?php if($AfterOrderInfo['line_type_info']['line_type_name'] == '新购业务'):?>
		
			<table class="table table-bordered configTable">
				<tr style="background:#a5a5a5;color:#fff;">
					<td style="background:#fff;width:110px;"></td>
					<td>产品渠道</td>
					<td>是否测试机</td>
					<td>服务器分类</td>
					<td>机房名称</td>
					<td>产品配置</td>
					<td>供应商</td>
					<td>起租时间</td>
				</tr>
				<tr>
					<td></td>
					<td>
						<?php if($content['preset']['servicerprovider'] == 1):?>
							<label class="label label-warning">供应商</label>
						<?php else:?>
							<label class="label label-success">自有</label>
						<?php endif;?>
					</td>
					<td>
						<?php if(isset($content['preset']['test_id']) && $content['preset']['test_id'] != ''):?>
							<label class="label label-success">是</label>
						<?php else:?>
							<label class="label label-default">否</label>
						<?php endif;?>
					</td>
					<td><?php echo Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$content['preset']["server_type_id"]."'")->queryScalar();?></td>
					<td><?php echo Yii::$app->db->createCommand("select name from pdt_room_manage where id = '".$content['preset']["room_id"]."'")->queryScalar();?></td>
					<td><?php echo Yii::$app->db->createCommand("select name from pdt_manage where id = '".$content['preset']["pdt_id"]."'")->queryScalar();?></td>
					<td><?php echo isset($content['preset']['provider_id']) && $content['preset']['provider_id'] != '' ? Yii::$app->db->createCommand("select name from provider where id = '".$content['preset']["provider_id"]."'")->queryScalar() : '-';?></td>
					<td><?php echo $content['preset']['start_date'];?></td>
				</tr>
				<tr>
					<td style="background:#a5a5a5;color:#fff;width:110px;">详细配置</td>
					<td colspan="4">
						<?php echo $content['preset']['config']['cpu'].' / '.$content['preset']['config']['ram'].' / '.$content['preset']['config']['hdd'].' / '.$content['preset']['config']['ipnumber'].' / '.$content['preset']['config']['defense'].' / '.$content['preset']['config']['operatsystem']?>
					</td>
					<td style="background:#a5a5a5;color:#fff;width:170px;">要求带宽 / 实际带宽</td>
					<td colspan="3">
						<?php echo $content['preset']['config']['requirement_bandwidth'].' / '.$content['preset']['config']['configbandwidth'];?>
					</td>
				</tr>
				<tr>
					<td style="background:#a5a5a5;color:#fff;width:110px;">IP地址</td>
					<td colspan="8">
						<?php foreach($content['preset']['ip'] as $v):?>
							<input type="text" value="<?php echo $v?>" readonly />
						<?php endforeach?>
					</td>
				</tr>
				<tr>
					<td style="background:#a5a5a5;color:#fff;width:110px;">登陆账号</td>
					<td colspan="2">
						<input type="text" value="<?php echo $content['preset']['install_account']?>" readonly />
					</td>
					<td style="background:#a5a5a5;color:#fff;width:110px;">密码</td>
					<td colspan="2">
						<input type="text" value="<?php echo $content['preset']['install_pass']?>" readonly />
					</td>
					<td style="background:#a5a5a5;color:#fff;width:110px;">端口号</td>
					<td colspan="4">
						<input type="text" value="<?php echo $content['preset']['install_port']?>" readonly />
					</td>
				</tr>
                <?php if(isset($content['preset']['operating_system']) && $content['preset']['operating_system'] != ''):?>
                <tr>
                    <td style="background:#a5a5a5;color:#fff;width:110px;">需安装系统</td>
                    <td colspan="8">
                        <?php echo $content['preset']['operating_system']?>
                    </td>
                </tr>
                <?php endif;?>
				<tr>
					<td style="background:#a5a5a5;color:#fff;width:110px;">配置备注</td>
					<td colspan="8">
						<?php echo $content['preset']['config_remark']?>
					</td>
				</tr>
                <?php if(isset($content['workorder_notes'])):?>
                    <tr>
                        <td style="background:#a5a5a5;color:#fff;width:110px;">工单备注</td>
                        <td colspan="8">
                            <?php echo $content['workorder_notes'];?>
                        </td>
                    </tr>
                <?php endif;?>
			</table>
			
			<?php if( in_array($admin_id, $allow_admin) && $AfterOrderInfo['ao_type'] == '未完成'):?>
				<?php $hasSonorder = false;
					foreach($sonList as $v) {
						if($v['ao_s_status'] == '未完成') {
							$hasSonorder = true;
							break;
						}
					}
				?>
				
				<?php if($hasSonorder):?>
					<label class="label label-danger">当前还有子订单尚未完成，完成后可在此处增加新的子订单</label>
				<?php else:?>
					<div class="add_sonorder_box">
						<?php if(in_array("after-order-son/purchase-replace-machine", $node) || $this->params['is_administrator_user']):?>
							<a href="<?php echo Url::to(['after-order-son/purchase-replace-machine', 'ao_id' => $AfterOrderInfo['ao_id']])?>" class="btn btn-info">重新分配机器</a>
						<?php endif;?>
						
						<?php if(!isset($content['preset']['test_id']) || $content['preset']['test_id'] == ''):?>
							<?php if(in_array("after-order-son/purchase-replaceip", $node) || $this->params['is_administrator_user']):?>
								<a href="<?php echo Url::to(['after-order-son/purchase-replaceip', 'ao_id' => $AfterOrderInfo['ao_id']])?>" class="btn btn-success">更换IP</a>
							<?php endif;?>
						<?php endif;?>
						
						<?php if($content['preset']['servicerprovider'] == 0):?>
							<?php if(in_array("after-order-son/reload-system", $node) || $this->params['is_administrator_user']):?>
								<a href="javascript:;" class="btn btn-danger Reload_system">自有机器 - 重装系统</a>
							<?php endif;?>
							
						<?php endif;?>
					</div>
				<?php endif;?>
			<?php endif?>
		<?php endif;?>
		
		
		<?php if($AfterOrderInfo['line_type_info']['line_type_name'] == '新增测试机'):?>
		
			<table class="table table-bordered configTable">
				<tr style="background:#a5a5a5;color:#fff;">
					<td style="background:#fff;width:110px;"></td>
					<td>产品渠道</td>
					<td>服务器分类</td>
					<td>机房名称</td>
					<td>产品配置</td>
					<td>供应商</td>
				</tr>
				<tr>
					<td></td>
					<td>
						<?php if($content['servicerprovider'] == 1):?>
							<label class="label label-warning">供应商</label>
						<?php else:?>
							<label class="label label-success">自有</label>
						<?php endif;?>
					</td>
					<td><?php echo Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$content["server_type_id"]."'")->queryScalar();?></td>
					<td><?php echo Yii::$app->db->createCommand("select name from pdt_room_manage where id = '".$content["room_id"]."'")->queryScalar();?></td>
					<td><?php echo Yii::$app->db->createCommand("select name from pdt_manage where id = '".$content["pdt_id"]."'")->queryScalar();?></td>
					<td><?php echo isset($content['provider_id']) && $content['provider_id'] != '' ? Yii::$app->db->createCommand("select name from provider where id = '".$content["provider_id"]."'")->queryScalar() : '-';?></td>
				</tr>
				<tr>
					<td style="background:#a5a5a5;color:#fff;width:110px;">详细配置</td>
					<td colspan="2">
						<?php echo $content['config']['cpu'].' / '.$content['config']['ram'].' / '.$content['config']['hdd'].' / '.$content['config']['ipnumber'].' / '.$content['config']['defense'].' / '.$content['config']['operatsystem']?>
					</td>
					<td style="background:#a5a5a5;color:#fff;width:170px;">要求带宽 / 实际带宽</td>
					<td colspan="2">
						<?php echo $content['config']['requirement_bandwidth'].' / '.$content['config']['configbandwidth'];?>
					</td>
				</tr>
				<tr>
					<td style="background:#a5a5a5;color:#fff;width:110px;">IP地址</td>
					<td colspan="5">
						<?php foreach($content['ip'] as $v):?>
							<input type="text" value="<?php echo $v?>" readonly />
						<?php endforeach?>
					</td>
				</tr>
				<tr>
					<td style="background:#a5a5a5;color:#fff;width:110px;">登陆账号</td>
					<td>
						<input type="text" value="<?php echo $content['account_name']?>" readonly />
					</td>
					<td style="background:#a5a5a5;color:#fff;width:110px;">密码</td>
					<td>
						<input type="text" value="<?php echo $content['account_pwd']?>" readonly />
					</td>
					<td style="background:#a5a5a5;color:#fff;width:110px;">端口号</td>
					<td>
						<input type="text" value="<?php echo $content['account_port']?>" readonly />
					</td>
				</tr>
				<tr>
					<td style="background:#a5a5a5;color:#fff;width:110px;">配置备注</td>
					<td colspan="5">
						<?php echo $content['server_remarks']?>
					</td>
				</tr>
				<?php if(isset($content['workorder_notes'])):?>
                    <tr>
                        <td style="background:#a5a5a5;color:#fff;width:110px;">工单备注</td>
                        <td colspan="5">
                            <?php echo $content['workorder_notes'];?>
                        </td>
                    </tr>
                <?php endif;?>
			</table>
			
			<?php if(in_array($admin_id, $allow_admin) && $AfterOrderInfo['ao_type'] == '未完成'):?>
				<?php $hasSonorder = false;
					foreach($sonList as $v) {
						if($v['ao_s_status'] == '未完成') {
							$hasSonorder = true;
							break;
						}
					}
				?>
				
				<?php if($hasSonorder):?>
					<label class="label label-danger">当前还有子订单尚未完成，完成后可在此处增加新的子订单</label>
				<?php else:?>
					<div class="add_sonorder_box">
						
						<?php if(in_array("after-order-son/addtestserver-replaceip", $node) || $this->params['is_administrator_user']):?>
							<a href="<?php echo Url::to(['after-order-son/addtestserver-replaceip', 'ao_id' => $AfterOrderInfo['ao_id']])?>" class="btn btn-success">更换IP</a>
						<?php endif;?>
						
						<?php if($content['servicerprovider'] == 0):?>
							<?php if(in_array("after-order-son/reload-system", $node) || $this->params['is_administrator_user']):?>
							<a href="javascript:;" class="btn btn-danger Reload_system">自有机器 - 重装系统</a>
							<?php endif;?>
						<?php endif;?>
						
					</div>
				<?php endif;?>
			<?php endif?>
		<?php endif;?>
		
		<?php if($AfterOrderInfo['line_type_info']['line_type_name'] == '修改测试机'):?>
			<table class="table table-bordered configTable">
				<tr style="background:#a5a5a5;color:#fff;">
					<td style="background:#fff;width:110px;"></td>
					<td>产品渠道</td>
					<td>服务器分类</td>
					<td>机房名称</td>
					<td>产品配置</td>
					<td>供应商</td>
				</tr>
				<tr>
					<td></td>
					<td>
						<?php if($content['servicerprovider'] == 1):?>
							<label class="label label-warning">供应商</label>
						<?php else:?>
							<label class="label label-success">自有</label>
						<?php endif;?>
					</td>
					<td><?php echo Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$content["server_type_id"]."'")->queryScalar();?></td>
					<td><?php echo Yii::$app->db->createCommand("select name from pdt_room_manage where id = '".$content["room_id"]."'")->queryScalar();?></td>
					<td><?php echo Yii::$app->db->createCommand("select name from pdt_manage where id = '".$content["pdt_id"]."'")->queryScalar();?></td>
					<td><?php echo isset($content['provider_id']) && $content['provider_id'] != '' ? Yii::$app->db->createCommand("select name from provider where id = '".$content["provider_id"]."'")->queryScalar() : '-';?></td>
				</tr>
				<tr>
					<td style="background:#a5a5a5;color:#fff;width:110px;">IP地址</td>
					<td colspan="2">
						<?php foreach($content['ip'] as $v):?>
							<input type="text" value="<?php echo $v?>" readonly />
						<?php endforeach?>
					</td>
					<td style="background:#a5a5a5;color:#fff;width:110px;">IPMI地址</td>
					<td colspan="2">
						<?php echo isset($content['ipmi_ip']) && $content['ipmi_ip'] != '' ? $content['ipmi_ip'] : '-'?>
					</td>
				</tr>
				<tr>
					<td style="background:#a5a5a5;color:#fff;width:110px;">变更项</td>
					<td colspan="5">
						<?php 
							foreach($content['modifyData'] as $v) {
								if($v['modify_type'] == 'hdd') {
									echo '硬盘：';
								} else if($v['modify_type'] == 'ram') {
									echo '内存：';
								} else if($v['modify_type'] == 'requirement_bandwidth') {
									echo '要求带宽：';
								} else if($v['modify_type'] == 'configbandwidth') {
									echo '实际带宽：';
								} else if($v['modify_type'] == 'ipnumber') {
									echo '可用IP数：';
								} else if($v['modify_type'] == 'defense') {
									echo '防御流量：';
								} else if($v['modify_type'] == 'operatsystem') {
									echo '操作系统：';
								} else if($v['modify_type'] == 'ip') {
									echo 'IP地址：';
								}
								
								
								if($v['modify_type'] != 'ip') {
									echo $v['modify_data']['old_config'].' <i class="fa fa-arrow-right"></i> '.$v['modify_data']['new_config'].'<br/>';
								} else {
									
									$oldcount = count($v['modify_data']['old_config']);
									$newcount = count($v['modify_data']['new_config']);
									
									if($oldcount > $newcount) {
										$diffArray = array_diff($v['modify_data']['old_config'], $v['modify_data']['new_config']);
										
										echo '<label class="label label-danger">移除IP</label>&nbsp;&nbsp;';
										
										foreach($diffArray as $ipk => $ipv) {
											echo '<input type="text" value="'.$ipv.'" readonly />';
										}
										
										echo '<br/>';
										
									} else if($oldcount < $newcount) {
										
										$diffArray = array_diff($v['modify_data']['new_config'], $v['modify_data']['old_config']);
										
										echo '<label class="label label-primary">增加IP</label>&nbsp;&nbsp;';
										
										foreach($diffArray as $ipk => $ipv) {
											echo '<input type="text" value="'.$ipv.'" readonly />';
										}
										
										echo '<br/>';
										
									} else {
										$diffArray = array_diff($v['modify_data']['old_config'], $v['modify_data']['new_config']);
										
										echo '<label class="label label-danger">移除IP</label>&nbsp;&nbsp;';
										
										foreach($diffArray as $ipk => $ipv) {
											echo '<input type="text" value="'.$ipv.'" readonly />';
										}
										
										echo '&nbsp;&nbsp;';
										
										$diffArray = array_diff($v['modify_data']['new_config'], $v['modify_data']['old_config']);
										
										echo '<label class="label label-primary">增加IP</label>&nbsp;&nbsp;';
										
										foreach($diffArray as $ipk => $ipv) {
											echo '<input type="text" value="'.$ipv.'" readonly />';
										}
										
										echo '<br/>';
									}
									
								}
							}
							
						?>
					</td>
				</tr>
			</table>
			
			<?php if(in_array($admin_id, $allow_admin) && $AfterOrderInfo['ao_type'] == '未完成'):?>
				<?php $hasSonorder = false;
					foreach($sonList as $v) {
						if($v['ao_s_status'] == '未完成') {
							$hasSonorder = true;
							break;
						}
					}
				?>
				
				<?php if($hasSonorder):?>
					<label class="label label-danger">当前还有子订单尚未完成，完成后可在此处增加新的子订单</label>
				<?php else:?>
					<div class="add_sonorder_box">					
						<?php if(in_array("after-order-son/updatetest-replaceip", $node) || $this->params['is_administrator_user']):?>
							<a class="btn btn-info" href="<?php echo Url::to(['after-order-son/updatetest-replaceip', 'ao_id' => $AfterOrderInfo['ao_id']])?>">更换IP</a>
						<?php endif;?>
						
					</div>
				<?php endif;?>
			<?php endif?>
		<?php endif;?>
		
		<?php if($AfterOrderInfo['line_type_info']['line_type_name'] == '删除测试机'):?>
			
			<table class="table table-bordered configTable">
				<tr style="background:#a5a5a5;color:#fff;">
					<td style="background:#fff;width:110px;"></td>
					<td>产品渠道</td>
					<td>服务器分类</td>
					<td>机房名称</td>
					<td>产品配置</td>
					<td>供应商</td>
				</tr>
				<tr>
					<td></td>
					<td>
						<?php if($content['servicerprovider'] == 1):?>
							<label class="label label-warning">供应商</label>
						<?php else:?>
							<label class="label label-success">自有</label>
						<?php endif;?>
					</td>
					<td><?php echo Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$content["server_type_id"]."'")->queryScalar();?></td>
					<td><?php echo Yii::$app->db->createCommand("select name from pdt_room_manage where id = '".$content["room_id"]."'")->queryScalar();?></td>
					<td><?php echo Yii::$app->db->createCommand("select name from pdt_manage where id = '".$content["pdt_id"]."'")->queryScalar();?></td>
					<td><?php echo isset($content['provider_id']) && $content['provider_id'] != '' ? Yii::$app->db->createCommand("select name from provider where id = '".$content["provider_id"]."'")->queryScalar() : '-';?></td>
				</tr>
				<tr>
					<td style="background:#a5a5a5;color:#fff;width:110px;">IP地址</td>
					<td colspan="2">
						<?php foreach($content['ip'] as $v):?>
							<input type="text" value="<?php echo $v?>" readonly />
						<?php endforeach?>
					</td>
					<td style="background:#a5a5a5;color:#fff;width:110px;">IPMI地址</td>
					<td colspan="2">
						<?php echo isset($content['ipmi_ip']) && $content['ipmi_ip'] != '' ? $content['ipmi_ip'] : '-'?>
					</td>
				</tr>
				
				<tr>
					<td style="background:#a5a5a5;color:#fff;width:110px;">关机下架</td>
					<td colspan="5">
							进行关机等下架操作
					</td>
				</tr>
			</table>
			
			<?php if(in_array($admin_id, $allow_admin) && $AfterOrderInfo['ao_type'] == '未完成'):?>
				<?php $hasSonorder = false;
					foreach($sonList as $v) {
						if($v['ao_s_status'] == '未完成') {
							$hasSonorder = true;
							break;
						}
					}
				?>
				
				<?php if($hasSonorder):?>
					<label class="label label-danger">当前还有子订单尚未完成，完成后可在此处增加新的子订单</label>
				<?php else:?>
					<div class="add_sonorder_box">		
						<?php if($content['servicerprovider'] == 0):?>
							<?php if(in_array("after-order-son/reset-ip", $node) || $this->params['is_administrator_user']):?>							
								<a href="javascript:;" class="btn btn-success reset_ip">机器IP重置</a>
							<?php endif;?>
							
							<?php if(in_array("after-order-son/set-bandwidth", $node) || $this->params['is_administrator_user']):?>								
								<a href="javascript:;" class="btn btn-success set_bandwidth" ao_id="<?php echo $AfterOrderInfo['ao_id'];?>">带宽设定</a>
							<?php endif;?>
							
						<?php endif;?>						
						
					</div>
				<?php endif;?>
			<?php endif?>
			
		<?php endif;?>
		
		<!--变更配置 开始-->
		<?php if(in_array($AfterOrderInfo['line_type_info']['line_type_name'], ['变更配置-补款', '变更配置-退款','变更配置-无金额']) ):?>
			<table class="table table-bordered configTable">
				<tr style="background:#a5a5a5;color:#fff;">
					<td style="background:#fff;width:110px;"></td>
					<td>产品渠道</td>
					<td>服务器分类</td>
					<td>机房名称</td>
					<td>产品配置</td>
					<td>供应商</td>
				</tr>
				<tr>
					<td></td>
					<td>
						<?php if($content['servicerprovider'] == 1):?>
							<label class="label label-warning">供应商</label>
						<?php else:?>
							<label class="label label-success">自有</label>
						<?php endif;?>
					</td>
					<td><?php echo Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$content["server_type_id"]."'")->queryScalar();?></td>
					<td><?php echo Yii::$app->db->createCommand("select name from pdt_room_manage where id = '".$content["room_id"]."'")->queryScalar();?></td>
					<td><?php echo Yii::$app->db->createCommand("select name from pdt_manage where id = '".$content["pdt_id"]."'")->queryScalar();?></td>
					<td><?php echo isset($content['provider_id']) && $content['provider_id'] != '' ? Yii::$app->db->createCommand("select name from provider where id = '".$content["provider_id"]."'")->queryScalar() : '-';?></td>
				</tr>
				<tr>
					<td style="background:#a5a5a5;color:#fff;width:110px;">IP地址</td>
					<td colspan="2">
						<?php foreach($content['ip'] as $v):?>
							<input type="text" value="<?php echo $v?>" readonly />
						<?php endforeach?>
					</td>
					<td style="background:#a5a5a5;color:#fff;width:110px;">IPMI地址</td>
					<td colspan="2">
						<?php echo isset($content['ipmi_ip']) && $content['ipmi_ip'] != '' ? $content['ipmi_ip'] : '-'?>
					</td>
				</tr>
				<tr>
					<td style="background:#a5a5a5;color:#fff;width:110px;">变更项</td>
					<td colspan="5">
						<?php 
							foreach($content['modifyData'] as $v) {
								if($v['modify_type'] == 'hdd') {
									echo '硬盘：';
								} else if($v['modify_type'] == 'ram') {
									echo '内存：';
								} else if($v['modify_type'] == 'requirement_bandwidth') {
									echo '要求带宽：';
								} else if($v['modify_type'] == 'configbandwidth') {
									echo '实际带宽：';
								} else if($v['modify_type'] == 'ipnumber') {
									echo '可用IP数：';
								} else if($v['modify_type'] == 'defense') {
									echo '防御流量：';
								} else if($v['modify_type'] == 'operatsystem') {
									echo '操作系统：';
								} else if($v['modify_type'] == 'ip') {
									echo 'IP地址：';
								}
								
								if($v['modify_type'] != 'ip') {
									echo $v['modify_data']['old_config'].' <i class="fa fa-arrow-right"></i> '.$v['modify_data']['new_config'].'<br/>';
								} else {
									
									$oldcount = count($v['modify_data']['old_config']);
									$newcount = count($v['modify_data']['new_config']);
									
									if($oldcount > $newcount) {
										$diffArray = array_diff($v['modify_data']['old_config'], $v['modify_data']['new_config']);
										
										echo '<label class="label label-danger">移除IP</label>&nbsp;&nbsp;';
										
										foreach($diffArray as $ipk => $ipv) {
											echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
										}
										
										echo '<br/>';
										
									} else if($oldcount < $newcount) {
										
										$diffArray = array_diff($v['modify_data']['new_config'], $v['modify_data']['old_config']);
										
										echo '<label class="label label-primary">增加IP</label>&nbsp;&nbsp;';
										
										foreach($diffArray as $ipk => $ipv) {
											echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
										}
										
										echo '<br/>';
										
									} else {
										$diffArray = array_diff($v['modify_data']['old_config'], $v['modify_data']['new_config']);
										
										echo '<label class="label label-danger">移除IP</label>&nbsp;&nbsp;';
										
										foreach($diffArray as $ipk => $ipv) {
											echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
										}
										
										echo '&nbsp;&nbsp;';
										
										$diffArray = array_diff($v['modify_data']['new_config'], $v['modify_data']['old_config']);
										
										echo '<label class="label label-primary">增加IP</label>&nbsp;&nbsp;';
										
										foreach($diffArray as $ipk => $ipv) {
											echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
										}
										
										echo '<br/>';
									}
								}
							}
						?>
					</td>
				</tr>
			</table>
			
			<?php if(in_array($admin_id, $allow_admin) && $AfterOrderInfo['ao_type'] == '未完成'):?>
				<?php $hasSonorder = false;
					foreach($sonList as $v) {
						if($v['ao_s_status'] == '未完成') {
							$hasSonorder = true;
							break;
						}
					}
				?>
				
				<?php if($hasSonorder):?>
					<label class="label label-danger">当前还有子订单尚未完成，完成后可在此处增加新的子订单</label>
				<?php else:?>
					<div class="add_sonorder_box">						
						<?php if(in_array("after-order-son/changeconfig-replaceip", $node) || $this->params['is_administrator_user']):?>
							<a class="btn btn-info" href="<?php echo Url::to(['after-order-son/changeconfig-replaceip', 'ao_id' => $AfterOrderInfo['ao_id']])?>">更换IP</a>
						<?php endif;?>
						
						<?php if($content['servicerprovider'] == 0):?>
						
							<?php if(in_array("after-order-son/set-bandwidth", $node) || $this->params['is_administrator_user']):?>								
								<a href="javascript:;" class="btn btn-success set_bandwidth" ao_id="<?php echo $AfterOrderInfo['ao_id'];?>">带宽设定</a>
							<?php endif;?>						
						<?php endif;?>						
						
					</div>
				<?php endif;?>
			<?php endif?>
		<?php endif;?>
		<!-- 变更配置end-->
		
		<!-- 用户更换机器 开始-->
		<?php if(in_array($AfterOrderInfo['line_type_info']['line_type_name'], ['更换机器-补款', '更换机器-退款','更换机器-无金额']) ):?>
			<table class="table table-bordered configTable">
				<tr>
					<td style="background:#a5a5a5;color:#fff;" colspan="2">更换前机器</td>
				</tr>
				<tr>
					<td>提供方</td>
					<td>
						<?php if($content['frontData']['servicerprovider'] == 1):?>
							<label class="label label-warning">供应商</label>
						<?php elseif($content['frontData']['servicerprovider'] == 0 && $content['frontData']['servicerprovider'] !== ''):?>
							<label class="label label-success">自有</label>
						<?php else:?>
							-
						<?php endif;?>
					</td>
				</tr>
				<tr>
					<td>区域配置</td>
					<td>
						<?php 
							$server_type = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$content['frontData']["server_type_id"]."'")->queryScalar();
							$room_name = Yii::$app->db->createCommand("select name from pdt_room_manage where id = '".$content['frontData']["room_id"]."'")->queryScalar();
							$pdt_name = Yii::$app->db->createCommand("select name from pdt_manage where id = '".$content['frontData']["pdt_id"]."'")->queryScalar();
							
							echo $server_type.' / '.$pdt_name.' / '.$room_name;
						?>
					</td>
				</tr>
				<tr>
					<td>配置详情</td>
					<td>
						<?php 
							echo $content['frontData']['config']['cpu'].' / '.$content['frontData']['config']['ram'].' / '.$content['frontData']['config']['hdd'].' / '.$content['frontData']['config']['ipnumber'].' / '.$content['frontData']['config']['defense'].' / '.$content['frontData']['config']['operatsystem'];
						?>
					</td>
				</tr>
				<tr>
					<td>带宽配置</td>
					<td>
						<?php 
							echo $content['frontData']['config']['requirement_bandwidth'].'（要求带宽）'.$content['frontData']['config']['configbandwidth'].'（实际带宽）';
						?>
					</td>
				</tr>
				<tr>
					<td>IP地址</td>
					<td>
						<?php 
							foreach($content['frontData']['ip'] as $ipk => $ipv) {
								echo '<input type="text" value="'.$ipv.'" readonly />';
							}
						?>
					</td>
				</tr>
				<tr>
					<td>IPMI地址</td>
					<td><?php echo isset($content['frontData']['ipmi_ip']) && $content['frontData']['ipmi_ip'] != '' ? $content['frontData']['ipmi_ip'] : '-';?></td>
				</tr>
			</table>
			<table class="table table-bordered configTable">
				<tr>
					<td style="background:#a5a5a5;color:#fff;" colspan="2">更换后机器</td>
				</tr>
				<tr>
					<td>提供方</td>
					<td>
						<?php if($content['afterData']['servicerprovider'] == 1):?>
							<label class="label label-warning">供应商</label>
						<?php elseif($content['afterData']['servicerprovider'] == 0 && $content['afterData']['servicerprovider'] !== ''):?>
							<label class="label label-success">自有</label>
						<?php else:?>
							-
						<?php endif;?>
					</td>
				</tr>
				<tr>
					<td>区域配置</td>
					<td>
						<?php 
							$server_type = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$content['afterData']["server_type_id"]."'")->queryScalar();
							$room_name = Yii::$app->db->createCommand("select name from pdt_room_manage where id = '".$content['afterData']["room_id"]."'")->queryScalar();
							$pdt_name = Yii::$app->db->createCommand("select name from pdt_manage where id = '".$content['afterData']["pdt_id"]."'")->queryScalar();
							
							echo $server_type.' / '.$pdt_name.' / '.$room_name;
						?>
					</td>
				</tr>
				<tr>
					<td>配置详情</td>
					<td>
						<?php 
							echo $content['afterData']['config']['cpu'].' / '.$content['afterData']['config']['ram'].' / '.$content['afterData']['config']['hdd'].' / '.$content['afterData']['config']['ipnumber'].' / '.$content['afterData']['config']['defense'].' / '.$content['afterData']['config']['operatsystem'];
						?>
					</td>
				</tr>
				<tr>
					<td>带宽配置</td>
					<td>
						<?php 
							echo $content['afterData']['config']['requirement_bandwidth'].'（要求带宽）'.$content['afterData']['config']['configbandwidth'].'（实际带宽）';
						?>
					</td>
				</tr>
				<tr>
					<td>IP地址</td>
					<td>
						<?php 
							foreach($content['afterData']['ip'] as $ipk => $ipv) {
								echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
							}
						?>
					</td>
				</tr>
				<?php if($content['use_original_ip'] == 1):?>
				<tr>
					<td>原机器IP</td>
					<td>
						<?php 
						if($content['afterData']['should_ip']){
							foreach($content['afterData']['should_ip'] as $ipk => $ipv) {
								echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
							}
						}
						?>
					</td>
				</tr>
				
				<?php endif;?>
				
				
				
				<tr>
					<td>IPMI地址</td>
					<td><?php echo isset($content['afterData']['ipmi_ip']) && $content['afterData']['ipmi_ip'] != '' ? $content['afterData']['ipmi_ip'] : '-';?></td>
				</tr>
			</table>
			<table class="table table-bordered configTable">
				<tr>
					<td style="width:150px;">★是否使用原机器IP</td>
					<td>
						<?php if($content['use_original_ip'] == 1):?>
							<font style="color:#f00;font-weight:bold;">使用原机器IP（互换IP）</font>
							<?php if( isset($content['is_exchangedip']) && $content['is_exchangedip'] == 1):?>
								<font style="color:#f00;font-weight:bold;">机器已进行IP互换</font>
							<?php endif;?>
						<?php else:?>
							不使用原机器IP（不更换IP）
						<?php endif?>
					</td>
				</tr>
				<tr>
					<td style="width:150px;">备注</td>
					<td>
						<?php echo $content['remarks'];?>							
					</td>
				</tr>
			</table>
			
			<?php if(in_array($admin_id, $allow_admin) && $AfterOrderInfo['ao_type'] == '未完成'):?>
				<?php $hasSonorder = false;
					foreach($sonList as $v) {
						if($v['ao_s_status'] == '未完成') {
							$hasSonorder = true;
							break;
						}
					}
				?>
				
				<?php if($hasSonorder):?>
					<label class="label label-danger">当前还有子订单尚未完成，完成后可在此处增加新的子订单</label>
				<?php else:?>
					<div class="add_sonorder_box">
						<?php if($content['use_original_ip'] == 1):?>
							<?php if(in_array("after-order/exchangedip", $node) || $this->params['is_administrator_user']):?>
								<?php if( !isset($content['is_exchangedip']) || $content['is_exchangedip'] != 1):?>
									<a href="javascript:;", ao_id="<?php echo $AfterOrderInfo['ao_id'];?>" class="btn btn-info exchangedip">进行机器IP对换</a>					
								<?php endif?>
							<?php endif?>
							
						<?php endif?>
								
						<?php if(in_array("after-order-son/business-replace-machine", $node) || $this->params['is_administrator_user']):?>						
							<a href="<?php echo Url::to(['after-order-son/business-replace-machine', 'ao_id' => $AfterOrderInfo['ao_id']])?>" class="btn btn-info">重新分配机器</a>
						<?php endif?>
						
						<?php if($content['use_original_ip'] != 1):?>						
							<?php if(in_array("after-order-son/replacemachine-replaceip", $node) || $this->params['is_administrator_user']):?>						
								<a href="<?php echo Url::to(['after-order-son/replacemachine-replaceip', 'ao_id' => $AfterOrderInfo['ao_id']])?>" class="btn btn-info">更换机器IP</a>
							<?php endif?>
							
						<?php endif?>
					
					</div>
				<?php endif;?>
			<?php endif?>
		<?php endif;?>
		<!-- 用户更换机器end-->
		<?php if($AfterOrderInfo['line_type_info']['line_type_name'] == '业务退款'):?>
			<table class="table table-bordered">
				<tr>
					<td>提供方</td>
					<td>
						<?php if($content['servicerprovider'] == 1):?>
							<label class="label label-warning">供应商</label>
						<?php elseif($content['servicerprovider'] == 0 && $content['servicerprovider'] !== ''):?>
							<label class="label label-success">自有</label>
						<?php else:?>
							-
						<?php endif;?>
					</td>
				</tr>
				<tr>
					<td>区域配置</td>
					<td>
						<?php 
							$server_type = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$content["server_type_id"]."'")->queryScalar();
							$room_name = Yii::$app->db->createCommand("select name from pdt_room_manage where id = '".$content["room_id"]."'")->queryScalar();
							$pdt_name = Yii::$app->db->createCommand("select name from pdt_manage where id = '".$content["pdt_id"]."'")->queryScalar();
							
							echo $server_type.' / '.$pdt_name.' / '.$room_name;
						?>
					</td>
				</tr>
				<tr>
					<td>IPMI地址</td>
					<td><input type="text" value="<?php echo $content['ipmi_ip'];?>" readonly /></td>
				</tr>
				<tr>
					<td>IP地址</td>
					<td>
						<?php 
							foreach($content['ip'] as $ipk => $ipv) {
								echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
							}
						?>
					</td>
				</tr>
				
				<tr>
					<td>退款理由</td>
					<td><?php echo $content['refund_reason'];?></td>
				</tr>
			</table>
			<?php if(in_array($admin_id, $allow_admin) && $AfterOrderInfo['ao_type'] == '未完成'):?>
				<?php $hasSonorder = false;
					foreach($sonList as $v) {
						if($v['ao_s_status'] == '未完成') {
							$hasSonorder = true;
							break;
						}
					}
				?>
				
				<?php if($hasSonorder):?>
					<label class="label label-danger">当前还有子订单尚未完成，完成后可在此处增加新的子订单</label>
				<?php else:?>
					<div class="add_sonorder_box">
						<?php if($content['servicerprovider'] == 0):?>
							<?php if(in_array("after-order-son/reset-ip", $node) || $this->params['is_administrator_user']):?>							
								<a href="javascript:;" class="btn btn-success reset_ip">机器IP重置</a>							
							<?php endif;?>
						
							<?php if(in_array("after-order-son/set-bandwidth", $node) || $this->params['is_administrator_user']):?>								
								<a href="javascript:;" class="btn btn-success set_bandwidth" ao_id="<?php echo $AfterOrderInfo['ao_id'];?>">带宽设定</a>
							<?php endif;?>
							
						<?php endif;?>
						
					</div>
				<?php endif;?>
			<?php endif?>
			
			
		<?php endif;?>
		
		<!--业务关机下架 start-->
		<?php if($AfterOrderInfo['line_type_info']['line_type_name'] == '业务关机下架'):?>
			<table class="table table-bordered">
				<tr>
					<td>提供方</td>
					<td>
						<?php if($content['servicerprovider'] == 1):?>
							<label class="label label-warning">供应商</label>
						<?php elseif($content['servicerprovider'] == 0 && $content['servicerprovider'] !== ''):?>
							<label class="label label-success">自有</label>
						<?php else:?>
							-
						<?php endif;?>
					</td>
				</tr>
				<tr>
					<td>区域配置</td>
					<td>
						<?php 
							$server_type = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$content["server_type_id"]."'")->queryScalar();
							$room_name = Yii::$app->db->createCommand("select name from pdt_room_manage where id = '".$content["room_id"]."'")->queryScalar();
							$pdt_name = Yii::$app->db->createCommand("select name from pdt_manage where id = '".$content["pdt_id"]."'")->queryScalar();
							
							echo $server_type.' / '.$pdt_name.' / '.$room_name;
						?>
					</td>
				</tr>
				<tr>
					<td>IPMI地址</td>
					<td><input type="text" value="<?php echo $content['ipmi_ip'];?>" readonly /></td>
				</tr>
				<tr>
					<td>IP地址</td>
					<td>
						<?php 
							foreach($content['ip'] as $ipk => $ipv) {
								echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
							}
						?>
					</td>
				</tr>
				<tr>
					<td>用户机器</td>
					<td><font style="color:red">已到期机器</font></td>
				</tr>
				
			</table>
			
			<?php if(in_array($admin_id, $allow_admin) && $AfterOrderInfo['ao_type'] == '未完成'):?>
				<?php $hasSonorder = false;
					foreach($sonList as $v) {
						if($v['ao_s_status'] == '未完成') {
							$hasSonorder = true;
							break;
						}
					}
				?>
				
				<?php if($hasSonorder):?>
					<label class="label label-danger">当前还有子订单尚未完成，完成后可在此处增加新的子订单</label>
				<?php else:?>
					<div class="add_sonorder_box">
						
						<?php if($content['servicerprovider'] == 0):?>
						
							<?php if(in_array("after-order-son/reset-ip", $node) || $this->params['is_administrator_user']):?>								
								<a href="javascript:;" class="btn btn-success reset_ip">机器IP重置</a>								
							<?php endif;?>
							
							<?php if(in_array("after-order-son/set-bandwidth", $node) || $this->params['is_administrator_user']):?>								
								<a href="javascript:;" class="btn btn-success set_bandwidth" ao_id="<?php echo $AfterOrderInfo['ao_id'];?>">带宽设定</a>
							<?php endif;?>
							
						<?php endif;?>
						
					</div>
				<?php endif;?>
			<?php endif?>			
			
		<?php endif;?>
		<!--业务关机下架 end-->
		
		<!-- 更换IP -->
		<?php if($AfterOrderInfo['line_type_info']['line_type_name'] == '更换IP'):?>
			<table class="table table-bordered configTable">
				<tr style="background:#a5a5a5;color:#fff;">
					<td>服务器分类</td>
					<td>产品配置</td>
					<td>IPMI地址</td>
				</tr>
				<tr>
					<td><?php echo Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$content['server_type_id']."'")->queryScalar();?></td>
					<td><?php echo Yii::$app->db->createCommand("select name from pdt_manage where id = '".$content["pdt_id"]."'")->queryScalar();?></td>
					<td><?php echo $content['ipmi_ip'];?></td>
				</tr>
				<tr>
					<td>更换前全部IP</td>
					<td colspan="2">
						<?php $front_ip = $content['frontData']['ip'];
							foreach($front_ip as $key=>$val){
								echo '<input type="text" value="'.$val.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
							}
						?>
					</td>
				</tr>
				<tr>
					<td>更换后全部IP</td>
					<td colspan="2">
						<?php $atfer_ip = $content['afterData']['ip'];
							foreach($atfer_ip as $key=>$val){
								echo '<input type="text" value="'.$val.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
							}
						?>
					</td>
				</tr>
				<tr>
					<td>IP差异</td>
					<td colspan="2">
						<?php 
						
							$front_ip = $content['frontData']['ip'];
							$after_ip = $content['afterData']['ip'];
							
							$oldcount = count($front_ip);
							$newcount = count($after_ip);
							
							if($oldcount > $newcount) {
								$diffArray = array_diff($front_ip, $after_ip);
								
								echo '<label class="label label-danger">移除IP</label>&nbsp;&nbsp;';
								
								foreach($diffArray as $ipk => $ipv) {
									echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
								}
								
								echo '<br/>';
								
							} else if($oldcount < $newcount) {
								
								$diffArray = array_diff($after_ip, $front_ip);
								
								echo '<label class="label label-primary">增加IP</label>&nbsp;&nbsp;';
								
								foreach($diffArray as $ipk => $ipv) {
									echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
								}
								
								echo '<br/>';
								
							} else {
								$diffArray = array_diff($front_ip, $after_ip);
								
								echo '<label class="label label-danger">移除IP</label>&nbsp;&nbsp;';
								
								foreach($diffArray as $ipk => $ipv) {
									echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
								}
								
								echo '&nbsp;&nbsp;';
								
								$diffArray = array_diff($after_ip, $front_ip);
								
								echo '<label class="label label-primary">增加IP</label>&nbsp;&nbsp;';
								
								foreach($diffArray as $ipk => $ipv) {
									echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
								}
								
								echo '<br/>';
							}
						?>
					
					</td>
				</tr>
			</table>
		<?php elseif($AfterOrderInfo['line_type_info']['line_type_name'] == '更换闲置机器IP'):?>
			<table class="table table-bordered configTable">
				<tr style="background:#a5a5a5;color:#fff;">
					<td>服务器分类</td>
					<td>产品配置</td>
					<td>IPMI地址</td>
				</tr>
				<tr>
					<td><?php echo Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$content['server_type_id']."'")->queryScalar();?></td>
					<td><?php echo Yii::$app->db->createCommand("select name from pdt_manage where id = '".$content["pdt_id"]."'")->queryScalar();?></td>
					<td><?php echo $content['ipmi_ip'];?></td>
				</tr>
				<tr>
					<td>更换前全部IP</td>
					<td colspan="2">
						<?php $front_ip = $content['replace_front']['ip'];
							foreach($front_ip as $key=>$val){
								echo '<input type="text" value="'.$val.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
							}
						?>
					</td>
				</tr>
				<tr>
					<td>更换后全部IP</td>
					<td colspan="2">
						<?php $atfer_ip = $content['replace_after']['ip'];
							foreach($atfer_ip as $key=>$val){
								echo '<input type="text" value="'.$val.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
							}
						?>
					</td>
				</tr>
				<tr>
					<td>IP差异</td>
					<td colspan="2">
						<?php 
						
							$front_ip = $content['replace_front']['ip'];
							$after_ip = $content['replace_after']['ip'];
							
							$oldcount = count($front_ip);
							$newcount = count($after_ip);
							
							if($oldcount > $newcount) {
								$diffArray = array_diff($front_ip, $after_ip);
								
								echo '<label class="label label-danger">移除IP</label>&nbsp;&nbsp;';
								
								foreach($diffArray as $ipk => $ipv) {
									echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
								}
								
								echo '<br/>';
								
							} else if($oldcount < $newcount) {
								
								$diffArray = array_diff($after_ip, $front_ip);
								
								echo '<label class="label label-primary">增加IP</label>&nbsp;&nbsp;';
								
								foreach($diffArray as $ipk => $ipv) {
									echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
								}
								
								echo '<br/>';
								
							} else {
								$diffArray = array_diff($front_ip, $after_ip);
								
								echo '<label class="label label-danger">移除IP</label>&nbsp;&nbsp;';
								
								foreach($diffArray as $ipk => $ipv) {
									echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
								}
								
								echo '&nbsp;&nbsp;';
								
								$diffArray = array_diff($after_ip, $front_ip);
								
								echo '<label class="label label-primary">增加IP</label>&nbsp;&nbsp;';
								
								foreach($diffArray as $ipk => $ipv) {
									echo '<input type="text" value="'.$ipv.'" readonly />&nbsp;&nbsp;&nbsp;&nbsp;';
								}
								
								echo '<br/>';
							}
						?>
					
					</td>
				</tr>
			</table>
		<?php endif;?>
	</div>
	<?php if($sonList):?>
	<div class="info-title">工单子单审核与处理</div>
	<div class="info-box">
		<table class="table table-bordered">
			<tr>
				<td>子单类型</td>
				<td>可审核人</td>
				<td>审核人</td>
				<td>审核备注</td>
				<td>审核时间</td>
				<td>审核状态</td>
				<td>发起时间</td>
				<td>完成时间</td>
				<td>处理时间</td>
				<td>子单状态</td>
				<td>操作</td>
			</tr>
			<?php foreach($sonList as $key => $val):?>
				<tr>
					<td><?php echo ArrayHelper::getValue($val,'ao_s_type')?></td>
					<td><?php echo implode(', ', $val['trailman'])?></td>
					<td><?php echo $val['ao_s_trial_admin_id'] ? $val['ao_s_trial_admin_name'] : '-'?></td>
					<td><?php echo $val['ao_s_trial_remark'] ? $val['ao_s_trial_remark'] : '-'?></td>
					<td><?php echo $val['ao_s_trial_time'] ? date("Y-m-d H:i:s", $val['ao_s_trial_time']) : '-'?></td>
					<td>
						<?php if($val['ao_s_trial_status'] == '等待审核'):?>
							<label class="label label-warning"><?php echo $val['ao_s_trial_status']?></label>
						<?php elseif($val['ao_s_trial_status'] == '无需审核'):?>
							<label class="label label-default"><?php echo $val['ao_s_trial_status']?></label>
						<?php elseif($val['ao_s_trial_status'] == '通过'):?>
							<label class="label label-success"><?php echo $val['ao_s_trial_status']?></label>
						<?php elseif($val['ao_s_trial_status'] == '拒绝'):?>
							<label class="label label-danger"><?php echo $val['ao_s_trial_status']?></label>
						<?php endif;?>
					</td>
					<td><?php echo date("Y-m-d H:i:s", $val['ao_s_request_time'])?></td>
					<td><?php echo $val['ao_s_finish_time'] ? date("Y-m-d H:i:s", $val['ao_s_finish_time']) : '-'?></td>
					<td><?php
					
						if($val['ao_s_finish_time']) {
							$minusTime = $val['ao_s_finish_time'] - $val['ao_s_request_time'];
							echo '总计：';
						} else {
							$minusTime = time() - $val['ao_s_request_time'];
							echo '已用时：';
						}
						
						if($minusTime < 60) {
							echo $minusTime.' 秒';
						} else if($minusTime > 60 && $minusTime < 3600) {
							echo (int)($minusTime / 60).' 分 '.($minusTime%60).' 秒';
						} else if($minusTime > 3600 && $minusTime < 86400) {
							echo (int)($minusTime / 3600).' 小时 '.(int)(($minusTime%3600) / 60).' 分';
						} else if($minusTime > 86400) {
							echo (int)($minusTime / 86400).' 天 '.(int)(($minusTime%86400) / 3600).' 小时';
						}
						
					?></td>
					<td>
						<?php if($val['ao_s_status'] == '未完成'):?>
							<label class="label label-warning"><?php echo $val['ao_s_status']?></label>
						<?php elseif($val['ao_s_status'] == '已完成'):?>
							<label class="label label-success"><?php echo $val['ao_s_status']?></label>
						<?php elseif($val['ao_s_status'] == '已取消'):?>
							<label class="label label-danger"><?php echo $val['ao_s_status']?></label>
						<?php endif;?>
					<td>
						<?php if($AfterOrderInfo['ao_type'] == '未完成'):?>
							<?php if($val['ao_s_status'] == '未完成'):?>
								<?php if($val['ao_s_trial_status'] == '无需审核'):?>	
									
									<?php if(in_array("after-order-son/son-order-operation", $node) || $this->params['is_administrator_user']):?>	
										<a href="javascript:;" class="complet_sub" sonorderid="<?php echo $val['ao_s_sonlist_id'];?>">完成子单</a>																
										&nbsp;&nbsp;<a href="javascript:;" class="cancel_sub" style="color:red" sonorderid="<?php echo $val['ao_s_sonlist_id'];?>">取消子单</a>
									<?php endif;?>
									
								<?php elseif($val['ao_s_trial_status'] == '等待审核'):?>
									
									<?php $cantrail = explode(',', $val['trail']['sonlist_trial_list']);?>
									<?php if(in_array($admin_id, $cantrail)): ?>
										<?php if($val['ao_s_trial_status'] == '等待审核'): ?>
										
											<?php if(in_array("after-order-son/trail-son-order", $node) || $this->params['is_administrator_user']):?>	
												<a href="javascript:;" class="trailSonorder" sonorderid="<?php echo $val['ao_s_sonlist_id']?>">审核子单</a>
											<?php endif;?>
											
										<?php endif;?>
									<?php endif;?>
									
									
									<?php if(in_array("after-order-son/son-order-operation", $node) || $this->params['is_administrator_user']):?>	
										<a href="javascript:;" class="cancel_sub" style="color:red" sonorderid="<?php echo $val['ao_s_sonlist_id'];?>">取消子单</a>
									<?php endif;?>
									
								<?php elseif($val['ao_s_trial_status'] == '通过'):?>									
									
									<?php if(in_array("after-order-son/son-order-operation", $node) || $this->params['is_administrator_user']):?>
										<a href="javascript:;" class="complet_sub" sonorderid="<?php echo $val['ao_s_sonlist_id'];?>">完成子单</a>&nbsp;&nbsp;
									<?php endif?>
									
									<?php if(in_array($admin_id, $allow_admin)):?>

										<?php if(in_array("after-order-son/son-order-operation", $node) || $this->params['is_administrator_user']):?>
											<a href="javascript:;" class="cancel_sub" style="color:red" sonorderid="<?php echo $val['ao_s_sonlist_id'];?>">取消子单</a>
										<?php endif;?>
										
									<?php endif?>
								<?php endif;?>
							<?php endif;?>
						<?php endif;?>
					</td>
				</tr>
				<tr>
					<td></td>
					<td colspan="10">
						<?php $sonorder_content = json_decode($val['ao_s_content'], true);?>
						<!--重新分配机器 start-->
						<?php if(ArrayHelper::getValue($val,'ao_s_type') == '重新分配机器'):?>
							<table class="table table-bordered">
								<tr>
									<td></td>
									<td>服务器所属</td>
									<td>IPMI地址</td>
									<td>更换前主IP地址</td>
									<td>更换后主IP地址
										<?php if($content['use_original_ip'] == 1):?><font style="color:#f00;font-weight:bold;"><br/>使用原机器IP（互换IP）</font><?php endif;?></td>
									<td>配置区域</td>
									<td>机器配置</td>
									<?php if(in_array($AfterOrderInfo['line_type_info']['line_type_name'], ['更换机器-补款', '更换机器-退款','更换机器-无金额']) ):?>
									<td >操作</td>
									<?php endif;?>
								</tr>
								<tr>
									<td>原配置机器</td>
									<td><?php echo $sonorder_content['replace_front']['servicerprovider'] == 1 ? '供应商' : '自有机器';?></td>
									<td><?php echo $sonorder_content['replace_front']['ipmi_ip'];?></td>									
									<td><?php echo $sonorder_content['replace_front']['ip'][0];?></td>
									<?php if($content['use_original_ip'] == 1):?>
									<td><?php echo $sonorder_content['replace_after']['ip'][0];?></td>
									<?php else:?>
									<td><?php echo $sonorder_content['replace_front']['ip'][0];?></td>
									<?php endif;?>
									<td>
										<?php 
											$servertype = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$sonorder_content['replace_front']["server_type_id"]."'")->queryScalar();
											$pdttype = Yii::$app->db->createCommand("select name from pdt_manage where id = '".$sonorder_content['replace_front']["pdt_id"]."'")->queryScalar();
											echo $servertype.' / '.$pdttype;
										?>
									</td>
									<td>
										<?php $config = $sonorder_content['replace_front']['config'];?>
										<?php echo $config['cpu'].' / '.$config['ram'].' / '.$config['hdd'].' / '.$config['ipnumber'].' / '.$config['defense'].' / '.$config['operatsystem']?>
									</td>
									<?php if(in_array($AfterOrderInfo['line_type_info']['line_type_name'], ['更换机器-补款', '更换机器-退款','更换机器-无金额']) ):?>
									<td rowspan="3">

										<?php if($val['ao_s_status'] == '未完成' && in_array($val['ao_s_trial_status'], ['无需审核', '通过']) && $content['use_original_ip'] == 1):?>									
											<?php if( !isset($sonorder_content['is_exchangedip']) || $sonorder_content['is_exchangedip'] != 1):?>	
												
												<?php if(in_array("after-order-son/son-exchangedip", $node) || $this->params['is_administrator_user']):?>	
													<a href="javascript:;", ao_sonid="<?php echo $val['ao_s_sonlist_id'];?>" class="son_exchangedip">进行机器IP对换</a>
												<?php endif?>
												
											<?php endif?>											
										<?php endif?>	
									</td>
									<?php endif;?>
									
								</tr>
								
								<tr>
									<td>重新分配机器</td>
									<td><?php echo $sonorder_content['replace_after']['servicerprovider'] == 1 ? '供应商' : '自有机器';?></td>
									<td><?php echo $sonorder_content['replace_after']['ipmi_ip'];?></td>
									<td><?php echo $sonorder_content['replace_after']['ip'][0];?></td>
									<?php if($content['use_original_ip'] == 1):?>
									<td><?php echo $sonorder_content['replace_front']['ip'][0];?></td>
									<?php else:?>
									<td><?php echo $sonorder_content['replace_after']['ip'][0];?></td>
									<?php endif;?>
									<td>
										<?php 
											$servertype = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$sonorder_content['replace_after']["server_type_id"]."'")->queryScalar();
											$pdttype = Yii::$app->db->createCommand("select name from pdt_manage where id = '".$sonorder_content['replace_after']["pdt_id"]."'")->queryScalar();
											echo $servertype.' / '.$pdttype;
										?>
									</td>
									<td>
										<?php $config = $sonorder_content['replace_after']['config'];?>
										<?php echo $config['cpu'].' / '.$config['ram'].' / '.$config['hdd'].' / '.$config['ipnumber'].' / '.$config['defense'].' / '.$config['operatsystem']?>
									</td>
								</tr>
								<tr>
									<td>备注</td>
									<td colspan="7"><?php echo $sonorder_content['replace_after']['config_remark']?></td>
									
								</tr>
								
							</table>
						<?php endif;?>
						<!--重新分配机器 END-->
						
						<!--更换IP start-->
						<?php if(ArrayHelper::getValue($val,'ao_s_type') == '更换IP'):?>
							<table class="table table-bordered">
								<tr>
									<td></td>
									<td>IPMI地址</td>
									<td>更换前IP</td>
									<td>更换后IP</td>
									
								</tr>
								<tr>
									<td>更换IP情况</td>
									<td><?php echo $sonorder_content['ipmi_ip'];?></td>
									<td>
										<?php $front_ip = $sonorder_content['replace_front']['ip'];
											foreach($front_ip as $key=>$val){
												echo $val.'<br/>';
											}
										?>
									</td>
									<td>
										<?php $after_ip = $sonorder_content['replace_after']['ip'];
											foreach($after_ip as $key=>$val){
												echo $val.'<br/>';
											}
										?>
									</td>									
								</tr>
							</table>
						<?php endif;?>						
						<!--更换IP end-->
						
						<!--重置IP start-->
						<?php if(ArrayHelper::getValue($val,'ao_s_type') == '重置IP'):?>
							<table class="table table-bordered">
								<tr>
									<td></td>
									<td>IPMI地址</td>
									<td>重置前IP</td>
									<td>重置后IP</td>									
								</tr>
								<tr>
									<td>重置IP情况</td>
									<td><?php echo $sonorder_content['ipmi_ip'];?></td>
									<td>
										<?php $front_ip = $sonorder_content['replace_front']['ip'];
											foreach($front_ip as $key=>$val){
												echo $val.'<br/>';
											}
										?>
									</td>
									<td>
										<?php $after_ip = $sonorder_content['replace_after']['ip'];
											foreach($after_ip as $key=>$val){
												echo $val.'<br/>';
											}
										?>
									</td>									
								</tr>
							</table>
						<?php endif;?>						
						<!--重置IP end-->
						
						
						<!-- 重装系统 -->
						<?php if(ArrayHelper::getValue($val,'ao_s_type') == '重装系统'):?>
							<table class="table table-bordered">
								<tr>
									<td>IPMI地址</td>
									<td>系统操作</td>
									<td>系统分类</td>
									<td>系统名称</td>		
									<td>操作</td>		
								</tr>
								<tr>
									<td><?php echo $sonorder_content['ipmi_ip'];?></td>
									<td><?php echo $sonorder_content['class_category'];?></td>
									<td><?php echo $sonorder_content['class_name'];?></td>
									<td><?php echo $sonorder_content['cloud_system_name'];?></td>
									<td>
										<?php if($val['ao_s_status'] == '未完成' &&in_array($val['ao_s_trial_status'], ['通过', '无需审核']) ):?>
											<?php if( isset($sonorder_content['submit_reload']) && $sonorder_content['submit_reload'] == 1 ):?>
											
												<?php if(in_array("after-order-son/reloadsystem-operation", $node) || $this->params['is_administrator_user']):?>	
													<a href="javascript:;" class="cancel_reload" style="color:red" sonorderid="<?php echo $val['ao_s_sonlist_id'];?>">取消重装</a>
												<?php endif;?>
												
											<?php else:?>
											
												<?php if(in_array("after-order-son/reloadsystem-operation", $node) || $this->params['is_administrator_user']):?>	
													<a href="javascript:;" class="immediate_reload" sonorderid="<?php echo $val['ao_s_sonlist_id'];?>">立即重装</a>
												<?php endif;?>
												
											<?php endif;?>
										<?php endif;?>
									</td>
								</tr>
							</table>
						<?php endif;?>
						
						<!-- 带宽设定 -->
						<?php if(ArrayHelper::getValue($val,'ao_s_type') == '带宽设定'):?>
							<table class="table table-bordered">
								<tr>
									<td>IPMI地址</td>
									<td>限速类型</td>
									<td>设定前限速</td>
									<td>设定后限速</td>		
									<td>操作</td>		
								</tr>
								<tr>
									<td><?php echo $sonorder_content['ipmi_ip'];?></td>
									<td><?php echo $sonorder_content['speed_type'] == 'inbound' ? '上行限速' : '下行限速';?></td>
									<td><?php echo $sonorder_content['old_speed_limit'];?></td>
									<td><?php echo $sonorder_content['new_speed_limit'];?></td>
									<td>
										<?php if($val['ao_s_status'] == '未完成' &&in_array($val['ao_s_trial_status'], ['通过', '无需审核']) ):?>
											<?php if( isset($sonorder_content['submit_update']) && $sonorder_content['submit_update'] == 1 ):?>
												
											<?php else:?>
											
												<?php if(in_array("after-order-son/set-bandwidth-operation", $node) || $this->params['is_administrator_user']):?>	
													<a href="javascript:;" class="set_bandwidth_go" sonorderid="<?php echo $val['ao_s_sonlist_id'];?>">立即设定</a>
												<?php endif;?>												
											<?php endif;?>
											
										<?php endif;?>
									</td>
								</tr>
							</table>
						<?php endif;?>						
						<!-- 带宽设定end -->
						
					</td>
				</tr>
			<?php endforeach;?>
		</table>
	</div>
	<?php endif;?>
	
	<style type="text/css">
		.ao-finish-box{padding-bottom:200px;height:150px;}
		.ao-finish-box div{display:inline-block;float:left;margin-right:20px;}
		.ao-finish-box div .ao_finish_remark{height:150px;padding:5px;min-width:500px;resize:none;}
		.ao-finish-box div .complet_workorder{margin-top:117px;}
	</style>
	<?php if($AfterOrderInfo['ao_type'] == '未完成' && $AfterOrderInfo['ao_takeover_admin_id'] != '' && (in_array($admin_id, $allow_admin) || $admin_id == 1)):?>
		<div class="info-title">工单处理结果与完结</div>
		<div class="info-box">
			<div class="ao-finish-box">
				<div>
					<textarea name="ao_finish_remark" class="ao_finish_remark" placeholder="在此填写一些工单完成或异常备注"></textarea>
				</div>
				<div>
					<?php if(in_array("after-order/complet-workorder", $node) || $this->params['is_administrator_user']):?>
						<button type="button" class="btn btn-success btn-sm complet_workorder" ao_id="<?php echo $AfterOrderInfo['ao_id'];?>">完成工单</button>
					<?php endif;?>
				</div>
			</div>
		</div>
	<?php elseif($AfterOrderInfo['ao_type'] == '已取消'):?>
		<div class="info-title">工单处理结果与完结</div>
		<div class="info-box">
			<img src="/img/close.png" style="display:block;width:300px;margin:0 auto;margin-top:50px;">
		</div>
	<?php else:?>
	<?php endif;?>
</div>

<div id="trailBox" style="display:none;">
	<table class="table float_table">
		<tr>
			<td><span style="color:red">*</span>审核结果：</td>
			<td>
				<select class="form-control trail_result">
					<option value="">请选择审核结果</option>
					<option value="通过">通过</option>
					<option value="拒绝">拒绝</option>
				</select>
			</td>
		</tr>
		<tr>
			<td>审核备注：</td>
			<td>
				<textarea class="form-control trail_remark" style="width:200px;height:100px;resize:none;"></textarea>
			</td>
		</tr>
	</table>
</div>


<!--重装系统 start-->
<style type="text/css">
.form-horizontal {padding-left: 30px;}
.font-size-12 {font-size: 12px;}
.ny-form .form-group {margin-bottom: 12px;line-height: 30px;}
.form-horizontal .form-group {margin-right: -15px;margin-left: -15px;}
.ny-form .ny-control-label {float: left;}
.ny-control-label {width: 160px;padding-right: 0;text-align: right;color: #808080;}
.ny-form-control {display: inline-block;white-space: nowrap;color: #555;background-color: #fff;background-image: none;outline: none;}    
.ny-number-container {float: left;line-height: 1;}
.number-input-box {width: 131px;}
.number-input-box {float: left;position: relative;width: 100px;border-radius: 2px;}
.alert-warn, .alert-error, .alert-success {padding: 7px 22px 5px 37px;background: url(img/uc/tip_icon_warn_16.png) no-repeat 10px 10px #fff3e5;
border: 1px solid #ff8800;border-radius: 2px;color: #ff8800;font-size: 12px;line-height: 2em;}
.margin-bottom-20 {margin-bottom: 20px;}

</style>
<div class="bootbox modal fade bootbox-prompt in" id="Reload_system_show" tabindex="-1" role="dialog" style="padding-right: 17px;">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">×</font>
					</font>
				</button>
				<h4 class="modal-title">
				<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">重装系统:</font></font></h4>
			</div>
			<div class="modal-body">
				<div class="bootbox-body">  
					<div class="alert-warn margin-bottom-20">
						1、重装系统可能导致数据丢失，请谨慎使用
					</div>
					<div class="clearfix">
						<form id="ReloadsystemForm" action="" class="bootbox-form form-horizontal ny-form ny-tab-container font-size-12 tab-group-undefined">
						<input type="hidden" name="ao_id" value="<?php echo $AfterOrderInfo['ao_id'];?>">
						<!--start -->
						<div class="form-group" style="border-bottom: 1px solid #DCE8F1;">
							<div class="ny-control-label" style="text-align: center;font-size: 14px;color:#393939;">选择系统</div>
							<div class="col-xs-8 ny-form-control">
							</div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">选择系统分类：</div>
							<div class="col-xs-8 ny-form-control">
								<div class="ny-number-container">																	
									<select name="cloudsystem_class" id="cloudsystem_class" class="col-xs-12"><option></option>
									</select>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="ny-control-label">选择系统：</div>
							<div class="col-xs-8 ny-form-control">
								<div class="ny-number-container">																	
									<select name="cloudsystem" id="cloudsystem" class="col-xs-12">
									<option>--请先选择系统模板--</option>
									</select>
								</div>
							</div>
						</div>
						<!--分区设置 -->
						<div id="partition_setting" style="display:none">
							<div class="form-group" style="border-bottom: 1px solid #DCE8F1;">
								<div class="ny-control-label" style="text-align: center;font-size: 14px;color:#393939;">分区设置</div>
								<div class="col-xs-8 ny-form-control">
								</div>
							</div>
							<div class="form-group">
								<div class="ny-control-label"><label style="color:red;font-size: 12px;">*&nbsp;</label>分区类型：</div>
								<div class="col-xs-8 ny-form-control">
									<div class="ny-number-container">
										<select name="partition_type" id="partition_type" class="col-xs-12">
											<option value="">请选择分区类型</option>
											<option value="LVM">LVM</option>
											<option value="Stardard">Stardard</option>
										</select>
									</div>
								</div>
							</div>
							<div class="form-group">
								<div class="ny-control-label"><label style="color:red;font-size: 12px;">*&nbsp;</label>文件系统类型：</div>
								<div class="col-xs-8 ny-form-control">
									<div class="ny-number-container">
										<select name="file_system_type" id="file_system_type" class="col-xs-12">
											<option value="">请选择文件系统类型</option>
											<option value="ext4">ext4</option>
											<option value="xfs">xfs</option>
										</select>
									</div>
								</div>
							</div>
							<div class="form-group">
								<div class="ny-control-label"><label style="color:red;font-size: 12px;">*&nbsp;</label>swap分区大小：</div>
								<div class="col-xs-8 ny-form-control">
									<div class="ny-number-container">
										<input type="text" name="swap_partition_size" id="swap_partition_size" value="2" style=""> G
									</div>
								</div>
								<label style="color:red;font-size: 10px;margin-bottom: 0px;">注：单位G, 值范围 2 - 32</label>
							</div>							
							<div class="form-group">
								<div class="ny-control-label"><label style="color:red;font-size: 12px;">*&nbsp;</label>根分区大小：</div>
								<div class="col-xs-8 ny-form-control">
									<div class="ny-number-container apply_surplus_space">
										<input type="text" name="root_partition_size" id="root_partition_size" value="all" readonly style="float:left">
										<!--&nbsp;&nbsp;<input type="radio" name="select_usesurplus" value="root_partition_size" checked>使用剩余空间-->
										<div class="radio" style="float:left">
											<label>
												<input name="select_usesurplus" type="radio" value="root_partition_size" checked class="ace">
												<span class="lbl"> 使用剩余空间</span>
											</label>
										</div>
										
									</div>
								</div>
								<label style="color:red;font-size: 10px;margin-bottom: 0px;">注：单位G, 值范围 >=1</label>
							</div>							
							<div class="form-group">
								<div class="ny-control-label"><label style="font-size: 12px;">（可选）&nbsp;</label>home分区大小：</div>
								<div class="col-xs-8 ny-form-control">
									<div class="ny-number-container apply_surplus_space">
										<input type="text" name="home_partition_size" id="home_partition_size" value="" style="float:left"  placeholder="单位G, 值范围 >=1">
										<!--&nbsp;&nbsp;<input type="radio" name="select_usesurplus" value="home_partition_size" >使用剩余空间-->
										<div class="radio" style="float:left">
											<label>
												<input name="select_usesurplus" type="radio" value="home_partition_size"  class="ace">
												<span class="lbl"> 使用剩余空间</span>
											</label>
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<div class="ny-control-label"><label style="font-size: 12px;">（可选）&nbsp;</label>www分区大小：</div>
								<div class="col-xs-8 ny-form-control">
									<div class="ny-number-container apply_surplus_space">
										<input type="text" name="www_partition_size" id="www_partition_size" value="" style="float:left"  placeholder="单位G, 值范围 >=1">
										<!--&nbsp;&nbsp;<input type="radio" name="select_usesurplus" value="www_partition_size" >使用剩余空间-->
										<div class="radio" style="float:left">
											<label>
												<input name="select_usesurplus" type="radio" value="www_partition_size"  class="ace">
												<span class="lbl"> 使用剩余空间</span>
											</label>
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<div class="ny-control-label"><label style="font-size: 12px;">（可选）&nbsp;</label>data分区大小：</div>
								<div class="col-xs-8 ny-form-control">
									<div class="ny-number-container apply_surplus_space">
										<input type="text" name="data_partition_size" id="data_partition_size" value="" style="float:left" placeholder="单位G, 值范围 >=1">
										<!--&nbsp;&nbsp;<input type="radio" name="select_usesurplus" value="data_partition_size" >使用剩余空间-->
										<div class="radio" style="float:left">
											<label>
												<input name="select_usesurplus" type="radio" value="data_partition_size" class="ace">
												<span class="lbl"> 使用剩余空间</span>
											</label>
										</div>
									</div>
								</div>
							</div>							
						</div>
						<!--end -->
						</form>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
					<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">关 闭</font></font>
				</button>
				<button class="btn btn-success" id="Reload_system_go" data-last="Finish">
					<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">确定提交</font></font>
				</button>
			</div>
		</div>
	</div>
</div>
<!-- 重装系统 End -->

<style type="text/css">
	.checkfont{font-size:0px;}
	.iplittlebox{float:left;display:inline-block;font-size:12px;margin-right:10px;margin-bottom:10px;}
	.iplittlebox input{height:31px;line-height:31px;}
</style>

<!--重置IP -->
<div class="bootbox modal fade bootbox-prompt in" id="reset_ip_moadl" tabindex="-1" role="dialog" style="padding-right: 17px;">
	<div class="modal-dialog" style="width: 660px;">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">×</font>
					</font>
				</button>
				<h4 class="modal-title">
				<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">机器重置IP:</font></font></h4>
			</div>
			<div class="modal-body">
				<div class="bootbox-body">  
					<div class="alert-warn margin-bottom-20">
						1、重置IP只能在当前机器配置IP中选择 <br/>
						2、重置IP必须与当前机器配置IP有差异
					</div>

					<div class="clearfix">
						<form id="resetip_form" action="" class="bootbox-form form-horizontal ny-form ny-tab-container font-size-12 tab-group-undefined">
						<input type="hidden" name="ao_id" value="<?php echo $AfterOrderInfo['ao_id'];?>">
						<!--start -->
						<div class="form-group" id="reset_iplist">
							<div class="iplittlebox">
								<input type="text" readonly="" placeholder="IP段：127.0.0.1-2" value="">            		
								<button type="button" id="add_ip" class="btn btn-xs btn-info btn-success"><i class="ace-icon glyphicon glyphicon-plus"></i></button>
							</div>
						
							<?php foreach(ArrayHelper::getValue($content,'ip',[]) as $ipk => $ipv): ?>
							<div class="iplittlebox">
								<input value="<?php echo $ipv;?>" type="text" name="ips[]" >
								<button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger">
								<i class="ace-icon glyphicon glyphicon-remove"></i></button>
							</div>				
							<?php endforeach;?>

						</div>
						
						</form>
					</div>
				</div>
			</div>
			<div class="modal-footer">				
				<button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
					<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">关 闭</font></font>
				</button>
				<button class="btn btn-success" id="reset_ip_go" data-last="Finish">
					<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">确定提交</font></font>
				</button>
			</div>
		</div>
	</div>
</div>
<!--重置IP -->

<!--设定带宽 -->
<div class="bootbox modal fade bootbox-prompt in" id="set_bandwidth_moadl" tabindex="-1" role="dialog" style="padding-right: 17px;">
	<div class="modal-dialog" style="width: 660px;">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">×</font>
					</font>
				</button>
				<h4 class="modal-title">
				<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">带宽设定:</font></font></h4>
			</div>
			<div class="modal-body">
				<div class="bootbox-body">  
					<div class="alert-warn margin-bottom-20">
						1、重置IP只能在当前机器配置IP中选择 <br/>
						2、重置IP必须与当前机器配置IP有差异
					</div>

					<div class="clearfix">
						<form id="resetip_form" action="" class="bootbox-form form-horizontal ny-form ny-tab-container font-size-12 tab-group-undefined">
						<input type="hidden" name="ao_id" value="<?php echo $AfterOrderInfo['ao_id'];?>">
							<div class="bootbox-body">
								<form id="update_inbound_form" action="" class="bootbox-form form-horizontal ny-form ny-tab-container font-size-12 tab-group-undefined">              			
									<div class="form-group">
										<div class="ny-control-label">当前上行限速：</div>
										<div class="ny-form-control col-xs-1 uplink_speed_limit"></div>
										<a href="javascript:;" class="btn btn-success update_speed_limit" rel="inbound" old_speed_limit="">修改上行</a>
									</div>
									<div class="form-group">
										<div class="ny-control-label">当前下行限速：</div>
										<div class="ny-form-control col-xs-1 downlink_speed_limit"></div>
										<a href="javascript:;" class="btn btn-success update_speed_limit" rel="outbound" old_speed_limit="">修改下行</a>
									</div>
								</form>            		
							</div>
						
						</form>
					</div>
				</div>
			</div>
			<div class="modal-footer">	
			
				<button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
					<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">关 闭</font></font>
				</button>
				
			</div>
		</div>
	</div>
</div>
<!--设定带宽 -->

<!-- 修改带宽 -->
<div class="bootbox modal fade bootbox-prompt in" id="update_speedlimit_show" tabindex="-1" role="dialog" style="padding-right: 17px;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
                    <font style="vertical-align: inherit;">
                        <font style="vertical-align: inherit;">×</font>
                    </font>
                </button>
                <h4 class="modal-title">
                <font style="vertical-align: inherit;"><font style="vertical-align: inherit;" class="update_speedlimit_name"></font></font></h4>
            </div>
            <div class="modal-body">
                <div class="bootbox-body">
                    <form id="update_speedlimit_form" action="" class="bootbox-form form-horizontal ny-form ny-tab-container font-size-12 tab-group-undefined">              			

            			<div class="form-group">
							<div class="ny-control-label" style="line-height: 40px;">选择限速策略：</div> 
            				<div class="col-xs-8 ny-form-control">
            					<div class="ny-number-container">
									<select name="speed_policy_select" id="speed_policy_select" class="form-control" style="margin-top: 5px;">
										<option>请选择</option>
    							    </select>
								</div>
            				</div>
            			</div>
						<input type="hidden" name="speed_type" value="">
						<input type="hidden" name="old_speed_limit" value="">
						<input type="hidden" name="ao_id" value="<?php echo $AfterOrderInfo['ao_id'];?>">
            		</form>            		
                </div>
            </div>
            <div class="modal-footer">
                <button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
                    <font style="vertical-align: inherit;">
                        <font style="vertical-align: inherit;">取消</font></font>
                </button>
                <button data-bb-handler="confirm" data-last="Finish" type="button" class="btn btn-primary" id="go_update_outbound">
                    <font style="vertical-align: inherit;"><font style="vertical-align: inherit;">确定</font></font>
                </button>
            </div>
        </div>
    </div>
</div>
<!--修改下行速度  END-->

<script type="text/javascript">

$(function() {
	//审核子单
	$(".trailSonorder").click(function() {
		var trail_id = $(this).attr("sonorderid");
		
		layer.open({
			type: 1, 
			area: ['500px', '400px'],
			title:"" || "审核子工单",
			content: $("#trailBox"),
			maxmin: true, //最大化按钮
			anim:3, //动画
			btn: ['确定', '关闭'],
			yes: function(index, layero) {
				layer.close(index);
				
				var trail_result = $(".trail_result").val();
				var trail_remark = $(".trail_remark").val();
				
				if(!trail_result) {
					layer.alert("请选择一种审核结果", {icon:7});
					return false;
				}
				
				var url = "<?php echo Url::to(['after-order-son/trail-son-order'])?>";
				
				$.post(url, {"trail_id":trail_id, "trail_result":trail_result, "trail_remark":trail_remark}, function(e) {
					
					if(e.data.status == 1) {
						layer.alert(e.data.info, {icon:1}, function() {
							location.reload();
						});
					} else {
						layer.alert(e.data.info, {icon:7});
					}
					
				}, "json");
			}
		});
	});
	
	//完成子单
	$(".complet_sub").click(function() {
		var sonorderid = $(this).attr("sonorderid");		
		layer.confirm("子单是否已经完成？", {icon:3,
			btn: ['确定','取消'], title:"子单完成确认"
		}, function() {
			var loading = layer.load(1, {shade: [0.7, '#fff']});
			var url = "<?php echo Url::to(['after-order-son/son-order-operation']);?>";			
			$.post(url, {"trail_id":sonorderid, 'operation_result':'完成'}, function(e) {
				layer.close(loading);
				if(e.data.status == 1) {
					layer.alert(e.data.info, {icon:1}, function(eee) {
						location.reload();
					});
				} else {
					layer.alert(e.data.info, {icon:7});
				}
			}, "json");
		});
	});
	
	//取消子单
	$(".cancel_sub").click(function() {
		var sonorderid = $(this).attr("sonorderid");
		layer.confirm("确定要取消子单吗？", {icon:3,
			btn: ['确定','取消'], title:"子单取消确认"
		}, function() {
			var loading = layer.load(1, {shade: [0.7, '#fff']});
			var url = "<?php echo Url::to(['after-order-son/son-order-operation']);?>";			
			$.post(url, {"trail_id":sonorderid, 'operation_result':'取消'}, function(e) {
				layer.close(loading);
				if(e.data.status == 1) {
					layer.alert(e.data.info, {icon:1}, function(eee) {
						location.reload();
					});
				} else {
					layer.alert(e.data.info, {icon:7});
				}
			}, "json");
		});
	});
	
	//立即重装
	$(".immediate_reload").click(function() {
		var sonorderid = $(this).attr("sonorderid");		
		layer.confirm("确认马上重装系统吗？", {icon:3,
			btn: ['确定','取消'], title:"重装系统确认"
		}, function() {
			var loading = layer.load(1, {shade: [0.7, '#fff']});
			var url = "<?php echo Url::to(['after-order-son/reloadsystem-operation']);?>";			
			$.post(url, {"trail_id":sonorderid, 'operation_result':'确认'}, function(e) {
				layer.close(loading);
				if(e.data.status == 1) {
					layer.alert(e.data.info, {icon:1}, function(eee) {
						location.reload();
					});
				} else {
					layer.alert(e.data.info, {icon:7});
				}
			}, "json");
		});
	});
	
	//取消重装
	$(".cancel_reload").click(function() {
		var sonorderid = $(this).attr("sonorderid");
		layer.confirm("确认取消重装系统吗？", {icon:3,
			btn: ['确定','取消'], title:"取消重装确认"
		}, function() {
			var loading = layer.load(1, {shade: [0.7, '#fff']});
			var url = "<?php echo Url::to(['after-order-son/reloadsystem-operation']);?>";			
			$.post(url, {"trail_id":sonorderid, 'operation_result':'取消'}, function(e) {
				layer.close(loading);
				if(e.data.status == 1) {
					layer.alert(e.data.info, {icon:1}, function(eee) {
						location.reload();
					});
				} else {
					layer.alert(e.data.info, {icon:7});
				}
			}, "json");
		});
	});
	
	//完成工单	
	$(".complet_workorder").click(function() {
		
		var ao_id = $(this).attr("ao_id");
		var ao_finish_remark = $(".ao_finish_remark").val();
		
		layer.confirm("工单是否已经完成？", {icon:3,
			btn: ['确定','取消'], title:"工单完成确认"
		}, function() {
			var loading = layer.load(1, {shade: [0.7, '#fff']});
			var url = "<?php echo Url::to(['complet-workorder']);?>";
			
			$.post(url, {"ao_id":ao_id, "ao_finish_remark":ao_finish_remark}, function(e) {
				layer.close(loading);
				if(e.data.status == 1) {
					layer.alert(e.data.info, {icon:1}, function(eee) {
						location.reload();
					});
				} else {
					layer.alert(e.data.info, {icon:7});
				}
			}, "json");
		});		
		
	});

	//立即设定带宽
	$(".set_bandwidth_go").click(function() {
		var sonorderid = $(this).attr("sonorderid");		
		layer.confirm("确认将此业务所属的自有机器带宽重新设定吗？", {icon:3,
			btn: ['确定','取消'], title:"带宽设定"
		}, function() {
			var loading = layer.load(1, {shade: [0.7, '#fff']});
			var url = "<?php echo Url::to(['after-order-son/set-bandwidth-operation']);?>";
			$.post(url, {"trail_id":sonorderid}, function(e) {
				layer.close(loading);
				if(e.data.status == 1) {
					layer.alert(e.data.info, {icon:1}, function(eee) {
						location.reload();
					});
				} else {
					layer.alert(e.data.info, {icon:7});
				}
			}, "json");
		});
	});
	
	
});

// set_bandwidth
$('.set_bandwidth').click(function(){	
	var loading = layer.load(2);
	var ao_id = $(this).attr("ao_id");	
	url = "<?php echo Url::to(['after-order-son/get-switch-config']);?>";
	$.post(url, {'ao_id':ao_id}, function(e) {
		layer.closeAll('loading');
		if ( e['data']['status'] ) {
			var config = e['data']['data'];
			$('.uplink_speed_limit').html(config.inbound);
			
			$('.downlink_speed_limit').html(config.outbound);
			$('#set_bandwidth_moadl').modal('show');
			return ;
		} else {
			layer.alert(e['data']['info'], {icon:7});
			return false;
		}
	},'json');	
	
	return ;
});

//获取限速策略  显示
$('.update_speed_limit').click(function() {
	var loading = layer.load(2, {shade: [0.7, '#fff']});
	var type = $(this).attr("rel");	
	var ao_id = "<?php echo $AfterOrderInfo['ao_id']?>";
	
	var _this = $(this);
	var url = "<?php echo Url::to(['after-order-son/get-speedstrategy']); ?>";
	$.post(url, {ao_id:ao_id}, function(data)	{
		layer.close(loading);
		
		if ( data['data']['status'] ) {	
			
			if( type == 'inbound') {
				
				var uplink = $(".uplink_speed_limit").html();
				_this.attr("old_speed_limit", uplink);
				var old_speed_limit = uplink;
				$('.update_speedlimit_name').html('修改上行限速');
			} else {
				
				var downlink = $(".downlink_speed_limit").html();
				_this.attr("old_speed_limit", downlink);
				var old_speed_limit = downlink;
				$('.update_speedlimit_name').html('修改下行限速');
			}

			$("[name='speed_type']").val(type);
			$("[name='old_speed_limit']").val(old_speed_limit);			
			$("[name='speed_policy_select']").empty();
			$("[name='speed_policy_select']").append('<option value="">请选择</option>');

			$.each(data['data']['data'], function(key, val){
				if(val.length == 0) {
				} else {
					$("[name='speed_policy_select']").append('<option value="'+val+'" rel="'+val+'">'+val+'</option>');
				}
			});
			
			$('#update_speedlimit_show').modal('show');
			
		} else {
			layer.alert(data['data']['info'], {icon:7});
		}
		
	},'json');
	return ;
	
});


//修改限速提交子单
$('#go_update_outbound').click(function(){
	
	var _this = $(this);
	
	var speed_policy_select = $('#speed_policy_select option:selected').val(); 

	if(!speed_policy_select) {
		layer.alert("请先选择限速策略", {icon:7});
		return false;
	}
	
	layer.confirm('确定要重新设定限速策略吗？',{icon:3,
		btn: ['确定','取消'] //按钮
	}, function() {
		
		var url = "<?php echo Url::to(['after-order-son/set-bandwidth']) ?>";
		var loading = layer.load(0, {shade:[0.7, '#f1f3f5']});
		
		$.post(url, $('#update_speedlimit_form').serialize(), function(data){
			
			layer.closeAll('loading');
			
			if ( data['data']['status'] ) {
				layer.alert(data['data']['info'], {icon:1}, function(index){				   		  
					location.reload();
				});
			} else {
				layer.alert(data['data']['info'], {icon:7});
			}
		},'json');
	}, function(){
	   
	});
	return false;
});	

// reset_ip
$('.reset_ip').click(function() {
	
	$("[name='cloudsystem']").empty();
	$("[name='cloudsystem']").append('<option value="">请选择系统</option>');  
	
	$("[name='cloudsystem_class']").empty();
	$("[name='cloudsystem_class']").append('<option value="">请选择系统分类</option>');  			
	
	$('#reset_ip_moadl').modal('show');
	return ;
});

$('#reset_ip_go').click(function() {
	var load = layer.load(2);
	url = "<?php echo Url::to(['after-order-son/reset-ip']); ?>";
	$.post(url, $('#resetip_form').serialize(), function(e){
		layer.closeAll();
		if ( e['data']['status'] ) {		
			layer.alert(e['data']['info'], {icon:1}, function(index){				   		  
				location.reload();
			});
		} else {
			layer.alert(e['data']['info'], {icon:7});
		}
	},'json');	
});

$(document).ready(function() {
	
	var MaxInputs    = 1000; //maximum input boxes allowed
	var InputsWrapper  = $("#reset_iplist"); //Input boxes wrapper ID
	var AddButton    = $("#add_ip"); //Add button ID
	var x = InputsWrapper.length; //initlal text box count
	var FieldCount=1; //to keep track of text box added
	$(AddButton).click(function (e) //on add input button click
	{
		if(x <= MaxInputs) //max input box allowed
		{
		  FieldCount++; //text box added increment
		  //add input box
		  $(InputsWrapper).append('<div class="iplittlebox"><input value="" type="text" name="ips[]"/><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button><div>');
		  x++; //text box increment
		}
		return false;
	});
	$("body").on("click",".removeclass", function(e){ //user click on remove text                
		$(this).parent('div').remove(); //remove text box                   
		return false;
	})
	
});

//#主工单中IP对换
$('.exchangedip').click(function(){
	var ao_id = $(this).attr("ao_id");
	layer.confirm("确定要进行机器IP互换吗？", {icon:3,
		btn: ['确定','取消'], title:"IP互换确认"
	}, function() {
		var loading = layer.load(1, {shade: [0.7, '#fff']});
		var url = "<?php echo Url::to(['exchangedip']);?>";
		
		$.post(url, {"ao_id":ao_id}, function(e) {
			layer.close(loading);
			if(e.data.status == 1) {
				layer.alert(e.data.info, {icon:1}, function(eee) {
					location.reload();
				});
			} else {
				layer.alert(e.data.info, {icon:7});
			}
		}, "json");
	});	
});

//#子工单中IP对换
$('.son_exchangedip').click(function(){
	var ao_sonid = $(this).attr("ao_sonid");

	layer.confirm("确定要进行机器IP互换吗？", {icon:3,
		btn: ['确定','取消'], title:"子单IP互换确认"
	}, function() {
		var loading = layer.load(1, {shade: [0.7, '#fff']});
		var url = "<?php echo Url::to(['after-order-son/son-exchangedip']);?>";
		
		$.post(url, {"ao_sonid":ao_sonid}, function(e) {
			layer.close(loading);
			if(e.data.status == 1) {
				layer.alert(e.data.info, {icon:1}, function(eee) {
					location.reload();
				});
			} else {
				layer.alert(e.data.info, {icon:7});
			}
		}, "json");
	});	
});

//重装系统页面
$('.Reload_system').click(function(){
	var load = layer.load(2);
	url = "<?php echo Url::to(['idle-pdt/get-cloudsystemclass']); ?>";
	$.post(url, {}, function(data){
		if ( data['data']['status'] ) {		
			$("[name='cloudsystem']").empty();
			$("[name='cloudsystem']").append('<option value="">请选择系统</option>');  
			
			$("[name='cloudsystem_class']").empty();				
			$("[name='cloudsystem_class']").append('<option value="">请选择系统分类</option>');  			
			$.each(data['data']['info'], function(key, val){
				$("[name='cloudsystem_class']").append('<option value="'+val['class_id']+'" rel="'+val['custom_partition']+'">'+val['class_name']+'</option>');
			});
			layer.closeAll('loading');
			$('#Reload_system_show').modal('show');
			$("#partition_setting").hide();
			return ;
		} else {
			layer.alert(data['data']['info'], {icon:7});
		}
	},'json');	
});

//获取重装系统模板		
$('#cloudsystem_class').change(function(){
	var loading = layer.load(2);
	var class_id = $(this).find('option:selected').val();    	
	url = "<?php echo Url::to(['idle-pdt/get-cloudsystem']);?>";
	$.post(url, {'class_id':class_id}, function(data){
		if ( data['data']['status'] ) {
			$("[name='cloudsystem']").empty();
			$("[name='cloudsystem']").append('<option value="">请选择系统</option>');  			
			$.each(data['data']['info'], function(key, val){
				$("[name='cloudsystem']").append('<option value="'+val['cloud_system_id']+'">'+val['cloud_system_name']+'</option>');
			});
			layer.closeAll('loading');
			return ;
		} else {
			layer.alert(data['data']['info'], {icon:7});
		}
	},'json');	
	if( $(this).find('option:selected').attr('rel') == 'Y') {
		$("#partition_setting").show();
	} else {
		$("#partition_setting").hide();
	}
});

//分区选择使用剩余空间
$('input[type=radio][name=select_usesurplus]').change(function() {
	$('.apply_surplus_space input[type="text"]').removeAttr("readOnly");
	$(".apply_surplus_space input[type='text']").each(function(){
		if($(this).val() == 'all') {
			var lastname = $(this).attr("name");
			$('input[name="'+lastname+'"]').val("");//根据name赋值
		}
	});
	var nowname = $('input[name="select_usesurplus"]:checked').val();
	$('input[name="'+nowname+'"]').val("all");//根据name赋值
	$('input[name="'+nowname+'"]').attr("readonly","readonly");
	return;
});

//重装系统提交
$('#Reload_system_go').click(function(){
	var _this = $(this);
	var cloudsystem = $('#cloudsystem option:selected').val(); 
	var cloudsystem_class = $('#cloudsystem_class option:selected').val(); 
	if(!cloudsystem_class) {
		layer.alert("请先选择系统分类", {icon:7});
		return false;
	}
	if(!cloudsystem) {
		layer.alert("请先选择系统模板", {icon:7});
		return false;
	}
	if( $('#cloudsystem_class option:selected').attr('rel') == 'Y' ) {
		var partition_type = $('#partition_type option:selected').val(); 
		var file_system_type = $('#file_system_type option:selected').val();
		var swap_partition_size = $('#swap_partition_size').val();
		var root_partition_size = $('#root_partition_size').val();			
		var home_partition_size = $('#home_partition_size').val();			
		var www_partition_size = $('#www_partition_size').val();
		var data_partition_size = $('#data_partition_size').val();
		if(!partition_type) {
			layer.alert("分区类型必须选择", {icon:7});
			return false;
		}
		if(!file_system_type) {
			layer.alert("文件系统类型必须选择", {icon:7});
			return false;
		}
		if(!swap_partition_size) {
			layer.alert("swap分区大小必须填写", {icon:7});
			return false;
		} else {
			if(!isPInt(swap_partition_size)) {
				layer.alert("swap分区大小值输入有误", {icon:7});
				return false;
			}
			if( swap_partition_size < 2 || swap_partition_size > 32) {
				layer.alert("swap分区大小值范围必须在2-32之间", {icon:7});
				return false;
			}
		}
		if(!root_partition_size) {
			layer.alert("根分区大小必须填写", {icon:7});
			return false;
		} else {
			if(root_partition_size != 'all') {
				if(!isPInt(root_partition_size)) {
					layer.alert("根分区大小值输入有误", {icon:7});
					return false;
				}
			}
		}
		if(home_partition_size) {
			if(home_partition_size != 'all') {
				if(!isPInt(home_partition_size)) {
					layer.alert("home分区大小值输入有误", {icon:7});
					return false;
				}
			}
		}			
		if(www_partition_size) {
			if(www_partition_size != 'all') {
				if(!isPInt(www_partition_size)) {
					layer.alert("www分区大小值输入有误", {icon:7});
					return false;
				}
			}
		}			
		if(data_partition_size) {
			if(data_partition_size != 'all') {
				if(!isPInt(data_partition_size)) {
					layer.alert("data分区大小值输入有误", {icon:7});
					return false;
				}
			}
		}			
	}
	
	layer.confirm('确定要重装系统吗？',{icon:3,
		btn: ['确定','取消'] //按钮
	}, function(){
		layer.closeAll();
		var url = "<?php echo Url::to(['after-order-son/reload-system']) ?>";
		var loading = layer.load(0, {shade:[0.7, '#f1f3f5']});
		$.post(url, $('#ReloadsystemForm').serialize(), function(data){
			
			layer.closeAll('loading');
			if ( data['data']['status'] ) {
				layer.alert(data['data']['info'], {icon:1}, function(index){				   		  
					location.reload();
				});
			} else {
				layer.alert(data['data']['info'], {icon:7});
			}
		},'json');
	}, function(){
	   
	});
	return false;
});	

//正整数验证
function isPInt(number) {
	var g = /^[1-9]*[1-9][0-9]*$/;
	if (!g.test(number)) {
　　　　return false;
　　} else {
		return true;
	}
}

</script>
<?php $this->endBlock(); ?>