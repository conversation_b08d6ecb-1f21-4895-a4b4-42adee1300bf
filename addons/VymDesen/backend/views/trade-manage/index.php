<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\LinkPager;
use addons\VymDesen\common\components\DataHelper;
use common\helpers\ArrayHelper;
use common\helpers\MemberHelper;

$this->title = '控制台管理-订单列表';
?>
<?php $this->beginBlock('content') ?>
<?php $node = \Yii::$app->session['auth']['node']; ?>
    <div class="row" style="margin-bottom: 10px;">
        <form action="">
            <input type="hidden" name="r" value="trade-manage/trade-list">
            <div class="col-xs-12">
                <div style="float: left;margin-right: 6px;margin-top:8px;">
                    <input type="text" class="form-control search-query" name="order_id"
                           value='<?php echo Html::encode(Yii::$app->controller->get('order_id')) ?>'
                           placeholder="订单编号">
                </div>
                <div style="float: left;margin-right: 6px;margin-top:8px;">
                    <input type="text" class="form-control search-query" name="order_user_nickname"
                           value='<?php echo Html::encode(Yii::$app->controller->get('order_user_nickname')) ?>'
                           placeholder="用户昵称">
                </div>
                <div style="float: left;margin-right: 6px;margin-top:8px;">
                    <input type="text" class="form-control search-query" name="order_user_email"
                           value='<?php echo Html::encode(Yii::$app->controller->get('order_user_email')) ?>'
                           placeholder="用户账户">
                </div>
                <div style="float: left;margin-right: 6px;margin-top:8px;">
                    <select class="form-control" name='order_type' id="form-field-select-1">
                        <option value="">订单分类</option>
                        <option value="新购"
                                <?php if (Html::encode(Yii::$app->controller->get('order_type')) == "新购"): ?>selected='selected'<?php endif ?>>
                            新购配置
                        </option>
                        <option value="续费"
                                <?php if (Html::encode(Yii::$app->controller->get('order_type')) == "续费"): ?>selected='selected'<?php endif ?>>
                            配置续费
                        </option>
                        <option value="变更配置"
                                <?php if (Html::encode(Yii::$app->controller->get('order_type')) == "变更配置"): ?>selected='selected'<?php endif ?>>
                            变更配置
                        </option>
                        <option value="更换机器"
                                <?php if (Html::encode(Yii::$app->controller->get('order_type')) == "更换机器"): ?>selected='selected'<?php endif ?>>
                            更换机器
                        </option>

                    </select>
                </div>
                <div style="float: left;margin-right: 6px;margin-top:8px;">
                    <select class="form-control" name='order_status' id="form-field-select-1">
                        <option value="">订单状态</option>
                        <option value="待审核"
                                <?php if (Html::encode(Yii::$app->controller->get('order_status')) == "待审核"): ?>selected='selected'<?php endif ?>>
                            待审核
                        </option>
                        <option value="进行中"
                                <?php if (Html::encode(Yii::$app->controller->get('order_status')) == "进行中"): ?>selected='selected'<?php endif ?>>
                            进行中
                        </option>
                        <option value="已完成"
                                <?php if (Html::encode(Yii::$app->controller->get('order_status')) == "已完成"): ?>selected='selected'<?php endif ?>>
                            已完成
                        </option>
                        <option value="已取消"
                                <?php if (Html::encode(Yii::$app->controller->get('order_status')) == "已取消"): ?>selected='selected'<?php endif ?>>
                            已取消
                        </option>
                    </select>
                </div>
                <?php if ($is_sale): ?>
                    <div style="float: left;margin-right: 6px;margin-top:8px;">
                        <select class="form-control" name='isAll' id="form-field-select-1">
                            <option value="0" <?php if (Html::encode(Yii::$app->controller->get('isAll')) == "0") {
                                echo 'selected';
                            } ?>>只显示自己的订单
                            </option>
                            <option value="1" <?php if (Html::encode(Yii::$app->controller->get('isAll')) == "1") {
                                echo 'selected';
                            } ?>>显示所有订单
                            </option>
                        </select>
                    </div>
                <?php else: ?>
                    <div style="float: left;margin-right: 6px;margin-top:8px;">
                        <select class="form-control" name='order_admin_id' id="form-field-select-1">
                            <option value="">所有销售</option>
                            <?php foreach ($UserAdminRes as $k => $v): ?>
                                <option value="<?php echo $v['admin_id'] ?>" <?php if (Html::encode(Yii::$app->controller->get('order_admin_id')) == $v['admin_id']) {
                                    echo 'selected';
                                } ?>><?php echo $v['uname'] ?></option>
                            <?php endforeach ?>

                        </select>
                    </div>
                <?php endif; ?>
                <div style="float:left;margin-right:6px;margin-top:8px;">
                    <button type="submit" class="btn btn-white btn-primary btn-bold">
                        <span class="ace-icon fa fa-search icon-on-right bigger-110"></span>
                        搜索
                    </button>
                    <button type="button" class="btn btn-white btn-success btn-bold" style="margin-left:10px;"
                            onclick="javascript:location.href='<?php echo Url::to(['trade-list']) ?>'">
                        <span class="ace-icon fa  fa-refresh"></span>
                        刷新
                    </button>
                </div>
            </div>
        </form>
    </div>
    <table id="simple-table" width="100%" class="table table-bordered">
        <thead>
        <tr>
            <th>下单信息</th>
            <th>付款信息</th>
            <th>订单编号</th>
            <th>订单类型</th>
            <th>用户账户</th>
            <th>用户昵称</th>
            <th>销售名称</th>
            <th>订单总额</th>
            <th>已支付总额</th>
            <th>支付状态</th>
            <th>价格锁定</th>
            <th>订单状态</th>
            <th>是否删除</th>
            <th>下单时间</th>
            <th>订单操作</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($arrRes as $key => $val): ?>
            <tr>
                <td><a href="javascript:;" class=" blue lookDetail"
                       data-orderid="<?php echo $val['order_id'] ?>">下单内容</a>
                </td>
                <td><a href="javascript:;" class=" blue lookPayorder" data-orderid="<?php echo $val['order_id'] ?>">查看付款单</a>
                </td>
                <td><?php echo $val['order_id'] ?></td>
                <td><?php echo $val['order_type'] ?></td>
                <td><?php echo ArrayHelper::getPresent($val, 'order_user_email', function($v){
                    return MemberHelper::userAccount($v);
                }, 'userMember') ?></td>
                <td><?php echo ArrayHelper::getPresent($val, 'order_user_nickname', function($v){
                    return MemberHelper::userNick($v);
                }, 'userMember') ?></td>
                <td><?php echo $val['order_admin_name'] ?></td>
                <td><?php echo $val['order_update_price'] ?></td>
                <td><?php echo $val['order_finish_pay'] ?></td>
                <td>
                    <?php if ($val['order_pay_status'] == '未支付'): ?>
                        <label class="label label-default">未支付</label>
                    <?php elseif ($val['order_pay_status'] == '部分支付'): ?>
                        <label class="label label-primary">部分支付</label>
                    <?php elseif ($val['order_pay_status'] == '已支付'): ?>
                        <label class="label label-success">已支付</label>
                    <?php else: ?>
                        <label class="label label-danger">已退款</label>
                    <?php endif; ?>
                </td>
                <td><?php echo $val['order_update_status'] == '未锁定' ? '<label class="label label-warning">未锁定</label>' : '<label class="label label-default">已锁定</label>' ?></td>
                <td>
                    <?php if ($val['order_status'] == '进行中'): ?>
                        <label class="label label-primary">进行中</label>
                    <?php elseif ($val['order_status'] == '待审核'): ?>
                        <label class="label label-warning">待审核</label>
                    <?php elseif ($val['order_status'] == '已完成'): ?>
                        <label class="label label-success">已完成</label>
                    <?php else: ?>
                        <label class="label label-default">已取消</label>
                    <?php endif; ?>
                </td>
                <td><?php echo $val['is_remove']; ?></td>
                <td><?php echo $val['order_time_create'] ? date("Y-m-d H:i:s", $val['order_time_create']) : '-' ?></td>
                <td>
                    <?php if ($val['order_status'] != '待审核'): ?>
                        <!-- 取消订单，订单后付款，订单详情 -->
                        <?php if (in_array("trade-manage/trade-detail", $node) || $this->params['is_administrator_user']): ?>
                            <a class="blue" href="<?php echo Url::to([
                                'trade-manage/trade-detail',
                                'order_id' => $val['order_id']
                            ]) ?>" title="订单详情">
                                查看详情
                            </a>&nbsp;&nbsp;
                        <?php endif; ?>
                    <?php endif; ?>

                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
    <div class="row">
        <div class="col-xs-6">
            <div class="dataTables_info" id="dynamic-table_info" role="status" aria-live="polite">
                共有<?php echo $iCount; ?>条记录
            </div>
        </div>
        <div class="col-xs-6 pagination1" style="text-align: right;">
            <?php
            echo LinkPager::widget([
                'pagination' => $page,
                'firstPageLabel' => "首页",
                'prevPageLabel' => '上一页',
                'nextPageLabel' => '下一页',
                'lastPageLabel' => '末页',
            ]);
            ?>
        </div>
    </div>
    <style type="text/css">
        .detailTable {
            width: 100%;
            border-collapse: collapse;
            background: #f9f9f9;
            border: 1px #ccc solid;
        }

        .detailTable tr td {
            padding: 8px;
            border: 1px #ccc solid;
        }

        .changeConfigBox {
            width: 400px;
            height: 30px;
            line-height: 30px;
        }

        .changeConfigBox .title {
            width: 70px;
            float: left;
            text-align: center;
            height: 30px;
            line-height: 30px;
            border-right: 1px #ccc solid;
        }

        .changeConfigBox .content {
            width: 320px;
            float: left;
            text-align: left;
            height: 30px;
            line-height: 30px;
        }

        .changeConfigBox .content .front_content {
            width: 140px;
            height: 30px;
            line-height: 30px;
            float: left;
            padding: 0 5px;
            border-right: 1px #ccc solid;
        }

        .changeConfigBox .content .after_content {
            width: 140px;
            height: 30px;
            line-height: 30px;
            float: left;
            padding: 0 5px;
        }

        .changeConfigBox .content .small {
            width: 100px !important;
        }

        .changeConfigBox .smallcontent {
            width: 240px !important;
        }

        .smallconfigbox {
            width: 320px !important
        }

        .renewBox {
            float: left;
            height: 30px;
            width: 30px;
            display: inline-block;
            text-align: center;
            line-height: 30px;
        }

        .minusRenew {
            border: 1px #ccc solid;
            cursor: pointer;
            font-size: 30px;
            line-height: 28px;
        }

        .plusRenew {
            border: 1px #ccc solid;
            cursor: pointer;
            font-size: 26px;
        }
    </style>

    <!-- inline scripts related to this page -->

    <script type="text/javascript">

        $("#simple-table").on("click", '.lookPayorder', function () {
            $(".orderDetail").remove();
            var orderid = $(this).attr("data-orderid");
            var url = "<?php echo Url::to(['trade-manage/select-pay-info'])?>";
            var _this = $(this);
            $.post(url, {"order_id": orderid}, function (e) {
                if (e.data.status == 0) {
                    layer.alert(e.data.info, {icon: 7});
                } else {
                    var nowDom = _this.parents('tr');

                    var detailInfo = '<tr class="orderDetail"><td colspan="15"><table class="detailTable"><tr><td>付款单项目抬头</td><td>付款单号</td><td>付款金额</td><td>付款平台</td><td>付款时间</td><td>付款状态</td><td>财务审核</td><td>付款单属性</td></tr>';
                    $.each(e.data.data, function (i, n) {
                        detailInfo += '<tr><td>' + n.general_payorder_title + '</td><td>' + n.general_payorder + '</td><td>' + n.general_pay_money + '</td>';
                        if (n.payment) {
                            detailInfo += '<td>' + n.payment.another_name + '</td>';
                        } else {
                            detailInfo += '<td>-</td>';
                        }

                        if (n.general_pay_time) {
                            detailInfo += '<td>' + ChangeDateFormat(n.general_pay_time) + '</td>';
                        } else {
                            detailInfo += '<td>-</td>';
                        }

                        detailInfo += '<td>' + n.general_pay_lock + '</td><td>' + n.general_payinfo_review + '</td>';

                        if (n.general_payorder_type == 'original') {
                            detailInfo += '<td>原始付款单</td>';
                        } else {
                            detailInfo += '<td>自定义付款单</td>';
                        }

                        detailInfo += '</tr>';
                    });

                    detailInfo += '</table></td></tr>';
                    _this.parents('tr').after(detailInfo);
                    $(".orderDetail").show();
                }

            }, "json");
        });

        $("#simple-table").on("click", '.lookDetail', function () {
            $(".orderDetail").remove();
            var orderid = $(this).attr("data-orderid");
            var url = "<?php echo Url::to(['trade-manage/select-order-info'])?>";
            var _this = $(this);
            $.post(url, {"order_id": orderid}, function (e) {
                //console.log(e.data);
                if (e.data.status == 0) {
                    layer.alert(e.data.info, {icon: 7});
                } else {
                    var nowDom = _this.parents('tr');

                    var trade_type = e.data.data_trade_type;

                    var detailInfo = '';

                    if (trade_type == '新购') {
                        detailInfo += '<tr class="orderDetail"><td colspan="15"><table class="detailTable"><tr><td>新购区域</td><td>新购配置</td><td>新购价格</td><td>新购备注</td></tr>';
                        $.each(e.data.data, function (i, n) {
                            detailInfo += '<tr><td>' + n.servername + ' / ' + n.pdtname + '</td><td>' + n.config.cpu + ' / ' + n.config.ram + ' / ' + n.config.hdd + ' / ' + n.config.ipnumber + ' / ' + n.config.defense + ' / ' + n.config.operatsystem + '</td><td>￥' + n.detail_price + '</td><td>' + n.detail_remark + '</td></tr>';
                        });
                    } else if (trade_type == '变更配置') {
                        detailInfo += '<tr class="orderDetail"><td colspan="15"><table class="detailTable"><tr><td>产品信息</td><td style="width:150px;">产品IP地址</td><td style="width:500px;">变更配置内容</td><td>变更续费单价 / 续费周期</td><td>补款总金额</td></tr>';
                        $.each(e.data.data, function (i, n) {
                            detailInfo += '<tr><td>' + n.servername + ' / ' + n.pdtname + '</td><td>' + n.mainip + '</td>';
                            detailInfo += '<td><div class="changeConfigBox"><div class="title">变更项</div><div class="content"><div class="front_content">变更前配置</div><div class="after_content">变更后配置</div></div></div>';
                            $.each(n.modify_data, function (a, b) {
                                detailInfo += '<div class="changeConfigBox"><div class="title">' + b.modify_type + '</div><div class="content"><div class="front_content">' + b.modify_data.old_config + '</div><div class="after_content">' + b.modify_data.new_config + '</div></div></div>';
                            });
                            detailInfo += '</td>';
                            detailInfo += '<td><div class="changeConfigBox"><div class="title">变更项</div><div class="content smallcontent"><div class="front_content small">变更前配置</div><div class="after_content small">变更后配置</div></div></div><div class="changeConfigBox"><div class="title">续费单价</div><div class="content smallcontent"><div class="front_content small">' + n.old_sell_price + ' / ' + n.payment_cycle + '</div><div class="after_content small">' + n.modify_sell_price + ' / ' + n.payment_cycle + '</div></div></div></td><td>￥' + n.detail_price + '</td></tr>';
                        });
                    } else if (trade_type == '更换机器') {
                        var datainfo = e.data.data[0];
                        detailInfo += '<tr class="orderDetail"><td colspan="15"><table class="detailTable"><tr><td></td><td>产品区域</td><td>产品IP地址</td><td>产品配置</td><td>产品续费单价 / 续费周期</td><td>补款总金额</td></tr>';
                        detailInfo += '<tr><td>更换前</td><td>' + datainfo.oldpdtname + ' / ' + datainfo.oldservername + '</td><td>' + datainfo.oldiplist + '</td><td>' + datainfo.oldconfiginfo + '</td><td>' + datainfo.oldsellprice + '</td><td rowspan="2">' + datainfo.paymoney + '</td></tr>';
                        detailInfo += '<tr><td>更换后</td><td>' + datainfo.newpdtname + ' / ' + datainfo.newservername + '</td><td>' + datainfo.newiplist + '</td><td>' + datainfo.newconfiginfo + '</td><td>' + datainfo.newsellprice + '</td></tr>';
                    } else if (trade_type == '续费') {
                        detailInfo += '<tr class="orderDetail"><td colspan="15"><table class="detailTable"><tr><td>IP地址</td><td>续费配置</td><td>到期时间</td><td>续费单价</td><td>续费周期</td><td>续费数</td></tr>';
                        $.each(e.data.data, function (i, n) {
                            detailInfo += '<tr><td>' + n.ip + '</td><td>' + n.config.cpu + ' / ' + n.config.ram + ' / ' + n.config.hdd + ' / ' + n.config.ipnumber + ' / ' + n.config.defense + ' / ' + n.config.operatsystem + '</td><td>' + n.end_time + '</td><td>￥' + n.sell_price + '</td><td>' + n.payment_cycle + '</td><td>' + n.renew_num + '</td></tr>';
                        });
                    }

                    detailInfo += '</table></td></tr>';
                    _this.parents('tr').after(detailInfo);
                    $(".orderDetail").show();
                }
            }, "json");

        });

        $(".cancelOrder").click(function () {
            var orderid = $(this).attr("orderid");
            var _this = $(this);

            var index = layer.confirm("你确定要取消这笔订单吗?", {btn: ["是的", "点错了"]}, function () {
                layer.close(index);
                $.post("<?php echo Url::to(['trade-manage/cancel-order'])?>", {"orderid": orderid}, function (e) {
                    if (e.data.status == 1) {
                        var alindex = layer.alert(e.data.info, {"icon": 1}, function () {
                            layer.close(alindex);
                            _this.parents('td').prev('td').html('<span class="label label-grey">订单取消</span>');
                            _this.remove();
                        });
                    } else {
                        layer.alert(e.data.info, {"icon": 7});
                    }
                }, "json");
            });
        });

        function ChangeDateFormat(timestamp) {
            var date = new Date((parseInt(timestamp.replace("/Date(", "").replace(")/", ""), 10)) * 1000);
            Y = date.getFullYear() + '-';
            M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
            D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' ';
            h = (date.getHours() < 10 ? '0' + (date.getHours()) : date.getHours()) + ':';
            m = (date.getMinutes() < 10 ? '0' + (date.getMinutes()) : date.getMinutes()) + ':';
            s = (date.getSeconds() < 10 ? '0' + (date.getSeconds()) : date.getSeconds());
            return Y + M + D + h + m + s;
        }

    </script>
    </body>
<?php $this->endBlock(); ?>