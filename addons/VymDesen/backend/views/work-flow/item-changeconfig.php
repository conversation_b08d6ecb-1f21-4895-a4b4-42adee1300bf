<?php

use yii\helpers\Url;
use yii\helpers\Html;
use addons\VymDesen\common\components\DataHelper;

$this->title = '控制台管理 - 变更配置流程';

$technical_group = Yii::$app->params['technical_group'];#获取技术角色组 组合
$aftersale_group = Yii::$app->params['aftersale_group'];#售后组
?>
<?php $this->beginBlock('content') ?>
<link rel="stylesheet" href="/css/layui.css"  media="all">
<style type="text/css">
	body {
		background-color: #e4e6e9;
		min-height: 100%;
		padding-bottom: 0;
		font-family: 'Open Sans';
		font-size: 13px;
		color: #393939;
	}
	*{margin:0;padding:0;}
	ul,li{list-style:none;margin:0;padding:0;}
	a{text-decoration:none;}
	a { color: #337ab7;}
	.clearfix{clear:both;}
	
	.info-title{height:40px;width:100%;line-height:50px;font-weight:bold;border-bottom:2px solid rgba(232,233,234,1);margin-bottom:10px;}
	.info-box{margin:20px 0px;padding:0 20px;margin-top:30px;}
	.info-box .customertable{border:none;}
	.info-box .customertable tr{border:none;}
	.info-box .customertable tr td{padding:4px;}
	.info-box .customertable tr:first-child td{border:none;}
	
	.info-box .visitor{padding:5px 10px;float:left;background:#438eb9;border-radius:29px;-webkit-border-radius:29px;color:#fff;margin-right:15px;}
	.info-box .visitor div{float:left;text-align:center;}
	.info-box .visitor .visit_name{margin-right:5px;}
	.info-box .visitor .visit_count{margin-left:5px;}
	
	.table tr td input{height:30px;line-height:30px;}
	.table tr td .fact_price{width:80px;}
	.table tr td .trade_num{width:40px;}
	
	.updatebox{margin-bottom:10px;}
	.updatebox .total-box{float:left;display:inline-block;margin-right:30px;}
	.updatebox .total-box div{float:left;height:35px;line-height:35px;display:block;}
	.updatebox .total-box input{float:left;height:35px;line-height:35px;}
	.updatebox .total-box .tips{color:#f00;width:100%;}
	.main_price_total, .main_price_fact, .main_pay_money, .main_pay_remark{margin-right:20px;font-size:20px;line-height:30px!important;}
	.main_price_total{color:#2b7dbc;}
	.main_price_fact{color:rgba(221, 55, 81, 1)}
	
	.money_green{color:#00CC00!important}
	.money_red{color:red!important}
	
	.verify_btn, .refund_btn{display:block;padding:10px 10px;text-align:center;float:right;margin-right:20px;}
	.verify_btn:link ,.refund_btn:link{color: #fff;}
	.verify_btn:visited ,.refund_btn:visited{color: #fff;}
	.verify_btn:hover ,.refund_btn:hover{color: #fff;text-decoration:none;}
	.verify_btn:active ,.refund_btn:active{color: #fff;}
	.verify_success{background:#82af6f;color:#fff;}
	.verify_fail{background:#d15b47;color:#fff;}
	
	.verify_result_success{color:#82af6f!important;}
	.verify_result_fail{color:#d15b47!important;}
	
	.idle_server{background:#82af6f;color:#fff;padding:3px 5px;margin-right:10px;}
	.supplier_server{background:#f89406;color:#fff;padding:3px 5px;margin-right:10px;}
	
	.float_table{border:none;}
	.float_table tr td{height:40px;line-height:40px!important;}
	.float_table tr td:first-child{text-align:right;width:150px;}
	.float_table tr td textarea{line-height:24px;}
	
	.config_item div{display:inline-block;}
	.config_item .config_title{width:90px;margin-right:10px;}
	.config_item .config_content{}
	.refund_btn{float:left!important;}
	
	.title_tr{background:#f5f5f5;font-weight:bold;}
</style>
<div class="clearfix">
	<div class="pull-right tableTools-container"></div>
</div>

<?php 
	$adminRole = Yii::$app->session['auth']['roles'];
	$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
?>

<div id="mainContainer">
	<div class="info-title">基本信息</div>
	<div class="info-box">
		<table class="table customertable">
			<tr>
				<td>流程编号</td>
				<td>流程类型</td>
				<td>订单号</td>
				<td>订单金额</td>
				<td>用户昵称</td>
				<td>销售名称</td>
				<td>财务审核 / 审核人</td>
				<td>操作审核 / 审核人</td>
				<td>流程状态</td>
			</tr>
			<tr>
				<td><?php echo $FlowRes['flow_id']?></td>
				<td><?php echo $FlowRes['flow_name']?></td>
				<td><a class="blue" href="<?php echo Url::to(['trade-manage/trade-detail', 'trade_orderid' => $FlowRes['flow_orderid']])?>" ><?php echo $FlowRes['flow_orderid'];?></a></td>
				<td><?php echo $FlowRes['flow_total_money']?></td>
				<td><?php echo $FlowRes['flow_username'];?></td>
				<td><?php echo $FlowRes['flow_admin_name'];?></td>
				<td><?php echo $FlowRes['flow_money_auditor_name'] ? $FlowRes['flow_money_audit_status'].' / '.$FlowRes['flow_money_auditor_name'] : $FlowRes['flow_money_audit_status'].' / --'?></td>
				<td><?php echo $FlowRes['flow_operate_auditor_name'] ? $FlowRes['flow_operate_audit_status'].' / '.$FlowRes['flow_operate_auditor_name'] : $FlowRes['flow_operate_audit_status'].' / --'?></td>
				<td><?php echo $FlowRes['flow_status'];?></td>
			</tr>		
		</table>
	</div>
	<div class="info-title">查看记录</div>
	<div class="info-box">
		<?php $visit_count = json_decode($FlowRes['flow_lookcount'], true);?>
		<?php foreach($visit_count as $key => $val):?>
			<div class="visitor"><div class="visit_name"><?php echo $val['admin_name']?></div><div>/</div><div class="visit_count"><?php echo $val['admin_count'];?>次</div></div>
		<?php endforeach;?>
		
		<div class="clearfix"></div>
	</div>
	
	<?php if(!DataHelper::search_array($adminRole, $technical_group)):?>
	<?php $config = json_decode($FlowDetail['flow_after_config'], true);?>
	<div class="info-title">财务审核</div>
	<div class="info-box">
		<table class="table money-item table-bordered">
			
			<?php if($OrderRes):?>
				<tr class="title_tr">
					<td>标准总金额</td>
					<td>折后总金额</td>
					<td>实付总金额</td>
					<td>改价备注</td>
					<td colspan="2">支付方式</td>
				</tr>
				
				<tr>
					<td><?php echo $OrderRes['trade_price_total']?></td>
					<td>
						<?php echo $OrderRes['trade_price_fact']?>
						<br/>与标准金额差价：<?php echo sprintf("%.2f", $OrderRes['trade_price_fact'] - $OrderRes['trade_price_total']) >= 0 ? '<span class="money_green">'.sprintf("%.2f", $OrderRes['trade_price_fact'] - $OrderRes['trade_price_total']).'</span>' : '<span class="money_red">'.sprintf("%.2f", $OrderRes['trade_price_fact'] - $OrderRes['trade_price_total']).'</span>';?>
					</td>
					<td>
						<?php echo $OrderRes['trade_pay_money']?>
						<br/>与标准金额差价：<?php echo sprintf("%.2f", $OrderRes['trade_pay_money'] - $OrderRes['trade_price_total']) >= 0 ? '<span class="money_green">'.sprintf("%.2f", $OrderRes['trade_pay_money'] - $OrderRes['trade_price_total']).'</span>' : '<span class="money_red">'.sprintf("%.2f", $OrderRes['trade_pay_money'] - $OrderRes['trade_price_total']).'</span>';?>
						<br/>与折后金额差价：<?php echo sprintf("%.2f", $OrderRes['trade_pay_money'] - $OrderRes['trade_price_fact']) >= 0 ? '<span class="money_green">'.sprintf("%.2f", $OrderRes['trade_pay_money'] - $OrderRes['trade_price_fact']).'</span>' : '<span class="money_red">'.sprintf("%.2f", $OrderRes['trade_pay_money'] - $OrderRes['trade_price_fact']).'</span>';?>
					</td>
					<td><?php echo $OrderRes['trade_remark']?></td>
					<td colspan="2"><?php echo $FlowRes['flow_name']?></td>
				</tr>
			<?php endif;?>
			<!--支付方式End-->
			
			<tr class="title_tr">
				<td>产品付费周期</td>
				<td>变更前续费价格</td>
				<td>变更后续费价格</td>
				<td>变更前配件成本</td>
				<td>变更后配件成本</td>
				<td>成本货币类型</td>
			</tr>
			<tr>
				<td><?php 
						if($config['payment_cycle'] == 1){
							echo "月付";
						} else if($config['payment_cycle'] == 3){
							echo "季度付";
						} else if($config['payment_cycle'] == 6){
							echo "半年付";
						} else if($config['payment_cycle'] == 12){
							echo "年付";
						}				
					?>
				</td>
				<td><?php echo $config['old_sell_price']?></td>
				<td><?php echo $config['normal_sell_price']?></td>
				<td><?php echo $config['old_upgrade_cost_price']?></td>
				<td><?php echo $config['normal_upgrade_cost_price']?></td>
				<td></td>
			</tr>
			
			
			<!-- 变更配置-线下打款, 变更配置-在线支付-->
			<?php if(in_array($FlowRes['flow_name'], ['变更配置-线下打款','变更配置-在线支付'])):?> 
				<?php if($FlowRes['flow_money_info']):?>
					<?php $moneyinfo = json_decode($FlowRes['flow_money_info'], true);?>
					<tr class="title_tr">
						<td colspan="6">线下支付 / 三方支付信息 &nbsp;&nbsp;&nbsp;&nbsp;<span style="color:red">注：若用户打款金额超过订单价格，超出部分在审核完成后，会进入用户账户余额</span></td>
					</tr>
					<?php if($FlowRes['flow_name'] == '变更配置-在线支付'):?>
					<tr>
						<td>支付金额</td>
						<td>支付回执单号</td>
						<td>三方平台</td>
						<td>支付时间</td>
						<td colspan="2">支付备注</td>
					</tr>
					<tr>
						<td><?php echo $moneyinfo['money_num'];?></td>
						<td><?php echo $moneyinfo['money_stream_number'];?></td>
						<td><?php echo $moneyinfo['money_platform'];?></td>						
						<td><?php echo $moneyinfo['money_time'];?></td>
						<td colspan="2"><?php echo $moneyinfo['money_remark'];?></td>
					</tr>
					<?php else:?>
					<tr>
						<td>支付金额</td>
						<td>支付的账户</td>
						<td>支付银行</td>
						<td>支付回执单号</td>
						<td>支付时间</td>
						<td>支付备注</td>
					</tr>
					<tr>
						<td><?php echo $moneyinfo['money_num'];?></td>
						<td><?php echo $moneyinfo['money_account'];?></td>
						<td><?php echo $moneyinfo['money_platform'];?></td>
						<td><?php echo $moneyinfo['money_stream_number'];?></td>
						<td><?php echo $moneyinfo['money_time'];?></td>
						<td colspan="2"><?php echo $moneyinfo['money_remark'];?></td>
					</tr>
					<?php endif;?>
				<?php endif;?>
			<?php endif;?>
			<!--支付信息END -->
			
			<?php if(in_array($FlowRes['flow_name'], ['变更配置-余额退款'])):?> 
				<?php if($FlowRes['flow_money_info']):?>
					<?php $moneyinfo = json_decode($FlowRes['flow_money_info'], true);?>
					<tr class="title_tr">
						<td colspan="6">余额退款 &nbsp;&nbsp;&nbsp;&nbsp;<span style="color:red">注：此次操作将返还金额于用户账户余额中</span></td>
					</tr>
					<tr>
						<td>返还金额</td>
						<td>支付的账户</td>
						<td>支付银行</td>
						<td>支付回执单号</td>
						<td>支付时间</td>
						<td>支付备注</td>
					</tr>
					<tr>
						<td><?php echo $moneyinfo['money_num'];?></td>
						<td><?php echo $moneyinfo['money_account'];?></td>
						<td><?php echo $moneyinfo['money_platform'];?></td>
						<td><?php echo $moneyinfo['money_stream_number'];?></td>
						<td><?php echo $moneyinfo['money_time'];?></td>
						<td colspan="2"><?php echo $moneyinfo['money_remark'];?></td>
					</tr>
				<?php endif;?>
			<?php endif;?>
			
			<!-- -->
			<?php if(in_array($FlowRes['flow_name'], ['变更配置-后付款'])):?>
				<tr class="title_tr">
					<td colspan="6">当前订单为<span style="color:red">后付款单</span>，等待补款后进行审核金额</td>
				</tr>
			<?php else:?>
			<!-- -->			
				<?php if($FlowRes['flow_money_audit_status'] != '无需审核'):?>
					<?php if($FlowRes['flow_money_audit_status'] == '等待审核'):?>				
						<?php if(DataHelper::search_array($adminRole, explode(',', $FlowRes['flowrole']['wr_money_verify_group']))):?>
							<tr>
								<?php if(in_array($FlowRes['flow_name'], '变更配置-无金额')):?>
									<td colspan="6"><a href="javascript:;" class="verify_btn money_success verify_success">已知晓</a><a href="javascript:;" class="verify_btn money_fail verify_fail">驳回申请</a></td>
								<?php else:?>
									<td colspan="6"><a href="javascript:;" class="verify_btn money_success verify_success">通过审核</a><a href="javascript:;" class="verify_btn money_fail verify_fail">驳回申请</a></td>
								<?php endif?>
								
							</tr>
						<?php endif;?>
						
					<?php elseif($FlowRes['flow_money_audit_status'] == '通过'):?>
						<?php if(in_array($FlowRes['flow_status'], ['处理驳回', '中止事务中'])):?>
							<!-- 金额状态为通过，但是操作驳回，那么则需要退款 -->
							<?php if(in_array($FlowRes['flow_name'], ['变更配置-在线支付','变更配置-余额支付'])):?>
								<?php $flow_money_info = json_decode($FlowRes['flow_money_info'], true);?>
								<?php if(!isset($flow_money_info['stop_refund'])):?>
									<tr>
										<td colspan="6">
											当前流程支付方式为 <<?php echo explode('-', $FlowRes['flow_name'])[1]?>> 需要进行退款，请填写退款信息<br/><br/>
											<a href="javascript:;" class="verify_fail refund_btn">填写退款信息</a>
										</td>
									</tr>
								<?php else:?>
									<?php if($flow_money_info['stop_refund']['type'] == 'balance'):?>
										<tr class="title_tr">
											<td colspan="6">已选择退款方式为：退款到账户余额</td>
										</tr>
										<tr>
											<td colspan="2">退款金额</td>
											<td colspan="2">退款时间</td>
											<td colspan="2">退款备注</td>
										</tr>
										<tr>
											<td colspan="2"><?php echo $flow_money_info['stop_refund']['money']?></td>
											<td colspan="2"><?php echo date("Y-m-d H:i:s", $flow_money_info['stop_refund']['time'])?></td>
											<td colspan="2"><?php echo $flow_money_info['stop_refund']['remark']?></td>
										</tr>
									<?php else:?>
										<tr class="title_tr">
											<td colspan="6">已选择退款方式为：通过平台打款</td>
										</tr>
										<tr>
											<td>退款金额</td>
											<td>退款账户</td>
											<td>退款银行</td>
											<td>退款时间</td>
											<td>退款回执单号</td>
											<td>退款备注</td>
										</tr>
										<tr>
											<td><?php echo $flow_money_info['stop_refund']['money']?></td>
											<td><?php echo $flow_money_info['stop_refund']['account']?></td>
											<td><?php echo $flow_money_info['stop_refund']['platform']?></td>
											<td><?php echo date("Y-m-d H:i:s", $flow_money_info['stop_refund']['time'])?></td>
											<td><?php echo $flow_money_info['stop_refund']['proof']?></td>
											<td><?php echo $flow_money_info['stop_refund']['remark']?></td>
										</tr>
									<?php endif;?>
								<?php endif;?>
							<?php else:?>
								<tr>
									<td colspan="6">当前流程支付方式为 <无金额> 无需进行退款操作</td>
								</tr>
							<?php endif;?>
						<?php else:?>
							<tr>
								<td colspan="6">
									审核结果：<span class="verify_result_success">审核通过</span>
									<br/>
									审核时间：<?php echo date("Y-m-d H:i", $FlowRes['flow_money_auditor_time'])?>
									<br/>
									审核人：<?php echo $FlowRes['flow_money_auditor_name'];?>
									<br/>
									审核理由：<?php echo $FlowRes['flow_money_auditor_reason']?>
								</td>
							</tr>
						<?php endif;?>
					<?php else:?>
						<tr>
							<td colspan="6">
								审核结果：<span class="verify_result_fail">审核驳回</span>
								<br/>
								审核时间：<?php echo date("Y-m-d H:i", $FlowRes['flow_money_auditor_time'])?>
								<br/>
								审核人：<?php echo $FlowRes['flow_money_auditor_name'];?>
								<br/>
								审核理由：<?php echo $FlowRes['flow_money_auditor_reason']?>
							</td>
						</tr>
					<?php endif?>
				
				<?php else:?>
					<?php if(in_array($FlowRes['flow_status'], ['处理驳回', '中止事务中', '中止事务完成'])):?>
						<!-- 金额状态为通过，但是操作驳回，那么则需要退款 -->
						<?php if(in_array($FlowRes['flow_name'], ['变更配置-在线支付','变更配置-余额支付'])):?>
							<?php $flow_money_info = json_decode($FlowRes['flow_money_info'], true);?>
							<?php if(!isset($flow_money_info['stop_refund'])):?>
								<tr>
									<td colspan="6">
										当前流程支付方式为 <<?php echo explode('-', $FlowRes['flow_name'])[1]?>> 需要进行退款，请填写退款信息，退款会在 <span style="color:red">流程全部完成后</span> 进行<br/><br/>
										<a href="javascript:;" class="verify_fail refund_btn">填写退款信息</a>
									</td>
								</tr>
							<?php else:?>
								<?php if($flow_money_info['stop_refund']['type'] == 'balance'):?>
									<tr>
										<td colspan="6">已选择退款方式为：退款到账户余额</td>
									</tr>
									<tr>
										<td colspan="2">退款金额</td>
										<td colspan="2">退款时间</td>
										<td colspan="2">退款备注</td>
									</tr>
									<tr>
										<td colspan="2"><?php echo $flow_money_info['stop_refund']['money']?></td>
										<td colspan="2"><?php echo date("Y-m-d H:i:s", $flow_money_info['stop_refund']['time'])?></td>
										<td colspan="2"><?php echo $flow_money_info['stop_refund']['remark']?></td>
									</tr>
								<?php else:?>
									<tr>
										<td colspan="6">已选择退款方式为：通过平台打款</td>
									</tr>
									<tr>
										<td>退款金额</td>
										<td>退款账户</td>
										<td>退款银行</td>
										<td>退款时间</td>
										<td>退款回执单号</td>
										<td>退款备注</td>
									</tr>
									<tr>
										<td><?php echo $flow_money_info['stop_refund']['money']?></td>
										<td><?php echo $flow_money_info['stop_refund']['account']?></td>
										<td><?php echo $flow_money_info['stop_refund']['platform']?></td>
										<td><?php echo date("Y-m-d H:i:s", $flow_money_info['stop_refund']['time'])?></td>
										<td><?php echo $flow_money_info['stop_refund']['proof']?></td>
										<td><?php echo $flow_money_info['stop_refund']['remark']?></td>
									</tr>
								<?php endif;?>
							<?php endif;?>
						<?php endif;?>
					<?php endif;?>
				<?php endif;?>	
			<?php endif;?>	
		</table>
		<div class="clearfix"></div>
	</div>

	<?php endif;?>
	
	<?php $config = json_decode($FlowDetail['flow_after_config'], true);?>
	<?php $frontconfig = json_decode($FlowDetail['flow_front_config'], true);?>
	<div class="info-title">技术处理</div>
	<div class="info-box">
		<table class="table table-bordered">
			<tr class="title_tr">
				<td style="width:150px;">配置提供方</td>
				<td>当前配置详情</td>
				<td>变更后配置详情</td>
				<td>当前配置IP</td>
				<?php if($config['isneed_replaceip'] == 1):?>
				<td>
					变更后配置IP 
					<?php if($config['isneed_replaceip'] == 1):?>
						<br/><span style="color:red">(变更配置同时更换IP)</span>
					<?php endif;?>
				</td>
				<?php endif;?>
				<td style="width:200px;">处理人 / 处理时间</td>
				<td>处理结果</td>
				<td>操作</td>
			</tr>
			
			<tr>
				<td>
					<?php 
						$html = '';
						$server_name = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$config['server_typeid']."'")->queryScalar();						
						if(isset($config['idle_id']) && $config['idle_id'] != '') {
							#自有服务器
							$html .= '<div class="idle_server">自有</div>';
						} else if(isset($config['provider_id']) && $config['provider_id'] != '') {
							$provider = Yii::$app->db->createCommand("select name from provider where id = '".$config['provider_id']."'")->queryScalar();
							$html .= '<div class="supplier_server">供应商 / '.$provider.'</div>';
						}
						
						$html .= '<br/><br/>'.$server_name.'<br/>';
						echo $html;
					?>
				</td>
				<td>
					<?php 
						if( !isset($frontconfig['pdt_id']) ) {
							$frontconfig['pdt_id'] = $MemberPdtRes['pdt_id'];
						}
						$pdt_name = Yii::$app->db->createCommand("select name from pdt_manage where id='".$frontconfig['pdt_id']."'")->queryScalar();
					?>
					<div class="config_item"><div class="config_title">产品配置类别</div><div class="config_content"><?php echo $pdt_name?></div></div>
					<div class="config_item"><div class="config_title">CPU</div><div class="config_content"><?php echo $frontconfig['config']['cpu']?></div></div>
					<div class="config_item"><div class="config_title">内存</div><div class="config_content"><?php echo $frontconfig['config']['ram']?></div></div>
					<div class="config_item"><div class="config_title">硬盘</div><div class="config_content"><?php echo $frontconfig['config']['hdd']?></div></div>
					<div class="config_item"><div class="config_title">实际带宽</div><div class="config_content"><?php echo $frontconfig['config']['configbandwidth']?></div></div>
					<div class="config_item"><div class="config_title">要求带宽</div><div class="config_content"><?php echo $frontconfig['config']['requirement_bandwidth']?></div></div>
					<div class="config_item"><div class="config_title">IP数</div><div class="config_content"><?php echo $frontconfig['config']['ipnumber']?></div></div>
					<div class="config_item"><div class="config_title">防御流量</div><div class="config_content"><?php echo $frontconfig['config']['defense']?></div></div>
					<div class="config_item"><div class="config_title">操作系统</div><div class="config_content"><?php echo $frontconfig['config']['operatsystem']?></div></div>
					
				</td>
				<td>
					<?php 
						$pdt_name = Yii::$app->db->createCommand("select name from pdt_manage where id='".$config['pdt_id']."'")->queryScalar();
					?>
					<div class="config_item"><div class="config_title">产品配置类别</div><div class="config_content"><?php echo $pdt_name?></div></div>
					<div class="config_item"><div class="config_title">CPU</div><div class="config_content"><?php echo $config['config']['cpu']?></div></div>
					<div class="config_item"><div class="config_title">内存</div><div class="config_content"><?php echo $config['config']['ram']?></div></div>
					<div class="config_item"><div class="config_title">硬盘</div><div class="config_content"><?php echo $config['config']['hdd']?></div></div>
					<div class="config_item"><div class="config_title">实际带宽</div><div class="config_content"><?php echo $config['config']['configbandwidth']?></div></div>
					<div class="config_item"><div class="config_title">要求带宽</div><div class="config_content"><?php echo $config['config']['requirement_bandwidth']?></div></div>
					<div class="config_item"><div class="config_title">IP数</div><div class="config_content"><?php echo $config['config']['ipnumber']?></div></div>
					<div class="config_item"><div class="config_title">防御流量</div><div class="config_content"><?php echo $config['config']['defense']?></div></div>
					<div class="config_item"><div class="config_title">操作系统</div><div class="config_content"><?php echo $config['config']['operatsystem']?></div></div>					
				</td>
				<td>
					<?php if ($frontconfig['ip'] == null):?>
						
					<?php else: ?>                      							     
						<?php foreach ($frontconfig['ip'] as $key=>$val2):?>
							<?php if ($key == "10"):?>
							<?php break;?>
							<?php endif;?>
							<?php echo $val2."<br/>";?>
						<?php endforeach;?>
						<?php if (count($frontconfig['ip']) > 10):?><br/><a href="javascript:;" class="lookallip">查看全部IP</a><?php endif;?>
					<?php endif;?>
					<input type="hidden" class="allipbox" value="<?php echo implode(',', $frontconfig['ip']);?>"/>
					
				</td>
				<?php if($config['isneed_replaceip'] == 1):?>
				<td>
					<?php if ($config['ip'] == null):?>
						<span style="color:red">暂未确定更换后IP</span>
					<?php else: ?>                      							     
						<?php foreach ($config['ip'] as $key=>$val2):?>
							<?php if ($key == "10"):?>
							<?php break;?>
							<?php endif;?>
							<?php echo $val2."<br/>";?>
						<?php endforeach;?>
						<?php if (count($config['ip']) > 10):?><br/><a href="javascript:;" class="lookallip">查看全部IP</a><?php endif;?>
					<?php endif;?>
					<input type="hidden" class="allipbox" value="<?php echo implode(',', $config['ip']);?>"/>
				</td>
				<?php endif;?>
				
				<td>
					<?php if($FlowDetail['flow_fix_id']):?>
						<?php if($FlowDetail['flow_fix_finish_time']) {
							echo '处理人：'.$FlowDetail['flow_fix_name'].'<br/>接手时间：'.date("Y-m-d H:i", $FlowDetail['flow_fix_confirm_time']).'<br/>完成时间：'.date("Y-m-d H:i", $FlowDetail['flow_fix_finish_time']).'<br/>';
							$timeformat = $FlowDetail['flow_fix_finish_time'] - $FlowDetail['flow_fix_confirm_time'];
							if($timeformat < 60) {
								echo '处理耗时：'.$timeformat.'秒';
							} else if($timeformat < 3600) {
								$min = intval($timeformat / 60);
								$second = $timeformat - ($min * 60);
								echo '处理耗时：'.$min.'分'.$second.'秒';
							} else {
								$hour = intval($timeformat / 3600);
								$min = intval(($timeformat - ($hour * 3600)) / 60);
								$second = intval($timeformat - ($hour * 3600) - ($min * 60));
								echo '处理耗时：'.$hour.'小时'.$min.'分'.$second.'秒';
							}
						} else {
							echo $FlowDetail['flow_fix_name'].' 正在处理中...<br/>接手时间：'.date("Y-m-d H:i", $FlowDetail['flow_fix_confirm_time']);
							
						}?>
					<?php else:?>
						等待技术确认
					<?php endif;?>
				</td>
				<td>
					<?php if($FlowDetail['flow_fix_id']):?>
						<?php if($FlowDetail['flow_fix_finish_time']):?>
							<?php $config = json_decode($FlowDetail['flow_after_config'], true);?>
							<?php 
								echo $config['complete_remark'] ? $config['complete_remark'] : '已完成配置变更';
							?>
						<?php endif;?>
					<?php else:?>
						等待技术确认
					<?php endif;?>
				</td>
				<td>
					<?php if($config['addition'] == 1):?>
						<span class="supplier_server">变更配置补录</span>
					<?php endif;?>
					
					<?php if(DataHelper::search_array($adminRole, $aftersale_group)):?>
						<?php if($FlowRes['flow_status'] == '事务处理中'):?>
							
							<?php if($FlowRes['flow_operate_audit_status'] != '无需审核'):?>
							
								<?php if($FlowRes['flow_operate_audit_status'] == '通过'):?>
									<?php if($FlowDetail['flow_fix_id']):?>
										<?php if($adminId == $FlowDetail['flow_fix_id']):?>
											
											<?php if($config['isneed_replaceip'] == 1):?>
											
												<?php if ($config['ip'] == null):?>											 
													<a href="javascript:;" class="setting_change_ip" data-id="<?php echo $FlowDetail['id'];?>">确定变更后IP</a>&nbsp;
												<?php else:?>
													<?php if(!$FlowDetail['flow_fix_finish_time']):?>
														<a href="javascript:;" class="setting_change_ip" data-id="<?php echo $FlowDetail['id'];?>">重新确定变更后IP</a>&nbsp;
														<a href="javascript:;" class="edit_complete_info" data-id="<?php echo $FlowDetail['id'];?>">处理完成</a>
													<?php endif;?>
												<?php endif;?>
												
											<?php else:?>
												<?php if(!$FlowDetail['flow_fix_finish_time']):?>
													<a href="javascript:;" class="edit_complete_info" data-id="<?php echo $FlowDetail['id'];?>">处理完成</a>
												<?php endif;?>
											<?php endif;?>
											
											
											
										<?php endif;?>
									<?php else:?>
										<a href="javascript:;" class="confirm_fix" data-id="<?php echo $FlowDetail['id'];?>">接手处理</a>
										&nbsp;&nbsp;
									<?php endif?>
									
								<?php endif;?>
								
							<?php else:?>
							
								<?php if($FlowDetail['flow_fix_id']):?>
									<?php if($adminId == $FlowDetail['flow_fix_id']):?>
									
										<?php if($config['isneed_replaceip'] == 1):?>
											
											<?php if ($config['ip'] == null):?>											 
												<a href="javascript:;" class="setting_change_ip" data-id="<?php echo $FlowDetail['id'];?>">确定变更后IP</a>&nbsp;
											<?php else:?>
												<?php if(!$FlowDetail['flow_fix_finish_time']):?>
													<a href="javascript:;" class="setting_change_ip" data-id="<?php echo $FlowDetail['id'];?>">重新确定变更后IP</a>&nbsp;
													<a href="javascript:;" class="edit_complete_info" data-id="<?php echo $FlowDetail['id'];?>">处理完成</a>
												<?php endif;?>
											<?php endif;?>
											
										<?php else:?>
											<?php if(!$FlowDetail['flow_fix_finish_time']):?>
												<a href="javascript:;" class="edit_complete_info" data-id="<?php echo $FlowDetail['id'];?>">处理完成</a>
											<?php endif;?>											
										<?php endif;?>
										
									<?php else:?>
										<?php if(!$FlowDetail['flow_fix_finish_time']):?>
											<?php echo $FlowDetail['flow_fix_name'].' 正在处理中...'?>
										<?php endif;?>										
									<?php endif;?>
									
									&nbsp;&nbsp;
								<?php else:?>
									<a href="javascript:;" class="confirm_fix" data-id="<?php echo $FlowDetail['id'];?>">接手处理</a>
									&nbsp;&nbsp;
								<?php endif?>
								
							<?php endif;?>
							
						<?php elseif($FlowRes['flow_status'] == '中止事务中'):?>
							
							<?php if($FlowDetail['flow_fix_confirm_time']):?>
								<?php if(isset($config['is_recovery'])):?>
									机器已经恢复原配置
								<?php else:?>
									<a href="javascript:;" class="restore_machine_configuration" data-id="<?php echo $FlowDetail['id'];?>">恢复机器原始配置完毕</a>
								<?php endif;?>
							<?php else:?>
								技术还未配置
							<?php endif;?>
						
						<?php elseif(in_array($FlowRes['flow_status'], ['处理驳回'])):?>
							
							<?php if($FlowDetail['flow_fix_confirm_time']):?>
								<?php if(isset($config['is_recovery'])):?>
									机器已经恢复原配置
								<?php else:?>
									<a href="javascript:;" class="restore_machine_configuration" data-id="<?php echo $FlowDetail['id'];?>">恢复机器原始配置完毕</a>
								<?php endif;?>
							<?php else:?>
								技术还未配置
							<?php endif;?>
						
						<?php endif;?>
						
					<?php endif;?>
					
				</td>
			</tr>


			<?php if($FlowRes['flow_status'] == '事务处理中'):?>
				<?php if($FlowRes['flow_operate_audit_status'] != '无需审核'):?>
					<?php if($FlowRes['flow_operate_audit_status'] == '等待审核'):?>
					
						<?php if(DataHelper::search_array($adminRole, explode(',', $FlowRes['flowrole']['wr_handle_verify_group']))):?>
							<tr>
								<td colspan="8"><a href="javascript:;" class="verify_btn handle_success verify_success">审核通过</a><a href="javascript:;" class="verify_btn handle_fail verify_fail">审核驳回</a></td>
							</tr>
						<?php endif;?>
					<?php else:?>
						<tr>
							<td colspan="8">
								<?php if($FlowRes['flow_operate_audit_status'] == '通过'):?>
									审核结果：<span class="verify_result_success">审核通过</span>
								<?php else:?>
									审核结果：<span class="verify_result_fail">审核驳回</span>
								<?php endif?>
								<br/>
								审核时间：<?php echo date("Y-m-d H:i", $FlowRes['flow_operate_auditor_time'])?>
								<br/>
								审核人：<?php echo $FlowRes['flow_operate_auditor_name'];?>
								<br/>
								审核理由：<?php echo $FlowRes['flow_operate_auditor_reason']?>
							</td>
						</tr>
					<?php endif;?>
				<?php endif;?>
			<?php endif;?>
		</table>
	</div>
	
	<!--返回列表  返回前页-->
	<div class="row" style="float: right;margin-right: 30px;margin-bottom: 20px;">								
		<div class="pull-right">
			<button class="btn btn-warning btn-sm" style="width:120px" onclick="window.history.back()">
				<i class="ace-icon fa fa-reply icon-only"></i>&nbsp;返回上一页
			</button> 
			<button class="btn btn-info  btn-sm" style="width:150px" onclick="location.href='<?php echo Url::to(['waiting-list'])?>'">
				<i class="ace-icon fa fa-reply icon-only"></i>&nbsp;返回待处理列表
			</button> 
		</div>
	</div>
</div>

<!-- IP列表-->
<div id="LookIPlist" style="display:none;"></div>

<!-- 技术确认完成-->
<div id="CompleteInfo" style="display:none;">
	<table class="table float_table">
		<tr>
			<td>备注：</td>
			<td><textarea class="complete_remark" style="resize:none;height:150px;"></textarea></td>
		</tr>
	</table>
</div>

<!-- 成本-->
<div id="FillCost" style="display:none;">
	<table class="table float_table">
		<tr>
			<td>基础成本：</td>
			<td><input type="text" class="cost_base" value="<?php echo $MemberPdtRes['cost_price'];?>"></td>
		</tr>
		<tr>
			<td>配件成本：</td>
			<td><input type="text" class="cost_part" value="<?php echo $config['normal_upgrade_cost_price'];?>"></td>
		</tr>
		<tr>
			<td>成本货币：</td>
			<td>
				<select name="" class="cost_type">
					<option value="RMB" selected>人民币</option>
					<option value="USD">美元</option>
				</select>
			</td>
		</tr>
	</table>
</div>
<!-- 成本 弹框 end -->

<!--退款 弹框 -->
<div id="refundMoney" style="display:none;">
	<?php $moneyInfo = json_decode($FlowRes['flow_money_info'], true);?>
	<table class="table float_table selectRefundType">
		<tr>
			<td>退款方式：</td>
			<td>
				<select name="refund_type" class="refund_type" value="">
					<option value="balance" selected>退款到账户余额</option>
					<option value="platform">通过平台打款</option>
				</select>
			</td>
		</tr>
	</table>
	
	<table class="table float_table balanceInfoBox">
		<tr>
			<td>付款总金额：</td>
			<td><?php echo $moneyInfo['money_num'] ? $moneyInfo['money_num'] : $OrderRes['trade_pay_money']?></td>
		</tr>
		<tr>
			<td>退款金额：</td>
			<td><input type="text" class="refund_balance_money" value="<?php echo $moneyInfo['money_num'] ? $moneyInfo['money_num'] : $OrderRes['trade_pay_money']?>"></td>
		</tr>
		<tr>
			<td>退款备注：</td>
			<td><textarea class="refund_balance_remark" style="resize:none;height:150px;"></textarea></td>
		</tr>
	</table>
	
	<table class="table float_table refundInfoBox" style="display:none;">
		<tr>
			<td>退款平台：</td>
			<td><input type="text" class="refund_platform" value=""></td>
		</tr>
		<tr>
			<td>退款账户：</td>
			<td><input type="text" class="refund_account" value=""></td>
		</tr>
		<tr>
			<td>退款姓名：</td>
			<td><input type="text" class="refund_name" value=""></td>
		</tr>
		<tr>
			<td>退款金额：</td>
			<td><input type="text" class="refund_platform_money" value="<?php echo $moneyInfo['money_num'] ? $moneyInfo['money_num'] : $OrderRes['trade_pay_money']?>"></td>
		</tr>
		<tr>
			<td>退款时间：</td>
			<td><input type="text" class="refund_time" id="refund_time" placeholder="点击选择时间" readonly value=""></td>
		</tr>
		<tr>
			<td>退款凭据：</td>
			<td><input type="text" class="refund_proof" value=""></td>
		</tr>
		<tr>
			<td>退款备注：</td>
			<td><textarea class="refund_platform_remark" style="resize:none;height:150px;"></textarea></td>
		</tr>
	</table>
</div>
<!-- 退款 弹框 end -->
<!--变更配置 IP变化 -->
<style type="text/css">
	.form-horizontal {padding-left: 30px;}
	.font-size-12 {font-size: 12px;}
	.ny-form .form-group {margin-bottom: 12px;line-height: 30px;}
	.form-horizontal .form-group {margin-right: -15px;margin-left: -15px;}
	.ny-form .ny-control-label {float: left;}
	.ny-control-label {width: 160px;padding-right: 0;text-align: right;color: #808080;}
	.ny-form-control {width:100px;display: inline-block;white-space: nowrap;color: #555;background-color: #fff;background-image: none;outline: none;}    
	.ny-number-container {float: left;line-height: 1;}
	.number-input-box {width: 131px;}
	.number-input-box {float: left;position: relative;width: 100px;border-radius: 2px;}
	.alert-warn, .alert-error, .alert-success {padding: 7px 22px 5px 37px;background: url(img/uc/tip_icon_warn_16.png) no-repeat 10px 10px #fff3e5;
	border: 1px solid #ff8800;border-radius: 2px;color: #ff8800;font-size: 12px;line-height: 2em;}
	.margin-bottom-20 {margin-bottom: 20px;}
</style>
<div class="bootbox modal fade bootbox-prompt in" id="setting_change_ip_modal" tabindex="-1" role="dialog" style="padding-right: 17px;">
	<div class="modal-dialog" style="width: 40%;">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">×</font>
					</font>
				</button>
				<h4 class="modal-title">
				<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">变更配置 IP设置</font></font></h4>
			</div>
			<div class="modal-body maxModalHeight1" style="overflow: auto;max-height:800px">
				<div class="bootbox-body">
					<div class="alert-warn margin-bottom-20">
						如若要以IP段形式填写，请按照规范填写，如：127.0.0.1-2
					</div>
					<form id="modifybandwidthForm" action="" class="bootbox-form form-horizontal ny-form ny-tab-container font-size-12 tab-group-undefined">                			
						
						<div class="form-group">
							<div style="float: left;">服务器提供商：</div>
							<?php if( $MemberPdtRes['servicerprovider'] == 1):?>
								<div class="col-xs-8 ny-form-control">供应商提供</div>
							<?php else:?>
								<div class="col-xs-8 ny-form-control">自有</div>
							<?php endif;?>
						</div>
						<style type="text/css">
							.checkfont{font-size:0px;}
							.iplittlebox{float:left;display:inline-block;font-size:12px;margin-right:10px;margin-bottom:10px;}
							.iplittlebox input{height:31px;line-height:31px;}
						</style>						
						<div class="form-group" <?php if($MemberPdtRes['servicerprovider'] == 1):?> style="display:none"<?php endif;?> id= "ips_have_div">
							<div style="float: left;">IP地址：</div>
							<div id="InputsWrapper1" class="checkfont">
								<div class="iplittlebox" >
									<button type="button" id="IPlibraryselection" class="btn btn-xs btn-info btn-success"><i class="ace-icon glyphicon glyphicon-plus">&nbsp;IP库选择</i></button>
								</div>
								<div id="iplistji1" style="clear: both; margin-left: 2%;">
									<?php if($MemberPdtRes['servicerprovider'] == 0):?>
										<?php if(empty($config['ip'])):?>
										
											<?php if(!empty(json_decode($MemberPdtRes['ip']))):?>
												<?php foreach (json_decode($MemberPdtRes['ip']) as $value):?>
													<div class="iplittlebox"><input value="<?=$value?>" type="text" name="ips[]" readonly><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button></div>
												<?php endforeach;?>
											<?php endif;?>
											
										<?php else:?>
											<?php foreach ($config['ip'] as $value):?>
												<div class="iplittlebox"><input value="<?=$value?>" type="text" name="ips[]" readonly><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button></div>
											<?php endforeach;?>											
										<?php endif;?>
										
									<?php endif;?>
							   </div>
							</div>
						</div>
						
						<!-- 供应商IP -->
						<div class="form-group" <?php if($MemberPdtRes['servicerprovider'] == 0):?> style="display:none"<?php endif;?> id= "ips_provider_div">
							<div style="float: left;">IP地址：</div>
							<div id="InputsWrapper" class="checkfont">
								<div class="iplittlebox" >       		
									<button type="button" id="AddMoreFileBox" class="btn btn-xs btn-info btn-success" ><i class="ace-icon glyphicon glyphicon-plus"></i>&nbsp;新 加</button>
								</div>
								<div id="iplistji" style="clear: both; margin-left: 2%;">
								<?php if($MemberPdtRes['servicerprovider'] == 1):?>
									<?php if(empty($config['ip'])):?>
									
										<?php if(!empty(json_decode($MemberPdtRes['ip']))):?>
											<?php foreach (json_decode($MemberPdtRes['ip']) as $value):?>
												<div class="iplittlebox"><input value="<?=$value?>" type="text" name="ips[]"><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button></div>
											<?php endforeach;?>
										<?php endif;?>
										
									<?php else:?>	
										<?php foreach ($config['ip'] as $value):?>
											<div class="iplittlebox"><input value="<?=$value?>" type="text" name="ips[]"><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button></div>
										<?php endforeach;?>
									<?php endif;?>
									
								<?php endif;?>
								</div>            	
							</div>
						</div>
		
					</form>
				</div>
			</div>
			<div class="modal-footer">
				<button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
					<font style="vertical-align: inherit;">
						<font style="vertical-align: inherit;">取消</font></font>
				</button>
				<button data-bb-handler="reset" data-last="Finish" type="button" class="btn btn btn-warning" id="ip_reset">
					<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">重置</font></font>
				</button>
				<button data-bb-handler="confirm" data-last="Finish" type="button" class="btn btn-primary" id="ip_setting_confirm">
					<font style="vertical-align: inherit;"><font style="vertical-align: inherit;">确定</font></font>
				</button>
			</div>
		</div>
	</div>
 </div>
<!-- 变更配置 IP变化 end -->

<!--IP库 Modal -->
<style type="text/css">
	.changeSelectModel{}
	.selectmodelbox{margin-left:20px;margin-right:20px;border-bottom:1px #ccc solid;padding-bottom:10px;padding-top:10px;}
	.selectmodle{display:inline-block;padding:0 20px;height:40px;line-height:40px;text-align:center;cursor:pointer;border:1px #ccc solid;}
	.selectmodelbox .active{background:#6fb3e0 !important;color:#fff;border:1px #6fb3e0 solid;}
	.result_title{height:40px;padding:10px 0;margin-top:20px;}
	.ipresult table{border-collapse:collapse;width:80%;margin-bottom:40px;}
	.ipresult table td{height:30px;padding:0 5px;text-align:center;line-height:30px;border:1px #ccc solid;}
	.ipresult table td:nth-child(1){width:120px;}
	.ipresult table td:nth-child(2){text-align:left;}
	.setiplist{margin:20px auto;}
	.setiplist li{display:inline-block;height:30px;padding:0 10px;border:1px #ccc solid;text-align:center;line-height:30px;margin-right:5px;margin-bottom:5px;}
</style>
<div id="IPlibraryselectionshow" class="modal">
	<div class="modal-dialog" style="min-width: 1200px;">
		<div class="modal-content">
			<div id="modal-wizard-container">	
				<div class="modal-header">					
					<h4 class="widget-title">IP库列表：</h4>					
				</div>
				<div class="selectmodelbox">
					<div class="changeTitleSingle selectmodle active">非连贯IP添加</div>
					<div class="changeTitleMore selectmodle">添加IP段</div>
				</div>
				<div class="changeSelectModelSingle">
					<div class="row" style="">
						<div class="col-xs-12" style="margin-left:20px;margin-top: 10px;">    		     		
							
							<div style="float: left;margin-right: 6px;">   
								<select class="form-control" name='class_id' id="class_id" >
									<option value="">所属IP分类</option>
									<?php foreach ($IpClassList as $value): ?>
									<option value="<?php echo $value['class_id'] ?>" rel="<?php echo $value['class_name'] ?>"   
									<?php if ($value['class_id'] == Html::encode(Yii::$app->controller->get('class_id')) ): ?>
									 selected='selected'
									 <?php endif ?>	
									><?php echo $value['class_name'] ?></option>
									<?php endforeach ?>
								</select>
							</div>
							<div style="float: left;margin-right: 6px;">   
								<select class="form-control" name='network_gateway' id="network_gateway" >
									<option value="">所属IP网段</option>
									<?php foreach ($IpNetworkList as $value): ?>
									<option value="<?php echo $value['gateway'] ?>" rel="<?php echo $value['network'] ?>"   
									<?php if ($value['gateway'] == Html::encode(Yii::$app->controller->get('network_gateway')) ): ?>
									 selected='selected'
									 <?php endif ?>	
									><?php echo $value['network'] ?></option>
									<?php endforeach ?>
								</select>
							</div>
							<div style="float: left;margin-right: 6px;">   
								<input type="text" class="form-control search-query" name="ifame_select_ip" value='<?php echo Html::encode(Yii::$app->controller->get('ip')) ?>'  placeholder="IP">
							</div>
							
							<div>
								<button type="submit" id="ifame_select_ip_submit" class="btn btn-white btn-primary" style="margin-left:15px">
									<span class="fa fa-search"></span>
									搜索
								</button>        	 
							</div>
					   </div>
					</div>
					<div class="modal-body step-content" style="padding-top:0px;">
						<div class="step-pane active" style="min-height:70px" data-step="1">
							<form action="" id='checkbox1'>
							   <table class="layui-hide" id="iplist"></table>
							</form>	
						</div>
					</div>
				</div>
				<div class="changeSelectModelMore" style="display:none">
					<div class="row">
						<div class="col-xs-12" style="margin-left:20px;margin-top: 10px;">
							<div style="float: left;margin-right: 6px;">   
								<select class="form-control" name='more_class_id' id="more_class_id" >
									<option value="">所属IP分类</option>
									<?php foreach ($IpClassList as $value): ?>
									<option value="<?php echo $value['class_id'] ?>" rel="<?php echo $value['class_name'] ?>"   
									<?php if ($value['class_id'] == Html::encode(Yii::$app->controller->get('class_id')) ): ?>
									 selected='selected'
									 <?php endif ?>	
									><?php echo $value['class_name'] ?></option>
									<?php endforeach ?>
								</select>
							</div>
							<div style="float: left;margin-right: 6px;">   
								<select class="form-control" name='more_network_gateway' id="more_network_gateway" >
									<option value="">所属IP网段</option>
									<?php foreach ($IpNetworkList as $value): ?>
									<option value="<?php echo $value['gateway'] ?>" rel="<?php echo $value['network'] ?>"   
									<?php if ($value['gateway'] == Html::encode(Yii::$app->controller->get('network_gateway')) ): ?>
									 selected='selected'
									 <?php endif ?>	
									><?php echo $value['network'] ?></option>
									<?php endforeach ?>
								</select>
							</div>
							<div style="float: left;margin-right: 6px;"> 
								IP段：						 
								<input style="display:inline-block;width:150px;"type="text" class="form-control search-query" name="ifame_more_select_ip" value='<?php echo Html::encode(Yii::$app->controller->get('ip')) ?>'  placeholder=" 例：192.168.1-10">
							</div>
							<div>
								<button type="submit" id="ifame_select_ipmore_submit" class="btn btn-white btn-primary" style="margin-left:15px">
									<span class="fa fa-search"></span>
									搜索
								</button>
							 </div>
					   </div>
				   </div>
				   <div class="modal-body step-content" style="padding-top:0px;">
						<div class="result_title">搜索结果详情：</div>
						<div class="ipresult">
							<table cellspacing="0" cellpadding="0" border="0" class="setipinfo">
							
							</table>
						</div>
				   </div>
				</div>
				<input type="hidden" class="submit_ip_value" value="">
			</div>
		</div>
		<div class="modal-footer wizard-actions">			
			<button class="btn btn-success btn-sm btn-next" id="selectip" data-dismiss="modal">确定
				<i class="ace-icon fa fa-arrow-right icon-on-right"></i>
			</button>
			<button class="btn btn-danger btn-sm pull-left" data-dismiss="modal"><i class="ace-icon fa fa-times"></i>
				取消
			</button>
		</div>
	</div>
</div>
<!--IP库 Modal End -->

<!--财务确定-->
<div id="financial_comfirm" style="display:none;">
	<table class="table float_table">
		<tr>
			<td>当前确定金额为：</td>
			<td><?php echo $moneyInfo['money_num'];?></td>
		</tr>
		<tr>
			<td>收款确定时间：</td>
			<td><input type="text" class="receivables_time" id="receivables_time" placeholder="点击选择时间" readonly value=""></td>
		</tr>
	</table>
</div>



<script src="/js/layui.js" charset="utf-8"></script>
<script type="text/html" id="statusTpl">
{{#  if(d.status === '0'){ }}
<span style="color: #d15b47;">闲置</span>
{{# } }}
{{#  if(d.status === '1'){ }}
<span style="color: #82af6f;">使用中</span>
{{#  } }}
{{#  if(d.status === '2'){ }}
<span style="color: #f89406;">预留</span>
{{# } }}
</script>
<script type="text/javascript">
	var refund_time = {
		elem: '#refund_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: true,
		istoday: true,
		choose: function(datas){

		}
	};
	var receivables_time = {
		elem: '#receivables_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: true,
		istoday: true,
		choose: function(datas){

		}
	};
	laydate(receivables_time);
	laydate(refund_time);
	laydate.skin('molv');
	
$(function() {
	
	
	
	//查看所有IP
	$(".lookallip").click(function() {
		var allIp = $(this).next('.allipbox').val();
		var ipArray = allIp.split(',');
		
		$("#LookIPlist").html('');
		
		for(var i in ipArray) {
			$("#LookIPlist").append('&nbsp;&nbsp;' + ipArray[i] + '<br/>');
		}

		layer.open({
			type: 1, 
			area: ['400px', '300px'],
			title:"" || "查看所有IP",
			content: $("#LookIPlist"),
			maxmin: true, //最大化按钮
			anim:3, //动画
			btn: ['确定', '关闭'],
			yes: function(index, layero) {
				layer.close(index);
			} 
		});
	});
	
	//设置变更后IP	
	$('.setting_change_ip').click(function(){
		$('#setting_change_ip_modal').modal('show');
		return ;
	});
	//IP页面选项卡
	var itemType = 'single';

	//选项卡
	$(".selectmodle").click(function() {
		$(".selectmodle").removeClass("active");
		$(this).addClass("active");
		if($(this).hasClass("changeTitleSingle")) {
			$(".changeSelectModelMore").fadeOut(400, function() {
				$(".changeSelectModelMore").css("display","none");
				$(".changeSelectModelSingle").fadeIn(200).css("display","block");
			});
			itemType = 'single';
		} else {
			$(".changeSelectModelSingle").fadeOut(400, function() {
				$(".changeSelectModelSingle").css("display","none");
				$(".changeSelectModelMore").fadeIn(200).css("display","block");
			});
			itemType = 'more';
		}
		$(".submit_ip_value").val('');
	});
	
	var room_id = "<?php echo $MemberPdtRes['room_id']; ?>";	
	//显示IP库
	$('#IPlibraryselection').click(function(){		
		layui.use('table', function(){
			var table = layui.table;	  
			table.render({
				elem: '#iplist',
				url:'<?php echo Url::to(['member-pdt/ajax-iplist'])?>',
				where: {room_id:room_id},
				limit:"15",
				cols: [[
				  {type:'checkbox'}
				  ,{field:'id', width:'100',title: 'ID', sort: true}
				  ,{field:'ip', width:'150', title: 'IP地址'}
				  ,{field:'vlan', width:'80',title: 'Vlan', sort: true}
				  ,{field:'mask', width:'150', title: '掩码'}    		      
				  ,{field:'gateway', width:'150', title: '网关'} 
				  ,{field:'status',width:'100', title: '状态', sort: true,templet: '#statusTpl'}   	
				  ,{field:'remarks', title: '备注'}       		      
				]],
				page: true,		   
			});
		});
		$('#IPlibraryselectionshow').modal('show');
		return ;
	});
	//分类改变
	$('[name="class_id"]').change(function(){
		var class_id = $("#class_id").val();
		var url = "<?php echo Url::to(['ip-class/ajax-getnetwork'])?>";
		var load = layer.load(2);
		$.post(url, {class_id:class_id,room_id:room_id}, function(data){
			if ( data['data']['status'] ) {
				$("[name='network_gateway']").empty();
				$("[name='network_gateway']").append('<option value="">所属IP网段</option>');
				$.each(data['data']['data'], function(key, val){					
					$("[name='network_gateway']").append('<option value="'+val['gateway']+'" rel="'+val['network']+'">'+val['network']+'</option>');
				});		
				layer.closeAll('loading');
			} else {
				layer.alert(data['data']['info'], {icon:7});
				return;
			}
			
		},'json');
	});	
	$('[name="more_class_id"]').change(function(){
		var class_id = $("#more_class_id").val();
		var url = "<?php echo Url::to(['ip-class/ajax-getnetwork'])?>";
		var load = layer.load(2);
		$.post(url, {class_id:class_id,room_id:room_id}, function(data){
			if ( data['data']['status'] ) {
				$("[name='more_network_gateway']").empty();
				$("[name='more_network_gateway']").append('<option value="">所属IP网段</option>');
				$.each(data['data']['data'], function(key, val){					
					$("[name='more_network_gateway']").append('<option value="'+val['gateway']+'" rel="'+val['network']+'">'+val['network']+'</option>');
				});		
				layer.closeAll('loading');
			} else {
				layer.alert(data['data']['info'], {icon:7});
				return;
			}
			
		},'json');
	});
	
	//根据IP查询
	$('#ifame_select_ip_submit').click(function() { 
		var selip = $("[name='ifame_select_ip']").val();
		var class_id = $("#class_id").val();
		var gateway = $("#network_gateway").val();
		layui.use('table', function(){
			  var table = layui.table;	  
			  table.render({
				elem: '#iplist',
				url:'<?php echo Url::to(['member-pdt/ajax-iplist'])?>',
				where: {room_id:room_id,ip:selip,class_id:class_id,gateway:gateway},
				limit:"15",    		   
				cols: [[
				  {type:'checkbox'}
				  ,{field:'id', width:'100',title: 'ID', sort: true}
				  ,{field:'ip', width:'150', title: 'IP地址'}
				  ,{field:'vlan', width:'80',title: 'Vlan', sort: true}
				  ,{field:'mask', width:'150', title: '掩码'}    		      
				  ,{field:'gateway', width:'150', title: '网关'} 
				  ,{field:'status', width:'100', title: '状态', sort: true,templet: '#statusTpl'}   	
				  ,{field:'remarks', title: '备注'}      		      
				]],
				page: true,		   
			 });
		});
		//$('#IPlibraryselectionshow').modal('show');
		return ;
	});

	//根据IP段查询
	$("#ifame_select_ipmore_submit").click(function() {
		var select_ip = $('[name="ifame_more_select_ip"]').val(); //IP段
		
		var class_id = $("#more_class_id").val();
		var gateway = $("#more_network_gateway").val();
		var url = "<?php echo Url::to(['member-pdt/ajax-iplist-morenew'])?>";
		
		if(!class_id) {
			layer.alert("所属IP分类必须选择", {icon:7});
			return false;
		}
		if( !select_ip && !gateway) {
			layer.alert("所属IP网段和IP段必选其中一个条件", {icon:7});
			return false;
		}
		
		$.post(url, {"room_id":room_id, "class_id":class_id,"gateway":gateway,"select_ip":select_ip}, function(e) {
			if( e.data.status) {
				if(e.data.datas.allcount > 0) {
					var html = '<tr><td>区段搜索的IP</td><td>'+e.data.datas.searchip+'</td></tr><tr><td>总计IP数</td><td>'+e.data.datas.allcount+'</td></tr><tr><td>即将分配IP数</td><td>'+(e.data.datas.canuse.length)+'个</td></tr><tr><td>不可用IP数</td><td>'+(e.data.datas.allcount - e.data.datas.canuse.length)+'个</td></tr>';
					html += '<tr><td>不可用IP列表</td><td><table class="setiplist"><ul class="setiplist">';
					$.each(e.data.datas.notuse, function(i, n) {
						html += '<li>'+n+'</li>';
					});
					html += '</ul></table></td></tr>';
					$(".setipinfo").html(html);
					if(e.data.datas.allcount - e.data.datas.canuse.length > 0) {
						$(".submit_ip_value").val('');
					} else {
						$(".submit_ip_value").val(e.data.datas.searchip);
					}
				}else {
					$(".setipinfo").html('无数据');
				}
			} else {
				layer.alert(e.data.info, {icon:7});
				return;
			}
			
			
		}, "json");
	});

	//选择好IP
	$('#selectip').click(function() {
		if(itemType == 'single') {
			var table = layui.table;
			var checkStatus = table.checkStatus('iplist');
			data = checkStatus.data;
			var url = "<?php echo Url::to(['member-pdt/get-selectip']) ?>";
			var load = layer.load(2);
			//var oldIP= document.getElementById("have_ips").value;
			//var oldIP = $('input[name="havp_ip"]').value();
			$.post(url, {data:JSON.stringify(data),ips:""}, function(data){
				$.each(data['data'], function(key, val){
					$("#iplistji1").append('<div class="iplittlebox"><input value="'+val+'" type="text" readonly="readonly" name="ips[]"/><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button><div>');			
				});
				layer.closeAll('loading');        	
			},'json');   
		} else {
			var pace = $(".submit_ip_value").val();
			if(!pace) {
				layer.alert("必须查询后或者没有不可用IP才可以提交", {icon:7});
				return false;
			}
			$("#iplistji1").append('<div class="iplittlebox"><input value="'+pace+'" type="text" readonly="readonly" name="ips[]"/><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button><div>');			
		}		 
	});
	
	//IP重置	
	$('#ip_reset').click(function(){
		var servicerprovider = '<?php echo $MemberPdtRes["servicerprovider"]?>';
		var ipstr = '<?php echo $MemberPdtRes["ip"]?>';
		var ipdata = JSON.parse(ipstr);
		if( servicerprovider == 0 ) {
			$('#iplistji1').html('');
			$.each(ipdata, function(key, val){
				$("#iplistji1").append('<div class="iplittlebox"><input value="'+val+'" type="text" readonly="readonly" name="ips[]"/><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button><div>');			
			});
		} else {
			$('#iplistji').html('');
			$.each(ipdata, function(key, val){
				$("#iplistji").append('<div class="iplittlebox"><input value="'+val+'" type="text" name="ips[]"/><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button><div>');
			});
		}
	});
	//IP 确定选择
	$('#ip_setting_confirm').click(function(){
		var ipdata = [];
		$("input[name='ips[]']").each(function(){
			ipdata.push($(this).val());
		});
		if( ipdata.length == 0 ) {
			layer.alert('想要更换的IP为空', {icon:7});
			return false;
		}
		var loading = layer.load(0, {shade:[0.7, '#f1f3f5']});
		var url = "<?php echo Url::to(['flow-settingip'])?>";		
		var flow_id = "<?php echo $FlowRes['flow_id']?>";
		$.post(url, {flow_id:flow_id,ipdata:ipdata}, function(data){
			layer.closeAll('loading');
			if ( data['data']['status'] ) {
				layer.alert("IP确定完毕", {icon:1}, function() {
					window.location.reload();
				});
			} else {
				layer.alert(data['data']['info'], {icon:7});
				return;
			}
			
		},'json');
			
	});
	
	//检测数组中是否存在某个字符串
	function in_array(search,array){
		for(var i in array){
			if(array[i]==search){
				return true;
			}
		}
		return false;
	}
	//审核模块（通过或者驳回）
	$(".verify_btn").click(function(e) {
		if($(this).hasClass('money_success')) {
			//金额审核通过，直接提交后台
			var flowname = "<?php echo $FlowRes['flow_name']?>";
			var tips = '确定这笔款项已经收到了吗？';
			if(flowname == '变更配置-无金额') {
				tips = '我已知晓金额变动，确定通过吗？';
			}
			
			var Arr= ['变更配置-线下打款', '变更配置-在线支付'];
			if( in_array(flowname, Arr))
			{
				var index = layer.confirm(tips, {btn: ['确定', '取消'], title:"金额审核再次确认"}, function() {
				
					layer.open({
						type: 1, 
						area: ['540px', '300px'],
						title:"" || "请选择收款确定时间",
						content: $("#financial_comfirm"),
						maxmin: true, //最大化按钮
						anim:3, //动画
						btn: ['确定', '取消'],
						yes: function(index, layero) {
							var loading = layer.load(1, {shade: [0.7, '#fff']});
							var receivables_time = $("#receivables_time").val();						
							if( receivables_time == '' ) {
								layer.close(loading);
								layer.alert('请选择收款确定时间', {icon:7});
								return false;
							}
							
							var url = "<?php echo Url::to(["work-flow/verify-money-result"])?>";
							var flow_id = "<?php echo $FlowRes['flow_id']?>";
							$.post(url, {"result":"Y", "flow_id":flow_id, "receivables_time":receivables_time}, function(e) {
								layer.close(loading);
								if(e.data.status == 1) {
									layer.alert("审核结果处理完成", {icon:1}, function() {
										window.location.reload();
									});
								} else {
									layer.alert(e.data.info, {icon:7});
								}
							}, "json");
						},
						btn2: function(){
							layer.closeAll();
						}
					});
				});
				return false;
			} else {
				var index = layer.confirm(tips, {btn: ['确定', '取消'], title:"金额审核再次确认"}, function() {
					layer.close(index);
					
					var loading = layer.load(1, {shade: [0.7, '#fff']});
					var url = "<?php echo Url::to(["work-flow/verify-money-result"])?>";
					var flow_id = "<?php echo $FlowRes['flow_id']?>";
					$.post(url, {"result":"Y", "flow_id":flow_id}, function(e) {
						layer.close(loading);
						if(e.data.status == 1) {
							layer.alert("审核结果处理完成", {icon:1}, function() {
								window.location.reload();
							});
						} else {
							layer.alert(e.data.info, {icon:7});
						}
					}, "json");
				});
			}
			
		} else if($(this).hasClass('money_fail')) {
			//金额审核驳回，填写原因
			layer.prompt({title: '请写下驳回理由', formType: 2}, function(text, index) {
				layer.close(index);
				
				var loading = layer.load(1, {shade: [0.7, '#fff']});
				var url = "<?php echo Url::to(["work-flow/verify-money-result"])?>";
				var flow_id = "<?php echo $FlowRes['flow_id']?>";
				
				$.post(url, {"result":"N", "flow_id":flow_id, "reason":text}, function(e) {
					layer.close(loading);
					if(e.data.status == 1) {
						layer.alert("审核结果处理完成", {icon:1}, function() {
							window.location.reload();
						});
					} else {
						layer.alert(e.data.info, {icon:7});
					}
				}, "json");
			});
			
			
		} else if($(this).hasClass('handle_success')) {
			//操作审核通过，直接提交后台

			var index = layer.confirm("确定要允许执行这个操作吗", {btn: ['确定', '取消'], title:"操作审核再次确认"}, function() {
				layer.close(index);
				
				var loading = layer.load(1, {shade: [0.7, '#fff']});
				var url = "<?php echo Url::to(["work-flow/verify-handle-result"])?>";
				var flow_id = "<?php echo $FlowRes['flow_id']?>";
				$.post(url, {"result":"Y", "flow_id":flow_id}, function(e) {
					layer.close(loading);
					if(e.data.status == 1) {
						layer.alert("审核结果处理完成", {icon:1}, function() {
							window.location.reload();
						});
					} else {
						layer.alert(e.data.info, {icon:7});
					}
				}, "json");
			});
		} else if($(this).hasClass('handle_fail')) {
			//操作审核驳回，填写原因
			layer.prompt({title: '请写下驳回理由', formType: 2}, function(text, index) {
				layer.close(index);
				
				var loading = layer.load(1, {shade: [0.7, '#fff']});
				var url = "<?php echo Url::to(["work-flow/verify-handle-result"])?>";
				var flow_id = "<?php echo $FlowRes['flow_id']?>";
				
				$.post(url, {"result":"N", "flow_id":flow_id, "reason":text}, function(e) {
					layer.close(loading);
					if(e.data.status == 1) {
						layer.alert("审核结果处理完成", {icon:1}, function() {
							window.location.reload();
						});
					} else {
						layer.alert(e.data.info, {icon:7});
					}
				}, "json");
			});
		} else {
			layer.alert("参数错误", {icon:2});
			return false;
		}
	});
	
	//技术确认
	$(".confirm_fix").click(function() {
		var detail_id = $(this).attr("data-id");
		var url = "<?php echo Url::to(['work-flow/fix-confirm'])?>";
		
		$.post(url, {"detail_id":detail_id}, function(e) {
			if(e.data.status == 1) {
				layer.alert("确认完成，此项由你进行处理，其他人不可参与", {icon:1}, function() {
					window.location.reload();
				});
			} else {
				layer.alert(e.data.info, {icon:7});
			}
		}, "json");
	});
	
	//中止事务完成	
	$(".finish_stop_flow_changeconfig").click(function() {
		var detail_id = $(this).attr("data-id");
		var url = "<?php echo Url::to(['work-flow/finish-stopflow-changeconfig'])?>";
		var index = layer.confirm("确定要中止事务完成吗", {btn: ['确定', '取消'], title:"操作审核再次确认"}, function() {
			layer.close(index);
			$.post(url, {"detail_id":detail_id}, function(e) {
				if(e.data.status == 1) {
					layer.alert("事务中止完成", {icon:1}, function() {
						window.location.reload();
					});
				} else {
					layer.alert(e.data.info, {icon:7});
				}
			}, "json");
		});
		
	});
	
	//技术配置完毕，处理完成
	$(".edit_complete_info").click(function() {
		var detail_id = $(this).attr("data-id");
		layer.open({
			type: 1, 
			area: ['400px', '300px'],
			title:"" || "填写安装信息",
			content: $("#CompleteInfo"),
			maxmin: true, //最大化按钮
			anim:3, //动画
			btn: ['提交', '关闭'],
			yes: function(index, layero) {
				layer.close(index);				
				var url = "<?php echo Url::to(['work-flow/completemoney'])?>";
				var remark = $(".complete_remark").val();
				
				var loading = layer.load(1, {shade: [0.7, '#fff']});
				$.post(url, {"detail_id":detail_id, "remark":remark}, function(e) {
					layer.close(loading);
					if(e.data.status == 1) {
						layer.alert("新配置处理完成", {icon:1}, function() {
							window.location.reload();
						});
					} else {
						layer.alert(e.data.info, {icon:7});
					}
				}, "json");
			} 
		});
	});
	
	//审核成本
	$(".writeCost").click(function() {
		var detail_id = $(this).attr("data-id");
		
		layer.open({
			type: 1, 
			area: ['400px', '300px'],
			title:"" || "填写成本",
			content: $("#FillCost"),
			maxmin: true, //最大化按钮
			anim:3, //动画
			btn: ['提交', '关闭'],
			yes: function(index, layero) {
				layer.close(index);
				
				var url = "<?php echo Url::to(['work-flow/fill-cost'])?>";
				var cost_base = $(".cost_base").val();
				var cost_part = $(".cost_part").val();
				var cost_type = $(".cost_type").val();
				if(cost_base == '' || cost_part == '' || !cost_type) {
					layer.alert("必须填写基础成本，配件成本以及成本货币类型，若没有成本则可以填0");
					return false;
				}
				
				if(checkRate(cost_base) && checkRate(cost_part)) {
					var loading = layer.load(1, {shade: [0.7, '#fff']});
					$.post(url, {"detail_id":detail_id, "cost_base":cost_base, "cost_part":cost_part, "cost_type":cost_type}, function(e) {
						layer.close(loading);
						if(e.data.status == 1) {
							layer.alert("提交成本信息完成", {icon:1}, function() {
								window.location.reload();
							});
						} else {
							layer.alert(e.data.info, {icon:7});
						}
					}, "json");
				} else {
					layer.alert("请正确填写金额格式，必须为数字");
					return false;
				}
			} 
		});
	});
	
	//中止流程，填写退款信息
	$(".refund_btn").click(function() {
		layer.open({
			type: 1, 
			area: ['500px', '700px'],
			title:"" || "中止流程 - 填写退款信息",
			content: $("#refundMoney"),
			maxmin: true, //最大化按钮
			anim:3, //动画
			btn: ['提交', '关闭'],
			yes: function(index, layero) {
				var type = $(".refund_type").val();
				var refund_data = {};
				
				if(type == 'balance') {
					var orderMoney = "<?php echo $moneyInfo['money_num'] ? $moneyInfo['money_num'] : $OrderRes['trade_pay_money']?>";
					var refundMoney = $(".refund_balance_money").val();
					var refundRemark = $(".refund_balance_remark").val();
					if(refundMoney == '' || !checkRate(refundMoney)) {
						layer.alert("请正确填写退款金额", {icon:2});
						return false;
					}
					
					if(refundMoney > orderMoney) {
						layer.alert("退款金额不能大于付款金额", {icon:2});
						return false;
					}
					
					refund_data.refund_type = type;
					refund_data.refund_balance_money = refundMoney;
					refund_data.refund_balance_remark = refundRemark;
					
				} else {
					if(!$(".refund_platform").val() || !$(".refund_account").val() || !$(".refund_platform_money").val() || !checkRate($(".refund_platform_money").val()) || !$(".refund_time").val() || !$(".refund_proof").val()) {
						layer.alert("请正确填写打款退款的所有项信息", {icon:2});
						return false;
					}
					var orderMoney = "<?php echo $moneyInfo['money_num'] ? $moneyInfo['money_num'] : $OrderRes['trade_pay_money']?>";
					if($(".refund_platform_money").val() > orderMoney) {
						layer.alert("退款金额不能大于付款金额", {icon:2});
						return false;
					}
					
					refund_data.refund_type = type;
					refund_data.refund_platform = $(".refund_platform").val();
					refund_data.refund_account = $(".refund_account").val();
					refund_data.refund_name = $(".refund_name").val();
					refund_data.refund_platform_money = $(".refund_platform_money").val();
					refund_data.refund_time = $(".refund_time").val();
					refund_data.refund_proof = $(".refund_proof").val();
					refund_data.refund_platform_remark = $(".refund_platform_remark").val();	
				
				}
				
				var confirmRefund = layer.confirm("请仔细核对退款信息，提交后则不可恢复信息！", {btn: ['确定', '取消'], title:"金额退款再次确认"}, function() {
					layer.close(index);
					layer.close(confirmRefund);
					var loading = layer.load(1, {shade: [0.7, '#fff']});
					var url = "<?php echo Url::to(['work-flow/stop-refund'])?>";
					var flow_id = "<?php echo $FlowRes['flow_id']?>";
					$.post(url, {"data":refund_data, "flow_id":flow_id}, function(e) {
						layer.close(loading);
						if(e.data.status == 0) {
							layer.alert(e.data.info, {"icon":7});
						} else if(e.data.status == 1) {
							layer.alert(e.data.info, {"icon":1}, function() {
								window.location.reload();
							});
						}
					}, "json");
					
				});
			} 
		});
	});
	
	//中止流程退款方式选择
	$(".refund_type").change(function() {
		var type = $(this).val();
		if(type == 'balance') {
			$(".balanceInfoBox").show();
			$(".refundInfoBox").hide();
		} else {
			$(".balanceInfoBox").hide();
			$(".refundInfoBox").show();
		}
	});
	
	//中止流程，关闭机器
	$(".close_machine").click(function() {
		var detail_id = $(this).attr('data-id');
		
		var index = layer.confirm("确认已关闭机器吗？", {btn: ['确定', '取消'], title:"关闭机器再次确认"}, function() {
			layer.close(index);
			var url = "<?php echo Url::to(['work-flow/stop-close'])?>";
			var loading = layer.load(1, {shade: [0.7, '#fff']});
			$.post(url, {"detail_id":detail_id}, function(e) {
				layer.close(loading);
				if(e.data.status == 1) {
					layer.alert(e.data.info, {"icon":1}, function() {
						window.location.reload();
					});
				} else {
					layer.alert(e.data.info, {"icon":7});
				}
			}, "json");
		});
	});
	
	//当处理驳回时，如果技术已经处理，需要将机器恢复到原来配置	
	$(".restore_machine_configuration").click(function() {
		var detail_id = $(this).attr('data-id');		
		var index = layer.confirm("确认已经恢复机器配置了吗？", {btn: ['确定', '取消'], title:"恢复机器配置再次确认"}, function() {
			layer.close(index);
			var url = "<?php echo Url::to(['work-flow/restore-machine-configuration'])?>";
			var loading = layer.load(1, {shade: [0.7, '#fff']});
			$.post(url, {"detail_id":detail_id}, function(e) {
				layer.close(loading);
				if(e.data.status == 1) {
					layer.alert(e.data.info, {"icon":1}, function() {
						window.location.reload();
					});
				} else {
					layer.alert(e.data.info, {"icon":7});
				}
			}, "json");
		});
	});
	//IP增减按钮事件
	$(document).ready(function() {
		var MaxInputs    = 1000; //maximum input boxes allowed
		var InputsWrapper  = $("#iplistji"); //Input boxes wrapper ID
		var AddButton    = $("#AddMoreFileBox"); //Add button ID
		var x = InputsWrapper.length; //initlal text box count
		var FieldCount=1; //to keep track of text box added
		$(AddButton).click(function (e) //on add input button click
		{
			if(x <= MaxInputs) //max input box allowed
			{
			  FieldCount++; //text box added increment
			  //add input box
			  $(InputsWrapper).append('<div class="iplittlebox"><input value="" type="text" name="ips[]"/><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button><div>');
			  x++; //text box increment
			}
			return false;
		});
		$("body").on("click",".removeclass", function(e){ //user click on remove text                
			$(this).parent('div').remove(); //remove text box                   
			return false;
		})
	});
});

function checkRate(number) {
　　var re = /^[0-9]+.?[0-9]*$/; 
　　if (!re.test(number)) {
　　　　return false;
　　} else {
		return true;
	}
}
</script>
<?php $this->endBlock(); ?>
