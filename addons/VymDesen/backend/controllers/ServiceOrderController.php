<?php

namespace addons\VymDesen\backend\controllers;

use addons\VymDesen\backend\models\Cost;
use addons\VymDesen\backend\models\IdlePdt;
use addons\VymDesen\backend\models\IpRecord;
use addons\VymDesen\backend\models\PdtHaveuselog;
use addons\VymDesen\backend\models\PdtProvideruselog;
use addons\VymDesen\backend\models\Provider;
use addons\VymDesen\backend\models\ServiceOrder;
use addons\VymDesen\backend\models\TestServer;
use addons\VymDesen\backend\controllers\BaseController;
use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\components\FinanceReport;
use addons\VymDesen\common\components\NoticeUserMail;
use addons\VymDesen\common\components\NotifyHandle;
use addons\VymDesen\common\components\PrePay;
use addons\VymDesen\common\models\Finance\FinanceManage;
use addons\VymDesen\common\models\Member\InitialAccount;
use addons\VymDesen\common\models\Member\MemberPdt;
use addons\VymDesen\common\models\Pdt\PdtCabinetManage;
use addons\VymDesen\common\models\Pdt\PdtIp;
use addons\VymDesen\common\models\Pdt\PdtManage;
use addons\VymDesen\common\models\Pdt\PdtManageType;
use addons\VymDesen\common\models\Pdt\PdtRoomMange;
use addons\VymDesen\common\models\Trade\Trade;
use addons\VymDesen\common\models\UserMember\UserMember;
use Yii;
use yii\db\Expression;
use yii\helpers\ArrayHelper;


/**
 *
 * 订单服务控制器
 * <AUTHOR>
 *
 */
class ServiceOrderController extends BaseController
{
    /**
     * 列表
     * @return Ambigous <string, string>
     */
    public function actionIndex()
    {
        $ServiceOrderModel = new ServiceOrder();
        $ServiceOrderQuery = $ServiceOrderModel->getListAll([], true);
        #创建搜索条件
        $ServiceOrderModel->createSearchWhere($ServiceOrderQuery, $this->get());
        $iCount = $ServiceOrderQuery->count();
        $oPage  = DataHelper::getPage($iCount);
        $arrRes = $ServiceOrderQuery->orderBy('service_time_start desc')->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();

        return $this->render('index', ['arrRes' => $arrRes, 'iCount' => $iCount, 'page' => $oPage]);
    }

    /*
		服务工单详情
	*/
    public function actionView()
    {

        $serviceid         = $this->get('serviceid');
        $ServiceOrderModel = new ServiceOrder();

        $ServiceOrderRes = $ServiceOrderModel->findOne(['serviceid' => $serviceid])->toArray();##print_r($ServiceOrderRes);die();
        if (empty($ServiceOrderRes)) {
            return $this->redirect(['index']);
        }

        $service_front_config = json_decode(preg_replace('/[\x00-\x1F\x80-\x9F]/u', '', trim($ServiceOrderRes['service_front_config'])), true);
        $service_after_config = json_decode(preg_replace('/[\x00-\x1F\x80-\x9F]/u', '', trim($ServiceOrderRes['service_after_config'])), true);

        #获取相关订单信息
        $TradeModel = new Trade();
        $TradeRes   = $TradeModel->find()->where(['serviceid' => $ServiceOrderRes['serviceid']])->asArray()->one();#print_r($TradeRes);
        #获取对应产品信息
        $MemberPdtModel = new MemberPdt();
        $MemberPdtRes   = $MemberPdtModel->findOne(['unionid' => $ServiceOrderRes['unionid']]);
        if (empty($MemberPdtRes)) {
            #获取服务器配置信息
            $roomId    = ArrayHelper::getValue($service_after_config, 'room_id');
            $room_name = $roomId ? Yii::$app->db->createCommand("select name from pdt_room_manage where id = '{$roomId}'")->queryScalar() : '未知';
            $pdt_name  = Yii::$app->db->createCommand("select name from pdt_manage where id = '" . $service_after_config['pdt_id'] . "'")->queryScalar();

            $server_type_id   = ArrayHelper::getValue($service_after_config, 'server_type_id');
            $server_type_name = $server_type_id ? Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '{$server_type_id}'")->queryScalar() : '未知';

            $ip = '';

            $MemberPdtRes = [];
        } else {
            #获取服务器配置信息
            $room_name        = Yii::$app->db->createCommand("select name from pdt_room_manage where id = '" . $MemberPdtRes->room_id . "'")->queryScalar();
            $pdt_name         = Yii::$app->db->createCommand("select name from pdt_manage where id = '" . $MemberPdtRes->pdt_id . "'")->queryScalar();
            $server_type_name = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '" . $MemberPdtRes->server_type_id . "'")->queryScalar();

            $ipArr = json_decode($MemberPdtRes->ip, true);
            $ip    = '';
            if (count($ipArr) > 12) {
                for ($i = 0; $i < count($ipArr); $i++) {
                    if ($i % 11 == 0 && $i != 0) {
                        if ($i == count($ipArr) - 1) {
                            $ip .= $ipArr[$i];
                        } else {
                            $ip .= $ipArr[$i] . '&nbsp;&nbsp;<br/>';
                        }
                    } else {
                        if ($i == count($ipArr) - 1) {
                            $ip .= $ipArr[$i];
                        } else {
                            $ip .= $ipArr[$i] . '&nbsp;&nbsp;';
                        }
                    }
                }
            } else {
                $ip = implode('&nbsp;&nbsp;', $ipArr);
            }

            $MemberPdtRes = $MemberPdtRes->toArray();
        }
        //var_dump($service_front_config);
        if (count($service_front_config['ip']) >= count($service_after_config['ip'])) {
            $minuesip = array_diff($service_front_config['ip'], $service_after_config['ip']);
        } else {
            $minuesip = array_diff($service_after_config['ip'], $service_front_config['ip']);
        }

        return $this->render('view', [
            'arrRes'               => $ServiceOrderRes,
            'TradeRes'             => $TradeRes,
            'service_front_config' => $service_front_config,
            'service_after_config' => $service_after_config,
            'minuesip'             => $minuesip,
            'room_name'            => $room_name,
            'pdt_name'             => $pdt_name,
            'server_type_name'     => $server_type_name,
            'ip'                   => $ip,
            'MemberPdtRes'         => $MemberPdtRes,
        ]);
    }

    /***
     *确认服务工单
     */
    public function actionConfirmthat()
    {
        $MemberPdtModel    = new MemberPdt();
        $ServiceOrderModel = new ServiceOrder();
        $serviceid         = $this->post('serviceid');
        $ServiceOrderQuery = $ServiceOrderModel->findOne(['serviceid' => $serviceid]);

        $ServiceOrderQuery->service_time_confirm_time = time();
        $ServiceOrderQuery->service_confirm_adminid   = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
        $ServiceOrderQuery->service_confirm_adminname = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
        $ServiceOrderQuery->service_status            = '确认工单';

        $transaction = Yii::$app->db->beginTransaction();

        if ($ServiceOrderQuery->update()) {
            $transaction->commit();
            $arrReturn = [
                'status' => 1,
                'info'   => "确认成功"
            ];
        } else {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => "确认失败"
            ];
        }
        return $this->renderJSON($arrReturn);
    }

    /**
     *    新购确认显示页
     */

    public function actionConfirmassigned()
    {
        $ServiceOrderModel = new ServiceOrder();
        $TradeModel        = new Trade();

        $serviceid       = $this->get('serviceid');
        $ServiceOrderRes = $ServiceOrderModel->find()->where(['serviceid' => $serviceid])->asArray()->one();

        $config = json_decode($ServiceOrderRes['service_after_config'], true);
        $arrRes = [];
        #获取机房
        $RoomModel = new PdtRoomMange();
        $roomRes   = $RoomModel->find()->where(['provider' => $config['servicerprovider']])->asArray()->all();
        #产品配置类别
        $PdtManageModel = new PdtManage();
        $PdtManageList  = $PdtManageModel->find()->where(new Expression('FIND_IN_SET(:pdt_type_id, pdt_type_id)'))->addParams([':pdt_type_id' => $config['server_typeid']])->asArray()->all();
        #获取订单信息
        $TradeModelRes = $TradeModel->findOne(['serviceid' => $serviceid]);
        $arrRes        = json_decode($TradeModelRes->trade_config, true);
        #服务器分类
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeRes   = $PdtManageTypeModel->find()->AsArray()->All();

        return $this->render('confirm_assigned', [
            'ServiceOrderRes'  => $ServiceOrderRes,
            'arrRes'           => $arrRes,
            'PdtManageTypeRes' => $PdtManageTypeRes,
            'roomRes'          => $roomRes,
            'PdtManageList'    => $PdtManageList,
            'config'           => $config['config']
        ]);
    }

    /**
     *    新购确认操作
     */
    public function actionConfirmPurchase()
    {
        $IdlePdtModel      = new IdlePdt();
        $TestServerModel   = new TestServer();
        $ServiceOrderModel = new ServiceOrder();

        $post = DataHelper::dotrim($this->post());

        $serviceOrderInfo = $ServiceOrderModel->find()->where(['serviceid' => $post['serviceid']])->One();
        $config           = json_decode($serviceOrderInfo->service_after_config, true);

        $testserverQuery = $TestServerModel->find()->where(['id' => $post['testid']])->One();

        #开启事务
        $transaction = Yii::$app->db->beginTransaction();
        #如果是测试服务器
        if ($post['istestserver'] == '0') {
            if ($post['testid'] == "") {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => "请选择测试产品"
                ]);
            }
            $config['testid'] = $post['testid'];
            #更新测试服务器状态
            $testserverQuery->status = 2;
            $updateTestServer        = $testserverQuery->update();
        } else {
            #自有：服务器分类，机房，产品
            #供应商：服务器分类，机房，IP
            if ($post['servicerprovider'] == '0') {
                #自有
                if ($post['idle_id'] == "") {
                    $transaction->rollBack();
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => "请选择闲置产品"
                    ]);
                }
                //$config['room_id'] = $post['room_id'];
                $config['idle_id'] = $post['idle_id'];
            } else {
                #供应商
                $config['room_id'] = $post['room_id'];
                $config['ip']      = DataHelper::dotrim($post['provider_ips']);
                if ($post['room_id'] == "") {
                    $transaction->rollBack();
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => "请选择机房"
                    ]);
                }
                if (empty($config['ip'])) {
                    $transaction->rollBack();
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => "请填写IP地址"
                    ]);
                }
            }
            $updateTestServer = true;
        }
        $config['servicerprovider'] = $post['servicerprovider'];

        $serviceOrderInfo->service_after_config      = json_encode($config, JSON_UNESCAPED_UNICODE);
        $serviceOrderInfo->service_time_confirm_time = time();
        $serviceOrderInfo->service_confirm_adminid   = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
        $serviceOrderInfo->service_confirm_adminname = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
        $serviceOrderInfo->service_status            = '确认工单';

        $updateRes = $serviceOrderInfo->update();

        if ($updateTestServer && $updateRes) {
            $transaction->commit();
            return $this->renderJSON([
                'status' => 1,
                'info'   => "确认成功"
            ]);
        } else {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => "确认失败"
            ]);
        }
    }


    /**
     *    选择测试工单
     */
    public function actionSelectTestOrder()
    {
        $TestServerModel = new TestServer();
        $status          = $this->get('status');
        if ($status) {
            $conntion = ['status' => $status];
        } else {
            $conntion = ['status' => "0"];
        }

        $servicerprovider = $this->get('servicerprovider');
        $server_type_id   = $this->get('server_type_id');
        $TestServerQuery  = $TestServerModel->getListAll($conntion, true);

        #创建搜索条件
        $TestServerModel->createSearchWhere($TestServerQuery, $this->get());
        //分页
        $iCount = $TestServerQuery->count();
        $oPage  = DataHelper::getPage($iCount, 20);

        $arrRes = $TestServerQuery->offset($oPage->offset)->limit($oPage->limit)->orderBy('id desc')->asArray()->all();

        return $this->render('select_test_order', [
            'arrRes'           => $arrRes,
            'iCount'           => $iCount,
            'page'             => $oPage,
            'servicerprovider' => $servicerprovider,
            'server_type_id'   => $server_type_id,
        ]);
    }


    /**
     * 新购服务器  配置单
     */
    public function actionAssigned()
    {
        $ServiceOrderModel = new ServiceOrder();
        $MemberPdtModel    = new MemberPdt();
        $IdlePdtModel      = new IdlePdt();
        $TradeModel        = new Trade();
        $PdtIpModel        = new PdtIp();
        $TestServerModel   = new TestServer();
        if (Yii::$app->request->post()) {
            $testid               = $this->post('testid');
            $serviceid            = $this->post('serviceid');
            $post                 = DataHelper::dotrim($this->post());
            $ServiceOrderRes      = $ServiceOrderModel->findone(['serviceid' => $serviceid]);
            $service_after_config = json_decode($ServiceOrderRes['service_after_config'], true);
            $TradeModelRes        = $TradeModel->findOne(['serviceid' => $serviceid]);
            $trade_config         = json_decode($TradeModelRes->trade_config, true);

            #获取产品即将绑定的IP
            #根据选择的服务器供应商，获取将绑定的IP
            if ($post['servicerprovider'] == 0) {
                if (empty($post['cabinet_id'])) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '机柜未选择'
                    ];
                    return $this->renderJSON($arrReturn);
                }
                if (empty($post['have_ips'])) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => 'IP地址未选择或未填写'
                    ];
                    return $this->renderJSON($arrReturn);
                } else {
                    $ip  = DataHelper::dotrim($post['have_ips']);
                    $ip2 = $ip;
                    #考虑为IP段，所以重新定义
                    foreach ($ip2 as $key => $value) {
                        if (strpos($value, '-') !== false) {
                            $value1 = explode('-', trim($value));#print_r($value1);
                            $num4   = substr($value1[0], strripos($value1[0], ".") + 1);
                            $str    = substr($value1[0], 0, strrpos($value1[0], '.'));
                            if ($num4 >= $value1[1]) {
                                $arrReturn = [
                                    'status' => 0,
                                    'info'   => 'IP段输入有误'
                                ];
                                return $this->renderJSON($arrReturn);
                            } else {
                                $num = $value1[1] - ($num4);#echo $num;
                                for ($n = 0; $n <= $num; $n++) {
                                    $newnum       = $num4 + $n;
                                    $newip        = $str . "." . $newnum;
                                    $IPResArray[] = trim($newip);
                                }
                            }
                        } else {
                            $IPResArray[] = trim($value);
                        }
                    }
                }
                #提供商为自有时，将一些数据清空
                $post['provider_id'] = "";
            } elseif ($post['servicerprovider'] == 1) {
                #提供商为供应商时，将一些数据清空
                $post['switch_location']   = "";
                $post['switch_port']       = "";
                $post['occupies_position'] = "";
                $post['cabinet_id']        = "";
                $post['property']          = "";

                if (empty($post['provider_ips'])) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => 'IP地址未选择或未填写'
                    ];
                    return $this->renderJSON($arrReturn);
                } else {
                    $ip  = DataHelper::dotrim($post['provider_ips']);
                    $ip2 = $ip;
                    #考虑供应商的IP可能为IP段，所以重新定义
                    #获取详细的IP
                    foreach ($ip2 as $key => $value) {
                        if (strpos(trim($value), '-') !== false) {
                            $value1 = explode('-', trim($value));#print_r($value1);
                            $num4   = substr(trim($value1[0]), strripos(trim($value1[0]), ".") + 1);
                            $str    = substr(trim($value1[0]), 0, strrpos(trim($value1[0]), '.'));
                            if ($num4 >= trim($value1[1])) {
                                $arrReturn = [
                                    'status' => 0,
                                    'info'   => 'IP段输入有误'
                                ];
                                return $this->renderJSON($arrReturn);
                            } else {
                                $num = trim($value1[1]) - ($num4);#echo $num;
                                for ($n = 0; $n <= $num; $n++) {
                                    $newnum = $num4 + $n;;
                                    $newip        = $str . "." . $newnum;
                                    $IPResArray[] = trim($newip);
                                }
                            }
                        } else {
                            $IPResArray[] = trim($value);
                        }
                    }
                }

                ##
                if (!empty($IPResArray)) {
                    foreach ($IPResArray as $value) {
                        $MemberPdtIpAll = $MemberPdtModel->find()->select('unionid,ip,ip2')->where(['like', 'ip2', '"' . $value . '"'])->andWhere('status != -1')->asArray()->all();
                        if (!empty($MemberPdtIpAll)) {
                            return $this->renderJSON([
                                'status' => 0,
                                'info'   => 'IP地址 :' . $value . ' 已经被使用'
                            ]);
                        }

                        if (!$testid) {
                            $TestServerIPAll = $TestServerModel->find()->select('ip,ip2')->where(['like', 'ip2', '"' . $value . '"'])->andWhere(['status' => ['0', '2']])->asArray()->all();
                        } else {
                            $TestServerIPAll = $TestServerModel->find()->select('ip,ip2')->where(['like', 'ip2', '"' . $value . '"'])->andWhere(['status' => ['0', '2']])->andWhere(['<>', 'id', $testid])->asArray()->all();
                        }
                        if (!empty($TestServerIPAll)) {
                            return $this->renderJSON([
                                'status' => 0,
                                'info'   => 'IP地址 :' . $value . ' 已经被使用(测试服务器)'
                            ]);
                        }
                    }
                }
                $PdtIpAll = $PdtIpModel->find()->select('ip')->where(['in', 'ip', $IPResArray])->asArray()->all();
                if (!empty($PdtIpAll)) {
                    $PdtIpAll  = array_column($PdtIpAll, 'ip');
                    $res       = implode('，', $PdtIpAll);
                    $arrReturn = [
                        'status' => 0,
                        'info'   => 'IP地址：' . $res . '存在于IP库'
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }

            #处理的得到的IP数组1
            $ipArray1 = array_unique($ip);#去重 去空
            $ipArray  = array_filter($ipArray1);
            #处理的得到的IP数组2
            $ipArray2 = array_unique($IPResArray);
            $ip2Array = array_filter($ipArray2);
            #print_r($ipArray);print_r($ip2Array);die();
            #判断IP组2是否正常(即为当为供应商时，输入为IP段验证)
            if (!empty($ip2Array)) {
                foreach ($ip2Array as $value) {
                    if (!filter_var(trim($value), FILTER_VALIDATE_IP)) {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => $value . '为不合法的IP地址'
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
            }
            #判断IP所属机房是否与选择的机房一致（仅在为自有时）
            if ($post['servicerprovider'] == 0) {
                $PdtIpAll = $PdtIpModel->find()->select('room_id')->where(['in', 'ip', $ip2Array])->asArray()->all();#print_r($PdtIpAll);die;
                foreach ($PdtIpAll as $v) {
                    $v      = join(',', $v);
                    $temp[] = $v;
                }
                $temp = array_unique($temp); #去掉重复的字符串,也就是重复的一维数组
                $temp = array_values($temp); #print_r($temp); 查询排序
                if (count($temp) > 1) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => 'IP中有属于其他机房的IP地址'
                    ];
                    return $this->renderJSON($arrReturn);
                } else if (count($temp) == 1) {
                    if ($temp[0] != $post['room_id']) {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => 'IP所属机房与产品选择机房不一致'
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
            }

            # 将Ip数组装为字符串  并赋值
            $ipRes       = trim(json_encode($ipArray));
            $ip2Res      = trim(json_encode($ip2Array));#print_r($ipRes);print_r($ip2Res);#die();
            $post['ip']  = $ipRes;
            $post['ip2'] = $ip2Res;

            #分配机器时存在2种情况，一是选择了产品库即自有，二是没有选择   即供应商
            #因为IP可能修改，IP也有解除绑定的情况，需要将解绑绑定IP的状态修改为闲置  首先获取原来的IP，再和新的IP 做比对
            #但只会存在于自有，这里获取到产品库原本绑定的IP
            if ($post['servicerprovider'] == 0) {
                if ($post['idle_id'] != "") {
                    $OldIdlePdtRes = $IdlePdtModel->getRowById($post['idle_id']);
                    $oldIP         = $OldIdlePdtRes['ip2'];
                    $oldIPArray    = json_decode($oldIP, true);#print_r($oldIPArray);print_r($ip2Array);die();

                    $checkMemberPdt = $MemberPdtModel->find()->where(['idle_id' => $post['idle_id']])->andWhere('status != -1')->asArray()->one();
                    if (!empty($checkMemberPdt)) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => '所选闲置产品已经在为用户使用中，此为异常情况，请立即联系技术！'
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                } else {
                    $oldIP      = "";
                    $oldIPArray = [];
                }
            } else {
                $oldIP      = "";
                $oldIPArray = [];
            }
            #作比较，获取2个IP数组的差集  先将2个数组合并并且去重
            $bgIPArray       = array_unique(array_merge($ip2Array, $oldIPArray));
            $result_bangding = array_diff($bgIPArray, $oldIPArray);#需要改为使用中的
            $result_jiechu   = array_diff($bgIPArray, $ip2Array);  #需要改为闲置的

            if ($post['pdt_id'] == "") {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '产品配置类别未选择'
                ];
                return $this->renderJSON($arrReturn);
            }
            #获取详细配置
            $serverconfig['cpu'] = $post['cpu'];
            unset($post['cpu']);
            $serverconfig['ram'] = $post['ram'];
            unset($post['ram']);
            $serverconfig['hdd'] = $post['hdd'];
            unset($post['hdd']);
            $serverconfig['configbandwidth'] = $post['configbandwidth'];
            unset($post['configbandwidth']);
            $serverconfig['ipnumber'] = $post['ipnumber'];
            unset($post['ipnumber']);
            if (!empty($post['defense'])) {
                $serverconfig['defense'] = $post['defense'];
            } else {
                $serverconfig['defense'] = "";
            }
            unset($post['defense']);
            $serverconfig['operatsystem'] = $post['operatsystem'];
            unset($post['operatsystem']);
            #用户要求带宽
            $serverconfig['requirement_bandwidth'] = trim($post['bandwidth']);
            $post['config']                        = json_encode($serverconfig, JSON_UNESCAPED_UNICODE);              #获取到配置，并解析成字符串

            #---------获取信息end------------#
            #开启事务
            $transaction          = Yii::$app->db->beginTransaction();
            $post['attribute_id'] = 2;
            #定义场景
            if ($post['servicerprovider'] == 0) {
                $scenario = "memberpdtassigned_have";
            } elseif ($post['servicerprovider'] == 1) {
                $scenario = "memberpdtassigned_provider";
            }

            #当选择的产品库的信息有修改，产品库也将跟着修改，并同时将更新状态
            $idle_id   = $this->post('idle_id');
            $idleQuery = $IdlePdtModel->getRowById($idle_id);
            if (!empty($idle_id)) {
                $post['start_time'] = time();
                #闲置产品库修改
                $updateidleRes = $IdlePdtModel->doupdate($idle_id, '1', $post);
                if (!$updateidleRes) {
                    #回滚事务
                    $transaction->rollBack();
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '闲置产品库信息数据同步更新失败'
                    ]);
                }
            }

            #上面判断出需要解绑和绑定的IP组合。但仍然需要根据提供商来判断是否需要更改
            if (!empty($result_bangding) || !empty($result_jiechu)) {
                $PdtIpModel = new PdtIp();
                if (!empty($result_bangding)) {
                    #$res1 = $PdtIpModel->modifyIPStatus($result_bangding,"1");
                    $res1          = $PdtIpModel->updateAll(['status' => 1, 'update_time' => time()], ['ip' => $result_bangding]);
                    $IpRecordModel = new IpRecord();
                    if ($post['servicerprovider'] == 0) {
                        $describe       = "管理员：" . $this->getAdminInfo('uname') . "对业务：" . $ServiceOrderRes->unionid . " 通过新配机器操作，进行了IP使用";
                        $addIpRecordRes = $IpRecordModel->addrecord($ServiceOrderRes->unionid, null, $result_bangding, $describe, $this->getAdminInfo('admin_id'), $this->getAdminInfo('uname'));
                        if (!$addIpRecordRes) {
                            #回滚事务
                            $transaction->rollBack();
                            $arrReturn = [
                                'status' => 0,
                                'info'   => "IP记录添加失败"
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                    }
                }

                if (!empty($result_jiechu)) {
                    #$res2 = $PdtIpModel->modifyIPStatus($result_jiechu,"0");
                    $res2 = $PdtIpModel->updateAll(['status' => 0, 'update_time' => time()], ['ip' => $result_jiechu]);
                }
            }

            unset($post['start_time']);
            $post['admin_id'] = $ServiceOrderRes->admin_id;
            #$post['bandwidth'] = $serverconfig['configbandwidth'];
            $post['type_id']       = 1;
            $post['property']      = 0;
            $post['user_id']       = $TradeModelRes->member_id;
            $post['user_name']     = $TradeModelRes->member_name;
            $post['unionid']       = $ServiceOrderRes->unionid;
            $post['sell_price']    = $TradeModelRes->trade_price_real;
            $post['payment_cycle'] = $trade_config['payment_cycle'];
            $post['status']        = 1;
            $post['trade_no']      = $TradeModelRes->trade_orderid;
            $post['audit_status']  = 0;
            $time                  = time();
            $unit                  = "Month";
            $expire                = $trade_config['payment_cycle'];      ##时长  及周期
            $end_time              = strtotime("+$expire" . $unit, $time);
            $post['start_time']    = $time;
            $post['end_time']      = $end_time;
            #进入场景
            $MemberPdtModel->scenario   = $scenario;
            $MemberPdtModel->attributes = $post;    #print_r($post);#die();
            #数据验证
            if (!$MemberPdtModel->validate()) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => $MemberPdtModel->errors
                ];
                return $this->renderJSON($arrReturn);
            }

            #判断IP是否添加绑定
            if (empty($ip2Array)) {
                #回滚事务
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => "IP地址未选择或未设置"
                ];
                return $this->renderJSON($arrReturn);
            }
            /*#添加成本表
			$CostModel = new Cost();
			$CostModel->unionid = $ServiceOrderRes->unionid;
			$CostModel->basic_money = $post['cost_price'];
			$CostModel->two_money = $post['upgrade_cost_price'];
			$CostModel->change_time = $time;
			$CostModel->admin_id = $ServiceOrderRes->admin_id;*/

            ##添加初始账户
            $InitialAccountModel              = new InitialAccount();
            $InitialAccountModel->unionid     = $ServiceOrderRes->unionid;
            $InitialAccountModel->name        = $post['name'];
            $InitialAccountModel->pwd         = $post['pwd'];
            $InitialAccountModel->port        = $post['port'];
            $InitialAccountModel->create_time = $time;
            $InitialAccountModel->scenario    = "add";
            if (!$InitialAccountModel->validate()) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => $InitialAccountModel->errors
                ];
                return $this->renderJSON($arrReturn);
            }
            #更新服务工单为完成
            $ServiceOrderRes->service_status   = "完成工单";
            $ServiceOrderRes->service_time_end = $time;

            $userinfosql = "select * from user_member where u_id = '" . $TradeModelRes->member_id . "'";
            $userinfo    = Yii::$app->db->createCommand($userinfosql)->queryOne();
            $usermail    = $userinfo['email'];
            $username    = $userinfo['truename'] ? $userinfo['truename'] : $userinfo['email'];
            $mailtype    = "业务：新购设备已配置";

            NoticeUserMail::createRecord($mailtype, "您有一台新购设备已配置完成，请及时前往网站查看", null, null, $usermail, $username, $mailtype, null, "N");


            $updateTestConfigServer = true; //更新配置测试服务器状态
            $updateTestPostServer   = true; //更新提交测试服务器状态
            #$UpdateConfigIdleInfo = true; //更新配置自有属性
            $UpdatePostIdleInfo = true;     //更新提交自有属性

            #判断测试ID
            if ($post['testid']) {
                $ServerOrderConfig = json_decode($ServiceOrderRes->service_after_config, true);
                if (isset($ServerOrderConfig['testid'])) {
                    if ($post['testid'] == $ServerOrderConfig['testid']) {
                        //正常状态，确认时使用的某个测试服务器，提交的时候也使用这个
                        $TestConfigQuery         = $TestServerModel->find()->where(['id' => $post['testid']])->one();
                        $TestConfigQuery->status = '1';
                        $updateTestConfigServer  = $TestConfigQuery->update();

                        //判断是否为自有服务器，如果测试服务器使用的是自有服务器，则修改自有列表该条数据attribute_id = 2 (已售服务器)，status = 1 (使用中)
                        /*if($TestConfigQuery->idle_id) {
							//如果自有ID存在，则根据ID查询
							$ConfigIdleQuery = $IdlePdtModel->find()->where(['id' => $TestConfigQuery->idle_id])->one();
							$ConfigIdleQuery->attribute_id = 2;
							$ConfigIdleQuery->status = 1;
							$UpdateConfigIdleInfo = $ConfigIdleQuery->update();
						}*/

                    } else {

                        #修改服务工单中配置的testid为，待使用状态
                        $TestConfigQuery         = $TestServerModel->find()->where(['id' => $ServerOrderConfig['testid']])->one();
                        $TestConfigQuery->status = '0';
                        $updateTestConfigServer  = $TestConfigQuery->update();

                        /*if($TestConfigQuery->idle_id) {
							//如果自有ID存在，则根据ID查询
							$ConfigIdleQuery = $IdlePdtModel->find()->where(['id' => $TestConfigQuery->idle_id])->one();
							$ConfigIdleQuery->attribute_id = 1;//改为闲置
							$ConfigIdleQuery->status = 0; //改为未使用
							$UpdateConfigIdleInfo = $ConfigIdleQuery->update();
						}*/

                        #修改当前传过来的testid为正式服务器状态
                        $TestPostQuery         = $TestServerModel->find()->where(['id' => $post['testid']])->one();
                        $TestPostQuery->status = '1';
                        $updateTestPostServer  = $TestPostQuery->update();
                    }
                } else {
                    #修改当前传过来的testid为正式服务器状态
                    $TestPostQuery         = $TestServerModel->find()->where(['id' => $post['testid']])->one();
                    $TestPostQuery->status = '1';
                    $updateTestPostServer  = $TestPostQuery->update();
                }
            } else {
                //如果传过来的testid为空，则表示用户没有使用测试服务器，新选择了服务器，则更新服务工单中的testid为待使用状态
                if (isset($ServerOrderConfig['testid'])) {
                    $TestConfigQuery         = $TestServerModel->find()->where(['id' => $ServerOrderConfig['testid']])->one();
                    $TestConfigQuery->status = '0';
                    $updateTestConfigServer  = $TestConfigQuery->update();

                    /*if($TestConfigQuery->idle_id) {
						//如果自有ID存在，则根据ID查询
						$ConfigIdleQuery = $IdlePdtModel->find()->where(['id' => $TestConfigQuery->idle_id])->one();
						$ConfigIdleQuery->attribute_id = 1;//改为闲置
						$ConfigIdleQuery->status = 0; //改为未使用
						$UpdateConfigIdleInfo = $ConfigIdleQuery->update();
					}*/
                }
            }
            /*
				此处还要更新测试工单ID
				(完成)1.如果服务工单配置为测试工单，并且与服务工单确认时测试工单ID一致，则更新测试工单为已使用的"1"状态
				(完成)2.如果服务工单测试ID与服务工单config记录的测试ID不一致，则说明是后面更换过的，那么则把之前的测试服务器更新为"0"未使用状态，把现在的测试工单ID更新为"-1"状态
				(完成)3.测试工单如果是自有服务器更新为"已售服务器"，"使用中"两个状态
				4.在提交过程中，判断选中的IP是否被使用(此判断已存在),是否被测试工单占用(需新增)
				(默认有)5.如果是自有机器，如果和原自有机器的配置不同，则要修改自有机器的配置
			*/

            #var_dump($updateTestConfigServer, $updateTestPostServer, $UpdatePostIdleInfo);exit;


            if (!$updateTestConfigServer || !$updateTestPostServer || !$UpdatePostIdleInfo) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '测试与自有服务器属性修改失败'
                ];
                return $this->renderJSON($arrReturn);
            }


            if ($MemberPdtModel->insert() && $ServiceOrderRes->update() && $InitialAccountModel->insert()) {

                #更新财务报表状态
                $FinanceInfo  = Yii::$app->db->createCommand("select * from finance_manage where trade_orderid= '" . $TradeModelRes->trade_orderid . "' and trade_unionid = '" . $ServiceOrderRes->unionid . "'")->queryOne();
                $ReportResult = new FinanceReport($ServiceOrderRes->unionid, $TradeModelRes->member_id, $TradeModelRes->trade_orderid, $ServiceOrderRes->serviceid, $FinanceInfo, 'machine_purchase_service');
                var_dump($ReportResult);
                exit;
                $PrePayResult = PrePay::PrePay_MachinePurchase($TradeModelRes->member_name, $ServiceOrderRes->unionid, $ReportResult, $ServiceOrderRes->serviceid, $TradeModelRes->trade_orderid);
                //var_dump($PrePayResult['status']);exit;
                if ($ReportResult && $PrePayResult['status']) {

                    $transaction->commit();
                    $arrReturn = [
                        'status' => 1,
                        'info'   => '操作成功'
                    ];
                } else {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '操作失败 '
                    ];
                }
            } else {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '操作失败 '
                ];
            }
            return $this->renderJSON($arrReturn);
        } else {
            #配置页面
            $serviceid = $this->get('serviceid');
            $testid    = $this->get('testid');

            $ServiceOrderRes = $ServiceOrderModel->find()->where(['serviceid' => $serviceid])->asArray()->one();

            $config = json_decode($ServiceOrderRes['service_after_config'], true); #print_r($config);exit;
            #如果重新选择了测试机
            if ($testid) {
                $testServerQuery = new TestServer();
                $testinfo        = $testServerQuery->find()->With('idlepdt')->where(['id' => $testid])->asArray()->one();
                #print_r($testinfo);exit;
                $config['testid']           = $testid;
                $config['room_id']          = $testinfo['room_id'];
                $config['servicerprovider'] = $testinfo['servicerprovider'];
                $config['server_typeid']    = $testinfo['server_type_id'];
                $config['idle_id']          = $testinfo['idle_id'];
                $config['pdt_id']           = $testinfo['pdt_id'];
                #$config['config']['configbandwidth'] = $testinfo['bandwidth'];
            }
            #获取机房
            $RoomModel = new PdtRoomMange();
            $roomRes   = $RoomModel->find()->where(['provider' => $config['servicerprovider']])->asArray()->all();

            #获取配置信息
            #如果testid存在，那么则是测试服务器，取测试服务器的机器数据
            #如果testid不存在，那么则看是自有还是供应商
            #遍历数据出来

            if (isset($config['testid'])) {
                $testServerModel = new TestServer();
                $confirmpdtinfo  = $testServerModel->find()->With('switch')->With('idlepdt')->where(['id' => $config['testid']])->asArray()->one();
                #print_r($confirmpdtinfo);exit;
                $confirmpdtinfo['idle_name'] = $confirmpdtinfo['idlepdt'][0]['idle_name'];
                $confirmpdtinfo['testid']    = $config['testid'];
                #$confirmpdtinfo['type_id'] = $config['type_id'];
                #$confirmpdtinfo['pdt_id'] = $config['pdt_id'];
                $confirmpdtinfo['server_typeid']    = $confirmpdtinfo['server_type_id'];
                $confirmpdtinfo['servicerprovider'] = $config['servicerprovider'];

                if (empty($confirmpdtinfo['idlepdt'])) {
                    $confirmpdtinfo['re_username'] = $confirmpdtinfo['account_name'];
                    $confirmpdtinfo['re_password'] = $confirmpdtinfo['account_pwd'];
                    $confirmpdtinfo['re_port']     = $confirmpdtinfo['account_port'];
                } else {
                    $confirmpdtinfo['re_username'] = $confirmpdtinfo['idlepdt'][0]['re_username'];
                    $confirmpdtinfo['re_password'] = $confirmpdtinfo['idlepdt'][0]['re_password'];
                    $confirmpdtinfo['re_port']     = $confirmpdtinfo['idlepdt'][0]['re_port'];
                }

                $confirmpdtinfo['config'] = json_decode($confirmpdtinfo['config'], true);
                #print_r($confirmpdtinfo);exit;
            } else {
                if (isset($config['idle_id'])) {
                    #自有服务器
                    $idleModel      = new IdlePdt();
                    $confirmpdtinfo = $idleModel->find()->With('switch')->where(['id' => $config['idle_id']])->asArray()->one();
                    #print_r($confirmpdtinfo);exit;
                    #$confirmpdtinfo['room_id'] = $config['room_id'];
                    $confirmpdtinfo['server_typeid']    = $confirmpdtinfo['server_type_id'];
                    $confirmpdtinfo['servicerprovider'] = $confirmpdtinfo['servicerprovider'];
                    $confirmpdtinfo['idle_id']          = $config['idle_id'];
                    #$confirmpdtinfo['type_id'] = $config['type_id'];
                    #$confirmpdtinfo['pdt_id'] = $config['pdt_id'];
                    $confirmpdtinfo['bandwidth'] = $config['config']['requirement_bandwidth'];

                    $confirmpdtinfo['config'] = json_decode($confirmpdtinfo['config'], true);
                } else {
                    #供应商
                    $confirmpdtinfo['room_id']          = $config['room_id'];
                    $confirmpdtinfo['servicerprovider'] = $config['servicerprovider'];
                    $confirmpdtinfo['idle_id']          = $config['idle_id'];
                    $confirmpdtinfo['type_id']          = $config['type_id'];
                    $confirmpdtinfo['pdt_id']           = $config['pdt_id'];
                    $confirmpdtinfo['server_typeid']    = $config['server_typeid'];
                    $confirmpdtinfo['ip']               = $config['ip'];
                    $confirmpdtinfo['config']           = $config['config'];
                    $confirmpdtinfo['bandwidth']        = $config['config']['requirement_bandwidth'];
                }
            }
            #print_r($confirmpdtinfo);exit;

            #服务器分类
            $PdtManageTypeModel = new PdtManageType();
            $PdtManageTypeRes   = $PdtManageTypeModel->find()->AsArray()->All();

            #机柜列表
            $PdtCabinetModel = new PdtCabinetManage();
            $PdtCabinetRes   = $PdtCabinetModel->find()->where(['room_id' => $confirmpdtinfo['room_id']])->AsArray()->All();

            #产品配置类别
            $PdtManageModel = new PdtManage();
            $PdtManageList  = $PdtManageModel->find()->where(new Expression('FIND_IN_SET(:pdt_type_id, pdt_type_id)'))->addParams([':pdt_type_id' => $confirmpdtinfo['server_typeid']])->asArray()->all();

            if (!$testid) {
                if ($config['testid']) {
                    $testid = $config['testid'];
                } else {
                    $testid = '';
                }
            }

            return $this->render('assigned', [
                'ServiceOrderRes'  => $ServiceOrderRes,
                #'config'=>$config,
                'PdtManageTypeRes' => $PdtManageTypeRes,
                'roomRes'          => $roomRes,
                'PdtManageList'    => $PdtManageList,
                'config'           => $confirmpdtinfo['config'],
                'res'              => $confirmpdtinfo,
                'PdtCabinetRes'    => $PdtCabinetRes,
                'testid'           => $testid
            ]);
        }
    }

    /**
     *变更配置确认完成
     */
    public function actionServerupgrade()
    {
        Yii::$app->request->isAjax || die('error');

        $ServiceOrderModel = new ServiceOrder();
        $MemberPdtModel    = new MemberPdt();
        $IdlePdtModel      = new IdlePdt();

        $serviceid = $this->post('serviceid');
        $post      = $this->post();

        #获取服务工单信息
        $ServiceOrderRes      = $ServiceOrderModel->findone(['serviceid' => $serviceid]);
        $service_after_config = json_decode($ServiceOrderRes['service_after_config'], true);  #获取变更后的配置
        #获取用户产品信息
        $MemberPdtRes = $MemberPdtModel->findone(['unionid' => $ServiceOrderRes->unionid]);
        #开启事务
        $transaction = Yii::$app->db->beginTransaction();
        #如果用户产品属于自有库中，更新选择的闲置产品库信息(如果为自有服务器，则变更闲置产品库信息   如果为供应商所有，则不操作)
        if ($MemberPdtRes->idle_id != "" || $MemberPdtRes->idle_id != null) {
            #获取闲置产品库信息
            $IdlePdtRes              = $IdlePdtModel->findOne($MemberPdtRes->idle_id);
            $IdlePdtRes->config      = json_encode($service_after_config['config'], JSON_UNESCAPED_UNICODE);
            $IdlePdtRes->update_time = time();
            if (!$IdlePdtRes->update()) {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => "产品库信息同步更新失败"
                ]);
            }
        }
        #更新服务工单信息
        $ServiceOrderRes->service_status   = "完成工单";
        $ServiceOrderRes->service_time_end = time();
        #更新用户产品信息
        $MemberPdtRes->status    = 1;
        $MemberPdtRes->config    = json_encode($service_after_config['config'], JSON_UNESCAPED_UNICODE);
        $MemberPdtRes->bandwidth = $service_after_config['config']['requirement_bandwidth'];

        #如果付款金额小于0 则代表要么不需要付款，要么退款给用户，不走订单，直接走服务工单
        #更新用户产品价格和额外配件成本
        #if($service_after_config['price'] <= 0)
        $MemberPdtRes->sell_price         = $service_after_config['normal_price'];
        $MemberPdtRes->upgrade_cost_price = $service_after_config['normal_cost_price'];
        #

        #更新成本信息表
        #查询产品在成本表在当天是否有记录
        $CostModel = new Cost();
        $start     = strtotime(date('Y-m-d 00:00:00'));
        $end       = strtotime(date('Y-m-d H:i:s'));
        $CostRes   = $CostModel->find()->where(['unionid' => $MemberPdtRes->unionid])->andwhere(['>', 'change_time', $start])->andWhere(['<', 'change_time', $end])->asArray()->all();
        #如果当天有记录  就更新。如果没有 就新建一条成本信息
        if (empty($CostRes)) {
            $CostModel->unionid       = $MemberPdtRes->unionid;
            $CostModel->basic_money   = $MemberPdtRes->cost_price;
            $CostModel->two_money     = $service_after_config['normal_cost_price'];
            $CostModel->currency_type = $service_after_config['currency_type'];
            $CostModel->change_time   = time();
            $CostModel->admin_id      = $MemberPdtRes->admin_id;
            if (!$CostModel->insert(false)) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '成本表信息更新失败'
                ];
                return $this->renderJSON($arrReturn);
            }
        } else {
            $CostModelQuery                = $CostModel->find()->where(['unionid' => $MemberPdtRes->unionid])->andwhere(['>', 'change_time', $start])->andWhere(['<', 'change_time', $end])->one();
            $CostModelQuery->unionid       = $MemberPdtRes->unionid;
            $CostModelQuery->basic_money   = $MemberPdtRes->cost_price;
            $CostModelQuery->two_money     = $service_after_config['normal_cost_price'];
            $CostModelQuery->currency_type = $service_after_config['currency_type'];
            $CostModelQuery->change_time   = time();
            $CostModelQuery->admin_id      = $MemberPdtRes->admin_id;
            if (!$CostModelQuery->update(false)) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '成本表信息更新失败'
                ];
                return $this->renderJSON($arrReturn);
            }
        }
        #用户信息
        $userinfosql = "select * from user_member where u_id = '" . $MemberPdtRes->user_id . "'";
        $userinfo    = Yii::$app->db->createCommand($userinfosql)->queryOne();
        $usermail    = $userinfo['email'];
        $username    = $userinfo['truename'] ? $userinfo['truename'] : $userinfo['email'];
        $mailtype    = "业务：升级设备已配置";
        #如果 price 小于0 则进行对用户退款
        #如果付款金额小于0 则代表要么不需要付款，要么退款给用户，不走订单，直接走服务工单
        if ($service_after_config['price'] < 0) {
            $newbalance      = -1 * $service_after_config['price'];
            $refundUserMoney = Yii::$app->db->createCommand("update user_member set balance = balance + " . $newbalance . " where u_id = '" . $ServiceOrderRes->member_id . "'")->execute();

            #加入流水记录
            $FinanceManageModel                        = new FinanceManage();
            $FinanceManageModel->user_id               = $MemberPdtRes->user_id;
            $FinanceManageModel->user_name             = $MemberPdtRes->user_name;
            $FinanceManageModel->admin_id              = $ServiceOrderRes->admin_id;
            $FinanceManageModel->admin_name            = $ServiceOrderRes->admin_name;
            $FinanceManageModel->trade_front_usermoney = $userinfo['balance'];
            $FinanceManageModel->trade_money           = $service_after_config['price'];
            $FinanceManageModel->trade_after_usermoney = $userinfo['balance'] + $newbalance;
            $FinanceManageModel->trade_imex            = '支出';
            $FinanceManageModel->trade_type            = Yii::$app->params['trade_type']['change_config_refund']['name'];
            $FinanceManageModel->trade_des             = '变更配置退款';
            $FinanceManageModel->trade_createtime      = time();
            $FinanceManageModel->trade_unionid         = $ServiceOrderRes->unionid;
            $FinanceManageModel->serviceid             = $ServiceOrderRes->serviceid;
            $FinanceManageModel->trade_point           = 'N';
            #新增
            $insertFinance = $FinanceManageModel->insert();

            if ($MemberPdtRes->update(false) && $ServiceOrderRes->update() && $refundUserMoney && $insertFinance) {

                $reportRes = new FinanceReport($ServiceOrderRes->unionid, $MemberPdtRes->user_id, null, $ServiceOrderRes->serviceid, $FinanceManageModel->id, 'change_config_refund');
                if ($reportRes) {
                    $transaction->commit();
                    NoticeUserMail::createRecord($mailtype, "您之前提交的设备升级服务现在已经处理完成，请及时前往网站查看", null, null, $usermail, $username, $mailtype, null, "N");
                    return $this->renderJSON([
                        'status' => 1,
                        'info'   => "配置变更确认成功"
                    ]);
                } else {
                    $transaction->rollBack();
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => "配置变更确认失败"
                    ]);
                }

            } else {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => "配置变更确认失败"
                ]);
            }
        } else {
            if ($MemberPdtRes->update(false) && $ServiceOrderRes->update()) {
                $transaction->commit();
                NoticeUserMail::createRecord($mailtype, "您之前提交的设备升级服务现在已经处理完成，请及时前往网站查看", null, null, $usermail, $username, $mailtype, null, "N");
                return $this->renderJSON([
                    'status' => 1,
                    'info'   => "配置变更确认成功"
                ]);
            } else {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => "配置变更确认失败"
                ]);
            }
        }
    }

    /**
     *机器变更完成
     */
    public function actionMachinechange()
    {
        $ServiceOrderModel      = new ServiceOrder();
        $MemberPdtModel         = new MemberPdt();
        $IdlePdtModel           = new IdlePdt();
        $PdtHaveuselogModel     = new PdtHaveuselog();
        $PdtProvideruselogModel = new PdtProvideruselog();
        $ProviderModel          = new Provider();
        $InitialAccountModel    = new InitialAccount();
        $PdtIpModel             = new PdtIp();

        $post = $this->post();
        if (empty($post['name']) || empty($post['pwd']) || empty($post['port'])) {
            $arrReturn = [
                'status' => 0,
                'info'   => '产品登录账户信息不能为空'
            ];
            return $this->renderJSON($arrReturn);
        }
        #开启事务
        $transaction = Yii::$app->db->beginTransaction();
        #获取服务工单 对象
        $ServiceOrderRes = $ServiceOrderModel->findOne(['serviceid' => $post['serviceid']]);
        $time            = time();
        #更新服务工单状态和完成时间
        $ServiceOrderRes->service_time_end = $time;
        $ServiceOrderRes->service_status   = "完成工单";
        #获取变更前后的配置
        $service_front_config = json_decode($ServiceOrderRes->service_front_config, true);
        $service_after_config = json_decode($ServiceOrderRes->service_after_config, true);
        #获取用户产品信息对象
        $MemberPdtRes = $MemberPdtModel->findOne(['unionid' => $ServiceOrderRes['unionid']]);
        #更新登录账户信息
        $InitialAccountRes              = $InitialAccountModel->findOne(['unionid' => $MemberPdtRes->unionid]);
        $InitialAccountRes->name        = $post['name'];
        $InitialAccountRes->pwd         = $post['pwd'];
        $InitialAccountRes->port        = $post['port'];
        $InitialAccountRes->create_time = time();
        if (!$InitialAccountRes->update()) {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => '登录账户信息更新失败'
            ];
            return $this->renderJSON($arrReturn);
        }

        #如果更换前产品属于自有产品库是自有的  将修改为闲置服务器和闲置状态	同时添加自有分配记录
        if ($MemberPdtRes->idle_id != "" || $MemberPdtRes->idle_id != null) {
            #获取更换前产品对象
            $IdlePdtQuery = $IdlePdtModel->findOne($MemberPdtRes->idle_id);
            #更改状态
            $IdlePdtModel->domodifystatus($MemberPdtRes->idle_id, 1, 0);
            #增加记录
            $addUseLogRes = $PdtHaveuselogModel->doadd($IdlePdtQuery, $MemberPdtRes);
            if (!$addUseLogRes) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '添加分配记录失败'
                ];
                return $this->renderJSON($arrReturn);
            }
        } else {
            #获取这个用户产品最后的更换机器的时间
            $sql       = "(select end_time from pdt_haveuselog where unionid = '" . $MemberPdtRes->unionid . "') UNION ALL (select end_time from pdt_provideruselog where unionid = '" . $MemberPdtRes->unionid . "') 
			ORDER BY end_time DESC LIMIT 1;";
            $UselogRes = Yii::$app->db->createCommand($sql)->queryScalar();  ###echo $UselogRes;die;
            #获取供应商信息
            $ProviderRes = $ProviderModel->find()->where(['id' => $MemberPdtRes->provider_id])->asArray()->one();
            #新增记录
            if ($UselogRes) {
                $ProviderRes['start_time'] = $UselogRes;
            } else {
                $ProviderRes['start_time'] = $MemberPdtRes->start_time;
            }
            $addProviderUseLogRes = $PdtProvideruselogModel->doadd($ProviderRes, $MemberPdtRes);
            if (!$addProviderUseLogRes) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '添加分配记录失败'
                ];
                return $this->renderJSON($arrReturn);
            }
        }

        #如果更换后机器是自有的，将状态改为正在使用中 。同时 如果更换时，IP发生变化，将对应的IP 改为闲置或者使用，同时将新的IP给产品库
        if ($service_after_config['servicerprovider'] == 0 && $service_after_config['idle_id'] != "") {

            $IdlePdtModel->domodifystatus($service_after_config['idle_id'], 2, 1);
            $IdlePdtRes      = $IdlePdtModel->findOne($service_after_config['idle_id']);
            $oldIP           = json_decode($IdlePdtRes->ip2, true);
            $bgIPArray       = array_unique(array_merge($service_after_config['ip2'], $oldIP));
            $result_bangding = array_diff($bgIPArray, $oldIP);                        #需要改为使用中的
            $result_jiechu   = array_diff($bgIPArray, $service_after_config['ip2']);  #需要改为闲置的

            if (!empty($result_bangding)) {
                #$res1 = $PdtIpModel->modifyIPStatus($result_bangding,"1");
                $res1           = $PdtIpModel->updateAll(['status' => 1, 'update_time' => time()], ['ip' => $result_bangding]);
                $IpRecordModel  = new IpRecord();
                $describe       = "管理员：" . $this->getAdminInfo('uname') . "对业务：" . $ServiceOrderRes->unionid . " 通过更换机器，进行了IP使用";
                $addIpRecordRes = $IpRecordModel->addrecord($ServiceOrderRes->unionid, null, $result_bangding, $describe, $this->getAdminInfo('admin_id'), $this->getAdminInfo('uname'));
                if (!$addIpRecordRes) {
                    #回滚事务
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => "IP记录添加失败"
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }
            if (!empty($result_jiechu)) {
                #$res2 = $PdtIpModel->modifyIPStatus($result_jiechu,"0");
                $res2 = $PdtIpModel->updateAll(['status' => 0, 'update_time' => time()], ['ip' => $result_jiechu]);
            }
            #将新的值赋予新选择的自有产品
            $IdlePdtRes->attributes = $service_after_config;
            $IdlePdtRes->start_time = $time;
            $IdlePdtRes->config     = json_encode($service_after_config['config'], JSON_UNESCAPED_UNICODE);
            $IdlePdtRes->ip         = json_encode($service_after_config['ip']);
            $IdlePdtRes->ip2        = json_encode($service_after_config['ip2']);
            if (!$IdlePdtRes->update()) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '自有产品库信息数据同步更新失败'
                ];
                return $this->renderJSON($arrReturn);
            }
        }
        #更新成本信息表
        #查询产品在成本表在当天是否有记录
        $CostModel = new Cost();
        $start     = strtotime(date('Y-m-d 00:00:00'));
        $end       = strtotime(date('Y-m-d H:i:s'));
        $CostRes   = $CostModel->find()->where(['unionid' => $MemberPdtRes->unionid])->andwhere(['>', 'change_time', $start])->andWhere(['<', 'change_time', $end])->asArray()->all();

        if (empty($CostRes)) {
            $CostModel->unionid       = $MemberPdtRes->unionid;
            $CostModel->basic_money   = $service_after_config['real_cost_price'];
            $CostModel->currency_type = $service_after_config['currency_type'];
            $CostModel->two_money     = "";
            $CostModel->change_time   = $time;
            $CostModel->admin_id      = $MemberPdtRes->admin_id;
            if (!$CostModel->insert()) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '成本表信息更新失败'
                ];
                return $this->renderJSON($arrReturn);
            }
        } else {
            $CostModelQuery                = $CostModel->find()->where(['unionid' => $MemberPdtRes->unionid])->andwhere(['>', 'change_time', $start])->andWhere(['<', 'change_time', $end])->one();#print_r($CostModelQuery);die();
            $CostModelQuery->unionid       = $MemberPdtRes->unionid;
            $CostModelQuery->basic_money   = $service_after_config['real_cost_price'];
            $CostModelQuery->two_money     = "";
            $CostModelQuery->currency_type = $service_after_config['currency_type'];
            $CostModelQuery->change_time   = $time;
            $CostModelQuery->admin_id      = $MemberPdtRes->admin_id;
            if (!$CostModelQuery->update()) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '成本表信息更新失败'
                ];
                return $this->renderJSON($arrReturn);
            }
        }
        #如果付款金额小于0 则代表要么不需要付款，要么退款给用户，不走订单，直接走服务工单
        if ($service_after_config['difference_price'] <= 0) {
            $MemberPdtRes->sell_price = $service_after_config['sell_price'];
            $MemberPdtRes->cost_price = $service_after_config['cost_price'];
        }
        unset($service_after_config['sell_price']);
        unset($service_after_config['cost_price']);
        unset($service_after_config['payment_cycle']);
        #将新的值赋予用户产品信息
        $MemberPdtRes->attributes = $service_after_config;
        $MemberPdtRes->status     = 1;
        $MemberPdtRes->config     = json_encode($service_after_config['config'], JSON_UNESCAPED_UNICODE);
        #$MemberPdtRes->bandwidth = $service_after_config['config']['requirement_bandwidth'];
        $MemberPdtRes->ip          = json_encode($service_after_config['ip']);
        $MemberPdtRes->ip2         = json_encode($service_after_config['ip2']);
        $MemberPdtRes->provider_id = $service_after_config['provider_id'];
        #获取用户信息
        $userinfosql = "select * from user_member where u_id = '" . $MemberPdtRes->user_id . "'";
        $userinfo    = Yii::$app->db->createCommand($userinfosql)->queryOne();
        $usermail    = $userinfo['email'];
        $username    = $userinfo['truename'] ? $userinfo['truename'] : $userinfo['email'];
        $mailtype    = "业务：变更设备已配置";
        #如果小于0 则进行用户退款

        #如果测试ID存在，那么则更新测试服务器状态为已使用
        if ($service_after_config['testid']) {
            $TestServerModel        = new TestServer();
            $TestServerRes          = $TestServerModel->findOne($service_after_config['testid']);
            $TestServerRes->status  = 1;
            $UpdateTestServerStatus = $TestServerRes->update();

            if ($TestServerRes->servicerprovider == 0) {
                #自有机器，则需要把机器更改属性
                $idleModelRes               = $IdlePdtModel->findOne($TestServerRes->idle_id);
                $idleModelRes->attribute_id = 2;
                $idleModelRes->status       = 1;
                $idleModelUpdate            = $idleModelRes->update();
            }
        }


        if ($service_after_config['difference_price'] < 0) {
            #更新用户余额
            $newbalance      = -1 * $service_after_config['difference_price'];
            $refundUserMoney = Yii::$app->db->createCommand("update user_member set balance = balance + " . $newbalance . " where u_id = '" . $ServiceOrderRes->member_id . "'")->execute();

            #记录金额流水，加入流水
            $FinanceManageModel                        = new FinanceManage();
            $FinanceManageModel->user_id               = $MemberPdtRes->user_id;
            $FinanceManageModel->user_name             = $MemberPdtRes->user_name;
            $FinanceManageModel->admin_id              = $ServiceOrderRes->admin_id;
            $FinanceManageModel->admin_name            = $ServiceOrderRes->admin_name;
            $FinanceManageModel->trade_front_usermoney = $userinfo['balance'];
            $FinanceManageModel->trade_money           = $service_after_config['difference_price'];
            $FinanceManageModel->trade_after_usermoney = $userinfo['balance'] + $newbalance;
            $FinanceManageModel->trade_imex            = '支出';
            $FinanceManageModel->trade_type            = Yii::$app->params['trade_type']['replace_machine_refund']['name'];
            $FinanceManageModel->trade_des             = '更换机器差价退款';
            $FinanceManageModel->trade_createtime      = time();
            $FinanceManageModel->trade_unionid         = $ServiceOrderRes->unionid;
            $FinanceManageModel->trade_point           = 'N';

            $insertFinance = $FinanceManageModel->insert();

            if (!$MemberPdtRes->update(false)) {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => '用户产品信息更新失败'
                ]);
            }
            if ($ServiceOrderRes->update() && $refundUserMoney && $insertFinance) {

                $reportRes = new FinanceReport($ServiceOrderRes->unionid, $MemberPdtRes->user_id, null, $ServiceOrderRes->serviceid, $FinanceManageModel->id, 'replace_machine_refund');

                if ($reportRes) {
                    $transaction->commit();
                    NoticeUserMail::createRecord($mailtype, "您之前提交的设备变更服务现在已经处理完成，请及时前往网站查看", null, null, $usermail, $username, $mailtype, null, "N");
                    return $this->renderJSON([
                        'status' => 1,
                        'info'   => "更换机器确认成功"
                    ]);
                } else {
                    $transaction->rollBack();
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => "更换机器确认失败"
                    ]);
                }

            } else {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => "更换机器确认失败"
                ]);
            }
        } else {
            if (!$MemberPdtRes->update(false)) {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => '用户产品信息更新失败'
                ]);
            }
            if ($ServiceOrderRes->update()) {
                $transaction->commit();
                NoticeUserMail::createRecord($mailtype, "您之前提交的设备变更服务现在已经处理完成，请及时前往网站查看", null, null, $usermail, $username, $mailtype, null, "N");
                return $this->renderJSON([
                    'status' => 1,
                    'info'   => "更换机器确认成功"
                ]);
            } else {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => "更换机器确认失败"
                ]);
            }
        }
    }

    /**
     *更换IP
     */
    public function actionChangeip()
    {
        Yii::$app->request->isAjax || die('error');
        $ServiceOrderModel = new ServiceOrder();
        $MemberPdtModel    = new MemberPdt();
        $IdlePdtModel      = new IdlePdt();
        $PdtIpModel        = new PdtIp();

        $serviceid            = $this->post('serviceid');
        $ServiceOrderRes      = $ServiceOrderModel->findOne(['serviceid' => $serviceid]);        #获取服务工单信息对象
        $service_after_config = json_decode($ServiceOrderRes->service_after_config, true);
        $MemberPdtRes         = $MemberPdtModel->findOne(['unionid' => $ServiceOrderRes->unionid]); #获取用户产品信息对象
        #开启事务
        $transaction = Yii::$app->db->beginTransaction();
        #如果为自有产品时，同步产品库中IP信息，IP库信息
        if ($MemberPdtRes->idle_id != "" && $MemberPdtRes->servicerprovider == "0") {
            $IdlePdtRes      = $IdlePdtModel->findOne(['id' => $MemberPdtRes->idle_id]);
            $IdlePdtRes->ip  = json_encode($service_after_config['ip']);
            $IdlePdtRes->ip2 = json_encode($service_after_config['ip2']);
            $updateIdleRes   = $IdlePdtRes->update();
            if (!$updateIdleRes) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '更新自有产品信息失败'
                ];
                return $this->renderJSON($arrReturn);
            }
            #更新IP库中IP的状态
            $oldIP           = json_decode($MemberPdtRes->ip2, true);
            $newIp           = $service_after_config['ip2'];
            $bgIPArray       = array_unique(array_merge($newIp, $oldIP));
            $result_bangding = array_diff($bgIPArray, $oldIP);       #需要改为使用中的
            $result_jiechu   = array_diff($bgIPArray, $newIp);       #需要改为闲置的
            if (!empty($result_bangding)) {
                #$res1 = $PdtIpModel->modifyIPStatus($result_bangding,"1");
                $res1 = $PdtIpModel->updateAll(['status' => 1, 'update_time' => time()], ['ip' => $result_bangding]);

                $IpRecordModel  = new IpRecord();
                $describe       = "管理员：" . $this->getAdminInfo('uname') . "对业务：" . $ServiceOrderRes->unionid . " 通过更换IP操作，进行了IP使用";
                $addIpRecordRes = $IpRecordModel->addrecord($ServiceOrderRes->unionid, null, $result_bangding, $describe, $this->getAdminInfo('admin_id'), $this->getAdminInfo('uname'));
                if (!$addIpRecordRes) {
                    #回滚事务
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => "IP记录添加失败"
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }
            if (!empty($result_jiechu)) {
                #$res2 = $PdtIpModel->modifyIPStatus($result_jiechu,"0");
                $res2 = $PdtIpModel->updateAll(['status' => 0, 'update_time' => time()], ['ip' => $result_jiechu]);
            }
        }
        #更新用户产品信息
        $MemberPdtRes->ip     = json_encode($service_after_config['ip']);
        $MemberPdtRes->ip2    = json_encode($service_after_config['ip2']);
        $MemberPdtRes->status = 1;

        $updateMemberPdtinfo = $MemberPdtRes->update(false);
        #更新服务工单信息
        $time                              = time();
        $ServiceOrderRes->service_time_end = $time;
        $ServiceOrderRes->service_status   = "完成工单";
        $updateServiceOrderinfo            = $ServiceOrderRes->update();

        #获取用户信息
        $userinfosql = "select * from user_member where u_id = '" . $MemberPdtRes->user_id . "'";
        $userinfo    = Yii::$app->db->createCommand($userinfosql)->queryOne();
        $usermail    = $userinfo['email'];
        $username    = $userinfo['truename'] ? $userinfo['truename'] : $userinfo['email'];
        $mailtype    = "业务：变更IP已配置";


        if ($updateMemberPdtinfo && $updateServiceOrderinfo) {
            NoticeUserMail::createRecord($mailtype, "您之前提交的IP变更服务现在已经处理完成，请及时前往网站查看", null, null, $usermail, $username, $mailtype, null, "N");
            $transaction->commit();
            $arrReturn = [
                'status' => 1,
                'info'   => 'IP更换确认完成'
            ];
        } else {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => '系统异常'
            ];
        }
        return $this->renderJSON($arrReturn);
    }

    /**
     *取消服务工单
     */
    public function actionCancelorder()
    {
        Yii::$app->request->isAjax || die('error');
        $TradeModel         = new Trade();
        $ServiceOrderModel  = new ServiceOrder();
        $MemberPdtModel     = new MemberPdt();
        $UserMemberModel    = new UserMember();
        $FinanceManageModel = new FinanceManage();
        $PdtIpModel         = new PdtIp();
        $IdlePdtModel       = new IdlePdt();
        $TestServerModel    = new TestServer();

        $serviceid            = $this->post('serviceid');
        $ServiceOrderRes      = $ServiceOrderModel->findOne(['serviceid' => $serviceid]);#
        $service_after_config = json_decode($ServiceOrderRes->service_after_config, true);
        $service_front_config = json_decode($ServiceOrderRes->service_front_config, true);
        $service_type_do      = $ServiceOrderRes->service_type_do;
        #查询出产品信息对象和订单信息
        $MemberPdtRes = $MemberPdtModel->findone(['unionid' => $ServiceOrderRes['unionid']]);
        $TradeRes     = $TradeModel->find()->where(['serviceid' => $serviceid])->asArray()->one();
        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        #查询取消服务工单是否为审核
        $cancelOrder = Yii::$app->db->createCommand("select nv_type from notice_manage where nv_key = 'cancel_service_order'")->queryScalar();

        #判断类型  根据类型进行各类取消操作
        if ($service_type_do == "新购") {

            #获取用户信息对象
            $UserMemberRes          = $UserMemberModel->findOne(['u_id' => $TradeRes['member_id']]);
            $frontmoney             = $UserMemberRes->balance;
            $UserMemberRes->balance = $UserMemberRes->balance + $TradeRes['trade_price_payment'];
            #添加新购取消返款流水信息
            $FinanceManageModel->user_id               = $TradeRes['member_id'];
            $FinanceManageModel->user_name             = $TradeRes['member_name'];
            $FinanceManageModel->admin_id              = $TradeRes['admin_id'];
            $FinanceManageModel->admin_name            = $TradeRes['admin_name'];
            $FinanceManageModel->trade_front_usermoney = $frontmoney;
            $FinanceManageModel->trade_money           = $TradeRes['trade_price_real'];
            $FinanceManageModel->trade_after_usermoney = $UserMemberRes->balance;
            $FinanceManageModel->trade_imex            = '支出';
            $FinanceManageModel->trade_type            = Yii::$app->params['trade_type']['cancel_order']['name'];
            $FinanceManageModel->trade_des             = "机器新购取消,账户退款";
            $FinanceManageModel->trade_createtime      = time();
            $FinanceManageModel->trade_unionid         = $TradeRes['unionid'];
            $FinanceManageModel->trade_orderid         = $TradeRes['trade_orderid'];
            $FinanceManageModel->serviceid             = $serviceid;
            $FinanceManageModel->trade_point           = 'N';
            $FinanceManageModel->is_audit              = 'N';

            #如果是后付款操作
            if ($TradeRes['trade_status'] == '后付款') {
                $FinanceManageModel->trade_des             = '新购取消,由于订单付款方式为<font style="color:#f00;font-weight:bold;">后付款</font>,不进行用户退款';
                $FinanceManageModel->trade_front_usermoney = $frontmoney;
                $FinanceManageModel->trade_money           = 0;
                $FinanceManageModel->trade_after_usermoney = $frontmoney;
            }

            $arr                = array(
                'user_id'               => $FinanceManageModel->user_id,
                'user_name'             => $FinanceManageModel->user_name,
                'admin_id'              => $FinanceManageModel->admin_id,
                'admin_name'            => $FinanceManageModel->admin_name,
                'trade_front_usermoney' => $FinanceManageModel->trade_front_usermoney,
                'trade_money'           => $FinanceManageModel->trade_money,
                'trade_after_usermoney' => $FinanceManageModel->trade_after_usermoney,
                'trade_imex'            => $FinanceManageModel->trade_imex,
                'trade_type'            => $FinanceManageModel->trade_type,
                'trade_des'             => $FinanceManageModel->trade_des,
                'trade_createtime'      => $FinanceManageModel->trade_createtime,
                'trade_unionid'         => $FinanceManageModel->trade_unionid,
                'trade_orderid'         => $FinanceManageModel->trade_orderid,
                'serviceid'             => $FinanceManageModel->serviceid,
                'trade_point'           => $FinanceManageModel->trade_point,
                'is_audit'              => $FinanceManageModel->is_audit,
            );
            $insert_key_array   = array();
            $insert_value_array = array();
            foreach ($arr as $key => $val) {
                $insert_key_array[]   = $key;
                $insert_value_array[] = "'" . $val . "'";
            }
            $insert_key   = implode(',', $insert_key_array);
            $insert_value = implode(',', $insert_value_array);
            ####
            if ($cancelOrder == '审核') {
                if ($TradeRes['trade_status'] == '后付款') {
                    #添加新购取消返款流水信息的sql
                    $dataContent[] = [
                        'sql' => base64_encode("insert finance_manage ($insert_key) values ($insert_value)")
                    ];
                    #如果后付款的订单改为取消的sql
                    $dataContent[] = [
                        'sql' => base64_encode("update trade set trade_status = '已取消' where trade_orderid = '" . $TradeRes['trade_orderid'] . "'")
                    ];
                } else {
                    #添加新购取消返款流水信息的sql
                    $dataContent[] = [
                        'sql' => base64_encode("insert finance_manage ($insert_key) values ($insert_value)")
                    ];
                    #退款。修改用户余额的sql
                    $dataContent[] = [
                        'sql' => base64_encode("update user_member set balance = balance + " . $TradeRes['trade_price_payment'] . " where u_id = " . $TradeRes['member_id'])
                    ];
                }
                #如果已经选择过 测试服务器，即需要将选择的测试服务器的状态改为0（测试服务器）
                if (isset($service_after_config['testid'])) {
                    $testid        = $service_after_config['testid'];
                    $dataContent[] = [
                        'sql' => base64_encode("update test_server set status = '0' where id = " . $testid)
                    ];
                }
                #
            } else {

                if ($TradeRes['trade_status'] == '后付款') {
                    #将订单改为取消
                    $cancelServiceOrder = Yii::$app->db->createCommand("update trade set trade_status = '已取消' where trade_orderid = '" . $TradeRes['trade_orderid'] . "'")->execute();
                    if (!$FinanceManageModel->insert(false) || !$cancelServiceOrder) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => '系统异常'
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                } else {
                    #退款。更新用户余额
                    if (!$FinanceManageModel->insert(false) || !$UserMemberRes->update()) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => '系统异常'
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
                #如果已经选择过 测试服务器，即需要将选择的测试服务器的状态改为0（测试服务器）
                if (isset($service_after_config['testid'])) {
                    $testid                  = $service_after_config['testid'];
                    $TestServerQuery         = $TestServerModel->findone($testid);
                    $TestServerQuery->status = '0';
                    if (!$TestServerQuery->update(false)) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => '测试服务器更新失败'
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
            }
        } elseif ($service_type_do == "变更配置") {
            #取消变更配置时，将用户产品状态以及价格更正为正常状态和之前价格
            #因为变更配置存在 生成订单和不生成订单
            #当有生成订单时 才会退款
            #
            $trade_config = json_decode($TradeRes['trade_config'], true);
            #重新设置用户产品状态、销售价格和配件成本价格
            $MemberPdtRes->status             = 1;
            $MemberPdtRes->sell_price         = $trade_config['old_sell_price'];
            $MemberPdtRes->upgrade_cost_price = $trade_config['old_upgrade_cost_price'];

            if (!empty($TradeRes)) {
                #获取用户信息 更新用户余额
                $UserMemberRes          = $UserMemberModel->findOne(['u_id' => $TradeRes['member_id']]);
                $frontmoney             = $UserMemberRes->balance;
                $UserMemberRes->balance = $UserMemberRes->balance + $TradeRes['trade_price_payment'];
                #
                #添加变更配置订单取消返款流水信息
                $FinanceManageModel->user_id               = $TradeRes['member_id'];
                $FinanceManageModel->user_name             = $TradeRes['member_name'];
                $FinanceManageModel->admin_id              = $TradeRes['admin_id'];
                $FinanceManageModel->admin_name            = $TradeRes['admin_name'];
                $FinanceManageModel->trade_front_usermoney = $frontmoney;
                $FinanceManageModel->trade_money           = $TradeRes['trade_price_real'];
                $FinanceManageModel->trade_after_usermoney = $UserMemberRes->balance;
                $FinanceManageModel->trade_imex            = '支出';
                $FinanceManageModel->trade_type            = Yii::$app->params['trade_type']['cancel_order']['name'];
                $FinanceManageModel->trade_des             = "变更配置取消,账户退款";
                $FinanceManageModel->trade_createtime      = time();
                $FinanceManageModel->trade_unionid         = $TradeRes['unionid'];
                $FinanceManageModel->trade_orderid         = $TradeRes['trade_orderid'];
                $FinanceManageModel->serviceid             = $serviceid;
                $FinanceManageModel->trade_point           = 'N';
                $FinanceManageModel->is_audit              = 'N';
                #如果是后付款
                if ($TradeRes['trade_status'] == '后付款') {
                    $FinanceManageModel->trade_des             = '变更配置取消,由于订单付款方式为<font style="color:#f00;font-weight:bold;">后付款</font>,不进行用户退款';
                    $FinanceManageModel->trade_front_usermoney = $frontmoney;
                    $FinanceManageModel->trade_money           = 0;
                    $FinanceManageModel->trade_after_usermoney = $frontmoney;
                }

                $arr                = array(
                    'user_id'               => $FinanceManageModel->user_id,
                    'user_name'             => $FinanceManageModel->user_name,
                    'admin_id'              => $FinanceManageModel->admin_id,
                    'admin_name'            => $FinanceManageModel->admin_name,
                    'trade_front_usermoney' => $FinanceManageModel->trade_front_usermoney,
                    'trade_money'           => $FinanceManageModel->trade_money,
                    'trade_after_usermoney' => $FinanceManageModel->trade_after_usermoney,
                    'trade_imex'            => $FinanceManageModel->trade_imex,
                    'trade_type'            => $FinanceManageModel->trade_type,
                    'trade_des'             => $FinanceManageModel->trade_des,
                    'trade_createtime'      => $FinanceManageModel->trade_createtime,
                    'trade_unionid'         => $FinanceManageModel->trade_unionid,
                    'trade_orderid'         => $FinanceManageModel->trade_orderid,
                    'serviceid'             => $FinanceManageModel->serviceid,
                    'trade_point'           => $FinanceManageModel->trade_point,
                    'is_audit'              => $FinanceManageModel->is_audit,
                );
                $insert_key_array   = array();
                $insert_value_array = array();
                foreach ($arr as $key => $val) {
                    $insert_key_array[]   = $key;
                    $insert_value_array[] = "'" . $val . "'";
                }
                $insert_key   = implode(',', $insert_key_array);
                $insert_value = implode(',', $insert_value_array);
                #if start
                if ($cancelOrder == '审核') {
                    if ($TradeRes['trade_status'] == '后付款') {
                        #添加变更配置取消返款流水信息的sql
                        $dataContent[] = [
                            'sql' => base64_encode("insert finance_manage ($insert_key) values ($insert_value)")
                        ];
                        #如果后付款的订单改为取消的sql
                        $dataContent[] = [
                            'sql' => base64_encode("update trade set trade_status = '已取消' where trade_orderid = '" . $TradeRes['trade_orderid'] . "'")
                        ];
                    } else {
                        #添加变更配置取消返款流水信息的sql
                        $dataContent[] = [
                            'sql' => base64_encode("insert finance_manage ($insert_key) values ($insert_value)")
                        ];
                        #退款。修改用户余额的sql
                        $dataContent[] = [
                            'sql' => base64_encode("update user_member set balance = balance + " . $TradeRes['trade_price_payment'] . " where u_id = " . $TradeRes['member_id'])
                        ];
                    }
                } else {
                    if ($TradeRes['trade_status'] == '后付款') {
                        #如果为后付款的订单，订单改为已取消
                        $cancelServiceOrder = Yii::$app->db->createCommand("update trade set trade_status = '已取消' where trade_orderid = '" . $TradeRes['trade_orderid'] . "'")->execute();
                        if (!$FinanceManageModel->insert(false) || !$cancelServiceOrder) {
                            $transaction->rollBack();
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '系统异常'
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                    } else {
                        if (!$FinanceManageModel->insert(false) || !$UserMemberRes->update()) {
                            $transaction->rollBack();
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '系统异常'
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                    }
                }
                #if end
            }

            #更新用户产品
            if ($cancelOrder == '审核') {
                #有差价时（有订单）
                if (!empty($TradeRes)) {
                    $dataContent[] = [
                        'sql' => base64_encode("update member_pdt set sell_price = " . $trade_config['old_sell_price'] . ", upgrade_cost_price = " . $trade_config['old_upgrade_cost_price'] . ", status = 1  where unionid = '" . $ServiceOrderRes['unionid'] . "'")
                    ];
                } else {
                    #无差价时（无订单）
                    $dataContent[] = [
                        'sql' => base64_encode("update member_pdt set sell_price = " . $service_after_config['old_sell_price'] . ", upgrade_cost_price = " . $service_after_config['old_upgrade_cost_price'] . ", status = 1  where unionid = '" . $ServiceOrderRes['unionid'] . "'")
                    ];
                }
            } else {
                if (!$MemberPdtRes->update(false)) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '用户产品信息更新失败'
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }

            # end
        } elseif ($service_type_do == "退款") {
            if ($cancelOrder == '审核') {
                $dataContent[] = [
                    'sql' => base64_encode("update member_pdt set status = 1 where unionid = '" . $ServiceOrderRes['unionid'] . "'")
                ];
            } else {
                $MemberPdtRes->status = 1;
                if (!$MemberPdtRes->update()) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '系统异常'
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }

        } elseif ($service_type_do == "IP变更") {

            #当机器为自有时
            if ($MemberPdtRes->idle_id != "" && $MemberPdtRes->servicerprovider == 0) {
                $afterIP         = $service_after_config['ip2'];           #获取变更后的IP集
                $frontIP         = json_decode($MemberPdtRes['ip2'], true);#获取变更前的IP集
                $bgIPArray       = array_unique(array_merge($afterIP, $frontIP));
                $result_bangding = array_diff($bgIPArray, $frontIP);   #之前应改为使用中的
                $result_jiechu   = array_diff($bgIPArray, $afterIP);   #之前应改为闲置的
                #
                if (!empty($result_bangding)) {
                    if ($cancelOrder == '审核') {
                        $iplist = "";
                        if (is_array($result_bangding)) {
                            foreach ($result_bangding as $vv) {
                                $iplist .= '\'' . $vv . '\',';
                            }
                            $iplist        = substr($iplist, 0, -1);
                            $dataContent[] = [
                                'sql' => base64_encode("update pdt_ip set status = 0 where ip in(" . $iplist . ")")
                            ];
                        } else {
                            $dataContent[] = [
                                'sql' => base64_encode("update pdt_ip set status = 0 where ip='" . $result_bangding . "'")
                            ];
                        }
                    } else {
                        #$res1 = $PdtIpModel->modifyIPStatus($result_bangding,"0");
                        $res1 = $PdtIpModel->updateAll(['status' => 0, 'update_time' => time()], ['ip' => $result_bangding]);
                    }
                }
                #
                if (!empty($result_jiechu)) {
                    if ($cancelOrder == '审核') {
                        $iplist = "";
                        if (is_array($result_jiechu)) {
                            foreach ($result_jiechu as $vv) {
                                $iplist .= '\'' . $vv . '\',';
                            }
                            $iplist        = substr($iplist, 0, -1);
                            $dataContent[] = [
                                'sql' => base64_encode("update pdt_ip set status = 1 where ip in(" . $iplist . ")")
                            ];
                        } else {
                            $dataContent[] = [
                                'sql' => base64_encode("update pdt_ip set status = 1 where ip='" . $result_jiechu . "'")
                            ];
                        }
                    } else {
                        #$res2 = $PdtIpModel->modifyIPStatus($result_jiechu,"1");
                        $res2 = $PdtIpModel->updateAll(['status' => 1, 'update_time' => time()], ['ip' => $result_jiechu]);
                    }
                }

            }

            #设置产品状态
            $MemberPdtRes->status = 1;
            if ($cancelOrder == '审核') {
                $dataContent[] = [
                    'sql' => base64_encode("update member_pdt set status = 1 where unionid = '" . $ServiceOrderRes['unionid'] . "'")
                ];
            } else {
                if (!$MemberPdtRes->update(false)) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '用户产品状态更新失败'
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }

        } elseif ($service_type_do == "更换机器") {

            if ($service_after_config['testid']) {
                $TestServerRes          = $TestServerModel->findOne($service_after_config['testid']);
                $TestServerRes->status  = 0;
                $UpdateTestServerStatus = $TestServerRes->update();

                //如果testid存在，那么一定会更新测试服务器状态，创建datacontent提供给审核使用

                $dataContent[] = [
                    'sql' => base64_encode("update test_server set status = '0' where id='" . $service_after_config['testid'] . "'")
                ];
            }


            #机器变更分为有订单和无订单（无订单表示无差价和有差价（0或者负数））
            #有订单表示有差价
            if (!empty($TradeRes)) {
                $trade_config = json_decode($TradeRes['trade_config'], true);
                #更新用户产品的销售价格，成本价格和配件成本价格
                $MemberPdtRes->sell_price         = $trade_config['old_sell_price'];
                $MemberPdtRes->cost_price         = $trade_config['old_cost_price'];
                $MemberPdtRes->upgrade_cost_price = $trade_config['old_upgrade_cost_price'];

                #获取用户信息 更新用户余额
                $UserMemberRes = $UserMemberModel->findOne(['u_id' => $TradeRes['member_id']]);
                $frontmoney    = $UserMemberRes->balance;
                #
                $UserMemberRes->balance = $UserMemberRes->balance + $TradeRes['trade_price_payment'];

                #取消机器更换时，生成取消更改退款的流水信息
                $FinanceManageModel->user_id               = $TradeRes['member_id'];
                $FinanceManageModel->user_name             = $TradeRes['member_name'];
                $FinanceManageModel->admin_id              = $TradeRes['admin_id'];
                $FinanceManageModel->admin_name            = $TradeRes['admin_name'];
                $FinanceManageModel->trade_front_usermoney = $frontmoney;
                $FinanceManageModel->trade_money           = $TradeRes['trade_price_real'];
                $FinanceManageModel->trade_after_usermoney = $UserMemberRes->balance;
                $FinanceManageModel->trade_imex            = '支出';
                $FinanceManageModel->trade_type            = Yii::$app->params['trade_type']['cancel_order']['name'];
                $FinanceManageModel->trade_des             = "更换机器取消,账户退款";
                $FinanceManageModel->trade_createtime      = time();
                $FinanceManageModel->trade_unionid         = $TradeRes['unionid'];
                $FinanceManageModel->trade_orderid         = $TradeRes['trade_orderid'];
                $FinanceManageModel->serviceid             = $serviceid;
                $FinanceManageModel->trade_point           = 'N';
                $FinanceManageModel->is_audit              = 'N';

                if ($TradeRes['trade_status'] == '后付款') {
                    $FinanceManageModel->trade_des             = '更换机器取消,由于订单付款方式为<font style="color:#f00;font-weight:bold;">后付款</font>,不进行用户退款';
                    $FinanceManageModel->trade_front_usermoney = $frontmoney;
                    $FinanceManageModel->trade_money           = 0;
                    $FinanceManageModel->trade_after_usermoney = $frontmoney;
                }

                $arr                = array(
                    'user_id'               => $FinanceManageModel->user_id,
                    'user_name'             => $FinanceManageModel->user_name,
                    'admin_id'              => $FinanceManageModel->admin_id,
                    'admin_name'            => $FinanceManageModel->admin_name,
                    'trade_front_usermoney' => $FinanceManageModel->trade_front_usermoney,
                    'trade_money'           => $FinanceManageModel->trade_money,
                    'trade_after_usermoney' => $FinanceManageModel->trade_after_usermoney,
                    'trade_imex'            => $FinanceManageModel->trade_imex,
                    'trade_type'            => $FinanceManageModel->trade_type,
                    'trade_des'             => $FinanceManageModel->trade_des,
                    'trade_createtime'      => $FinanceManageModel->trade_createtime,
                    'trade_unionid'         => $FinanceManageModel->trade_unionid,
                    'trade_orderid'         => $FinanceManageModel->trade_orderid,
                    'serviceid'             => $FinanceManageModel->serviceid,
                    'trade_point'           => $FinanceManageModel->trade_point,
                    'is_audit'              => $FinanceManageModel->is_audit,
                );
                $insert_key_array   = array();
                $insert_value_array = array();
                foreach ($arr as $key => $val) {
                    $insert_key_array[]   = $key;
                    $insert_value_array[] = "'" . $val . "'";
                }
                $insert_key   = implode(',', $insert_key_array);
                $insert_value = implode(',', $insert_value_array);

                #if start
                if ($cancelOrder == '审核') {
                    if ($TradeRes['trade_status'] == '后付款') {
                        #添加更换机器取消返款流水信息的sql
                        $dataContent[] = [
                            'sql' => base64_encode("insert finance_manage ($insert_key) values ($insert_value)")
                        ];
                        #如果后付款的订单改为取消的sql （#未考虑后付款已经付款）
                        $dataContent[] = [
                            'sql' => base64_encode("update trade set trade_status = '已取消' where trade_orderid = '" . $TradeRes['trade_orderid'] . "'")
                        ];
                    } else {
                        #添加更换机器取消返款流水信息的sql
                        $dataContent[] = [
                            'sql' => base64_encode("insert finance_manage ($insert_key) values ($insert_value)")
                        ];
                        #修改用户余额的sql
                        $dataContent[] = [
                            'sql' => base64_encode("update user_member set balance = balance + " . $TradeRes['trade_price_payment'] . " where u_id = " . $TradeRes['member_id'])
                        ];
                    }

                    #如果要更换的用户产品 为自有时  需要将对应的自有产品 改回之前状态
                    if ($MemberPdtRes->idle_id != "" && $MemberPdtRes->servicerprovider == 0) {
                        #如果更换前为自有，取消工单时 即改为之前状态
                        $dataContent[] = [
                            'sql' => base64_encode("update idle_pdt set status = 1 where id = " . $trade_config['old_idle_id'])
                        ];

                        #当新选择的机器为自有时 需要将状态改回之前，同时将涉及到的IP改回原状态
                        if ($MemberPdtRes->servicerprovider == 0) {
                            $newIdlePdtRes = $IdlePdtModel->findOne(['id' => $trade_config['idle_id']]);
                            #将涉及到的IP改回原状态
                            $afterIP         = $service_after_config['ip2'];            #获取变更后的IP集
                            $frontIP         = json_decode($newIdlePdtRes['ip2'], true);#获取变更前的IP集
                            $bgIPArray       = array_unique(array_merge($afterIP, $frontIP));
                            $result_bangding = array_diff($bgIPArray, $frontIP);   #之前应改为使用中的
                            $result_jiechu   = array_diff($bgIPArray, $afterIP);   #之前应改为闲置的
                            #
                            if (!empty($result_bangding)) {
                                $iplist = "";
                                if (is_array($result_bangding)) {
                                    foreach ($result_bangding as $vv) {
                                        $iplist .= '\'' . $vv . '\',';
                                    }
                                    $iplist        = substr($iplist, 0, -1);
                                    $dataContent[] = [
                                        'sql' => base64_encode("update pdt_ip set status = 0 where ip in(" . $iplist . ")")
                                    ];
                                } else {
                                    $dataContent[] = [
                                        'sql' => base64_encode("update pdt_ip set status = 0 where ip='" . $result_bangding . "'")
                                    ];
                                }
                            }
                            #
                            if (!empty($result_jiechu)) {
                                $iplist = "";
                                if (is_array($result_jiechu)) {
                                    foreach ($result_jiechu as $vv) {
                                        $iplist .= '\'' . $vv . '\',';
                                    }
                                    $iplist        = substr($iplist, 0, -1);
                                    $dataContent[] = [
                                        'sql' => base64_encode("update pdt_ip set status = 1 where ip in(" . $iplist . ")")
                                    ];
                                } else {
                                    $dataContent[] = [
                                        'sql' => base64_encode("update pdt_ip set status = 1 where ip='" . $result_jiechu . "'")
                                    ];
                                }
                            }
                            #将新选择的自有机器 改为闲置
                            $dataContent[] = [
                                'sql' => base64_encode("update idle_pdt set status = 0 where id = " . $trade_config['idle_id'])
                            ];
                        }
                    }
                } else {
                    #
                    if ($TradeRes['trade_status'] == '后付款') {
                        #订单为后付款时取消订单
                        $cancelServiceOrder = Yii::$app->db->createCommand("update trade set trade_status = '已取消' where trade_orderid = '" . $TradeRes['trade_orderid'] . "'")->execute();
                        if (!$FinanceManageModel->insert(false) || !$cancelServiceOrder) {
                            $transaction->rollBack();
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '系统异常'
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                    } else {
                        #不是后付款时。新增退款流水和更新用户余额（退款）
                        if (!$FinanceManageModel->insert(false) || !$UserMemberRes->update()) {
                            $transaction->rollBack();
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '系统异常'
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                    }
                    #
                    $MemberPdtRes->status = 1;
                    #如果要更换的用户产品 为自有时  需要将对应的自有产品 改回之前状态
                    if ($MemberPdtRes->idle_id != "" && $MemberPdtRes->servicerprovider == 0) {
                        $IdlePdtRes         = $IdlePdtModel->findOne(['id' => $trade_config['old_idle_id']]);
                        $IdlePdtRes->status = 1;
                        if (!$IdlePdtRes->update()) {
                            $transaction->rollBack();
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '更新自有产品状态失败'
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                        #当新选择的机器为自有时 需要将状态改回之前
                        if ($MemberPdtRes->servicerprovider == 0) {
                            $newIdlePdtRes = $IdlePdtModel->findOne(['id' => $trade_config['idle_id']]);
                            #将涉及到的IP改回原状态
                            $afterIP         = $service_after_config['ip2'];            #获取变更后的IP集
                            $frontIP         = json_decode($newIdlePdtRes['ip2'], true);#获取变更前的IP集
                            $bgIPArray       = array_unique(array_merge($afterIP, $frontIP));
                            $result_bangding = array_diff($bgIPArray, $frontIP);   #之前应改为使用中的
                            $result_jiechu   = array_diff($bgIPArray, $afterIP);   #之前应改为闲置的
                            #
                            if (!empty($result_bangding)) {
                                #$res1 = $PdtIpModel->modifyIPStatus($result_bangding,"0");
                                $res1 = $PdtIpModel->updateAll(['status' => 0, 'update_time' => time()], ['ip' => $result_bangding]);
                            }
                            if (!empty($result_jiechu)) {
                                #$res2 = $PdtIpModel->modifyIPStatus($result_jiechu,"1");
                                $res2 = $PdtIpModel->updateAll(['status' => 1, 'update_time' => time()], ['ip' => $result_jiechu]);
                            }
                            $newIdlePdtRes->status = 0;
                            if (!$newIdlePdtRes->update()) {
                                $transaction->rollBack();
                                $arrReturn = [
                                    'status' => 0,
                                    'info'   => '更新自有产品状态失败'
                                ];
                                return $this->renderJSON($arrReturn);
                            }
                        }
                    }
                }
                #if 审核 end
            } else {

                #不产生订单时
                #更新用户产品的销售价格，成本价格和配件成本价格(不产生订单，但相关价格还是有可能会变化，所以要更新回之前)
                $MemberPdtRes->sell_price         = $service_after_config['old_sell_price'];
                $MemberPdtRes->cost_price         = $service_after_config['old_cost_price'];
                $MemberPdtRes->upgrade_cost_price = $service_after_config['old_upgrade_cost_price'];

                if ($cancelOrder == '审核') {

                    #如果要更换的用户产品 为自有时  需要将对应的自有产品 改回之前状态
                    if ($MemberPdtRes->idle_id != "" && $MemberPdtRes->servicerprovider == 0) {
                        #如果更换前为自有，取消工单时 即改为之前状态
                        $dataContent[] = [
                            'sql' => base64_encode("update idle_pdt set status = 1 where id = " . $service_after_config['old_idle_id'])
                        ];

                        #当新选择的机器为自有时 需要将状态改回之前
                        if ($MemberPdtRes->servicerprovider == 0) {
                            $newIdlePdtRes = $IdlePdtModel->findOne(['id' => $service_after_config['idle_id']]);
                            #将涉及到的IP改回原状态
                            $afterIP         = $service_after_config['ip2'];            #获取变更后的IP集
                            $frontIP         = json_decode($newIdlePdtRes['ip2'], true);#获取变更前的IP集
                            $bgIPArray       = array_unique(array_merge($afterIP, $frontIP));
                            $result_bangding = array_diff($bgIPArray, $frontIP);   #之前应改为使用中的
                            $result_jiechu   = array_diff($bgIPArray, $afterIP);   #之前应改为闲置的
                            #
                            if (!empty($result_bangding)) {
                                $iplist = "";
                                if (is_array($result_bangding)) {
                                    foreach ($result_bangding as $vv) {
                                        $iplist .= '\'' . $vv . '\',';
                                    }
                                    $iplist        = substr($iplist, 0, -1);
                                    $dataContent[] = [
                                        'sql' => base64_encode("update pdt_ip set status = 0 where ip in(" . $iplist . ")")
                                    ];
                                } else {
                                    $dataContent[] = [
                                        'sql' => base64_encode("update pdt_ip set status = 0 where ip='" . $result_bangding . "'")
                                    ];
                                }
                            }
                            #
                            if (!empty($result_jiechu)) {
                                $iplist = "";
                                if (is_array($result_jiechu)) {
                                    foreach ($result_jiechu as $vv) {
                                        $iplist .= '\'' . $vv . '\',';
                                    }
                                    $iplist        = substr($iplist, 0, -1);
                                    $dataContent[] = [
                                        'sql' => base64_encode("update pdt_ip set status = 1 where ip in(" . $iplist . ")")
                                    ];
                                } else {
                                    $dataContent[] = [
                                        'sql' => base64_encode("update pdt_ip set status = 1 where ip='" . $result_jiechu . "'")
                                    ];
                                }
                            }
                            #将新选择的自有机器改回原来状态 改为闲置
                            $dataContent[] = [
                                'sql' => base64_encode("update idle_pdt set status = 0 where id = " . $service_after_config['idle_id'])
                            ];
                        }
                    }
                } else {
                    $MemberPdtRes->status = 1;
                    #如果要更换的用户产品 为自有时  需要将对应的自有产品 改回之前状态
                    if ($MemberPdtRes->idle_id != "" && $MemberPdtRes->servicerprovider == 0) {
                        $IdlePdtRes         = $IdlePdtModel->findOne(['id' => $service_after_config['old_idle_id']]);
                        $IdlePdtRes->status = 1;
                        if (!$IdlePdtRes->update()) {
                            $transaction->rollBack();
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '更新自有产品失败'
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                        #当新选择的机器为自有时 需要将状态改回之前
                        if ($MemberPdtRes->servicerprovider == 0) {
                            $newIdlePdtRes = $IdlePdtModel->findOne(['id' => $service_after_config['idle_id']]);
                            #将涉及到的IP改回原状态
                            $afterIP         = $service_after_config['ip2'];            #获取变更后的IP集
                            $frontIP         = json_decode($newIdlePdtRes['ip2'], true);#获取变更前的IP集
                            $bgIPArray       = array_unique(array_merge($afterIP, $frontIP));
                            $result_bangding = array_diff($bgIPArray, $frontIP);   #之前应改为使用中的
                            $result_jiechu   = array_diff($bgIPArray, $afterIP);   #之前应改为闲置的
                            #
                            if (!empty($result_bangding)) {
                                #$res1 = $PdtIpModel->modifyIPStatus($result_bangding,"0");
                                $res1 = $PdtIpModel->updateAll(['status' => 0, 'update_time' => time()], ['ip' => $result_bangding]);
                            }
                            if (!empty($result_jiechu)) {
                                #$res2 = $PdtIpModel->modifyIPStatus($result_jiechu,"1");
                                $res2 = $PdtIpModel->updateAll(['status' => 1, 'update_time' => time()], ['ip' => $result_jiechu]);
                            }
                            #
                            $newIdlePdtRes->status = 0;
                            if (!$newIdlePdtRes->update()) {
                                $transaction->rollBack();
                                $arrReturn = [
                                    'status' => 0,
                                    'info'   => '更新自有产品失败'
                                ];
                                return $this->renderJSON($arrReturn);
                            }
                        }
                    }

                } #if 审核 end
            } #if 是否有订单end

            #更新用户产品
            if ($cancelOrder == '审核') {
                #有差价时（有订单）
                if (!empty($TradeRes)) {
                    $dataContent[] = [
                        'sql' => base64_encode("update member_pdt set sell_price = " . $trade_config['old_sell_price'] . ", cost_price = " . $trade_config['old_cost_price'] . ", upgrade_cost_price = " . $trade_config['old_upgrade_cost_price'] . ", status = 1  where unionid = '" . $ServiceOrderRes['unionid'] . "'")
                    ];
                } else {
                    #无差价时（无订单）
                    $dataContent[] = [
                        'sql' => base64_encode("update member_pdt set sell_price = " . $service_after_config['old_sell_price'] . ", cost_price = " . $service_after_config['old_cost_price'] . ", upgrade_cost_price = " . $service_after_config['old_upgrade_cost_price'] . ", status = 1  where unionid = '" . $ServiceOrderRes['unionid'] . "'")
                    ];
                }
            } else {
                if (!$MemberPdtRes->update(false)) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '用户产品信息更新失败'
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }
        }#行为 end
        #

        $oldstatus         = $ServiceOrderRes->service_status;
        $confirm_adminid   = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
        $confirm_adminname = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
        $time_end          = time();

        if ($cancelOrder == '审核') {
            #修改工单状态 操作人
            $dataContent[] = [
                'sql' => base64_encode("update service_order set service_status = '取消工单', service_confirm_adminid = " . $confirm_adminid . ", service_confirm_adminname = '" . $confirm_adminname . "', service_time_end = " . $time_end . " where serviceid = '" . $serviceid . "'")
            ];

            $backContent[] = [
                'sql' => base64_encode("update service_order set service_audit = 'N' where serviceid = '" . $serviceid . "'")
            ];

            $dataJson = json_encode($dataContent);
            $backJson = json_encode($backContent);

            #生成审核信息
            NotifyHandle::CancelServiceOrder_Notify($oldstatus, '取消工单', $TradeRes['member_id'], $confirm_adminid, $ServiceOrderRes->unionid, $ServiceOrderRes->serviceid, $TradeRes['trade_orderid'], $dataJson, $backJson);
            #有审核时，将工单设为等待审核状态
            $ServiceOrderRes->service_audit = 'W';
            if ($ServiceOrderRes->update(false)) {
                $transaction->commit();
                $arrReturn = [
                    'status' => 1,
                    'info'   => '取消工单申请提交成功，请等待审核'
                ];

            } else {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '工单信息更新失败'
                ];
            }
            return $this->renderJSON($arrReturn);
        } else {
            #修改工单状态 操作人
            $ServiceOrderRes->service_status            = "取消工单";
            $ServiceOrderRes->service_confirm_adminid   = $confirm_adminid;
            $ServiceOrderRes->service_confirm_adminname = $confirm_adminname;
            $ServiceOrderRes->service_time_end          = $time_end;
            if ($ServiceOrderRes->update()) {
                #无审核时，有流水变动，生成报表
                if ($TradeRes) {
                    $userinfo  = $UserMemberModel->findOne(['u_id' => $TradeRes['user_id']]);
                    $reportRes = new FinanceReport($TradeRes['unionid'], $userinfo->u_id, $TradeRes, $ServiceOrderRes->serviceid, null, 'cancel_order');
                    #生成审核通知消息
                    NotifyHandle::CancelServiceOrder_Notify($oldstatus, '已取消', $TradeRes['member_id'], $confirm_adminid, $TradeRes['unionid'], $ServiceOrderRes->serviceid, $TradeRes['trade_orderid'], null, null);

                    if ($reportRes) {
                        $transaction->commit();
                        $arrReturn = [
                            'status' => 1,
                            'info'   => '取消工单成功'
                        ];
                    } else {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => '工单信息更新失败'
                        ];
                    }
                } else {
                    $transaction->commit();
                    $arrReturn = [
                        'status' => 1,
                        'info'   => '取消工单成功'
                    ];
                }
            } else {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '工单信息更新失败'
                ];
            }
        }
        return $this->renderJSON($arrReturn);
    }

    /**
     * 服务工单js定时请求
     */

    public function actionAutoTipsNewServiceOrder()
    {
        $orderNum  = Yii::$app->db->createCommand("select count(*) from service_order where service_time_confirm_time is null and service_time_end is null and service_status = '等待确认'")->queryScalar();
        $arrReturn = [
            'status' => 1,
            'data'   => $orderNum
        ];
        return $this->renderJSON($arrReturn);
    }


    /**
     * 接收选择的闲置产品，然后返回到页面
     */
    public function actionGetSelectidle()
    {
        Yii::$app->request->isAjax || die('error');
        #获取已经选择过的闲置产品ID
        $id           = trim($this->post('id'));
        $IdlePdtModel = new IdlePdt();

        $data = $IdlePdtModel->getRowById($id);

        $config = json_decode($data['config'], true);
        $ip     = json_decode($data['ip'], true);
        if ($data['servicerprovider'] == 0) {
            $switch   = $data['switch']['0'];
            $provider = "";
        } else {
            $switch   = "";
            $provider = $data['provider']['0'];
        }

        $data['config'] = $config;
        $data['ip']     = $ip;
        $data['switch'] = $switch;

        $data['provider'] = $provider;
        #根据所选机器的的服务器分类   将该服务器分类下所有配置弄出
        $PdtManageModel = new PdtManage();
        $PdtManageRes   = $PdtManageModel->find()->where(new Expression('FIND_IN_SET(:pdt_type_id, pdt_type_id)'))->addParams([':pdt_type_id' => $data['server_type_id']])->asArray()->all();
        #print_r($PdtManageRes); exit;
        $data['pdtmanage'] = $PdtManageRes;
        #根据机器的机房   将该机房下机柜弄出
        $PdtCabinetManageModel = new PdtCabinetManage();
        $PdtCabinetRes         = $PdtCabinetManageModel->find()->where(['room_id' => $data['room_id']])->asArray()->all();
        $data['pdtcabinet']    = $PdtCabinetRes;
        return $this->renderJSON($data);
    }

    /**
     * 接收选择的IP，然后返回到页面
     */
    public function actionGetSelectip()
    {
        Yii::$app->request->isAjax || die('error');

        #获取已经选择过的IP
        $oldIp = trim($this->post('ips'));
        $oldIp = str_replace(' ', '', $oldIp);
        $oldIp = array_filter(explode("\n", $oldIp), function ($v) {
            return !empty($v);
        });

        if (!empty($oldIp)) {
            foreach ($oldIp as $key => $value) {
                $newIp[] = $value;#将原来写的IP也写入
            }
        }
        #获取刚选择的IP数据
        $iparray = json_decode($this->post('data'), true);
        if (empty($iparray)) {
            $arrReturn = [];
            return $this->renderJSON($arrReturn);
        } else {
            foreach ($iparray as $key => $value) {
                $newIp[] = $value['ip'];
            }
            $newIp = array_flip($newIp);
            $newIp = array_flip($newIp);
            #rsort($newIp);
            #print_r($newIp);
            # $strRes = implode("\r\n", $newIp);echo $strRes;
            #$strRes .= "\r\n";
            return $this->renderJSON($newIp);
        }
    }

    /**
     * 异步联动获取配置
     */
    public function actionAjaxPdt()
    {
        Yii::$app->request->isAjax || die('error');

        $pdt_id = $this->post('id');
        if (empty($pdt_id)) {
            $arrReturn = [
                'status' => 0,
                'info'   => '未选择产品配置类别'
            ];
            return $this->renderJSON($arrReturn);
        }

        $PdtManageModel = new PdtManage();
        #获取整个的产品配置
        $PdtManageRes = $PdtManageModel->getRowById($pdt_id);
        if (empty($PdtManageRes)) {
            $pdtRes = "";
            return $this->renderJSON($pdtRes);
        }
        $cpuRes       = json_decode($PdtManageRes['cpu'], true);
        $ramRes       = json_decode($PdtManageRes['ram'], true);
        $hddRes       = json_decode($PdtManageRes['hdd'], true);
        $bandwidthRes = json_decode($PdtManageRes['bandwidth'], true);
        $ipnumberRes  = json_decode($PdtManageRes['ipnumber'], true);
        $defenseRes   = json_decode($PdtManageRes['defense'], true);
        $systemRes    = json_decode($PdtManageRes['operatsystem'], true);

        $pdtRes['cpu']       = $cpuRes;
        $pdtRes['ram']       = $ramRes;
        $pdtRes['hdd']       = $hddRes;
        $pdtRes['bandwidth'] = $bandwidthRes;
        $pdtRes['ipnumber']  = $ipnumberRes;
        $pdtRes['defense']   = $defenseRes;
        $pdtRes['system']    = $systemRes;
        $pdtRes['status']    = 1;

        return $this->renderJSON($pdtRes);
    }
}

?>