<?php

namespace addons\VymCloud\common\models;

use common\behaviors\MerchantStoreBehavior;
use common\models\merchant\Merchant;
use Yii;

/**
 * This is the model class for table "{{%addon_cloud_backup}}".
 *
 * @property int         $id
 * @property int         $order_id    云主机订单ID
 * @property int|null    $merchant_id 商户id
 * @property int|null    $store_id    店铺id
 * @property string      $name        备份名称
 * @property string|null $volid       备份ID
 * @property string|null $size        备份大小
 * @property string|null $vmid
 * @property int|null    $ctime       创建时间
 * @property string|null $format      格式
 * @property string|null $storage     所属存储名
 * @property string|null $notes       备注
 * @property string|null $status      状态
 * @property string|null $msg         执行结果
 */
class CloudBackup extends \yii\db\ActiveRecord
{

    use MerchantStoreBehavior;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%addon_cloud_backup}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['volid', 'vmid', 'format', 'storage', 'notes', 'status', 'msg'], 'default', 'value' => null],
            [['ctime'], 'default', 'value' => 0],
            [['merchant_id', 'store_id'], 'default', 'value' => 0],
            [['order_id', 'ctime'], 'integer'],
            [['name'], 'required'],
            [['msg'], 'string'],
            [['name', 'volid', 'notes'], 'string', 'max' => 225],
            [['size', 'format', 'storage'], 'string', 'max' => 30],
            [['vmid', 'status'], 'string', 'max' => 20],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'          => 'ID',
            'order_id'    => '云主机订单ID',
            'merchant_id' => '商户ID',
            'store_id'    => '店铺ID',
            'name'        => '备份名称',
            'volid'       => '备份ID',
            'size'        => '备份大小',
            'vmid'        => 'Vmid',
            'ctime'       => '创建时间',
            'format'      => '格式',
            'storage'     => '所属存储名',
            'notes'       => '备注',
            'status'      => '状态',
            'msg'         => '执行结果',
        ];
    }

    /**
     * 关联商户
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }
}
