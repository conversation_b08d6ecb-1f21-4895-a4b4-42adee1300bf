<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue';

import { Bad<PERSON>, Button, message } from 'ant-design-vue';
// @ts-ignore
import NoVNC from 'novnc-core';

interface Props {
  serverId: number;
  serverName?: string;
  consoleUrl: string;
  autoConnect?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  serverName: 'Cloud Server',
  autoConnect: true,
});

// 组件状态
const vncDisplay = ref<HTMLElement | null>(null);
const vnc = ref<any>(null);
const connecting = ref(false);
const connected = ref(false);
const error = ref('');
const showTips = ref(true);
const isFullscreen = ref(false);

// 连接状态
const connectionStatus = computed(() => {
  if (error.value) return 'error';
  if (connected.value) return 'success';
  if (connecting.value) return 'processing';
  return 'default';
});

const statusText = computed(() => {
  if (error.value) return '连接错误';
  if (connected.value) return '已连接';
  if (connecting.value) return '连接中';
  return '未连接';
});

/**
 * 连接 VNC 服务器
 */
async function connect() {
  if (!props.consoleUrl) {
    error.value = '控制台地址无效';
    return;
  }

  if (!vncDisplay.value) {
    error.value = 'VNC 显示容器未初始化';
    return;
  }

  connecting.value = true;
  error.value = '';

  try {
    // 清理之前的连接
    if (vnc.value) {
      vnc.value.disconnect();
      vnc.value = null;
    }

    // 等待 DOM 更新
    await nextTick();

    // 创建 NoVNC 实例
    const rfb = new NoVNC(vncDisplay.value, props.consoleUrl, {
      wsProtocols: ['binary', 'base64'],
      repeaterID: '',
      shared: true,
      credentials: {
        username: '',
        password: '',
      },
    });

    // 绑定事件监听器
    rfb.addEventListener('connect', handleConnect);
    rfb.addEventListener('disconnect', handleDisconnect);
    rfb.addEventListener('credentialsrequired', handleCredentials);
    rfb.addEventListener('securityfailure', handleSecurityFailure);

    vnc.value = rfb;
  } catch (error_: any) {
    connecting.value = false;
    error.value = `连接失败: ${error_.message}`;
    message.error(error.value);
  }
}

/**
 * 断开 VNC 连接
 */
function disconnect() {
  if (vnc.value) {
    vnc.value.disconnect();
    vnc.value = null;
  }
  connected.value = false;
  connecting.value = false;
  error.value = '';
}

/**
 * 发送 Ctrl+Alt+Del 组合键
 */
function sendCtrlAltDel() {
  if (vnc.value && connected.value) {
    vnc.value.sendCtrlAltDel();
    message.success('已发送 Ctrl+Alt+Del');
  }
}

/**
 * 切换全屏模式
 */
function toggleFullscreen() {
  if (document.fullscreenElement) {
    document.exitFullscreen();
  } else {
    vncDisplay.value?.requestFullscreen();
  }
}

/**
 * 刷新连接
 */
function refreshConnection() {
  disconnect();
  setTimeout(() => {
    connect();
  }, 1000);
}

/**
 * 重试连接
 */
function retryConnection() {
  error.value = '';
  connect();
}

// VNC 事件处理
function handleConnect(e: any) {
  console.log('VNC 连接成功', e);
  connecting.value = false;
  connected.value = true;
  error.value = '';
  message.success('VNC 连接成功');
}

function handleDisconnect(e: any) {
  console.log('VNC 连接断开', e);
  connecting.value = false;
  connected.value = false;

  if (e.detail.clean) {
    message.info('VNC 连接已断开');
  } else {
    error.value = '连接意外断开';
    message.error('VNC 连接意外断开');
  }
}

function handleCredentials(e: any) {
  console.log('需要认证信息', e);
  // 这里可以处理认证逻辑
  message.warning('服务器需要认证，请检查用户配置');
}

function handleSecurityFailure(e: any) {
  console.error('VNC 安全验证失败', e);
  connecting.value = false;
  error.value = '安全验证失败，请检查服务器配置';
  message.error(error.value);
}

// 全屏状态监听
function handleFullscreenChange() {
  isFullscreen.value = !!document.fullscreenElement;
}

// 组件生命周期
onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange);

  if (props.autoConnect) {
    // 延迟一下自动连接，确保组件完全加载
    setTimeout(() => {
      connect();
    }, 500);
  }
});

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  disconnect();
});
</script>

<template>
  <div class="vnc-container">
    <!-- VNC 工具栏 -->
    <div class="vnc-toolbar">
      <div class="toolbar-left">
        <span class="server-info">{{ serverName }}</span>
        <div class="connection-status">
          <Badge :status="connectionStatus" :text="statusText" />
        </div>
      </div>
      <div class="toolbar-right">
        <Button
          @click="connect"
          :loading="connecting"
          :disabled="connected"
          type="primary"
          size="small"
        >
          {{ connected ? '已连接' : '连接' }}
        </Button>
        <Button @click="disconnect" :disabled="!connected" size="small">
          断开
        </Button>
        <Button @click="sendCtrlAltDel" :disabled="!connected" size="small">
          Ctrl+Alt+Del
        </Button>
        <Button @click="toggleFullscreen" size="small">
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </Button>
        <Button @click="refreshConnection" :loading="connecting" size="small">
          刷新
        </Button>
      </div>
    </div>

    <!-- VNC 显示区域 -->
    <div class="vnc-content">
      <div v-if="error" class="vnc-error">
        <div class="error-icon">⚠️</div>
        <div class="error-text">{{ error }}</div>
        <Button @click="retryConnection" type="primary">重试连接</Button>
      </div>
      <div v-else-if="!connected && !connecting" class="vnc-waiting">
        <div class="waiting-icon">🖥️</div>
        <div class="waiting-text">点击"连接"按钮开始远程控制</div>
      </div>
      <div v-else-if="connecting" class="vnc-connecting">
        <div class="connecting-icon">🔄</div>
        <div class="connecting-text">正在连接VNC服务器...</div>
      </div>
      <div
        ref="vncDisplay"
        class="vnc-display"
        :class="{ 'vnc-connected': connected }"
      ></div>
    </div>
    <!-- 连接信息提示 -->
    <div v-if="showTips" class="vnc-tips">
      <div class="tips-content">
        <h4>VNC 连接提示：</h4>
        <ul>
          <li>确保云服务器已正常启动</li>
          <li>首次连接可能需要等待几秒钟</li>
          <li>如果连接失败，请尝试重启服务器后再连接</li>
          <li>使用 Ctrl+Alt+Del 可以发送组合键到远程服务器</li>
        </ul>
        <Button @click="showTips = false" size="small">知道了</Button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.vnc-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.vnc-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.server-info {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.connection-status {
  font-size: 14px;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.vnc-content {
  flex: 1;
  position: relative;
  background: #000;
  overflow: hidden;
}

.vnc-display {
  width: 100%;
  height: 100%;
  background: #000;
}

.vnc-display.vnc-connected {
  cursor: crosshair;
}

.vnc-error,
.vnc-waiting,
.vnc-connecting {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
}

.vnc-error {
  color: #f5222d;
}

.error-icon,
.waiting-icon,
.connecting-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.connecting-icon {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.error-text,
.waiting-text,
.connecting-text {
  font-size: 16px;
  margin-bottom: 16px;
}

.vnc-tips {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  max-width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.tips-content h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
}

.tips-content ul {
  margin: 0 0 12px 0;
  padding-left: 20px;
  font-size: 12px;
  color: #666;
}

.tips-content li {
  margin-bottom: 4px;
}

/* 全屏时隐藏提示 */
.vnc-container:fullscreen .vnc-tips {
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vnc-toolbar {
    flex-direction: column;
    gap: 10px;
    padding: 8px 12px;
  }

  .toolbar-right {
    flex-wrap: wrap;
    justify-content: center;
  }

  .vnc-tips {
    position: relative;
    top: auto;
    right: auto;
    margin: 10px;
    max-width: none;
  }
}
</style>
