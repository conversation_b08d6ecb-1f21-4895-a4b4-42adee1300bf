<?php

namespace addons\VymDesen\backend\controllers;

use addons\VymDesen\backend\components\LogWrite;
use addons\VymDesen\backend\models\Cost;
use addons\VymDesen\backend\models\IdlePdt;
use addons\VymDesen\backend\models\IpManagement\PdtIpClass;
use addons\VymDesen\backend\models\IpManagement\PdtIpNetwork;
use addons\VymDesen\backend\models\IpManagement\SupplierIp;
use addons\VymDesen\backend\models\Log\PdtLog;
use addons\VymDesen\backend\models\PdtHaveuselog;
use addons\VymDesen\backend\models\PdtRefund;
use addons\VymDesen\backend\models\Provider;
use addons\VymDesen\backend\models\ServiceOrder;
use addons\VymDesen\backend\models\TestServer;
use addons\VymDesen\backend\models\UserAdmin\UserAdmin;
use addons\VymDesen\backend\models\WorkFlow\WorkFlow;
use addons\VymDesen\backend\models\WorkFlow\WorkFlowRole;
use addons\VymDesen\backend\controllers\BaseController;
use addons\VymDesen\common\components\BusinessRelated\BusinessCheck;
use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\components\FinanceReport;
use addons\VymDesen\common\components\IpmiApi;
use addons\VymDesen\common\components\NewPipe\PipeLine;
use addons\VymDesen\common\components\NotifyHandle;
use addons\VymDesen\common\components\OutPut;
use addons\VymDesen\common\components\RevenueNotes;
use addons\VymDesen\common\components\SwitchRequest;
use addons\VymDesen\common\components\WorkFlowApi;
use addons\VymDesen\common\models\AdminSystemConfig;
use addons\VymDesen\common\models\Finance\FinanceManage;
use addons\VymDesen\common\models\Member\BusinessDeleteRecord;
use addons\VymDesen\common\models\Member\InitialAccount;
use addons\VymDesen\common\models\Member\MemberPdt;
use addons\VymDesen\common\models\Member\MemberPdtAppointOff;
use addons\VymDesen\common\models\NewTrade\TradeDetail;
use addons\VymDesen\common\models\NewTrade\TradeMain;
use addons\VymDesen\common\models\PayOrder\PaymentFlow;
use addons\VymDesen\common\models\PayOrder\PayorderDetail;
use addons\VymDesen\common\models\PayOrder\PayorderGeneral;
use addons\VymDesen\common\models\PayOrder\PayorderOriginal;
use addons\VymDesen\common\models\Pdt\PdtCabinetManage;
use addons\VymDesen\common\models\Pdt\PdtIp;
use addons\VymDesen\common\models\Pdt\PdtManage;
use addons\VymDesen\common\models\Pdt\PdtManageType;
use addons\VymDesen\common\models\Pdt\PdtRoomMange;
use addons\VymDesen\common\models\Pdt\PdtType;
use addons\VymDesen\common\models\Pdt\SwitchLine;
use addons\VymDesen\common\models\Pdt\SwitchManage;
use addons\VymDesen\common\models\PipeLine\PipelineTypes;
use addons\VymDesen\common\models\Server\ServerAttribute;
use addons\VymDesen\common\models\Trade\Trade;
use addons\VymDesen\common\models\UserCredit\UserCredit;
use addons\VymDesen\common\models\UserCredit\UserCreditHistory;
use addons\VymDesen\common\models\UserMember\UserMember;
use moonland\phpexcel\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Yii;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;

/**
 * 用户产品控制器
 *
 * <AUTHOR>
 *
 */
class MemberPdtController extends BaseController
{

    #列表
    public function actionIndex()
    {
        $MemberPdtModel = new MemberPdt();
        $time           = time();

        $MemberPdtModelQuery = $MemberPdtModel->find()->select('*')->With('servertype')->With('pdtroom')->With('pdtcabinet')->
        With('pdtmanage')->With('pdttype')->With('serverattribute')->With('provider')->With('trade')->With('switch')->
        With('usermember')->andWhere('status != -1')->orderBy('status desc, end_time asc');
        #创建搜索条件
        $MemberPdtModel->createSearchWhere($MemberPdtModelQuery, $this->get(),true);

        $iCount   = $MemberPdtModelQuery->count();//echo $iCount;
        $pageSize = 30;
        $oPage    = DataHelper::getPage($iCount, $pageSize);

        $arrRes = $MemberPdtModelQuery->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();//die(print_r($arrRes));

        //获取机房列表
        $RoomManageModel = new PdtRoomMange();
        $roomRes         = $RoomManageModel->getListAll();

        //获取机柜
        $PdtCabinetManageModel = new PdtCabinetManage();
        if ($this->get('room_id') == "") {
            $cabinetRes = [];
        } else {
            $condition  = ['room_id' => $this->get('room_id')];
            $cabinetRes = $PdtCabinetManageModel->getListAll($condition);
        }
        //获取产品配置类别
        $PdtManageModel = new PdtManage();
        if ($this->get('server_type_id') == "") {
            $pdtRes = $PdtManageModel->getListAll();
        } else {
            $condition1 = ['pdt_type_id' => $this->get('server_type_id')];
            $pdtRes     = $PdtManageModel->getListAll($condition1);
        }

        #获取客服
        $UserAdminModel         = new UserAdmin();
        $AdminSystemConfigModel = new AdminSystemConfig();
        $salesConfig            = $AdminSystemConfigModel->find()->where(['config_name' => 'order_default_myself'])->asArray()->one();
        $adminlist              = $UserAdminModel->find()->where(['in', 'admin_id', explode(',', $salesConfig['config_value'])])->asArray()->all();
        //获取服务器状态属性
        $ServerAttributeModel = new ServerAttribute();
        $ServerAttributeRes   = $ServerAttributeModel->getListAll();
        #服务器分类
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeList  = $PdtManageTypeModel->find()->asArray()->all();#print_r($PdtManageTypeList);exit;

        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
        #看是否是只看自己客户账户的
        $isAllow = true;

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'business_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            $isAllow = false;
        }
        foreach ($arrRes as $k => $ve) {
            $defenseIP           = '';
            $ip2                 = '';
            $intranetIP          = ArrayHelper::getValue($ve, 'ipmi_ip');
            $InitialAccountModel = new InitialAccount();
            $InitialAccountRes   = $InitialAccountModel->find()->where(['unionid' => $ve['unionid']])->asArray()->one();
            $arr                 = json_decode(ArrayHelper::getValue($arrRes[$k], 'ip'));
            $ip                  = '';
            if ($arr && is_array($arr)) {
                $ips = explode('-', ArrayHelper::getValue($arr, 0));
                $ip  = ArrayHelper::getValue($ips, 0);
//                $ip2 = ArrayHelper::getValue($ips, 1);
            }
            $username                = ArrayHelper::getValue($InitialAccountRes, 'name');
            $password                = ArrayHelper::getValue($InitialAccountRes, 'pwd');
            $port                    = ArrayHelper::getValue($InitialAccountRes, 'port');
            $system                  = ArrayHelper::getValue(json_decode(ArrayHelper::getValue($ve, 'config'), true), 'operatsystem');
            $ip                     = $ip ? "主 IP：$ip\n" : '';
            $ip2                     = $ip2 ? "副 IP：$intranetIP\n" : '';
            $defenseIP               = $defenseIP ? "高防IP：$intranetIP\n" : '';
            $intranetIP              = $intranetIP ? "內網IP：$intranetIP\n" : '';
            $arrRes[$k]['copy_text'] = "
{$ip}{$ip2}{$defenseIP}{$intranetIP}
系統帳號：$username
系統密碼：$password
遠程端口：$port
操作系統：$system

------------建议事项-------------
● 为保障您的服务器安全, 建议在首次成功登录服务器后修改登入密码。
● 为保障您的数据安全, 建议定期备份服务器资料以防不可预期的故障给您的数据带来影响。
● 为维护网络安全，凡涉钓鱼欺诈业务而被投诉，服务器会立即封停，数据将无法恢复。由此而导致的任何后果或损失均由用户自行承担，本公司不会承担任何责任。
● Windows系统,  请在出机時及定期进行系统更新以提高系统的安全性及稳定性及安装防毒软件(如网站安全狗/服务器安全狗)。
● 把网站或业务数据存放在D盘或空间较大的分区里, 避免C盘空间满载时出现系统问题影响业务。
● Linux系统定期进行系统更新以提高系统的安全性及稳定性及安装您习惯使用的防御策略或软件。
● 把宝塔的文档或业务数据存放在路径 /home 或空间较大的分区里, 避免系统的分区满载时出现系统问题影响业务。
● 為了宝塔運行正常，我们会放一个宝塔监控脚本，当脚本检测到宝塔没有响应时，脚本会重啟宝塔。 您亦可以随时要求我们的技术删除此脚本。";

        }

        return $this->render('index',
            [
                'arrRes'             => $arrRes,
                'roomRes'            => $roomRes,
                'cabinetRes'         => $cabinetRes,
                'pdtRes'             => $pdtRes,
                'UserAdminRes'       => $adminlist,
                'ServerAttributeRes' => $ServerAttributeRes,
                'PdtManageTypeList'  => $PdtManageTypeList,
                'admin_id'           => $admin_id,
                'is_allow'           => $isAllow,
                'iCount'             => $iCount,
                'page'               => $oPage,
                'pageSize'           => $pageSize,
            ]);
    }


    #已过期列表
    public function actionOveduelist()
    {

        $MemberPdtModel      = new MemberPdt();
        $time                = time();
        $MemberPdtModelQuery = $MemberPdtModel->find()->select('*')->With('servertype')->With('pdtroom')->With('pdtcabinet')->
        With('pdtmanage')->With('pdttype')->With('serverattribute')->With('provider')->With('trade')->With('switch')->
        With('usermember')->where('end_time <' . $time . ' and status !=-1')->orderBy('end_time asc');

        #创建搜索条件
        $MemberPdtModel->createSearchWhere($MemberPdtModelQuery, $this->get());

        $iCount = $MemberPdtModelQuery->count();//echo $iCount;
        $oPage  = DataHelper::getPage($iCount);

        $arrRes = $MemberPdtModelQuery->offset($oPage->offset)->limit($oPage->limit)->orderBy('end_time asc')->asArray()->all();//die(print_r($arrRes));

        //获取机房列表
        $RoomManageModel = new PdtRoomMange();
        $roomRes         = $RoomManageModel->getListAll();
        //获取机柜
        $PdtCabinetManageModel = new PdtCabinetManage();
        if ($this->get('room_id') == "") {
            $cabinetRes = [];
        } else {
            $condition  = ['room_id' => $this->get('room_id')];
            $cabinetRes = $PdtCabinetManageModel->getListAll($condition);
        }
        //获取产品配置类别
        $PdtManageModel = new PdtManage();
        if ($this->get('server_type_id') == "") {
            $pdtRes = $PdtManageModel->getListAll();
        } else {
            $condition1 = ['pdt_type_id' => $this->get('server_type_id')];
            $pdtRes     = $PdtManageModel->getListAll($condition1);
        }
        //获取后台角色管理
        #获取客服
        $UserAdminModel         = new UserAdmin();
        $AdminSystemConfigModel = new AdminSystemConfig();
        $salesConfig            = $AdminSystemConfigModel->find()->where(['config_name' => 'order_default_myself'])->asArray()->one();
        $adminlist              = $UserAdminModel->find()->where(['in', 'admin_id', explode(',', $salesConfig['config_value'])])->asArray()->all();
        //获取服务器状态属性
        $ServerAttributeModel = new ServerAttribute();
        $ServerAttributeRes   = $ServerAttributeModel->getListAll();
        #服务器分类
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeList  = $PdtManageTypeModel->find()->asArray()->all();#print_r($PdtManageTypeList);exit;

        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
        #看是否是只看自己客户账户的
        $isAllow = true;

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'pipe_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            $isAllow = false;
        }

        return $this->render('oveduelist',
            [
                'arrRes'             => $arrRes,
                'roomRes'            => $roomRes,
                'cabinetRes'         => $cabinetRes,
                'pdtRes'             => $pdtRes,
                'UserAdminRes'       => $adminlist,
                'ServerAttributeRes' => $ServerAttributeRes,
                'PdtManageTypeList'  => $PdtManageTypeList,
                'admin_id'           => $admin_id,
                'is_allow'           => $isAllow,
                'iCount'             => $iCount,
                'page'               => $oPage,
            ]);
    }

    #已删除列表
    public
    function actionDeletelist()
    {
        $MemberPdtModel = new MemberPdt();
        $time           = time();

        $MemberPdtModelQuery = $MemberPdtModel->find()->select('*')->With('servertype')->With('pdtroom')->With('pdtcabinet')->
        With('pdtmanage')->With('pdttype')->With('serverattribute')->With('provider')->With('trade')->With('switch')->
        With('usermember')->where(['status' => -1])->orderBy('id desc');

        #创建搜索条件
        $MemberPdtModel->createSearchWhere($MemberPdtModelQuery, $this->get());

        $iCount = $MemberPdtModelQuery->count();//echo $iCount;
        $oPage  = DataHelper::getPage($iCount);

        $arrRes = $MemberPdtModelQuery->offset($oPage->offset)->limit($oPage->limit)->orderBy('id desc')->asArray()->all();//die(print_r($arrRes));

        //获取机房列表
        $RoomManageModel = new PdtRoomMange();
        $roomRes         = $RoomManageModel->getListAll();
        //获取机柜
        $PdtCabinetManageModel = new PdtCabinetManage();
        if ($this->get('room_id') == "") {
            $cabinetRes = [];
        } else {
            $condition  = ['room_id' => $this->get('room_id')];
            $cabinetRes = $PdtCabinetManageModel->getListAll($condition);
        }
        //获取产品配置类别
        $PdtManageModel = new PdtManage();
        if ($this->get('server_type_id') == "") {
            $pdtRes = $PdtManageModel->getListAll();
        } else {
            $condition1 = ['pdt_type_id' => $this->get('server_type_id')];
            $pdtRes     = $PdtManageModel->getListAll($condition1);
        }
        //获取后台角色管理
        #获取客服
        $UserAdminModel         = new UserAdmin();
        $AdminSystemConfigModel = new AdminSystemConfig();
        $salesConfig            = $AdminSystemConfigModel->find()->where(['config_name' => 'order_default_myself'])->asArray()->one();
        $adminlist              = $UserAdminModel->find()->where(['in', 'admin_id', explode(',', $salesConfig['config_value'])])->asArray()->all();
        //获取服务器状态属性
        $ServerAttributeModel = new ServerAttribute();
        $ServerAttributeRes   = $ServerAttributeModel->getListAll();
        #服务器分类
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeList  = $PdtManageTypeModel->find()->asArray()->all();#print_r($PdtManageTypeList);exit;

        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
        #看是否是只看自己客户账户的
        $isAllow = true;

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'pipe_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            $isAllow = false;
        }
        return $this->render('deletelist',
            [
                'arrRes'             => $arrRes,
                'roomRes'            => $roomRes,
                'cabinetRes'         => $cabinetRes,
                'pdtRes'             => $pdtRes,
                'UserAdminRes'       => $adminlist,
                'ServerAttributeRes' => $ServerAttributeRes,
                'PdtManageTypeList'  => $PdtManageTypeList,
                'admin_id'           => $admin_id,
                'is_allow'           => $isAllow,
                'iCount'             => $iCount,
                'page'               => $oPage,
            ]);
    }

    #配置详情页
    public
    function actionConfigItem()
    {

        $unionid = $this->get('unionid');

        $MemberPdtModel = new MemberPdt();
        $AppointModel   = new MemberPdtAppointOff();

        $MemberPdtRes = $MemberPdtModel->find()->select('*')->With('servertype')->With('pdtroom')->With('pdtcabinet')->
        With('pdtmanage')->With('pdttype')->With('serverattribute')->With('provider')->With('trade')->With('switch')->
        With('usermember')->where(['unionid' => $unionid])->asArray()->one();

        if (empty($MemberPdtRes))
            return $this->redirect(Url::to(['index']));

        $config = json_decode($MemberPdtRes['config'], true);
        #获取所属用户
        $UserMemberModel = new UserMember();
        $UserMemberRes   = $UserMemberModel->getRowById($MemberPdtRes['user_id']);

        #获取初始账户
        $InitialAccountModel = new InitialAccount();
        $InitialAccountRes   = $InitialAccountModel->find()->where(['unionid' => $MemberPdtRes['unionid']])->asArray()->one();

        $hasBackPayOrder = Yii::$app->db->createCommand("select count(*) from trade where unionid = '" . $MemberPdtRes['unionid'] . "' and trade_status = '后付款' and trade_type_do = '新购'")->queryScalar();

        #获取过户记录信息
        $TransferRecordRes = Yii::$app->db->createCommand("select * from transfer_record where unionid = '" . $MemberPdtRes['unionid'] . "'")->queryAll(); ##print_r($TransferRecordRes);die;\

        #根据所属服务器分类   获取该服务器分类下所有的配置类别
        $PdtManageModel = new PdtManage();
        $PdtManageList  = $PdtManageModel->find()->where(new Expression('FIND_IN_SET(:pdt_type_id, pdt_type_id)'))->addParams([':pdt_type_id' => $MemberPdtRes['server_type_id']])->asArray()->all();

        #获取IP分类
        $PdtIpClassModel = new PdtIpClass();
        $IpClassList     = $PdtIpClassModel->find()->asArray()->all();
        #网段
        $PdtIpNetworkModel = new PdtIpNetwork();
        $IpNetworkList     = $PdtIpNetworkModel->find()->where(['room_id' => $MemberPdtRes['room_id']])->asArray()->all();

        #是否预约了关机下架
        $appointInfo = $AppointModel->find()->where(["appoint_unionid" => $MemberPdtRes['unionid']])->asArray()->one();
        if ($appointInfo) {
            $appointOff = true;
        } else {
            $appointOff = false;
        }

        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
        #看是否是只看自己客户账户的
        $isAllow = true;

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'pipe_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            if ($MemberPdtRes['admin_id'] != $admin_id) {
                $isAllow = false;
            }
        }

        return $this->render('config-item', [
            'MemberPdtRes'      => $MemberPdtRes,
            'UserMemberRes'     => $UserMemberRes,
            'InitialAccountRes' => $InitialAccountRes,
            'config'            => $config,
            'hasBackPayOrder'   => $hasBackPayOrder,
            'TransferRecordRes' => $TransferRecordRes,
            'PdtManageList'     => $PdtManageList,
            'IpClassList'       => $IpClassList,
            'IpNetworkList'     => $IpNetworkList,
            'AppointOff'        => $appointOff,
            'admin_id'          => $admin_id,
            'is_allow'          => $isAllow,
        ]);

    }

    #新版更换IP
    public
    function actionReplaceIp()
    {
        $MemberPdtModel    = new MemberPdt();
        $WorkFlowModel     = new WorkFlow();
        $WorkFlowRoleModel = new WorkFlowRole();
        $UserMemberModel   = new UserMember();
        $UserAdminModel    = new UserAdmin();
        $PdtIpModel        = new PdtIp();
        $TestServerModel   = new TestServer();
        $PdtIpNetworkModel = new PdtIpNetwork();
        $SupplierIpModel   = new SupplierIp();
        $SwitchLineModel   = new SwitchLine();
        $IdlePdtModel      = new IdlePdt();

        if (Yii::$app->request->post()) {
            $post           = $this->post();
            $MemberPdtQuery = $MemberPdtModel->findOne(['id' => $post['id']]);    #获取用户产品信息对象

            $oldIP = json_decode($MemberPdtQuery->ip2, true); #print_r($oldIP);die();

            $UserRes = $UserMemberModel->find()->where(['u_id' => $MemberPdtQuery->user_id])->asArray()->one();

            $IpArr = DataHelper::dotrim($post['ips']);
            if (empty($IpArr)) {
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => 'IP地址未选择或未填写',
                ]);
            }
            #处理的得到的IP数组1   #去重 去空
            $ipArray = array_unique($IpArr);
            $ipArray = array_filter($ipArray);

            #开启事务
            $transaction = Yii::$app->db->beginTransaction();
            #拆分IP
            $ip2            = $ipArray;
            $Retuen_IPArray = DataHelper::splitIP($ip2);
            if (!$Retuen_IPArray['status']) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => $Retuen_IPArray['info'],
                ];
                return $this->renderJSON($arrReturn);
            }
            $ipArray2 = $Retuen_IPArray['data'];
            #将提交的IP进行验证 分为自有和供应商的
            if ($MemberPdtQuery->servicerprovider == 0) {
                #
            } elseif ($MemberPdtQuery->servicerprovider == 1) {

                if ($ipArray2 == $oldIP) {
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '未有IP做更换',
                    ]);
                }
                #供应商IP进行检测（只判断新增的IP，减少IP不检测）
                $bgIPArray = array_unique(array_merge($ipArray2, $oldIP));
                $diffArray = array_diff($ipArray2, $oldIP); #获取新增的IP
                if (!empty($diffArray)) {
                    #检测新增的IP
                    $CheckRes = DataHelper::detect_supplierip($diffArray);
                    if ($CheckRes['status'] == 0) {
                        $transaction->rollBack();
                        return $this->renderJSON([
                            'status' => 0,
                            'info'   => $CheckRes['info'],
                        ]);
                    }
                }
            }
            #判断IP组2是否正常(再一次验证)
            if (!empty($ipArray2)) {
                foreach ($ipArray2 as $value) {
                    if (!filter_var($value, FILTER_VALIDATE_IP)) {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => $value . '为不合法的IP地址',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
            }

            $aaoldIP2    = json_decode($MemberPdtQuery->ip2, true);
            $hasChangeIP = false;
            foreach ($ipArray2 as $k => $v) {
                if (!in_array($v, $aaoldIP2)) {
                    $hasChangeIP = true;
                    break;
                }
            }
            foreach ($aaoldIP2 as $k => $v) {
                if (!in_array($v, $ipArray2)) {
                    $hasChangeIP = true;
                    break;
                }
            }
            if (!$hasChangeIP) {
                #如果没有更换IP，只是调换顺序
                $MemberPdtQuery->ip  = json_encode($ipArray);
                $MemberPdtQuery->ip2 = json_encode($ipArray2);
                $MemberPdtQuery->save();
                $arrReturn = [
                    'status' => 1,
                    'info'   => 'IP地址没有更换，但是调换了顺序，不需要进入流程',
                ];
                return $this->renderJSON($arrReturn);
            }

            if (count($ipArray2) >= count($oldIP)) {
                if (empty(array_diff($ipArray2, $oldIP))) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '未有IP改变',
                    ];
                    return $this->renderJSON($arrReturn);
                }
            } elseif (count($oldIP) >= count($ipArray2)) {
                if (empty(array_diff($oldIP, $ipArray2))) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '未有IP改变',
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }

            #当在为自有时。判断IP所属机房是否与产品的机房一致
            if ($MemberPdtQuery->servicerprovider == 0) {
                $PdtIpAll = $PdtIpModel->find()->select('room_id, line_type_id, vlan')->where(['in', 'ip', $ipArray2])->asArray()->all();#print_r($PdtIpAll);die;

                $temp = array_column($PdtIpAll, 'room_id');#print_r($temp);exit;
                $temp = array_unique($temp);               #去掉重复的字符串,也就是重复的一维数组
                $temp = array_values($temp);               #print_r($temp); 查询排序
                if (count($temp) > 1) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => 'IP中有属于其他机房的IP地址',
                    ];
                    return $this->renderJSON($arrReturn);
                } else if (count($temp) == 1) {
                    if ($temp[0] != $MemberPdtQuery->room_id) {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => 'IP所属机房与产品选择机房不一致',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
                #判断线路类型
                $line_typeList = array_unique(array_column($PdtIpAll, 'line_type_id'));
                if (count($line_typeList) > 1) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '所选IP与其他IP的线路类型不匹配',
                    ];
                    return $this->renderJSON($arrReturn);
                }
                #获取机器对应交换机的线路信息
                $IdlePdtRes = $IdlePdtModel->find()->where(['id' => $MemberPdtQuery->idle_id])->asArray()->one();

                $SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtRes['switch_location'], 'line_type_id' => $line_typeList[0]])->asArray()->one();
                if (empty($SwitchLineList)) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '机器对应的交换机线路中未有IP的线路类型',
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }

            $data        = [
                'unionid'            => $MemberPdtQuery->unionid,
                'ip'                 => $ipArray,
                'ip2'                => $ipArray2,
                'request_admin_id'   => $this->getAdminInfo('admin_id'),
                'request_admin_name' => $this->getAdminInfo('uname'),
            ];
            $callbackRes = PipeLine::Line_ReplaceIP($data);
            if ($callbackRes['status']) {
                $transaction->commit();
                $arrReturn = [
                    'status' => 1,
                    'info'   => '更换IP加入工作流成功',
                ];
            } else {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => $callbackRes['info'],
                ];
            }
            return $this->renderJSON($arrReturn);

        } else {
            $id = $this->get('id');

            $MemberPdtRes = $MemberPdtModel->find()->where(['id' => $id])->asArray()->one();
            if (empty($MemberPdtRes))
                return $this->redirect(Url::to(['index']));

            #获取IP分类
            $PdtIpClassModel = new PdtIpClass();
            $IpClassList     = $PdtIpClassModel->find()->asArray()->all();
            #网段
            $IpNetworkList = $PdtIpNetworkModel->find()->where(['room_id' => $MemberPdtRes['room_id']])->asArray()->all();
            return $this->render('replace-ip', ['arrRes' => $MemberPdtRes, 'IpClassList' => $IpClassList, 'IpNetworkList' => $IpNetworkList]);
        }
    }

    #新版 变更配置 有金额差异进入订单。无金额差异直接进入流程
    public
    function actionChangeConfig()
    {
        $TradeMainModel   = new TradeMain();
        $TradeDetailModel = new TradeDetail();
        $MemberPdtModel   = new MemberPdt();
        $UserMemberModel  = new  UserMember;
        $WorkFlowModel    = new WorkFlow();
        if (Yii::$app->request->post()) {
            $post = DataHelper::dotrim($this->post());
            if ($post['ram'] == "" && $post['hdd'] == "" && $post['bandwidth'] == "" && $post['ipnumber'] == "" && $post['defense'] == "" && $post['real_bandwidth'] == "") {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '请至少变更一种配置',
                ];
                return $this->renderJSON($arrReturn);
            }
            if (!is_numeric($post['normal_price']) || !is_numeric($post['price'])) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '价格费用不能为空，若没有价格变动则填写原价格',
                ];
                return $this->renderJSON($arrReturn);
            }
            if (!is_numeric($post['normal_price']) || $post['price'] == '') {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '请输入正确的价格',
                ];
                return $this->renderJSON($arrReturn);
            }
            #获取当前的产品信息
            $MemberPdtRes = $MemberPdtModel->find()->where(['id' => $post['id']])->asArray()->one();
            if (empty($MemberPdtRes)) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '未知的业务信息',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($MemberPdtRes['status'] != 1 || $MemberPdtRes['audit_status'] != 1) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '当前产品不在正常状态或未审核',
                ];
                return $this->renderJSON($arrReturn);
            }

            if (strtotime($post['service_starttime']) >= strtotime($post['service_endtime'])) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '服务结束时间必须大于开始时间',
                ];
                return $this->renderJSON($arrReturn);
            }

            #原产品配置类别
            $PdtManageModel = new PdtManage();
            $pdtmanageRes   = $PdtManageModel->getRowById($MemberPdtRes['pdt_id']);

            #获取当前产品的用户信息
            $UserRes = $UserMemberModel->find()->where(['u_id' => $MemberPdtRes['user_id']])->asArray()->one();

            #当前的服务器配置
            $now_config = json_decode($MemberPdtRes['config'], true); #var_dump($now_config);exit;
            #当前的机器IP
            $oldIP = json_decode($MemberPdtRes['ip2'], true);

            #得到真实的升级信息
            $modifyData = [];

            if ($post['ram'] != "" || $post['ram'] != null) {

                $modifyData[] = [
                    'modify_type' => 'ram',
                    'modify_data' => [
                        'old_config' => $now_config['ram'],
                        'new_config' => $post['ram'],
                    ],
                ];
            }

            if ($post['hdd'] != "" || $post['hdd'] != null) {

                $modifyData[] = [
                    'modify_type' => 'hdd',
                    'modify_data' => [
                        'old_config' => $now_config['hdd'],
                        'new_config' => $post['hdd'],
                    ],
                ];
            }

            #客户要求带宽
            if ($post['bandwidth'] != "" || $post['bandwidth'] != null) {

                $modifyData[] = [
                    'modify_type' => 'requirement_bandwidth',
                    'modify_data' => [
                        'old_config' => $MemberPdtRes['bandwidth'],
                        'new_config' => $post['bandwidth'],
                    ],
                ];
            }

            #实际带宽
            if ($post['real_bandwidth'] != "" || $post['real_bandwidth'] != null) {

                $modifyData[] = [
                    'modify_type' => 'configbandwidth',
                    'modify_data' => [
                        'old_config' => $now_config['configbandwidth'],
                        'new_config' => $post['real_bandwidth'],
                    ],
                ];
            }

            if ($post['ipnumber'] != "" || $post['ipnumber'] != null) {

                $modifyData[] = [
                    'modify_type' => 'ipnumber',
                    'modify_data' => [
                        'old_config' => $now_config['ipnumber'],
                        'new_config' => $post['ipnumber'],
                    ],
                ];
            }

            if ($post['defense'] != "" || $post['defense'] != null) {

                $modifyData[] = [
                    'modify_type' => 'defense',
                    'modify_data' => [
                        'old_config' => $now_config['defense'],
                        'new_config' => $post['defense'],
                    ],
                ];
            }

            if ($MemberPdtRes['pdt_id'] != $post['pdt_id']) {

                $modifyData[] = [
                    'modify_type' => 'pdt_id',
                    'modify_data' => [
                        'old_config' => $MemberPdtRes['pdt_id'],
                        'new_config' => $post['pdt_id'],
                    ],
                ];
            }

            $trade_config['unionid']          = $MemberPdtRes['unionid'];
            $trade_config['servicerprovider'] = $MemberPdtRes['servicerprovider'];
            $trade_config['server_type_id']   = $MemberPdtRes['server_type_id'];
            $trade_config['pdt_id']           = $MemberPdtRes['pdt_id'];
            $trade_config['room_id']          = $MemberPdtRes['room_id'];
            $trade_config['ipmi_ip']          = $MemberPdtRes['ipmi_ip'];
            $trade_config['ip']               = json_decode($MemberPdtRes['ip'], true);
            $trade_config['user_id']          = $MemberPdtRes['user_id'];

            $trade_config['service_starttime'] = $post['service_starttime'];
            $trade_config['service_endtime']   = $post['service_endtime'];

            $trade_config['modifyData'] = $modifyData;  #新配置

            #变更后的续费价格
            $trade_config['modify_sell_price'] = $post['normal_price'] ? sprintf("%.2f", $post['normal_price']) : $MemberPdtRes['sell_price'];
            $trade_config['old_sell_price']    = $MemberPdtRes['sell_price'];
            $trade_config['need_workorder']    = $post['need_workorder'];
            #开启事务
            $transaction = Yii::$app->db->beginTransaction();
            $time        = time();

            #当本次变更配置需要支付补款，则生成订单
            if ($post['price'] > 0) {

                #获取订单号
                $tradeOrderid = DataHelper::createTradeno();

                $OriginalModel                       = new PayorderOriginal();
                $OriginalModel->order_id             = $tradeOrderid;
                $OriginalModel->order_type           = '变更配置';
                $OriginalModel->order_user_id        = $UserRes['u_id'];
                $OriginalModel->order_user_nickname  = $UserRes['uname'];
                $OriginalModel->order_user_email     = $UserRes['email'];
                $OriginalModel->order_admin_id       = $UserRes['admin_id'];
                $OriginalModel->order_admin_name     = $UserRes['admin_name'];
                $OriginalModel->order_original_price = $post['price'];
                $OriginalModel->order_update_price   = $post['price'];
                $OriginalModel->order_update_remark  = null;
                $OriginalModel->order_update_status  = '未锁定';
                $OriginalModel->order_amount_money   = 0;
                $OriginalModel->order_finish_pay     = 0;
                $OriginalModel->order_pay_status     = '未支付';
                $OriginalModel->order_remark         = null;
                $OriginalModel->order_status         = '进行中';
                $OriginalModel->order_time_create    = $time;

                #生成支付单insert

                $payOrder = DataHelper::createPayorderGeneralID();

                $GeneralModel                               = new PayorderGeneral();
                $GeneralModel->general_payorder_title       = '变更配置';
                $GeneralModel->general_payorder_number      = null;
                $GeneralModel->general_payorder             = $payOrder;
                $GeneralModel->general_original_order       = $tradeOrderid;
                $GeneralModel->general_pay_money            = $post['price'];
                $GeneralModel->general_pay_platform         = null;
                $GeneralModel->general_pay_channel          = null;
                $GeneralModel->general_pay_time             = null;
                $GeneralModel->general_pay_lock             = '等待支付';
                $GeneralModel->general_payinfo_review       = '未审核';
                $GeneralModel->general_payorder_type        = 'original';
                $GeneralModel->general_callback_stream      = null;
                $GeneralModel->general_command_adminid_list = $UserRes['admin_id'];
                $GeneralModel->general_payment_userid       = $UserRes['u_id'];
                $GeneralModel->general_create_time          = $time;


                #生成订单详情
                $DetailModel                        = new PayorderDetail();
                $DetailModel->detail_original_order = $tradeOrderid;
                $DetailModel->detail_title          = '变更产品配置';
                $DetailModel->detail_content        = json_encode($trade_config, JSON_UNESCAPED_UNICODE);
                $DetailModel->detail_price          = $post['price'];
                $DetailModel->detail_remark         = null;

                $insertMainResult    = $OriginalModel->save();
                $insertGeneralResult = $GeneralModel->save();
                $insertDetailResult  = $DetailModel->save();

                $createPipe = PipeLine::Line_ChangeConfig('补款', $tradeOrderid, $trade_config);

                if ($insertMainResult && $insertGeneralResult && $insertDetailResult && $createPipe['status']) {

                    $transaction->commit();
                    $arrReturn = [
                        'status' => 1,
                        'info'   => '订单创建成功',
                    ];

                } else {
                    #回滚事务
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '订单创建失败',
                    ];
                }

                return $this->renderJSON($arrReturn);

            } else if ($post['price'] <= 0) {
                #当补款金额为0，则表示无差价更换配置，直接生成流程。或者补款金额小于0时，直接生成流程，还需要退款客户到余额中
                if ($post['price'] == 0) {
                    $payType    = '无金额';
                    $createPipe = PipeLine::Line_ChangeConfig('无金额', null, $trade_config);
                } else {
                    $refund_amount                 = $post['price'] * -1;
                    $trade_config['refund_amount'] = $refund_amount;
                    $payType                       = '余额退款';
                    $createPipe                    = PipeLine::Line_ChangeConfig('退款', null, $trade_config);
                }

                if ($createPipe['status']) {
                    $transaction->commit();
                    $arrReturn = ['status' => 1, 'info' => $createPipe['info']];
                } else {
                    $transaction->rollBack();
                    $arrReturn = ['status' => 0, 'info' => $createPipe['info']];
                }
                return $this->renderJSON($arrReturn);

            }
        }
    }

    #新版 修改产品 服务起止时间（未用）
    public
    function actionUpdateServicetime()
    {
        $MemberPdtModel = new MemberPdt();
        if (Yii::$app->request->post()) {
            $post           = $this->post();
            $MemberPdtQuery = $MemberPdtModel->findOne($post['id']);
            #时间逻辑判断
            $start_time = strtotime($post['start_time']);
            $end_time   = strtotime($post['end_time']);

            if (empty($start_time) || empty($end_time)) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '创建或到期时间不能为空',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($start_time > $end_time) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '创建时间不能小于到期时间',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($start_time == $MemberPdtQuery->start_time && $end_time == $MemberPdtQuery->end_time) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '时间未修改',
                ];
                return $this->renderJSON($arrReturn);
            }
            #开启事务
            $transaction = Yii::$app->db->beginTransaction();

            $ReturnFlowRes = WorkFlowApi::workflow_updateservicetime($MemberPdtQuery->unionid, $start_time, $end_time, $this->getAdminInfo('uname'));

            if ($ReturnFlowRes) {
                $transaction->commit();
                $arrReturn = [
                    'status' => 1,
                    'info'   => '更改产品时间操作或新增流程成功',
                ];
            } else {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '更改产品时间操作或新增流程失败',
                ];
            }
            return $this->renderJSON($arrReturn);

        } else {
            $id     = $this->get('id');
            $id     = intval($id);
            $arrRes = $MemberPdtModel->getRowById($id); #print_r($arrRes);die();
            if (empty($arrRes))
                return $this->redirect(Url::to(['index']));

            return $this->render('update_time', ['arrRes' => $arrRes]);

        }

    }

    /**
     *新版  修改信息
     *    主要修改 ：产品销售价格 (续费周期)
     * 付款周期
     * 基础成本价格（续费周期）
     * 额外配件成本价格（续费周期）
     * 成本价货币类型
     * 机器服务起止时间
     **/
    public
    function actionUpdate()
    {
        $MemberPdtModel = new MemberPdt();
        $PdtLogModel    = new PdtLog();
        if (Yii::$app->request->post()) {
            $post               = $this->post();
            $id                 = $post['id'];
            $id                 = intval($id);
            $post['start_time'] = strtotime($post['start_time']);
            $post['end_time']   = strtotime($post['end_time']);

            $MemberPdtQuery = $MemberPdtModel->findOne($id);
            if ($MemberPdtQuery->status != 1 || $MemberPdtQuery->audit_status != 1) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '当前产品不在正常状态或未审核',
                ];
                return $this->renderJSON($arrReturn);
            }

            #对比数据是否有变化
            $hasDataChanged = false;
            foreach ($post as $ek => $ev) {
                foreach ($MemberPdtQuery as $kk => $vv) {
                    if ($ek == $kk) {
                        if ($ev != $vv) {
                            $hasDataChanged = true;
                            break;
                        }
                    }
                }
            }

            if (!$hasDataChanged) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '未修改信息',
                ];
                return $this->renderJSON($arrReturn);
            }

            #开启事务
            $transaction                = Yii::$app->db->beginTransaction();
            $time                       = time();
            $post['request_admin_id']   = $this->getAdminInfo('admin_id');
            $post['request_admin_name'] = $this->getAdminInfo('uname');

            $callbackRes = PipeLine::Line_UpdateProductInfo($post);
            if ($callbackRes['status']) {
                $transaction->commit();
                $arrReturn = [
                    'status' => 1,
                    'info'   => '修改产品信息工作流提交成功',
                ];
            } else {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '修改产品信息工作流提交失败',
                ];
            }
            return $this->renderJSON($arrReturn);

        } else {
            $id     = $this->get('id');
            $id     = intval($id);
            $arrRes = $MemberPdtModel->find()->where(['id' => $id])->asArray()->one();
            if (empty($arrRes))
                return $this->redirect(Url::to(['index']));

            return $this->render('item', [
                'arrRes' => $arrRes,
            ]);
        }
    }

    #新版流程 更换机器
    public function actionReplace()
    {

        $PdtIpModel            = new PdtIp();
        $MemberPdtModel        = new MemberPdt();
        $PdtManageModel        = new PdtManage();
        $RoomManageModel       = new PdtRoomMange();
        $PdtCabinetManageModel = new PdtCabinetManage();
        $PdtLogModel           = new PdtLog();
        $IdlePdtModel          = new IdlePdt();
        $UserMemberModel       = new UserMember();
        $TestServerModel       = new TestServer();
        $TradeMainModel        = new TradeMain();
        $TradeDetailModel      = new TradeDetail();
        $SupplierIpModel       = new SupplierIp();
        $SwitchLineModel       = new SwitchLine();
        $InitialAccountModel   = new InitialAccount();

        if (Yii::$app->request->post()) {

            $post     = DataHelper::dotrim($this->post()); #print_r($post);exit;
            $unionid  = $post['unionid'];
            $testid   = ArrayHelper::getValue($post, 'data.test_id');
            $provider = $post['provider'];

            #获取当前产品的对象信息
            $MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $unionid])->asArray()->one();

            if (empty($MemberPdtRes)) {
                $arrReturn = ['status' => 0, 'info' => '未知的用户产品'];
                return $this->renderJSON($arrReturn);
            }
            if ($MemberPdtRes['status'] != 1 || $MemberPdtRes['audit_status'] != 1) {
                $arrReturn = ['status' => 0, 'info' => '当前产品不在正常状态或未审核'];
                return $this->renderJSON($arrReturn);
            }
            if ($MemberPdtRes['end_time'] < time()) {
                $arrReturn = ['status' => 0, 'info' => '该产品已属于已到期产品'];
                return $this->renderJSON($arrReturn);
            }

            #获取初始账户
            $InitialAccountRes = $InitialAccountModel->find()->where(['unionid' => $MemberPdtRes['unionid']])->asArray()->one();

            $oldIP = json_decode($MemberPdtRes['ip2']);
            #获取当前产品的用户信息
            $UserMemberRes = $UserMemberModel->find()->where(['u_id' => $MemberPdtRes['user_id']])->asArray()->one();

            $trade_config['unionid']         = $unionid;
            $trade_config['use_original_ip'] = $post['data']['use_original_ip'];
            $trade_config['need_workorder']  = $post['data']['need_workorder'];
            #开启事务
            $transaction = Yii::$app->db->beginTransaction();

            #判定是否选择的是测试机
            if (isset($testid) && $testid) {
                #查询出测试机的对象信息
                $TestServerQuery = $TestServerModel->find()->where(['id' => $testid])->one();
                if (empty($TestServerQuery)) {
                    $arrReturn = ['status' => 0, 'info' => '所选测试机器不存在'];
                    return $this->renderJSON($arrReturn);
                }
                #如果测试机状态不为0 即不为测试阶段
                if ($TestServerQuery->status != '0') {
                    $arrReturn = ['status' => 0, 'info' => '所选测试机器当前状态不可用'];
                    return $this->renderJSON($arrReturn);
                }
                #然后将测试机改为占用
                $TestServerQuery->status = 4;
                if (!$TestServerQuery->save()) {
                    $transaction->rollBack();
                    $arrReturn = ['status' => 0, 'info' => '所选测试机器状态更新失败'];
                    return $this->renderJSON($arrReturn);
                }
                $trade_config['testid'] = $testid;

                #获取配置#如果为自有的
                if ($provider == 0) {
                    $trade_config['servicerprovider'] = 0;
                    $trade_config['server_type_id']   = $TestServerQuery->server_type_id;
                    $trade_config['room_id']          = $TestServerQuery->room_id;
                    $trade_config['cabinet_id']       = $TestServerQuery->cabinet_id;
                    $trade_config['pdt_id']           = $TestServerQuery->pdt_id;

                    $trade_config['idle_id'] = $TestServerQuery->idle_id;
                    $trade_config['ipmi_ip'] = $TestServerQuery->ipmi_ip;

                    $config                          = json_decode($TestServerQuery->config, true);
                    $config['requirement_bandwidth'] = $post['data']['requirement_bandwidth'];
                    $trade_config['config']          = $config;

                    $trade_config['ip']  = json_decode($TestServerQuery->ip, true);
                    $trade_config['ip2'] = json_decode($TestServerQuery->ip2, true);

                    $trade_config['install_account'] = $TestServerQuery->account_name;
                    $trade_config['install_pass']    = $TestServerQuery->account_pwd;
                    $trade_config['install_port']    = $TestServerQuery->account_port;

                    $check_room_id = $TestServerQuery->room_id;

                } else {
                    #如果为供应商测试机
                    $trade_config['servicerprovider'] = 1;
                    $trade_config['server_type_id']   = $post['data']['server_typeid'];
                    $trade_config['room_id']          = $post['data']['room_id'];
                    $trade_config['pdt_id']           = $post['data']['pdt_id'];

                    $trade_config['provider_id'] = $post['data']['provider_id'];
                    $trade_config['ipmi_ip']     = '';

                    $config                          = $post['data']['config'];
                    $config['requirement_bandwidth'] = $post['data']['requirement_bandwidth'];
                    $trade_config['config']          = $config;

                    $iplist = DataHelper::dotrim($post['data']['iplist']);
                    #去重 去空
                    $iplist = array_filter(array_unique($iplist));

                    #如果使用原IP ，供应商可以不提交IP
                    if ($trade_config['use_original_ip'] != 1) {
                        if (empty($iplist) || !$iplist) {
                            $transaction->rollBack();
                            $arrReturn = ['status' => 0, 'info' => 'IP地址未填写！'];
                            return $this->renderJSON($arrReturn);
                        }
                    } else {
                        if (empty($iplist) || !$iplist) {
                            $iplist = [];
                        }
                    }
                    #拆分
                    $Retuen_IPArray = DataHelper::splitIP($iplist);
                    if (!$Retuen_IPArray['status']) {
                        $arrReturn = ['status' => 0, 'info' => $Retuen_IPArray['info']];
                        return $this->renderJSON($arrReturn);
                    }
                    $IPArray = $Retuen_IPArray['data'];

                    $trade_config['ip']  = $iplist;
                    $trade_config['ip2'] = $IPArray;

                    $trade_config['install_account'] = $post['data']['install_account'];
                    $trade_config['install_pass']    = $post['data']['install_pass'];
                    $trade_config['install_port']    = $post['data']['install_port'];

                    $check_provider_id = $post['data']['provider_id'];
                    $check_room_id     = $post['data']['room_id'];
                }

            } else {
                #如果不是测试机
                #根据提交信息判定为自有还是供应商 ( 1 为供应商的  0为自有的)
                if ($provider == 0) {
                    $IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $post['data']['idle_id']])->one();
                    if (empty($IdlePdtQuery)) {
                        $transaction->rollBack();
                        $arrReturn = ['status' => 0, 'info' => '所选自有库机器不存在！'];
                        return $this->renderJSON($arrReturn);
                    }
                    #如果自有库机器状态不为闲置  服务器状态不为闲置服务器 就返回
                    if ($IdlePdtQuery->status != 0 || $IdlePdtQuery->attribute_id != 1) {
                        $transaction->rollBack();
                        $arrReturn = ['status' => 0, 'info' => '所选自有库机器状态不为闲置！'];
                        return $this->renderJSON($arrReturn);
                    }
                    #将自有机器状态改为待定
                    $IdlePdtQuery->status = 4;
                    if (!$IdlePdtQuery->save()) {
                        $transaction->rollBack();
                        $arrReturn = ['status' => 0, 'info' => '所选自有机器状态更新失败'];
                        return $this->renderJSON($arrReturn);
                    }

                    $trade_config['servicerprovider'] = 0;
                    $trade_config['server_type_id']   = $IdlePdtQuery->server_type_id;
                    $trade_config['room_id']          = $IdlePdtQuery->room_id;
                    $trade_config['cabinet_id']       = $IdlePdtQuery->cabinet_id;
                    $trade_config['pdt_id']           = $IdlePdtQuery->pdt_id;

                    $trade_config['idle_id'] = $IdlePdtQuery->id;
                    $trade_config['ipmi_ip'] = $IdlePdtQuery->ipmi_ip;

                    $config                          = json_decode($IdlePdtQuery->config, true);
                    $config['requirement_bandwidth'] = $post['data']['requirement_bandwidth'];
                    $trade_config['config']          = $config;

                    $trade_config['ip']  = json_decode($IdlePdtQuery->ip, true);
                    $trade_config['ip2'] = json_decode($IdlePdtQuery->ip2, true);

                    $check_room_id = $IdlePdtQuery->room_id;

                } else {
                    #供应商机器
                    $iplist = DataHelper::dotrim($post['data']['iplist']);
                    #去重 去空
                    $iplist && $iplist = array_filter(array_unique($iplist));

                    if ($trade_config['use_original_ip'] != 1) {             #不使用原IP  IP必填
                        if (empty($iplist) || !$iplist) {
                            $transaction->rollBack();
                            $arrReturn = ['status' => 0, 'info' => 'IP地址未填写！'];
                            return $this->renderJSON($arrReturn);
                        }
                        #拆分
                        $Retuen_IPArray = DataHelper::splitIP($iplist);
                        if (!$Retuen_IPArray['status']) {
                            $arrReturn = ['status' => 0, 'info' => $Retuen_IPArray['info']];
                            return $this->renderJSON($arrReturn);
                        }
                        $IPArray = $Retuen_IPArray['data'];

                        #判定IP
                        $CheckRes = DataHelper::detect_supplierip($IPArray); #print_r($CheckRes);exit;
                        if ($CheckRes['status'] == 0) {
                            $transaction->rollBack();
                            return $this->renderJSON(['status' => 0, 'info' => $CheckRes['info']]);
                        }
                        #供应商IP入库
                        $insertRes = $SupplierIpModel->add_peration($IPArray, $post['data']['provider_id'], $post['data']['room_id'], '2');
                        if ($insertRes != count($IPArray)) {
                            $transaction->rollBack();
                            $arrReturn = ['status' => 0, 'info' => '供应商IP入库失败'];
                            return $this->renderJSON($arrReturn);
                        }

                    } else { #如果使用原IP ，供应商可以不提交IP
                        #拆分
                        $Retuen_IPArray = DataHelper::splitIP($iplist);
                        if (!$Retuen_IPArray['status']) {
                            $arrReturn = ['status' => 0, 'info' => $Retuen_IPArray['info']];
                            return $this->renderJSON($arrReturn);
                        }
                        $IPArray = $Retuen_IPArray['data'];
                    }

                    #print_r($return_insert);exit;

                    $trade_config['servicerprovider'] = 1;
                    $trade_config['server_type_id']   = $post['data']['server_typeid'];
                    $trade_config['room_id']          = $post['data']['room_id'];
                    $trade_config['pdt_id']           = $post['data']['pdt_id'];

                    $trade_config['provider_id'] = $post['data']['provider_id'];
                    $trade_config['ipmi_ip']     = '';

                    $config                          = $post['data']['config'];
                    $config['requirement_bandwidth'] = $post['data']['requirement_bandwidth']; #要求带宽
                    $trade_config['config']          = $config;

                    $trade_config['ip']  = $iplist;
                    $trade_config['ip2'] = $IPArray;

                    $trade_config['install_account'] = $post['data']['install_account'];
                    $trade_config['install_pass']    = $post['data']['install_pass'];
                    $trade_config['install_port']    = $post['data']['install_port'];

                    $check_provider_id = $post['data']['provider_id'];
                    $check_room_id     = $post['data']['room_id'];
                }
            }


            $trade_config['service_starttime'] = date("Y-m-d", time());
            $trade_config['service_endtime']   = date("Y-m-d", $MemberPdtRes['end_time']);

            $trade_config['provider'] = $provider;
            $trade_config['remark']   = $post['data']['remark'];
            $trade_config['server_id'] = $post['data']['server_id'];

            #如果等于1 就是用之前的IP
            if ($trade_config['use_original_ip'] == 1) {
                #但如果提供商之前和现在不是一致的
                if ($MemberPdtRes['servicerprovider'] != $provider) {
                    $transaction->rollBack();
                    $arrReturn = ['status' => 0, 'info' => '产品之前服务提供者与更换后不一致，不能选择使用之前IP！'];
                    return $this->renderJSON($arrReturn);
                } else {
                    #如果提供商为一致的
                    if ($provider == 0) {
                        #如果为自有的，判断机房是否一致
                        if ($check_room_id != $MemberPdtRes['room_id']) {
                            $transaction->rollBack();
                            $arrReturn = ['status' => 0, 'info' => '选择使用原IP，但更换机器前后机房不一致！'];
                            return $this->renderJSON($arrReturn);
                        }
                        #验证IP线路与交换机线路
                        #判定一下 现在的IP是否的Vlan 是符合 #获取机器交换机对应线路的Vlan
                        $check_ip = json_decode($MemberPdtRes['ip2'], true);

                        if (!empty($check_ip)) {
                            $PdtIpAll      = $PdtIpModel->find()->select('room_id, line_type_id,vlan')->where(['in', 'ip', $check_ip])->asArray()->all();
                            $line_typeList = array_column($PdtIpAll, 'line_type_id');
                            $line_typeList = array_unique($line_typeList);
                            if (count($line_typeList) > 1) {
                                $transaction->rollBack();
                                $arrReturn = ['status' => 0, 'info' => '原IP与其他IP的线路类型不匹配'];
                                return $this->renderJSON($arrReturn);
                            }

                            $IdlePdtRes_1 = $IdlePdtModel->find()->where(['id' => $trade_config['idle_id']])->asArray()->one();

                            #获取机器对应交换机的线路信息
                            $SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtRes_1['switch_location'], 'line_type_id' => $line_typeList[0]])->asArray()->one();
                            #print_r($SwitchLineList);exit;
                            if (empty($SwitchLineList)) {
                                $transaction->rollBack();
                                $arrReturn = ['status' => 0, 'info' => '所选机器对应的交换机线路中未有IP的线路类型'];
                                return $this->renderJSON($arrReturn);
                            }
                        }

                    } else {
                        #如果为供应商的。判断供应商是否一致
                        if ($check_provider_id != $MemberPdtRes['provider_id'] || $check_room_id != $MemberPdtRes['room_id']) {
                            $transaction->rollBack();
                            $arrReturn = ['status' => 0, 'info' => '选择使用原IP，但更换机器前后机房或供应商不一致！'];
                            return $this->renderJSON($arrReturn);
                        }
                    }
                }
            }

            $trade_config['sell_price']       = $post['data']['sell_price'];
            $trade_config['difference_price'] = $post['data']['difference_price'];

            #集成更换机器前的信息
            $front_data['provider']         = $MemberPdtRes['servicerprovider'];
            $front_data['servicerprovider'] = $MemberPdtRes['servicerprovider'];
            $front_data['room_id']          = $MemberPdtRes['room_id'];
            $front_data['cabinet_id']       = $MemberPdtRes['cabinet_id'];
            $front_data['server_type_id']   = $MemberPdtRes['server_type_id'];
            $front_data['pdt_id']           = $MemberPdtRes['pdt_id'];

            $front_data['idle_id']     = $MemberPdtRes['idle_id'];
            $front_data['provider_id'] = $MemberPdtRes['provider_id'];
            $front_data['ipmi_ip']     = $MemberPdtRes['ipmi_ip'];

            $front_data['config'] = json_decode($MemberPdtRes['config'], true);
            $front_data['ip']     = json_decode($MemberPdtRes['ip'], true);
            $front_data['ip2']    = json_decode($MemberPdtRes['ip2'], true);

            $front_data['install_account'] = $InitialAccountRes['name'];
            $front_data['install_pass']    = $InitialAccountRes['pwd'];
            $front_data['install_port']    = $InitialAccountRes['port'];
            #集成更换后的信息
            $after_data['provider']         = $trade_config['provider'];
            $after_data['servicerprovider'] = $trade_config['servicerprovider'];
            $after_data['server_type_id']   = $trade_config['server_type_id'];
            $after_data['room_id']          = $trade_config['room_id'];
            $after_data['pdt_id']           = $trade_config['pdt_id'];

            $after_data['test_id']     = $testid;
            $after_data['idle_id']     = $trade_config['idle_id'];
            $after_data['provider_id'] = $trade_config['provider_id'];
            $after_data['ipmi_ip']     = $trade_config['ipmi_ip'];

            $after_data['config'] = $trade_config['config'];
            if ($trade_config['use_original_ip'] == 1) {
                $after_data['ip']        = json_decode($MemberPdtRes['ip'], true);
                $after_data['ip2']       = json_decode($MemberPdtRes['ip2'], true);
                $after_data['should_ip'] = $trade_config['ip'];
            } else {
                $after_data['ip']  = $trade_config['ip'];
                $after_data['ip2'] = $trade_config['ip2'];
            }

            if ($trade_config['install_account']) {
                $after_data['install_account'] = $trade_config['install_account'];
                $after_data['install_pass']    = $trade_config['install_pass'];
                $after_data['install_port']    = $trade_config['install_port'];
            }
            #合成
            $Postdata['unionid'] = $trade_config['unionid'];
            #$Postdata['testid'] = $testid;
            $Postdata['user_id']           = $MemberPdtRes['user_id'];
            $Postdata['frontData']         = $front_data;
            $Postdata['afterData']         = $after_data;
            $Postdata['use_original_ip']   = $trade_config['use_original_ip'];
            $Postdata['need_workorder']    = $trade_config['need_workorder'];
            $Postdata['service_starttime'] = $trade_config['service_starttime'];
            $Postdata['service_endtime']   = $trade_config['service_endtime'];
            $Postdata['remarks']           = $trade_config['remark'];
            $Postdata['old_sell_price']    = $MemberPdtRes['sell_price'];
            $Postdata['sell_price']        = $trade_config['sell_price'];
            $Postdata['difference_price']  = $trade_config['difference_price'];

            #判定金额
            if ($post['data']['sell_price'] == '' || $post['data']['difference_price'] == '') {
                $arrReturn = ['status' => 0, 'info' => '请输入正确的价格！'];
                return $this->renderJSON($arrReturn);
            }

            if (!is_numeric($post['data']['sell_price']) || !is_numeric($post['data']['difference_price'])) {
                $arrReturn = ['status' => 0, 'info' => '请输入正确的价格！'];
                return $this->renderJSON($arrReturn);
            }
            #判断是否有差价。如果有差价 就要生成订单，反之就根据流程是否审核，生成流程或是直接完成
            #当补款金额大于零时  下订单。等于零 不下订单。小于零是不下订单  但在完成时会返回款项

            if ($post['data']['difference_price'] > 0) {

                $time         = time();
                $tradeOrderid = DataHelper::createTradeno();

                $PipeTypesModel = new PipelineTypes();
                $PipeTypesRes   = $PipeTypesModel->find()->where(['line_type_name' => '更换机器-补款'])->asArray()->one();

                if (!$PipeTypesRes) {
                    return ['status' => 0, 'info' => '工作记录类型不存在'];
                }
                if ($PipeTypesRes['line_first_trial'] == 'Y') {
                    $order_status = '待审核';
                } else {
                    $order_status = '进行中';
                }

                #生成主订单insert
                $OriginalModel                       = new PayorderOriginal();
                $OriginalModel->order_id             = $tradeOrderid;
                $OriginalModel->order_type           = '更换机器';
                $OriginalModel->order_user_id        = $UserMemberRes['u_id'];
                $OriginalModel->order_user_nickname  = $UserMemberRes['uname'];
                $OriginalModel->order_user_email     = $UserMemberRes['email'];
                $OriginalModel->order_admin_id       = $UserMemberRes['admin_id'];
                $OriginalModel->order_admin_name     = $UserMemberRes['admin_name'];
                $OriginalModel->order_original_price = $post['data']['difference_price'];
                $OriginalModel->order_update_price   = $post['data']['difference_price'];
                $OriginalModel->order_update_remark  = null;
                $OriginalModel->order_update_status  = '未锁定';
                $OriginalModel->order_amount_money   = 0;
                $OriginalModel->order_finish_pay     = 0;
                $OriginalModel->order_pay_status     = '未支付';
                $OriginalModel->order_remark         = null;
                $OriginalModel->order_status         = $order_status;
                $OriginalModel->order_time_create    = $time;

                #生成支付单insert
                $parentOrder = DataHelper::createPayorderGeneralID();

                $GeneralModel                               = new PayorderGeneral();
                $GeneralModel->general_payorder_title       = '更换机器';
                $GeneralModel->general_payorder_number      = null;
                $GeneralModel->general_payorder             = $parentOrder;
                $GeneralModel->general_original_order       = $tradeOrderid;
                $GeneralModel->general_pay_money            = $post['data']['difference_price'];
                $GeneralModel->general_pay_platform         = null;
                $GeneralModel->general_pay_channel          = null;
                $GeneralModel->general_pay_time             = null;
                $GeneralModel->general_pay_lock             = '等待支付';
                $GeneralModel->general_payinfo_review       = '未审核';
                $GeneralModel->general_payorder_type        = 'original';
                $GeneralModel->general_callback_stream      = null;
                $GeneralModel->general_command_adminid_list = $UserMemberRes['admin_id'];
                $GeneralModel->general_payment_userid       = $MemberPdtRes['user_id'];
                $GeneralModel->general_create_time          = $time;

                #生成订单详情
                $DetailModel                        = new PayorderDetail();
                $DetailModel->detail_original_order = $tradeOrderid;
                $DetailModel->detail_title          = '更换产品';
                $DetailModel->detail_content        = json_encode($trade_config, JSON_UNESCAPED_UNICODE);
                $DetailModel->detail_price          = $post['data']['difference_price'];
                $DetailModel->detail_remark         = null;

                $insertMainResult    = $OriginalModel->save();
                $insertGeneralResult = $GeneralModel->save();
                $insertDetailResult  = $DetailModel->save();

                if (!$insertMainResult || !$insertGeneralResult || !$insertDetailResult) {
                    $arrReturn = ['status' => 1, 'info' => '新增更换机器订单失败！'];
                }

                #创建更换机器工作流
                $createPipe = PipeLine::Line_ReplaceMachine('补款', $tradeOrderid, $Postdata);

                if ($createPipe['status']) {
                    $transaction->commit();
                    $arrReturn = ['status' => 1, 'info' => '提交更换机器工作流成功！'];
                } else {
                    $transaction->rollBack();
                    $arrReturn = ['status' => 0, 'info' => $createPipe['info']];
                }
                return $this->renderJSON($arrReturn);

            } else {
                if ($post['data']['difference_price'] < 0) {
                    $payType = '余额退款';
                } else {
                    $payType = '无金额';
                }
            }

            if ($payType == '余额退款') {

                $Postdata['difference_price'] = $Postdata['difference_price'] * -1;

                #创建更换机器工作流
                $createPipe = PipeLine::Line_ReplaceMachine('退款', null, $Postdata);

                if ($createPipe['status']) {
                    $transaction->commit();
                    $arrReturn = ['status' => 1, 'info' => '提交更换机器工作流成功！'];
                } else {
                    $transaction->rollBack();
                    $arrReturn = ['status' => 0, 'info' => $createPipe['info']];
                }
                return $this->renderJSON($arrReturn);

            } else {

                //无金额的直接走工单...
                #创建更换机器工作流
                $createPipe = PipeLine::Line_ReplaceMachine('无金额', null, $Postdata);

                if ($createPipe['status']) {
                    $transaction->commit();
                    $arrReturn = ['status' => 1, 'info' => $createPipe['info']];
                } else {
                    $transaction->rollBack();
                    $arrReturn = ['status' => 0, 'info' => $createPipe['info']];
                }
                return $this->renderJSON($arrReturn);

            }

        } else {
            #更换机器页面

            $id           = $this->get('id');
            $MemberPdtRes = $MemberPdtModel->find()->select('*')->With('servertype')->With('pdtroom')->With('pdtmanage')->where(['id' => $id])->asArray()->one();
            # print_r($MemberPdtRes);exit;exit;
            if (empty($MemberPdtRes)) {
                return $this->redirect(['index']);
            }
            #当前机器的配置
            $config = json_decode($MemberPdtRes['config'], true);
            #当前所属机器类别
            $pdtmanage = $MemberPdtRes['pdtmanage'][0];
            #当前服务器分类
            $servertype = $MemberPdtRes['servertype'][0];

            #获取机房
            $room_list = $RoomManageModel->find()->AsArray()->All();

            #获取服务器分类
            $PdtManageTypeModel = new PdtManageType();
            $pdt_type_list      = $PdtManageTypeModel->find()->AsArray()->All();
            #获取供应商
            $ProviderModel = new Provider();
            $ProviderList  = $ProviderModel->find()->asArray()->all();
            #货币类型
            $currency_typeRes = Yii::$app->params['currency_type']; #print_r($currency_type);die;
            $CostModel        = new Cost();
            $CostRes          = $CostModel->find()->where(['unionid' => $MemberPdtRes['unionid']])->asArray()->one();
            if ($CostRes) {
                $currency_type = $CostRes['currency_type'];
            } else {
                $currency_type = ArrayHelper::getValue($currency_typeRes, '0');
            }
            $IPArr = json_decode($MemberPdtRes['ip'], true); #print_r($IPArr);exit;
            $IPstr = implode(',', $IPArr);
            return $this->render('replace', [
                'MemberPdtRes'     => $MemberPdtRes,
                'room_list'        => $room_list,
                'nowprovider'      => $MemberPdtRes['servicerprovider'],
                'pdt_type_list'    => $pdt_type_list,
                'config'           => $config,
                'pdtmanage'        => $pdtmanage,
                'servertype'       => $servertype,
                'currency_typeRes' => $currency_typeRes,
                'currency_type'    => $currency_type,
                'testid'           => $testid ?? '',
                'IPstr'            => $IPstr,
                'ProviderList'     => $ProviderList,
            ]);
        }
    }

    /**
     * 新版流程 用户业务退款
     */
    public
    function actionRefund()
    {
        $MemberPdtModel         = new MemberPdt();
        $TradeModel             = new Trade();
        $PaymentFlowModel       = new PaymentFlow();
        $UserCreditHistoryModel = new UserCreditHistory();
        $UserCreditModel        = new UserCredit();

        if (Yii::$app->request->post()) {

            Yii::$app->request->isAjax || die('error');

            $post = $this->post();

            if ($post['money'] == null || $post['reason'] == null) {
                $arrReturn = ['status' => 0, 'info' => '退款金额与退款原因必须填写'];
                return $this->renderJSON($arrReturn);
            }

            if ($post['money'] < 0) {
                $arrReturn = ['status' => 0, 'info' => '自定义退款金额必须大于等于0元'];
                return $this->renderJSON($arrReturn);
            }

            #查询出产品的信息
            $MemberPdtQuery = $MemberPdtModel->findOne($post['id']);

            if (empty($MemberPdtQuery)) {
                $arrReturn = ['status' => 0, 'info' => '未知的产品信息'];
                return $this->renderJSON($arrReturn);
            }
            if ($MemberPdtQuery['status'] != 1 || $MemberPdtQuery['audit_status'] != 1) {
                $arrReturn = ['status' => 0, 'info' => '当前产品不在正常状态或未审核'];
                return $this->renderJSON($arrReturn);
            }

            if ($MemberPdtQuery['sell_price'] < $post['money']) {
                $arrReturn = ['status' => 0, 'info' => '退款金额不能大于业务总价'];
                return $this->renderJSON($arrReturn);
            }

            #开启事务
            $transaction = Yii::$app->db->beginTransaction();

            if ($post['refund_to'] == 'credit_deduction') {

                if ($post['money'] == 0) {
                    $arrReturn = ['status' => 0, 'info' => '挂账抵扣金额必须大于0'];
                    return $this->renderJSON($arrReturn);
                }

                $refund_amount = $post['money'];

                if ($post['payment_flow_no'] == '' || $post['payment_flow_no'] == null) {
                    $arrReturn = ['status' => 0, 'info' => '选择挂账抵扣需要填写支付流水交易号'];
                    return $this->renderJSON($arrReturn);
                }

                $PaymentFlowRes = $PaymentFlowModel->find()->where(['payment_flow_no' => $post['payment_flow_no']])->asArray()->one(); #流水是只会存在一笔

                if (empty($PaymentFlowRes)) {
                    $arrReturn = ['status' => 0, 'info' => '所填写的支付流水不存在'];
                    return $this->renderJSON($arrReturn);
                }

                $UserCreditHistoryRes = $UserCreditHistoryModel->find()->where(['credit_payment_flow_id' => $post['payment_flow_no'], 'credit_pay_status' => '等待支付'])->asArray()->all();  #挂账开始只有一笔，但可能拆分多笔

                if (empty($UserCreditHistoryRes)) {
                    $arrReturn = ['status' => 0, 'info' => '未有对应的挂账记录'];
                    return $this->renderJSON($arrReturn);
                }

                $credit_ids = array_unique(array_column($UserCreditHistoryRes, 'credit_id'));
                if (count($credit_ids) > 1) {
                    $arrReturn = ['status' => 0, 'info' => '匹配的挂账记录存在多个挂账账户'];
                    return $this->renderJSON($arrReturn);
                }

                $credit_id = $credit_ids[0];

                $UserCreditRes = $UserCreditModel->find()->where(['credit_id' => $credit_id])->asArray()->one();

                if (empty($UserCreditRes)) {
                    $arrReturn = ['status' => 0, 'info' => '挂账账户不存在'];
                    return $this->renderJSON($arrReturn);
                }

                if ($UserCreditRes['user_id'] != $MemberPdtQuery->user_id) {
                    $arrReturn = ['status' => 0, 'info' => '机器所属用户与挂账账户的用户不一致'];
                    return $this->renderJSON($arrReturn);
                }

                $matching_credits = [];
                $amount           = $refund_amount;
                foreach ($UserCreditHistoryRes as $key => $val) { #选择用哪笔挂账来抵扣

                    if ($amount < $val['credit_now_cmoney']) {
                        $matching_credits[] = $val['credit_h_id'];
                        break;
                    } else if ($amount == $val['credit_now_cmoney']) {
                        $matching_credits[] = $val['credit_h_id'];
                        break;
                    } else if ($amount > $val['credit_now_cmoney']) {
                        $matching_credits[] = $val['credit_h_id'];
                        continue;
                    }

                    $amount = $amount - $val['credit_now_cmoney'];
                    if ($amount <= 0) {
                        break;
                    }

                }

                if (empty($matching_credits)) {
                    $arrReturn = ['status' => 0, 'info' => '未匹配到对应的挂账记录'];
                    return $this->renderJSON($arrReturn);
                }

                #将匹配到挂账记录更改为支付中
                $UpdateCreditHistoryStatus = $UserCreditHistoryModel->updateAll(['credit_pay_status' => '支付中'], ['credit_h_id' => $matching_credits]);
                if ($UpdateCreditHistoryStatus != count($matching_credits)) {
                    $transaction->rollBack();
                    $arrReturn = ['status' => 0, 'info' => '匹配到对应的挂账记录信息更改失败'];
                    return $this->renderJSON($arrReturn);
                }

            }

            $data['refund_amount'] = $post['money'];
            $data['refund_reason'] = $post['reason'];
            $data['need_handle']   = $post['need_handle'];
            $data['refund_to']     = $post['refund_to'];
            if ($post['refund_to'] == 'credit_deduction') {
                $data['payment_flow_no']  = $post['payment_flow_no'];
                $data['matching_credits'] = $matching_credits;
            }

            $createPipe = PipeLine::Line_BusinessRefund($MemberPdtQuery->unionid, $data);

            if ($createPipe['status']) {
                $transaction->commit();
                $arrReturn = ['status' => 1, 'info' => $createPipe['info']];
            } else {
                $transaction->rollBack();
                $arrReturn = ['status' => 0, 'info' => $createPipe['info']];

            }
            return $this->renderJSON($arrReturn);

        } else {
            $id           = $this->get('id');
            $MemberPdtRes = $MemberPdtModel->getRowById($id);                                                                                               ##// print_r($MemberPdtRes);die();
            if (empty($MemberPdtRes)) {
                return $this->redirect(['index']);
            }

            $ipArr = json_decode(ArrayHelper::getValue($MemberPdtRes, 'ip'), true);
            $ip    = '';
            if (count($ipArr) > 12) {
                for ($i = 0; $i < count($ipArr); $i++) {
                    if ($i % 11 == 0 && $i != 0) {
                        if ($i == count($ipArr) - 1) {
                            $ip .= $ipArr[$i];
                        } else {
                            $ip .= $ipArr[$i] . '&nbsp;&nbsp;<br/>';
                        }
                    } else {
                        if ($i == count($ipArr) - 1) {
                            $ip .= $ipArr[$i];
                        } else {
                            $ip .= $ipArr[$i] . '&nbsp;&nbsp;';
                        }
                    }
                }
            } else {
                $ip = implode('&nbsp;&nbsp;', $ipArr);
            }

            //获取到购买的单
            $TradeRes = $TradeModel->find()->where(['unionid' => $MemberPdtRes['unionid'], 'trade_orderid' => $MemberPdtRes['trade_no']])->asArray()->one();//print_r($TradeRes);die();
            //获取对应的订单信息

            $TradelistRes = $TradeModel->find()->where(['unionid' => $MemberPdtRes['unionid'], 'trade_status' => "已支付"])->orderBy("trade_time_create")->asArray()->all();//print_r($TradeRes);die();

            return $this->render('refund', ['MemberPdtRes' => $MemberPdtRes, 'TradeRes' => $TradeRes, 'TradelistRes' => $TradelistRes, 'ip' => $ip]);
        }
    }

    /*
		新版流程 预约下架
	*/
    public
    function actionAppointOff()
    {

        Yii::$app->request->isAjax || die('error');

        $AppointModel   = new MemberPdtAppointOff();
        $MemberPdtModel = new MemberPdt();

        $post = $this->post();

        if (!$post['unionid'] || !$post['appoint_time']) {
            return $this->renderJSON(['status' => 0, 'info' => '缺少预约信息']);
        }

        $pdtinfo = $MemberPdtModel->find()->where(["unionid" => $post['unionid']])->one();

        if (!$pdtinfo) {
            return $this->renderJSON(['status' => 0, 'info' => '业务不存在']);
        }

        if ($pdtinfo->status != 1) {
            return $this->renderJSON(['status' => 0, 'info' => '机器状态异常，请处理完所有业务并且未到期，再预约下架']);
        }

        if ($pdtinfo->end_time < strtotime($post['appoint_time'])) {
            return $this->renderJSON(['status' => 0, 'info' => '预约时间超过了到期时间，不能进行预约']);
        }

        if (strtotime($post['appoint_time']) < time()) {
            return $this->renderJSON(['status' => 0, 'info' => '预约时间异常，请重新选择']);
        }

        #检查是否可以关机下架
        $CheckRes = BusinessCheck::CheckOverdueDel($pdtinfo->unionid);
        if (!$CheckRes['status']) {
            return $this->renderJSON(['status' => 0, 'info' => $CheckRes['info']]);
        }

        $AppointModel->appoint_unionid     = $pdtinfo->unionid;
        $AppointModel->appoint_create_time = time();
        $AppointModel->appoint_time        = strtotime($post['appoint_time']);

        if ($AppointModel->insert()) {
            return $this->renderJSON(['status' => 1, 'info' => '预约完成']);
        } else {
            return $this->renderJSON(['status' => 0, 'info' => '预约机器下架异常']);
        }
    }

    /*
		新版流程 取消预约下架
	*/
    public
    function actionAppointCancel()
    {

        Yii::$app->request->isAjax || die('error');

        $AppointModel   = new MemberPdtAppointOff();
        $MemberPdtModel = new MemberPdt();

        $post = $this->post();

        if (!$post['unionid']) {
            return $this->renderJSON(['status' => 0, 'info' => '缺少预约信息']);
        }

        $pdtinfo = $MemberPdtModel->find()->where(["unionid" => $post['unionid']])->one();

        if (!$pdtinfo) {
            return $this->renderJSON(['status' => 0, 'info' => '业务不存在']);
        }

        $appointInfo = $AppointModel->find()->where(["appoint_unionid" => $post['unionid']])->one();

        if (!$appointInfo) {
            return $this->renderJSON(['status' => 0, 'info' => '预约已被取消']);
        }

        if ($appointInfo->delete()) {
            return $this->renderJSON(['status' => 1, 'info' => '取消预约下架完成']);
        } else {
            return $this->renderJSON(['status' => 0, 'info' => '取消预约下架出现异常']);
        }
    }

    /*
		新版流程 立即下架
	*/
    public
    function actionAppointOffNow()
    {

        Yii::$app->request->isAjax || die('error');

        $MemberPdtModel = new MemberPdt();

        $post = $this->post();

        if (!$post['unionid']) {
            return $this->renderJSON(['status' => 0, 'info' => '缺少业务信息']);
        }

        $pdtinfo = $MemberPdtModel->find()->where(["unionid" => $post['unionid']])->one();

        if (!$pdtinfo) {
            return $this->renderJSON(['status' => 0, 'info' => '业务不存在']);
        }

        if ($pdtinfo->status != 1) {
            return $this->renderJSON(['status' => 0, 'info' => '机器状态异常，请处理完所有业务并且未到期，再进行下架操作']);
        }

        $BackRes = PipeLine::Line_BusinessShutdownLowerShelf($post["unionid"]);

        if ($BackRes['status'] == 1) {
            return $this->renderJSON(['status' => 1, 'info' => '操作成功，已创建工作任务']);
        } else {
            return $this->renderJSON(['status' => 0, 'info' => '操作出现异常']);
        }
    }

    /**
     * 新版 业务过户
     */
    public
    function actionBusinessTransfer()
    {
        $MemberPdtModel    = new MemberPdt();
        $UserMemberModel   = new UserMember();
        $UserAdminModel    = new UserAdmin();
        $WorkFlowRoleModel = new WorkFlowRole();

        if (Yii::$app->request->post()) {
            $post       = $this->post();
            $toUserName = $post['toUserName'];
            $toUserID   = $post['toUserID'];
            $remarks    = $post['remarks'];

            $MemberPdtQuery = $MemberPdtModel->findone($post['id']);  //print_r($post);die;

            if (empty($MemberPdtQuery)) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '业务不存在',
                ];
                return $this->renderJSON($arrReturn);
            }

            if ($MemberPdtQuery->status != 1 || $MemberPdtQuery->audit_status != 1) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '当前业务不在正常状态或未审核',
                ];
                return $this->renderJSON($arrReturn);
            }

            $UserMemberRes = $UserMemberModel->find()->where(['u_id' => $toUserID])->asArray()->one();
            if (empty($UserMemberRes)) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '选择的用户不存在',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($UserMemberRes['admin_id'] == "" || $UserMemberRes['admin_id'] == NULL) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '选择的用户未分配客服',
                ];
                return $this->renderJSON($arrReturn);
            }

            if ($UserMemberRes['u_id'] == $MemberPdtQuery->user_id) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '双方用户信息不能一样！',
                ];
                return $this->renderJSON($arrReturn);
            }
            $data = [
                'unionid'            => $MemberPdtQuery->unionid,
                'front_useremail'    => $MemberPdtQuery->user_name,
                'front_userid'       => $MemberPdtQuery->user_id,
                'after_useremail'    => $toUserName,
                'after_userid'       => $toUserID,
                'remarks'            => $remarks,
                'request_admin_id'   => $this->getAdminInfo('admin_id'),
                'request_admin_name' => $this->getAdminInfo('uname'),
            ];
            #开启事务
            $transaction = Yii::$app->db->beginTransaction();
            /*
			$WorkFlowRoleRes = $WorkFlowRoleModel->find()->where(['wr_name' => '用户机器过户'])->asArray()->one();

			$RetuenRes = WorkFlowApi::workflow_business_transfer($MemberPdtQuery->unionid, $toUserID,$remarks, $this->getAdminInfo('admin_id'), $this->getAdminInfo('uname'));
			*/
            $callbackRes = PipeLine::Line_MachineTransfer($data);
            if (!$callbackRes['status']) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => $callbackRes['info'],
                ];
            } else {
                $transaction->commit();
                $arrReturn = [
                    'status' => 1,
                    'info'   => '业务过户工作提交成功，请等待处理',
                ];

            }

            return $this->renderJSON($arrReturn);

        } else {
            $id           = $this->get('id');
            $MemberPdtRes = $MemberPdtModel->find()->where(['id' => $id])->asArray()->one();  ##print_r($MemberPdtRes);die;
            $username = ArrayHelper::getValue($MemberPdtRes, 'user_name');
            $user_id = ArrayHelper::getValue($MemberPdtRes, 'user_id');
            if(!$username){
                $user = UserMember::find()->where(['u_id'=>$user_id])->with('member')->one();
                $username = \common\helpers\MemberHelper::userAccount($user);
                ArrayHelper::setValue($MemberPdtRes, 'user_name', $username);
            }

            return $this->render('business-transfer', ['MemberPdtRes' => $MemberPdtRes]);
        }
    }

    /**
     * 新版 续费确认页
     * 新版 续费执行创建订单
     */
    public
    function actionRenew()
    {
        $MemberPdtModel  = new MemberPdt();
        $UserMemberModel = new UserMember;

        $TradeMainModel       = new TradeMain();
        $TradeMainDetailModel = new TradeDetail();

        if (Yii::$app->request->post()) {
            Yii::$app->request->isAjax || die('error');

            /*#基本业务判断
            if (!empty($this->post('expire')) &&  $this->post('expire') !== '' ) {
                if(!in_array($this->post('expire'), [1,3,6,12])) {
                    return $this->renderJSON([
                        'status'=>0,
                        'info'=>'续费时长选择未知'
                    ]);
                }
            }*/

            $post = $this->post();
            #要下单的业务信息
            $pdtInfo = $MemberPdtModel->find()->where(['id' => $post['id']])->asArray()->one();

            if (empty($pdtInfo)) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '操作错误，因未有对应的产品',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($pdtInfo['status'] != 1 || $pdtInfo['audit_status'] != 1) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '当前产品不在正常状态或未审核',
                ];
                return $this->renderJSON($arrReturn);
            }

            #初始化变量
            $time         = time();
            $tradeOrderid = DataHelper::createTradeno();
            #开启事务
            $transaction = Yii::$app->db->beginTransaction();
            #用户信息
            $userInfo = $UserMemberModel->find()->where(['u_id' => $pdtInfo['user_id']])->asArray()->one();

            #订单主表
            $TradeMainModel->trade_orderid     = $tradeOrderid;
            $TradeMainModel->user_id           = $userInfo['u_id'];
            $TradeMainModel->user_account      = $userInfo['email'];
            $TradeMainModel->admin_id          = $userInfo['admin_id'];
            $TradeMainModel->admin_name        = $userInfo['admin_name'];
            $TradeMainModel->trade_type        = 'machine_renewal';
            $TradeMainModel->trade_price_total = 0;
            $TradeMainModel->trade_price_fact  = 0;
            $TradeMainModel->trade_pay_money   = 0;
            $TradeMainModel->trade_status      = '未支付';
            $TradeMainModel->trade_time        = $time;
            $TradeMainModel->trade_report_id   = null;

            #订单副表
            $detailColumn = ['trade_orderid', 'trade_config', 'trade_num', 'price_normal', 'price_fact', 'trade_remark'];
            $insertArray  = [];

            $TradeMainModel->trade_price_total = $TradeMainModel->trade_pay_money = $TradeMainModel->trade_price_fact += ($pdtInfo['sell_price'] * $post['renew_num']);

            $config = json_decode($pdtInfo['config'], true);

            if (!isset($config['requirement_bandwidth'])) {
                $config['requirement_bandwidth'] = $pdtInfo['bandwidth'];
            }
            $newConfig['config'] = $config;

            $newConfig['unionid']       = $pdtInfo['unionid'];
            $newConfig['old_end_time']  = $pdtInfo['end_time'];
            $newConfig['new_end_time']  = DataHelper::getRenewEndtime($pdtInfo['end_time'], $pdtInfo['payment_cycle'] * $post['renew_num']); #批量续费单默认1个单位，所以payment_cycle不需要乘上续费单位数
            $newConfig['pdt_id']        = $pdtInfo['pdt_id'];
            $newConfig['server_typeid'] = $pdtInfo['server_type_id'];
            $newConfig['ip']            = json_decode($pdtInfo['ip'], true);
            $newConfig['ip2']           = json_decode($pdtInfo['ip2'], true);
            $newConfig['payment_cycle'] = $pdtInfo['payment_cycle'];
            $newConfig['renew_num']     = $post['renew_num'];

            $insertArray[] = [
                $tradeOrderid,
                json_encode($newConfig, JSON_UNESCAPED_UNICODE),
                1,
                $pdtInfo['sell_price'],
                $pdtInfo['sell_price'],
                '',
            ];

            $insertMainResult   = $TradeMainModel->save();
            $insertDetailResult = Yii::$app->db->createCommand()->batchInsert(TradeDetail::tableName(), $detailColumn, $insertArray)->execute();
            $insertRevenueNotes = RevenueNotes::CreateNotes($tradeOrderid);

            if ($insertMainResult && $insertDetailResult && $insertRevenueNotes['status']) {
                $transaction->commit();
                return $this->renderJSON([
                    'status' => 1,
                    'info'   => '新增续费订单操作成功',
                ]);
            } else {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => '新增续费订单异常',
                ]);
            }
            exit;
        } else {
            $id           = $this->get('id');
            $MemberPdtRes = $MemberPdtModel->getRowById($id);//print_r($MemberPdtRes);die();
            if (empty($MemberPdtRes))
                return $this->redirect(Url::to(['index']));
            // if($MemberPdtRes['status'] == 2) return $this->redirect(Url::to(['index']));
            //获取当前服务器所属配置
            $pdtManageModel = new PdtManage();
            $pdtManageRes   = $pdtManageModel->getRowById($MemberPdtRes['pdt_id']);//die(print_r($pdtManageRes));
            //获取当前服务器配置的价格
            //$RenewRes = DataHelper::getServerCreateInfo($pdtManageRes['id'],$MemberPdtRes['config']);//die(print_r($RenewRes));
            $RenewRes = $MemberPdtRes['sell_price'];                               //die(print_r($RenewRes));

            $unit = "Month";
            //$expire = $arrCreateInfo['info']['expire'];
            $expire = $MemberPdtRes['payment_cycle'];
            #$end_time = strtotime("+$expire".$unit, $MemberPdtRes['end_time']);//echo $end_time;die();
            $end_time = DataHelper::getRenewEndtime($MemberPdtRes['end_time'], $expire);

            return $this->render('renew', ['MemberPdtRes' => $MemberPdtRes, 'end_time' => $end_time, 'pdtManageRes' => $pdtManageRes]);
        }
    }

    /*

	*/
    public
    function actionGetIpnotes()
    {
        $IpModel = new PdtIp();

        if (Yii::$app->request->post()) {
            $post = $this->post();
            $ip   = $post['ip'];
            if (!$ip || $ip == '' || $ip == null) {
                $arrReturn = ['status' => 0, 'info' => '未有IP'];
                return $this->renderJSON($arrReturn);
            }
            $IpRes = $IpModel->find()->where(['ip' => trim($ip)])->asArray()->one();
            if (empty($IpRes)) {
                $arrReturn = ['status' => 0, 'info' => '未查询到IP'];
            } else {
                $arrReturn = ['status' => 1, 'info' => '获取成功', 'remarks' => $IpRes['remarks']];
            }
            return $this->renderJSON($arrReturn);
        } else {
            $arrReturn = [
                'status' => 0,
                'info'   => '未知的参数',
            ];
            return $this->renderJSON($arrReturn);
        }
    }

    /**
     * 旧版 修改信息
     */
    public
    function actionOldUpdate()
    {

        $PdtManageModel = new PdtManage();
        $MemberPdtModel = new MemberPdt();
        $PdtLogModel    = new PdtLog();
        $idleModel      = new IdlePdt();
        if (Yii::$app->request->post()) {

            $post = $this->post();
            $id   = $post['id'];
            $id   = intval($id);

            $MemberPdtQueryModel = $MemberPdtModel->findOne($id);

            if ($MemberPdtQueryModel['status'] != 1 || $MemberPdtQueryModel['audit_status'] != 1) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '当前产品不在正常状态或未审核',
                ];
                return $this->renderJSON($arrReturn);
            }

            //对比数据是否有变化

            $hasDataChanged = false;
            foreach ($post as $ek => $ev) {
                foreach ($MemberPdtQueryModel as $kk => $vv) {
                    if ($ek == $kk) {
                        if ($ev != $vv) {
                            $hasDataChanged = true;
                            break;
                        }
                    }
                }
            }

            if (!$hasDataChanged) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '未修改信息',
                ];
                return $this->renderJSON($arrReturn);
            }

            #查询由后台发起的修改信息是否为审核
            $edittype      = Yii::$app->db->createCommand("select nv_type from notice_manage where nv_key = 'edit_info'")->queryScalar();
            $notice_do_man = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');


            #开启事务
            $transaction = Yii::$app->db->beginTransaction();
            $time        = time();

            $userMemberRes = Yii::$app->db->createCommand("select * from user_member where u_id = '" . $MemberPdtQueryModel->user_id . "'")->queryOne();
            $hasCheck      = Yii::$app->db->createCommand("select * from notice_record where notice_unionid = '" . $MemberPdtQueryModel->unionid . "' and notice_key = 'edit_info' and notice_type = '审核' and notice_audit_time is null")->queryOne();


            //因成本问题，判断成本价格为必填，升级价格可以为空
            //#修改时，当价格发生变化，到成本表查询，如果当天有记录，直接覆盖，没有就添加
            $cost_price         = $post['cost_price'];
            $upgrade_cost_price = $post['upgrade_cost_price'];

            if ($MemberPdtQueryModel->cost_price != $cost_price || $MemberPdtQueryModel->upgrade_cost_price != $upgrade_cost_price) {
                //查询产品在成本表在当天是否有记录
                $CostModel = new Cost;
                $start     = strtotime(date('Y-m-d 00:00:00'));
                $end       = strtotime(date('Y-m-d H:i:s'));
                $CostRes   = $CostModel->find()->where(['unionid' => $MemberPdtQueryModel->unionid])->andwhere(['>', 'change_time', $start])->andWhere(['<', 'change_time', $end])->asArray()->all();
                if (empty($CostRes)) {
                    $CostModel->unionid       = $MemberPdtQueryModel->unionid;
                    $CostModel->basic_money   = $cost_price;
                    $CostModel->two_money     = $upgrade_cost_price;
                    $CostModel->currency_type = $post['currency_type'];

                    $CostModel->change_time = $time;
                    $CostModel->admin_id    = $MemberPdtQueryModel->admin_id;

                    if ($edittype == '审核') {

                        if ($hasCheck) {
                            return $this->renderJSON(['status' => 0, 'info' => '当前已有一笔待审核编辑当前业务信息的操作，请先处理后再行操作']);
                        }


                        $CostModel->basic_money = $CostModel->basic_money ? $CostModel->basic_money : 0.00;
                        $CostModel->two_money   = $CostModel->two_money ? $CostModel->two_money : 0.00;

                        $dataContent[] = [
                            'sql' => base64_encode("insert into cost (unionid,basic_money,two_money,change_time,admin_id,currency_type) values ('" . $CostModel->unionid . "', '" . $CostModel->basic_money . "', '" . $CostModel->two_money . "', '" . $CostModel->change_time . "', '" . $CostModel->admin_id . "', '" . $post['currency_type'] . "')"),
                        ];
                        /*
						$dataContent[] = [
							'sql' => base64_encode("insert into cost values (null, '".$CostModel->unionid."', '".$CostModel->basic_money."', '".$CostModel->two_money."', '".$CostModel->change_time."', '".$CostModel->admin_id."')"),
						];
						*/

                    } else {
                        if (!$CostModel->insert(false)) {
                            $transaction->rollBack();
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '更新成本信息错误',
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                    }

                } else {
                    $CostModelQuery                = $CostModel->findone(['unionid' => $MemberPdtQueryModel->unionid]);//echo"11";//print_r($CostModelQuery);die();
                    $CostModelQuery->unionid       = $MemberPdtQueryModel->unionid;
                    $CostModelQuery->basic_money   = $cost_price;
                    $CostModelQuery->two_money     = $upgrade_cost_price;
                    $CostModelQuery->currency_type = $post['currency_type'];
                    $CostModelQuery->change_time   = $time;
                    $CostModelQuery->admin_id      = $MemberPdtQueryModel->admin_id;

                    if ($edittype == '审核') {

                        if ($hasCheck) {
                            return $this->renderJSON(['status' => 0, 'info' => '当前已有一笔待审核编辑当前业务信息的操作，请先处理后再行操作']);
                        }

                        $CostModelQuery->basic_money = $CostModelQuery->basic_money ? $CostModelQuery->basic_money : 0.00;
                        $CostModelQuery->two_money   = $CostModelQuery->two_money ? $CostModelQuery->two_money : 0.00;

                        $dataContent[] = [
                            'sql' => base64_encode("update cost set unionid='" . $CostModelQuery->unionid . "', basic_money='" . $CostModelQuery->basic_money . "', two_money='" . $CostModelQuery->two_money . "', change_time='" . $CostModelQuery->change_time . "', admin_id='" . $CostModelQuery->admin_id . "' , currency_type='" . $post['currency_type'] . "' where id = '" . $CostModelQuery->id . "'"),
                        ];
                    } else {
                        if (!$CostModelQuery->update(false)) {
                            $transaction->rollBack();
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '更新成本信息错误',
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                    }
                }
            }

            $MemberPdtQueryModel->scenario   = "modify";
            $MemberPdtQueryModel->attributes = $post;
            #数据验证
            if (!$MemberPdtQueryModel->validate()) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => $MemberPdtQueryModel->errors,
                ];
                return $this->renderJSON($arrReturn);
            }

            $newModel   = $MemberPdtQueryModel->Attributes;
            $oldModel   = $MemberPdtQueryModel->oldAttributes;
            $arrayModel = array_diff_assoc($MemberPdtQueryModel->Attributes, $MemberPdtQueryModel->oldAttributes);
            $message    = "管理员" . $this->getAdminInfo('uname') . "修改了用户产品表ID为" . $id . "的信息，";


            if ($edittype == '审核') {

                if ($hasCheck) {
                    return $this->renderJSON(['status' => 0, 'info' => '当前已有一笔待审核编辑当前业务信息的操作，请先处理后再行操作']);
                }

                $transaction->rollBack();
                $post['upgrade_cost_price'] = $post['upgrade_cost_price'] ? $post['upgrade_cost_price'] : 0;
                $post['cost_price']         = $post['cost_price'] ? $post['cost_price'] : 0;
                $dataContent[]              = [
                    'sql' => base64_encode("update member_pdt set ipmi_ip='" . $post['ipmi_ip'] . "', sell_price='" . $post['sell_price'] . "', payment_cycle='" . $post['payment_cycle'] . "', cost_price='" . $post['cost_price'] . "', upgrade_cost_price='" . $post['upgrade_cost_price'] . "' where unionid='" . $MemberPdtQueryModel->unionid . "'"),
                ];

                $dataJson = json_encode($dataContent, JSON_UNESCAPED_UNICODE);

                NotifyHandle::EditOrder_Notify($MemberPdtQueryModel->oldAttributes, $MemberPdtQueryModel->Attributes, $notice_do_man, $MemberPdtQueryModel->unionid, $MemberPdtQueryModel->user_id, $dataJson);

                return $this->renderJSON([
                    'status' => 1,
                    'info'   => '已提交编辑申请，等待审核',
                ]);

            } else {
                NotifyHandle::EditOrder_Notify($MemberPdtQueryModel->oldAttributes, $MemberPdtQueryModel->Attributes, $notice_do_man, $MemberPdtQueryModel->unionid, $MemberPdtQueryModel->user_id, null);

                if ($MemberPdtQueryModel->update()) {
                    $transaction->commit();
                    $arrReturn = [
                        'status' => 1,
                        'info'   => "产品信息修改成功",
                    ];
                    foreach ($arrayModel as $key => $value) {
                        if (empty($oldModel[$key])) {
                            $oldvalue = "空";
                        } else {
                            $oldvalue = $oldModel[$key];
                        }
                        $message .= $MemberPdtModel->attributeLabels()[$key] . '原:' . $oldvalue . "改为" . $value . ",";
                    }
                    $PdtLogModel->doadd($this->getAdminInfo('admin_id'), $this->getAdminInfo('uname'), $message);
                } else {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '未修改信息',
                    ];
                }
            }

            return $this->renderJSON($arrReturn);
        } else {
            $id     = $this->get('id');
            $id     = intval($id);
            $arrRes = $MemberPdtModel->getRowById($id);     //print_r($arrRes);die();
            if (empty($arrRes))
                return $this->redirect(Url::to(['index']));

            //当前机器的配置
            $config = json_decode($arrRes['config'], true); //print_r($config);die();
            //获取机房
            $RoomManageModel = new PdtRoomMange();
            $roomRes         = $RoomManageModel->getListAll(['provider' => $arrRes['servicerprovider']]);//print_r($roomRes);die();
            //获取当前机房下的机柜
            $PdtCabinetManageModel = new PdtCabinetManage();
            $cabinetRes            = $PdtCabinetManageModel->getRowByRoomId($arrRes["room_id"]);

            //获取当前机房下产品配置类别  根据是自有还是供应商来判别（来）
            if ($arrRes['servicerprovider'] == 0) {
                $PdtManageList = $PdtManageModel->find()->where(new Expression('FIND_IN_SET(:pdt_type_id, pdt_type_id)'))->addParams([':pdt_type_id' => $arrRes['server_type_id']])->asArray()->all();
            } elseif ($arrRes['servicerprovider'] == 1) {
                $PdtManageList = $PdtManageModel->getListAll();//print_r($PdtManageList);die();
            }

            //获取有效的产品分组
            $PdtTypeModel = new PdtType();
            $PdtTypeRes   = $PdtTypeModel->getValidListAll();
            //获取服务器属性
            $ServerAttributeModel = new ServerAttribute();
            $ServerAttRes         = $ServerAttributeModel->getListAll();
            //获取有效的后台角色管理
            $UserAdminModel = new UserAdmin();
            $UserAdminRes   = $UserAdminModel->getValidListAll();
            //获取服务器分类
            $PdtManageTypeModel = new PdtManageType();
            $PdtManageTypeRes   = $PdtManageTypeModel->find()->AsArray()->All();
            #货币类型
            $currency_typeRes = Yii::$app->params['currency_type'];                                      #print_r($currency_typeRes);
            $CostModel        = new Cost();
            $CostRes          = $CostModel->find()->where(['unionid' => $arrRes['unionid']])->asArray()->one(); #print_r($CostRes);exit;
            if ($CostRes) {
                $currency_type = $CostRes['currency_type'];
            } else {
                $currency_type = $currency_typeRes[0];
            }

            return $this->render('item', [
                'arrRes'           => $arrRes,
                'roomRes'          => $roomRes,
                'cabinetRes'       => $cabinetRes,
                'PdtManageList'    => $PdtManageList,
                'PdtTypeRes'       => $PdtTypeRes,
                'ServerAttRes'     => $ServerAttRes,
                'UserAdminRes'     => $UserAdminRes,
                'PdtManageTypeRes' => $PdtManageTypeRes,
                'config'           => $config,
                'currency_typeRes' => $currency_typeRes,
                'currency_type'    => $currency_type,
            ]);
        }

    }

    /*
		旧版 修改时间
	*/
    public
    function actionUpdatePdttime()
    {

        $MemberPdtModel = new MemberPdt();

        if (Yii::$app->request->post()) {
            $post                = $this->post();
            $MemberPdtQueryModel = $MemberPdtModel->findOne($this->post('id'));

            //时间逻辑判断
            $post['start_time'] = strtotime($post['start_time']);
            $post['end_time']   = strtotime($post['end_time']);

            if (empty($post['start_time']) || empty($post['end_time'])) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '创建或到期时间不能为空',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($post['start_time'] > $post['end_time']) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '创建时间不能小于到期时间',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($post['start_time'] == $MemberPdtQueryModel->start_time && $post['end_time'] == $MemberPdtQueryModel->end_time) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '时间未修改',
                ];
                return $this->renderJSON($arrReturn);
            }

            $MemberPdtQueryModel->scenario   = "update_time";
            $MemberPdtQueryModel->attributes = $post;
            #数据验证
            if (!$MemberPdtQueryModel->validate()) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => $MemberPdtQueryModel->errors,
                ];
                return $this->renderJSON($arrReturn);
            }

            $newModel   = $MemberPdtQueryModel->Attributes;
            $oldModel   = $MemberPdtQueryModel->oldAttributes;
            $arrayModel = array_diff_assoc($MemberPdtQueryModel->Attributes, $MemberPdtQueryModel->oldAttributes);
            $message    = "管理员" . $this->getAdminInfo('uname') . "修改了用户产品表ID为" . $id . "的信息，";


            #查询由后台发起的修改信息是否为审核
            $update_pdt_time = Yii::$app->db->createCommand("select nv_type from notice_manage where nv_key = 'update_pdt_time'")->queryScalar();
            $notice_do_man   = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

            #开启事务
            $transaction = Yii::$app->db->beginTransaction();
            $time        = time();

            $userMemberRes = Yii::$app->db->createCommand("select * from user_member where u_id = '" . $MemberPdtQueryModel->user_id . "'")->queryOne();
            $hasCheck      = Yii::$app->db->createCommand("select * from notice_record where notice_unionid = '" . $MemberPdtQueryModel->unionid . "' and notice_key = 'update_pdt_time' and notice_type = '审核' and notice_audit_time is null")->queryOne();

            if ($update_pdt_time == '审核') {

                $transaction->rollBack();
                if ($hasCheck) {
                    return $this->renderJSON(['status' => 0, 'info' => '当前已有一笔待审核编辑当前业务信息的操作，请先处理后再行操作']);
                }


                $dataContent[] = [
                    'sql' => base64_encode("update member_pdt set start_time='" . $post['start_time'] . "', end_time ='" . $post['end_time'] . "' where unionid='" . $MemberPdtQueryModel->unionid . "'"),
                ];

                $dataJson = json_encode($dataContent, JSON_UNESCAPED_UNICODE);

                NotifyHandle::EditPdtTime_Notify($MemberPdtQueryModel->oldAttributes, $MemberPdtQueryModel->Attributes, $notice_do_man, $MemberPdtQueryModel->unionid, $MemberPdtQueryModel->user_id, $dataJson);

                return $this->renderJSON([
                    'status' => 1,
                    'info'   => '已提交编辑申请，等待审核',
                ]);
            } else {

                NotifyHandle::EditPdtTime_Notify($MemberPdtQueryModel->oldAttributes, $MemberPdtQueryModel->Attributes, $notice_do_man, $MemberPdtQueryModel->unionid, $MemberPdtQueryModel->user_id, null);

                if ($MemberPdtQueryModel->update()) {
                    $transaction->commit();
                    $arrReturn = [
                        'status' => 1,
                        'info'   => "产品信息修改成功",
                    ];
                    foreach ($arrayModel as $key => $value) {
                        if (empty($oldModel[$key])) {
                            $oldvalue = "空";
                        } else {
                            $oldvalue = $oldModel[$key];
                        }
                        $message .= $MemberPdtModel->attributeLabels()[$key] . '原:' . $oldvalue . "改为" . $value . ",";
                    }
                    $PdtLogModel->doadd($this->getAdminInfo('admin_id'), $this->getAdminInfo('uname'), $message);
                } else {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '未修改信息',
                    ];
                }
            }

        } else {
            $id     = $this->get('id');
            $id     = intval($id);
            $arrRes = $MemberPdtModel->getRowById($id); //print_r($arrRes);die();
            if (empty($arrRes))
                return $this->redirect(Url::to(['index']));

            return $this->render('update_time', [
                'arrRes' => $arrRes,
            ]);
        }
    }

    /**
     * 产品审核
     */
    public
    function actionReviewed()
    {
        Yii::$app->request->isAjax || die('error');

        $PdtLogModel = new PdtLog();
        $post        = $this->post();
        $id          = $this->post('id');
        $id          = intval($id);

        if ($id == '')
            return $this->renderJSON(['status' => 0, 'info' => '操作失效']);

        $MemberPdtModel = new MemberPdt();
        $MemberPdtRes   = $MemberPdtModel->findOne($id);
        //未找到对应信息
        if (empty($MemberPdtRes)) {
            $arrReturn = [
                'status' => 0,
                'info'   => '操作失效，未找到对应产品信息',
            ];
            return $this->renderJSON($arrReturn);
        }
        if ($post['cost_price'] == "" || $post['cost_price'] == null) {
            $arrReturn = [
                'status' => 0,
                'info'   => '成本价未填写',
            ];
            return $this->renderJSON($arrReturn);
        }

        #开启事务
        $transaction = \Yii::$app->db->beginTransaction();

        //添加成本表
        $CostModel                = new Cost();
        $CostModel->unionid       = $MemberPdtRes->unionid;
        $CostModel->basic_money   = $post['cost_price'];
        $CostModel->two_money     = $post['upgrade_cost_price'];
        $CostModel->change_time   = $MemberPdtRes->start_time;
        $CostModel->admin_id      = $MemberPdtRes->admin_id;
        $CostModel->currency_type = $post['currency_type'];

        $MemberPdtRes->cost_price         = $post['cost_price'];
        $MemberPdtRes->upgrade_cost_price = $post['upgrade_cost_price'];
        $MemberPdtRes->audit_status       = 1;

        if ($MemberPdtRes->update(false) && $CostModel->insert()) {

            $ReportResult = new FinanceReport($MemberPdtRes->unionid, $MemberPdtRes->user_id, null, null, null, 'machine_purchase_verify_cost');
            if ($ReportResult) {
                $transaction->commit();
                $arrReturn = [
                    'status' => 1,
                    'info'   => '用户产品审核通过成功',
                ];
                $message   = "管理员" . $this->getAdminInfo('uname') . "审核了用户产品表一条信息，ID号为：" . $id;
                $PdtLogModel->doadd($this->getAdminInfo('admin_id'), $this->getAdminInfo('uname'), $message);
            } else {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '用户产品审核通过失败',
                ];
            }

        } else {
            #事务回滚
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => '用户产品审核通过失败',
            ];
        }
        return $this->renderJSON($arrReturn);
    }

    /**
     * 旧版 续费确认页
     * 旧版 续费执行创建订单
     */
    public
    function actionOldRenew()
    {
        $MemberPdtModel  = new MemberPdt();
        $UserMemberModel = new UserMember;
        if (Yii::$app->request->post()) {
            Yii::$app->request->isAjax || die('error');

            //基本业务判断
            if (!empty($this->post('expire')) && $this->post('expire') !== '') {
                if (!in_array($this->post('expire'), [1, 3, 6, 12])) {
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '续费时长选择未知',
                    ]);
                }
            }
            //获取产品信息
            $MemberPdtRes = $MemberPdtModel->getRowById($this->post('id'));//print_r($MemberPdtRes);die();

            if (empty($MemberPdtRes)) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '操作错误，因未有对应的产品',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($MemberPdtRes['status'] != 1 || $MemberPdtRes['audit_status'] != 1) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '当前产品不在正常状态或未审核',
                ];
                return $this->renderJSON($arrReturn);
            }
            //产品配置类别
            $PdtManageModel = new PdtManage();
            $pdtmanageRes   = $PdtManageModel->getRowById($MemberPdtRes['pdt_id']);//die(print_r($pdtmanageRes));

            //获取用户信息
            $UserMemberRes = $UserMemberModel->findOne(['u_id' => $MemberPdtRes['user_id']]);

            $renew_total = $MemberPdtRes['sell_price'] * $this->post('renew_num');//echo $renew_total;die();
            $expire      = $MemberPdtRes['payment_cycle'] * $this->post('renew_num');

            #创建订单编号
            $trade_orderid = DataHelper::createTradeno();

            $TradeModel = new Trade();
            #开启事务
            $transaction = \Yii::$app->db->beginTransaction();

            $config                          = json_decode($MemberPdtRes['config'], true);
            $config['requirement_bandwidth'] = $MemberPdtRes['bandwidth']; #将配置中增加用户要求带宽参数。

            $trade_config['config']        = $config;
            $trade_config['ip']            = json_decode($MemberPdtRes['ip'], true);
            $trade_config['ip2']           = json_decode($MemberPdtRes['ip2'], true);
            $trade_config['renew_num']     = $this->post('renew_num');
            $trade_config['sell_price']    = $MemberPdtRes['sell_price'];
            $trade_config['renew_money']   = $renew_total;
            $trade_config['server_typeid'] = $pdtmanageRes['pdt_type_id'];
            $trade_config['payment_cycle'] = $MemberPdtRes['payment_cycle'];
            $trade_config['expire']        = $expire;
            $time                          = time();
            $array                         = [
                'trade_orderid'        => $trade_orderid,
                'admin_id'             => $UserMemberRes['admin_id'],
                'admin_name'           => $UserMemberRes['admin_name'],
                'member_id'            => $UserMemberRes['u_id'],
                'member_name'          => $UserMemberRes['email'],
                'unionid'              => $MemberPdtRes['unionid'],
                'serviceid'            => "",
                'trade_type_pay'       => "",
                'trade_type_money'     => Yii::$app->params['trade_type']['machine_renewal']['name'],
                'trade_price_original' => $renew_total,
                'trade_price_real'     => $renew_total,
                'trade_price_payment'  => "",
                'trade_type_do'        => "续费",
                'trade_config'         => json_encode($trade_config, JSON_UNESCAPED_UNICODE),
                'trade_time_create'    => $time,
                'trade_remark'         => "",
                'trade_audit'          => 'Y',
            ];

            #查询由后台发起的续费订单是否为审核
            $renewtype     = Yii::$app->db->createCommand("select nv_type from notice_manage where nv_key = 'renew_start_order'")->queryScalar();
            $notice_do_man = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

            $hasCheck = Yii::$app->db->createCommand("select * from notice_record where notice_user like '%" . $UserMemberRes['email'] . "%' and notice_key = 'renew_start_order' and notice_type = '审核' and notice_audit_time is null")->queryOne();


            if ($renewtype == '审核') {

                if ($hasCheck) {
                    return $this->renderJSON(['status' => 0, 'info' => '当前已有一笔待审核业务续费操作，请先处理后再行操作']);
                }

                $transaction->rollBack();
                $dataContent[] = [
                    'sql' => base64_encode("insert into trade (trade_orderid,member_id,member_name,admin_id,admin_name,unionid,trade_type_money,trade_price_original,trade_price_real,trade_type_do,trade_config,trade_status,trade_time_create,trade_audit) 
					values('" . $trade_orderid . "', '" . $UserMemberRes['u_id'] . "', '" . $UserMemberRes['email'] . "', '" . $UserMemberRes['admin_id'] . "', '" . $UserMemberRes['admin_name'] . "','" . $MemberPdtRes['unionid'] . "', '" . Yii::$app->params['trade_type']['machine_renewal']['name'] . "', '" . $renew_total . "', '" . $renew_total . "', '续费', '" . json_encode($trade_config, JSON_UNESCAPED_UNICODE) . "', '未支付', '" . $time . "', null, 'Y')"),
                ];
                /*
				$dataContent[] = [
					'sql' => base64_encode("insert into trade values(null, '".$trade_orderid."', '".$UserMemberRes['u_id']."', '".$UserMemberRes['email']."', '".$UserMemberRes['admin_id']."', '".$UserMemberRes['admin_name']."',
					'".$MemberPdtRes['unionid']."', '', null, '', 'consume', '".$renew_total."', '".$renew_total."', null, '续费',
					'".json_encode($trade_config, JSON_UNESCAPED_UNICODE)."', '未支付', '".$time."', null, '', 'Y')"),
				];
				*/

                $dataJson = json_encode($dataContent, JSON_UNESCAPED_UNICODE);

                NotifyHandle::RenewOrderStart_Notify($MemberPdtRes['end_time'], strtotime("+$expire" . 'Month', $MemberPdtRes['end_time']), $notice_do_man, $MemberPdtRes['unionid'], $UserMemberRes['u_id'], $dataJson);
                return $this->renderJSON([
                    'status' => 1,
                    'info'   => '已提交续费申请，等待审核',
                ]);
            } else {
                NotifyHandle::RenewOrderStart_Notify($MemberPdtRes['end_time'], strtotime("+$expire" . 'Month', $MemberPdtRes['end_time']), $notice_do_man, $MemberPdtRes['unionid'], $UserMemberRes['u_id'], null);

                $resTrade = $TradeModel->InsertTradeData($array);
                //创建订单失败
                if (!$resTrade) {
                    $transaction->rollBack();
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '创建基础订单失败',
                    ]);
                } else {
                    $transaction->commit();
                    $PdtLogModel = new PdtLog();
                    $message     = "管理员" . $this->getAdminInfo('uname') . "创建了用户产品表ID号为：" . $this->post('memberpdt_id') . "的续费订单,订单号为：" . $trade_orderid;
                    $PdtLogModel->doadd($this->getAdminInfo('admin_id'), $this->getAdminInfo('uname'), $message);
                    return $this->renderJSON([
                        'status' => 1,
                        'info'   => '续费订单已经生成，请等待支付',
                    ]);
                }
            }
        } else {
            $id           = $this->get('id');
            $MemberPdtRes = $MemberPdtModel->getRowById($id);//print_r($MemberPdtRes);die();
            if (empty($MemberPdtRes))
                return $this->redirect(Url::to(['index']));
            // if($MemberPdtRes['status'] == 2) return $this->redirect(Url::to(['index']));
            //获取当前服务器所属配置
            $pdtManageModel = new PdtManage();
            $pdtManageRes   = $pdtManageModel->getRowById($MemberPdtRes['pdt_id']);//die(print_r($pdtManageRes));
            //获取当前服务器配置的价格
            //$RenewRes = DataHelper::getServerCreateInfo($pdtManageRes['id'],$MemberPdtRes['config']);//die(print_r($RenewRes));
            $RenewRes = $MemberPdtRes['sell_price'];                               //die(print_r($RenewRes));

            $unit = "Month";
            //$expire = $arrCreateInfo['info']['expire'];
            $expire   = $MemberPdtRes['payment_cycle'];
            $end_time = strtotime("+$expire" . $unit, $MemberPdtRes['end_time']);//echo $end_time;die();

            return $this->render('renew', ['MemberPdtRes' => $MemberPdtRes, 'end_time' => $end_time, 'pdtManageRes' => $pdtManageRes]);
        }
    }

    /**
     *修改续费方式   Y 自动续费
     */
    public
    function actionChangeRenewalmode()
    {
        Yii::$app->request->isAjax || die('error');
        $MemberPdtModel = new MemberPdt;
        $memberpdt_id   = $this->post('id');
        $is_auto        = $this->post('is_auto');

        $MemberPdtQuery = $MemberPdtModel->findOne($memberpdt_id);

        $MemberPdtQuery->is_auto = $is_auto;
        if ($MemberPdtQuery->update(false)) {
            $arrReturn = [
                'status' => 1,
                'info'   => '操作成功',
            ];
        } else {
            $arrReturn = [
                'status' => 0,
                'info'   => '操作失败',
            ];
        }
        return $this->renderJSON($arrReturn);
    }

    /**
     * 过期删除 （关机下架）
     */
    public
    function actionOverduedel()
    {

        Yii::$app->request->isAjax || die('error');

        $id = $this->post('id');

        $MemberPdtModel  = new MemberPdt();
        $IdlePdtModel    = new IdlePdt();
        $SupplierIpModel = new SupplierIp();

        $MemberPdtQuery = $MemberPdtModel->findOne($id);
        if (empty($MemberPdtQuery)) {
            $arrReturn = ['status' => 0, 'info' => '未知的用户产品'];
            return $this->renderJSON($arrReturn);
        }

        if ($MemberPdtQuery->status != 1 || $MemberPdtQuery->audit_status != 1) {
            $arrReturn = ['status' => 0, 'info' => '当前产品不在正常状态或未审核'];
            return $this->renderJSON($arrReturn);
        }
        #检查是否可以关机下架
        $CheckRes = BusinessCheck::CheckOverdueDel($MemberPdtQuery->unionid);
        if (!$CheckRes['status']) {
            return $this->renderJSON(['status' => 0, 'info' => $CheckRes['info']]);
        }

        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
        #看是否是只看自己客户账户的

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'pipe_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            if ($MemberPdtQuery->admin_id != $admin_id) {
                $arrReturn = ['status' => 0, 'info' => '您不属于该用户的专属客服，无法进行此操作'];
                return $this->renderJSON($arrReturn);
            }
        }

        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        #这里删除是将状态 改为删除，将绑定的产品库改为闲置.如果不是自有的，就不用管
        #添加分配记录，自有添加自有分配记录， 供应商的添加供应商分配记录
        #如果为自有库机器。则需要通过接口，进行关机
        if ($MemberPdtQuery->idle_id != "" || $MemberPdtQuery->idle_id != null && $MemberPdtQuery->servicerprovider == 0) {
            $IdlePdtQuery = $IdlePdtModel->findOne($MemberPdtQuery->idle_id);
            if (empty($IdlePdtQuery)) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '未知的产品库信息',
                ];
                return $this->renderJSON($arrReturn);
            }

            #添加记录  自有
            $PdtHaveuselogModel = new PdtHaveuselog();
            $addUseLogRes       = $PdtHaveuselogModel->doadd($IdlePdtQuery, $MemberPdtQuery);
            if (!$addUseLogRes) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '添加分配记录失败',
                ];
                return $this->renderJSON($arrReturn);
            }

            #同时将客户要求带宽清空
            $config                          = json_decode($IdlePdtQuery->config, true);
            $config['requirement_bandwidth'] = '';
            #更新
            $IdlePdtQuery->config       = json_encode($config, JSON_UNESCAPED_UNICODE);
            $IdlePdtQuery->status       = 0;
            $IdlePdtQuery->attribute_id = 1;
            $IdlePdtQuery->update_time  = time();
            if (!$IdlePdtQuery->update(false)) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '更新产品库信息失败',
                ];
                return $this->renderJSON($arrReturn);
            }

            #进行关机
            $CabinetModel = new PdtCabinetManage();

            $idle_id = $MemberPdtQuery->idle_id;
            #status 状态   on  开机  off  关机   reset  重启  pxe  PXE启动  bios   BIOS启动   ipmireset 重启ipmi
            $str_action  = "off";      #关机指示
            $port_status = 'portdown'; #端口状态改为关闭
            #机器所属机柜
            $cabinet_id = $IdlePdtQuery->cabinet_id;
            #获取机柜相关信息
            if ($cabinet_id == "" || $cabinet_id == null) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '该服务器未选择机柜',
                ];
                return $this->renderJSON($arrReturn);
            } else {
                $CabinetRes = $CabinetModel->find()->where(['id' => $cabinet_id])->asArray()->one();
                if (!$CabinetRes) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '未知的机柜信息',
                    ];
                    return $this->renderJSON($arrReturn);
                } else {
                    $cloudboot_key  = $CabinetRes['cloudboot_key'];
                    $cloudboot_api  = $CabinetRes['cloudboot_api'];
                    $cloudboot_node = $CabinetRes['cloudboot_node'];

                    if ($cloudboot_api == "" || $cloudboot_api == null) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => '对应机柜未填写云装机API地址',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                    if ($cloudboot_key == "" || $cloudboot_key == null) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => '对应机柜未填写云装机密钥',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
            }

            $SwitchManageModel = new SwitchManage();
            if ($IdlePdtQuery->switch_location != '' || $IdlePdtQuery->switch_location != null) {
                $SwitchManageRes = $SwitchManageModel->find()->where(['id' => $IdlePdtQuery->switch_location])->asArray()->one();
                #交换机信息
                $switch_ip      = $SwitchManageRes['ip'];            #交换机IP
                $login_port     = $SwitchManageRes['login_port'];    #交换机登录端口
                $login_user     = $SwitchManageRes['login_user'];    #交换机登录账户
                $login_password = $SwitchManageRes['login_password'];#交换机登录密码
                $switch_port    = $IdlePdtQuery->switch_port;        #交换机端口
            } else {
                #交换机信息
                $switch_ip      = '';                          #交换机IP
                $login_port     = '';                          #交换机登录端口
                $login_user     = '';                          #交换机登录账户
                $login_password = '';                          #交换机登录密码
                $switch_port    = $IdlePdtQuery->switch_port;  #交换机端口
            }

            #判定机柜属性  1为 独立服务器   2为云主机 VPS
            if ($CabinetRes['attribute'] == 1) {
                $ipmi_name = $IdlePdtQuery->ipmi_name;
                $ipmi_pwd  = $IdlePdtQuery->ipmi_pwd;
                if ($ipmi_name == "" || $ipmi_name == null) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => 'IPMI 账户未设置',
                    ];
                    return $this->renderJSON($arrReturn);
                }
                if ($ipmi_pwd == "" || $ipmi_pwd == null) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => 'IPMI 密码未设置',
                    ];
                    return $this->renderJSON($arrReturn);
                }

                $ipmi_ip = $IdlePdtQuery->ipmi_ip;
                if ($ipmi_ip == "" || $ipmi_ip == null) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '该自有服务器未填写IPMI IP地址',
                    ];
                    return $this->renderJSON($arrReturn);
                } else {
                    if (filter_var($ipmi_ip, FILTER_VALIDATE_IP)) {
                        #对端口状态进行操作
                        $UpdatePortStatusRes = SwitchRequest::UpdatePortStatus($cloudboot_api, $switch_ip, $login_port, $login_user, $login_password, $switch_port, $cloudboot_key, $port_status);  #print_r($GetRes);die;

                        #如果是正常的IPMI地址。开始关机
                        $sendRes = IpmiApi::sendipmirequest($cloudboot_api, $str_action, $ipmi_ip, $ipmi_name, $ipmi_pwd, $cloudboot_key);                                                          #print_r($sendRes);exit;
                        if ($sendRes['Status'] == "success" && $UpdatePortStatusRes['status'] != 1) {
                            $info = "操作成功。机器已经成功关机";
                        } else {
                            $info = "操作成功。但机器并未关机，请联系技术关机";
                        }
                    } else {
                        $info = "操作成功。但机器并未关机，请联系技术关机";
                    }
                }
            } else {

                #判定机柜属性  1为 独立服务器   2为云主机 VPS
                /*$sn_code = $IdlePdtQuery->sn_code;
                if( $sn_code =='' || $sn_code ==null || !isset($sn_code) )
                {
                    $arrReturn = [
                        'status' => 0,
                        'info' => 'SN码未设置'
                    ];
                    return $this->renderJSON($arrReturn);
                }*/

                #$vmid = preg_replace('/[^\d]*/', '', $sn_code);
                /*$password = $CabinetRes['cloudboot_key'];
                $api_url = $CabinetRes['cloudboot_api'];
				$cloudboot_node = $CabinetRes['cloudboot_node'];

                $GetTokenRes = IpmiApi::LoginGetToken($api_url, $password);
                if( empty($GetTokenRes['data']) )
                {
                    $arrReturn = [
                        'status' => 0,
                        'info' => '登录获取令牌失败'
                    ];
                    return $this->renderJSON($arrReturn);
                }
                $token = $GetTokenRes['data']['CSRFPreventionToken'];
                $ticket = $GetTokenRes['data']['ticket'];

                $GetStatusRes = IpmiApi::GetStatus($api_url, $vmid, $ticket, $cloudboot_node);
                if( empty($GetStatusRes) ) {
                    $arrReturn = [
                        'status' => 0,
                        'info' => '获取电源状态失败'
                    ];
                    return $this->renderJSON($arrReturn);
                }

                $status = $GetStatusRes['data']['status'];

                if($str_action == 'off') {
                    if( $status != 'stopped') {
                        $OperationRes = IpmiApi::PowerOperation($api_url, $str_action, $vmid, $ticket, $token, $cloudboot_node);
                        if( empty($OperationRes['data']) )
                        {
                            $info = "操作成功。但机器并未关机，请联系技术关机";
                        } else {
                            $info = "操作成功。机器已经成功关机";
                        }
                    } else {
                        $info = "操作成功。机器已经成功关机";
                    }
                }*/
                $info = "操作成功。但机器可能未关机，请联系技术关机";
            }

            #增加删除记录
            $UserAdminModel = new UserAdmin();
            $UserAdminRes   = $UserAdminModel->find()->where(['admin_id' => $MemberPdtQuery->admin_id])->asArray()->one();

            $DeleteRecordModel = new BusinessDeleteRecord();

            $user_id                          = $MemberPdtQuery->user_id;
            $DeleteRecordModel->unionid       = $MemberPdtQuery->unionid;
            $DeleteRecordModel->user_id       = $user_id;
            $DeleteRecordModel->user_name     = $MemberPdtQuery->user_name ?: "ID:{$user_id}";
            $DeleteRecordModel->admin_id      = $MemberPdtQuery->admin_id;
            $DeleteRecordModel->admin_name    = $UserAdminRes['uname'];
            $DeleteRecordModel->ip            = $MemberPdtQuery->ip;
            $DeleteRecordModel->ip2           = $MemberPdtQuery->ip2;
            $DeleteRecordModel->ipmi_ip       = $MemberPdtQuery->ipmi_ip;
            $DeleteRecordModel->operator_name = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username');
            $DeleteRecordModel->delete_time   = time();
            $DeleteRecordModel->delete_reason = '到期超过时限手动删除';

            if (!$DeleteRecordModel->insert()) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '新增删除记录失败',
                    'errors' => $DeleteRecordModel->errors,
                ];
                return $this->renderJSON($arrReturn);
            }

            $MemberPdtQuery->status      = -1;
            $MemberPdtQuery->delete_time = time();

            if ($MemberPdtQuery->update(false)) {
                $transaction->commit();
                $arrReturn = [
                    'status' => 1,
                    'info'   => $info,
                ];
            } else {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '操作失败。用户产品信息更新失败！',
                ];
            }
            return $this->renderJSON($arrReturn);

        } else {

            $createRes = PipeLine::Line_BusinessShutdownLowerShelf($MemberPdtQuery->unionid);

            if ($createRes['status'] == 0) {
                $transaction->rollBack();
                return $this->renderJSON(["status" => 0, "info" => $createRes['info']]);
            } else {
                $transaction->commit();
                return $this->renderJSON(["status" => 1, 'info' => '当前机器为供应商机器，关机下架创建工作任务完成']);
            }


            /*
            $PdtProvideruselogModel = new PdtProvideruselog();
            $ProviderModel = new Provider();

            $sql = "(select end_time from pdt_haveuselog where unionid = '".$MemberPdtQuery->unionid."') UNION ALL (select end_time from pdt_provideruselog where unionid = '".$MemberPdtQuery->unionid."')
            ORDER BY end_time DESC LIMIT 1;";
            $UselogRes = Yii::$app->db->createCommand($sql)->queryScalar();  ###echo $UselogRes;die;

            $ProviderRes = $ProviderModel->find()->where(['id'=>$MemberPdtQuery->provider_id])->asArray()->one();

            if( $UselogRes) {
                $ProviderRes['start_time'] = $UselogRes;
            } else {
                $ProviderRes['start_time'] = $MemberPdtQuery->start_time;
            }

            $addProviderUseLogRes = $PdtProvideruselogModel->doadd($ProviderRes,$MemberPdtQuery);
            if( !$addProviderUseLogRes ) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info' => '添加分配记录失败'
                ];
                return $this->renderJSON($arrReturn);
            }
            $info = "操作成功。因为第三方机器，所以未关机，请联系技术关机";

            #删除供应商IP库中的IP
            $deleteRes = $SupplierIpModel->deleteAll(['ip' => json_decode($MemberPdtQuery->ip2, true)]);
            */
        }
    }

    /**
     * 业务恢复
     * 将用户删除状态的产品重新恢复正常
     * 这里需要重新匹配之前所用的产品库和IP 是否都一致
     * 如果所属销售不一样了，需更新所属销售
     */
    public
    function actionRecovery()
    {
        Yii::$app->request->isAjax || die('error');

        $MemberPdtModel  = new MemberPdt();
        $IdlePdtModel    = new IdlePdt();
        $PdtIpModel      = new PdtIp();
        $UserMemberModel = new UserMember();
        $TestServerModel = new TestServer();
        $SupplierIpModel = new SupplierIp();

        $id = $this->post('id');

        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        $MemberPdtQuery = $MemberPdtModel->findOne($id);
        $idle_id        = $MemberPdtQuery->idle_id;
        #原机器中绑定的IP
        $MemberPdt_ip = json_decode($MemberPdtQuery->ip2, true);

        #获取用户信息
        $UserMemberRes = $UserMemberModel->find()->where(['u_id' => $MemberPdtQuery->user_id])->asArray()->one();
        if ($UserMemberRes['status'] == "-1" || empty($UserMemberRes)) {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => "产品所属用户已被锁定或不存在",
            ];
            return $this->renderJSON($arrReturn);
        }

        #当是供应商的时候，只判断IP 。自有的时候，判断产品库和IP

        #为供应商所有时，判断IP
        if ($idle_id == "" || $idle_id == null && $MemberPdtQuery->servicerprovider == 1) {

            $CheckRes = DataHelper::detect_supplierip($MemberPdt_ip);
            if ($CheckRes['status'] == 0) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => $CheckRes['info'],
                ];
                return $this->renderJSON($arrReturn);
            }

            #供应商IP入库
            $insertRes = $SupplierIpModel->add_peration($MemberPdt_ip, $MemberPdtQuery['provider_id'], $MemberPdtQuery['room_id'], '1');
            if ($insertRes != count($MemberPdt_ip)) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '供应商IP入库失败',
                ];
                return $this->renderJSON($arrReturn);
            }
            $info = "业务恢复操作成功";
        } else {
            #为自有时   #获取该自有库机器信息
            $IdlePdtRes = $IdlePdtModel->findone($idle_id);
            #自有库状态不为闲置 就不可恢复了
            if ($IdlePdtRes->status != 0) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '恢复失败，所属产品库已经被使用',
                ];
                return $this->renderJSON($arrReturn);
            } else {

                $idle_ip = json_decode($IdlePdtRes->ip2, true); #目前库中所绑定的IP

                if (count($idle_ip) > count($MemberPdt_ip)) {
                    $ipRes = array_diff($idle_ip, $MemberPdt_ip);  #获取交集
                } else {
                    $ipRes = array_diff($MemberPdt_ip, $idle_ip);  #获取交集
                }
                if (!empty($ipRes)) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => 'IP发生变化，请查看自有库配置IP是否与用户业务IP是否一致',
                    ];
                    return $this->renderJSON($arrReturn);
                }
                #如果交集不为空
                /*if( !empty($ipRes) ) {
					foreach ($ipRes as $value) {
						$res = $PdtIpModel->find()->where(['ip'=>$value])->asArray()->one();
						if( $res['status'] != 0 ) {
							$arrReturn = [
                               'status'=>0,
                               'info'=>'恢复失败，所属产品库中已有IP已经被使用'
                           ];
                           return $this->renderJSON($arrReturn);
						}
					}
                }*/
            }
            #当所有条件都允许时，更新自有库信息
            $IdlePdtRes->status       = 1;
            $IdlePdtRes->attribute_id = 2;
            #同时更新用户要求带宽
            $config                          = json_decode($IdlePdtRes->config, true);
            $config['requirement_bandwidth'] = $MemberPdtQuery->bandwidth;
            $IdlePdtRes->config              = json_encode($config, JSON_UNESCAPED_UNICODE);

            if (!$IdlePdtRes->update()) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '产品库信息更新失败',
                ];
                return $this->renderJSON($arrReturn);
            }

            #同时如果为自有库机器时，恢复时，应开机
            $CabinetModel = new PdtCabinetManage();
            $idle_id      = $IdlePdtRes->id;
            #status 状态   on  开机  off  关机   reset  重启  pxe  PXE启动  bios   BIOS启动   ipmireset 重启ipmi
            $str_action  = "on";     #开机指示
            $port_status = 'portup'; #端口改为开启

            $cabinet_id = $IdlePdtRes->cabinet_id;
            #获取机柜相关信息
            $CabinetRes = $CabinetModel->find()->where(['id' => $cabinet_id])->asArray()->one();

            $cloudboot_key = $CabinetRes['cloudboot_key'];
            $cloudboot_api = $CabinetRes['cloudboot_api'];
            $ipmi_name     = $IdlePdtRes->ipmi_name;
            $ipmi_pwd      = $IdlePdtRes->ipmi_pwd;
            $ipmi_ip       = $IdlePdtRes->ipmi_ip;

            $SwitchManageModel = new SwitchManage();
            if ($IdlePdtRes->switch_location != '' || $IdlePdtRes->switch_location != null) {
                $SwitchManageRes = $SwitchManageModel->find()->where(['id' => $IdlePdtRes->switch_location])->asArray()->one();
                #交换机信息
                $switch_ip      = $SwitchManageRes['ip'];            #交换机IP
                $login_port     = $SwitchManageRes['login_port'];    #交换机登录端口
                $login_user     = $SwitchManageRes['login_user'];    #交换机登录账户
                $login_password = $SwitchManageRes['login_password'];#交换机登录密码
                $switch_port    = $IdlePdtRes->switch_port;          #交换机端口
            } else {
                #交换机信息
                $switch_ip      = '';                        #交换机IP
                $login_port     = '';                        #交换机登录端口
                $login_user     = '';                        #交换机登录账户
                $login_password = '';                        #交换机登录密码
                $switch_port    = $IdlePdtRes->switch_port;  #交换机端口
            }

            if ($ipmi_ip == "" || $cabinet_id == "" || empty($CabinetRes) || $cloudboot_key == "" || $cloudboot_api == "" || $ipmi_name == "" || $ipmi_pwd == "") {
                $info = "业务恢复操作成功，但机器可能处于关机状态，请联系技术开机";
            } else {

                #判定机柜属性  1为 独立服务器   2为云主机 VPS
                if ($CabinetRes['attribute'] == 1) {
                    if (filter_var($ipmi_ip, FILTER_VALIDATE_IP)) {
                        #对端口状态进行操作
                        $UpdatePortStatusRes = SwitchRequest::UpdatePortStatus($cloudboot_api, $switch_ip, $login_port, $login_user, $login_password, $switch_port, $cloudboot_key, $port_status);  #print_r($GetRes);die;

                        # 如果是正常的IPMI地址。开始开机
                        $sendRes = IpmiApi::sendipmirequest($cloudboot_api, $str_action, $ipmi_ip, $ipmi_name, $ipmi_pwd, $cloudboot_key);
                        $info    = "业务恢复操作成功";
                    } else {
                        $info = "业务恢复操作成功，但机器可能处于关机状态，请联系技术开机";
                    }
                } else {

                    /*$sn_code = $IdlePdtRes->sn_code;
					if( $sn_code =='' || $sn_code ==null || !isset($sn_code) ) {
						$arrReturn = ['status' => 0,'info' => 'SN码未设置'];
						return $this->renderJSON($arrReturn);
					}*/

                    #$vmid = preg_replace('/[^\d]*/', '', $sn_code);
                    /*$password = $CabinetRes['cloudboot_key'];
					$api_url = $CabinetRes['cloudboot_api'];
					$cloudboot_node = $CabinetRes['cloudboot_node'];

					$GetTokenRes = IpmiApi::LoginGetToken($api_url, $password);
					if( empty($GetTokenRes['data']) ) {
						$arrReturn = ['status' => 0,'info' => '登录获取令牌失败'];
						return $this->renderJSON($arrReturn);
					}
					$token = $GetTokenRes['data']['CSRFPreventionToken'];
					$ticket = $GetTokenRes['data']['ticket'];

					$GetStatusRes = IpmiApi::GetStatus($api_url, $vmid, $ticket, $cloudboot_node);
					if( empty($GetStatusRes) ) {
						$arrReturn = ['status' => 0,'info' => '获取电源状态失败'];
						return $this->renderJSON($arrReturn);
					}

					$status = $GetStatusRes['data']['status'];

					if($str_action == 'on') {
						if( $status != 'running') {
							$OperationRes = IpmiApi::PowerOperation($api_url, $str_action, $vmid, $ticket, $token, $cloudboot_node);
							if( empty($OperationRes['data']) ) {
								$info = "操作成功。但机器并未开机，请联系技术开机";
							} else {
								$info = "操作成功。机器已经成功开机";
							}
						} else {
							$info = "操作成功。机器已经处于开机";
						}
					}*/
                    $info = "操作成功。但机器可能未开机，请先查看机器状态";
                }

            }

            #这里自有机器要将最新的配置信息更新到用户机器
            $MemberPdtQuery->config         = json_encode($config, JSON_UNESCAPED_UNICODE);
            $MemberPdtQuery->real_bandwidth = $config['configbandwidth'];
            #
            $MemberPdtQuery->pdt_id            = $IdlePdtRes->pdt_id;
            $MemberPdtQuery->provider_id       = $IdlePdtRes->provider_id;
            $MemberPdtQuery->server_type_id    = $IdlePdtRes->server_type_id;
            $MemberPdtQuery->room_id           = $IdlePdtRes->room_id;
            $MemberPdtQuery->cabinet_id        = $IdlePdtRes->cabinet_id;
            $MemberPdtQuery->occupies_position = $IdlePdtRes->occupies_position;
            $MemberPdtQuery->property          = $IdlePdtRes->property;
            $MemberPdtQuery->type_id           = $IdlePdtRes->type_id;
            $MemberPdtQuery->switch_location   = $IdlePdtRes->switch_location;
            $MemberPdtQuery->switch_port       = $IdlePdtRes->switch_port;
            $MemberPdtQuery->ipmi_ip           = $IdlePdtRes->ipmi_ip;
            $MemberPdtQuery->ipmi_name         = $IdlePdtRes->ipmi_name;
            $MemberPdtQuery->ipmi_pwd          = $IdlePdtRes->ipmi_pwd;

        }
        #更新用户产品状态
        $MemberPdtQuery->status   = 1;
        $MemberPdtQuery->admin_id = $UserMemberRes['admin_id'];
        #原时间不变
        #$MemberPdtQuery->start_time = time();
        #$MemberPdtQuery->end_time = time();

        $OutputRes = OutPut::ReuseRecordDelete($MemberPdtQuery->unionid);


        if ($MemberPdtQuery->update() && $OutputRes['status']) {
            $transaction->commit();
            $arrReturn = [
                'status' => 1,
                'info'   => $info,
            ];
        } else {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => '业务恢复操作失败',
            ];
        }
        return $this->renderJSON($arrReturn);
    }

    /**
     * 删除数据处理方法  这里的删除是永久删除
     */
    public
    function actionDel()
    {
        Yii::$app->request->isAjax || die('error');
        $PdtLogModel         = new PdtLog();
        $MemberPdtModel      = new MemberPdt();
        $idleModel           = new IdlePdt();
        $InitialAccountModel = new InitialAccount();
        if ($this->post('id') == '')
            return $this->renderJSON(['status' => 0, 'info' => '操作失效']);
        #开启事务
        $transaction  = Yii::$app->db->beginTransaction();
        $MemberPdtRes = $MemberPdtModel->findOne($this->post('id'));

        //未找到对应信息
        if (empty($MemberPdtRes)) {
            $arrReturn = [
                'status' => 0,
                'info'   => '操作失效，未找到对应产品信息',
            ];
            return $this->renderJSON($arrReturn);
        }
        /*if( $MemberPdtRes['status'] != 1 || $MemberPdtRes['audit_status'] != 1) {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'当前产品的状态不能删除'
			];
			return $this->renderJSON($arrReturn);
		} */
        //删除初始账户
        $InitialAccountRes = $InitialAccountModel->findOne(['unionid' => $MemberPdtRes['unionid']]);
        if (!empty($InitialAccountRes)) {
            $res = $InitialAccountModel->del($InitialAccountRes->id);
            if ($res <= 0) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '初始账户删除失败',
                ];
                return $this->renderJSON($arrReturn);
            }
        }
        if ($MemberPdtModel->del($this->post('id')) > 0) {
            $transaction->commit();
            $arrReturn = [
                'status' => 1,
                'info'   => '用户产品删除成功',
            ];
            LogWrite::pdtlogdel($this->post('memberpdt_id'), $MemberPdtRes, $MemberPdtModel->attributeLabels(), $MemberPdtModel->tableNamealias());
        } else {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => '用户产品信息删除失败',
            ];
        }
        return $this->renderJSON($arrReturn);
    }

    //修改初始账户
    public
    function actionModfiyaccount()
    {
        Yii::$app->request->isAjax || die('error');

        $InitialAccountModel = new InitialAccount();
        $AccountRes          = $InitialAccountModel->findOne($this->post('id'));

        if (empty($AccountRes)) {
            $arrReturn = [
                'status' => 0,
                'info'   => "未知的初始账户信息",
            ];
        }
        $AccountRes->scenario   = 'modify';
        $AccountRes->attributes = $this->post();
        //验证失败
        if (!$AccountRes->validate()) {
            $arrReturn = [
                'status' => 0,
                'info'   => "输入信息有误",
            ];
            return $this->renderJSON($arrReturn);
        }
        $newModel = $AccountRes->Attributes;   //print_r($newModel);
        $oldModel = $AccountRes->oldAttributes;//print_r($oldModel);

        if ($AccountRes->update()) {
            $arrReturn = [
                'status' => 1,
                'info'   => '初始账户修改成功',
            ];
            #LogWrite::pdtlogmodfiy($newModel, $oldModel, $InitialAccountModel->attributeLabels(), $InitialAccountModel->tableNamealias(), $AccountRes['id']);
        } else {
            $arrReturn = [
                'status' => 0,
                'info'   => '初始账户信息未修改',
            ];
        }
        return $this->renderJSON($arrReturn);
    }

    /**
     * 退款
     *
     * @return string|\yii\web\Response|Ambigous <string, string>
     */
    public
    function actionOldRefund()
    {
        $MemberPdtModel = new MemberPdt();
        $TradeModel     = new Trade();

        if (Yii::$app->request->post()) {
            Yii::$app->request->isAjax || die('error');
            $post = $this->post();
            #//查询出产品的信息
            $MemberPdtQuery = $MemberPdtModel->findOne($post['id']);
            #开启事务
            $transaction = Yii::$app->db->beginTransaction();
            if (empty($MemberPdtQuery)) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '未知的产品信息',
                ];
                return $this->renderJSON($arrReturn);
            }

            if ($MemberPdtQuery['status'] != 1 || $MemberPdtQuery['audit_status'] != 1) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '当前产品不在正常状态或未审核',
                ];
                return $this->renderJSON($arrReturn);
            }
            //获取到购买是的订单信息
            $TradeRes = $TradeModel->find()->where(['unionid' => $MemberPdtQuery['unionid'], 'trade_orderid' => $MemberPdtQuery['trade_no']])->asArray()->one();//print_r($TradeRes);die();
            if ($post['refund_type'] == 'date') {  //按照时间
                $Date_1 = $MemberPdtQuery['start_time'];
                $Date_3 = $MemberPdtQuery['end_time'];

                $Date_2 = $post['refundDate'];
                $d2     = strtotime($Date_2);
                if ($d2 < $Date_1 || $d2 > $Date_3) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '选择的截止计算时间有误',
                    ];
                    return $this->renderJSON($arrReturn);
                }
                $Days          = round(($d2 - $Date_1) / 3600 / 24);                                             //实际天数
                $Days1         = round(($Date_3 - $Date_1) / 3600 / 24);                                         //理论使用时间
                $refund_amount = sprintf("%01.2f", $TradeRes['trade_price_payment'] / $Days1 * ($Days1 - $Days));//每天的钱*（理论使用时间 - 实际使用时间）
            } elseif ($post['refund_type'] == 'custom') { //自定义价格
                $refund_amount = $post['refundcustom'];
                if ($refund_amount <= 0) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '自定义退款金额必须大于0元',
                    ];
                    return $this->renderJSON($arrReturn);
                }
                /*if( $refund_amount > $TradeRes['trade_price_payment']) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status'=>0,
                        'info'=>'自定义退款金额不能大于购买所用金额'
                    ];
                    return $this->renderJSON($arrReturn);
                }*/
            } elseif ($post['refund_type'] == 'full') {  //全额退款
                $refund_amount = $TradeRes['trade_price_payment'];
            }

            $UserMemberModel = new UserMember();
            $UserMemberRes   = $UserMemberModel->findOne(['u_id' => $MemberPdtQuery['user_id']]);
            //退款记录
            $PdtRefundModel = new PdtRefund();
            $time           = time();

            $PdtRefundModel->unionid       = $MemberPdtQuery['unionid'];
            $PdtRefundModel->member_id     = $UserMemberRes['u_id'];
            $PdtRefundModel->member_name   = $UserMemberRes['email'];
            $PdtRefundModel->admin_id      = $UserMemberRes['admin_id'];
            $PdtRefundModel->admin_name    = $UserMemberRes['admin_name'];
            $PdtRefundModel->refund_reason = $post['refund_reason'];
            $PdtRefundModel->reasontext    = $post['reasontext'];
            $PdtRefundModel->refund_type   = $post['refund_type'];
            $PdtRefundModel->refund_amount = $refund_amount;
            $PdtRefundModel->create_time   = $time;
            $PdtRefundModel->status        = 0;
            $PdtRefundModel->remarks       = "";

            $MemberPdtQuery->status = -2;//将状态改为退款中

            #查询由后台发起的业务退款是否为审核
            $refundOrder   = Yii::$app->db->createCommand("select nv_type from notice_manage where nv_key = 'refund_order'")->queryScalar();
            $notice_do_man = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

            $hasCheck = Yii::$app->db->createCommand("select * from notice_record where notice_user like '%" . $UserMemberRes['email'] . "%' and notice_key = 'refund_order' and notice_type = '审核' and notice_audit_time is null")->queryOne();

            if ($refundOrder == '审核') {

                if ($hasCheck) {
                    return $this->renderJSON(['status' => 0, 'info' => '当前已有一笔待审核业务退款操作，请先处理后再行操作']);
                }

                $dataContent[] = [
                    'sql' => base64_encode("update member_pdt set status='" . $MemberPdtQuery['status'] . "' where unionid = '" . $MemberPdtQuery['unionid'] . "'"),
                ];
                $dataContent[] = [
                    'sql' => base64_encode("insert into pdt_refund (unionid,member_id,member_name,admin_id,admin_name,refund_reason,reasontext,refund_type,refund_amount,create_time,status) 
					values('" . $MemberPdtQuery['unionid'] . "', '" . $UserMemberRes['u_id'] . "', '" . $UserMemberRes['email'] . "', '" . $UserMemberRes['admin_id'] . "', '" . $UserMemberRes['admin_name'] . "', '" . $post['refund_reason'] . "', '" . $post['reasontext'] . "', '" . $post['refund_type'] . "', '" . $refund_amount . "', '" . $time . "', '0')"),
                ];
                /*
				$dataContent[] = [
					'sql' => base64_encode("insert into pdt_refund values(null, '".$MemberPdtQuery['unionid']."', '".$UserMemberRes['u_id']."', '".$UserMemberRes['email']."', '".$UserMemberRes['admin_id']."', '".$UserMemberRes['admin_name']."',
					'".$post['refund_reason']."', '".$post['reasontext']."', '".$post['refund_type']."', '".$refund_amount."', '".$time."', null, '0', '', null)"),
				];
				*/

                $dataJson = json_encode($dataContent);

                NotifyHandle::RefundOrder_Notify(0, $refund_amount, $UserMemberRes['u_id'], $notice_do_man, $MemberPdtQuery['unionid'], $dataJson);
                $transaction->commit();
                $arrReturn = [
                    'status' => 1,
                    'info'   => '已提交退款申请，等待审核',
                ];
            } else {
                NotifyHandle::RefundOrder_Notify(0, $refund_amount, $UserMemberRes['u_id'], $notice_do_man, $MemberPdtQuery['unionid'], null);

                if ($PdtRefundModel->insert() && $MemberPdtQuery->update(false)) {
                    $transaction->commit();
                    $arrReturn = [
                        'status' => 1,
                        'info'   => '退款申请成功,请等待处理',
                    ];
                } else {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '退款申请失败,请稍后重新申请',
                    ];
                }
            }
            return $this->renderJSON($arrReturn);

        } else {
            $id           = $this->get('id');
            $MemberPdtRes = $MemberPdtModel->getRowById($id);                                                                                               ##// print_r($MemberPdtRes);die();
            if (empty($MemberPdtRes)) {
                return $this->redirect(['index']);
            }

            $ipArr = json_decode($MemberPdtRes['ip'], true);
            $ip    = '';
            if (count($ipArr > 12)) {
                for ($i = 0; $i < count($ipArr); $i++) {
                    if ($i % 11 == 0 && $i != 0) {
                        if ($i == count($ipArr) - 1) {
                            $ip .= $ipArr[$i];
                        } else {
                            $ip .= $ipArr[$i] . '&nbsp;&nbsp;<br/>';
                        }
                    } else {
                        if ($i == count($ipArr) - 1) {
                            $ip .= $ipArr[$i];
                        } else {
                            $ip .= $ipArr[$i] . '&nbsp;&nbsp;';
                        }
                    }
                }
            } else {
                $ip = implode('&nbsp;&nbsp;', $ipArr);
            }

            //获取到购买的单
            $TradeRes = $TradeModel->find()->where(['unionid' => $MemberPdtRes['unionid'], 'trade_orderid' => $MemberPdtRes['trade_no']])->asArray()->one();//print_r($TradeRes);die();
            //获取对应的订单信息

            $TradelistRes = $TradeModel->find()->where(['unionid' => $MemberPdtRes['unionid'], 'trade_status' => "已支付"])->orderBy("trade_time_create")->asArray()->all();//print_r($TradeRes);die();

            return $this->render('refund', ['MemberPdtRes' => $MemberPdtRes, 'TradeRes' => $TradeRes, 'TradelistRes' => $TradelistRes, 'ip' => $ip]);
        }
    }

    /* *
	* 变更配置
	* */
    public
    function actionUpgrade()
    {
        $TradeModel      = new Trade();
        $MemberPdtModel  = new MemberPdt();
        $UserMemberModel = new  UserMember;
        if (Yii::$app->request->post()) {
            $post = DataHelper::dotrim($this->post());
            if ($post['ram'] == "" && $post['hdd'] == "" && $post['bandwidth'] == "" && $post['ipnumber'] == "" && $post['defense'] == "") {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '请至少变更一种配置',
                ];
                return $this->renderJSON($arrReturn);
            }
            if (!is_numeric($post['normal_price']) || !is_numeric($post['normal_cost_price']) || !is_numeric($post['price'])) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '价格费用不能为空，若没有价格变动则填写原价格',
                ];
                return $this->renderJSON($arrReturn);
            }
            if (!is_numeric($post['normal_price']) || !is_numeric($post['normal_cost_price']) || $post['price'] == '') {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '请输入正确的价格',
                ];
                return $this->renderJSON($arrReturn);
            }
            //#获取当前的产品信息
            $MemberPdtRes = $MemberPdtModel->find()->where(['id' => $post['id']])->asArray()->one();
            if (empty($MemberPdtRes)) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '未知的业务信息',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($MemberPdtRes['status'] != 1 || $MemberPdtRes['audit_status'] != 1) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '当前产品不在正常状态或未审核',
                ];
                return $this->renderJSON($arrReturn);
            }

            #原产品配置类别
            $PdtManageModel = new PdtManage();
            $pdtmanageRes   = $PdtManageModel->getRowById($MemberPdtRes['pdt_id']);

            #//获取当前产品的用户信息
            $UserRes = $UserMemberModel->find()->where(['u_id' => $MemberPdtRes['user_id']])->asArray()->one();

            #//原来的服务器配置
            $now_config = json_decode($MemberPdtRes['config'], true);  #var_dump($now_config);exit;
            //得到真实的升级信息
            $new_config = [];
            $message    = "";
            #CPU 不允许更换
            $new_config['cpu'] = $now_config['cpu'];

            if ($post['ram'] != "" || $post['ram'] != null) {
                $message           .= "原内存：" . $now_config['ram'] . "变更为：" . $post['ram'] . "<br/>";
                $new_config['ram'] = $post['ram'];
            } else {
                $new_config['ram'] = $now_config['ram'];
            }
            if ($post['hdd'] != "" || $post['hdd'] != null) {
                $message           .= "原硬盘：" . $now_config['hdd'] . "变更为：" . $post['hdd'] . "<br/>";
                $new_config['hdd'] = $post['hdd'];
            } else {
                $new_config['hdd'] = $now_config['hdd'];
            }
            if ($post['bandwidth'] != "" || $post['bandwidth'] != null) {
                $message                             .= "原带宽：" . $MemberPdtRes['bandwidth'] . "变更为：" . $post['bandwidth'] . "<br/>";
                $new_config['requirement_bandwidth'] = $post['bandwidth'];             #变更的是客户要求带宽
                $new_config['configbandwidth']       = $now_config['configbandwidth']; #实际带宽没有变
            } else {
                $new_config['configbandwidth']       = $now_config['configbandwidth'];
                $new_config['requirement_bandwidth'] = $MemberPdtRes['bandwidth'];
            }
            if ($post['ipnumber'] != "" || $post['ipnumber'] != null) {
                $message                .= "原IP可用数：" . $now_config['ipnumber'] . "变更为：" . $post['ipnumber'] . "<br/>";
                $new_config['ipnumber'] = $post['ipnumber'];
            } else {
                $new_config['ipnumber'] = $now_config['ipnumber'];
            }
            if ($post['defense'] != "" || $post['defense'] != null) {
                $message               .= "原防御值：" . $now_config['defense'] . "变更为：" . $post['defense'] . "<br/>";
                $new_config['defense'] = $post['defense'];
            } else {
                $new_config['defense'] = $now_config['defense'];
            }
            #系统不更换
            $new_config['operatsystem'] = $now_config['operatsystem']; #print_r($new_config);exit;

            #开启事务
            $transaction = Yii::$app->db->beginTransaction();
            $time        = time();

            #当本次变更配置需要支付补款，则生成订单
            if ($post['price'] > 0) {
                $trade_config['config']                 = $new_config;
                $trade_config['ip']                     = $now_config['ip'] = json_decode($MemberPdtRes['ip'], true);
                $trade_config['ip2']                    = $now_config['ip2'] = json_decode($MemberPdtRes['ip2'], true);
                $trade_config['normal_price']           = sprintf("%.2f", $post['normal_price']);
                $trade_config['normal_cost_price']      = sprintf("%.2f", $post['normal_cost_price']);
                $trade_config['old_sell_price']         = $MemberPdtRes['sell_price'];
                $trade_config['old_cost_price']         = $MemberPdtRes['cost_price'] ? $MemberPdtRes['cost_price'] : 0;
                $trade_config['old_upgrade_cost_price'] = $MemberPdtRes['upgrade_cost_price'] ? $MemberPdtRes['upgrade_cost_price'] : 0;

                $trade_config['price']         = $post['price'];
                $trade_config['currency_type'] = $this->post('currency_type');  #货币类型
                $trade_config['server_typeid'] = $pdtmanageRes['pdt_type_id'];

                #获取订单号
                $trade_orderid = DataHelper::createTradeno();
                #创建订单
                $array = [
                    'trade_orderid'        => $trade_orderid,
                    'admin_id'             => $UserRes['admin_id'],
                    'admin_name'           => $UserRes['admin_name'],
                    'member_id'            => $UserRes['u_id'],
                    'member_name'          => $UserRes['email'],
                    'unionid'              => $MemberPdtRes['unionid'],
                    'serviceid'            => "",
                    'trade_type_pay'       => "",
                    'trade_type_money'     => Yii::$app->params['trade_type']['change_config_rubsidy']['name'],
                    'trade_price_original' => $this->post('price'),
                    'trade_price_real'     => $this->post('price'),
                    'trade_price_payment'  => null,
                    'trade_type_do'        => "变更配置",
                    'trade_config'         => json_encode($trade_config, JSON_UNESCAPED_UNICODE),
                    'trade_time_create'    => $time,
                    'trade_remark'         => "",
                ];

                #查询由后台发起的升级配置是否为审核
                $upgrade_order = Yii::$app->db->createCommand("select nv_type from notice_manage where nv_key = 'upgrade_order'")->queryScalar();

                $notice_do_man                      = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
                $notify_config                      = $trade_config;
                $notify_config['server_typeid']     = Yii::$app->db->createCommand("select name from pdt_room_type where id = '" . $notify_config['server_typeid'] . "'")->queryScalar();
                $notify_config['normal_price']      .= ' / ' . $MemberPdtRes['payment_cycle'] . '个月';
                $notify_config['normal_cost_price'] .= ' / ' . $MemberPdtRes['payment_cycle'] . '个月';
                $now_config['normal_price']         = $MemberPdtRes['sell_price'] . ' / ' . $MemberPdtRes['payment_cycle'] . '个月';
                $now_config['normal_cost_price']    = $MemberPdtRes['upgrade_cost_price'] . ' / ' . $MemberPdtRes['payment_cycle'] . '个月';
                $now_config['price']                = '0.00';


                $UserMemberRes = Yii::$app->db->createCommand("select * from user_member where u_id = '" . $MemberPdtRes['user_id'] . "'")->queryOne();
                $hasCheck      = Yii::$app->db->createCommand("select * from notice_record where notice_user like '%" . $UserMemberRes['email'] . "%' and notice_key = 'upgrade_order' and notice_type = '审核' and notice_audit_time is null")->queryOne();

                if ($upgrade_order == '审核') {

                    if ($hasCheck) {
                        return $this->renderJSON(['status' => 0, 'info' => '当前已有一笔待审核变更配置操作，请先处理后再行操作']);
                    }
                    $transaction->rollBack();
                    $dataContent[] = [
                        'sql' => base64_encode("insert into trade (trade_orderid,member_id,member_name,admin_id,admin_name,unionid,trade_type_money,trade_price_original,trade_price_real,trade_type_do,trade_config,trade_status,trade_time_create,trade_audit) 
						values('" . $array['trade_orderid'] . "', '" . $array['member_id'] . "', '" . $array['member_name'] . "', '" . $array['admin_id'] . "', '" . $array['admin_name'] . "',
						'" . $array['unionid'] . "', '" . $array['trade_type_money'] . "', '" . $array['trade_price_original'] . "', '" . $array['trade_price_real'] . "', '" . $array['trade_type_do'] . "', 
						'" . $array['trade_config'] . "', '未支付', '" . $array['trade_time_create'] . "', 'Y')"),
                    ];

                    $dataJson = json_encode($dataContent, JSON_UNESCAPED_UNICODE);

                    NotifyHandle::UpgradeOrderStart_Notify($now_config, $notify_config, $MemberPdtRes['user_id'], $notice_do_man, $MemberPdtRes['unionid'], $dataJson);
                    $arrReturn = [
                        'status' => 1,
                        'info'   => '已提交变更配置申请，等待审核',
                    ];
                } else {
                    NotifyHandle::UpgradeOrderStart_Notify($now_config, $notify_config, $MemberPdtRes['user_id'], $notice_do_man, $MemberPdtRes['unionid'], null);

                    $resTrade = $TradeModel->InsertTradeData($array);
                    if (!$resTrade) {
                        #回滚事务
                        $transaction->rollBack();
                        return $this->renderJSON([
                            'status' => 0,
                            'info'   => '订单创建失败',
                        ]);
                    } else {
                        $transaction->commit();
                        return $this->renderJSON([
                            'status' => 1,
                            'info'   => '订单创建成功',
                        ]);
                    }
                }
                return $this->renderJSON($arrReturn);

            } else if ($post['price'] <= 0) {
                //当补款金额为0，则表示无差价更换配置，直接生成服务工单
                #加入服务管理
                $newservice_front_config['config']            = $now_config;
                $newservice_front_config['normal_price']      = $MemberPdtRes['sell_price'];        #配件变更的续费价格
                $newservice_front_config['normal_cost_price'] = $MemberPdtRes['upgrade_cost_price'];#配件升级的成本价格
                $newservice_front_config['price']             = '0.00';                             #本次需要续费的费用

                $newservice_after_config['config']            = $new_config;
                $newservice_after_config['ip']                = $newservice_front_config['ip'] = json_decode($MemberPdtRes['ip'], true);
                $newservice_after_config['ip2']               = $newservice_front_config['ip2'] = json_decode($MemberPdtRes['ip2'], true);
                $newservice_after_config['normal_price']      = $post['normal_price'];
                $newservice_after_config['normal_cost_price'] = $post['normal_cost_price'];

                $newservice_after_config['old_sell_price']         = $MemberPdtRes['sell_price'];
                $newservice_after_config['old_cost_price']         = $MemberPdtRes['cost_price'] ? $MemberPdtRes['cost_price'] : 0;
                $newservice_after_config['old_upgrade_cost_price'] = $MemberPdtRes['upgrade_cost_price'] ? $MemberPdtRes['upgrade_cost_price'] : 0;

                $newservice_after_config['price']         = $post['price'];
                $newservice_after_config['currency_type'] = $this->post('currency_type');  #货币类型
                $newservice_after_config['server_typeid'] = $pdtmanageRes['pdt_type_id'];

                $serviceid                               = DataHelper::createServiceid();
                $ServiceOrderModel                       = new ServiceOrder();
                $ServiceOrderModel->serviceid            = $serviceid;
                $ServiceOrderModel->unionid              = $MemberPdtRes['unionid'];
                $ServiceOrderModel->member_id            = $UserRes['u_id'];
                $ServiceOrderModel->member_name          = $UserRes['email'];
                $ServiceOrderModel->admin_id             = $UserRes['admin_id'];
                $ServiceOrderModel->admin_name           = $UserRes['admin_name'];
                $ServiceOrderModel->service_type_do      = "变更配置";
                $ServiceOrderModel->service_front_config = json_encode($newservice_front_config, JSON_UNESCAPED_UNICODE);
                $ServiceOrderModel->service_after_config = json_encode($newservice_after_config, JSON_UNESCAPED_UNICODE);
                $ServiceOrderModel->service_time_start   = $time;
                $ServiceOrderModel->service_status       = "等待确认";
                $ServiceOrderModel->service_remark       = "";


                #查询由后台发起的升级配置是否为审核
                $upgrade_order = Yii::$app->db->createCommand("select nv_type from notice_manage where nv_key = 'upgrade_order'")->queryScalar();
                $notice_do_man = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

                $notify_after_config = $newservice_after_config;
                $notify_front_config = $newservice_front_config;

                $newservice_after_config['normal_price']      .= ' / ' . $MemberPdtRes['payment_cycle'] . '个月';
                $newservice_after_config['normal_cost_price'] .= ' / ' . $MemberPdtRes['payment_cycle'] . '个月';
                $newservice_front_config['normal_price']      .= ' / ' . $MemberPdtRes['payment_cycle'] . '个月';
                $newservice_front_config['normal_cost_price'] .= ' / ' . $MemberPdtRes['payment_cycle'] . '个月';

                $newservice_after_config['server_typeid'] = Yii::$app->db->createCommand("select name from pdt_room_type where id = '" . $newservice_after_config['server_typeid'] . "'")->queryScalar();

                $UserMemberRes = Yii::$app->db->createCommand("select * from user_member where u_id = '" . $MemberPdtRes['user_id'] . "'")->queryOne();
                $hasCheck      = Yii::$app->db->createCommand("select * from notice_record where notice_user like '%" . $UserMemberRes['email'] . "%' and notice_unionid='" . $MemberPdtRes['unionid'] . "' and notice_key = 'upgrade_order' and notice_type = '审核' and notice_audit_time is null")->queryOne();

                if ($upgrade_order == '审核') {

                    if ($hasCheck) {
                        return $this->renderJSON(['status' => 0, 'info' => '当前已有一笔待审核变更配置操作，请先处理后再行操作']);
                    }

                    $transaction->rollBack();
                    $dataContent[] = [
                        'sql' => base64_encode("insert into service_order (serviceid,unionid,member_id,member_name,admin_id,admin_name,service_type_do,service_front_config,service_after_config,service_time_start,service_status,service_audit) 
						values('" . $serviceid . "', '" . $MemberPdtRes['unionid'] . "', '" . $UserRes['u_id'] . "', '" . $UserRes['email'] . "', '" . $UserRes['admin_id'] . "', '" . $UserRes['admin_name'] . "',
						'变更配置', '" . json_encode($notify_front_config, JSON_UNESCAPED_UNICODE) . "', '" . json_encode($notify_after_config, JSON_UNESCAPED_UNICODE) . "', '" . $time . "', '等待确认', 'Y')"),
                    ];

                    /*$dataContent[] = [
						'sql' => base64_encode("insert into service_order values(null, '".$serviceid."', '".$MemberPdtRes['unionid']."', '".$UserRes['u_id']."', '".$UserRes['email']."', '".$UserRes['admin_id']."', '".$UserRes['admin_name']."',
						'变更配置', '".json_encode($notify_front_config, JSON_UNESCAPED_UNICODE)."', '".json_encode($notify_after_config, JSON_UNESCAPED_UNICODE)."', '".$time."', null, null, null, null, '等待确认', '', 'Y')"),
					];*/

                    $dataContent[] = [
                        'sql' => base64_encode("update member_pdt set status = 2 where unionid = '" . $MemberPdtRes['unionid'] . "'"),
                    ];

                    $dataJson = json_encode($dataContent, JSON_UNESCAPED_UNICODE);

                    NotifyHandle::UpgradeOrderStart_Notify($newservice_front_config, $newservice_after_config, $MemberPdtRes['user_id'], $notice_do_man, $MemberPdtRes['unionid'], $dataJson);
                    $arrReturn = [
                        'status' => 1,
                        'info'   => '已提交变更配置申请，等待审核',
                    ];
                } else {
                    NotifyHandle::UpgradeOrderStart_Notify($newservice_front_config, $newservice_after_config, $MemberPdtRes['user_id'], $notice_do_man, $MemberPdtRes['unionid'], null);

                    $ServiceOrderResult    = $ServiceOrderModel->insert();
                    $updateMemberPdtStatus = Yii::$app->db->createCommand("update member_pdt set status = 2 where id = '" . $MemberPdtRes['unionid'] . "'")->execute();

                    if (!$ServiceOrderResult || !$updateMemberPdtStatus) {
                        #回滚事务
                        $transaction->rollBack();
                        return $this->renderJSON([
                            'status' => 0,
                            'info'   => '服务工单创建失败',
                        ]);
                    } else {
                        $transaction->commit();
                        return $this->renderJSON([
                            'status' => 1,
                            'info'   => '服务工单创建成功',
                        ]);
                    }
                }
                return $this->renderJSON($arrReturn);
            }
        }
    }

    /**
     *更换IP
     */
    public
    function actionChangeip()
    {
        $MemberPdtModel    = new MemberPdt();
        $ServiceOrderModel = new ServiceOrder();
        $UserMemberModel   = new UserMember();
        $PdtIpModel        = new PdtIp();
        $TestServerModel   = new TestServer();

        if (Yii::$app->request->post()) {
            $post         = $this->post();
            $MemberPdtRes = $MemberPdtModel->findOne(['id' => $post['id']]);          #获取用户产品信息对象
            $oldIP        = json_decode($MemberPdtRes->ip2, true);                    #print_r($oldIP);die();
            $UserRes      = $UserMemberModel->findOne(['u_id' => $MemberPdtRes->user_id]);

            $IpArr = DataHelper::dotrim($post['ips']);
            if (empty($IpArr)) {
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => 'IP地址未选择或未填写',
                ]);
            }

            //将提交的IP进行验证
            if ($MemberPdtRes->servicerprovider == 0) {
                //$IPResArray= $IpArr;
                $ip2 = $IpArr;
                //考虑为IP段，所以重新定义
                foreach ($ip2 as $key => $value) {
                    if (strpos($value, '-') !== false) {
                        $value1 = explode('-', trim($value));//print_r($value1);
                        $num4   = substr(trim($value1[0]), strripos(trim($value1[0]), ".") + 1);
                        $str    = substr(trim($value1[0]), 0, strrpos(trim($value1[0]), '.'));
                        if ($num4 >= trim($value1[1])) {
                            $arrReturn = [
                                'status' => 0,
                                'info'   => 'IP段输入有误',
                            ];
                            return $this->renderJSON($arrReturn);
                        } else {
                            $num = $value1[1] - ($num4);//echo $num;
                            for ($n = 0; $n <= $num; $n++) {
                                $newnum = $num4 + $n;;
                                $newip        = $str . "." . $newnum;
                                $IPResArray[] = trim($newip);
                            }
                        }
                    } else {
                        $IPResArray[] = trim($value);
                    }
                }
                if ($IPResArray == $oldIP) {
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '未有IP做更换',
                    ]);
                }
            } elseif ($MemberPdtRes->servicerprovider == 1) {
                $ip2 = $IpArr;
                //考虑供应商的IP可能为IP段，所以重新定义
                foreach ($ip2 as $key => $value) {
                    if (strpos($value, '-') !== false) {
                        $value1 = explode('-', trim($value));//print_r($value1);
                        $num4   = substr(trim($value1[0]), strripos(trim($value1[0]), ".") + 1);
                        $str    = substr(trim($value1[0]), 0, strrpos(trim($value1[0]), '.'));
                        if ($num4 >= trim($value1[1])) {
                            $arrReturn = [
                                'status' => 0,
                                'info'   => 'IP段输入有误',
                            ];
                            return $this->renderJSON($arrReturn);
                        } else {
                            $num = $value1[1] - ($num4);//echo $num;
                            for ($n = 0; $n <= $num; $n++) {
                                $newnum = $num4 + $n;;
                                $newip        = $str . "." . $newnum;
                                $IPResArray[] = trim($newip);
                            }
                        }
                    } else {
                        $IPResArray[] = trim($value);
                    }
                }

                if ($IPResArray == $oldIP) {
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '未有IP做更换',
                    ]);
                }
                $diffArray = array_diff($IPResArray, $oldIP);
                ##如果不为空 加了IP   如果为空 代表减少了IP
                if (!empty($diffArray)) {
                    foreach ($diffArray as $value) {
                        $MemberPdtIpAll = $MemberPdtModel->find()->select('unionid,ip,ip2')->where(['like', 'ip2', '"' . $value . '"'])->andWhere('status !=-1')->asArray()->all();
                        if (!empty($MemberPdtIpAll)) {
                            return $this->renderJSON([
                                'status' => 0,
                                'info'   => 'IP地址 :' . $value . ' 已经被使用',
                            ]);
                        }
                        $TestServerIPAll = $TestServerModel->find()->select('ip,ip2')->where(['like', 'ip2', '"' . $value . '"'])->andWhere(['status' => ['0', '2']])->asArray()->all();
                        if (!empty($TestServerIPAll)) {
                            return $this->renderJSON([
                                'status' => 0,
                                'info'   => 'IP地址 :' . $value . ' 已经被使用(测试服务器)',
                            ]);
                        }

                    }
                }

                $PdtIpAll = $PdtIpModel->find()->where(['in', 'ip', $IPResArray])->asArray()->all();
                if (!empty($PdtIpAll)) {
                    $PdtIpAll  = array_column($PdtIpAll, 'ip');
                    $res       = implode('，', $PdtIpAll);
                    $arrReturn = [
                        'status' => 0,
                        'info'   => 'IP地址：' . $res . '存在于IP库',
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }
            //处理的得到的IP数组1
            $ipArray1 = array_unique($IpArr);                                         //去重 去空
            $ipArray  = array_filter($ipArray1);
            //处理的得到的IP数组2
            $ipArray2 = array_unique($IPResArray);
            $ip2Array = array_filter($ipArray2);
            //print_r($ipArray);print_r($ip2Array);die();
            //判断IP组2是否正常(再一次验证)
            if (!empty($ip2Array)) {
                foreach ($ip2Array as $value) {
                    if (!filter_var($value, FILTER_VALIDATE_IP)) {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => $value . '为不合法的IP地址',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
            }

            if (count($ip2Array) >= count($oldIP)) {
                if (empty(array_diff($ip2Array, $oldIP))) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '未有IP改变',
                    ];
                    return $this->renderJSON($arrReturn);
                }
            } elseif (count($oldIP) >= count($ip2Array)) {
                if (empty(array_diff($oldIP, $ip2Array))) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '未有IP改变',
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }

            #判断IP所属机房是否与产品的机房一致（仅在为自有时）
            if ($MemberPdtRes->servicerprovider == 0) {
                $PdtIpAll = $PdtIpModel->find()->select('room_id')->where(['in', 'ip', $ip2Array])->asArray()->all();#print_r($PdtIpAll);die;
                foreach ($PdtIpAll as $v) {
                    $v      = join(',', $v);
                    $temp[] = $v;
                }
                $temp = array_unique($temp); #去掉重复的字符串,也就是重复的一维数组
                $temp = array_values($temp); #print_r($temp); 查询排序
                if (count($temp) > 1) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => 'IP中有属于其他机房的IP地址',
                    ];
                    return $this->renderJSON($arrReturn);
                } else if (count($temp) == 1) {
                    if ($temp[0] != $MemberPdtRes->room_id) {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => 'IP所属机房与产品选择机房不一致',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
            }
            #开启事务
            $transaction = Yii::$app->db->beginTransaction();

            //如果产品是自有库产品，#//将新选择的IP 状态改为待定 （作比较，获取2个IP数组的差集  先将2个数组合并并且去重）
            if ($MemberPdtRes->servicerprovider == 0 && $MemberPdtRes->idle_id != "") {
                $bgIPArray       = array_unique(array_merge($ip2Array, $oldIP));
                $result_bangding = array_diff($bgIPArray, $oldIP);   //需要改为使用中的
                $result_jiechu   = array_diff($bgIPArray, $ip2Array);//需要改为闲置的


                /*foreach($result_bangding as $velue){
					$PdtIpRes = $PdtIpModel->find()->where(['ip'=>$value])->asArray()->one();
					if( empty($PdtIpRes) ) {
						$transaction->rollBack();
						$arrReturn = [
                            'status'=>0,
                            'info'=> 'IP库不存在IP：'.$value
                        ];
                        return $this->renderJSON($arrReturn);
					}else{
						if( $PdtIpRes['status'] != 0) {
							$transaction->rollBack();
							$arrReturn = [
								'status'=>0,
								'info'=> 'IP：'.$value.' 不处于闲置状态'
							];
							return $this->renderJSON($arrReturn);
						}
					}
				}*/

                $PdtIpAll         = $PdtIpModel->find()->select('ip,status,room_id')->where(['in', 'ip', $result_bangding])->asArray()->all(); #print_r($PdtIpAll);die;
                $PdtIpAll_IP      = array_column($PdtIpAll, 'ip');                                                                             #print_r($PdtIpAll_IP);
                $PdtIpAll_status  = array_column($PdtIpAll, 'status');                                                                         #print_r($PdtIpAll_status);exit;
                $PdtIpAll_room_id = array_column($PdtIpAll, 'room_id');                                                                        #print_r($PdtIpAll_room_id);die;

                foreach ($result_bangding as $val) {
                    if (!in_array($val, $PdtIpAll_IP)) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => 'IP：' . $val . '不存在于IP库中',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
                foreach ($PdtIpAll_status as $key => $val) {
                    if ($val != 0) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => 'IP：' . $PdtIpAll_IP[$key] . '未在闲置中',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
                foreach ($PdtIpAll_room_id as $key => $val) {
                    if ($val != $MemberPdtRes->room_id) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => 'IP：' . $PdtIpAll_IP[$key] . '与库IP所对应的机房不一致',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }

                //新的ip需要绑定的
                if (!empty($result_bangding)) {
                    #$res1 = $PdtIpModel->modifyIPStatus($result_bangding,"3");
                    $res1 = $PdtIpModel->updateAll(['status' => 3, 'update_time' => time()], ['ip' => $result_bangding]);

                    $iplist = "";
                    if (is_array($result_bangding)) {
                        foreach ($result_bangding as $vv) {
                            $iplist .= '\'' . $vv . '\',';
                        }
                        $iplist = substr($iplist, 0, -1);
                        //0-闲置 1-使用中 2-预留 3-待定 4-不可用
                        Yii::$app->db->createCommand("update pdt_ip set status = '3' where ip in(" . $iplist . ")")->execute();
                        $backContent[] = [
                            'sql' => base64_encode("update pdt_ip set status = '0' where ip in(" . $iplist . ")"),
                        ];

                    } else {
                        Yii::$app->db->createCommand("update pdt_ip set status = '3' where ip='" . $result_bangding . "'")->execute();
                        $backContent[] = [
                            'sql' => base64_encode("update pdt_ip set status = '0' where ip='" . $result_bangding . "'"),
                        ];
                    }
                }

                //需要解除绑定的IP
                if (!empty($result_jiechu)) {
                    #$res2 = $PdtIpModel->modifyIPStatus($result_jiechu,"3");
                    $res2 = $PdtIpModel->updateAll(['status' => 3, 'update_time' => time()], ['ip' => $result_jiechu]);

                    $iplist = "";
                    if (is_array($result_jiechu)) {
                        foreach ($result_jiechu as $vv) {
                            $iplist .= '\'' . $vv . '\',';
                        }
                        $iplist = substr($iplist, 0, -1);

                        Yii::$app->db->createCommand("update pdt_ip set status = '3' where ip in(" . $iplist . ")")->execute();

                        $backContent[] = [
                            'sql' => base64_encode("update pdt_ip set status = '1' where ip in(" . $iplist . ")"),
                        ];

                    } else {
                        Yii::$app->db->createCommand("update pdt_ip set status = '3' where ip='" . $result_jiechu . "'")->execute();

                        $backContent[] = [
                            'sql' => base64_encode("update pdt_ip set status = '1' where ip='" . $result_jiechu . "'"),
                        ];
                    }
                }
            }

            //生成服务ID
            $serviceid                                       = DataHelper::createServiceid();
            $time                                            = time();
            $newMemberPdtRes                                 = $MemberPdtRes->toArray();
            $trade_config['config']                          = json_decode($newMemberPdtRes['config'], true);
            $trade_config['config']['requirement_bandwidth'] = $newMemberPdtRes['bandwidth'];
            $trade_config['ip']                              = $ipArray;
            $trade_config['ip2']                             = $ip2Array;

            $service_front_config['config']                          = json_decode($newMemberPdtRes['config'], true);
            $service_front_config['config']['requirement_bandwidth'] = $newMemberPdtRes['bandwidth'];
            $service_front_config['ip']                              = json_decode($newMemberPdtRes['ip'], true);
            $service_front_config['ip2']                             = json_decode($newMemberPdtRes['ip2'], true);

            #加入服务管理
            $ServiceOrderModel                       = new ServiceOrder();
            $ServiceOrderModel->serviceid            = $serviceid;
            $ServiceOrderModel->unionid              = $MemberPdtRes->unionid;
            $ServiceOrderModel->member_id            = $UserRes['u_id'];
            $ServiceOrderModel->member_name          = $UserRes['email'];
            $ServiceOrderModel->admin_id             = $UserRes['admin_id'];
            $ServiceOrderModel->admin_name           = $UserRes['admin_name'];
            $ServiceOrderModel->service_type_do      = "IP变更";
            $ServiceOrderModel->service_front_config = json_encode($service_front_config, JSON_UNESCAPED_UNICODE);
            $ServiceOrderModel->service_after_config = json_encode($trade_config, JSON_UNESCAPED_UNICODE);
            $ServiceOrderModel->service_time_start   = $time;
            $ServiceOrderModel->service_status       = "等待确认";
            $ServiceOrderModel->service_remark       = "";

            $MemberPdtRes->status = 4;

            #查询由后台发起的ip变更是否为审核
            $change_IP     = Yii::$app->db->createCommand("select nv_type from notice_manage where nv_key = 'change_IP'")->queryScalar();
            $notice_do_man = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

            $UserMemberRes = Yii::$app->db->createCommand("select * from user_member where u_id = '" . $MemberPdtRes->user_id . "'")->queryOne();
            $hasCheck      = Yii::$app->db->createCommand("select * from notice_record where notice_user like '%" . $UserMemberRes['email'] . "%' and notice_key = 'change_IP' and notice_type = '审核' and notice_audit_time is null")->queryOne();

            if ($change_IP == '审核') {
                if ($hasCheck) {
                    return $this->renderJSON(['status' => 0, 'info' => '当前已有一笔待审核更换IP操作，请先处理后再行操作']);
                }
                $dataContent[] = [
                    'sql' => base64_encode("update member_pdt set status = '4' where unionid='" . $MemberPdtRes->unionid . "'"),
                ];

                $dataContent[] = [
                    'sql' => base64_encode("insert into service_order (serviceid,unionid,member_id,member_name,admin_id,admin_name,service_type_do,service_front_config,service_after_config,service_time_start,service_status,service_audit) 
					values('" . $serviceid . "', '" . $MemberPdtRes->unionid . "', '" . $UserRes['u_id'] . "', '" . $UserRes['email'] . "', '" . $UserRes['admin_id'] . "', '" . $UserRes['admin_name'] . "',
					'IP变更', '" . json_encode($service_front_config, JSON_UNESCAPED_UNICODE) . "', '" . json_encode($trade_config, JSON_UNESCAPED_UNICODE) . "', '" . $time . "', '等待确认', 'Y')"),
                ];

                /*$dataContent[] = [
					'sql' => base64_encode("insert into service_order values(null, '".$serviceid."', '".$MemberPdtRes->unionid."', '".$UserRes['u_id']."', '".$UserRes['email']."', '".$UserRes['admin_id']."', '".$UserRes['admin_name']."',
					'IP变更', '".json_encode($service_front_config, JSON_UNESCAPED_UNICODE)."', '".json_encode($trade_config, JSON_UNESCAPED_UNICODE)."', '".$time."', null, null, null, null, '等待确认', '', 'Y')"),
				];*/

                $dataJson = json_encode($dataContent, JSON_UNESCAPED_UNICODE);
                if (isset($backContent)) {
                    $backJson = json_encode($backContent, JSON_UNESCAPED_UNICODE);
                    NotifyHandle::ChangeIp_Notify($service_front_config, $trade_config, $MemberPdtRes['user_id'], $notice_do_man, $MemberPdtRes['unionid'], $dataJson, $backJson);
                } else {
                    NotifyHandle::ChangeIp_Notify($service_front_config, $trade_config, $MemberPdtRes['user_id'], $notice_do_man, $MemberPdtRes['unionid'], $dataJson, null);
                }

                $arrReturn = [
                    'status' => 1,
                    'info'   => '已提交更换IP申请，等待审核',
                ];

                $transaction->commit();

            } else {
                NotifyHandle::ChangeIp_Notify($service_front_config, $trade_config, $MemberPdtRes['user_id'], $notice_do_man, $MemberPdtRes['unionid'], null, null);

                if ($ServiceOrderModel->insert() && $MemberPdtRes->update(false)) {
                    $transaction->commit();
                    $arrReturn = [
                        'status' => 1,
                        'info'   => '提交成功',
                    ];
                } else {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '系统异常',
                    ];
                }
            }
            return $this->renderJSON($arrReturn);


        } else {
            $id = $this->get('id');

            $MemberPdtRes = $MemberPdtModel->find()->where(['id' => $id])->asArray()->one();
            if (empty($MemberPdtRes))
                return $this->redirect(Url::to(['index']));
            return $this->render('changeip', ['arrRes' => $MemberPdtRes]);
        }
    }

    /**
     * 业务过户
     */
    public
    function actionOldBusinessTransfer()
    {
        $MemberPdtModel  = new MemberPdt();
        $UserMemberModel = new UserMember();
        $UserAdminModel  = new UserAdmin();

        if (Yii::$app->request->post()) {
            $post         = $this->post();
            $toUserName   = $post['toUserName'];
            $toUserID     = $post['toUserID'];
            $remarks      = $post['remarks'];
            $MemberPdtRes = $MemberPdtModel->findone($post['id']);  //print_r($post);die;
            if (empty($MemberPdtRes)) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '业务不存在',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($MemberPdtRes['status'] != 1 || $MemberPdtRes['audit_status'] != 1) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '当前产品不在正常状态或未审核',
                ];
                return $this->renderJSON($arrReturn);
            }

            if ($MemberPdtRes['status'] != 1) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '业务未处于正常使用状态，不能过户',
                ];
                return $this->renderJSON($arrReturn);
            }
            $hasCheck = Yii::$app->db->createCommand("select * from notice_record where notice_unionid like '%" . $MemberPdtRes['unionid'] . "%' and notice_type = '审核' and notice_audit_time is null")->queryOne();
            if (!empty($hasCheck)) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '当前业务在通知审核中有未审核操作，不能过户',
                ];
                return $this->renderJSON($arrReturn);
            }

            $UserMemberRes = $UserMemberModel->find()->where(['u_id' => $toUserID])->asArray()->one();
            if (empty($UserMemberRes)) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '选择的用户不存在',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($UserMemberRes['admin_id'] == "" || $UserMemberRes['admin_id'] == NULL) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '选择的用户未分配客服',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($UserMemberRes['u_id'] == $MemberPdtRes->user_id) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '双方用户信息不能一样！',
                ];
                return $this->renderJSON($arrReturn);
            }

            $frontuser                     = Yii::$app->db->createCommand("select * from user_member where u_id = '" . $MemberPdtRes->user_id . "'")->queryOne();
            $frontuser['transfer_remarks'] = '';
            $afteruser                     = Yii::$app->db->createCommand("select * from user_member where u_id = '" . $UserMemberRes['u_id'] . "'")->queryOne();
            $afteruser['transfer_remarks'] = $remarks;

            $MemberPdtRes->user_id   = $UserMemberRes['u_id'];
            $MemberPdtRes->user_name = $UserMemberRes['email'];
            $MemberPdtRes->admin_id  = $UserMemberRes['admin_id'];


            #查询由后台发起的升级配置是否为审核
            $transfer_order = Yii::$app->db->createCommand("select nv_type from notice_manage where nv_key = 'transfer_order'")->queryScalar();
            $notice_do_man  = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

            #开启事务
            $transaction = Yii::$app->db->beginTransaction();
            $time        = time();

            if ($transfer_order == '审核') {
                $transaction->rollBack();
                $dataContent[] = [
                    'sql' => base64_encode("update member_pdt set user_id = '" . $UserMemberRes['u_id'] . "', user_name = '" . $UserMemberRes['email'] . "', admin_id = '" . $UserMemberRes['admin_id'] . "' where unionid = '" . $MemberPdtRes->unionid . "'"),
                ];

                $dataContent[] = [
                    'sql' => base64_encode("insert into transfer_record (unionid,front_userid,front_username,after_userid,after_username,transfer_time,transfer_remarks,operator_admin_id) 
					values('" . $MemberPdtRes->unionid . "', '" . $frontuser['u_id'] . "', '" . $frontuser['email'] . "', '" . $afteruser['u_id'] . "', '" . $afteruser['email'] . "', '" . $time . "', '" . $remarks . "', '" . $notice_do_man . "')"),
                ];
                /*
				$dataContent[] = [
					'sql' => base64_encode("insert into transfer_record values(null, '".$MemberPdtRes->unionid."', '".$frontuser['u_id']."', '".$frontuser['email']."', '".$afteruser['u_id']."', '".$afteruser['email']."', '".$time."', '".$remarks."', '".$notice_do_man."')"),
				];
				*/

                $dataJson = json_encode($dataContent, JSON_UNESCAPED_UNICODE);

                NotifyHandle::Transfer_Notify($frontuser, $afteruser, $frontuser['u_id'], $notice_do_man, $MemberPdtRes->unionid, $dataJson);

                $arrReturn = [
                    'status' => 1,
                    'info'   => '业务过户操作已提交审核，审核通过则过户完成',
                ];

            } else {
                NotifyHandle::Transfer_Notify($frontuser, $afteruser, $frontuser['u_id'], $notice_do_man, $MemberPdtRes->unionid, null);
                //记录
                $insertIntoRecord = Yii::$app->db->createCommand("insert into transfer_record (unionid,front_userid,front_username,after_userid,after_username,transfer_time,transfer_remarks,operator_admin_id) values('" . $MemberPdtRes->unionid . "', '" . $frontuser['u_id'] . "', '" . $frontuser['email'] . "', '" . $afteruser['u_id'] . "', '" . $afteruser['email'] . "', '" . $time . "', '" . $remarks . "', '" . $notice_do_man . "')")->execute();

                if ($MemberPdtRes->update(false) && $insertIntoRecord) {
                    $transaction->commit();
                    $arrReturn = [
                        'status' => 1,
                        'info'   => '过户操作成功',
                    ];
                } else {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '过户操作失败',
                    ];
                }

            }
            return $this->renderJSON($arrReturn);
        } else {
            $id           = $this->get('id');
            $MemberPdtRes = $MemberPdtModel->find()->where(['id' => $id])->asArray()->one();  ##print_r($MemberPdtRes);die;

            return $this->render('business-transfer', ['MemberPdtRes' => $MemberPdtRes]);
        }
    }

    /**
     * 更新备注
     */
    public
    function actionModifyRemarks()
    {

        Yii::$app->request->isAjax || die('error');

        $post = $this->post();//print_r($post);//die();

        $MemberPdtModel = new MemberPdt();
        $MemberPdtQuery = $MemberPdtModel->findOne($post['id']);

        $MemberPdtQuery->scenario   = 'modifyremarks';
        $MemberPdtQuery->attributes = $post;

        if (empty($MemberPdtQuery->dirtyAttributes)) {
            $arrReturn = [
                'status' => 0,
                'info'   => '备注信息未做更改',
            ];
            return $this->renderJSON($arrReturn);
        }
        #数据验证
        if (!$MemberPdtQuery->validate()) {
            $arrReturn = [
                'status' => 0,
                'info'   => $MemberPdtQuery->errors,
            ];
            return $this->renderJSON($arrReturn);
        }
        if ($MemberPdtQuery->update(false)) {
            $return = [
                'status' => 1,
                'info'   => '备注信息更新成功',
            ];
        } else {
            $return = [
                'status' => 0,
                'info'   => '备注信息未做更改',
            ];
        }
        return $this->renderJSON($return);
    }

    /**
     *  旧版 更换机器
     */
    public
    function actionOldReplace()
    {

        $PdtIpModel            = new PdtIp();
        $MemberPdtModel        = new MemberPdt();
        $PdtManageModel        = new PdtManage();
        $RoomManageModel       = new PdtRoomMange();
        $PdtCabinetManageModel = new PdtCabinetManage();
        $PdtLogModel           = new PdtLog();
        $idleModel             = new IdlePdt();
        $UserMemberModel       = new UserMember();
        $TestServerModel       = new TestServer();

        if (Yii::$app->request->post()) {
            $post   = DataHelper::dotrim($this->post());
            $id     = $post['id'];
            $testid = $post['testid'];

            $MemberPdtQueryModel = $MemberPdtModel->findOne($post['id']);
            //#获取当前的产品信息
            $MemberPdtRes = $MemberPdtModel->findOne($post['id'])->toArray();
            if (empty($MemberPdtRes)) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '未知的产品',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($MemberPdtRes['status'] != 1 || $MemberPdtRes['audit_status'] != 1) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '当前产品不在正常状态或未审核',
                ];
                return $this->renderJSON($arrReturn);
            }

            //$MemberPdtRes = $TestServerModel->find()->where(['id' => $testid])->with('idlepdt')->with('switch')->asArray()->one();

            //时间逻辑判断
            $post['start_time'] = strtotime($this->post('start_time'));
            $post['end_time']   = strtotime($this->post('end_time'));

            if ($post['start_time'] > $post['end_time']) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '服务器创建时间不能小于到期时间',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($post['pdt_id'] == "") {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '产品配置类别未选择',
                ];
                return $this->renderJSON($arrReturn);
            }
            if ($post['have_price_difference'] == 1) {
                if (!is_numeric($post['real_sell_price']) || !is_numeric($post['cost_price']) || !is_numeric($post['difference_price'])) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '请输入正确的价格',
                    ];
                    return $this->renderJSON($arrReturn);
                }
                if (!is_numeric($post['real_sell_price']) || !is_numeric($post['cost_price']) || $post['difference_price'] == '') {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '请输入正确的价格',
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }
            //将提交的IP进行验证
            if ($post['servicerprovider'] == 0) {
                if ($MemberPdtQueryModel['idle_id'] == $post['idle_id']) {
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '机器未做更换',
                    ]);
                }
                if (empty($post['have_ips'])) {
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => 'IP地址未选择或未填写',
                    ]);
                } else {
                    $ip  = DataHelper::dotrim($post['have_ips']);
                    $ip2 = $ip;
                    //考虑供应商的IP可能为IP段，所以重新定义
                    //获取详细的IP
                    foreach ($ip2 as $key => $value) {
                        if (strpos($value, '-') !== false) {
                            $value1 = explode('-', trim($value));//print_r($value1);
                            $num4   = substr(trim($value1[0]), strripos(trim($value1[0]), ".") + 1);
                            $str    = substr(trim($value1[0]), 0, strrpos(trim($value1[0]), '.'));
                            if ($num4 >= trim($value1[1])) {
                                $arrReturn = [
                                    'status' => 0,
                                    'info'   => 'IP段输入有误',
                                ];
                                return $this->renderJSON($arrReturn);
                            } else {
                                $num = trim($value1[1]) - ($num4);//echo $num;
                                for ($n = 0; $n <= $num; $n++) {
                                    $newnum = $num4 + $n;;
                                    $newip        = $str . "." . $newnum;
                                    $IPResArray[] = trim($newip);
                                }
                            }
                        } else {
                            $IPResArray[] = trim($value);
                        }
                    }
                }
                //提供商为自有时，将一些数据清空及默认选项
                $post['provider_id'] = "";


            } elseif ($post['servicerprovider'] == 1) {
                //提供商为供应商时，将一些数据清空
                $post['switch_location']   = "";
                $post['switch_port']       = "";
                $post['occupies_position'] = "";
                $post['cabinet_id']        = "";
                $post['property']          = "";

                if (empty($post['provider_ips'])) {
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => 'IP地址未选择或未填写',
                    ]);
                } else {
                    $ip  = DataHelper::dotrim($post['provider_ips']);
                    $ip2 = $ip;
                    //考虑供应商的IP可能为IP段，所以重新定义
                    //获取详细的IP
                    foreach ($ip2 as $key => $value) {
                        if (strpos($value, '-') !== false) {
                            $value1 = explode('-', trim($value));//print_r($value1);
                            $num4   = substr(trim($value1[0]), strripos(trim($value1[0]), ".") + 1);
                            $str    = substr(trim($value1[0]), 0, strrpos(trim($value1[0]), '.'));
                            if ($num4 >= trim($value1[1])) {
                                $arrReturn = [
                                    'status' => 0,
                                    'info'   => 'IP段输入有误',
                                ];
                                return $this->renderJSON($arrReturn);
                            } else {
                                $num = trim($value1[1]) - ($num4);//echo $num;
                                for ($n = 0; $n <= $num; $n++) {
                                    $newnum = $num4 + $n;;
                                    $newip        = $str . "." . $newnum;
                                    $IPResArray[] = trim($newip);
                                }
                            }
                        } else {
                            $IPResArray[] = trim($value);
                        }
                    }
                    ##
                    if (!empty($IPResArray)) {
                        foreach ($IPResArray as $value) {
                            $MemberPdtIpAll = $MemberPdtModel->find()->select('unionid,ip,ip2')->where(['like', 'ip2', '"' . $value . '"'])->andWhere('status != -1')->asArray()->all();
                            if (!empty($MemberPdtIpAll)) {
                                return $this->renderJSON([
                                    'status' => 0,
                                    'info'   => 'IP地址 :' . $value . ' 已经被使用',
                                ]);
                            }
                            if (!$testid) {
                                $TestServerIPAll = $TestServerModel->find()->select('ip,ip2')->where(['like', 'ip2', '"' . $value . '"'])->andWhere(['status' => ['0', '2']])->asArray()->all();
                            } else {
                                $TestServerIPAll = $TestServerModel->find()->select('ip,ip2')->where(['like', 'ip2', '"' . $value . '"'])->andWhere(['status' => ['0', '2']])->andWhere(['<>', 'id', $testid])->asArray()->all();
                            }

                            if (!empty($TestServerIPAll)) {
                                return $this->renderJSON([
                                    'status' => 0,
                                    'info'   => 'IP地址 :' . $value . ' 已经被使用(测试服务器)',
                                ]);
                            }
                        }
                    }
                    $PdtIpAll = $PdtIpModel->find()->where(['in', 'ip', $IPResArray])->asArray()->all();
                    if (!empty($PdtIpAll)) {
                        $PdtIpAll  = array_column($PdtIpAll, 'ip');
                        $res       = implode('，', $PdtIpAll);
                        $arrReturn = [
                            'status' => 0,
                            'info'   => 'IP地址：' . $res . '存在于IP库',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
            }
            //处理的得到的IP数组1
            $ipArray1 = array_unique($ip);//去重 去空
            $ipArray  = array_filter($ipArray1);
            //处理的得到的IP数组2
            $ipArray2 = array_unique($IPResArray);
            $ip2Array = array_filter($ipArray2);
            #//print_r($ipArray);print_r($ip2Array);die();
            //判断IP组2是否正常(再一次验证)
            if (!empty($ip2Array)) {
                foreach ($ip2Array as $value) {
                    if (!filter_var(trim($value), FILTER_VALIDATE_IP)) {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => $value . '为不合法的IP地址',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
            }

            #开启事务
            $transaction = Yii::$app->db->beginTransaction();

            #判断IP所属机房是否与产品的机房一致（仅在为自有时）
            if ($post['servicerprovider'] == 0) {
                $PdtIpAll = $PdtIpModel->find()->select('room_id')->where(['in', 'ip', $ip2Array])->asArray()->all();#print_r($PdtIpAll);die;
                foreach ($PdtIpAll as $v) {
                    $v      = join(',', $v);
                    $temp[] = $v;
                }
                $temp = array_unique($temp); #去掉重复的字符串,也就是重复的一维数组
                $temp = array_values($temp); #print_r($temp); 查询排序
                if (count($temp) > 1) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => 'IP中有属于其他机房的IP地址',
                    ];
                    return $this->renderJSON($arrReturn);
                } else if (count($temp) == 1) {
                    if ($temp[0] != $post['room_id']) {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => 'IP所属机房与产品选择机房不一致',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
            }
            if ($post['bandwidth'] == "" || $post['bandwidth'] == null) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '用户要求带宽未填写',
                ];
                return $this->renderJSON($arrReturn);
            }

            #将Ip数组装为字符串  并赋值
            $ipRes       = trim(json_encode($ipArray));
            $ip2Res      = trim(json_encode($ip2Array));//print_r($ipRes);print_r($ip2Res);die();
            $post['ip']  = $ipRes;
            $post['ip2'] = $ip2Res;

            #如果更换前也是自有的，获取之前的闲置产品库ID
            if ($MemberPdtQueryModel->servicerprovider == 0 && $MemberPdtQueryModel->idle_id != "") {
                //将之前的产品库ID也传过去
                $old_idle_id         = $MemberPdtQueryModel->idle_id;
                $post['old_idle_id'] = $old_idle_id;
            }
            #如果更换后得为自有  获取选择的自有服务器的IP
            if ($post['servicerprovider'] == 0 && $post['idle_id'] != "") {

                $checkMemberPdt = $MemberPdtModel->find()->where(['idle_id' => $post['idle_id']])->andWhere('status != -1')->asArray()->one();
                if (!empty($checkMemberPdt)) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '所选闲置产品已经在为用户使用中，此为异常情况，请立即联系技术！',
                    ];
                    return $this->renderJSON($arrReturn);
                }

                $newIdleRes = $idleModel->find()->where(['id' => $post['idle_id']])->asArray()->one();
                #产权
                $post['property'] = $newIdleRes['property'];

                $oldIP           = json_decode($newIdleRes['ip2'], true);
                $bgIPArray       = array_unique(array_merge($ip2Array, $oldIP));
                $result_bangding = array_diff($bgIPArray, $oldIP);     //需要改为使用中的
                $result_jiechu   = array_diff($bgIPArray, $ip2Array);  //需要改为闲置的
                //将有变化的IP 改为待定
                if (!empty($result_bangding)) {
                    #$res1 = $PdtIpModel->modifyIPStatus($result_bangding,"3");
                    $res1 = $PdtIpModel->updateAll(['status' => 3, 'update_time' => time()], ['ip' => $result_bangding]);

                    $iplist = "";
                    if (is_array($result_bangding)) {
                        foreach ($result_bangding as $vv) {
                            $iplist .= '\'' . $vv . '\',';
                        }
                        $iplist = substr($iplist, 0, -1);

                        $backContent[] = [
                            'sql' => base64_encode("update pdt_ip set status = '0' where ip in(" . $iplist . ")"),
                        ];

                    } else {
                        $backContent[] = [
                            'sql' => base64_encode("update pdt_ip set status = '0' where ip='" . $result_bangding . "'"),
                        ];
                    }
                }
                if (!empty($result_jiechu)) {
                    #$res2 = $PdtIpModel->modifyIPStatus($result_jiechu,"3");
                    $res2 = $PdtIpModel->updateAll(['status' => 3, 'update_time' => time()], ['ip' => $result_jiechu]);

                    $iplist = "";
                    if (is_array($result_jiechu)) {
                        foreach ($result_jiechu as $vv) {
                            $iplist .= '\'' . $vv . '\',';
                        }
                        $iplist = substr($iplist, 0, -1);

                        $backContent[] = [
                            'sql' => base64_encode("update pdt_ip set status = '1' where ip in(" . $iplist . ")"),
                        ];

                    } else {
                        $backContent[] = [
                            'sql' => base64_encode("update pdt_ip set status = '1' where ip='" . $result_jiechu . "'"),
                        ];
                    }

                }
            }

            //将更换的机器默认为已售服务器
            $post['attribute_id'] = 2;

            //获取详细配置
            $serverconfig['cpu'] = $post['cpu'];
            unset($post['cpu']);
            $serverconfig['ram'] = $post['ram'];
            unset($post['ram']);
            $serverconfig['hdd'] = $post['hdd'];
            unset($post['hdd']);
            $serverconfig['configbandwidth']       = $post['configbandwidth'];
            $serverconfig['requirement_bandwidth'] = $post['bandwidth'];
            unset($post['configbandwidth']);
            $serverconfig['ipnumber'] = $post['ipnumber'];
            unset($post['ipnumber']);
            if (!empty($post['defense'])) {
                $serverconfig['defense'] = $post['defense'];
            } else {
                $serverconfig['defense'] = "";
            }
            unset($post['defense']);
            $serverconfig['operatsystem'] = $post['operatsystem'];
            unset($post['operatsystem']);
            //将之前的价格和周期也返回到订单中
            $post['old_sell_price']    = $MemberPdtQueryModel->sell_price;
            $post['old_payment_cycle'] = $MemberPdtQueryModel->payment_cycle;
            $post['old_cost_price']    = $MemberPdtQueryModel->cost_price;
            //---------获取信息end------------//
            //假如更改机器存在差异,需要下订单，等待支付成功后， 更改机器，将更换机器的信息反馈到服务管理，等待完成
            //如果不存在差异，只提交到服务管理，然后等会处理
            //考虑到选择的闲置机器，可能会被用到，所以先把所选择的闲置改为待定
            //实例化订单模型  服务工单模型
            $tradeModel   = new Trade();
            $ServiceOrder = new ServiceOrder();
            $time         = time();
            #//获取当前产品的用户信息
            $UserRes = $UserMemberModel->findOne(['u_id' => $MemberPdtRes['user_id']])->toArray();
            #//当前的服务器配置
            $now_config                     = json_decode($MemberPdtRes['config'], true);
            $service_front_config['config'] = $now_config;
            $service_front_config['ip']     = json_decode($MemberPdtRes['ip'], true);
            $service_front_config['ip2']    = json_decode($MemberPdtRes['ip2'], true);

            $trade_config['config']                = $serverconfig;
            $trade_config['servicerprovider']      = $post['servicerprovider'];
            $trade_config['server_type_id']        = $post['server_type_id'];
            $trade_config['room_id']               = $post['room_id'];
            $trade_config['cabinet_id']            = $post['cabinet_id'];
            $trade_config['provider_id']           = $post['provider_id'];
            $trade_config['idle_id']               = $post['idle_id'];
            $trade_config['pdt_id']                = $post['pdt_id'];
            $trade_config['type_id']               = $MemberPdtQueryModel->type_id;
            $trade_config['bandwidth']             = $post['bandwidth'];
            $trade_config['real_bandwidth']        = $post['real_bandwidth'];
            $trade_config['switch_location']       = $post['switch_location'];
            $trade_config['switch_port']           = $post['switch_port'];
            $trade_config['occupies_position']     = $post['occupies_position'];
            $trade_config['start_time']            = $post['start_time'];
            $trade_config['end_time']              = $post['end_time'];
            $trade_config['ipmi_ip']               = $post['ipmi_ip'];
            $trade_config['ipmi_name']             = $post['ipmi_name'];
            $trade_config['ipmi_pwd']              = $post['ipmi_pwd'];
            $trade_config['have_price_difference'] = $post['have_price_difference'];
            $trade_config['difference_price']      = $post['difference_price'];
            $trade_config['real_sell_price']       = $post['real_sell_price'];
            $trade_config['real_cost_price']       = $post['cost_price'];
            $trade_config['sell_price']            = $post['real_sell_price'] ? $post['real_sell_price'] : $MemberPdtQueryModel->sell_price;
            $trade_config['cost_price']            = $post['cost_price'] ? $post['cost_price'] : $MemberPdtQueryModel->cost_price;
            $trade_config['currency_type']         = $post['currency_type'];
            $trade_config['upgrade_cost_price']    = "0.00";
            $trade_config['payment_cycle']         = $MemberPdtQueryModel->payment_cycle;
            $trade_config['property']              = $post['property'];
            $trade_config['ip']                    = json_decode($post['ip'], true);
            $trade_config['ip2']                   = json_decode($post['ip2'], true);
            if (isset($post['old_idle_id'])) {
                $trade_config['old_idle_id'] = $post['old_idle_id'];
            }
            $trade_config['attribute_id']           = $post['attribute_id'];
            $trade_config['old_sell_price']         = $post['old_sell_price'];
            $trade_config['old_payment_cycle']      = $post['old_payment_cycle'];
            $trade_config['old_cost_price']         = $post['old_cost_price'];
            $trade_config['old_upgrade_cost_price'] = $MemberPdtQueryModel->upgrade_cost_price;
            $trade_config['audit_status']           = $MemberPdtQueryModel->audit_status;
            $trade_config['is_auto']                = $MemberPdtQueryModel->is_auto;
            $trade_config['testid']                 = $testid;


            /*
				如果testid存在，那么就更新测试服务器状态为被占用
			*/

            if ($testid) {
                $TestServerRes          = $TestServerModel->findOne($testid);
                $TestServerRes->status  = 2;
                $UpdateTestServerStatus = $TestServerRes->update();

                //如果testid存在，那么一定会更新测试服务器状态，创建datacontent提供给审核使用
                $dataContent[] = [
                    'sql' => base64_encode("update test_server set status = '2' where id='" . $testid . "'"),
                ];

            } else {
                $UpdateTestServerStatus = true;
            }


            //当有差价的时候  当补款金额大于零时  下订单。等于零 不下订单。小于零是不下订单  但在完成时会返回款项
            if ($post['have_price_difference'] == 1) {

                $difference_price = $post['difference_price'];
                $real_sell_price  = $post['real_sell_price'];
                $cost_price       = $post['cost_price'];
                if ($difference_price == "" || $real_sell_price == "" || $cost_price == "") {
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '输入的金额有误',
                    ]);
                }
                if ($difference_price > 0) {
                    #创建订单编号
                    $trade_orderid = DataHelper::createTradeno();
                    #创建基础订单 #创建基础订单
                    $array    = [
                        'trade_orderid'        => $trade_orderid,
                        'admin_id'             => $UserRes['admin_id'],
                        'admin_name'           => $UserRes['admin_name'],
                        'member_id'            => $UserRes['u_id'],
                        'member_name'          => $UserRes['email'],
                        'unionid'              => $MemberPdtRes['unionid'],
                        'serviceid'            => "",
                        'trade_type_pay'       => "",
                        'trade_type_money'     => Yii::$app->params['trade_type']['replace_machine_rubsidy']['name'],
                        'trade_price_original' => $difference_price,
                        'trade_price_real'     => $difference_price,
                        'trade_price_payment'  => "",
                        'trade_type_do'        => "更换机器",
                        'trade_config'         => json_encode($trade_config, JSON_UNESCAPED_UNICODE),
                        'trade_time_create'    => $time,
                        'trade_remark'         => "",
                    ];
                    $resTrade = $tradeModel->InsertTradeData($array);
                    if (!$resTrade) {
                        #回滚事务
                        $transaction->rollBack();
                        return $this->renderJSON([
                            'status' => 0,
                            'info'   => '创建订单失败',
                        ]);
                    }
                    $info = "已成功创建机器更换订单，请等待用户支付";

                    $dataContent[] = [
                        'sql' => base64_encode("insert into trade (trade_orderid,member_id,member_name,admin_id,admin_name,unionid,trade_type_money,trade_price_original,trade_price_real,trade_type_do,trade_config,trade_status,trade_time_create,trade_audit) 
						values('" . $trade_orderid . "', '" . $UserRes['u_id'] . "', '" . $UserRes['email'] . "', '" . $UserRes['admin_id'] . "', '" . $UserRes['admin_name'] . "','" . $MemberPdtRes['unionid'] . "', '" . Yii::$app->params['trade_type']['replace_machine_rubsidy']['name'] . "', '" . $difference_price . "', '" . $difference_price . "', '更换机器', 
						'" . json_encode($trade_config, JSON_UNESCAPED_UNICODE) . "', '未支付', '" . $time . "', 'Y')"),
                    ];
                    /*$dataContent[] = [
						'sql' => base64_encode("insert into trade values(null, '".$trade_orderid."', '".$UserRes['u_id']."', '".$UserRes['email']."', '".$UserRes['admin_id']."', '".$UserRes['admin_name']."',
						'".$MemberPdtRes['unionid']."', '', null, '', 'consume', '".$difference_price."', '".$difference_price."', null, '更换机器',
						'".json_encode($trade_config, JSON_UNESCAPED_UNICODE)."', '未支付', '".$time."', null, '', 'Y')"),
					];*/
                    $newConfig           = $trade_config;
                    $newConfig['status'] = 1;
                } else if ($difference_price <= 0) {
                    #当补款金额为零或者负数时 不下单。通过审核后直接生成服务工单
                    $MemberPdtQueryModel->status = 3;
                    if (!$MemberPdtQueryModel->update(false)) {
                        #回滚事务
                        $transaction->rollBack();
                        return $this->renderJSON([
                            'status' => 0,
                            'info'   => '更新产品状态失败',
                        ]);
                    }
                    //生成服务ID
                    $serviceid = DataHelper::createServiceid();
                    #加入服务管理
                    $ServiceOrderModel                       = new ServiceOrder();
                    $ServiceOrderModel->serviceid            = $serviceid;
                    $ServiceOrderModel->unionid              = $MemberPdtQueryModel->unionid;
                    $ServiceOrderModel->member_id            = $UserRes['u_id'];
                    $ServiceOrderModel->member_name          = $UserRes['email'];
                    $ServiceOrderModel->admin_id             = $UserRes['admin_id'];
                    $ServiceOrderModel->admin_name           = $UserRes['admin_name'];
                    $ServiceOrderModel->service_type_do      = "更换机器";
                    $ServiceOrderModel->service_front_config = json_encode($service_front_config, JSON_UNESCAPED_UNICODE);
                    $ServiceOrderModel->service_after_config = json_encode($trade_config, JSON_UNESCAPED_UNICODE);
                    $ServiceOrderModel->service_time_start   = $time;
                    $ServiceOrderModel->service_status       = "等待确认";
                    $ServiceOrderModel->service_remark       = "";

                    $ServiceOrderResult = $ServiceOrderModel->insert();
                    if (!$ServiceOrderResult) {
                        #回滚事务
                        $transaction->rollBack();
                        return $this->renderJSON([
                            'status' => 0,
                            'info'   => '发送到服务管理失败',
                        ]);
                    }

                    $dataContent[] = [
                        'sql' => base64_encode("update member_pdt set status = '3' where unionid='" . $MemberPdtQueryModel->unionid . "'"),
                    ];

                    $dataContent[] = [
                        'sql' => base64_encode("insert into service_order (serviceid,unionid,member_id,member_name,admin_id,admin_name,service_type_do,service_front_config,service_after_config,service_time_start,service_status,service_audit)
						values('" . $serviceid . "', '" . $MemberPdtQueryModel->unionid . "', '" . $UserRes['u_id'] . "', '" . $UserRes['email'] . "', '" . $UserRes['admin_id'] . "', '" . $UserRes['admin_name'] . "', '更换机器', '" . json_encode($service_front_config, JSON_UNESCAPED_UNICODE) . "', '" . json_encode($trade_config, JSON_UNESCAPED_UNICODE) . "', '" . $time . "', '等待确认', 'Y')"),
                    ];
                    /*
					$dataContent[] = [
						'sql' => base64_encode("insert into service_order values(null, '".$serviceid."', '".$MemberPdtQueryModel->unionid."', '".$UserRes['u_id']."', '".$UserRes['email']."', '".$UserRes['admin_id']."', '".$UserRes['admin_name']."',
						'更换机器', '".json_encode($service_front_config, JSON_UNESCAPED_UNICODE)."', '".json_encode($trade_config, JSON_UNESCAPED_UNICODE)."', '".$time."', null, null, null, null, '等待确认', '', 'Y')"),
					];
					*/
                    $newConfig           = $trade_config;
                    $newConfig['status'] = 3;
                    #发送邮件
                    /*NotifyHandle::ChangeOrder_Notify($MemberPdtRes, $newConfig, $MemberPdtRes['unionid'], $UserRes['u_id'], $serviceid, "");*/
                }
            } else {
                #当没有差价的时候  直接添加到服务管理 。 同时将用户的产品状态修改
                $MemberPdtQueryModel->status = 3;
                if (!$MemberPdtQueryModel->update(false)) {
                    #回滚事务
                    $transaction->rollBack();
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '更新产品状态失败',
                    ]);
                }
                //生成服务ID
                $serviceid = DataHelper::createServiceid();
                #加入服务管理
                $ServiceOrderModel                       = new ServiceOrder();
                $ServiceOrderModel->serviceid            = $serviceid;
                $ServiceOrderModel->unionid              = $MemberPdtQueryModel->unionid;
                $ServiceOrderModel->member_id            = $UserRes['u_id'];
                $ServiceOrderModel->member_name          = $UserRes['email'];
                $ServiceOrderModel->admin_id             = $UserRes['admin_id'];
                $ServiceOrderModel->admin_name           = $UserRes['admin_name'];
                $ServiceOrderModel->service_type_do      = "更换机器";
                $ServiceOrderModel->service_front_config = json_encode($service_front_config, JSON_UNESCAPED_UNICODE);
                $ServiceOrderModel->service_after_config = json_encode($trade_config, JSON_UNESCAPED_UNICODE);
                $ServiceOrderModel->service_time_start   = $time;
                $ServiceOrderModel->service_status       = "等待确认";
                $ServiceOrderModel->service_remark       = "";

                $ServiceOrderResult = $ServiceOrderModel->insert();
                if (!$ServiceOrderResult) {
                    #回滚事务
                    $transaction->rollBack();
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '发送到服务管理失败',
                    ]);
                }

                $dataContent[] = [
                    'sql' => base64_encode("update member_pdt set status = '3' where unionid='" . $MemberPdtQueryModel->unionid . "'"),
                ];

                $dataContent[] = [
                    'sql' => base64_encode("insert into service_order (serviceid,unionid,member_id,member_name,admin_id,admin_name,service_type_do,service_front_config,service_after_config,service_time_start,service_status,service_audit)
					values('" . $serviceid . "', '" . $MemberPdtQueryModel->unionid . "', '" . $UserRes['u_id'] . "', '" . $UserRes['email'] . "', '" . $UserRes['admin_id'] . "', '" . $UserRes['admin_name'] . "', '更换机器', '" . json_encode($service_front_config, JSON_UNESCAPED_UNICODE) . "', '" . json_encode($trade_config, JSON_UNESCAPED_UNICODE) . "', '" . $time . "', '等待确认', 'Y')"),
                ];
                /*
				$dataContent[] = [
					'sql' => base64_encode("insert into service_order values(null, '".$serviceid."', '".$MemberPdtQueryModel->unionid."', '".$UserRes['u_id']."', '".$UserRes['email']."', '".$UserRes['admin_id']."', '".$UserRes['admin_name']."',
					'更换机器', '".json_encode($service_front_config, JSON_UNESCAPED_UNICODE)."', '".json_encode($trade_config, JSON_UNESCAPED_UNICODE)."', '".$time."', null, null, null, null, '等待确认', '', 'Y')"),
				];
				*/
                $newConfig           = $trade_config;
                $newConfig['status'] = 3;
                #发送邮件
                /*NotifyHandle::ChangeOrder_Notify($MemberPdtRes, $newConfig, $MemberPdtRes['unionid'], $UserRes['u_id'], $serviceid, "");*/
            }


            #//上面已经下订单或者直接写入服务管理
            #//这里将之前的产品库改为待定，选择的自有也改为待定，供应商的就先不管，等服务处理了，就更改
            //如果有信息修改，产品库也将跟着修改
            $idle_id = $this->post('idle_id');

            #查询由后台发起的更换机器是否为审核
            $change_order  = Yii::$app->db->createCommand("select nv_type from notice_manage where nv_key = 'change_order'")->queryScalar();
            $notice_do_man = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

            $UserMemberRes = Yii::$app->db->createCommand("select * from user_member where u_id = '" . $MemberPdtRes['user_id'] . "'")->queryOne();
            $hasCheck      = Yii::$app->db->createCommand("select * from notice_record where notice_user like '%" . $UserMemberRes['email'] . "%' and notice_key = 'change_order' and notice_type = '审核' and notice_audit_time is null")->queryOne();


            if ($change_order == '审核') {

                if ($hasCheck) {
                    return $this->renderJSON(['status' => 0, 'info' => '当前已有一笔待审核更换机器操作，请先处理后再行操作']);
                }

                $transaction->rollBack();

                if ($post['servicerprovider'] == 0) {
                    if ($MemberPdtQueryModel->servicerprovider == 0) {
                        $dataContent[] = [
                            'sql' => base64_encode("update idle_pdt set attribute_id='2', status='2' where id = '" . $old_idle_id . "'"),
                        ];
                    }

                    $dataContent[] = [
                        'sql' => base64_encode("update idle_pdt set attribute_id='1', status='2' where id = '" . $idle_id . "'"),
                    ];

                } elseif ($post['servicerprovider'] == 1) {
                    if ($MemberPdtQueryModel->servicerprovider == 0) {
                        $dataContent[] = [
                            'sql' => base64_encode("update idle_pdt set attribute_id='2', status='2' where id = '" . $old_idle_id . "'"),
                        ];
                    }
                }

                $dataJson = json_encode($dataContent, JSON_UNESCAPED_UNICODE);
                if (isset($dataContent)) {
                    $backJson = json_encode($backContent, JSON_UNESCAPED_UNICODE);
                    NotifyHandle::ChangeOrderDo_Notify($MemberPdtRes, $newConfig, $UserRes['u_id'], $notice_do_man, $MemberPdtRes['unionid'], $dataJson, $backJson);
                } else {
                    NotifyHandle::ChangeOrderDo_Notify($MemberPdtRes, $newConfig, $UserRes['u_id'], $notice_do_man, $MemberPdtRes['unionid'], $dataJson, null);
                }

                return $this->renderJSON([
                    'status' => 1,
                    'info'   => '已提交更换机器设备信息申请，等待审核',
                ]);

            } else {
                NotifyHandle::ChangeOrderDo_Notify($MemberPdtRes, $newConfig, $UserRes['u_id'], $notice_do_man, $MemberPdtRes['unionid'], null, null);

                //选择了新的产品库，将新选择的产品库 更改状态，并且将原来的产品库产品改为闲置
                if ($post['servicerprovider'] == 0) {
                    //修改旧的产品，这里就只修改状态待定和改为闲置服务器
                    if ($MemberPdtQueryModel->servicerprovider == 0) {
                        $oldidleRes = $idleModel->domodifystatus($old_idle_id, 2, 2);
                        $newidleRes = $idleModel->domodifystatus($idle_id, 1, 2);
                        if (!$oldidleRes || !$newidleRes) {
                            #回滚事务
                            $transaction->rollBack();
                            return $this->renderJSON([
                                'status' => 0,
                                'info'   => '修改产品库失败',
                            ]);
                        } else {
                            $transaction->commit();
                            return $this->renderJSON([
                                'status' => 1,
                                'info'   => '操作成功',
                            ]);
                        }
                    } else {
                        $newidleRes = $idleModel->domodifystatus($idle_id, 1, 2);
                        if (!$newidleRes) {
                            #回滚事务
                            $transaction->rollBack();
                            return $this->renderJSON([
                                'status' => 0,
                                'info'   => '修改产品库失败',
                            ]);
                        } else {
                            $transaction->commit();
                            return $this->renderJSON([
                                'status' => 1,
                                'info'   => '操作成功',
                            ]);
                        }
                    }
                } elseif ($post['servicerprovider'] == 1) {
                    //修改旧的产品，这里就只修改状态待定和改为闲置服务器
                    if ($MemberPdtQueryModel->servicerprovider == 0) {
                        $oldidleRes = $idleModel->domodifystatus($old_idle_id, 2, 2);
                        if (!$oldidleRes) {
                            #回滚事务
                            $transaction->rollBack();
                            return $this->renderJSON([
                                'status' => 0,
                                'info'   => '修改产品库失败',
                            ]);
                        } else {
                            $transaction->commit();
                            return $this->renderJSON([
                                'status' => 1,
                                'info'   => '操作成功',
                            ]);
                        }
                    } else {
                        $transaction->commit();
                        return $this->renderJSON([
                            'status' => 1,
                            'info'   => '操作成功',
                        ]);
                    }
                }
            }


        } else {
            #更换机器页面
            $testid       = $this->get('testid');
            $MemberPdtRes = $MemberPdtModel->findOne($this->get('id'));
            if (!$testid) {
                if (empty($MemberPdtRes)) {
                    return $this->redirect(['index']);
                }
                //当前机器的配置
                $config = json_decode($MemberPdtRes['config'], true);

                //获取机房
                $roomRes = $RoomManageModel->getListAll(['provider' => $MemberPdtRes['servicerprovider']]);

                //获取当前机房下的机柜
                $cabinetRes = $PdtCabinetManageModel->getRowByRoomId($MemberPdtRes["room_id"]);

                //获取当前机房下产品Model  根据是自有还是供应商来判别
                if ($MemberPdtRes['servicerprovider'] == 0) {
                    $PdtManageList = $PdtManageModel->find()->where(new Expression('FIND_IN_SET(:pdt_type_id, pdt_type_id)'))->addParams([':pdt_type_id' => $MemberPdtRes['server_type_id']])->asArray()->all();
                } elseif ($MemberPdtRes['servicerprovider'] == 1) {
                    $PdtManageList = $PdtManageModel->getListAll();
                }
                //获取服务器分类
                $PdtManageTypeModel = new PdtManageType();
                $PdtManageTypeRes   = $PdtManageTypeModel->find()->AsArray()->All();
                #货币类型
                $currency_typeRes = Yii::$app->params['currency_type']; #print_r($currency_type);die;
                $CostModel        = new Cost();
                $CostRes          = $CostModel->find()->where(['unionid' => $MemberPdtRes['unionid']])->asArray()->one();
                if ($CostRes) {
                    $currency_type = $CostRes['currency_type'];
                } else {
                    $currency_type = $currency_typeRes[0];
                }


                return $this->render('replace', [
                    'arrRes'           => $MemberPdtRes, 'roomRes' => $roomRes,
                    'cabinetRes'       => $cabinetRes,
                    'PdtManageList'    => $PdtManageList,
                    'config'           => $config,
                    'PdtManageTypeRes' => $PdtManageTypeRes,
                    'currency_typeRes' => $currency_typeRes,
                    'currency_type'    => $currency_type,
                    'testid'           => $testid,
                ]);


            } else {
                $TestPdtRes = $TestServerModel->find()->where(['id' => $testid])->with('idlepdt')->with('switch')->with('provider')->asArray()->one();

                $TestPdtRes['id']                 = $MemberPdtRes['id'];
                $TestPdtRes['unionid']            = $MemberPdtRes['unionid'];
                $TestPdtRes['start_time']         = $MemberPdtRes['start_time'];
                $TestPdtRes['end_time']           = $MemberPdtRes['end_time'];
                $TestPdtRes['sell_price']         = $MemberPdtRes['sell_price'];
                $TestPdtRes['cost_price']         = $MemberPdtRes['cost_price'];
                $TestPdtRes['upgrade_cost_price'] = $MemberPdtRes['upgrade_cost_price'];
                $TestPdtRes['payment_cycle']      = $MemberPdtRes['payment_cycle'];

                if (empty($TestPdtRes)) {
                    return $this->redirect(['index']);
                }

                //当前机器的配置
                $config = json_decode($TestPdtRes['config'], true);
                //获取机房
                $roomRes = $RoomManageModel->getListAll(['provider' => $TestPdtRes['servicerprovider']]);
                //获取当前机房下的机柜
                $cabinetRes = $PdtCabinetManageModel->getRowByRoomId($TestPdtRes["room_id"]);
                //获取当前机房下产品Model  根据是自有还是供应商来判别
                if ($TestPdtRes['servicerprovider'] == 0) {
                    $PdtManageList = $PdtManageModel->find()->where(new Expression('FIND_IN_SET(:pdt_type_id, pdt_type_id)'))->addParams([':pdt_type_id' => $TestPdtRes['server_type_id']])->asArray()->all();
                } elseif ($TestPdtRes['servicerprovider'] == 1) {
                    $PdtManageList = $PdtManageModel->getListAll();//print_r($PdtManageList);die();
                }
                //获取服务器分类
                $PdtManageTypeModel = new PdtManageType();
                $PdtManageTypeRes   = $PdtManageTypeModel->find()->AsArray()->All();
                #货币类型
                $currency_typeRes = Yii::$app->params['currency_type'];

                $CostModel = new Cost();
                $CostRes   = $CostModel->find()->where(['unionid' => $TestPdtRes['unionid']])->asArray()->one();
                if ($CostRes) {
                    $currency_type = $CostRes['currency_type'];
                } else {
                    $currency_type = $currency_typeRes[0];
                }


                return $this->render('replace', [
                    'arrRes'           => $TestPdtRes, 'roomRes' => $roomRes,
                    'cabinetRes'       => $cabinetRes,
                    'PdtManageList'    => $PdtManageList,
                    'config'           => $config,
                    'PdtManageTypeRes' => $PdtManageTypeRes,
                    'currency_typeRes' => $currency_typeRes,
                    'currency_type'    => $currency_type,
                    'testid'           => $testid,
                ]);

            }


        }
    }

    /**
     * 更改实际带宽
     */
    public
    function actionModifyRealbandwidth()
    {

        Yii::$app->request->isAjax || die('error');

        $post = $this->post();#print_r($post);//die();

        $IdlePdtModel   = new IdlePdt();
        $MemberPdtModel = new MemberPdt();
        $MemberPdtQuery = $MemberPdtModel->findOne($post['id']);

        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        $real_bandwidth = $post['real_bandwidth'];

        if ($real_bandwidth == "" || $real_bandwidth == null) {
            $return = [
                'status' => 0,
                'info'   => '实际带宽未填写',
            ];
            return $this->renderJSON($return);
        }

        if ($real_bandwidth == $MemberPdtQuery->real_bandwidth) {
            $return = [
                'status' => 0,
                'info'   => '更改前后的实际带宽一致',
            ];
            return $this->renderJSON($return);
        }
        if ($MemberPdtQuery->status != 1 || $MemberPdtQuery->end_time < time()) {
            $return = [
                'status' => 0,
                'info'   => '产品当前状态不允许更改实际带宽或已到期',
            ];
            return $this->renderJSON($return);
        }

        #如果用户产品为自有的。那么实际带宽就要同步到自有
        if ($MemberPdtQuery->idle_id != "" && $MemberPdtQuery->servicerprovider == 0) {
            /*#先将用户的实际配置更新
			$MemberPdtQuery->real_bandwidth = $real_bandwidth;
			$config = json_decode($MemberPdtQuery->config,true);
			$config['configbandwidth'] = $real_bandwidth;
			$MemberPdtQuery->config = json_encode($config);
			#更新自有库信息
			$IdlePdtQuery = $IdlePdtModel->findone($MemberPdtQuery->idle_id);
			$idle_config = json_decode($IdlePdtQuery->config,true);
			$idle_config['configbandwidth'] = $real_bandwidth; #实际带宽
			$idle_config['requirement_bandwidth'] = $MemberPdtQuery->bandwidth; #同时更新下要求带宽
			$IdlePdtQuery->config = json_encode($idle_config);

			$IdlePdtUpdateRes = $IdlePdtQuery->update(false);
			if( !$IdlePdtUpdateRes ) {
				$transaction->rollBack();
				$return =  [
					'status' => 0,
					'info' => '用户所属自有库产品信息同步更新失败',
				];
				return $this->renderJSON($return);
			}
			$info = '实际带宽更改成功，稍后请在交换机更改限速策略';*/

            $transaction->rollBack();
            $return = [
                'status' => 0,
                'info'   => '自有库机器请在交换机管理更改限速策略',
            ];
            return $this->renderJSON($return);

        } else {
            $MemberPdtQuery->real_bandwidth = $real_bandwidth;
            $config                         = json_decode($MemberPdtQuery->config, true);
            $config['configbandwidth']      = $real_bandwidth;

            $MemberPdtQuery->config = json_encode($config);

            $info = '实际带宽更改成功';
        }

        if ($MemberPdtQuery->update(false)) {
            $transaction->commit();
            $return = [
                'status' => 1,
                'info'   => $info,
            ];
        } else {
            $transaction->rollBack();
            $return = [
                'status' => 0,
                'info'   => '实际带宽更改失败',
            ];
        }
        return $this->renderJSON($return);
    }

    //自定义导出类目
    public
    function actionCustomExport()
    {
        $MemberPdtModel      = new MemberPdt();
        $UserMemberModel     = new UserMember();
        $MemberPdtModelQuery = $MemberPdtModel->getListAll(["admin_id" => 30], true);
        #创建搜索条件
        $MemberPdtModel->createSearchWhere($MemberPdtModelQuery, $this->get());
        $arrRes = $MemberPdtModelQuery->andWhere('status != -1')->orderBy('id asc')->asArray()->all();

        $models = [];

        foreach ($arrRes as $key => $value) {

            $models[$key]['unionid']       = $value['unionid'];
            $UserInfo                      = $UserMemberModel->find()->where(['email' => $value['user_name']])->asArray()->one();
            $models[$key]['user_account']  = $UserInfo['email'];
            $models[$key]['user_nickname'] = $UserInfo['uname'];
            $models[$key]['user_balance']  = $UserInfo['balance'];
            if (!empty($value['servertype'])) {
                $models[$key]['server_type_id'] = $value['servertype'][0]['type_name'];
            }
            if (!empty($value['pdtroom'])) {
                $models[$key]['room_id'] = $value['pdtroom'][0]['name'];
            }
            if (!empty($value['pdtcabinet'])) {
                $models[$key]['cabinet_id'] = $value['pdtcabinet'][0]['name'];
            }
            if (!empty($value['pdtmanage'])) {
                $models[$key]['pdt_id'] = $value['pdtmanage'][0]['name'];
            }

            $models[$key]['servicerprovider'] = $value['servicerprovider'] ? '供应商' : '自有';
            if (!empty(json_decode($value['config'], true))) {
                $models[$key]['config'] = implode(",  ", json_decode($value['config'], true));
            } else {
                $models[$key]['config'] = "";
            }

            $models[$key]['bandwidth']      = $value['bandwidth'];
            $models[$key]['real_bandwidth'] = $value['real_bandwidth'];

            if (!empty(json_decode($value['ip'], true))) {
                $IParr = json_decode($value['ip'], true);
                $ip    = $IParr[0];
                if (strpos($ip, '-') !== false) {
                    $IParr2             = json_decode($value['ip2'], true);
                    $models[$key]['ip'] = $IParr2[0];
                } else {

                    $models[$key]['ip'] = $ip;
                }

            } else {
                $models[$key]['ip'] = "";
            }

            $models[$key]['start_time'] = date("Y-m-d H:i:s", $value['start_time']);
            $models[$key]['end_time']   = date("Y-m-d H:i:s", $value['end_time']);
            $models[$key]['sell_price'] = $value['sell_price'];
            $models[$key]['cost_price'] = $value['cost_price'];

        }

        $url = Yii::$app->request->referrer;

        ob_end_clean(); //解决ob缓存导致导出乱码的问题

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="用户服务器表_' . date("Y-m-d H:i:s", time()) . '.xls"');
        header('Cache-Control: max-age=0');

        Excel::export([
            'models'   => $models,
            'fileName' => "用户服务器表_" . date("Y-m-d H:i:s", time()),
            'columns'  => [
                'unionid', 'user_account', 'user_nickname', 'user_balance', 'server_type_id', 'room_id', 'pdt_id', 'servicerprovider', 'config', 'bandwidth', 'real_bandwidth', 'ip',
                'start_time', 'end_time', 'sell_price', 'cost_price',
            ],
            'headers'  => [
                'unionid'          => '业务ID',
                'user_account'     => '用户账户',
                'user_nickname'    => '用户昵称',
                'user_balance'     => '用户余额',
                'room_id'          => '机房',
                'server_type_id'   => '服务器分类',
                'pdt_id'           => '产品配置类别',
                'servicerprovider' => '服务提供商',
                'config'           => '详细配置',
                'bandwidth'        => '要求带宽',
                'real_bandwidth'   => '真实带宽',
                'ip'               => 'IP地址',
                'start_time'       => '起租时间',
                'end_time'         => '到期时间',
                'sell_price'       => '续费价格',
                'cost_price'       => '成本价格',
            ],
        ]);
    }

    /**
     * 导出
     */
    public
    function actionExport()
    {

        $MemberPdtModel      = new MemberPdt();
        $FinanceManageModel  = new FinanceManage();
        $MemberPdtModelQuery = $MemberPdtModel->getListAll([], true);
        #创建搜索条件
        $MemberPdtModel->createSearchWhere($MemberPdtModelQuery, $this->get());

        $arrRes = $MemberPdtModelQuery->andWhere('status != -1')->orderBy('id desc')->asArray()->all();

        $models = [];
        foreach ($arrRes as $key => $value) {

            $models[$key]['unionid'] = $value['unionid'];
            #查询到产品最新的一次续费时间
            //$FinanceManageRes = $FinanceManageModel->find()->where(['trade_unionid' => $value['unionid'], 'trade_type' => 'machine_renewal'])->orderBy('trade_createtime desc')->asArray()->one(); #print_r($FinanceManageRes);exit;
            //if( !empty($FinanceManageRes) )
            //{
            //	$models[$key]['last_renewaltime'] = date("Y-m-d H:i:s",$FinanceManageRes['trade_createtime']);;
            //}
            $models[$key]['last_renewaltime'] = '';

            if (!empty($value['servertype'])) {
                $models[$key]['server_type_id'] = $value['servertype'][0]['type_name'];
            }
            if (!empty($value['pdtroom'])) {
                $models[$key]['room_id'] = $value['pdtroom'][0]['name'];
            }
            if (!empty($value['pdtcabinet'])) {
                $models[$key]['cabinet_id'] = $value['pdtcabinet'][0]['name'];
            }
            if (!empty($value['serverattribute'])) {
                $models[$key]['attribute_id'] = $value['serverattribute'][0]['name'];
            }
            if (!empty($value['provider'])) {
                $models[$key]['provider_id'] = $value['provider'][0]['name'];
                $models[$key]['host_name']   = $value['ipmi_ip'];
            } else {
                $models[$key]['host_name'] = "";
            }
            if (!empty($value['switch'])) {
                $models[$key]['switch_location'] = $value['switch'][0]['ip'];
            }
            if (!empty($value['pdtmanage'])) {
                $models[$key]['pdt_id'] = $value['pdtmanage'][0]['name'];
            }
            //print_r(json_decode($value['config'],true));//die();
            $models[$key]['user_name']        = $value['user_name'];
            $models[$key]['uname']            = $value['usermember'][0]['uname'];
            $models[$key]['admin_name']       = $value['useradmin'][0]['rename'];
            $models[$key]['servicerprovider'] = $value['servicerprovider'] ? '供应商提供' : '自有';
            if (!empty(json_decode($value['config'], true))) {
                $config                          = json_decode($value['config'], true);
                $models[$key]['config']          = implode(",  ", $config);
                $models[$key]['cpu']             = $config['cpu'];
                $models[$key]['ram']             = $config['ram'];
                $models[$key]['hdd']             = $config['hdd'];
                $models[$key]['configbandwidth'] = $config['configbandwidth'];
                $models[$key]['ipnumber']        = $config['ipnumber'];
                $models[$key]['defense']         = $config['defense'];
                $models[$key]['operatsystem']    = $config['operatsystem'];

            } else {
                $models[$key]['config']          = "";
                $models[$key]['cpu']             = '';
                $models[$key]['ram']             = '';
                $models[$key]['hdd']             = '';
                $models[$key]['configbandwidth'] = '';
                $models[$key]['ipnumber']        = '';
                $models[$key]['defense']         = '';
                $models[$key]['operatsystem']    = '';
            }

            $models[$key]['bandwidth']         = $value['bandwidth'];
            $models[$key]['real_bandwidth']    = $value['real_bandwidth'];
            $models[$key]['occupies_position'] = $value['occupies_position'];
            $models[$key]['switch_port']       = $value['switch_port'];

            if (!empty(json_decode($value['ip'], true))) {
                #$models[$key]['ip'] = implode(",  ", json_decode($value['ip'],true));
                $IParr = json_decode($value['ip'], true);
                $ip    = $IParr[0];
                if (strpos($ip, '-') !== false) {
                    $IParr2             = json_decode($value['ip2'], true);
                    $models[$key]['ip'] = $IParr2[0];
                } else {

                    $models[$key]['ip'] = $ip;
                }

            } else {
                $models[$key]['ip'] = "";
            }

            $models[$key]['end_time']   = date("Y-m-d H:i:s", $value['end_time']);
            $models[$key]['start_time'] = date("Y-m-d H:i:s", $value['start_time']);
            $models[$key]['sell_price'] = number_format($value['sell_price'], 2);

            $payment_cycle = '';

            switch ($value["payment_cycle"]) {
                case 1:
                    $payment_cycle = '月付';
                    break;
                case 3:
                    $payment_cycle = '季付';
                    break;
                case 6:
                    $payment_cycle = '半年付';
                    break;
                case 12:
                    $payment_cycle = '年付';
                    break;
                default:
                    $payment_cycle = $value["payment_cycle"] . '月付';
            }

            $models[$key]['payment_cycle'] = $payment_cycle;
            $models[$key]['ipmi_ip']       = ArrayHelper::getValue($value, 'ipmi_ip');
            $models[$key]['ipmi_name']     = ArrayHelper::getValue($value, 'ipmi_name');
            $models[$key]['ipmi_pwd']      = ArrayHelper::getValue($value, 'ipmi_pwd');
            $models[$key]['sn_code']       = ArrayHelper::getValue($value, 'sn_code');

        }
        #print_r($models);exit;
        $newExcel = new Spreadsheet;
        $objSheet = $newExcel->getActiveSheet(0);  //获取当前操作sheet的对象
        $objSheet->setTitle('用户服务器表');       //设置当前sheet的标题
        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        #水平方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        #垂直方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        #设置列宽
        $newExcel->getActiveSheet()->getColumnDimension('A')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('B')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('C')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('D')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('E')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('F')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('G')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('H')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('I')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('J')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('K')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('L')->setWidth(15);

        $newExcel->getActiveSheet()->getColumnDimension('M')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('N')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('O')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('P')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('Q')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('R')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('S')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('T')->setWidth(25);

        $newExcel->getActiveSheet()->getColumnDimension('U')->setWidth(18);
        $newExcel->getActiveSheet()->getColumnDimension('V')->setWidth(18);
        $newExcel->getActiveSheet()->getColumnDimension('W')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('X')->setWidth(15);

        $newExcel->getActiveSheet()->getColumnDimension('Y')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('Z')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('AA')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('AB')->setWidth(10);

        $newExcel->getActiveSheet()->getColumnDimension('AC')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('AD')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('AE')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('AF')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('AG')->setWidth(15);

        #设置列名
        $objSheet->setCellValue('A1', '业务ID');
        $objSheet->setCellValue('B1', '主机名');
        $objSheet->setCellValue('C1', '服务器分类');
        $objSheet->setCellValue('D1', '机房');
        $objSheet->setCellValue('E1', '机柜');
        $objSheet->setCellValue('F1', '状态属性');
        $objSheet->setCellValue('G1', '服务提供商');
        $objSheet->setCellValue('H1', '供应商');
        $objSheet->setCellValue('I1', '用户名');
        $objSheet->setCellValue('J1', '用户昵称');
        $objSheet->setCellValue('K1', '所属销售');
        $objSheet->setCellValue('L1', '产品配置类别');

        $objSheet->setCellValue('M1', 'CPU');
        $objSheet->setCellValue('N1', '内存大小');
        $objSheet->setCellValue('O1', '硬盘');
        $objSheet->setCellValue('P1', '带宽');
        $objSheet->setCellValue('Q1', '客户要求带宽');
        $objSheet->setCellValue('R1', 'IP数');
        $objSheet->setCellValue('S1', '防御流量');
        $objSheet->setCellValue('T1', '操作系统');

        $objSheet->setCellValue('U1', '所占机位');
        $objSheet->setCellValue('V1', '交换机');
        $objSheet->setCellValue('W1', '交换机端口');
        $objSheet->setCellValue('X1', 'IP地址');
        $objSheet->setCellValue('Y1', '开始时间');
        $objSheet->setCellValue('Z1', '到期时间');
        $objSheet->setCellValue('AA1', '上一次收款时间');
        $objSheet->setCellValue('AB1', '销售价格');
        $objSheet->setCellValue('AC1', '付费周期');
        $objSheet->setCellValue('AD1', 'IPMI地址');
        $objSheet->setCellValue('AE1', 'IPMI用户名');
        $objSheet->setCellValue('AF1', 'IPMI密码');
        $objSheet->setCellValue('AG1', 'SN码');


        $data = [];

        foreach ($models as $key => $val) {

            $k = $key + 2;

            $objSheet->setCellValue('A' . $k, $val['unionid']);
            $objSheet->setCellValue('B' . $k, $val['host_name']);
            $objSheet->setCellValue('C' . $k, $val['server_type_id']);
            $objSheet->setCellValue('D' . $k, $val['room_id']);
            $objSheet->setCellValue('E' . $k, $val['cabinet_id']);
            $objSheet->setCellValue('F' . $k, $val['attribute_id']);
            $objSheet->setCellValue('G' . $k, $val['servicerprovider']);
            $objSheet->setCellValue('H' . $k, $val['provider_id']);
            $objSheet->setCellValue('I' . $k, $val['user_name']);
            $objSheet->setCellValue('J' . $k, $val['uname']);
            $objSheet->setCellValue('K' . $k, $val['admin_name']);
            $objSheet->setCellValue('L' . $k, $val['pdt_id']);

            $objSheet->setCellValue('M' . $k, $val['cpu']);
            $objSheet->setCellValue('N' . $k, $val['ram']);
            $objSheet->setCellValue('O' . $k, $val['hdd']);
            $objSheet->setCellValue('P' . $k, $val['configbandwidth']);
            $objSheet->setCellValue('Q' . $k, $val['bandwidth']);
            $objSheet->setCellValue('R' . $k, $val['ipnumber']);
            $objSheet->setCellValue('S' . $k, $val['defense']);
            $objSheet->setCellValue('T' . $k, $val['operatsystem']);

            $objSheet->setCellValue('U' . $k, $val['occupies_position']);
            $objSheet->setCellValue('V' . $k, $val['switch_location']);
            $objSheet->setCellValue('W' . $k, $val['switch_port']);
            $objSheet->setCellValue('X' . $k, $val['ip']);
            $objSheet->setCellValue('Y' . $k, $val['start_time']);
            $objSheet->setCellValue('Z' . $k, $val['end_time']);
            $objSheet->setCellValue('AA' . $k, $val['last_renewaltime']);
            $objSheet->setCellValue('AB' . $k, $val['sell_price']);
            $objSheet->setCellValue('AC' . $k, $val['payment_cycle']);
            $objSheet->setCellValue('AD' . $k, $val['ipmi_ip']);
            $objSheet->setCellValue('AE' . $k, $val['ipmi_name']);
            $objSheet->setCellValue('AF' . $k, $val['ipmi_pwd']);
            $objSheet->setCellValue('AG' . $k, $val['sn_code']);

        }
        //设置第一栏的标题

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=用户服务器表_" . date('Y-m-d', time()) . '.' . strtolower("Xlsx"));
        header('Cache-Control: max-age=0');
        $objWriter = IOFactory::createWriter($newExcel, 'Xlsx');
        $objWriter->save('php://output');
        die();
    }


    #已过期的导出
    public
    function actionOveduelistDownload()
    {
        $MemberPdtModel      = new MemberPdt();
        $FinanceManageModel  = new FinanceManage();
        $MemberPdtModelQuery = $MemberPdtModel->getListAll([], true);
        #创建搜索条件
        $MemberPdtModel->createSearchWhere($MemberPdtModelQuery, $this->get());
        $time   = time();
        $arrRes = $MemberPdtModelQuery->andWhere('status != -1')->andwhere('end_time <' . $time)->orderBy('id desc')->asArray()->all();

        $models = [];
        foreach ($arrRes as $key => $value) {

            $models[$key]['unionid'] = $value['unionid'];
            #查询到产品最新的一次续费时间
            //$FinanceManageRes = $FinanceManageModel->find()->where(['trade_unionid' => $value['unionid'], 'trade_type' => 'machine_renewal'])->orderBy('trade_createtime desc')->asArray()->one(); #print_r($FinanceManageRes);exit;
            //if( !empty($FinanceManageRes) )
            //{
            //	$models[$key]['last_renewaltime'] = date("Y-m-d H:i:s",$FinanceManageRes['trade_createtime']);;
            //}
            $models[$key]['last_renewaltime'] = '';

            if (!empty($value['servertype'])) {
                $models[$key]['server_type_id'] = $value['servertype'][0]['type_name'];
            }
            if (!empty($value['pdtroom'])) {
                $models[$key]['room_id'] = $value['pdtroom'][0]['name'];
            }
            if (!empty($value['pdtcabinet'])) {
                $models[$key]['cabinet_id'] = $value['pdtcabinet'][0]['name'];
            }
            if (!empty($value['serverattribute'])) {
                $models[$key]['attribute_id'] = $value['serverattribute'][0]['name'];
            }
            if (!empty($value['provider'])) {
                $models[$key]['provider_id'] = $value['provider'][0]['name'];
                $models[$key]['host_name']   = $value['ipmi_ip'];
            } else {
                $models[$key]['host_name'] = "";
            }
            if (!empty($value['switch'])) {
                $models[$key]['switch_location'] = $value['switch'][0]['ip'];
            }
            if (!empty($value['pdtmanage'])) {
                $models[$key]['pdt_id'] = $value['pdtmanage'][0]['name'];
            }
            //print_r(json_decode($value['config'],true));//die();
            $models[$key]['user_name']        = $value['user_name'];
            $models[$key]['uname']            = $value['usermember'][0]['uname'];
            $models[$key]['admin_name']       = $value['useradmin'][0]['rename'];
            $models[$key]['servicerprovider'] = $value['servicerprovider'] ? '供应商提供' : '自有';
            if (!empty(json_decode($value['config'], true))) {
                $config                          = json_decode($value['config'], true);
                $models[$key]['config']          = implode(",  ", $config);
                $models[$key]['cpu']             = $config['cpu'];
                $models[$key]['ram']             = $config['ram'];
                $models[$key]['hdd']             = $config['hdd'];
                $models[$key]['configbandwidth'] = $config['configbandwidth'];
                $models[$key]['ipnumber']        = $config['ipnumber'];
                $models[$key]['defense']         = $config['defense'];
                $models[$key]['operatsystem']    = $config['operatsystem'];

            } else {
                $models[$key]['config']          = "";
                $models[$key]['cpu']             = '';
                $models[$key]['ram']             = '';
                $models[$key]['hdd']             = '';
                $models[$key]['configbandwidth'] = '';
                $models[$key]['ipnumber']        = '';
                $models[$key]['defense']         = '';
                $models[$key]['operatsystem']    = '';
            }

            $models[$key]['bandwidth']         = $value['bandwidth'];
            $models[$key]['real_bandwidth']    = $value['real_bandwidth'];
            $models[$key]['occupies_position'] = $value['occupies_position'];
            $models[$key]['switch_port']       = $value['switch_port'];

            if (!empty(json_decode($value['ip'], true))) {
                #$models[$key]['ip'] = implode(",  ", json_decode($value['ip'],true));
                $IParr = json_decode($value['ip'], true);
                $ip    = $IParr[0];
                if (strpos($ip, '-') !== false) {
                    $IParr2             = json_decode($value['ip2'], true);
                    $models[$key]['ip'] = $IParr2[0];
                } else {

                    $models[$key]['ip'] = $ip;
                }

            } else {
                $models[$key]['ip'] = "";
            }

            $models[$key]['end_time']   = date("Y-m-d H:i:s", $value['end_time']);
            $models[$key]['start_time'] = date("Y-m-d H:i:s", $value['start_time']);
            $models[$key]['sell_price'] = number_format($value['sell_price'], 2);

            $payment_cycle = '';

            switch ($value["payment_cycle"]) {
                case 1:
                    $payment_cycle = '月付';
                    break;
                case 3:
                    $payment_cycle = '季付';
                    break;
                case 6:
                    $payment_cycle = '半年付';
                    break;
                case 12:
                    $payment_cycle = '年付';
                    break;
                default:
                    $payment_cycle = $value["payment_cycle"] . '月付';
            }

            $models[$key]['payment_cycle'] = $payment_cycle;
            $models[$key]['ipmi_ip']       = $value['ipmi_ip'];
            $models[$key]['ipmi_name']     = $value['ipmi_name'];
            $models[$key]['ipmi_pwd']      = $value['ipmi_pwd'];
            $models[$key]['sn_code']       = $value['sn_code'];

        }
        #print_r($models);exit;
        $newExcel = new Spreadsheet;
        $objSheet = $newExcel->getActiveSheet(0);  //获取当前操作sheet的对象
        $objSheet->setTitle('用户服务器表');       //设置当前sheet的标题
        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        #水平方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        #垂直方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        #设置列宽
        $newExcel->getActiveSheet()->getColumnDimension('A')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('B')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('C')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('D')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('E')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('F')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('G')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('H')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('I')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('J')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('K')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('L')->setWidth(15);

        $newExcel->getActiveSheet()->getColumnDimension('M')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('N')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('O')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('P')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('Q')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('R')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('S')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('T')->setWidth(25);

        $newExcel->getActiveSheet()->getColumnDimension('U')->setWidth(18);
        $newExcel->getActiveSheet()->getColumnDimension('V')->setWidth(18);
        $newExcel->getActiveSheet()->getColumnDimension('W')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('X')->setWidth(15);

        $newExcel->getActiveSheet()->getColumnDimension('Y')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('Z')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('AA')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('AB')->setWidth(10);

        $newExcel->getActiveSheet()->getColumnDimension('AC')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('AD')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('AE')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('AF')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('AG')->setWidth(15);

        #设置列名
        $objSheet->setCellValue('A1', '业务ID');
        $objSheet->setCellValue('B1', '主机名');
        $objSheet->setCellValue('C1', '服务器分类');
        $objSheet->setCellValue('D1', '机房');
        $objSheet->setCellValue('E1', '机柜');
        $objSheet->setCellValue('F1', '状态属性');
        $objSheet->setCellValue('G1', '服务提供商');
        $objSheet->setCellValue('H1', '供应商');
        $objSheet->setCellValue('I1', '用户名');
        $objSheet->setCellValue('J1', '用户昵称');
        $objSheet->setCellValue('K1', '所属销售');
        $objSheet->setCellValue('L1', '产品配置类别');

        $objSheet->setCellValue('M1', 'CPU');
        $objSheet->setCellValue('N1', '内存大小');
        $objSheet->setCellValue('O1', '硬盘');
        $objSheet->setCellValue('P1', '带宽');
        $objSheet->setCellValue('Q1', '客户要求带宽');
        $objSheet->setCellValue('R1', 'IP数');
        $objSheet->setCellValue('S1', '防御流量');
        $objSheet->setCellValue('T1', '操作系统');

        $objSheet->setCellValue('U1', '所占机位');
        $objSheet->setCellValue('V1', '交换机');
        $objSheet->setCellValue('W1', '交换机端口');
        $objSheet->setCellValue('X1', 'IP地址');
        $objSheet->setCellValue('Y1', '开始时间');
        $objSheet->setCellValue('Z1', '到期时间');
        $objSheet->setCellValue('AA1', '上一次收款时间');
        $objSheet->setCellValue('AB1', '销售价格');
        $objSheet->setCellValue('AC1', '付费周期');
        $objSheet->setCellValue('AD1', 'IPMI地址');
        $objSheet->setCellValue('AE1', 'IPMI用户名');
        $objSheet->setCellValue('AF1', 'IPMI密码');
        $objSheet->setCellValue('AG1', 'SN码');


        $data = [];

        foreach ($models as $key => $val) {

            $k = $key + 2;

            $objSheet->setCellValue('A' . $k, $val['unionid']);
            $objSheet->setCellValue('B' . $k, $val['host_name']);
            $objSheet->setCellValue('C' . $k, $val['server_type_id']);
            $objSheet->setCellValue('D' . $k, $val['room_id']);
            $objSheet->setCellValue('E' . $k, $val['cabinet_id']);
            $objSheet->setCellValue('F' . $k, $val['attribute_id']);
            $objSheet->setCellValue('G' . $k, $val['servicerprovider']);
            $objSheet->setCellValue('H' . $k, $val['provider_id']);
            $objSheet->setCellValue('I' . $k, $val['user_name']);
            $objSheet->setCellValue('J' . $k, $val['uname']);
            $objSheet->setCellValue('K' . $k, $val['admin_name']);
            $objSheet->setCellValue('L' . $k, $val['pdt_id']);

            $objSheet->setCellValue('M' . $k, $val['cpu']);
            $objSheet->setCellValue('N' . $k, $val['ram']);
            $objSheet->setCellValue('O' . $k, $val['hdd']);
            $objSheet->setCellValue('P' . $k, $val['configbandwidth']);
            $objSheet->setCellValue('Q' . $k, $val['bandwidth']);
            $objSheet->setCellValue('R' . $k, $val['ipnumber']);
            $objSheet->setCellValue('S' . $k, $val['defense']);
            $objSheet->setCellValue('T' . $k, $val['operatsystem']);

            $objSheet->setCellValue('U' . $k, $val['occupies_position']);
            $objSheet->setCellValue('V' . $k, $val['switch_location']);
            $objSheet->setCellValue('W' . $k, $val['switch_port']);
            $objSheet->setCellValue('X' . $k, $val['ip']);
            $objSheet->setCellValue('Y' . $k, $val['start_time']);
            $objSheet->setCellValue('Z' . $k, $val['end_time']);
            $objSheet->setCellValue('AA' . $k, $val['last_renewaltime']);
            $objSheet->setCellValue('AB' . $k, $val['sell_price']);
            $objSheet->setCellValue('AC' . $k, $val['payment_cycle']);
            $objSheet->setCellValue('AD' . $k, $val['ipmi_ip']);
            $objSheet->setCellValue('AE' . $k, $val['ipmi_name']);
            $objSheet->setCellValue('AF' . $k, $val['ipmi_pwd']);
            $objSheet->setCellValue('AG' . $k, $val['sn_code']);

        }
        //设置第一栏的标题

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=用户到期服务器表_" . date('Y-m-d', time()) . '.' . strtolower("Xlsx"));
        header('Cache-Control: max-age=0');
        $objWriter = IOFactory::createWriter($newExcel, 'Xlsx');
        $objWriter->save('php://output');
    }

    /**
     * 接收选择的闲置产品，然后返回到页面
     */
    public
    function actionGetSelectidle()
    {

        Yii::$app->request->isAjax || die('error');
        //获取已经选择过的闲置产品ID
        $id = trim($this->post('id'));

        $IdlePdtModel = new IdlePdt();

        $data = $IdlePdtModel->getRowById($id);

        $config = json_decode($data['config'], true);
        $ip     = json_decode($data['ip'], true);
        if ($data['servicerprovider'] == 0) {
            $switch   = $data['switch']['0'];
            $provider = "";
        } else {
            $switch   = "";
            $provider = $data['provider']['0'];
        }
        $data['config']   = $config;
        $data['ip']       = $ip;
        $data['switch']   = $switch;
        $data['provider'] = $provider;

        $data['provider'] = $provider;
        #根据所选机器的的服务器分类   将该服务器分类下所有配置弄出
        $PdtManageModel = new PdtManage();
        $PdtManageRes   = $PdtManageModel->find()->where(new Expression('FIND_IN_SET(:pdt_type_id, pdt_type_id)'))->addParams([':pdt_type_id' => $data['server_type_id']])->asArray()->all();
        #print_r($PdtManageRes); exit;
        $data['pdtmanage'] = $PdtManageRes;
        #根据机器的机房   将该机房下机柜弄出
        $PdtCabinetManageModel = new PdtCabinetManage();
        $PdtCabinetRes         = $PdtCabinetManageModel->find()->where(['room_id' => $data['room_id']])->asArray()->all();
        $data['pdtcabinet']    = $PdtCabinetRes;

        return $this->renderJSON($data);
    }

    /**
     * 接收选择的IP，然后返回到页面
     */
    public
    function actionGetSelectip()
    {
        Yii::$app->request->isAjax || die('error');

        //获取已经选择过的IP
        $oldIp = trim($this->post('ips'));                //echo $oldIp;//die();
        $oldIp = str_replace(' ', '', $oldIp);
        $oldIp = array_filter(explode("\n", $oldIp), function ($v) {
            return !empty($v);
        });

        if (!empty($oldIp)) {
            foreach ($oldIp as $key => $value) {
                $newIp[] = $value;//将原来写的IP也写入
            }
        }
        //print_r($newIp);
        //获取刚选择的IP数据
        $iparray = json_decode($this->post('data'), true);//print_r($iparray);//die();
        if (empty($iparray)) {
            $arrReturn = [];
            return $this->renderJSON($arrReturn);
        } else {
            foreach ($iparray as $key => $value) {
                $newIp[] = $value['ip'];
            }
            $newIp = array_flip($newIp);
            $newIp = array_flip($newIp);
            //rsort($newIp);
            //print_r($newIp);
            // $strRes = implode("\r\n", $newIp);echo $strRes;
            //$strRes .= "\r\n";
            return $this->renderJSON($newIp);
        }
    }

    /**
     * 异步联动闲置的IP单独列表
     */
    public
    function actionAjaxIplist()
    {

        $IpModel      = new PdtIp();
        $IpModelQuery = $IpModel->getListAll(['status' => 0], true);
        #创建搜索条件
        $IpModel->createSearchWhere($IpModelQuery, $this->get());
        $iCount = $IpModelQuery->count();
        $oPage  = DataHelper::getPage($iCount, $this->get('limit'));
        $arrRes = $IpModelQuery->orderBy('id desc')->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();
        //获取机房列表
        $RoomManageModel = new PdtRoomMange();
        $roomRes         = $RoomManageModel->getListAll();
        #获取IP分类
        #$PdtIpClassModel = new PdtIpClass();
        #$IpClassList = $PdtIpClassModel->find()->asArray()->all();
        //return $this->renderJSON($arrRes,"",$iCount,0);
        return json_encode([
            "code"  => "0",
            "msg"   => "",
            "data"  => $arrRes,
            "count" => $iCount,
            #"IpClassList" => $IpClassList,
        ], true);
    }

    /**
     * 异步联动闲置的IP段列表
     */
    public
    function actionAjaxIplistMore()
    {
        $postData = $this->post();
        #构造sql语句in条件
        $count = trim($postData['afterip2']) - trim($postData['afterip1']) + 1;
        $insql = "";
        for ($i = trim($postData['afterip1']); $i <= trim($postData['afterip2']); $i++) {
            $insql .= "'" . trim($postData['frontip']) . "." . $i . "',";
        }
        $insql = substr($insql, 0, -1);

        $iplist = Yii::$app->db->createCommand("select * from pdt_ip where room_id = '" . $postData['room_id'] . "' and ip in (" . $insql . ")")->queryAll();
        //var_dump($insql);exit;
        $canuse = [];
        $notuse = [];
        foreach ($iplist as $k => $v) {
            if ($v['status'] == 0) {
                $canuse[] = $v['ip'];
            } else {
                $notuse[] = $v['ip'];
            }
        }
        $searchip = trim($postData['frontip']) . '.' . trim($postData['afterip1']) . '-' . trim($postData['afterip2']);
        //echo $IP;die;
        $arrReturn = [
            'status' => 1,
            'info'   => '查询完成',
            'datas'  => ['allcount' => $count, 'canuse' => $canuse, 'notuse' => $notuse, 'searchip' => $searchip],
        ];
        return $this->renderJSON($arrReturn);

    }

    /**
     * 异步联动闲置的IP段列表(新的)
     */
    public
    function actionAjaxIplistMorenew()
    {

        $PdtIpModel            = new PdtIp();
        $postData              = $this->post();
        $select_ip             = $postData['select_ip'];
        $connection['room_id'] = $postData['room_id'];

        if ($postData['class_id'] != '' && $postData['class_id'] != null && isset($postData['class_id']) && $postData['class_id']) {
            $connection['class_id'] = $postData['class_id'];
        }
        if ($postData['gateway'] != '' && $postData['gateway'] != null && isset($postData['gateway']) && $postData['gateway']) {
            $connection['gateway'] = $postData['gateway'];
        }

        #如果有填写IP段
        if ($select_ip != '' && $select_ip != null && isset($select_ip) && $select_ip) {
            #echo $select_ip;exit;
            #可能为多条IP段
            $Select_IPArray = array_filter(explode("\n", $select_ip)); #数组

            $searchip = str_replace(' ', '', implode(',', $Select_IPArray));
            #IP段地址拆分
            $return_IPArr = DataHelper::splitIP($Select_IPArray);
            if (!$return_IPArr['status']) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => $return_IPArr['info'],
                ];
                return $this->renderJSON($arrReturn);
            }
            $IPArr = $return_IPArr['data']; #print_r($IPArr);exit;
            $count = count($IPArr);
            #查询
            $iplist = $PdtIpModel->find()->where(['in', 'ip', $IPArr])->andwhere($connection)->asArray()->all();
            #print_r($connection);exit;
            #print_r($iplist);exit;
        } else {
            $iplist   = $PdtIpModel->find()->where($connection)->asArray()->all();
            $count    = count($iplist);
            $searchip = $select_ip;
        }

        $canuse = [];
        $notuse = [];
        foreach ($iplist as $k => $v) {
            if ($v['status'] == 0) {
                $canuse[] = $v['ip'];
            } else {
                $notuse[] = $v['ip'];
            }
        }

        //echo $IP;die;
        $arrReturn = [
            'status' => 1,
            'info'   => '查询完成',
            'datas'  => ['allcount' => $count, 'canuse' => $canuse, 'notuse' => $notuse, 'searchip' => $searchip],
        ];
        return $this->renderJSON($arrReturn);

    }


    /**
     * 异步联动获取配置
     */
    public
    function actionAjaxPdt()
    {
        Yii::$app->request->isAjax || die('error');

        $pdt_id = $this->post('id');                         //echo $pdt_id;die();
        if (empty($pdt_id)) {
            $arrReturn = [
                'status' => 0,
                'info'   => '未选择产品配置类别',
            ];
            return $this->renderJSON($arrReturn);
        }

        $PdtManageModel = new PdtManage();
        //获取整个的产品配置
        $PdtManageRes = $PdtManageModel->getRowById($pdt_id);//print_r($PdtManageRes);die();
        if (empty($PdtManageRes)) {
            $pdtRes = "";
            return $this->renderJSON($pdtRes);
        }
        $cpuRes       = json_decode($PdtManageRes['cpu'], true);
        $ramRes       = json_decode($PdtManageRes['ram'], true);
        $hddRes       = json_decode($PdtManageRes['hdd'], true);
        $bandwidthRes = json_decode($PdtManageRes['bandwidth'], true);
        $ipnumberRes  = json_decode($PdtManageRes['ipnumber'], true);
        $defenseRes   = json_decode($PdtManageRes['defense'], true);
        $systemRes    = json_decode($PdtManageRes['operatsystem'], true);

        $pdtRes['cpu']       = $cpuRes;
        $pdtRes['ram']       = $ramRes;
        $pdtRes['hdd']       = $hddRes;
        $pdtRes['bandwidth'] = $bandwidthRes;
        $pdtRes['ipnumber']  = $ipnumberRes;
        $pdtRes['defense']   = $defenseRes;
        $pdtRes['system']    = $systemRes;
        //print_r($pdtRes);die();
        $pdtRes['status'] = 1;
        return $this->renderJSON($pdtRes);
    }

    /**
     * ajax获取退款金额
     */
    public
    function actionGetRefundprice()
    {
        Yii::$app->request->isAjax || die('error');

        $post = $this->post();//print_r($post);//die();

        $MemberPdtModel = new MemberPdt();
        $MemberPdtRes   = $MemberPdtModel->findOne($post['id'])->toArray();

        $TradeModel = new Trade();

        $TradeRes = $TradeModel->find()->where(['trade_orderid' => $MemberPdtRes['trade_no']])->asArray()->one();
        //按照时间
        if ($post['type'] == 'date') {
            $Date_1 = $MemberPdtRes['start_time'];
            $Date_3 = $MemberPdtRes['end_time'];

            $d2 = strtotime($post['time']);

            $Days = round(($d2 - $Date_1) / 3600 / 24) + 1;//实际天数

            $Days1 = round(($Date_3 - $Date_1) / 3600 / 24);//理论使用时间

            $refund_amount = sprintf("%01.2f", $TradeRes['trade_price_payment'] / $Days1 * ($Days1 - $Days));//每天的钱*（理论使用时间 - 实际使用时间）

            $return = [
                'status' => 1,
                'info'   => [
                    'price' => $refund_amount,
                ],
            ];
        }
        return $this->renderJSON($return);
    }

    /**
     * ajax获取用户产品
     */
    public
    function actionAjaxGetmemberpdt()
    {
        Yii::$app->request->isAjax || die('error');

        $post = $this->post();//print_r($post);//die();

        $MemberPdtModel = new MemberPdt();
        $fields         = ['id', 'sell_price', 'payment_cycle', 'config', 'ip'];
        $MemberPdtRes   = $MemberPdtModel->find()->select($fields)->where(['id' => $post['id']])->asArray()->one();  #die(print_r($MemberPdtRes));

        $currency_type = Yii::$app->params['currency_type']; #print_r($currency_type);die;
        $return        = [
            'status' => 1,
            'info'   => $MemberPdtRes,
            'data'   => $currency_type,
        ];
        return $this->renderJSON($return);
    }

    /**
     *获取续费信息
     */
    public
    function actionGetrenewparams()
    {
        Yii::$app->request->isAjax || die('error');
        $renew_num = $this->post('renew_num');
        $id        = $this->post('id');

        $MemberPdtModel = new MemberPdt();

        $MemberPdtRes = $MemberPdtModel->findOne($id);

        $payment_cycle = $MemberPdtRes['payment_cycle'];

        $expire = $payment_cycle * $renew_num;
        $price  = $MemberPdtRes['sell_price'] * $renew_num;
        $unit   = "Month";

        $end_time = date('Y-m-d H:i:s', strtotime("+$expire " . $unit, $MemberPdtRes['end_time']));

        $return = [
            'status'   => 1,
            'price'    => $price,
            'end_time' => $end_time,
        ];
        return $this->renderJSON($return);
    }
}
