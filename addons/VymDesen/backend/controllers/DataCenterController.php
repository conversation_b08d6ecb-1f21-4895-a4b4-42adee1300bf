<?php

namespace addons\VymDesen\backend\controllers;

use addons\VymDesen\backend\models\PaymentAccount;
use addons\VymDesen\backend\models\RevenueNotes;
use addons\VymDesen\backend\models\RevenueReceivable;
use addons\VymDesen\backend\models\UserAdmin\UserAdmin;
use addons\VymDesen\backend\models\WorkFlow\WorkFlow;
use addons\VymDesen\backend\models\WorkFlow\WorkFlowDetail;
use addons\VymDesen\backend\controllers\BaseController;
use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\models\Finance\FinanceFundflow;
use addons\VymDesen\common\models\Finance\FinanceReport;
use addons\VymDesen\common\models\Member\MemberPdt;
use addons\VymDesen\common\models\MoneyAccountRecord;
use addons\VymDesen\common\models\Pdt\PdtManage;
use addons\VymDesen\common\models\Pdt\PdtManageType;
use addons\VymDesen\common\models\Pdt\PdtRoomMange;
use addons\VymDesen\common\models\Prepay\PrepayQueue;
use addons\VymDesen\common\models\Prepay\PrepayRecord;
use addons\VymDesen\common\models\UserMember\UserMember;
use moonland\phpexcel\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Yii;
use yii\data\Pagination;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;

/**
 * 财务信息 控制器
 *
 * <AUTHOR>
 *
 */
class DataCenterController extends BaseController
{

    #数据概览
    public function actionCollectData()
    {
        $UserMemberModel     = new UserMember;
        $PrepayQueueModel    = new PrepayQueue;
        $PrepayRecordModel   = new PrepayRecord;
        $MemberPdtModel      = new MemberPdt;
        $WorkFlowModel       = new WorkFlow;
        $WorkFlowDetailModel = new WorkFlowDetail;

        #用户数量以及评级分类
        $userCount = Yii::$app->db->createCommand("SELECT user_rating, COUNT(*) AS usernum FROM user_member WHERE STATUS = 1 AND email <> '<EMAIL>' AND user_rating IS NOT NULL AND email NOT LIKE '288%' GROUP BY user_rating")->queryAll();

        #用户实名认证数量
        $userVerifyCount = $UserMemberModel->find()->where(['status' => 1])->andwhere(['<>', 'email', '<EMAIL>'])->andwhere(['not like', 'email', '288%', false])->andwhere(['or', ['not', ['idverifyinfo' => null]], ['not', ['bankverifyinfo' => null]]])->count();

        #用户总余额
        $balanceArr       = $UserMemberModel->find()->where(['status' => 1])->andwhere(['<>', 'email', '<EMAIL>'])->andwhere(['not like', 'email', '288%', false])->select(['total_balance' => 'sum(balance)'])->asArray()->one();
        $userBalanceTotal = $balanceArr['total_balance'];

        #预收基础数据
        $prepayTotalGroup = [
            'total'         => 0,
            'payment_12'    => 0,
            'payment_6'     => 0,
            'payment_3'     => 0,
            'payment_1'     => 0,
            'payment_other' => 0,
        ];

        #未确认预收总金额
        $queueMoneyTotal           = $PrepayQueueModel->find()->where(['pq_status' => '未扣款'])->select(['totalQueue' => 'sum(pq_money_now)'])->asArray()->one();
        $prepayTotalGroup['total'] = $queueMoneyTotal['totalQueue'];

        #未确认预收分类集合

        #年付
        $sql                            = "SELECT SUM(pq_money_now) FROM prepay_queue WHERE pq_batch_id IN(SELECT pr_batch_id FROM prepay_record WHERE pr_payment_cycle = '12') AND pq_status = '未扣款'";
        $prepayTotalGroup['payment_12'] = Yii::$app->db->createCommand($sql)->queryScalar();

        #半年付
        $sql                           = "SELECT SUM(pq_money_now) FROM prepay_queue WHERE pq_batch_id IN(SELECT pr_batch_id FROM prepay_record WHERE pr_payment_cycle = '6') AND pq_status = '未扣款'";
        $prepayTotalGroup['payment_6'] = Yii::$app->db->createCommand($sql)->queryScalar();

        #季付
        $sql                           = "SELECT SUM(pq_money_now) FROM prepay_queue WHERE pq_batch_id IN(SELECT pr_batch_id FROM prepay_record WHERE pr_payment_cycle = '3') AND pq_status = '未扣款'";
        $prepayTotalGroup['payment_3'] = Yii::$app->db->createCommand($sql)->queryScalar();

        #月付
        $sql                           = "SELECT SUM(pq_money_now) FROM prepay_queue WHERE pq_batch_id IN(SELECT pr_batch_id FROM prepay_record WHERE pr_payment_cycle = '1') AND pq_status = '未扣款'";
        $prepayTotalGroup['payment_1'] = Yii::$app->db->createCommand($sql)->queryScalar();

        #其他
        $sql                               = "SELECT SUM(pq_money_now) FROM prepay_queue WHERE pq_batch_id IN(SELECT pr_batch_id FROM prepay_record WHERE pr_payment_cycle not in('1', '3', '6', '12')) AND pq_status = '未扣款'";
        $prepayTotalGroup['payment_other'] = Yii::$app->db->createCommand($sql)->queryScalar();


        #机器数量
        #正常状态机器数量
        $machineNum = $MemberPdtModel->find()->where(['not in', 'status', ['0', '-1', '-2']])->andwhere(['>', 'end_time', time()])->count();

        #已过期机器数量
        $overtimeMachineNum = $MemberPdtModel->find()->where(['not in', 'status', ['0', '-1', '-2']])->andwhere(['<=', 'end_time', time()])->count();

        return $this->render('data-center',
            [
                'userCount'          => json_encode($userCount),
                'userVerifyCount'    => $userVerifyCount,
                'userBalanceTotal'   => $userBalanceTotal,
                'prepayTotalGroup'   => $prepayTotalGroup,
                'machineNum'         => $machineNum,
                'overtimeMachineNum' => $overtimeMachineNum,
            ]);
    }

    #收支流水列表
    public function actionFinanceList()
    {
        $FinanceModel       = new FinanceReport();
        $PdtManageTypeModel = new PdtManageType();
        $PdtRoomMangeModel  = new PdtRoomMange();

        $FinanceModelQuery = $FinanceModel->find()->orderBy('id desc');
        #创建搜索条件
        $FinanceModel->createSearchWhere($FinanceModelQuery, $this->get());

        $iCount   = $FinanceModelQuery->count();//echo $iCount;
        $pageSize = 30;
        $oPage    = DataHelper::getPage($iCount, $pageSize);

        $arrRes = $FinanceModelQuery->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();

        $PdtManageRes     = $PdtManageTypeModel->find()->asArray()->all();
        $PdtRoomManageRes = $PdtRoomMangeModel->find()->asArray()->all();

        $UserAdminModel = new UserAdmin();
        #获取客服
        $adminlist_sales      = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 4])->andwhere(['status' => 1])->asArray()->all();
        $adminlist_saleleader = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 7])->andwhere(['status' => 1])->asArray()->all();
        $adminlist            = array_merge($adminlist_sales, $adminlist_saleleader);

        return $this->render('finance-list',
            [
                'arrRes'     => $arrRes,
                'pdtManage'  => $PdtManageRes,
                'roomManage' => $PdtRoomManageRes,
                'adminlist'  => $adminlist,
                'iCount'     => $iCount,
                'page'       => $oPage,
                'pageSize'   => $pageSize,
            ]);
    }

    #财务报表详细信息
    public function actionFinanceDetail()
    {
        $report_id = $this->get('report_id');
        if (!$report_id) {
            $this->redirect('/index.php?r=data-center/finance-list');
        }

        $report_detail = Yii::$app->db->createCommand("select * from finance_report where report_id = '" . $report_id . "'")->queryOne();
        $tableColumns  = Yii::$app->db->createCommand("select column_name,column_comment from information_schema.Columns where table_name='finance_report' and table_schema='jiusu'")->queryAll();

        $newdata = [];
        foreach ($report_detail as $key => $val) {
            foreach ($tableColumns as $kk => $vv) {
                if ($key == $vv['column_name']) {
                    $newdata[] = ['column' => $vv['column_name'], 'value' => $val, 'comment' => $vv['column_comment']];
                    break;
                }
            }
        }
        $timeArray = ['report_server_start_time', 'report_server_end_time', 'device_time_end'];
        return $this->render('report_detail', [
            'res'       => $newdata,
            'timeArray' => $timeArray,
        ]);
    }

    #预收款列表
    public function actionPrepayList()
    {
        $PrepayRecordModel = new PrepayRecord();

        $PrepayRecordQuery = $PrepayRecordModel->find()->orderBy('pr_id desc');

        $getData = $this->get();

        $getData['start_time'] = ArrayHelper::getValue($getData, 'start_time') ?: date("Y-m-d", time());
        $getData['end_time']   = ArrayHelper::getValue($getData, 'end_time') ?: date("Y-m-d", time() + 86400);


        #创建搜索条件
        $PrepayRecordModel->createSearchWhere($PrepayRecordQuery, $getData);

        $PrepayRecordQuery->andwhere(['<>', 'pr_payment_cycle', 1]);

        $iCount   = $PrepayRecordQuery->count();//echo $iCount;
        $pageSize = 50;
        $oPage    = DataHelper::getPage($iCount, $pageSize);

        $arrRes = $PrepayRecordQuery->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();

        return $this->render('prepay-list', [
            'arrRes'   => $arrRes,
            'iCount'   => $iCount,
            'page'     => $oPage,
            'pageSize' => $pageSize,
        ]);
    }


    #预存款详情
    public function actionPrepayDetail()
    {
        $batch_id = $this->get('batch_id');
        if (!$batch_id) {
            $this->redirect('/index.php?r=data-center/prepay-list');
        }
        $PrepayQueueModel  = new PrepayQueue();
        $PrepayRecordModel = new PrepayRecord();

        $PrepayQueueList = $PrepayQueueModel->find()->where(['pq_batch_id' => $batch_id])->asArray()->all();

        $PrepayRecordRes = $PrepayRecordModel->find()->where(['pr_batch_id' => $batch_id])->asArray()->one();
        return $this->render('prepay-detail', [
            'list'            => $PrepayQueueList,
            'PrepayRecordRes' => $PrepayRecordRes,
        ]);

    }

    #预收款列表下载
    public function actionPrepayDownload()
    {
        $PrepayRecordModel = new PrepayRecord();

        $PrepayRecordQuery = $PrepayRecordModel->find()->orderBy('pr_id desc');

        $getData = $this->get();

        $getData['start_time'] = $getData['start_time'] ? $getData['start_time'] : date("Y-m-d", time());
        $getData['end_time']   = $getData['end_time'] ? $getData['end_time'] : date("Y-m-d", time() + 86400);


        #创建搜索条件
        $PrepayRecordModel->createSearchWhere($PrepayRecordQuery, $getData);

        $arrRes = $PrepayRecordQuery->andwhere(['<>', 'pr_payment_cycle', 1])->asArray()->all();


        $newExcel = new Spreadsheet;
        $objSheet = $newExcel->getActiveSheet(0);  //获取当前操作sheet的对象
        $objSheet->setTitle('预收款明细');  //设置当前sheet的标题
        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        #水平方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        #垂直方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        #设置列宽
        $newExcel->getActiveSheet()->getColumnDimension('A')->setWidth(30);
        $newExcel->getActiveSheet()->getColumnDimension('B')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('C')->setWidth(25);
        $newExcel->getActiveSheet()->getColumnDimension('D')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('E')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('F')->setWidth(30);
        $newExcel->getActiveSheet()->getColumnDimension('G')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('H')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('I')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('J')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('K')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('L')->setWidth(10);


        #金额保留两位小数
        $newExcel->getActiveSheet()->getStyle('I')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER_00);

        #设置列名
        $objSheet->setCellValue('A1', '预收款批号');
        $objSheet->setCellValue('B1', '流程号');
        $objSheet->setCellValue('C1', '用户账户');
        $objSheet->setCellValue('D1', '用户昵称');
        $objSheet->setCellValue('E1', '业务编号');
        $objSheet->setCellValue('F1', 'IP地址');
        $objSheet->setCellValue('G1', '总期数');
        $objSheet->setCellValue('H1', '当前扣款期数');
        $objSheet->setCellValue('I1', '款项总金额');
        $objSheet->setCellValue('J1', '付费周期');
        $objSheet->setCellValue('K1', '预收款状态');
        $objSheet->setCellValue('L1', '预收款创建时间');


        $data = [];

        foreach ($arrRes as $key => $val) {

            $k = $key + 2;

            $iplist = json_decode($val['pr_ip'], true);

            $objSheet->setCellValue('A' . $k, $val['pr_batch_id']);
            $objSheet->setCellValue('B' . $k, $val['pr_flow_id']);
            $objSheet->setCellValue('C' . $k, $val['pr_user_mail']);
            $objSheet->setCellValue('D' . $k, $val['pr_user_nick']);
            $objSheet->setCellValue('E' . $k, $val['pr_unionid']);
            $objSheet->setCellValue('F' . $k, $iplist[0]);
            $objSheet->setCellValue('G' . $k, $val['pr_record_num']);
            $objSheet->setCellValue('H' . $k, $val['pr_record_now']);
            $objSheet->setCellValue('I' . $k, $val['pr_issue_money']);

            switch ($val['pr_payment_cycle']) {
                case "1":
                    $payment = '月付';
                    break;
                case "3":
                    $payment = '季付';
                    break;
                case "6":
                    $payment = '半年付';
                    break;
                case "12":
                    $payment = '年付';
                    break;
                default:
                    $payment = $val['pr_payment_cycle'] . '月付';
            }


            $objSheet->setCellValue('J' . $k, $payment);
            $objSheet->setCellValue('K' . $k, $val['pr_status']);
            $objSheet->setCellValue('L' . $k, date("Y-m-d", $val['pr_create_time']));
        }
        //设置第一栏的标题

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=预收款明细_" . date('Y-m-d', time()) . '.' . strtolower("Xlsx"));
        header('Cache-Control: max-age=0');
        $objWriter = IOFactory::createWriter($newExcel, 'Xlsx');
        $objWriter->save('php://output');
    }

    #自有库机器统计
    public function actionIdlemachineStatistics()
    {

        $queryList = $this->get();

        $room_id          = !isset($queryList['room_id']) || !$queryList['room_id'] ? "" : $queryList['room_id'];
        $server_type_id   = !isset($queryList['server_type_id']) || !$queryList['server_type_id'] ? "" : $queryList['server_type_id'];
        $servicerprovider = ArrayHelper::getValue($queryList, 'servicerprovider');
        $server_nature    = !isset($queryList['server_nature']) || !$queryList['server_nature'] ? "" : $queryList['server_nature'];


        $condition = '';
        if ($queryList && !empty($queryList)) {
            if ($room_id != '' && isset($room_id)) {
                $condition .= " AND room_id=$room_id";
            }
            if ($server_type_id != '' && isset($server_type_id)) {
                $condition .= " AND server_type_id=$server_type_id";
            }
            if ($servicerprovider != '') {
                $condition .= " AND servicerprovider='$servicerprovider'";
            }
            if ($server_nature != '' && isset($server_nature)) {
                if ($server_nature == '独立服务器') {
                    $condition .= " AND (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) not like '%站群%'";
                } elseif ($server_nature == '站群服务器') {
                    $condition .= " AND (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) like '%站群%'";
                }
            }
        } else {
            $condition = "";
        }

        $sql = "SELECT a.name as room_name,c.type_name,
		sum(case when b.servicerprovider=1 then 1  else '0' end) AS 'supplier_machines_num',
		sum(case when b.servicerprovider=0 then 1  else '0' end) AS 'idle_machines_num',
		sum(case when b.attribute_id=1 then 1  else '0' end) AS 'idle_server_num',
		sum(case when b.attribute_id=2 then 1  else '0' end) AS 'selled_server_num',
		sum(case when b.attribute_id=3 then 1  else '0' end) AS 'selfservice_server_num',
		sum(case when b.attribute_id=4 then 1  else '0' end) AS 'test_server_num',
		sum(case when b.attribute_id=5 then 1  else '0' end) AS 'fault_server_num',
		sum(case when b.status=0 then 1  else '0' end) AS 'in_idleness_nm',
		sum(case when b.status=1 then 1  else '0' end) AS 'in_use_num',
		sum(case when b.status=2 then 1  else '0' end) AS 'undetermined_num',
		sum(case when (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) like '%站群%' then 1  else '0' end) AS 'station_group_num',
		sum(case when (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) not like '%站群%' then 1  else '0' end) AS 'alone_server_num',
		sum(case when b.ip='[]' or b.ip='' or b.ip='null' or ip is null then 1 else '0' end) AS 'unboundip_num',
		count(*) AS 'machines_total'
		FROM pdt_room_manage as a LEFT JOIN idle_pdt b ON a.id = b.room_id LEFT JOIN pdt_manage_type as c ON b.server_type_id = c.type_id  where b.`status` != -1 $condition GROUP BY b.room_id,c.type_id";

        #echo $sql;exit;
        #$connection = Yii::$app->db;
        $list = Yii::$app->db->createCommand($sql)->queryAll();#print_r($list);exit;


        if (!empty($list)) {
            $total = [];

            $total['type_name']              = '总计';
            $total['supplier_machines_num']  = array_sum(array_column($list, 'supplier_machines_num'));
            $total['idle_machines_num']      = array_sum(array_column($list, 'idle_machines_num'));
            $total['idle_server_num']        = array_sum(array_column($list, 'idle_server_num'));
            $total['selled_server_num']      = array_sum(array_column($list, 'selled_server_num'));
            $total['selfservice_server_num'] = array_sum(array_column($list, 'selfservice_server_num'));
            $total['test_server_num']        = array_sum(array_column($list, 'test_server_num'));
            $total['fault_server_num']       = array_sum(array_column($list, 'fault_server_num'));
            $total['in_idleness_nm']         = array_sum(array_column($list, 'in_idleness_nm'));
            $total['in_use_num']             = array_sum(array_column($list, 'in_use_num'));
            $total['undetermined_num']       = array_sum(array_column($list, 'undetermined_num'));
            $total['station_group_num']      = array_sum(array_column($list, 'station_group_num'));
            $total['alone_server_num']       = array_sum(array_column($list, 'alone_server_num'));
            $total['unboundip_num']          = array_sum(array_column($list, 'unboundip_num'));
            $total['machines_total']         = array_sum(array_column($list, 'machines_total'));

            $list[] = $total;

        }
        foreach ($list as $key => $val) {
            $idle_rate               = $val['idle_server_num'] / $val['machines_total'] * 100;
            $list[$key]['idle_rate'] = sprintf("%01.2f", $idle_rate) . '%';
            #计算未配置IP的的机器占率
            $unboundip_rate               = $val['unboundip_num'] / $val['machines_total'] * 100;
            $list[$key]['unboundip_rate'] = sprintf("%01.2f", $unboundip_rate) . '%';
        }
        #print_r($list);exit;

        #获取机房
        $PdtRoomMangeModel = new PdtRoomMange();
        $PdtRoomList       = $PdtRoomMangeModel->find()->select('id,name')->asArray()->All();
        #服务器分类
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeList  = $PdtManageTypeModel->find()->select('type_id,type_name')->asArray()->All();

        return $this->render('idlemachine-statistics', [
            'list'              => $list,
            'PdtRoomList'       => $PdtRoomList,
            'PdtManageTypeList' => $PdtManageTypeList,
        ]);
    }

    #自有库机器情况下载
    public function actionIdleStatisticsDownload()
    {
        $queryList = $this->get();

        $room_id          = !isset($queryList['room_id']) || !$queryList['room_id'] ? "" : $queryList['room_id'];
        $server_type_id   = !isset($queryList['server_type_id']) || !$queryList['server_type_id'] ? "" : $queryList['server_type_id'];
        $servicerprovider = $queryList['servicerprovider'];
        $server_nature    = !isset($queryList['server_nature']) || !$queryList['server_nature'] ? "" : $queryList['server_nature'];


        $condition = '';
        if ($queryList && !empty($queryList)) {
            if ($room_id != '' && isset($room_id)) {
                $condition .= " AND room_id=$room_id";
            }
            if ($server_type_id != '' && isset($server_type_id)) {
                $condition .= " AND server_type_id=$server_type_id";
            }
            if ($servicerprovider != '') {
                $condition .= " AND servicerprovider='$servicerprovider'";
            }
            if ($server_nature != '' && isset($server_nature)) {
                if ($server_nature == '独立服务器') {
                    $condition .= " AND (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) not like '%站群%'";
                } elseif ($server_nature == '站群服务器') {
                    $condition .= " AND (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) like '%站群%'";
                }
            }
        } else {
            $condition = "";
        }

        $sql = "SELECT a.name as room_name,c.type_name,
		sum(case when b.servicerprovider=1 then 1  else '0' end) AS 'supplier_machines_num',
		sum(case when b.servicerprovider=0 then 1  else '0' end) AS 'idle_machines_num',
		sum(case when b.attribute_id=1 then 1  else '0' end) AS 'idle_server_num',
		sum(case when b.attribute_id=2 then 1  else '0' end) AS 'selled_server_num',
		sum(case when b.attribute_id=3 then 1  else '0' end) AS 'selfservice_server_num',
		sum(case when b.attribute_id=4 then 1  else '0' end) AS 'test_server_num',
		sum(case when b.attribute_id=5 then 1  else '0' end) AS 'fault_server_num',
		sum(case when b.status=0 then 1  else '0' end) AS 'in_idleness_nm',
		sum(case when b.status=1 then 1  else '0' end) AS 'in_use_num',
		sum(case when b.status=2 then 1  else '0' end) AS 'undetermined_num',
		sum(case when (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) like '%站群%' then 1  else '0' end) AS 'station_group_num',
		sum(case when (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) not like '%站群%' then 1  else '0' end) AS 'alone_server_num',
		sum(case when b.ip='[]' or b.ip='' or b.ip='null' or ip is null then 1 else '0' end) AS 'unboundip_num',
		count(*) AS 'machines_total'
		FROM pdt_room_manage as a LEFT JOIN idle_pdt b ON a.id = b.room_id LEFT JOIN pdt_manage_type as c ON b.server_type_id = c.type_id  where b.`status` != -1 $condition GROUP BY b.room_id,c.type_id";

        #echo $sql;exit;
        #$connection = Yii::$app->db;
        $list = Yii::$app->db->createCommand($sql)->queryAll();#print_r($list);exit;

        if (!empty($list)) {
            $total = [];

            $total['type_name']              = '总计';
            $total['supplier_machines_num']  = array_sum(array_column($list, 'supplier_machines_num'));
            $total['idle_machines_num']      = array_sum(array_column($list, 'idle_machines_num'));
            $total['idle_server_num']        = array_sum(array_column($list, 'idle_server_num'));
            $total['selled_server_num']      = array_sum(array_column($list, 'selled_server_num'));
            $total['selfservice_server_num'] = array_sum(array_column($list, 'selfservice_server_num'));
            $total['test_server_num']        = array_sum(array_column($list, 'test_server_num'));
            $total['fault_server_num']       = array_sum(array_column($list, 'fault_server_num'));
            $total['in_idleness_nm']         = array_sum(array_column($list, 'in_idleness_nm'));
            $total['in_use_num']             = array_sum(array_column($list, 'in_use_num'));
            $total['undetermined_num']       = array_sum(array_column($list, 'undetermined_num'));
            $total['station_group_num']      = array_sum(array_column($list, 'station_group_num'));
            $total['alone_server_num']       = array_sum(array_column($list, 'alone_server_num'));
            $total['machines_total']         = array_sum(array_column($list, 'machines_total'));
            $total['unboundip_num']          = array_sum(array_column($list, 'unboundip_num'));

            $list[] = $total;
        }
        foreach ($list as $key => $val) {
            #计算闲置率
            $idle_rate               = $val['idle_server_num'] / $val['machines_total'] * 100;
            $list[$key]['idle_rate'] = sprintf("%01.2f", $idle_rate) . '%';
            #计算未配置IP的的机器占率
            $unboundip_rate               = $val['unboundip_num'] / $val['machines_total'] * 100;
            $list[$key]['unboundip_rate'] = sprintf("%01.2f", $unboundip_rate) . '%';
        }

        $newExcel = new Spreadsheet;
        $objSheet = $newExcel->getActiveSheet(0);  //获取当前操作sheet的对象
        $objSheet->setTitle('自有库机器统计');  //设置当前sheet的标题
        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        #水平方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        #垂直方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        #设置列宽
        $newExcel->getActiveSheet()->getColumnDimension('A')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('B')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('C')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('D')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('E')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('F')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('G')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('H')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('I')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('J')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('K')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('L')->setWidth(12);

        $newExcel->getActiveSheet()->getColumnDimension('P')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('Q')->setWidth(15);

        #设置列名
        $objSheet->setCellValue('A1', '机 房');
        $objSheet->setCellValue('B1', '服务器分类');
        $objSheet->setCellValue('C1', '自 有');
        $objSheet->setCellValue('D1', '供应商');
        $objSheet->setCellValue('E1', '闲置服务器');
        $objSheet->setCellValue('F1', '闲置率');
        $objSheet->setCellValue('G1', '已售服务器');
        $objSheet->setCellValue('H1', '自用服务器');
        $objSheet->setCellValue('I1', '测试服务器');
        $objSheet->setCellValue('J1', '故障服务器');
        $objSheet->setCellValue('K1', '状态闲置中');
        $objSheet->setCellValue('L1', '状态使用中');
        $objSheet->setCellValue('M1', '状态待定中');
        $objSheet->setCellValue('N1', '站 群');
        $objSheet->setCellValue('O1', '独 服');
        $objSheet->setCellValue('P1', '未绑定IP机器数');
        $objSheet->setCellValue('Q1', '未绑定IP机器占比');
        $objSheet->setCellValue('R1', '机器总数');

        $data = [];

        foreach ($list as $key => $val) {
            $k = $key + 2;
            $objSheet->setCellValue('A' . $k, $val['room_name']);
            $objSheet->setCellValue('B' . $k, $val['type_name']);
            $objSheet->setCellValue('C' . $k, $val['idle_machines_num']);
            $objSheet->setCellValue('D' . $k, $val['supplier_machines_num']);
            $objSheet->setCellValue('E' . $k, $val['idle_server_num']);
            $objSheet->setCellValue('F' . $k, $val['idle_rate']);
            $objSheet->setCellValue('G' . $k, $val['selled_server_num']);
            $objSheet->setCellValue('H' . $k, $val['selfservice_server_num']);
            $objSheet->setCellValue('I' . $k, $val['test_server_num']);
            $objSheet->setCellValue('J' . $k, $val['fault_server_num']);
            $objSheet->setCellValue('K' . $k, $val['in_idleness_nm']);
            $objSheet->setCellValue('L' . $k, $val['in_use_num']);
            $objSheet->setCellValue('M' . $k, $val['undetermined_num']);
            $objSheet->setCellValue('N' . $k, $val['station_group_num']);
            $objSheet->setCellValue('O' . $k, $val['alone_server_num']);
            $objSheet->setCellValue('P' . $k, $val['unboundip_num']);
            $objSheet->setCellValue('Q' . $k, $val['unboundip_rate']);
            $objSheet->setCellValue('R' . $k, $val['machines_total']);
        }
        //设置第一栏的标题

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=自有库机器统计" . date('Y-m-d', time()) . '.' . strtolower("Xlsx"));
        header('Cache-Control: max-age=0');
        $objWriter = IOFactory::createWriter($newExcel, 'Xlsx');
        $objWriter->save('php://output');

    }

    #售出机器统计
    public function actionSellingMachineStatistics()
    {

        $queryList = $this->get();

        $room_id        = !isset($queryList['room_id']) || !$queryList['room_id'] ? "" : $queryList['room_id'];
        $server_type_id = !isset($queryList['server_type_id']) || !$queryList['server_type_id'] ? "" : $queryList['server_type_id'];
        #$servicerprovider = !isset($queryList['servicerprovider']) || !$queryList['servicerprovider'] ? "" : $queryList['servicerprovider'];
        $servicerprovider = ArrayHelper::getValue($queryList, 'servicerprovider');
        $server_nature    = !isset($queryList['server_nature']) || !$queryList['server_nature'] ? "" : $queryList['server_nature'];

        $condition = '';
        if ($queryList && !empty($queryList)) {

            if ($room_id != '' && isset($room_id)) {
                $condition .= " AND room_id=$room_id";
            }
            if ($server_type_id != '' && isset($server_type_id)) {
                $condition .= " AND server_type_id=$server_type_id";
            }
            if ($servicerprovider != '') {
                $condition .= " AND servicerprovider='$servicerprovider'";
            }
            if ($server_nature != '' && isset($server_nature)) {
                if ($server_nature == '独立服务器') {
                    $condition .= " AND (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) not like '%站群%'";
                } elseif ($server_nature == '站群服务器') {
                    $condition .= " AND (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) like '%站群%'";
                }
            }
        } else {
            $condition = "";
        }

        $sql = "SELECT b.room_id,a.name as room_name,c.type_name,
		sum(case when b.servicerprovider=1 then 1  else '0' end) AS 'supplier_machines_num',
		sum(case when b.servicerprovider=0 then 1  else '0' end) AS 'idle_machines_num',
		sum(case when b.attribute_id=1 then 1  else '0' end) AS 'idle_server_num',
		sum(case when b.attribute_id=2 then 1  else '0' end) AS 'selled_server_num',
		sum(case when b.attribute_id=3 then 1  else '0' end) AS 'selfservice_server_num',
		sum(case when b.attribute_id=4 then 1  else '0' end) AS 'test_server_num',
		sum(case when b.attribute_id=5 then 1  else '0' end) AS 'fault_server_num',
		sum(case when (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) like '%站群%' then 1  else '0' end) AS 'station_group_num',
		sum(case when (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) not like '%站群%' then 1  else '0' end) AS 'alone_server_num',
		sum(case when (b.end_time  < unix_timestamp(now()) + 86400*3 ) and ( b.end_time > unix_timestamp(now()) ) then 1  else '0' end) AS 'now_total_end_machines',
		sum(case when (b.end_time  < unix_timestamp(now()) )  then 1  else '0' end) AS 'now_total_ovedue_machines',
		count(*) AS 'machines_total'
		FROM pdt_room_manage as a LEFT JOIN member_pdt b ON a.id = b.room_id LEFT JOIN pdt_manage_type as c ON b.server_type_id = c.type_id  where b.`status` != -1 $condition GROUP BY a.id,c.type_id";

        #echo $sql;exit;
        #$connection = Yii::$app->db;
        $list = Yii::$app->db->createCommand($sql)->queryAll();#print_r($list);exit;

        if (!empty($list)) {
            $total = [];

            $total['type_name']              = '总计';
            $total['supplier_machines_num']  = array_sum(array_column($list, 'supplier_machines_num'));
            $total['idle_machines_num']      = array_sum(array_column($list, 'idle_machines_num'));
            $total['idle_server_num']        = array_sum(array_column($list, 'idle_server_num'));
            $total['selled_server_num']      = array_sum(array_column($list, 'selled_server_num'));
            $total['selfservice_server_num'] = array_sum(array_column($list, 'selfservice_server_num'));
            $total['test_server_num']        = array_sum(array_column($list, 'test_server_num'));
            $total['fault_server_num']       = array_sum(array_column($list, 'fault_server_num'));
            $total['in_idleness_nm']         = array_sum(array_column($list, 'in_idleness_nm'));
            $total['in_use_num']             = array_sum(array_column($list, 'in_use_num'));
            $total['undetermined_num']       = array_sum(array_column($list, 'undetermined_num'));
            $total['station_group_num']      = array_sum(array_column($list, 'station_group_num'));
            $total['alone_server_num']       = array_sum(array_column($list, 'alone_server_num'));

            $total['now_total_end_machines']    = array_sum(array_column($list, 'now_total_end_machines'));
            $total['now_total_ovedue_machines'] = array_sum(array_column($list, 'now_total_ovedue_machines'));

            $total['machines_total'] = array_sum(array_column($list, 'machines_total'));

            $list[] = $total;

        }

        #获取机房
        $PdtRoomMangeModel = new PdtRoomMange();
        $PdtRoomList       = $PdtRoomMangeModel->find()->select('id,name')->asArray()->All();
        #服务器分类
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeList  = $PdtManageTypeModel->find()->select('type_id,type_name')->asArray()->All();

        return $this->render('selling-machine-statistics', [
            'list'              => $list,
            'PdtRoomList'       => $PdtRoomList,
            'PdtManageTypeList' => $PdtManageTypeList,
        ]);
    }

    public function actionSellingStatisticsDownload()
    {

        $queryList = $this->get();

        $room_id        = !isset($queryList['room_id']) || !$queryList['room_id'] ? "" : $queryList['room_id'];
        $server_type_id = !isset($queryList['server_type_id']) || !$queryList['server_type_id'] ? "" : $queryList['server_type_id'];
        #$servicerprovider = !isset($queryList['servicerprovider']) || !$queryList['servicerprovider'] ? "" : $queryList['servicerprovider'];
        $servicerprovider = $queryList['servicerprovider'];
        $server_nature    = !isset($queryList['server_nature']) || !$queryList['server_nature'] ? "" : $queryList['server_nature'];

        $condition = '';
        if ($queryList && !empty($queryList)) {

            if ($room_id != '' && isset($room_id)) {
                $condition .= " AND room_id=$room_id";
            }
            if ($server_type_id != '' && isset($server_type_id)) {
                $condition .= " AND server_type_id=$server_type_id";
            }
            if ($servicerprovider != '') {
                $condition .= " AND servicerprovider='$servicerprovider'";
            }
            if ($server_nature != '' && isset($server_nature)) {
                if ($server_nature == '独立服务器') {
                    $condition .= " AND (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) not like '%站群%'";
                } elseif ($server_nature == '站群服务器') {
                    $condition .= " AND (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) like '%站群%'";
                }
            }
        } else {
            $condition = "";
        }

        $sql = "SELECT b.room_id,a.name as room_name,c.type_name,
		sum(case when b.servicerprovider=1 then 1  else '0' end) AS 'supplier_machines_num',
		sum(case when b.servicerprovider=0 then 1  else '0' end) AS 'idle_machines_num',
		sum(case when b.attribute_id=1 then 1  else '0' end) AS 'idle_server_num',
		sum(case when b.attribute_id=2 then 1  else '0' end) AS 'selled_server_num',
		sum(case when b.attribute_id=3 then 1  else '0' end) AS 'selfservice_server_num',
		sum(case when b.attribute_id=4 then 1  else '0' end) AS 'test_server_num',
		sum(case when b.attribute_id=5 then 1  else '0' end) AS 'fault_server_num',
		sum(case when (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) like '%站群%' then 1  else '0' end) AS 'station_group_num',
		sum(case when (SELECT type_name from pdt_manage_type where type_id = b.server_type_id) not like '%站群%' then 1  else '0' end) AS 'alone_server_num',
		sum(case when (b.end_time  < unix_timestamp(now()) + 86400*3 ) and ( b.end_time > unix_timestamp(now()) ) then 1  else '0' end) AS 'now_total_end_machines',
		sum(case when (b.end_time  < unix_timestamp(now()) )  then 1  else '0' end) AS 'now_total_ovedue_machines',
		count(*) AS 'machines_total'
		FROM pdt_room_manage as a LEFT JOIN member_pdt b ON a.id = b.room_id LEFT JOIN pdt_manage_type as c ON b.server_type_id = c.type_id  where b.`status` != -1 $condition GROUP BY a.id,c.type_id";

        #echo $sql;exit;
        #$connection = Yii::$app->db;
        $list = Yii::$app->db->createCommand($sql)->queryAll();#print_r($list);exit;

        if (!empty($list)) {
            $total = [];

            $total['type_name']              = '总计';
            $total['supplier_machines_num']  = array_sum(array_column($list, 'supplier_machines_num'));
            $total['idle_machines_num']      = array_sum(array_column($list, 'idle_machines_num'));
            $total['idle_server_num']        = array_sum(array_column($list, 'idle_server_num'));
            $total['selled_server_num']      = array_sum(array_column($list, 'selled_server_num'));
            $total['selfservice_server_num'] = array_sum(array_column($list, 'selfservice_server_num'));
            $total['test_server_num']        = array_sum(array_column($list, 'test_server_num'));
            $total['fault_server_num']       = array_sum(array_column($list, 'fault_server_num'));
            $total['in_idleness_nm']         = array_sum(array_column($list, 'in_idleness_nm'));
            $total['in_use_num']             = array_sum(array_column($list, 'in_use_num'));
            $total['undetermined_num']       = array_sum(array_column($list, 'undetermined_num'));
            $total['station_group_num']      = array_sum(array_column($list, 'station_group_num'));
            $total['alone_server_num']       = array_sum(array_column($list, 'alone_server_num'));

            $total['now_total_end_machines']    = array_sum(array_column($list, 'now_total_end_machines'));
            $total['now_total_ovedue_machines'] = array_sum(array_column($list, 'now_total_ovedue_machines'));

            $total['machines_total'] = array_sum(array_column($list, 'machines_total'));

            $list[] = $total;
        }
        $newExcel = new Spreadsheet;
        $objSheet = $newExcel->getActiveSheet(0);  //获取当前操作sheet的对象
        $objSheet->setTitle('所有业务机器统计');  //设置当前sheet的标题
        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        #水平方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        #垂直方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        #设置列宽
        $newExcel->getActiveSheet()->getColumnDimension('A')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('B')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('C')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('D')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('E')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('F')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('G')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('H')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('I')->setWidth(12);

        #设置列名
        $objSheet->setCellValue('A1', '机 房');
        $objSheet->setCellValue('B1', '服务器分类');
        $objSheet->setCellValue('C1', '自 有');
        $objSheet->setCellValue('D1', '供应商');
        $objSheet->setCellValue('E1', '站 群');
        $objSheet->setCellValue('F1', '独 服');
        $objSheet->setCellValue('G1', '即将到期');
        $objSheet->setCellValue('H1', '已到期');
        $objSheet->setCellValue('I1', '总 数');

        $data = [];

        foreach ($list as $key => $val) {
            $k = $key + 2;
            $objSheet->setCellValue('A' . $k, $val['room_name']);
            $objSheet->setCellValue('B' . $k, $val['type_name']);
            $objSheet->setCellValue('C' . $k, $val['idle_machines_num']);
            $objSheet->setCellValue('D' . $k, $val['supplier_machines_num']);
            $objSheet->setCellValue('E' . $k, $val['station_group_num']);
            $objSheet->setCellValue('F' . $k, $val['alone_server_num']);
            $objSheet->setCellValue('G' . $k, $val['now_total_end_machines']);
            $objSheet->setCellValue('H' . $k, $val['now_total_ovedue_machines']);
            $objSheet->setCellValue('I' . $k, $val['machines_total']);
        }
        //设置第一栏的标题

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=业务机器统计" . date('Y-m-d', time()) . '.' . strtolower("Xlsx"));
        header('Cache-Control: max-age=0');
        $objWriter = IOFactory::createWriter($newExcel, 'Xlsx');
        $objWriter->save('php://output');
    }

    #用户信息列表
    public function actionUserinfoCenter()
    {

        $queryList = $this->get();

        $admin_id                       = ArrayHelper::getValue($queryList, 'admin_id');
        $useremail                      = trim(ArrayHelper::getValue($queryList, 'useremail'));
        $uname                          = trim(ArrayHelper::getValue($queryList, 'uname'));
        $machine_number_sorting         = trim(ArrayHelper::getValue($queryList, 'machine_number_sorting'));
        $expired_machine_number_sorting = trim(ArrayHelper::getValue($queryList, 'expired_machine_number_sorting'));
        $last_purchase_time_sorting     = trim(ArrayHelper::getValue($queryList, 'last_purchase_time_sorting'));

        $condition  = '';
        $sortString = '';

        if ($queryList && !empty($queryList)) {

            if ($admin_id != '' && isset($admin_id)) {
                $condition .= " AND usermember.admin_id = $admin_id";
            }
            if ($useremail != '' && isset($useremail)) {
                $condition .= " AND usermember.email like '%$useremail%'";
            }
            if ($uname != '' && isset($uname)) {
                $condition .= " AND usermember.uname like '%$uname%'";
            }
            #用户机器数排序
            if ($machine_number_sorting != '' && isset($machine_number_sorting)) {
                if ($machine_number_sorting == 'desc') {
                    $sortString .= "now_total_machines desc";
                } else if ($machine_number_sorting == 'asc') {
                    $sortString .= "now_total_machines asc";
                }
            }
            #用户已到期机器排序
            if ($expired_machine_number_sorting != '' && isset($expired_machine_number_sorting)) {
                if ($expired_machine_number_sorting == 'desc') {
                    if ($sortString == '') {
                        $sortString .= "now_total_ovedue_machines desc";
                    } else {
                        $sortString .= ", now_total_ovedue_machines desc";
                    }
                }
            }
            #用户上次开机时间排序
            if ($last_purchase_time_sorting != '' && isset($last_purchase_time_sorting)) {
                if ($last_purchase_time_sorting == 'desc') {
                    if ($sortString == '') {
                        $sortString .= "last_purchase_time desc";
                    } else {
                        $sortString .= ", last_purchase_time desc";
                    }
                } else if ($last_purchase_time_sorting == 'asc') {
                    if ($sortString == '') {
                        $sortString .= "last_purchase_time asc";
                    } else {
                        $sortString .= ", last_purchase_time asc";
                    }
                }
            }

            if ($sortString == '') {
                $sortString .= "usermember.u_id desc";
            } else {
                $sortString .= ", usermember.u_id desc";
            }

        } else {
            $condition = "";
            $sortString .= "usermember.u_id desc";
        }

        $connection = Yii::$app->db;
        $sql        = "SELECT usermember.u_id,usermember.email,usermember.uname,usermember.truename,usermember.admin_name,
		max(pdt.start_time) as last_purchase_time,
		sum(case when pdt.status != -1 then 1  else '0' end) AS 'now_total_machines',
		sum(case when (pdt.end_time  < unix_timestamp(now()) + 86400*3 ) and ( pdt.end_time > unix_timestamp(now()) ) and pdt.`status` != -1 then 1  else '0' end) AS 'now_total_end_machines',
		sum(case when (pdt.end_time  < unix_timestamp(now()) ) and pdt.`status` != -1 then 1  else '0' end) AS 'now_total_ovedue_machines',
		sum(case when pdt.status = -1 then 1  else '0' end) AS 'total_historiy_machines'
		FROM user_member as usermember LEFT JOIN member_pdt as pdt ON usermember.u_id = pdt.user_id where pdt.status != -1 $condition  GROUP BY usermember.u_id ORDER BY $sortString";

        #echo $sql;exit;

        $command  = $connection->createCommand($sql)->queryAll();
        $iCount   = count($command);
        $pageSize = 50;
        $pages    = new Pagination(['totalCount' => $iCount, 'pageSize' => $pageSize]);

        $list = $connection->createCommand($sql . " limit " . $pages->limit . " offset " . $pages->offset . "")->queryAll();

        /*$dataprovider = new ArrayDataProvider([
            'allModels' => $list,
        ]);*/

        $UserAdminModel = new UserAdmin();
        #获取客服
        $adminlist_sales      = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 4])->andwhere(['status' => 1])->asArray()->all();
        $adminlist_saleleader = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 7])->andwhere(['status' => 1])->asArray()->all();
        $adminlist            = array_merge($adminlist_sales, $adminlist_saleleader);

        return $this->render('user-info-center', [
            'list'      => $list,
            'iCount'    => $iCount,
            'pageSize'  => $pageSize,
            'page'      => $pages,
            'adminlist' => $adminlist,
        ]);
    }

    #用户信息详情
    public function actionUserinfoItem()
    {

        $UserAdminModel  = new UserAdmin();
        $UserMemberModel = new UserMember();

        $id = $this->get('userid');
        $id = intval($id);

        $UserMemberRes = $UserMemberModel->find()->where(['u_id' => $id])->asArray()->one();
        if (empty($UserMemberRes))
            return $this->redirect(Url::to(['userinfo-center']));

        #获取用户机器信息
        $sql = "SELECT managetype.type_name,managetype.type_id as server_type_id,room.name as room_name,
		sum(case when pdt.servicerprovider=1 then 1  else '0' end) AS 'supplier_machines_num',
		sum(case when pdt.servicerprovider=0 then 1  else '0' end) AS 'idle_machines_num',
		sum(case when pdt.attribute_id=1 then 1  else '0' end) AS 'idle_server_num',
		sum(case when pdt.attribute_id=2 then 1  else '0' end) AS 'selled_server_num',
		sum(case when pdt.attribute_id=3 then 1  else '0' end) AS 'selfservice_server_num',
		sum(case when pdt.attribute_id=4 then 1  else '0' end) AS 'test_server_num',
		sum(case when pdt.attribute_id=5 then 1  else '0' end) AS 'fault_server_num',
		sum(case when (SELECT type_name from pdt_manage_type where type_id = pdt.server_type_id) like '%站群%' then 1  else '0' end) AS 'station_group_num',
		sum(case when (SELECT type_name from pdt_manage_type where type_id = pdt.server_type_id) not like '%站群%' then 1  else '0' end) AS 'alone_server_num',
		sum(case when (pdt.end_time  < unix_timestamp(now()) + 86400*3 ) and ( pdt.end_time > unix_timestamp(now()) ) then 1  else '0' end) AS 'now_total_end_machines',
		sum(case when (pdt.end_time  < unix_timestamp(now()) )  then 1  else '0' end) AS 'now_total_ovedue_machines',
		sum(case when (pdt.end_time  < unix_timestamp(now()) )  then pdt.sell_price  else '0' end) AS 'renewal_due_machines_total_amount',
		count(*) AS 'machines_total'
		FROM member_pdt as pdt LEFT JOIN pdt_manage_type as managetype ON pdt.server_type_id = managetype.type_id  LEFT JOIN pdt_room_manage as room ON room.id = pdt.room_id
		where pdt.`status` != -1 and pdt.user_id = $id GROUP BY managetype.type_id,room.id";

        #echo $sql;exit;
        $pdtlist = Yii::$app->db->createCommand($sql)->queryAll(); #print_r($list);exit;

        #获取用户订单
        $sql2 = "SELECT
		sum(case when trade_type = 'machine_purchase' then 1  else '0' end) AS 'purchase_num',
		sum(case when trade_type = 'machine_purchase' then trade_price_total  else '0' end) AS 'purchase_total',
		sum(case when trade_type = 'machine_purchase' then trade_pay_money  else '0' end) AS 'purchase_pay_total',
		sum(case when trade_type = 'machine_renewal' then 1  else '0' end) AS 'renewal_num',
		sum(case when trade_type = 'machine_renewal' then trade_price_total  else '0' end) AS 'renewal_total',
		sum(case when trade_type = 'machine_renewal' then trade_pay_money  else '0' end) AS 'renewal_pay_total',
		sum(case when trade_type = 'change_config_rubsidy' then 1  else '0' end) AS 'changeconfig_num',
		sum(case when trade_type = 'change_config_rubsidy' then trade_price_total  else '0' end) AS 'changeconfig_total',
		sum(case when trade_type = 'change_config_rubsidy' then trade_pay_money  else '0' end) AS 'changeconfig_pay_total',
		sum(case when trade_type = 'replace_machine_rubsidy' then 1  else '0' end) AS 'replacemachine_num',
		sum(case when trade_type = 'replace_machine_rubsidy' then trade_price_total  else '0' end) AS 'replacemachine_total',
		sum(case when trade_type = 'replace_machine_rubsidy' then trade_pay_money  else '0' end) AS 'replacemachine_pay_total'
		FROM trade_main as trade  where user_id = $id and trade_status = '已支付'";

        $tradelist = Yii::$app->db->createCommand($sql2)->queryOne(); #print_r($tradelist);exit;

        #获取用户流水
        $email = $UserMemberRes['email'];

        $sql3 = "SELECT report_trade_type,
		sum(case when report_trade_pay_type = '余额支付' then 1  else '0' end) AS 'balance_num',
		sum(case when report_trade_pay_type = '余额支付' then report_pay_fact  else '0' end) AS 'balance_total',
		sum(case when report_trade_pay_type = '在线支付' then 1  else '0' end) AS 'online_num',
		sum(case when report_trade_pay_type = '在线支付' then report_pay_fact  else '0' end) AS 'online_total',
		sum(case when report_trade_pay_type = '线下打款' then 1  else '0' end) AS 'underline_num',
		sum(case when report_trade_pay_type = '线下打款' then report_pay_fact  else '0' end) AS 'underline_total' 
		from finance_report as report where report_user_email = '$email' GROUP BY report_trade_type";

        $financelist = Yii::$app->db->createCommand($sql3)->queryAll(); #print_r($financelist);exit;

        #用户预付款统计
        $sql4 = "SELECT a.pr_batch_id, a.pr_unionid,a.pr_issue_money,a.pr_record_now,a.pr_record_num,
		sum(case when b.pq_status = '已扣款' then b.pq_money_now  else 0 end) AS 'deducted_amount', 
		sum(case when b.pq_status = '未扣款' then b.pq_money_now  else 0 end) AS 'not_deducted_amount',
		a.pr_status
		FROM prepay_record as a LEFT JOIN prepay_queue b on a.pr_batch_id = b.pq_batch_id where a.pr_user_mail = '$email' AND a.pr_status = '未完成'
		GROUP BY a.pr_batch_id,a.pr_unionid,a.pr_issue_money,a.pr_record_now,a.pr_record_num,a.pr_status";

        $prepaylist = Yii::$app->db->createCommand($sql4)->queryAll(); #print_r($prepaylist);exit;

        $sql5 = "SELECT
		sum(a.pr_issue_money) AS 'total_amount',
		sum(case when a.pr_status = '已完成' then a.pr_issue_money  else 0 end) AS 'finish_prepay_amount_total',
		sum(case when a.pr_status = '未完成' then a.pr_issue_money  else 0 end) AS 'notfinish_prepay_amount_total',
		sum(case when b.pq_status = '已扣款' and a.pr_status = '未完成' then b.pq_money_now  else 0 end) AS 'notfinish_prepay_deducted_amount_total', 
		sum(case when b.pq_status = '未扣款' and a.pr_status = '未完成' then b.pq_money_now  else 0 end) AS 'notfinish_prepay_nodeducted_amount_total'
		FROM prepay_record as a LEFT JOIN prepay_queue b on a.pr_batch_id = b.pq_batch_id where a.pr_user_mail = '$email'";

        $prepaytotalRes = Yii::$app->db->createCommand($sql5)->queryone(); #print_r($prepaylist);exit;
        #流程统计

        return $this->render('user-info-item', [
            'UserMemberRes'  => $UserMemberRes,
            'pdtlist'        => $pdtlist,
            'tradelist'      => $tradelist,
            'financelist'    => $financelist,
            'prepaylist'     => $prepaylist,
            'prepaytotalRes' => $prepaytotalRes,
        ]);
    }

    #用户资金流动记录列表
    public function actionFundflowRecordsList()
    {
        $FinanceFundflowModel = new FinanceFundflow();

        $FinanceFundflowQuery = $FinanceFundflowModel->find();
        #创建搜索条件
        $FinanceFundflowModel->createSearchWhere($FinanceFundflowQuery, $this->get());

        $iCount   = $FinanceFundflowQuery->count();//echo $iCount;
        $pageSize = 50;
        $oPage    = DataHelper::getPage($iCount, $pageSize);

        $arrRes = $FinanceFundflowQuery->With('usermember')->offset($oPage->offset)->limit($oPage->limit)->orderBy('id desc')->asArray()->all(); #print_r($arrRes);exit;

        $UserAdminModel = new UserAdmin();
        #获取客服
        $adminlist_sales      = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 4])->andwhere(['status' => 1])->asArray()->all();
        $adminlist_saleleader = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 7])->andwhere(['status' => 1])->asArray()->all();
        $adminlist            = array_merge($adminlist_sales, $adminlist_saleleader);

        return $this->render('fund-flow-list', [
            'arrRes'    => $arrRes,
            'iCount'    => $iCount,
            'page'      => $oPage,
            'pageSize'  => $pageSize,
            'adminlist' => $adminlist,
        ]);

    }

    #用户余额比对
    public function actionUserBalanceComparison()
    {
        $UserMemberModel = new UserMember();
        $arrRes          = $UserMemberModel->find()->select('u_id,email,uname,balance')->where('balance != 0')->orderBy('balance asc,u_id asc')->asArray()->all();

        $balance_arr = array_column($arrRes, 'balance');
        $email_arr   = array_column($arrRes, 'email');

        $arrRes = array_combine($email_arr, $balance_arr);

        return $this->render('user-balance-comparison', [
            'arrRes'      => json_encode($arrRes),
            'balance_str' => json_encode($balance_arr),
            'email_str'   => json_encode($email_arr),
        ]);
    }

    public function actionGetUserBalance()
    {
        $UserMemberModel = new UserMember();
        $arrRes          = $UserMemberModel->find()->select('u_id,email,uname,balance')->where('balance != 0')->orderBy('balance asc,u_id asc')->asArray()->all();

        $balance_arr = array_column($arrRes, 'balance');
        $email_arr   = array_column($arrRes, 'email');

        $arrRes    = array_combine($email_arr, $balance_arr);
        $arrReturn = [
            'status' => 1,
            'info'   => '获取成功',
            'data'   => $arrRes,
        ];
        return $this->renderJSON($arrReturn);

    }
    /*
    public function actionReceivablesList() {

        #获取用户到期  续费应收款
        $sql1 = "SELECT room.id,room.name as name,sum(sell_price) as 'total_amount',count(*) as 'total_num',1 as type
        FROM member_pdt as pdt JOIN pdt_room_manage as room ON pdt.room_id = room.id where pdt.end_time < unix_timestamp(now()) and pdt.`status` != -1 GROUP BY room.id";

        $ReceivablesList1 = Yii::$app->db->createCommand($sql1)->queryAll(); #print_r($list);exit;
        #获取流程的应收款
        $sql2 = "SELECT flow_name as 'name',sum(flow_total_money) as 'total_amount', count(*) as 'total_num', 2 as type
        from workflow where (flow_status = '事务处理中' or flow_name = '等待预配置') and flow_name like '%后付款%' GROUP BY flow_name";

        $ReceivablesList2 = Yii::$app->db->createCommand($sql2)->queryAll(); #print_r($list);exit;
        #合并
        $ReceivablesList = array_merge($ReceivablesList1, $ReceivablesList2);

        return $this->render('receivables-list',[
            'ReceivablesList' => $ReceivablesList,
        ]);
    } */

    #应收款统计
    public function actionReceivablesList()
    {
        $RevenueReceivableModel = new RevenueReceivable;

        $getData               = $this->get();
        $getData['start_time'] = ArrayHelper::getValue($getData, 'start_time') ? $getData['start_time'] : date("Y-m-d", time());
        $getData['end_time']   = ArrayHelper::getValue($getData, 'end_time') ? $getData['end_time'] : date("Y-m-d", time() + 86400);


        $RevenueReceivableQuery = $RevenueReceivableModel->find();

        #创建搜索条件
        $RevenueReceivableModel->createSearchWhere($RevenueReceivableQuery, $getData);

        $checkData = ArrayHelper::getValue($getData, 'start_time');
        if ($checkData == '全部记录' || $checkData == '') {
            $RevenueReceivableQuery->andwhere(['<>', 'make_status', '已取消']);
        } else if ($checkData == '已确认') {
            $RevenueReceivableQuery->andwhere(['make_status' => '已确认']);
        } else {
            $RevenueReceivableQuery->andwhere(['make_status' => '未确认']);
        }

        $iCount   = $RevenueReceivableQuery->count();
        $pageSize = 50;
        $oPage    = DataHelper::getPage($iCount, $pageSize);

        #统计数据
        $countData = $RevenueReceivableQuery->asArray()->all();
        $dataView  = [
            'total'              => $iCount, #总笔数
            'confirm_num_finish' => 0, #已确认笔数
            'confirm_num_none'   => 0, #未确认笔数
        ];

        foreach ($countData as $key => $val) {
            if ($val['make_status'] == '已确认') {
                $dataView['confirm_num_finish']++;
            } else {
                $dataView['confirm_num_none']++;
            }
        }


        $arrRes = $RevenueReceivableQuery->joinWith('notes')->offset($oPage->offset)->limit($oPage->limit)->orderBy("make_money_ordertime desc")->asArray()->all();


        $UserAdminModel = new UserAdmin();
        #获取客服
        $adminlist_sales      = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 4])->andwhere(['status' => 1])->asArray()->all();
        $adminlist_saleleader = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 7])->andwhere(['status' => 1])->asArray()->all();
        $adminlist            = array_merge($adminlist_sales, $adminlist_saleleader);

        #获取机房
        $PdtRoomMangeModel = new PdtRoomMange();
        $PdtRoomList       = $PdtRoomMangeModel->find()->select('id,name')->asArray()->All();
        #服务器分类
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeList  = $PdtManageTypeModel->find()->select('type_id,type_name')->asArray()->All();

        return $this->render('receivables-list', [
            'arrRes'            => $arrRes,
            'iCount'            => $iCount,
            'page'              => $oPage,
            'pageSize'          => $pageSize,
            'adminlist'         => $adminlist,
            'PdtRoomList'       => $PdtRoomList,
            'PdtManageTypeList' => $PdtManageTypeList,
            'countData'         => $dataView,
        ]);
    }

    #应收款列表下载
    public function actionReceivablesDownload()
    {
        $RevenueReceivableModel = new RevenueReceivable;

        $getData               = $this->get();
        $getData['start_time'] = $getData['start_time'] ? $getData['start_time'] : date("Y-m-d", time());
        $getData['end_time']   = $getData['end_time'] ? $getData['end_time'] : date("Y-m-d", time() + 86400);


        $RevenueReceivableQuery = $RevenueReceivableModel->find();

        #创建搜索条件
        $RevenueReceivableModel->createSearchWhere($RevenueReceivableQuery, $getData);

        if ($getData['checkData'] == '全部记录' || $getData['checkData'] == '') {
            $RevenueReceivableQuery->andwhere(['<>', 'make_status', '已取消']);
        } else if ($getData['checkData'] == '已确认') {
            $RevenueReceivableQuery->andwhere(['make_status' => '已确认']);
        } else {
            $RevenueReceivableQuery->andwhere(['make_status' => '未确认']);
        }

        $arrRes = $RevenueReceivableQuery->joinWith('notes')->orderBy("make_money_ordertime desc")->asArray()->all();


        $newExcel = new Spreadsheet;
        $objSheet = $newExcel->getActiveSheet(0);  //获取当前操作sheet的对象
        $objSheet->setTitle('应收款明细');  //设置当前sheet的标题
        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        #水平方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        #垂直方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        #设置列宽
        $newExcel->getActiveSheet()->getColumnDimension('A')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('B')->setWidth(25);
        $newExcel->getActiveSheet()->getColumnDimension('C')->setWidth(25);
        $newExcel->getActiveSheet()->getColumnDimension('D')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('E')->setWidth(25);
        $newExcel->getActiveSheet()->getColumnDimension('F')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('G')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('H')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('I')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('J')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('K')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('L')->setWidth(25);
        $newExcel->getActiveSheet()->getColumnDimension('M')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('N')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('O')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('P')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('Q')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('R')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('S')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('T')->setWidth(20);

        #金额保留两位小数
        $newExcel->getActiveSheet()->getStyle('M')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER_00);
        $newExcel->getActiveSheet()->getStyle('N')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER_00);
        $newExcel->getActiveSheet()->getStyle('O')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER_00);


        #设置列名
        $objSheet->setCellValue('A1', '流程号');
        $objSheet->setCellValue('B1', '订单号');
        $objSheet->setCellValue('C1', '订单批号');
        $objSheet->setCellValue('D1', '业务号');
        $objSheet->setCellValue('E1', '用户账户');
        $objSheet->setCellValue('F1', '用户昵称');
        $objSheet->setCellValue('G1', '交易类别');
        $objSheet->setCellValue('H1', '所属销售');
        $objSheet->setCellValue('I1', '机房');
        $objSheet->setCellValue('J1', '服务器分类');
        $objSheet->setCellValue('K1', '提供方');
        $objSheet->setCellValue('L1', 'IP地址');
        $objSheet->setCellValue('M1', '总金额');
        $objSheet->setCellValue('N1', '确认金额');
        $objSheet->setCellValue('O1', '预存金额');
        $objSheet->setCellValue('P1', '是否确认');
        $objSheet->setCellValue('Q1', '付款方式');
        $objSheet->setCellValue('R1', '流程进度');
        $objSheet->setCellValue('S1', '金额审核');
        $objSheet->setCellValue('T1', '确认时间');


        $data = [];

        foreach ($arrRes as $key => $val) {

            $k = $key + 2;

            $iplist = json_decode($val['make_ip'], true);

            $objSheet->setCellValue('A' . $k, $val['make_flowid']);
            $objSheet->setCellValue('B' . $k, $val['make_orderid'] . "\t");
            $objSheet->setCellValue('C' . $k, $val['make_orderparentid']);
            $objSheet->setCellValue('D' . $k, $val['make_unionid']);
            $objSheet->setCellValue('E' . $k, $val['make_useraccount']);
            $objSheet->setCellValue('F' . $k, $val['make_usernick']);
            $objSheet->setCellValue('G' . $k, $val['make_type']);
            $objSheet->setCellValue('H' . $k, $val['make_adminname']);
            $objSheet->setCellValue('I' . $k, $val['make_roomname']);
            $objSheet->setCellValue('J' . $k, $val['make_servertypename']);
            $objSheet->setCellValue('K' . $k, $val['make_provider']);
            $objSheet->setCellValue('L' . $k, $iplist[0]);
            $objSheet->setCellValue('M' . $k, $val['make_money_total']); //FORMAT_NUMBER_00
            $objSheet->setCellValue('N' . $k, $val['make_money_now']);
            $objSheet->setCellValue('O' . $k, $val['make_money_prepay']);
            $objSheet->setCellValue('P' . $k, $val['make_status']);
            $objSheet->setCellValue('Q' . $k, $val['make_pay_type']);
            $objSheet->setCellValue('R' . $k, $val['make_flow_status']);
            $objSheet->setCellValue('S' . $k, $val['make_audit_status']);
            $objSheet->setCellValue('T' . $k, date("Y-m-d", $val['make_money_ordertime']));
        }
        //设置第一栏的标题

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=应收款明细_" . date('Y-m-d', time()) . '.' . strtolower("Xlsx"));
        header('Cache-Control: max-age=0');
        $objWriter = IOFactory::createWriter($newExcel, 'Xlsx');
        $objWriter->save('php://output');
    }


    /* 机房维度 计算 机房收入 成本 */
    public function actionMachineMoney()
    {
        $queryList  = $this->get();
        $start_time = ArrayHelper::getValue($queryList, 'start_time') ? strtotime($queryList['start_time']) : strtotime(date("Y-m-d", time()));
        $end_time   = ArrayHelper::getValue($queryList, 'end_time') ? strtotime($queryList['end_time']) : strtotime(date("Y-m-d", time())) + 86400;

        $sql = "select rs_roomname, rs_servertypename, rs_provider, 
				sum(rs_purchase_balance) as purchase_balance_money, sum(rs_purchase_balance_num) as purchase_balance_num, sum(rs_purchase_online) as purchase_online_money, sum(rs_purchase_online_num) as purchase_online_num, sum(rs_purchase_underline) as purchase_underline_money, sum(rs_purchase_underline_num) as purchase_underline_num, 
				sum(rs_renew_balance) as renew_balance_money, sum(rs_renew_balance_num) as renew_balance_num, sum(rs_renew_online) as renew_online_money, sum(rs_renew_online_num) as renew_online_num, sum(rs_renew_underline) as renew_underline_money, sum(rs_renew_underline_num) as renew_underline_num, 
				sum(rs_change_balance) as change_balance_money, sum(rs_change_balance_num) as change_balance_num, sum(rs_change_online) as change_online_money, sum(rs_change_online_num) as change_online_num, sum(rs_change_underline) as change_underline_money, sum(rs_change_underline_num) as change_underline_num, sum(rs_change_none_num) as change_none_num, sum(rs_change_refund) as change_refund_money, sum(rs_change_refund_num) as change_refund_num, 
				sum(rs_replace_balance) as replace_balance_money, sum(rs_replace_balance_num) as replace_balance_num, sum(rs_replace_online) as replace_online_money, sum(rs_replace_online_num) as replace_online_num, sum(rs_replace_underline) as replace_underline_money, sum(rs_replace_underline_num) as replace_underline_num, sum(rs_replace_none_num) as replace_none_num, sum(rs_replace_refund) as replace_refund_money, sum(rs_replace_refund_num) as replace_refund_num, 
				sum(rs_refund) as refund_money, sum(rs_refund_num) as refund_num 
				from revenue_statistics where rs_datatime >= '" . $start_time . "' and rs_datatime < '" . $end_time . "'";

        if (ArrayHelper::getValue($queryList, 'provider')) {
            $sql .= " and rs_provider = '" . $queryList['provider'] . "'";
        }

        if (ArrayHelper::getValue($queryList, 'room_id')) {
            $sql .= " and rs_roomid = '" . $queryList['room_id'] . "'";
        }

        if (ArrayHelper::getValue($queryList, 'server_type_id')) {
            $sql .= " and rs_servertypeid = '" . $queryList['server_type_id'] . "'";
        }

        if (ArrayHelper::getValue($queryList, 'is_station')) {
            if ($queryList['is_station'] == '站群') {
                $sql .= " and rs_servertypename like '%站群%'";
            } else {
                $sql .= " and rs_servertypename not like '%站群%'";
            }

        }

        $sql .= " group by rs_roomname, rs_servertypename, rs_provider";

        //echo $sql;exit;

        $res = Yii::$app->db->createCommand($sql)->queryAll();

        $totalData = [
            'rs_roomname'       => '',
            'rs_servertypename' => '',
            'rs_provider'       => '',

            'purchase_balance_money'   => 0,
            'purchase_balance_num'     => 0,
            'purchase_online_money'    => 0,
            'purchase_online_num'      => 0,
            'purchase_underline_money' => 0,
            'purchase_underline_num'   => 0,

            'renew_balance_money'   => 0,
            'renew_balance_num'     => 0,
            'renew_online_money'    => 0,
            'renew_online_num'      => 0,
            'renew_underline_money' => 0,
            'renew_underline_num'   => 0,

            'change_balance_money'   => 0,
            'change_balance_num'     => 0,
            'change_online_money'    => 0,
            'change_online_num'      => 0,
            'change_underline_money' => 0,
            'change_underline_num'   => 0,
            'change_none_num'        => 0,
            'change_refund_money'    => 0,
            'change_refund_num'      => 0,

            'replace_balance_money'   => 0,
            'replace_balance_num'     => 0,
            'replace_online_money'    => 0,
            'replace_online_num'      => 0,
            'replace_underline_money' => 0,
            'replace_underline_num'   => 0,
            'replace_none_num'        => 0,
            'replace_refund_money'    => 0,
            'replace_refund_num'      => 0,

            'refund_money' => 0,
            'refund_num'   => 0,
        ];

        if ($res) {
            foreach ($res as $key => $val) {

                $totalData['purchase_balance_money']   += $val['purchase_balance_money'];
                $totalData['purchase_balance_num']     += $val['purchase_balance_num'];
                $totalData['purchase_online_money']    += $val['purchase_online_money'];
                $totalData['purchase_online_num']      += $val['purchase_online_num'];
                $totalData['purchase_underline_money'] += $val['purchase_underline_money'];
                $totalData['purchase_underline_num']   += $val['purchase_underline_num'];

                $totalData['renew_balance_money']   += $val['renew_balance_money'];
                $totalData['renew_balance_num']     += $val['renew_balance_num'];
                $totalData['renew_online_money']    += $val['renew_online_money'];
                $totalData['renew_online_num']      += $val['renew_online_num'];
                $totalData['renew_underline_money'] += $val['renew_underline_money'];
                $totalData['renew_underline_num']   += $val['renew_underline_num'];

                $totalData['change_balance_money']   += $val['change_balance_money'];
                $totalData['change_balance_num']     += $val['change_balance_num'];
                $totalData['change_online_money']    += $val['change_online_money'];
                $totalData['change_online_num']      += $val['change_online_num'];
                $totalData['change_underline_money'] += $val['change_underline_money'];
                $totalData['change_underline_num']   += $val['change_underline_num'];
                $totalData['change_none_num']        += $val['change_none_num'];
                $totalData['change_refund_money']    += $val['change_refund_money'];
                $totalData['change_refund_num']      += $val['change_refund_num'];

                $totalData['replace_balance_money']   += $val['replace_balance_money'];
                $totalData['replace_balance_num']     += $val['replace_balance_num'];
                $totalData['replace_online_money']    += $val['replace_online_money'];
                $totalData['replace_online_num']      += $val['replace_online_num'];
                $totalData['replace_underline_money'] += $val['replace_underline_money'];
                $totalData['replace_underline_num']   += $val['replace_underline_num'];
                $totalData['replace_none_num']        += $val['replace_none_num'];
                $totalData['replace_refund_money']    += $val['replace_refund_money'];
                $totalData['replace_refund_num']      += $val['replace_refund_num'];

                $totalData['refund_money'] += $val['refund_money'];
                $totalData['refund_num']   += $val['refund_num'];
            }
        }

        array_unshift($res, $totalData);

        #获取机房
        $PdtRoomMangeModel = new PdtRoomMange();
        $PdtRoomList       = $PdtRoomMangeModel->find()->select('id,name')->asArray()->All();
        #服务器分类
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeList  = $PdtManageTypeModel->find()->select('type_id,type_name')->asArray()->All();

        return $this->render('revenue', [
            'data'              => $res,
            'PdtRoomList'       => $PdtRoomList,
            'PdtManageTypeList' => $PdtManageTypeList,
        ]);

    }


    #收入明细列表
    public function actionRevenueNoteList()
    {
        #服务器分类，机房，自有/供应商，IP，用户昵称，销售，类型（新购...），支付方式（...）,流程号，订单号，订单批号，业务号，时间开始，时间结束
        $MoneyAccountRecordModel = new MoneyAccountRecord();
        $MoneyAccountRecordQuery = $MoneyAccountRecordModel->find()->where("record_user_account != '<EMAIL>'");

        $getData = $this->get();

        $start_time = ArrayHelper::getValue($getData, 'start_time') ? strtotime($getData['start_time']) : strtotime(date("Y-m-d", time()));
        $end_time   = ArrayHelper::getValue($getData, 'end_time') ? strtotime($getData['end_time']) : strtotime(date("Y-m-d", time() + 86400));

        unset($getData['start_time'], $getData['end_time']);

        #创建搜索条件
        $MoneyAccountRecordModel->createSearchWhere($MoneyAccountRecordQuery, $getData);

        $MoneyAccountRecordQuery->andwhere(['>=', "record_account_time", $start_time])->andwhere(['<', "record_account_time", $end_time]);

        $iCount   = $MoneyAccountRecordQuery->count();
        $pageSize = 30;
        $oPage    = DataHelper::getPage($iCount, $pageSize);

        $arrRes = $MoneyAccountRecordQuery->offset($oPage->offset)->limit($oPage->limit)->orderBy('record_account_time desc')->asArray()->all();

        $UserAdminModel = new UserAdmin();
        #获取客服
        $adminlist_sales      = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 4])->andwhere(['status' => 1])->asArray()->all();
        $adminlist_saleleader = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 7])->andwhere(['status' => 1])->asArray()->all();
        $adminlist            = array_merge($adminlist_sales, $adminlist_saleleader);

        #获取机房
        $PdtRoomMangeModel = new PdtRoomMange();
        $PdtRoomList       = $PdtRoomMangeModel->find()->select('id,name')->asArray()->All();
        #服务器分类
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeList  = $PdtManageTypeModel->find()->select('type_id,type_name')->asArray()->All();

        return $this->render('money_account_record', [
            'data'              => $arrRes,
            'iCount'            => $iCount,
            'page'              => $oPage,
            'pageSize'          => $pageSize,
            'adminlist'         => $adminlist,
            'PdtRoomList'       => $PdtRoomList,
            'PdtManageTypeList' => $PdtManageTypeList,
        ]);
    }

    #收入明细列表下载
    public function actionRevenueNoteListDownload()
    {
        #服务器分类，机房，自有/供应商，IP，用户昵称，销售，类型（新购...），支付方式（...）,流程号，订单号，订单批号，业务号，时间开始，时间结束
        $MoneyAccountRecordModel = new MoneyAccountRecord();
        $MoneyAccountRecordQuery = $MoneyAccountRecordModel->find()->where("record_user_account != '<EMAIL>'");

        $getData = $this->get();

        $start_time = $getData['start_time'] ? strtotime($getData['start_time']) : strtotime(date("Y-m-d", time()));
        $end_time   = $getData['end_time'] ? strtotime($getData['end_time']) : strtotime(date("Y-m-d", time() + 86400));

        unset($getData['start_time'], $getData['end_time']);

        #创建搜索条件
        $MoneyAccountRecordModel->createSearchWhere($MoneyAccountRecordQuery, $getData);

        $arrRes = $MoneyAccountRecordQuery->andwhere(['>=', "record_account_time", $start_time])->andwhere(['<', "record_account_time", $end_time])->orderBy('record_account_time desc')->asArray()->all();


        $newExcel = new Spreadsheet;
        $objSheet = $newExcel->getActiveSheet(0);  //获取当前操作sheet的对象
        $objSheet->setTitle('三方收款明细');  //设置当前sheet的标题
        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        #水平方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        #垂直方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        #设置列宽
        $newExcel->getActiveSheet()->getColumnDimension('A')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('B')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('C')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('D')->setWidth(18);
        $newExcel->getActiveSheet()->getColumnDimension('E')->setWidth(18);
        $newExcel->getActiveSheet()->getColumnDimension('F')->setWidth(18);
        $newExcel->getActiveSheet()->getColumnDimension('G')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('H')->setWidth(25);
        $newExcel->getActiveSheet()->getColumnDimension('I')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('J')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('K')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('L')->setWidth(20);
        $newExcel->getActiveSheet()->getColumnDimension('M')->setWidth(30);
        $newExcel->getActiveSheet()->getColumnDimension('N')->setWidth(30);

        #金额保留两位小数
        $newExcel->getActiveSheet()->getStyle('J')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER_00);

        #设置列名
        $objSheet->setCellValue('A1', '交易类型');
        $objSheet->setCellValue('B1', '用户昵称');
        $objSheet->setCellValue('C1', '所属销售');
        $objSheet->setCellValue('D1', '机房名称');
        $objSheet->setCellValue('E1', '服务器分类');
        $objSheet->setCellValue('F1', '提供方');
        $objSheet->setCellValue('G1', 'IP地址');
        $objSheet->setCellValue('H1', '交易配置');
        $objSheet->setCellValue('I1', '支付方式');
        $objSheet->setCellValue('J1', '打款金额');
        $objSheet->setCellValue('K1', '到账时间');
        $objSheet->setCellValue('L1', '打款平台');
        $objSheet->setCellValue('M1', '回执单号');
        $objSheet->setCellValue('N1', '打款备注');


        $data = [];

        foreach ($arrRes as $key => $val) {

            $k = $key + 2;

            $configList = json_decode($val['record_config'], true);
            if ($configList) {
                $config = $configList['cpu'] . ' / ' . $configList['ram'] . ' / ' . $configList['hdd'];
            } else {
                $config = null;
            }

            if ($val['record_ip2']) {
                $iplist = json_decode($val['record_ip2'], true);
                $mainip = $iplist[0];
            } else {
                $mainip = null;
            }

            $objSheet->setCellValue('A' . $k, $val['record_type']);
            $objSheet->setCellValue('B' . $k, $val['record_user_nick']);
            $objSheet->setCellValue('C' . $k, $val['record_sales']);
            $objSheet->setCellValue('D' . $k, $val['record_room_name']);
            $objSheet->setCellValue('E' . $k, $val['record_server_type'] . "\t");
            $objSheet->setCellValue('F' . $k, $val['record_provider']);
            $objSheet->setCellValue('G' . $k, $mainip);
            $objSheet->setCellValue('H' . $k, $config);
            $objSheet->setCellValue('I' . $k, $val['record_pay_type']);
            $objSheet->setCellValue('J' . $k, $val['record_money']);
            $objSheet->setCellValue('K' . $k, date("Y-m-d", $val['record_account_time']));
            $objSheet->setCellValue('L' . $k, $val['record_platform']);
            $objSheet->setCellValue('M' . $k, $val['record_stream_number']);
            $objSheet->setCellValue('N' . $k, $val['record_remark']);

        }
        //设置第一栏的标题

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=三方收款明细_" . $start_time . '_' . $end_time . '.' . strtolower("Xlsx"));
        header('Cache-Control: max-age=0');
        $objWriter = IOFactory::createWriter($newExcel, 'Xlsx');
        $objWriter->save('php://output');

    }


    #未确认收款
    public function actionUnconfirmMoney()
    {
        $MoneyAccountRecordModel = new MoneyAccountRecord();
        $PaymentAccountModel     = new PaymentAccount();

        if (Yii::$app->request->post()) {

            $post = $this->post();

            $accountInfo = $PaymentAccountModel->find()->where(['id' => $post['payment_account']])->asArray()->one();

            $MoneyAccountRecordModel->record_type         = '未确认收款';
            $MoneyAccountRecordModel->record_money        = $post['record_money'];
            $MoneyAccountRecordModel->record_account_time = strtotime($post['record_time']);
            $MoneyAccountRecordModel->record_platform     = $accountInfo['account_name'];

            if ($MoneyAccountRecordModel->insert()) {
                return $this->renderJSON(['status' => 1, 'info' => '记录未确认款项完成']);
            } else {
                return $this->renderJSON(['status' => 0, 'info' => '记录未确认款项出现异常，请联系管理员']);
            }

        } else {

            $accountList = $PaymentAccountModel->find()->asArray()->all();

            return $this->render('unconfirm-money', ['accountList' => $accountList]);

        }
    }

    #删除未确认收款
    public function actionDeleteUnconfirmMoney()
    {

        if (Yii::$app->request->post()) {

            $id = $this->post('id');

            $MoneyAccountRecordModel = new MoneyAccountRecord();

            $Record = $MoneyAccountRecordModel->find()->where(['id' => $id])->one();

            if (!$Record) {
                return $this->renderJSON(['status' => 0, 'info' => '当前收款不存在']);
            }

            if ($Record->delete()) {
                return $this->renderJSON(['status' => 1, 'info' => '删除未确认收款完成']);
            } else {
                return $this->renderJSON(['status' => 0, 'info' => '删除未确认收款出现异常，请练习管理员']);
            }

        } else {
            return $this->renderJSON(['status' => 0, 'info' => '异常访问，请联系管理员']);
        }
    }


    #获取收入明细配置信息
    public function actionGetConfigChange()
    {
        $noteid             = $this->post('id');
        $RevenueNotesModel  = new RevenueNotes();
        $PdtRoomMangeModel  = new PdtRoomMange();
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageModel     = new PdtManage();

        $NoteRes = $RevenueNotesModel->find()->where(['note_id' => $noteid])->asArray()->one();

        $newData = [];

        $frontConfig = json_decode($NoteRes['note_front_config'], true);
        $afterConfig = json_decode($NoteRes['note_after_config'], true);

        $frontServerType = $PdtManageTypeModel->find()->where(['type_id' => $frontConfig['server_type_id']])->asArray()->one();
        $frontRoom       = $PdtRoomMangeModel->find()->where(['id' => $frontConfig['room_id']])->asArray()->one();
        $frontPdtType    = $PdtManageModel->find()->where(['id' => $frontConfig['pdt_id']])->asArray()->one();

        $afterServerType = $PdtManageTypeModel->find()->where(['type_id' => $afterConfig['server_type_id']])->asArray()->one();
        $afterRoom       = $PdtRoomMangeModel->find()->where(['id' => $afterConfig['room_id']])->asArray()->one();
        $afterPdtType    = $PdtManageModel->find()->where(['id' => $afterConfig['pdt_id']])->asArray()->one();


        $newData = [
            [
                'title' => '机房',
                'front' => $frontRoom['name'],
                'after' => $afterRoom['name'],
            ],
            [
                'title' => '服务器分类',
                'front' => $frontServerType['type_name'],
                'after' => $afterServerType['type_name'],
            ],
            [
                'title' => '产品配置类别',
                'front' => $frontPdtType['name'],
                'after' => $afterPdtType['name'],
            ],
            [
                'title' => '机器提供方',
                'front' => $frontConfig['idle_id'] ? '自有' : '供应商',
                'after' => $afterConfig['idle_id'] ? '自有' : '供应商',
            ],
            [
                'title' => '服务开始时间',
                'front' => $frontConfig['start_time'] ? date("Y-m-d H:i:s", $frontConfig['start_time']) : null,
                'after' => $afterConfig['start_time'] ? date("Y-m-d H:i:s", $afterConfig['start_time']) : null,
            ],
            [
                'title' => '服务结束时间',
                'front' => $frontConfig['end_time'] ? date("Y-m-d H:i:s", $frontConfig['end_time']) : null,
                'after' => $afterConfig['end_time'] ? date("Y-m-d H:i:s", $afterConfig['end_time']) : null,
            ],
            [
                'title' => '售出价格',
                'front' => $frontConfig['sell_price'],
                'after' => $afterConfig['sell_price'],
            ],
            [
                'title' => 'CPU',
                'front' => $frontConfig['config']['cpu'],
                'after' => $afterConfig['config']['cpu'],
            ],
            [
                'title' => '内存',
                'front' => $frontConfig['config']['ram'],
                'after' => $afterConfig['config']['ram'],
            ],
            [
                'title' => '硬盘',
                'front' => $frontConfig['config']['hdd'],
                'after' => $afterConfig['config']['hdd'],
            ],
            [
                'title' => '配置带宽',
                'front' => $frontConfig['config']['configbandwidth'],
                'after' => $afterConfig['config']['configbandwidth'],
            ],
            [
                'title' => '可用IP数',
                'front' => $frontConfig['config']['ipnumber'],
                'after' => $afterConfig['config']['ipnumber'],
            ],
            [
                'title' => '系统防御',
                'front' => $frontConfig['config']['defense'],
                'after' => $afterConfig['config']['defense'],
            ],
            [
                'title' => '操作系统',
                'front' => $frontConfig['config']['operatsystem'],
                'after' => $afterConfig['config']['operatsystem'],
            ],
        ];

        foreach ($newData as $key => $val) {
            if ($val['front'] != $val['after']) {
                $newData[$key]['after'] = '<span style="color:red">' . $newData[$key]['after'] . '</span>';
            }
        }

        $arrReturn = [
            'status' => 1,
            'data'   => $newData,
        ];

        return $this->renderJSON($arrReturn);
    }


    #计算月份和天数
    public function MathMonthDays($start, $end)
    {

        if (date("m", $start) < 12) {
            $nextMonthStart = strtotime(date("Y", $start) . '-' . (date("m", $start) + 1));
        } else {
            $nextMonthStart = strtotime(date("Y", $start) + 1 . '-01');
        }

        if (date("Y-m", $start) == date("Y-m", $end)) {

            #两个时间节点都一定在当月
            $this->daysData[] = [
                "days_total" => ($nextMonthStart - strtotime(date("Y-m", $start))) / 86400,
                "days_now"   => ($end - $start) / 86400,
            ];

        } else {
            #不知道有多少个月，递归

            $this->daysData[] = [
                "days_total" => ($nextMonthStart - $start) / 86400,
                "days_now"   => ($nextMonthStart - $start) / 86400,
            ];

            self::MathMonthDays($nextMonthStart, $end);
        }
    }

    /**
     *销售报表
     */
    public function actionSalesStatistics()
    {
        $queryList = $this->get();

        $UserAdmin = new UserAdmin();
        //获取负责人信息
        $UserAdminModel       = new UserAdmin();
        $adminlist_sales      = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 4])->asArray()->all();
        $adminlist_saleleader = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 7])->asArray()->all();
        $adminArrlist         = array_merge($adminlist_sales, $adminlist_saleleader);
        $adminRes             = $adminArrlist;
        foreach ($adminArrlist as $value) {
            $adminid_total = $value['admin_id'];
        }

        $adminid_string = implode(',', $adminid_list);//echo $adminid_string;die;

        #时间  默认为当前月
        $starttime  = !isset($queryList['starttime']) || !$queryList['starttime'] ? strtotime(date("Y-m", time())) : strtotime($queryList['starttime']);
        $endtime    = !isset($queryList['endtime']) || !$queryList['endtime'] ? time() : strtotime($queryList['endtime']);//echo $endtime;die;
        $admin_name = !isset($queryList['admin_name']) || !$queryList['admin_name'] ? "" : $queryList['admin_name'];
        if (!empty($admin_name)) {
            $condition = "AND a.uname='$admin_name'";
        } else {
            $condition = "";
        }
        //$condition .= !isset($queryList['admin_name']) || !$queryList['admin_name'] ? "" : "AND a.uname = ".$queryList['admin_name'];
        $sql = "SELECT a.admin_id, a.uname,
SUM(case when b.trade_type='consume' then b.trade_money else '0' end ) as consume_total,sum(case when b.trade_type='consume' then 1  else '0' end) AS 'consume_num',
SUM(case when b.trade_type='refund' then b.trade_money  else '0' end ) as refund_total,sum(case when b.trade_type='refund' then 1  else '0' end) AS 'refund_num',
SUM(case when b.trade_type='dummy' then b.trade_money  else '0' end ) as dummy_total,sum(case when b.trade_type='dummy' then 1  else '0' end) AS 'dummy_num',
SUM(case when b.trade_type='recharge' then b.trade_money  else '0' end ) as recharge_total,sum(case when b.trade_type='recharge' then 1  else '0' end) AS 'recharge_num',
SUM(case when b.trade_type='withdrawal' then b.trade_money  else '0' end ) as withdrawal_total,sum(case when b.trade_type='withdrawal' then 1  else '0' end) AS 'withdrawal_num',
SUM(case when b.trade_type='order' then b.trade_money  else '0' end ) as order_total,sum(case when b.trade_type='order' then 1  else '0' end) AS 'order_num',
SUM(case when b.trade_type='fee' then b.trade_money  else '0' end ) as fee_total,sum(case when b.trade_type='fee' then 1  else '0' end) AS 'fee_num'
FROM user_admin as a LEFT JOIN finance_manage b ON a.admin_id = b.admin_id
WHERE  a.admin_id IN($adminid_string) AND b.trade_createtime BETWEEN $starttime AND $endtime $condition GROUP BY a.admin_id";

        #每个销售的
        $connection = Yii::$app->db;

        $Statisticslist = $connection->createCommand($sql)->queryAll();//print_r($Statisticslist);die;
        $newData        = [];
        foreach ($adminArrlist as $key => $value) {
            foreach ($Statisticslist as $key1 => $value1) {
                if ($value['admin_id'] == $value1['admin_id']) {
                    $newData[] = $value1;
                    unset($adminArrlist[$key]);
                    unset($Statisticslist[$key1]);
                    break;
                } else {
                    $newData[] = [
                        'admin_id'         => $value['admin_id'],
                        'uname'            => $value['uname'],
                        'consume_total'    => "0",
                        'consume_num'      => "0",
                        'refund_total'     => "0",
                        'refund_num'       => "0",
                        'dummy_total'      => "0",
                        'dummy_num'        => "0",
                        'recharge_total'   => "0",
                        'recharge_num'     => "0",
                        'withdrawal_total' => "0",
                        'withdrawal_num'   => "0",
                        'order_total'      => "0",
                        'order_num'        => "0",
                        'fee_total'        => "0",
                        'fee_num'          => "0",
                    ];
                    unset($adminArrlist[$key]);
                    break;
                }
            }
        }

        return $this->render('sales-statistics', [
            'Statisticslist' => $newData,
            'adminlist'      => $adminRes,
            'starttime'      => $starttime,
            'endtime'        => $endtime,
        ]);
    }

}
