<?php

namespace addons\VymGeneral\common\models;

use common\enums\TicketStatusEnum;
use common\models\merchant\Merchant;
use Yii;
use common\behaviors\MerchantBehavior;
use common\models\base\BaseModel;
use common\models\member\Member;
use common\models\rbac\AuthAssignment;

/**
 * This is the model class for table "{{%addon_general_ticket}}".
 *
 * @property int $id 主键
 * @property int $merchant_id 商户id
 * @property int $store_id 店铺id
 * @property int $user_id 用户id
 * @property int $admin_id 处理人id
 * @property int $category_id 分类id
 * @property string $title 工单标题
 * @property string $content 工单内容
 * @property string $images 图片附件JSON
 * @property string $attachments 其他附件JSON
 * @property int $priority 优先级[1:低; 2:中; 3:高]
 * @property int $status 状态[1:待处理; 2:处理中; 3:已解决; 4:已关闭]
 * @property string $related_type 关联类型
 * @property int $related_id 关联id
 * @property string $reply_list 回复列表JSON
 * @property int $reply_count 回复数量
 * @property int $last_reply_time 最后回复时间
 * @property int $last_reply_user_id 最后回复用户id
 * @property int $last_reply_user_type 最后回复用户类型[1:客户; 2:管理员]
 * @property int $satisfaction 满意度[1:不满意; 2:一般; 3:满意]
 * @property string $ip IP地址
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 */
class Ticket extends BaseModel
{
    use MerchantBehavior;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%addon_general_ticket}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['merchant_id', 'store_id', 'user_id', 'admin_id', 'category_id', 'priority', 'status', 'related_id', 'reply_count', 'last_reply_time', 'last_reply_user_id', 'last_reply_user_type', 'satisfaction', 'created_at', 'updated_at'], 'integer'],
            [['title', 'content'], 'required'],
            [['content', 'images', 'attachments', 'reply_list'], 'string'],
            [['title'], 'string', 'max' => 100],
            [['related_type'], 'string', 'max' => 50],
            [['ip'], 'string', 'max' => 50],
            [['priority'], 'default', 'value' => 1],
            [['status'], 'default', 'value' => 1],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'merchant_id' => '商户ID',
            'store_id' => '店铺ID',
            'user_id' => '用户ID',
            'admin_id' => '处理人ID',
            'category_id' => '分类',
            'title' => '标题',
            'content' => '内容',
            'images' => '图片附件',
            'attachments' => '其他附件',
            'priority' => '优先级',
            'status' => '状态',
            'related_type' => '关联类型',
            'related_id' => '关联ID',
            'reply_list' => '回复列表',
            'reply_count' => '回复数量',
            'last_reply_time' => '最后回复时间',
            'last_reply_user_id' => '最后回复用户ID',
            'last_reply_user_type' => '最后回复用户类型',
            'satisfaction' => '满意度',
            'ip' => 'IP地址',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
        ];
    }

    /**
     * 获取优先级标签
     *
     * @return string
     */
    public function getPriorityLabel()
    {
        $priorities = [
            1 => '低',
            2 => '中',
            3 => '高',
        ];

        return $priorities[$this->priority] ?? '未知';
    }

    /**
     * 获取状态标签
     *
     * @return string
     */
    public function getStatusLabel()
    {
        $statuses = TicketStatusEnum::getMap();
//        [
//            1 => '待处理',
//            2 => '处理中',
//            3 => '已解决',
//            4 => '已关闭',
//        ];

        return $statuses[$this->status] ?? '未知';
    }

    /**
     * 获取满意度标签
     *
     * @return string
     */
    public function getSatisfactionLabel()
    {
        $satisfactions = [
            0 => '未评价',
            1 => '不满意',
            2 => '一般',
            3 => '满意',
        ];

        return $satisfactions[$this->satisfaction] ?? '未知';
    }

    /**
     * 获取分类
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCategory()
    {
        return $this->hasOne(TicketCategory::class, ['id' => 'category_id']);
    }

    /**
     * 获取用户
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(Member::class, ['id' => 'user_id']);
    }

    /**
     * 获取处理人
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAdmin()
    {
        return $this->hasOne(AuthAssignment::class, ['id' => 'admin_id']);
    }

    /**
     * 获取图片数组
     *
     * @return array
     */
    public function getImagesList()
    {
        return $this->images ? json_decode($this->images, true) : [];
    }

    /**
     * 获取附件数组
     *
     * @return array
     */
    public function getAttachmentsList()
    {
        return $this->attachments ? json_decode($this->attachments, true) : [];
    }

    /**
     * 获取回复列表数组
     *
     * @return array
     */
    public function getRepliesList()
    {
        return $this->reply_list ? json_decode($this->reply_list, true) : [];
    }

    /**
     * 添加回复
     *
     * @param string $content 回复内容
     * @param array $images 图片列表
     * @param int $user_id 用户ID
     * @param int $user_type 用户类型 1:客户 2:管理员
     * @param string $user_name 用户名称
     * @return bool
     */
    public function addReply($content, $images = [], $user_id = 0, $user_type = 2, $user_name = '')
    {
        $replies = $this->getRepliesList();
        
        $reply = [
            'id' => count($replies) + 1,
            'content' => $content,
            'images' => $images,
            'user_id' => $user_id,
            'user_type' => $user_type,
            'user_name' => $user_name,
            'created_at' => time(),
            'ip' => Yii::$app->request->userIP,
        ];
        
        $replies[] = $reply;
        
        $this->reply_list = json_encode($replies, JSON_UNESCAPED_UNICODE);
        $this->reply_count = count($replies);
        $this->last_reply_time = time();
        $this->last_reply_user_id = $user_id;
        $this->last_reply_user_type = $user_type;
        
        // 如果是管理员回复，且工单状态为待处理，则更新为处理中
        if ($user_type == 2 && $this->status == 1) {
            $this->status = 2;
        }
        
        return $this->save();
    }

    /**
     * 商户
     * @return \yii\db\ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }
}
