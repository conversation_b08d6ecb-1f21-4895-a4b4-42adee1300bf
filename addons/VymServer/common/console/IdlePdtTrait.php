<?php

namespace addons\VymServer\common\console;

use addons\VymDesen\backend\models\CustomPartitionSetting;
use addons\VymDesen\backend\models\IdlePdt;
use addons\VymDesen\backend\models\ReinstallConfirm;
use addons\VymDesen\common\components\CloudBootApi;
use addons\VymDesen\common\components\IpmiApi;
use addons\VymDesen\common\components\SwitchRequest;
use addons\VymDesen\common\models\CloudSystemModel;
use addons\VymDesen\common\models\CloudSystemModelClass;
use addons\VymDesen\common\models\Member\MemberPdt;
use addons\VymDesen\common\models\Pdt\PdtCabinetManage;
use addons\VymDesen\common\models\ServerDorecord;
use common\helpers\ArrayHelper;
use ErrorException;
use Yii;

trait IdlePdtTrait
{
    /**
     *    重装系统
     */
    public function reloadSystem($post)
    {
        $CloudSystemModel = new CloudSystemModel();
        $CloudSystemModelClassModel = new CloudSystemModelClass();
        $IdlePdtModel = new IdlePdt();
        $CabinetModel = new PdtCabinetManage();
        $CustomPartitionSettingModel = new CustomPartitionSetting();

        #开启事务
        $transaction = \Yii::$app->db->beginTransaction();

        $CloudSystemRes = $CloudSystemModel->find()->where(['cloud_system_id' => $post['cloudsystem']])->asArray()->one();
        if (empty($CloudSystemRes)) {
            $arrReturn = [
                'status' => 0,
                'info' => "未知的模板信息",
            ];
            return $this->renderJSON($arrReturn);
        }
        $SystemModelClassRes = $CloudSystemModelClassModel->find()->where(['class_id' => $CloudSystemRes['class_id']])->asArray()->one();
        if (empty($SystemModelClassRes)) {
            $arrReturn = [
                'status' => 0,
                'info' => "未知的模板分类",
            ];
            return $this->renderJSON($arrReturn);
        }
        #如果选择为自定义分区类
        if ($SystemModelClassRes['custom_partition'] == 'Y') {
            $type = $post['partition_type'];
            $file_system_type = $post['file_system_type'];
            $swap_size = $post['swap_partition_size'];
            $root_size = $post['root_partition_size'];
            $home_size = $post['home_partition_size'];
            $www_size = $post['www_partition_size'];
            $data_size = $post['data_partition_size'];
            #判断参数
            if (!in_array($type, ['LVM', 'Stardard'])) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info' => "分区类型错误",
                ];
                return $this->renderJSON($arrReturn);
            }

            if (!in_array($file_system_type, ['ext4', 'xfs'])) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info' => "文件系统类型错误",
                ];
                return $this->renderJSON($arrReturn);
            }

            if (!$swap_size) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info' => "swap分区大小必须填写",
                ];
                return $this->renderJSON($arrReturn);
            } else {
                if (!preg_match("/^[1-9][0-9]*$/", $swap_size)) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info' => "swap分区大小填写有误",
                    ];
                    return $this->renderJSON($arrReturn);
                } else {
                    if ($swap_size < 2 || $swap_size > 32) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info' => "swap分区大小值范围在2-32之间",
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
                $swap_size = strval($swap_size * 1024);
            }

            if (!$root_size) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info' => "根分区大小必须填写",
                ];
                return $this->renderJSON($arrReturn);
            } else {
                if ($root_size == 'all') {
                    $root_size = 'all';
                } else {
                    if (!preg_match("/^[1-9][0-9]*$/", $swap_size)) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info' => "根分区大小填写有误",
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                    $root_size = strval($root_size * 1024);
                }
            }

            if ($home_size) {
                if ($home_size == 'all') {
                    $home_size = 'all';
                } else {
                    if (!preg_match("/^[1-9][0-9]*$/", $home_size)) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info' => "Home分区大小填写有误",
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                    $home_size = strval($home_size * 1024);
                }
            } else {
                $home_size = '0';
            }

            if ($www_size) {
                if ($www_size == 'all') {
                    $www_size = 'all';
                } else {
                    if (!preg_match("/^[1-9][0-9]*$/", $www_size)) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info' => "WWW分区大小填写有误",
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                    $www_size = strval($www_size * 1024);
                }
            } else {
                $www_size = '0';
            }

            if ($data_size) {
                if ($data_size == 'all') {
                    $data_size = 'all';
                } else {
                    if (!preg_match("/^[1-9][0-9]*$/", $data_size)) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info' => "Data分区大小填写有误",
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                    $data_size = strval($data_size * 1024);
                }
            } else {
                $data_size = '0';
            }

            #boot  home  www  data中只能存在一个all
            $check_arr = [$root_size, $home_size, $www_size, $data_size];
            $value_num = array_count_values($check_arr);
            if ($value_num['all'] > 1) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info' => "只能有一个参数值为all",
                ];
                return $this->renderJSON($arrReturn);
            }

        }
        $IdlePdtRes = $IdlePdtModel->find()->With('switch')->where(['id' => $post['id']])->asArray()->one();

        if (empty($IdlePdtRes)) {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info' => "未知的机器",
            ];
            return $this->renderJSON($arrReturn);
        }

        $Sn = $IdlePdtRes['sn_code'];
        if ($Sn == "" || $Sn == null) {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info' => "SN码为空",
            ];
            return $this->renderJSON($arrReturn);
        }
        //获取机柜信息
        $cabinet_id = $IdlePdtRes['cabinet_id'];
        if ($cabinet_id == "" || $cabinet_id == null) {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info' => "该机器未选择机柜信息",
            ];
            return $this->renderJSON($arrReturn);
        } else {
            $CabinetRes = $CabinetModel->find()->where(['id' => $cabinet_id])->asArray()->one();
            if (empty($CabinetRes)) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info' => "未知的机柜信息",
                ];
                return $this->renderJSON($arrReturn);
            } else {
                $cloudboot_api = $CabinetRes['cloudboot_api'];
                $cloudboot_key = $CabinetRes['cloudboot_key'];
                if ($cloudboot_api == "" || $cloudboot_api == null) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info' => '对应机柜未填写云装机API地址',
                    ];
                    return $this->renderJSON($arrReturn);
                }
                if ($cloudboot_key == "" || $cloudboot_key == null) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info' => '对应机柜未填写云装机密钥',
                    ];
                    return $this->renderJSON($arrReturn);
                }

            }
        }
        #
        $ipmi_ip = $IdlePdtRes['ipmi_ip'];
        $ipmi_name = $IdlePdtRes['ipmi_name'];
        $ipmi_pwd = $IdlePdtRes['ipmi_pwd']; #echo $ipmi_name;echo $ipmi_pwd;exit;
        if ($ipmi_ip == "" || $ipmi_ip == null) {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info' => '该自有服务器未填写IPMI IP地址',
            ];
            return $this->renderJSON($arrReturn);
        } else {
            if (!filter_var($ipmi_ip, FILTER_VALIDATE_IP)) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info' => $ipmi_ip . '为不合法的IP地址',
                ];
                return $this->renderJSON($arrReturn);
            }
        }

        if ($ipmi_name == "" || $ipmi_name == null || $ipmi_pwd == "" || $ipmi_pwd == null) {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info' => 'IPMI 账户名或者密码未设置',
            ];
            return $this->renderJSON($arrReturn);
        }
        $ReinstallConfirmModel = new ReinstallConfirm();
        $ReinstallConfirmRes = $ReinstallConfirmModel->find()->where(['idle_pdt_id' => $post['id'], 'status' => '待确认'])->asArray()->one();
        if ($ReinstallConfirmRes) {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info' => '该机器已有重装请求未处理，如若继续，请先取消重装',
            ];
            return $this->renderJSON($arrReturn);
        }

        #如果选择为自定义分区类，将分区设置新增到数据库中
        if ($SystemModelClassRes['custom_partition'] == 'Y') {
            $PartitionSettingQuery = $CustomPartitionSettingModel->find()->where(['sn_code' => $Sn])->one();
            if ($PartitionSettingQuery) {
                #更新
                $PartitionSettingQuery->type = $type;
                $PartitionSettingQuery->file_system_type = $file_system_type;
                $PartitionSettingQuery->swap_size = $swap_size;
                $PartitionSettingQuery->root_size = $root_size;
                $PartitionSettingQuery->home_size = $home_size;
                $PartitionSettingQuery->www_size = $www_size;
                $PartitionSettingQuery->data_size = $data_size;
                $PartitionSettingQuery->update_time = time();

                $updatePartitionSetting = $PartitionSettingQuery->update();
                if (!$updatePartitionSetting) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info' => '更新自定义分区设置信息失败',
                    ];
                    return $this->renderJSON($arrReturn);
                }
            } else {
                #新增
                $CustomPartitionSettingModel->sn_code = $Sn;
                $CustomPartitionSettingModel->type = $type;
                $CustomPartitionSettingModel->file_system_type = $file_system_type;
                $CustomPartitionSettingModel->swap_size = $swap_size;
                $CustomPartitionSettingModel->root_size = $root_size;
                $CustomPartitionSettingModel->home_size = $home_size;
                $CustomPartitionSettingModel->www_size = $www_size;
                $CustomPartitionSettingModel->data_size = $data_size;
                $CustomPartitionSettingModel->create_time = time();

                $insertPartitionSetting = $CustomPartitionSettingModel->insert();
                if (!$insertPartitionSetting) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info' => '新增自定义分区设置信息失败',
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }
        }
        #加入重装确认
        $ReinstallConfirmModel->idle_pdt_id = $post['id'];
        $ReinstallConfirmModel->system_id = $CloudSystemRes['cloud_system_id'];
        $ReinstallConfirmModel->system_name = $CloudSystemRes['cloud_system_name'];
        $ReinstallConfirmModel->ipmi_ip = $ipmi_ip;
        $ReinstallConfirmModel->admin_id = 0;
        $ReinstallConfirmModel->admin_name = '';
        $ReinstallConfirmModel->request_time = time();
        $ReinstallConfirmModel->status = '待确认';

        if ($ReinstallConfirmModel->insert()) {
            $transaction->commit();
            $arrReturn = [
                'status' => 1,
                'info' => '已加入申请，请等待审核确认',
            ];
        } else {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info' => '加入申请失败，请稍后重试',
            ];
        }
        return $this->renderJSON($arrReturn);
    }

    /**
     * 获取系统模板
     */
    public function getCloudsystem($post)
    {
        $class_id = ArrayHelper::getValue($post, 'class_id');
        $class_category = ArrayHelper::getValue($post, 'class_category');
        $CloudSystemModelClassModel = new CloudSystemModelClass();
        if ($class_id != '') {
            $condition = [
                'class_id' => $post['class_id'],
            ];
        } else if ($class_category != '') {
            $classid_arr = $CloudSystemModelClassModel->find()->select('class_id')->where(['class_category' => $post['class_category']])->column();
            $condition = [
                'class_id' => $classid_arr,
            ];
        } else {
            $condition = [];
        }
        $CloudSystemModel = new CloudSystemModel();
        $CloudSystemRes = $CloudSystemModel->find()->where(['status' => 'Y'])->andWhere($condition)->orderBy('sort_number asc')->asArray()->all();#print_r($CloudSystemRes);die;

        $arrReturn = [
            'status' => 1,
            'info' => $CloudSystemRes,
        ];
        return $this->renderJSON($arrReturn);
    }

    /**
     * 镜像列表
     */
    public function getMirrors($params)
    {
        $CloudSystemModelClassModel = new CloudSystemModelClass();
        $class_id = ArrayHelper::getValue($params, 'class_id');
        $class_cat = ArrayHelper::getValue($params, 'class_category');
        if ($class_id != '') {
            $condition = [
                'class_id' => $class_id,
            ];
        } else if ($class_cat != '') {
            $classid_arr = $CloudSystemModelClassModel->find()->select('class_id')->where(['class_category' => $class_cat])->column();
            $condition = [
                'class_id' => $classid_arr,
            ];
        } else {
            $condition = [];
        }
        $CloudSystemModel = new CloudSystemModel();
        $CloudSystemRes = $CloudSystemModel->find()->where(['status' => 'Y'])->andWhere($condition)->orderBy('sort_number asc')->asArray()->all();#print_r($CloudSystemRes);die;

        return [
            'status' => 1,
            'info' => $CloudSystemRes,
        ];
    }

    public function getMirrorTypes($params)
    {
        $CloudSystemModelClassModel = new CloudSystemModelClass();
        $CloudSystemModelClassRes = $CloudSystemModelClassModel->find()->where(['class_category' => '重装系统'])->orderBy('sort_number asc')->asArray()->all();
        return ['status' => 1, 'info' => $CloudSystemModelClassRes];
    }

    public function doPowerOperate($params, $action)
    {
        $str_action = ArrayHelper::getValue($params, 'action', $action);
        $member_id = ArrayHelper::getValue($params, 'member_id');
        $user_id = ArrayHelper::getValue($params, 'user_id');

        if (!$member_id || !$str_action) {
            throw new ErrorException('参数错误');
        }

        $_action_allow = array('on', 'off', );
        if (!in_array($str_action, $_action_allow)) {
            throw new ErrorException('执行的操作异常');
        }
        //操作人员信息
        $operator = $this->uname ?? 'user';
        $operator_role = $this->uname ? '管理员' : '用户';

        $condition = ['id' => $member_id];
        if ($user_id) {
            $condition['user_id'] = $user_id;
        }

        //查询服务器
        $detail = (new MemberPdt())->find()->select('*')
            ->With('servertype')->With('idlepdt')->With('pdtmanage')->With('pdttype')
            ->where($condition)->asArray()->one();

        $CabinetModel = new PdtCabinetManage();
        if (!$detail) {
            throw new ErrorException('未知的服务器');
        }

        #交换机信息
        $switch_ip = ArrayHelper::getValue($detail, 'idlepdt.switch.0.ip');             ##交换机IP
        $login_port = ArrayHelper::getValue($detail, 'idlepdt.switch.0.login_port');     ##交换机登录端口
        $login_user = ArrayHelper::getValue($detail, 'idlepdt.switch.0.login_user');     ##交换机登录账户
        $login_password = ArrayHelper::getValue($detail, 'idlepdt.switch.0.login_password'); ##交换机登录密码
        $switch_port = ArrayHelper::getValue($detail, 'idlepdt.switch_port');             ##交换机端口

        #获取机柜相关信息
        $cabinet_id = ArrayHelper::getValue($detail, 'cabinet_id');
        if (!$cabinet_id) {
            //该服务器未选择机柜
            throw new ErrorException('该服务器配置异常(001)');
        }

        $CabinetRes = $CabinetModel->find()->where(['id' => $cabinet_id])->AsArray()->one();
        if (!$CabinetRes) {
            throw new ErrorException('该服务器配置异常(002)'); //根据机柜ID未找到
        }

        $cloudBoot_key = ArrayHelper::getValue($CabinetRes, 'cloudboot_key');
        $cloudBoot_api = ArrayHelper::getValue($CabinetRes, 'cloudboot_api');
        $cloudBoot_node = ArrayHelper::getValue($CabinetRes, 'cloudboot_node');
        if (!$cloudBoot_api) {
            throw new ErrorException('该服务器配置异常(003)'); //对应机柜未填写云装机API地址
        }
        if (!$cloudBoot_key) {
            throw new ErrorException('该服务器配置异常(004)'); //对应机柜未填写云装机密钥
        }

        $ipmi_ip = ArrayHelper::getValue($detail, 'ipmi_ip');
        //判定机柜属性  1为 独立服务器   2为云主机 VPS
        if (ArrayHelper::getValue($CabinetRes, 'attribute') == 1) {
            if (!$ipmi_ip) {
                throw new ErrorException('该服务器配置异常(005)'); //该服务器未填写IPMI IP地址
            }
            if (!filter_var($ipmi_ip, FILTER_VALIDATE_IP)) {
                throw new ErrorException('该服务器配置异常(006)'); //该服务器IPMI IP地址,不合法的IP地址
            }

            $ipmi_name = ArrayHelper::getValue($detail, 'ipmi_name');
            $ipmi_pwd = ArrayHelper::getValue($detail, 'ipmi_pwd');
            if (!$ipmi_name || !$ipmi_pwd) {
                throw new ErrorException('该服务器配置异常(007)'); //该服务器IPMI 账户/密码未设置
            }

            //对应命令
            $_actions = ['on' => 'portup', 'off' => 'portdown', 'reset' => 'reset'];
            $_action = ArrayHelper::getValue($_actions, $str_action, 'portup');

            #对端口状态进行操作
            if ($str_action != 'reset') {
                $UpStatusRes = SwitchRequest::UpdatePortStatus($cloudBoot_api, $switch_ip, $login_port, $login_user, $login_password, $switch_port, $cloudBoot_key, $_action);
                // $UpStatusRes = ['status'=>0];
                $upStatus = ArrayHelper::getValue($UpStatusRes, 'status');
                if ($upStatus != 0) {
                    $message = ArrayHelper::getValue($UpStatusRes, 'Message', '执行失败');
                    throw new ErrorException("该服务器操作异常({$message})"); //操作异常
                }
            }
            #print_r($GetRes);die;

            #进行开关机操作
            $sendRes = IpmiApi::sendipmirequest($cloudBoot_api, $str_action, $ipmi_ip, $ipmi_name, $ipmi_pwd, $cloudBoot_key);
            // $sendRes = ['Status'=>'success'];
            #print_r($sendRes);exit;
            $status = ArrayHelper::getValue($sendRes, 'Status');
            $message = ArrayHelper::getValue($sendRes, 'Message', '执行失败');
            if ($status !== "success") {
                throw new ErrorException("该服务器操作异常({$message})"); //操作异常
            }

            $reData = [];

            #新增操作记录
            // $recordRes = (new IdleMachineOperationRecord())->adding_record(ArrayHelper::getValue($detail, 'idle_id'), $ipmi_ip, ArrayHelper::getValue($detail, 'ip'), ArrayHelper::getValue($detail, 'ip2'), $str_action, ArrayHelper::getValue($this->_user,'uname'), '用户');
            switch (trim($message)) {
                case "Chassis Power Control: Up/On":
                    $reData = [
                        'success' => true,
                        'status' => 1,
                        'msg' => '服务器电源状态：已打开'
                    ];
                    break;
                case "Chassis Power Control: Down/Off":
                    $reData = [
                        'success' => true,
                        'status' => 0,
                        'msg' => '服务器电源状态：已关闭'
                    ];
                    break;
                case "Chassis Power Control: Reset":
                    $reData = [
                        'success' => true,
                        'status' => 1,
                        'msg' => '服务器已重启，请过2 - 5分钟后测试'
                    ];
                    break;
                default:
                    $reData = [
                        'success' => true,
                        'status' => -1,
                        'msg' => '服务器电源状态：未知,请重试',
                    ];
            }
            ArrayHelper::setValue($reData, 'upStatusRes', $UpStatusRes);//把请求的结果赋值给返回数组去
            ArrayHelper::setValue($reData, 'sendRes', $sendRes);        //把请求的结果赋值给返回数组去
        } else {
            $sn_code = ArrayHelper::getValue($detail, 'idlepdt.sn_code');
            if (!$sn_code) {
                throw new ErrorException('该服务器配置异常(0001)'); //SN码未设置
            }

            // 登录获取令牌
            $GetTokenRes = IpmiApi::LoginGetToken($cloudBoot_api, $cloudBoot_key);
            if (!ArrayHelper::getValue($GetTokenRes, 'data')) {
                throw new ErrorException('该服务器配置异常(0002)'); //登录获取令牌失败
            }

            // 获取电源状态
            $vmid = preg_replace('/[^\d]*/', '', $sn_code);
            $token = ArrayHelper::getValue($GetTokenRes, 'data.CSRFPreventionToken');
            $ticket = ArrayHelper::getValue($GetTokenRes, 'data.ticket');
            $statusRes = IpmiApi::GetStatus($cloudBoot_api, $vmid, $ticket, $cloudBoot_node);
            if (!ArrayHelper::getValue($statusRes, 'data')) {
                throw new ErrorException('该服务器配置异常(0003)'); //获取电源状态失败
            }

            // 根据状态判断
            $status = ArrayHelper::getValue($statusRes, 'data.status');
            $success_message = '操作成功';
            $error_message = '操作失败';
            if ($str_action == 'on') {
                if ($status == 'running') {
                    throw new ErrorException('当前机器已在运行中,不需要启动'); //当前机器已在运行中
                }
                $success_message = '开机操作成功';
                $error_message = '开机操作失败';
            } else if ($str_action == 'off') {
                if ($status == 'stopped') {
                    throw new ErrorException('当前机器已处于关机,不需要关机'); //当前机器已处于关机
                }
                $success_message = '关机操作成功';
                $error_message = '关机操作失败';
            } else if ($str_action == 'reset') {
                $str_action = $status == 'stopped' ? 'on' : $str_action;
                $success_message = '重启操作成功';
                $error_message = '重启操作失败';
            }

            // 执行操作
            $OperationRes = IpmiApi::PowerOperation($cloudBoot_api, $str_action, $vmid, $ticket, $token, $cloudBoot_node);
            if (!ArrayHelper::getValue($OperationRes, 'data')) {
                throw new ErrorException($error_message); //执行操作失败
            }
            #新增操作记录
            // $recordRes = (new IdleMachineOperationRecord())->adding_record(ArrayHelper::getValue($detail, 'idle_id'), $ipmi_ip, ArrayHelper::getValue($detail, 'ip'), ArrayHelper::getValue($detail, 'ip2'), $str_action, ArrayHelper::getValue($this->_user,'uname'), '用户');
            $reData = [
                'success' => true,
                'status' => 1,
                'msg' => $success_message
            ];
            ArrayHelper::setValue($reData, 'statusRes', $statusRes);      //把请求的结果赋值给返回数组去
            ArrayHelper::setValue($reData, 'operationRes', $OperationRes);//把请求的结果赋值给返回数组去
        }
        $this->pushLogData('idle_id', ArrayHelper::getValue($detail, 'idle_id'));
        $this->pushLogData('ipmi_ip', $ipmi_ip);
        $this->pushLogData('idle_ip', ArrayHelper::getValue($detail, 'idlepdt.ip'));
        $this->pushLogData('idle_ip2', ArrayHelper::getValue($detail, 'idlepdt.ip2'));

        //查询结束，返回结果
        return $reData;
    }

    /**
     * 开关状态
     */
    public function doPowerState($params)
    {
        $str_action = ArrayHelper::getValue($params, 'action', 'status');
        $member_id = ArrayHelper::getValue($params, 'member_id');
        $user_id = ArrayHelper::getValue($params, 'user_id');

        //操作人员信息
        $operator = $this->uname ?? 'user';
        $operator_role = $this->uname ? '管理员' : '用户';
        $condition = ['id' => $member_id];
        if ($user_id) {
            $condition['user_id'] = $user_id;
        }

        $detail = (new MemberPdt())->find()->select('*')
            ->With('servertype')->With('idlepdt')->With('pdtmanage')->With('pdttype')
            ->where($condition)->asArray()->one();

        $CabinetModel = new PdtCabinetManage();
        if (!$detail) {
            throw new ErrorException('未知的服务器');
        }

        $cabinet_id = ArrayHelper::getValue($detail, 'cabinet_id');
        #获取机柜相关信息
        if (!$cabinet_id) {
            //该服务器未选择机柜
            throw new ErrorException('该服务器配置异常(001)');
        }

        $CabinetRes = $CabinetModel->find()->where(['id' => $cabinet_id])->AsArray()->one();

        if (!$CabinetRes) {
            throw new ErrorException('该服务器配置异常(002)'); //根据机柜ID未找到
        }

        $cloudBoot_key = ArrayHelper::getValue($CabinetRes, 'cloudboot_key');
        $cloudBoot_api = ArrayHelper::getValue($CabinetRes, 'cloudboot_api');
        $cloudBoot_node = ArrayHelper::getValue($CabinetRes, 'cloudboot_node');
        if (!$cloudBoot_api) {
            throw new ErrorException('该服务器配置异常(003)'); //对应机柜未填写云装机API地址
        }
        if (!$cloudBoot_key) {
            throw new ErrorException('该服务器配置异常(004)'); //对应机柜未填写云装机密钥
        }

        $ipmi_ip = ArrayHelper::getValue($detail, 'ipmi_ip');
        //判定机柜属性  1为 独立服务器   2为云主机 VPS
        if (ArrayHelper::getValue($CabinetRes, 'attribute') == 1) {
            if (!$ipmi_ip) {
                throw new ErrorException('该服务器配置异常(005)'); //该服务器未填写IPMI IP地址
            }
            if (!filter_var($ipmi_ip, FILTER_VALIDATE_IP)) {
                throw new ErrorException('该服务器配置异常(006)'); //该服务器IPMI IP地址,不合法的IP地址
            }

            $ipmi_name = ArrayHelper::getValue($detail, 'ipmi_name');
            $ipmi_pwd = ArrayHelper::getValue($detail, 'ipmi_pwd');
            if (!$ipmi_name || !$ipmi_pwd) {
                throw new ErrorException('该服务器配置异常(007)'); //该服务器IPMI 账户/密码未设置
            }

            //请求执行操作
            $sendRes = ['Status' => 'success', 'Message' => ''];
            //IpmiApi::sendipmirequest($cloudBoot_api, $str_action, $ipmi_ip, $ipmi_name, $ipmi_pwd, $cloudBoot_key);
            $status = ArrayHelper::getValue($sendRes, 'Status');
            $message = ArrayHelper::getValue($sendRes, 'Message');
            if ($status !== "success") {
                throw new ErrorException("该服务器配置异常({$message})"); //查询异常
            }

            switch (trim($message)) {
                case 'Chassis Power is off':
                    $reData = [
                        'success' => true,
                        'status' => 0,
                        'msg' => '服务器电源状态：已关闭',
                        'power' => 'off',
                    ];
                    break;
                case "Chassis Power is on":
                    $reData = [
                        'success' => true,
                        'status' => 1,
                        'msg' => '服务器电源状态：运行中',
                        'power' => 'on',
                    ];
                    break;
                default:
                    $reData = [
                        'success' => true,
                        'status' => -1,
                        'msg' => '服务器电源状态：未知,请重试',
                        'power' => 'unknown',
                    ];
            }
            ArrayHelper::setValue($reData, 're', $sendRes);//把请求的结果赋值给返回数组去
        } else {
            $sn_code = ArrayHelper::getValue($detail, 'idlepdt.sn_code');
            if (!$sn_code) {
                throw new ErrorException('该服务器配置异常(0001)'); //SN码未设置
            }

            $vmid = preg_replace('/[^\d]*/', '', $sn_code);
            $GetTokenRes = IpmiApi::LoginGetToken($cloudBoot_api, $cloudBoot_key);
            if (!ArrayHelper::getValue($GetTokenRes, 'data')) {
                throw new ErrorException('该服务器配置异常(0002)'); //登录获取令牌失败
            }
            $token = ArrayHelper::getValue($GetTokenRes, 'data.CSRFPreventionToken');
            $ticket = ArrayHelper::getValue($GetTokenRes, 'data.ticket');
            $GetStatusRes = IpmiApi::GetStatus($cloudBoot_api, $vmid, $ticket, $cloudBoot_node);
            if (!ArrayHelper::getValue($GetStatusRes, 'data')) {
                throw new ErrorException('该服务器配置异常(0003)'); //获取电源状态失败
            }
            #新增操作记录
            // $recordRes = (new IdleMachineOperationRecord())->adding_record(ArrayHelper::getValue($detail, 'idlepdt.id'), $ipmi_ip, ArrayHelper::getValue($detail, 'idlepdt.ip'), ArrayHelper::getValue($detail, 'idlepdt.ip2'), $str_action, 'user', '用户');
            $status = ArrayHelper::getValue($GetStatusRes, 'data.status');
            if ($status == 'running') {
                $reData = [
                    'success' => true,
                    'status' => 1,
                    'msg' => '服务器电源状态：运行中',
                    'power' => 'on',
                ];
            } else {
                $reData = [
                    'success' => true,
                    'status' => 0,
                    'msg' => '服务器电源状态：已关闭',
                    'power' => 'off',
                ];
            }
            ArrayHelper::setValue($reData, 're', $GetStatusRes);//把请求的结果赋值给返回数组去
        }
        $this->pushLogData('idle_id', ArrayHelper::getValue($detail, 'idlepdt.idle_id'));
        $this->pushLogData('ipmi_ip', $ipmi_ip);
        $this->pushLogData('idle_ip', ArrayHelper::getValue($detail, 'ip'));
        $this->pushLogData('idle_ip2', ArrayHelper::getValue($detail, 'ip2'));
        return $reData;
    }

    #取消重装
    public function doCancelReload($post)
    {
        $id                    = ArrayHelper::getValue($post, 'id');
        $IdlePdtModel          = new IdlePdt();
        $ServerRecordModel     = new ServerDorecord();
        $ReinstallConfirmModel = new ReinstallConfirm();

        $IdlePdtRes = $IdlePdtModel->find()->With('switch')->With('pdtcabinet')->where(['id' => $id])->asArray()->one();
        if (empty($IdlePdtRes)) {
            $arrReturn = [
                'status' => 0,
                'info'   => "未知的机器",
            ];
            return $this->renderJSON($arrReturn);
        }
        $Sn = $IdlePdtRes['sn_code'];
        #开启事务
        $transaction = \Yii::$app->db->beginTransaction();
        //记录操作日志记录
        $recordRes = $ServerRecordModel->find()->where(['do_idleid' => $IdlePdtRes['id'], 'do_status' => '处理中'])->one();

        if ($recordRes) {
            $recordRes->do_status = '取消处理';
            $recordRes->save();

            $cloudboot_api = $IdlePdtRes['pdtcabinet'][0]['cloudboot_api'];
            $cloudboot_key = $IdlePdtRes['pdtcabinet'][0]['cloudboot_key'];

            $ReinstallConfirmRes = $ReinstallConfirmModel->find()->where(['idle_pdt_id' => $IdlePdtRes['id'], 'status' => '已确认'])->orderBy('request_time desc')->asArray()->one();
            #print_r($ReinstallConfirmRes);exit;
            $ReinstallConfirmQuery = $ReinstallConfirmModel->findone($ReinstallConfirmRes['id']);

            if (!empty($ReinstallConfirmQuery)) {
                $ReinstallConfirmQuery->status = '已取消';

                if (!$ReinstallConfirmQuery->update()) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '重装确认记录更新失败，取消失败',
                    ];
                }
            }

            //请求取消重装接口
            $CancelRes = CloudBootApi::osinstall_cancelReload($cloudboot_api, $Sn, $cloudboot_key);//print_r($CancelRes);die;

            if ($CancelRes['Status'] == 'success') {
                $transaction->commit();
                $arrReturn = [
                    'status' => 1,
                    'info'   => '操作成功',
                ];
            } else {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => $CancelRes['Message'],
                ];
            }
        } else {
            $ReinstallConfirmRes = $ReinstallConfirmModel->find()->where(['idle_pdt_id' => $IdlePdtRes['id'], 'status' => '待确认'])->orderBy('request_time desc')->asArray()->one();
            if (!$ReinstallConfirmRes) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '系统重装未在处理中状态',
                ];
                return $this->renderJSON($arrReturn);
            } else {
                $ReinstallConfirmQuery         = $ReinstallConfirmModel->findone($ReinstallConfirmRes['id']);
                $ReinstallConfirmQuery->status = '已取消';
                if ($ReinstallConfirmQuery->update()) {
                    $transaction->commit();
                    $arrReturn = [
                        'status' => 1,
                        'info'   => '操作成功',
                    ];
                } else {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '重装确认记录更新失败，取消失败',
                    ];
                }
            }
        }
        return $this->renderJSON($arrReturn);
    }

    #KVM
    public function kvmManage($post)
    {
        $idle_id = ArrayHelper::getValue($post, 'idle_id');
        $IdlePdtModel = new IdlePdt();
        $IdlePdtRes = $IdlePdtModel->find()->where(['id' => $idle_id])->asArray()->one();

        if (empty($IdlePdtRes)) {
            return $this->renderJSON(['status' => 0, 'info' => '未知的自有机器信息']);
        }

        $ipmi_ip = $IdlePdtRes['ipmi_ip'];

        if ($ipmi_ip == '' || $ipmi_ip == null) {
            return $this->renderJSON(['status' => 0, 'info' => '未配置IPMI信息']);
        }

        $vnc_pwd = uniqid();           #随机密码
        $port    = rand(20000, 30000); #随机端口
        #判断是否已经存在这个IPMI的容器
        $CheckResult = IpmiApi::InspectContainer($ipmi_ip);
        if ($CheckResult == 500) {
            return $this->renderJSON(['status' => 0, 'info' => '检查容器出现错误']);
        }
        $container_name = null;
        if ($CheckResult == 200) {
            $container_name = $ipmi_ip . '-' . $port;
        }

        if ($CheckResult == 404) {
            $container_name = $ipmi_ip;
        }
        if(!$container_name){
            $container_name = $ipmi_ip;
        }
        #
        $ListRes = IpmiApi::ListContainers();

        if (!empty($ListRes)) {
            $check_str = '/' . $ipmi_ip;
            $nams_list = [];
            $num       = 0;
            foreach ($ListRes as $key => $val) {
                $names = $val['Names'][0];
                if (strpos($names, $check_str) !== false) {
                    $num += 1;
                }
            }

            if ($num >= 3) {
                return $this->renderJSON(['status' => 0, 'info' => '当前连接数超过限制，请先关闭部分连接，稍后再重新连接']);
            }
        }
        #创建容器
        $CreateResult = IpmiApi::ContainerCreate($container_name, $ipmi_ip, $IdlePdtRes['ipmi_type'], $IdlePdtRes['ipmi_name'], $IdlePdtRes['ipmi_pwd'], $vnc_pwd, $port);

        if ($CreateResult != 201) {
            return $this->renderJSON(['status' => 0, 'info' => '创建容器失败']);
        }
        #启动容器
        $StartResult = IpmiApi::StartContainer($container_name);

        if ($StartResult != 204) {
            return $this->renderJSON(['status' => 0, 'info' => '创建容器失败，状态码：' . $StartResult]);
        } else {
            $kvm_config = Yii::$app->params['kvm'];
            $url        = $kvm_config['api_url'];

            $kvm_url = $url . ':' . $port . '/vnc.html?autoconnect=true&password=' . $vnc_pwd . '&resize=scale';
            return $this->renderJSON(['status' => 1, 'info' => $kvm_url]);
        }

    }
}