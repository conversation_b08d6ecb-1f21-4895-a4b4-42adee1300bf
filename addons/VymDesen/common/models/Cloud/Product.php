<?php

namespace addons\VymDesen\common\models\Cloud;

use common\behaviors\MerchantStoreBehavior;
use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "module_cloud_product".
 *
 * @property int         $id
 * @property string      $title         标题
 * @property string|null $desc          简短描述
 * @property string|null $content       内容
 * @property int|null    $status        状态
 * @property int|null    $sort          排序
 * @property int|null    $time_add      创建时间
 * @property int|null    $time_up       更新时间
 * @property string|null $cluster_ids   所属集群
 * @property string|null $server_config 服务器配置
 * @property string|null $price_config  价格配置表
 * @property string|null $price_upgrade 配置升级价格表
 * @property string|null $options       其他配置
 * @property int|null    $stock         库存
 * @property int|null    $merchant_id   商户id
 * @property int|null    $store_id      店铺id
 */
class Product extends \yii\db\ActiveRecord
{

    use MerchantStoreBehavior;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'module_cloud_product';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title'], 'required'],
            [['content', 'cluster_ids'], 'string'],
            [['merchant_id', 'store_id'], 'default', 'value' => 0],
            [['status', 'sort', 'time_add', 'time_up', 'stock'], 'integer'],
            [['server_config', 'price_config', 'price_upgrade', 'options'], 'safe'],
            [['title', 'desc'], 'string', 'max' => 200],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'            => Yii::t('app', 'ID'),
            'title'         => Yii::t('app', '标题'),
            'desc'          => Yii::t('app', '简短描述'),
            'content'       => Yii::t('app', '内容'),
            'status'        => Yii::t('app', '状态'),
            'sort'          => Yii::t('app', '排序'),
            'time_add'      => Yii::t('app', '创建时间'),
            'time_up'       => Yii::t('app', '更新时间'),
            'cluster_ids'   => Yii::t('app', '所属集群'),
            'server_config' => Yii::t('app', '服务器配置'),
            'price_config'  => Yii::t('app', '价格配置表'),
            'price_upgrade' => Yii::t('app', '配置升级价格表'),
            'options'       => Yii::t('app', '其他配置'),
            'stock'         => Yii::t('app', '库存'),
            'merchant_id'   => Yii::t('app', '商户id'),
            'store_id'      => Yii::t('app', '店铺id'),
        ];
    }


    /**
     * @param $params
     *
     * @return \yii\db\ActiveQuery
     * @throws \Exception
     */
    public function search($params)
    {
        $query = self::find();

        $query->andFilterWhere([
            'area_id' => ArrayHelper::getValue($params, 'area_id'),
        ]);
        $query->andFilterWhere(['like', 'cluster_name', ArrayHelper::getValue($params, 'cluster_name')]);

        return $query;
    }
}
