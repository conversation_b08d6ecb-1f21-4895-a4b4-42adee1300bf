<?php

namespace addons\VymServer\merchant\controllers;

use addons\VymServer\common\models\ServerOrder;
use addons\VymServer\common\models\ServerOrderSearch;
use common\models\base\SearchModel;
use addons\VymServer\merchant\controllers\BaseController;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * OrderController implements the CRUD actions for ServerOrder model.
 */
class OrderController extends BaseController
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class'   => VerbFilter::className(),
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }

    /**
     * @var ServerOrder
     */
    public $modelClass = ServerOrder::class;

    /**
     * Lists all ServerOrder models.
     *
     * @return string
     */
    public function actionIndex()
    {
        $searchModel = new ServerOrderSearch();

        $dataProvider = $searchModel->search($this->request->queryParams);
        $this->addQueryParam($dataProvider, 's');
        return $this->render('index', [
            'dataProvider' => $dataProvider,
            'searchModel'  => $searchModel,
        ]);
    }

    /**
     * Displays a single ServerOrder model.
     *
     * @param int $id ID
     *
     * @return string
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);

        // 获取完整的订单信息
        $orderFullInfo = \Yii::$app->vymServerService->order->getOrderFullInfo($model);

        return $this->render('view', [
            'model'         => $model,
            'orderFullInfo' => $orderFullInfo,
        ]);
    }

    /**
     * Creates a new ServerOrder model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return string|\yii\web\Response
     */
    public function actionCreate()
    {
        $model = new ServerOrder();

        if ($this->request->isPost) {
            if ($model->load($this->request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
        } else {
            $model->loadDefaultValues();
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing ServerOrder model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param int $id ID
     *
     * @return string|\yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($this->request->isPost && $model->load($this->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing ServerOrder model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param int $id ID
     *
     * @return \yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ServerOrder model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id ID
     *
     * @return ServerOrder the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        $merId = $this->getMerchantId();

        $query = ServerOrder::find()
                            ->with([
                                'original',    // 主订单关联
                                'general',     // 付款单关联
                                'goods',     // 商户产品关联
                                'product',   // DS产品关联
                                'merchant',     // 商户关联
                            ])
                            ->where([
                                'id' => $id,
                            ]);
        if ($merId !== null && $merId !== '') {
            $query->andWhere([
                'merchant_id' => $merId,
            ]);
        }
        $model = $query->one();

        if ($model !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
