<?php

use addons\VymCloud\common\models\CloudOrder;
use addons\VymCloud\common\enums\OrderEnum;
use common\enums\AppEnum;
use common\helpers\ArrayHelper;
use common\helpers\Html;
use common\helpers\Url;
use yii\grid\ActionColumn;
use yii\grid\GridView;
use yii\widgets\ActiveForm;

/** @var yii\web\View $this */
/** @var yii\data\ActiveDataProvider $dataProvider */
/** @var common\models\base\SearchModel $searchModel */

$this->title                   = '业务订单';
$this->params['breadcrumbs'][] = $this->title;

?>
<!-- Main content -->
<div class="container-fluid">

    <!-- 搜索筛选区域 -->
    <div class="card search-card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-search mr-1"></i>
                筛选条件
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <?php $form = ActiveForm::begin([
                'method'                 => 'get',
                'enableClientValidation' => false,
                'enableAjaxValidation'   => false,
                'options'                => ['class' => 'form-row'],
                'fieldConfig'            => [
                    'template'     => '<div class="input-group-prepend">{label}</div>{input}{error}',
                    'labelOptions' => ['class' => 'col-form-label-sm input-group-text'],
                    'options'      => ['class' => 'input-group col-sm-12 col-md-6 col-lg-3 col-lg-2 mb-2'],
                ],
            ]); ?>

            <?php if (Yii::$app->id == AppEnum::BACKEND): ?>
                <?= $form->field($searchModel, 'merchant_id')->dropDownList(
                    ArrayHelper::map(Yii::$app->services->merchant->findAllByCondition([]), 'id', 'title'),
                    ['prompt' => '选择商户']
                )->label('所属商户') ?>
            <?php endif; ?>

            <?= $form->field($searchModel, 'order_no')->textInput(['placeholder' => '订单号',])->label('订单号') ?>
            <?= $form->field($searchModel, 'payment_no')->textInput(['placeholder' => '支付流水号',])->label('支付流水号') ?>
            <?= $form->field($searchModel, 'uid')->textInput(['placeholder' => '用户ID',])->label('用户ID') ?>
            <?= $form->field($searchModel, 'payment_st')->dropDownList(OrderEnum::getPaymentStatus(), ['prompt' => '支付状态',])->label('支付状态') ?>
            <?= $form->field($searchModel, 'order_st')->dropDownList(OrderEnum::getMap(), ['prompt' => '订单状态',])->label('订单状态') ?>

            <div class="col-sm-12 col-md-6 col-lg-3">
                <?= Html::submitButton('<i class="fas fa-search"></i> 搜索', ['class' => 'btn btn-primary btn-md mr-2',]) ?>
                <?= Html::a('<i class="fas fa-redo"></i> 重置', ['index'], ['class' => 'btn btn-outline-secondary btn-md',]) ?>
            </div>
            <?php ActiveForm::end(); ?>
        </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-shopping-cart mr-1"></i>
                <?= Html::encode($this->title) ?>
            </h3>
            <div class="card-tools">
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <?= GridView::widget([
                    'dataProvider' => $dataProvider,
                    'tableOptions' => [
                        'class' => 'table table-hover rf-table mb-0',
                    ],
                    'columns'      => [
                        [
                            'attribute'      => 'id',
                            'headerOptions'  => ['class' => 'text-left'],
                            'contentOptions' => ['class' => 'text-left'],
                        ],
                        [
                            'label'          => '所属商户',
                            'headerOptions'  => ['style' => 'width: 120px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'visible'        => Yii::$app->id == AppEnum::BACKEND, // 只在总后台显示
                            'value'         => function ($model) {
                                if ($model->merchant && $mId = $model->merchant->id) {
                                    if ($mTitle = $model->merchant->title) {
                                        return Html::tag('span', $mTitle, ['class' => 'badge badge-primary']);
                                    }
                                    return Html::tag('span', "ID:{$mId}", ['class' => 'badge badge-primary']);
                                }
                                return Html::tag('span', '平台直属', ['class' => 'badge badge-secondary']);
                            },
                        ],
                        [
                            'label'          => '会员账号',
                            'headerOptions'  => ['style' => 'width: 130px;'],
                            'contentOptions' => ['class' => 'text-left'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $uid        = $model->uid;
                                $userName   = $model->member->username;
                                $userEmail  = $model->member->email;
                                $userMobile = $model->member->mobile;
                                $userInfo   = "UID: {$uid}";
                                if ($userName) {
                                    $userInfo .= "<br/>用户: {$userName}";
                                }
                                if ($userEmail) {
                                    $userInfo .= "<br/>邮箱: {$userEmail}";
                                }
                                if ($userMobile) {
                                    $userInfo .= "<br/>手机: {$userMobile}";
                                }
                                return $userInfo;
                            },
                        ],
                        [
                            'label'          => '订单信息',
                            'headerOptions'  => ['style' => 'width: 130px;'],
                            'contentOptions' => ['class' => 'text-left'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $order_no   = $model->order_no;
                                $payment_no = $model->payment_no;
                                //要从枚举中获取订单类型
                                $type       = OrderEnum::getOrderType($model->order_type);
                                $type_color = OrderEnum::getOrderTypeColors($model->order_type);
                                $str        = "订单类型: " . Html::tag('span', $type, ['class' => "badge badge-{$type_color}"]);
                                $str        .= "<br/>订单编号: {$order_no}";
                                if ($payment_no) {
                                    $str .= "<br/>付款单号: {$payment_no}";
                                }
                                return $str;
                            },
                        ],
                        [
                            'label'         => '订单金额',
                            'headerOptions' => ['style' => 'width: 120px;'],
                            'format'        => 'raw',
                            'value'         => function ($model) {
                                $amount = "订单金额:" . $model->amount_total . "<br/>";
                                $amount .= "应付金额:" . $model->amount_pay . "<br/>";
                                $model->amount_discount > 0 && $amount .= "折扣金额:" . $model->amount_discount . "<br/>";
                                $model->amount_coupon > 0 && $amount .= "优惠券金额:" . $model->amount_coupon;
                                return $amount;
                            },
                        ],
                        [
                            'attribute' => 'server_detail',
                            'label'     => '服务器配置',
                            'format'    => 'raw',
                            'value'     => function ($model) {
                                $basic     = $model->server_detail;
                                $cpu       = ArrayHelper::getValue($basic, 'config.cpu');
                                $mem       = ArrayHelper::getValue($basic, 'config.mem');
                                $disk_os   = ArrayHelper::getValue($basic, 'config.disk_os');
                                $disk_data = ArrayHelper::getValue($basic, 'config.disk_data');
                                $ip_num    = ArrayHelper::getValue($basic, 'config.ip_num');
                                $bw        = ArrayHelper::getValue($basic, 'config.bw');
                                $def       = ArrayHelper::getValue($basic, 'config.def');

                                $html = "CPU内存：CPU:{$cpu}H / 内存: {$mem}GB<br/>";
                                $html .= "磁盘配置：系统盘:{$disk_os}GB";
                                if ($disk_data) {
                                    $html .= " / 数据盘:{$disk_data}GB<br/>";
                                } else {
                                    $html .= "<br/>";
                                }
                                $html .= "网络配置：IP数:{$ip_num}个 / 带宽:{$bw}Mbps";
                                $def && $html .= " / 防御：{$def}Gbps";
                                return $html;
                            },
                        ],
                        [
                            'attribute'      => 'payment_st',
                            'headerOptions'  => ['style' => 'width: 100px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $statusMap = OrderEnum::getPaymentStatus();
                                $status    = $statusMap[$model->payment_st] ?? '未知';
                                $colorMap  = [
                                    OrderEnum::PAYMENT_STATUS_PENDING => 'warning',
                                    OrderEnum::PAYMENT_STATUS_PAID    => 'success',
                                    OrderEnum::PAYMENT_STATUS_FAILED  => 'danger',
                                ];
                                $color     = $colorMap[$model->payment_st] ?? 'secondary';
                                return Html::tag('span', $status, ['class' => "badge badge-{$color}"]);
                            },
                        ],
                        [
                            'attribute'      => 'order_st',
                            'headerOptions'  => ['style' => 'width: 100px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $statusMap = OrderEnum::getMap();
                                $status    = $statusMap[$model->order_st] ?? '未知';
                                $colorMap  = OrderEnum::getColors();
                                $color     = $colorMap[$model->order_st] ?? 'secondary';
                                // 转换AdminLTE的label颜色到Bootstrap 4的badge颜色
                                $badgeColorMap = [
                                    'success' => 'success',
                                    'warning' => 'warning',
                                    'danger'  => 'danger',
                                    'info'    => 'info',
                                    'primary' => 'primary',
                                    'default' => 'secondary',
                                ];
                                $badgeColor    = $badgeColorMap[$color] ?? 'secondary';
                                return Html::tag('span', $status, ['class' => "badge badge-{$badgeColor}"]);
                            },
                        ],
                        [
                            'label'          => '订单时间',
                            'headerOptions'  => ['style' => 'width: 130px;'],
                            'contentOptions' => ['class' => 'text-left'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $created_at = $model->created_at;
                                $payment_at = $model->payment_at;
                                $timeStr    = "创建时间: {$created_at}";
                                if ($payment_at) {
                                    $timeStr .= "<br/>支付时间: {$payment_at}";
                                }
                                return $timeStr;
                            },
                        ],
                        [
                            'header'         => '操作',
                            'headerOptions'  => ['style' => 'width: 120px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $buttons   = [];
                                $buttons[] = Html::a('<i class="fas fa-eye"></i> 详情', ['view', 'id' => $model->id], [
                                    'class' => 'btn btn-sm btn-outline-info',
                                    'title' => '查看详情',
                                ]);
                                return implode(' ', $buttons);
                            },
                        ],
                    ],
                ]); ?>
            </div>
        </div>
    </div>

</div>