<?php

namespace addons\VymServer\merchant\controllers;

use addons\VymServer\common\models\ServerProduct;
use common\helpers\ArrayHelper;
use common\helpers\ResultHelper;
use common\models\base\SearchModel;
use common\helpers\ExcelHelper;
use Yii;
use yii\db\Exception;
use yii\db\StaleObjectException;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * ProductController implements the CRUD actions for ServerProduct model.
 */
class ProductController extends BaseController
{

    /**
     * @var ServerProduct
     */
    public $modelClass = ServerProduct::class;


    /**
     * 首页
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionIndex()
    {
        $searchModel = new SearchModel([
            'model'                  => $this->modelClass,
            'scenario'               => 'default',
            'relations'              => ['detail' => []], // 关联 pdtmanage 表的所有字段
            'partialMatchAttributes' => ['title', 'name'], // 模糊查询
            'defaultOrder'           => [
                'id'     => SORT_DESC,
                'orders' => SORT_DESC,
            ],
            'pageSize'               => $this->pageSize,
        ]);

        $dataProvider = $searchModel->search($this->request->queryParams);
        $this->addQueryParam($dataProvider);
        return $this->render($this->action->id, [
            'dataProvider' => $dataProvider,
            'searchModel'  => $searchModel,
        ]);
    }

    /**
     * 新增/编辑
     *
     * @param int|null $id 套餐ID
     *
     * @return string|Response
     * @throws Exception
     */
    public function actionEdit($id = null)
    {
        $model = $this->findModel($id);
        $post  = $this->request->post();
        if ($this->request->isPost && $model->load($post)) {
            return $model->save()
                ? $this->message('操作成功', $this->redirect($this->request->referrer))
                : $this->message($this->getError($model), $this->redirect($this->request->referrer), 'error');
        }

        return $this->render($this->action->id, [
            'model' => $model,
        ]);
    }


    /**
     * 删除
     *
     * @param $id
     *
     * @return mixed
     * @throws \Throwable|StaleObjectException
     */
    public function actionDelete($id)
    {
        if ($this->findModel($id)->delete()) {
            return $this->message("删除成功", $this->redirect($this->request->referrer));
        }

        return $this->message("删除失败", $this->redirect($this->request->referrer), 'error');
    }


    /**
     * 同步产品
     * @return array|mixed|string|Response
     */
    public function actionSync()
    {
        $merchantId = $this->request->get('merchant_id', 0);
        $isForce    = $this->request->post('is_force', 0);
        if ($this->request->isPost) {
            $re = Yii::$app->vymServerService->product->syncProduct($isForce);
            return ResultHelper::json($re['status'] ? 200 : 404, $re['msg'], $data = []);
        }
        return ResultHelper::json(404, '异常操作,请刷新重新操作');
    }

    /**
     * 导出产品
     * @return array|mixed|string|Response
     */
    public function actionExport()
    {
        $merchantId = $this->request->get('merchant_id', 0);

        $re = Yii::$app->vymServerService->product->exportProduct();
        return $this->message("导出失败", $this->redirect($this->request->referrer), 'error');
    }

    /**
     * 导入产品
     * @return array|mixed|string|Response
     */
    public function actionImport()
    {
        $merchantId = $this->request->get('merchant_id', 0);
        $file_path       = $this->request->post('file_path', "");
        $filePath   = Yii::getAlias('@attachment'). '/' . $file_path;
//        $startRow   = 1;
        if ($this->request->isPost) {

//            $data = ExcelHelper::import($filePath, $startRow);
            $re   = Yii::$app->vymServerService->product->importProduct($filePath);
            return ResultHelper::json(200, $re['msg'], $data = []);
        }
        return ResultHelper::json(404, '异常操作,请刷新重新操作');
    }
}
