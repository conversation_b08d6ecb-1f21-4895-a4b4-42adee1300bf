<?php

use yii\helpers\Url;

$this->title = '控制台管理-供应商管理-修改供应商信息';
?>
<?php $this->beginBlock('content') ?>

<div class="row">
	<div class="col-xs-12">
		<form id='form' class="form-horizontal" method='post' role="form">
			<input type="hidden" name='id' value="<?php echo $arrRes['id'] ?>">
			<div class="form-group">
				<label class="col-sm-3 control-label no-padding-right" for="form-field-1"> 供应商名称</label>
				<div class="col-sm-9">
					<input type="text" name='name' value='<?php echo $arrRes['name'] ?>' class="col-xs-10 col-sm-5">
					<span class="middle" style="color:red;margin-left:5px"></span>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label no-padding-right"> 供应商简称</label>
				<div class="col-sm-9">
					<input type="text" name='short' value='<?php echo $arrRes['short'] ?>' class="col-xs-10 col-sm-5">
					<span class="middle" style="color:red;margin-left:5px"></span>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label no-padding-right"> 联系邮箱</label>
				<div class="col-sm-9">
					<input type="text" name='contacts_name' value='<?php echo json_decode($arrRes['contacts'], true)['contacts_name'] ?>' class="col-xs-10 col-sm-5">
					<span class="middle" style="color:red;margin-left:5px"></span>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label no-padding-right"> 后台地址</label>
				<div class="col-sm-9">
					<input type="text" name='contacts_tel' value='<?php echo json_decode($arrRes['contacts'], true)['contacts_tel'] ?>' class="col-xs-10 col-sm-5">
					<span class="middle" style="color:red;margin-left:5px"></span>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label no-padding-right"> 联系人QQ</label>
				<div class="col-sm-9">
					<input type="text" name='contacts_qq' value='<?php echo json_decode($arrRes['contacts'], true)['contacts_qq'] ?>' class="col-xs-10 col-sm-5">
					<span class="middle" style="color:red;margin-left:5px"></span>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label no-padding-right"> App配置</label>
				<div class="col-sm-9">
					<select name="appcfg_name" >
						<option value="">请选择App配置</option>
						<?php
							foreach ($appcfgOptions as $key => $one) {
								if($one['config_name'] == $arrRes['appcfg_name']) {
									echo "<option value=".$one['config_name']." selected = 'selected'>".$one['config_alias']."</option>";
								} else {
									echo "<option value=".$one['config_name'].">".$one['config_alias']."</option>";
								}
							}
						?>
					</select>
					<span class="middle" style="color:red;margin-left:5px"></span>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label no-padding-right" for="form-field-1"> 备注</label>
				<div class="col-sm-9">
					<textarea class="form-control col-xs-10 col-sm-5 cn" name='remarks'><?php echo $arrRes['remarks'] ?></textarea>
					<span class="middle" style="color:red;margin-left:5px"></span>
				</div>
			</div>
			<div class="clearfix form-actions">
				<div class="col-md-offset-3 col-md-9">
					<button id="go" class="btn btn-info" type="submit">
						<i class="ace-icon fa fa-check bigger-110"></i>
						数据提交
					</button>
					&nbsp; &nbsp; &nbsp;
					<button class="btn" type="reset">
						<i class="ace-icon fa fa-undo bigger-110"></i>
						还原内容
					</button>
					&nbsp; &nbsp; &nbsp;
					<button class="btn btn-danger" type="button" style="width:120px" onclick="window.history.back()">
						<i class="ace-icon fa fa-reply icon-only"></i>
						返回上一页
					</button>
				</div>
			</div>
			<div class="hr hr-24"></div>
		</form>
	</div>
</div>
<script type="text/javascript">
	$('#go').click(function() {
		var url = '<?php echo Url::to(["update"]) ?>';
		layer.msg('正在提交...', {
			icon: 16,
			time: 0
		});
		$.post(url, $('#form').serialize(), function(data) {
			if (data['data']['status']) {
				layer.alert(data['data']['info'], {
					icon: 1
				}, function(index) {
					window.location.href = '<?php echo Url::to(["provider/index"]) ?>';
				});
			} else {
				if (typeof data['data']['info'] === 'string') {
					layer.alert(data['data']['info'], {
						icon: 7
					});
					return;
				}
				$.each(data['data']['info'], function(key, val) {
					layer.alert('信息填写有误', {
						icon: 7
					});
					$("[name='" + key + "']").next('span').html('<i class="ace-icon fa fa-exclamation-circle bigger-110">' + val[0] + '</i>');

				});
			}

		}, 'json');

		return false;

	});
</script>

<?php $this->endBlock(); ?>