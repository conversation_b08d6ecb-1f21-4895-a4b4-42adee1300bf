<?php

namespace addons\VymServer\merchant\controllers;

use addons\VymServer\common\models\ServerArea;
use addons\VymServer\merchant\controllers\BaseController;
use common\models\base\SearchModel;
use Yii;

/**
 * AreaController implements the CRUD actions for ServerArea model.
 */
class AreaController extends BaseController
{

    // use MerchantCurd;

    /**
     * @var ServerArea
     */
    public $modelClass = ServerArea::class;


    /**
     * 首页
     *
     * @return string
     * @throws \yii\web\NotFoundHttpException
     */
    public function actionIndex()
    {
        $searchModel = new SearchModel([
            'model'                  => $this->modelClass,
            'scenario'               => 'default',
            'relations'              => ['regional'=>[]], // 关联 regional 表的所有字段
            'partialMatchAttributes' => ['name'], // 模糊查询
            'defaultOrder'           => [
                'sort' => SORT_ASC,
                'id'   => SORT_DESC,
            ],
            'pageSize'               => $this->pageSize,
        ]);
        $merId       = $this->getMerchantId();
        $dataProvider = $searchModel->search($this->request->queryParams);
        // dd($dataProvider->getModels());
        $this->addQueryParam($dataProvider);
        return $this->render($this->action->id, [
            'dataProvider' => $dataProvider,
            'searchModel'  => $searchModel,
        ]);
    }

    /**
     * ajax编辑/创建
     *
     * @return mixed|string|\yii\web\Response
     * @throws \yii\base\ExitException
     */
    public function actionAjaxEdit()
    {
        $id = $this->request->get('id');

        $model = $this->findModel($id);
        // ajax 校验
        $this->activeFormValidate($model);
        if ($model->load($this->request->post())) {
            return $model->save()
                ? $this->redirect($this->request->referrer)
                : $this->message($this->getError($model), $this->redirect($this->request->referrer), 'error');
        }

        return $this->renderAjax($this->action->id, [
            'model'        => $model,
            'dropDownList' => Yii::$app->vymServerService->area->getRegionalDropDown(),
        ]);
    }


    /**
     * 删除
     *
     * @param $id
     *
     * @return mixed
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    public function actionDelete($id)
    {
        if ($this->findModel($id)->delete()) {
            return $this->message("删除成功", $this->redirect($this->request->referrer));
        }

        return $this->message("删除失败", $this->redirect($this->request->referrer), 'error');
    }
}
