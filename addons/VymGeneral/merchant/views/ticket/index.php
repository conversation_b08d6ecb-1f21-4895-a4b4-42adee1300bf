<?php

use common\enums\AppEnum;
use common\helpers\ArrayHelper;
use yii\grid\GridView;
use common\helpers\Html;
use common\helpers\Url;
use common\enums\StatusEnum;

$this->title = '工单管理';
$this->params['breadcrumbs'][] = $this->title;

?>

<div class="row">
    <div class="col-12">
        <!-- 统计卡片 -->
        <div class="row">
            <div class="col-md-3 col-sm-6 col-xs-12">
                <div class="info-box">
                    <span class="info-box-icon bg-aqua"><i class="fa fa-ticket"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">待处理工单</span>
                        <span class="info-box-number"><?= $statistics['pending'] ?></span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 col-xs-12">
                <div class="info-box">
                    <span class="info-box-icon bg-yellow"><i class="fa fa-spinner"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">处理中工单</span>
                        <span class="info-box-number"><?= $statistics['processing'] ?></span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 col-xs-12">
                <div class="info-box">
                    <span class="info-box-icon bg-green"><i class="fa fa-check-circle"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">已解决工单</span>
                        <span class="info-box-number"><?= $statistics['resolved'] ?></span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 col-xs-12">
                <div class="info-box">
                    <span class="info-box-icon bg-gray"><i class="fa fa-times-circle"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">已关闭工单</span>
                        <span class="info-box-number"><?= $statistics['closed'] ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-12">
        <div class="box">
            <div class="box-header">
                <h3 class="box-title"><?= $this->title; ?></h3>
                <div class="box-tools">
                    <?= Html::create(['create'], '创建工单'); ?>
                    <div class="btn-group">
                        <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                            批量操作 <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a href="javascript:;" onclick="batchStatus(1)">批量待处理</a></li>
                            <li><a href="javascript:;" onclick="batchStatus(2)">批量处理中</a></li>
                            <li><a href="javascript:;" onclick="batchStatus(3)">批量已解决</a></li>
                            <li><a href="javascript:;" onclick="batchStatus(4)">批量已关闭</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="box-body table-responsive">
                <?= GridView::widget([
                    'dataProvider' => $dataProvider,
                    'filterModel' => $searchModel,
                    'tableOptions' => ['class' => 'table table-hover'],
                    'id' => 'grid',
                    'columns' => [
                        [
                            'class' => 'yii\grid\CheckboxColumn',
                            'name' => 'ids',
                        ],
                        'id',
                        [
                            'label'          => '所属商户',
                            'headerOptions'  => ['style' => 'width: 120px;'],
                            'contentOptions' => ['class' => 'text-left'],
                            'filter' => Html::activeDropDownList($searchModel, 'merchant_id', $merchantList, [
                                'prompt' => '全部',
                                'class' => 'form-control',
                            ]),
                            'format'         => 'raw',
                            'visible'        => Yii::$app->id == AppEnum::BACKEND, // 只在总后台显示
                            'value'          => function ($model) {
                                $merchantId    = ArrayHelper::getValue($model->merchant,"id");
                                $merchantTitle =  ArrayHelper::getValue($model->merchant,"title","未设置"); //$model->merchant->title ?? '未设置';
                                $str           = "未关联商户";
                                if ($merchantId) {
                                    $str = "ID: {$merchantId}";
                                    if ($merchantTitle) {
                                        $str .= "<br/>名称: {$merchantTitle}";
                                    }
                                }
                                return $str;
                            },
                        ],
                        [
                            'attribute' => 'title',
                            'format' => 'raw',
                            'value' => function ($model) {
                                return Html::a($model->title, ['view', 'id' => $model->id], ['class' => 'text-primary']);
                            },
                        ],
                        [
                            'attribute' => 'category_id',
                            'format' => 'raw',
                            'filter' => Html::activeDropDownList($searchModel, 'category_id', $categoryDropDownList, [
                                'prompt' => '全部',
                                'class' => 'form-control',
                            ]),
                            'value' => function ($model) {
                                return $model->category ? $model->category->title : '-';
                            },
                        ],
                        [
                            'attribute' => 'priority',
                            'format' => 'raw',
                            'filter' => Html::activeDropDownList($searchModel, 'priority', $priorityList, [
                                'prompt' => '全部',
                                'class' => 'form-control',
                            ]),
                            'value' => function ($model) {
                                $labels = [
                                    1 => '<span class="label label-info">低</span>',
                                    2 => '<span class="label label-warning">中</span>',
                                    3 => '<span class="label label-danger">高</span>',
                                ];
                                return $labels[$model->priority] ?? '-';
                            },
                        ],
                        [
                            'attribute' => 'status',
                            'format' => 'raw',
                            'filter' => Html::activeDropDownList($searchModel, 'status', $statusList, [
                                'prompt' => '全部',
                                'class' => 'form-control',
                            ]),
                            'value' => function ($model) {
                                $labels = [
                                    1 => '<span class="label label-info">待处理</span>',
                                    2 => '<span class="label label-warning">处理中</span>',
                                    3 => '<span class="label label-success">已解决</span>',
                                    4 => '<span class="label label-default">已关闭</span>',
                                ];
                                return $labels[$model->status] ?? '-';
                            },
                        ],
                        [
                            'attribute' => 'reply_count',
                            'format' => 'raw',
                            'value' => function ($model) {
                                return $model->reply_count > 0 ? 
                                    '<span class="badge bg-green">' . $model->reply_count . '</span>' : 
                                    '<span class="badge bg-gray">0</span>';
                            },
                        ],
                        [
                            'attribute' => 'created_at',
                            'format' => ['date', 'php:Y-m-d H:i'],
                        ],
                        [
                            'header' => "操作",
                            'class' => 'yii\grid\ActionColumn',
                            'template' => '{view}',
                            'buttons' => [
                                'view' => function ($url, $model, $key) {
                                    return Html::a('<i class="fa fa-eye"></i>', ['view', 'id' => $model->id], [
                                        'title' => '查看',
                                        'class' => 'btn btn-info btn-sm',
                                    ]);
                                },
                            ],
                        ],
                    ],
                ]); ?>
            </div>
        </div>
    </div>
</div>

<script>
// 批量更新状态
function batchStatus(status) {
    var batchStatusUrl = "<?= Url::to(['batch-status']);?>"
    var ids = $('#grid').yiiGridView('getSelectedRows');
    if(ids.length === 0){
        rfWarning('请选择工单');
        return false;
    }

    var statusText = '';
    switch(status) {
        case 1: statusText = '待处理'; break;
        case 2: statusText = '处理中'; break;
        case 3: statusText = '已解决'; break;
        case 4: statusText = '已关闭'; break;
    }

    if (!confirm('确定要将选中的工单标记为' + statusText + '吗？')) {
        return false;
    }

    $.ajax({
        type: "post",
        url: batchStatusUrl,
        dataType: "json",
        data: {ids: ids, status: status},
        success: function(data) {
            if (data.code == 200) {
                rfSuccess(data.message);
                // 刷新页面
                window.location.reload();
            } else {
                rfWarning(data.message);
            }
        }
    });
}
</script>

