<?php

use common\enums\AppEnum;
use common\helpers\Html;
use jianyan\treegrid\TreeGrid;

/** @var yii\web\View $this */
/** @var yii\data\ActiveDataProvider $dataProvider */

$this->title                   = '业务地域';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="row">
    <div class="col-12 col-xs-12">
        <div class="box">
            <div class="box-header">
                <h3 class="box-title"><?= Html::encode($this->title); ?></h3>
                <div class="box-tools">
                    <?= Html::create(['ajax-edit'], '创建', [
                        'data-toggle' => 'modal',
                        'data-target' => '#ajaxModal',
                        'class'       => 'btn btn-primary btn-sm no-loading',
                    ]); ?>
                </div>
            </div>
            <div class="box-body table-responsive">
                <?= TreeGrid::widget([
                    'dataProvider'     => $dataProvider,
                    'keyColumnName'    => 'id',
                    'parentColumnName' => 'pid',
                    'parentRootValue'  => '0', //first parentId value
                    'pluginOptions'    => [
                        'initialState' => 'collapsed',
                    ],
                    'options'          => ['class' => 'table table-hover rf-table'],
                    'columns'          => [
                        'id',
                        [
                            'label'         => '所属商户',
                            'format'        => 'raw',
                            'visible'       => Yii::$app->id == AppEnum::BACKEND, // 只在总后台显示
                            'value'         => function ($model) {
                                if ($model->merchant && $mId = $model->merchant->id) {
                                    if ($mTitle = $model->merchant->title) {
                                        return Html::tag('span', $mTitle, ['class' => 'badge badge-primary']);
                                    }
                                    return Html::tag('span', "ID:{$mId}", ['class' => 'badge badge-primary']);
                                }
                                return Html::tag('span', '平台直属', ['class' => 'badge badge-secondary']);
                            },
                        ],
                        [
                            'attribute'     => 'name',
                            'format'        => 'raw',
                            'value'         => function ($model, $key, $index, $column) {
                                $str = Html::tag('span', $model->name, [
                                    'class' => 'm-l-sm',
                                ]);
                                $str .= Html::a(' <i class="iconfont iconplus-circle pointer"></i>',
                                    ['ajax-edit', 'pid' => $model['id']], [
                                        'data-toggle' => 'modal',
                                        'data-target' => '#ajaxModal',
                                        'class'       => 'no-loading',
                                    ]);
                                return $str;
                            },
                        ],
                        [
                            'attribute'     => 'sort',
                            'format'        => 'raw',
                            'headerOptions' => ['class' => 'col-md-1'],
                            'value'         => function ($model, $key, $index, $column) {
                                return Html::sort($model->sort);
                            },
                        ],
                        [
                            'header'        => "操作",
                            'class'         => 'yii\grid\ActionColumn',
                            'headerOptions' => ['class' => 'col-md-1'],
                            'template'      => '{edit} {delete}',
                            'buttons'       => [
                                'edit'   => function ($url, $model, $key) {
                                    return Html::edit(['ajax-edit', 'id' => $model->id], '编辑', [
                                        'data-toggle' => 'modal',
                                        'data-target' => '#ajaxModal',
                                        'class'       => 'btn btn-primary btn-sm no-loading',
                                    ]);
                                },
                                'delete' => function ($url, $model, $key) {
                                    return Html::delete(['delete', 'id' => $model->id]);
                                },
                            ],
                        ],
                    ],
                ]); ?>
            </div>
        </div>
    </div>
</div>
