<?php

namespace addons\VymCloud\common\models;

use common\models\merchant\Merchant;
use Yii;

/**
 * This is the model class for table "{{%addon_cloud_ips}}".
 *
 * @property int         $id
 * @property int|null    $merchant_id 商户ID
 * @property int|null    $store_id    店铺id
 * @property int|null    $cluster_id  集群ID
 * @property int|null    $server_id   业务ID
 * @property string      $ip          IP
 * @property string      $gateway     网关地址
 * @property string|null $mask        子网掩码
 * @property string|null $mac         MAC地址
 * @property string|null $desc        备注
 * @property int|null    $is_use      是否占用
 * @property int|null    $is_main     是否主IP
 * @property int|null    $is_inside   是否内网
 * @property int|null    $is_ipv6     是否IPv6
 * @property string|null $dns         DNS
 * @property int|null    $vlan        VLAN
 * @property int|null    $sort        排序
 * @property string|null $created_at  创建时间
 * @property string|null $updated_at  更新时间
 * @property string|null $deleted_at  删除时间
 */
class CloudIps extends \yii\db\ActiveRecord
{

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%addon_cloud_ips}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['mask', 'desc', 'created_at', 'updated_at', 'deleted_at'], 'default', 'value' => null],
            [['type'], 'default', 'value' => 'ip4'],
            [['vlan'], 'default', 'value' => 0],
            [['sort'], 'default', 'value' => 1],
            [['cluster_id', 'server_id', 'order_id', 'is_use', 'is_main', 'is_inside', 'vlan', 'sort'], 'integer'],
            [['ip', 'type', 'is_inside', 'is_use', 'gateway'], 'required'],
            [['mac', 'desc'], 'string'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['ip', 'gateway', 'mask'], 'string', 'max' => 20],
            [['merchant_id', 'store_id'], 'default', 'value' => 0],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'          => 'ID',
            'cluster_id'  => '集群ID',
            'server_id'   => '业务ID',
            'merchant_id' => '商户ID',
            'store_id'    => '店铺ID',
            'ip'          => 'IP',
            'gateway'     => '网关地址',
            'mask'        => '子网掩码',
            'mac'         => 'MAC地址',
            'desc'        => '备注',
            'is_use'      => '是否占用',
            'is_main'     => '是否主IP',
            'is_inside'   => '是否内网',
            'is_ipv6'     => '是否IPv6',
            'type'        => 'IP类型',
            'dns'         => 'DNS',
            'vlan'        => 'VLAN',
            'sort'        => '排序',
            'created_at'  => '创建时间',
            'updated_at'  => '更新时间',
            'deleted_at'  => '删除时间',
        ];
    }


    /**
     * 获取服务器
     *
     * @return \yii\db\ActiveQuery
     */
    public function getServer()
    {
        return $this->hasOne(CloudServer::class, ['id' => 'server_id']);
    }

    /**
     * 获取商户
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }
}
