<?php

namespace addons\VymDesen\backend\controllers;

use addons\VymDesen\backend\components\LogWrite;
use addons\VymDesen\backend\controllers\BaseController;
use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\models\Member\MemberPdt;
use addons\VymDesen\common\models\Pdt\PdtBandwidth;
use addons\VymDesen\common\models\Pdt\PdtCard;
use addons\VymDesen\common\models\Pdt\PdtCpu;
use addons\VymDesen\common\models\Pdt\PdtDefense;
use addons\VymDesen\common\models\Pdt\PdtHdd;
use addons\VymDesen\common\models\Pdt\PdtIpnumber;
use addons\VymDesen\common\models\Pdt\PdtManage;
use addons\VymDesen\common\models\Pdt\PdtManageType;
use addons\VymDesen\common\models\Pdt\PdtOperatsystem;
use addons\VymDesen\common\models\Pdt\PdtRam;
use addons\VymDesen\common\models\Pdt\PdtRoomMange;
use addons\VymDesen\common\models\Pdt\PdtType;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Yii;
use yii\helpers\Url;

#导出需要的

/**
 *
 * 产品控制器
 *
 * <AUTHOR>
 *
 */
class PdtManageController extends BaseController
{
    /**
     * 列表
     *
     * @return Ambigous <string, string>
     */
    public function actionIndex()
    {
        $PdtManageModel = new PdtManage();

        $PdtManageModelQuery = $PdtManageModel->getListAll([], true);
        #创建搜索条件
        $PdtManageModel->createSearchWhere($PdtManageModelQuery, $this->get());

        $iCount = $PdtManageModelQuery->count();
        $oPage  = DataHelper::getPage($iCount);

        $arrRes = $PdtManageModelQuery->offset($oPage->offset)->limit($oPage->limit)->orderBy('id desc')->asArray()
                                      ->all();   //die(print_r($arrRes));
        //获取机房列表
        $RoomManageModel = new PdtRoomMange();
        $roomRes         = $RoomManageModel->getListAll();
        //获取配置分类列表
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeRes   = $PdtManageTypeModel->find()->AsArray()->all();///print_r($PdtManageTypeRes);die;
        //
        $PdtTypeModel = new PdtType();
        $PdtTypeRes   = $PdtTypeModel->getListAll();

        return $this->render('index', [
            'arrRes'           => $arrRes,
            'roomRes'          => $roomRes,
            'PdtManageTypeRes' => $PdtManageTypeRes,
            'PdtTypeRes'       => $PdtTypeRes,
            'iCount'           => $iCount,
            'page'             => $oPage
        ]);
    }

    /**
     * 详情页
     */
    public function actionItem()
    {
        $id = $this->get('id');
        $id = intval($id);

        $PdtManageModel = new PdtManage();
        $arrRes         = $PdtManageModel->getRowById($id);//print_r($arrRes);die();
        if (empty($arrRes))
            return $this->redirect(Url::to(['index']));

        $pdtRoom       = explode(",", $arrRes['room_id']);    //print_r($pdtRoom);die();
        $pdtTypeIdList = explode(",", $arrRes['pdt_type_id']);//print_r($pdtRoom);die();

        $RoomManageModel = new PdtRoomMange();
        $roomRes         = $RoomManageModel->getListAll();
        //获取配置分类列表
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeRes   = $PdtManageTypeModel->find()->AsArray()->all();///print_r($PdtManageTypeRes);die;
        //获取产品属性类别
        $PdtTypeModel = new PdtType();
        $PdtTypeRes   = $PdtTypeModel->getValidListAll();
        //cpu
        $PdtCpuModel = new PdtCpu();
        $PdtCpuRes   = $PdtCpuModel->getListAll();//print_r($PdtCpuRes);//die();
        //内存
        $PdtRamModel = new PdtRam();
        $PdtRamRes   = $PdtRamModel->getListAll();//print_r($PdtCpuRes);die();
        //硬盘
        $PdtHddModel = new PdtHdd();
        $PdtHddRes   = $PdtHddModel->getListAll();//print_r($PdtCpuRes);die();
        //带宽
        $PdtBandwidthModel = new PdtBandwidth();
        $PdtBandwidthRes   = $PdtBandwidthModel->getListAll();//print_r($PdtCpuRes);die();
        //iP数量
        $PdtIpnumberModel = new PdtIpnumber();
        $PdtIpnumberRes   = $PdtIpnumberModel->getListAll();//print_r($PdtCpuRes);die();
        //防御流量
        $PdtDefenseModel = new PdtDefense();
        $PdtDefenseRes   = $PdtDefenseModel->getListAll();//print_r($PdtCpuRes);die();
        //操作系统
        $PdtSystemModel = new PdtOperatsystem();
        $PdtSystemRes   = $PdtSystemModel->getListAll();//print_r($PdtCpuRes);die();
        //显卡
        $PdtCardModel = new PdtCard();
        $PdtCardRes   = $PdtCardModel->getListAll();

        $cpu          = json_decode($arrRes['cpu'], true);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         //print_r($cpu);die();
        $ram          = json_decode($arrRes['ram'], true);
        $hdd          = json_decode($arrRes['hdd'], true);
        $bandwidth    = json_decode($arrRes['bandwidth'], true);
        $ipnumber     = json_decode($arrRes['ipnumber'], true);
        $defense      = json_decode($arrRes['defense'], true);
        $operatsystem = json_decode($arrRes['operatsystem'], true);
        $card         = json_decode($arrRes['card'], true);
        //将需要的数组合并为一个数组
        $cpuRes       = $PdtManageModel->merge($PdtCpuRes, $cpu);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              //die(print_r($cpuRes));
        $ramRes = $PdtManageModel->merge($PdtRamRes, $ram);
        $hddRes = $PdtManageModel->merge($PdtHddRes, $hdd);
        $bandwidthRes = $PdtManageModel->merge($PdtBandwidthRes, $bandwidth);
        $ipnumberRes  = $PdtManageModel->merge($PdtIpnumberRes, $ipnumber);
        if ( !empty($defense))
        {
            $defenseRes = $PdtManageModel->merge($PdtDefenseRes, $defense);
        }
        else
        {
            $defenseRes = $PdtDefenseRes;
        }
        $operatsystemRes = $PdtManageModel->merge($PdtSystemRes, $operatsystem);

        $cardRes = $card ? $PdtManageModel->merge($PdtCardRes, $card) : $PdtCardRes;

        return $this->render('item', [
            'arrRes'           => $arrRes,
            'pdtRoom' => $pdtRoom,
            'pdtTypeIdList' => $pdtTypeIdList,
            'roomRes'       => $roomRes,
            'PdtTypeRes'    => $PdtTypeRes,
            'PdtManageTypeRes' => $PdtManageTypeRes,
            'PdtCpuRes'        => $cpuRes,
            'PdtRamRes'        => $ramRes,
            'PdtHddRes'        => $hddRes,
            'PdtBandwidthRes'  => $bandwidthRes,
            'PdtIpnumberRes'   => $ipnumberRes,
            'PdtDefenseRes'    => $defenseRes,
            'PdtSystemRes'     => $operatsystemRes,
            'PdtCardRes'       => $cardRes,
            'cpu'              => $cpu,
            'ram'              => $ram,
            'hdd'              => $hdd,
            'bandwidth'        => $bandwidth,
            'ipnumber'         => $ipnumber,
            'defense'          => $defense,
            'operatsystem'     => $operatsystem,
            'card'             => $card,
        ]);

    }

    public function actionAdd()
    {
        $RoomManageModel = new PdtRoomMange();
        $roomRes         = $RoomManageModel->getListAll();
        //获取配置分类列表
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeRes   = $PdtManageTypeModel->find()->AsArray()->all();///print_r($PdtManageTypeRes);die;
        //获取产品属性类别
        $PdtTypeModel = new PdtType();
        $PdtTypeRes   = $PdtTypeModel->getValidListAll();
        //cpu
        $PdtCpuModel = new PdtCpu();
        $PdtCpuRes   = $PdtCpuModel->getListAll();//print_r($PdtCpuRes);die();
        //内存
        $PdtRamModel = new PdtRam();
        $PdtRamRes   = $PdtRamModel->getListAll();//print_r($PdtCpuRes);die();
        //硬盘
        $PdtHddModel = new PdtHdd();
        $PdtHddRes   = $PdtHddModel->getListAll();//print_r($PdtCpuRes);die();
        //带宽
        $PdtBandwidthModel = new PdtBandwidth();
        $PdtBandwidthRes   = $PdtBandwidthModel->getListAll();//print_r($PdtCpuRes);die();
        //iP数量
        $PdtIpnumberModel = new PdtIpnumber();
        $PdtIpnumberRes   = $PdtIpnumberModel->getListAll();//print_r($PdtCpuRes);die();
        //防御流量
        $PdtDefenseModel = new PdtDefense();
        $PdtDefenseRes   = $PdtDefenseModel->getListAll();//print_r($PdtCpuRes);die();
        //操作系统
        $PdtSystemModel = new PdtOperatsystem();
        $PdtSystemRes   = $PdtSystemModel->getListAll();//print_r($PdtCpuRes);die();
        //显卡
        $PdtCardModel = new PdtCard();
        $PdtCardRes   = $PdtCardModel->getListAll();
        return $this->render('add', [
            'roomRes'          => $roomRes,
            'PdtManageTypeRes' => $PdtManageTypeRes,
            'PdtTypeRes'       => $PdtTypeRes,
            'PdtCpuRes'        => $PdtCpuRes,
            'PdtRamRes'        => $PdtRamRes,
            'PdtHddRes'        => $PdtHddRes,
            'PdtBandwidthRes'  => $PdtBandwidthRes,
            'PdtIpnumberRes'   => $PdtIpnumberRes,
            'PdtDefenseRes'    => $PdtDefenseRes,
            'PdtSystemRes'     => $PdtSystemRes,
            'PdtCardRes'       => $PdtCardRes,
        ]);
    }

    /*
     * 导出下载
     */
    public function actionExport()
    {

        $PdtManageModel = new PdtManage();

        $PdtManageModelQuery = $PdtManageModel->find()->with('pdtmanagetype');
        //print_r($this->post());die();
        #创建搜索条件
        $PdtManageModel->createSearchWhere($PdtManageModelQuery, $this->get());

        $arrRes = $PdtManageModelQuery->orderBy('id desc')->asArray()->all();#die(print_r($arrRes));
        $models = [];
        foreach ($arrRes as $key => $value)
        {
            $models[$key]['id']   = $value['id'];
            $models[$key]['name'] = $value['name'];

            if ( !empty($value['pdt_type_id']))
            {
                $name = [];
                foreach ($value['pdtmanagetype'] as $val_1)
                {
                    $name[] = $val_1['type_name'];
                }
                $models[$key]['pdt_type_id'] = implode(',', $name);
            }

            if ( !empty($value['cpu']))
            {
                $cpuRes  = json_decode($value['cpu'], true);
                $cpulist = [];
                foreach ($cpuRes['info'] as $val_2)
                {
                    $cpulist[] = $val_2['name'] . '：金额：' . $val_2['price'];
                }
                $models[$key]['cpu'] = implode(' | ', $cpulist);
            }
            if ( !empty($value['ram']))
            {
                $ramRes  = json_decode($value['ram'], true);
                $ramlist = [];
                foreach ($ramRes['info'] as $val_2)
                {
                    $ramlist[] = $val_2['name'] . '：金额：' . $val_2['price'];
                }
                $models[$key]['ram'] = implode(' | ', $ramlist);
            }
            if ( !empty($value['hdd']))
            {
                $hddRes  = json_decode($value['hdd'], true);
                $hddlist = [];
                foreach ($hddRes['info'] as $val_2)
                {
                    $hddlist[] = $val_2['name'] . '：金额：' . $val_2['price'];
                }
                $models[$key]['hdd'] = implode(' | ', $hddlist);
            }
            if ( !empty($value['bandwidth']))
            {
                $bandwidthRes  = json_decode($value['bandwidth'], true);
                $bandwidthlist = [];
                foreach ($bandwidthRes['info'] as $val_2)
                {
                    $bandwidthlist[] = $val_2['name'] . '：金额：' . $val_2['price'];
                }
                $models[$key]['bandwidth'] = implode(' | ', $bandwidthlist);
            }
            if ( !empty($value['ipnumber']))
            {
                $ipnumberRes  = json_decode($value['ipnumber'], true);
                $ipnumberlist = [];
                foreach ($ipnumberRes['info'] as $val_2)
                {
                    $ipnumberlist[] = $val_2['name'] . '：金额：' . $val_2['price'];
                }
                $models[$key]['ipnumber'] = implode(' | ', $ipnumberlist);
            }
            if ( !empty($value['defense']))
            {
                $defenseRes  = json_decode($value['defense'], true);
                $defenselist = [];
                foreach ($defenseRes['info'] as $val_2)
                {
                    $defenselist[] = $val_2['name'] . '：金额：' . $val_2['price'];
                }
                $models[$key]['defense'] = implode(' | ', $defenselist);
            }
            if ( !empty($value['operatsystem']))
            {
                $operatsystemRes  = json_decode($value['operatsystem'], true);
                $operatsystemlist = [];
                foreach ($operatsystemRes['info'] as $val_2)
                {
                    $operatsystemlist[] = $val_2['name'];
                }
                $models[$key]['operatsystem'] = implode(' | ', $operatsystemlist);
            }
            if ( !empty($value['card']))
            {
                $cardRes  = json_decode($value['card'], true);
                $cardlist = [];
                foreach ($cardRes['info'] as $val_2)
                {
                    $cardlist[] = $val_2['name'] . '：金额：' . $val_2['price'];
                }
                $models[$key]['card'] = implode(' | ', $cardlist);
            }

            if ($value['payment_cycle'] == "1")
            {
                $models[$key]['payment_cycle'] = "年";
            }
            elseif ($value['payment_cycle'] == "2")
            {
                $models[$key]['payment_cycle'] = "月";
            }
            elseif ($value['payment_cycle'] == "3")
            {
                $models[$key]['payment_cycle'] = "天";
            }

            $models[$key]['sell_price']    = $value['sell_price'];
            $models[$key]['price_quarter'] = $value['price_quarter'];
            $models[$key]['price_half']    = $value['price_half'];
            $models[$key]['price_year']    = $value['price_year'];
            $models[$key]['status']        = $value['status'] ? '公开' : '隐藏';
            $models[$key]['orders']        = $value['orders'];
            $models[$key]['recommend']     = $value['recommend'] ? '是' : '否';
        }

        $newExcel = new Spreadsheet;
        $objSheet = $newExcel->getActiveSheet(0);  //获取当前操作sheet的对象
        $objSheet->setTitle('自有机器信息表');            //设置当前sheet的标题
        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        #水平方向对齐
        $newExcel->getDefaultStyle()->getAlignment()
                 ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        #垂直方向对齐
        $newExcel->getDefaultStyle()->getAlignment()
                 ->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        #设置列宽
        $newExcel->getActiveSheet()->getColumnDimension('A')->setWidth(6);
        $newExcel->getActiveSheet()->getColumnDimension('B')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('C')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('D')->setWidth(25);
        $newExcel->getActiveSheet()->getColumnDimension('E')->setWidth(25);
        $newExcel->getActiveSheet()->getColumnDimension('F')->setWidth(25);
        $newExcel->getActiveSheet()->getColumnDimension('G')->setWidth(25);
        $newExcel->getActiveSheet()->getColumnDimension('H')->setWidth(25);
        $newExcel->getActiveSheet()->getColumnDimension('I')->setWidth(25);
        $newExcel->getActiveSheet()->getColumnDimension('J')->setWidth(25);
        $newExcel->getActiveSheet()->getColumnDimension('K')->setWidth(10);

        $newExcel->getActiveSheet()->getColumnDimension('L')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('M')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('N')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('O')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('P')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('Q')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('R')->setWidth(10);


        #设置列名
        $objSheet->setCellValue('A1', 'ID号');
        $objSheet->setCellValue('B1', '名称');
        $objSheet->setCellValue('C1', '所属分类');
        $objSheet->setCellValue('D1', 'CPU');
        $objSheet->setCellValue('E1', '内存');
        $objSheet->setCellValue('F1', '硬盘');
        $objSheet->setCellValue('G1', '带宽');
        $objSheet->setCellValue('H1', 'IP数');
        $objSheet->setCellValue('I1', '防御');
        $objSheet->setCellValue('J1', '操作系统');
        $objSheet->setCellValue('K1', '显卡');
        $objSheet->setCellValue('L1', '周期单位');
        $objSheet->setCellValue('M1', '月付价格');
        $objSheet->setCellValue('N1', '季付价格');
        $objSheet->setCellValue('O1', '半年付价格');
        $objSheet->setCellValue('P1', '年付价格');
        $objSheet->setCellValue('Q1', '状态');
        $objSheet->setCellValue('R1', '排序');
        $objSheet->setCellValue('S1', '是否推荐');


        $data = [];

        foreach ($models as $key => $val)
        {

            $k = $key + 2;

            $objSheet->setCellValue('A' . $k, $val['id']);
            $objSheet->setCellValue('B' . $k, $val['name']);
            $objSheet->setCellValue('C' . $k, $val['pdt_type_id']);
            $objSheet->setCellValue('D' . $k, $val['cpu']);
            $objSheet->setCellValue('E' . $k, $val['ram']);
            $objSheet->setCellValue('F' . $k, $val['hdd']);
            $objSheet->setCellValue('G' . $k, $val['bandwidth']);
            $objSheet->setCellValue('H' . $k, $val['ipnumber']);
            $objSheet->setCellValue('I' . $k, $val['defense']);
            $objSheet->setCellValue('J' . $k, $val['operatsystem']);
            $objSheet->setCellValue('K' . $k, $val['operatsystem']);
            $objSheet->setCellValue('L' . $k, $val['payment_cycle']);

            $objSheet->setCellValue('M' . $k, $val['sell_price']);
            $objSheet->setCellValue('N' . $k, $val['price_quarter']);
            $objSheet->setCellValue('O' . $k, $val['price_half']);
            $objSheet->setCellValue('P' . $k, $val['price_year']);
            $objSheet->setCellValue('Q' . $k, $val['status']);
            $objSheet->setCellValue('R' . $k, $val['orders']);
            $objSheet->setCellValue('S' . $k, $val['recommend']);

        }
        //设置第一栏的标题

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=配置类别信息表_" . date('Y-m-d', time()) . '.' . strtolower("Xlsx"));
        header('Cache-Control: max-age=0');
        $objWriter = IOFactory::createWriter($newExcel, 'Xlsx');
        $objWriter->save('php://output');

    }

    /**
     * 添加修改信息
     */
    public function actionDoItem()
    {

        $id   = $this->post('id');
        $id = intval($id);
        $post = $this->post();

        $PdtManageModel = new PdtManage();

        if ( !isset($post['pdt_type_id']))
        {
            $arrReturn = [
                'status' => 0,
                'info'   => "配置分类必须选择",
            ];
            return $this->renderJSON($arrReturn);
        }

        $pdt_type_id         = $post['pdt_type_id'];
        $post['pdt_type_id'] = trim(implode(",", $pdt_type_id));

        //CPU
        $cpuconfig          = [];
        $config_Cpu_price = $this->post('config_Cpu_price');
        $config_Cpu_default = $this->post('config_Cpu_default');
        if (empty($this->post('config_Cpu_openly')) || empty($config_Cpu_default))
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '至少选择和默认一个CPU信息'
            ];
            return $this->renderJSON($arrReturn);
        }
        if ( !in_array($config_Cpu_default, $this->post('config_Cpu_openly')))
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '默认CPU信息未被选择'
            ];
            return $this->renderJSON($arrReturn);
        }

        foreach ($this->post('config_Cpu_openly') as $key => $value)
        {
            $key              = array_search($value, $this->post("config_Cpu_id"));
            $config['id'] = $this->post('config_Cpu_id')[$key];
            $config['openly'] = $value;
            $config['price']  = $config_Cpu_price[$key];
            $config['name']   = $this->post('config_Cpu_name')[$key];
            #当默认配置的价格不为零时，会导致价格不匹配，这里判断默认配置必须为零
            if ($config['id'] == $config_Cpu_default)
            {
                if ($config['price'] != 0)
                {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '默认CPU配置价格只能为0'
                    ];
                    return $this->renderJSON($arrReturn);
                }
                array_unshift($cpuconfig, $config);
            }
            else
            {
                array_push($cpuconfig, $config);
            }

        }
        $cpuconfigRes['default'] = $config_Cpu_default;
        $cpuconfigRes['info']    = $cpuconfig;

        //内存
        $ramconfig          = [];
        $config_Ram_price = $this->post('config_Ram_price');
        $config_Ram_default = $this->post('config_Ram_default');
        if (empty($this->post('config_Ram_openly')) || empty($config_Ram_default))
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '至少选择和默认一个内存大小信息'
            ];
            return $this->renderJSON($arrReturn);
        }
        if ( !in_array($config_Ram_default, $this->post('config_Ram_openly')))
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '默认内存大小信息未被选择'
            ];
            return $this->renderJSON($arrReturn);
        }
        foreach ($this->post('config_Ram_openly') as $value)
        {
            $key              = array_search($value, $this->post("config_Ram_id"));
            $config['id'] = $this->post("config_Ram_id")[$key];
            $config['openly'] = $value;
            $config['price']  = $config_Ram_price[$key];
            $config['name']   = $this->post('config_Ram_name')[$key];
            #当默认配置的价格不为零时，会导致价格不匹配，这里判断默认配置必须为零
            if ($config['id'] == $config_Ram_default)
            {
                if ($config['price'] != 0)
                {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '默认内存配置价格只能为0'
                    ];
                    return $this->renderJSON($arrReturn);
                }
                array_unshift($ramconfig, $config);
            }
            else
            {
                array_push($ramconfig, $config);
            }
        }
        $ramconfigRes['default'] = $config_Ram_default;
        $ramconfigRes['info']    = $ramconfig;

        //硬盘
        $hddconfig          = [];
        $config_Hdd_price = $this->post('config_Hdd_price');
        $config_Hdd_default = $this->post('config_Hdd_default');

        if (empty($this->post('config_Hdd_openly')) || empty($config_Hdd_default))
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '至少选择和默认一个硬盘信息'
            ];
            return $this->renderJSON($arrReturn);
        }
        if ( !in_array($config_Hdd_default, $this->post('config_Hdd_openly')))
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '默认硬盘信息未被选择'
            ];
            return $this->renderJSON($arrReturn);
        }

        foreach ($this->post('config_Hdd_openly') as $key => $value)
        {
            $key              = array_search($value, $this->post("config_Hdd_id"));
            $config['id'] = $this->post('config_Hdd_id')[$key];
            $config['openly'] = $value;
            $config['price']  = $config_Hdd_price[$key];
            $config['name']   = $this->post('config_Hdd_name')[$key];
            #当默认配置的价格不为零时，会导致价格不匹配，这里判断默认配置必须为零
            if ($config['id'] == $config_Hdd_default)
            {
                if ($config['price'] != 0)
                {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '默认硬盘配置价格只能为0'
                    ];
                    return $this->renderJSON($arrReturn);
                }
                array_unshift($hddconfig, $config);
            }
            else
            {
                array_push($hddconfig, $config);
            }
        }

        $hddconfigRes['default'] = $config_Hdd_default;
        $hddconfigRes['info']    = $hddconfig;
        //带宽
        $bandwidthconfig          = [];
        $config_Bandwidth_price = $this->post('config_Bandwidth_price');
        $config_Bandwidth_default = $this->post('config_Bandwidth_default');
        if (empty($this->post('config_Bandwidth_openly')) || empty($config_Bandwidth_default))
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '至少选择和默认一个带宽信息'
            ];
            return $this->renderJSON($arrReturn);
        }
        if ( !in_array($config_Bandwidth_default, $this->post('config_Bandwidth_openly')))
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '默认带宽信息未被选择'
            ];
            return $this->renderJSON($arrReturn);
        }
        foreach ($this->post('config_Bandwidth_openly') as $key => $value)
        {
            $key              = array_search($value, $this->post("config_Bandwidth_id"));
            $config['id'] = $this->post('config_Bandwidth_id')[$key];
            $config['openly'] = $value;
            $config['price']  = $config_Bandwidth_price[$key];
            $config['name']   = $this->post('config_Bandwidth_name')[$key];
            #当默认配置的价格不为零时，会导致价格不匹配，这里判断默认配置必须为零
            if ($config['id'] == $config_Bandwidth_default)
            {
                if ($config['price'] != 0)
                {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '默认带宽配置价格只能为0'
                    ];
                    return $this->renderJSON($arrReturn);
                }
                array_unshift($bandwidthconfig, $config);
            }
            else
            {
                array_push($bandwidthconfig, $config);
            }
        }
        $bandwidthconfigRes['default'] = $config_Bandwidth_default;
        $bandwidthconfigRes['info']    = $bandwidthconfig;
        //IP数
        $ipnumberconfig          = [];
        $config_Ipnumber_price = $this->post('config_Ipnumber_price');
        $config_Ipnumber_default = $this->post('config_Ipnumber_default');
        if (empty($this->post('config_Ipnumber_openly')) || empty($config_Ipnumber_default))
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '至少选择和默认一个IP数量信息'
            ];
            return $this->renderJSON($arrReturn);
        }
        if ( !in_array($config_Ipnumber_default, $this->post('config_Ipnumber_openly')))
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '默认IP数量信息未被选择'
            ];
            return $this->renderJSON($arrReturn);
        }
        foreach ($this->post('config_Ipnumber_openly') as $key => $value)
        {
            $key              = array_search($value, $this->post("config_Ipnumber_id"));
            $config['id'] = $this->post('config_Ipnumber_id')[$key];
            $config['openly'] = $value;
            $config['price']  = $config_Ipnumber_price[$key];
            $config['name']   = $this->post('config_Ipnumber_name')[$key];
            #当默认配置的价格不为零时，会导致价格不匹配，这里判断默认配置必须为零
            if ($config['id'] == $config_Ipnumber_default)
            {
                if ($config['price'] != 0)
                {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '默认IP数配置价格只能为0'
                    ];
                    return $this->renderJSON($arrReturn);
                }
                array_unshift($ipnumberconfig, $config);
            }
            else
            {
                array_push($ipnumberconfig, $config);
            }
        }
        $ipnumberconfigRes['default'] = $config_Ipnumber_default;
        $ipnumberconfigRes['info']    = $ipnumberconfig;
        //防御流量
        $defenseconfig          = [];
        $config_Defense_price = $this->post('config_Defense_price');
        $config_Defense_default = $this->post('config_Defense_default');
        /*if(empty($this->post('config_Defense_openly')) || empty($config_Defense_default))
        {
            $arrReturn = [
                'status'=>0,
                'info'=>'至少选择和默认一个防御流量信息'
            ];
            return $this->renderJSON($arrReturn);
        }
        if(!in_array($config_Defense_default, $this->post('config_Defense_openly')))
        {
            $arrReturn = [
                'status'=>0,
                'info'=>'默认防御流量信息未被选择'
            ];
            return $this->renderJSON($arrReturn);
        }  */
        if ( !empty($this->post('config_Defense_openly')))
        {
            foreach ($this->post('config_Defense_openly') as $key => $value)
            {
                $key              = array_search($value, $this->post("config_Defense_id"));
                $config['id'] = $this->post('config_Defense_id')[$key];
                $config['openly'] = $value;
                $config['price']  = $config_Defense_price[$key];
                $config['name']   = $this->post('config_Defense_name')[$key];
                #当默认配置的价格不为零时，会导致价格不匹配，这里判断默认配置必须为零
                if ($config['id'] == $config_Defense_default)
                {
                    if ($config['price'] != 0)
                    {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => '默认防御配置价格只能为0'
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                    array_unshift($defenseconfig, $config);
                }
                else
                {
                    array_push($defenseconfig, $config);
                }
            }
            $defenseconfigRes['default'] = $config_Defense_default;
            $defenseconfigRes['info']    = $defenseconfig;
        }
        else
        {
            $defenseconfigRes = "";
        }

        //操作系统
        $systemconfig          = [];
        $config_System_price = $this->post('config_System_price');
        $config_System_default = $this->post('config_System_default');
        if (empty($this->post('config_System_openly')) || empty($config_System_default))
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '至少选择和默认一个操作系统信息'
            ];
            return $this->renderJSON($arrReturn);
        }
        if ( !in_array($config_System_default, $this->post('config_System_openly')))
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '默认操作系统信息未被选择'
            ];
            return $this->renderJSON($arrReturn);
        }

        foreach ($this->post('config_System_openly') as $key => $value)
        {
            $key = array_search($value, $this->post("config_System_id"));

            $config['id']     = $this->post('config_System_id')[$key];
            $config['openly'] = $value;
            $config['price']  = $config_System_price[$key];
            $config['name']   = $this->post('config_System_name')[$key];

            if ($config['id'] == $config_System_default)
            {
                array_unshift($systemconfig, $config);
            }
            else
            {
                array_push($systemconfig, $config);
            }
        }

        $systemconfigRes['default'] = $config_System_default;
        $systemconfigRes['info']    = $systemconfig;


        $cardconfig          = [];
        $config_Card_price = $this->post('config_Card_price');
        $config_Card_default = $this->post('config_Card_default');
        if ( !empty($this->post('config_Card_openly')))
        {
            foreach ($this->post('config_Card_openly') as $key => $value)
            {
                $key             = array_search($value, $this->post('config_Card_id'));
                $array['id'] = $this->post('config_Card_id')[$key];
                $array['openly'] = $value;
                $array['price']  = $config_Card_price[$key];
                $array['name']   = $this->post('config_Card_name')[$key];
                if ($array['id'] == $config_Card_default)
                {
                    array_unshift($cardconfig, $array);
                }
                else
                {
                    array_push($cardconfig, $array);
                }
            }
            $cardconfigRes['default'] = $config_Card_default;
            $cardconfigRes['info']    = $cardconfig;
        }
        else
        {
            $cardconfigRes = '';
        }
        $post['id']           = $id;
        $post['cpu'] = json_encode($cpuconfigRes, JSON_UNESCAPED_UNICODE);
        $post['ram'] = json_encode($ramconfigRes, JSON_UNESCAPED_UNICODE);
        $post['hdd'] = json_encode($hddconfigRes, JSON_UNESCAPED_UNICODE);
        $post['bandwidth'] = json_encode($bandwidthconfigRes, JSON_UNESCAPED_UNICODE);
        $post['ipnumber']  = json_encode($ipnumberconfigRes, JSON_UNESCAPED_UNICODE);
        $post['defense']   = json_encode($defenseconfigRes, JSON_UNESCAPED_UNICODE);
        $post['operatsystem'] = json_encode($systemconfigRes, JSON_UNESCAPED_UNICODE);
        $post['card']         = json_encode($cardconfigRes, JSON_UNESCAPED_UNICODE);

        //$room_id = $post['room_id'];
        //$post['room_id'] = trim(implode(",", $room_id));
        //修改处理...

        if ($id > 0)
        {
            $PdtManageModelQuery = $PdtManageModel->findOne($id);     //print_r($PdtManageModelQuery);die();
            #进入场景
            $PdtManageModelQuery->scenario   = 'pdtmodify';
            $PdtManageModelQuery->attributes = $post;

            #数据验证
            if ( !$PdtManageModelQuery->validate())
            {
                $arrReturn = [
                    'status' => 0,
                    'info'   => $PdtManageModelQuery->errors
                ];
                return $this->renderJSON($arrReturn);
            }
            /* $newModel = $PdtManageModelQuery->Attributes;//print_r($newModel);
             $oldModel = $PdtManageModelQuery->oldAttributes;//print_r($oldModel);
             $arrayModel = array_diff_assoc($newModel,$oldModel);//print_r($arrayModel);
             $message = "管理员".$this->getAdminInfo('uname')."修改了产品配置类别表ID为".$id."的信息，";*/
            /**
             * 更新信息
             */


            if ($PdtManageModelQuery->update())
            {
                $arrReturn = [
                    'status' => 1,
                    'info'   => '产品配置信息修改成功'
                ];
                /*foreach ($arrayModel as $key=>$value)
                {
                    $message .= $PdtManageModel->attributeLabels()[$key].':'.$oldModel[$key]."改为".$value.",";
                }
                $PdtLogModel->doadd($this->getAdminInfo('admin_id'), $this->getAdminInfo('uname'), $message);*/

            }
            else
            {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '未修改信息'
                ];
            }
            return $this->renderJSON($arrReturn);
        }
        /*//添加
        $RoomManageModel = new PdtRoomMange();
        if (!empty($room_id)) {
            foreach ($room_id as $value) {
                //判断机房信息是否存在
                $roomResult = $RoomManageModel->getRowById($value);
                if(empty($roomResult)) {
                    $arrReturn = [
                        'status'=>0,
                        'info'=>'未知的机房信息'
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }
        }*/

        #进入场景
        $PdtManageModel->scenario   = 'pdtadd';
        $PdtManageModel->attributes = $post;

        #数据验证
        if ( !$PdtManageModel->validate())
        {
            $arrReturn = [
                'status' => 0,
                'info'   => $PdtManageModel->errors
            ];
            return $this->renderJSON($arrReturn);
        }
        if ($PdtManageModel->addPdtmanage())
        {
            $arrReturn = [
                'status' => 1,
                'info'   => '产品配置信息添加成功'
            ];
            /*$arrayModel = $PdtManageModel->attributes;
            $message = "管理员".$this->getAdminInfo('uname')."添加了产品配置类别表一条信息，";
            unset($arrayModel['create_time']);
            foreach ($arrayModel as $key=>$value)
            {
                $message .= $PdtManageModel->attributeLabels()[$key].':'.$value.",";
            }
            $PdtLogModel->doadd($this->getAdminInfo('admin_id'), $this->getAdminInfo('uname'), $message);*/
        }
        else
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '产品配置信息添加失败'
            ];
        }
        return $this->renderJSON($arrReturn);

    }

    public function actionUpdateorder()
    {
        Yii::$app->request->isAjax || die('error');

        if ($this->post('id') == '')
            return $this->renderJSON(['status' => 0, 'info' => '操作失效']);
        $post = $this->post();

        $PdtManageModel      = new PdtManage();
        $PdtManageModelQuery = $PdtManageModel->findOne($this->post('id'));
        //print_r($PdtManageModelQuery);die();
        #进入场景
        $PdtManageModelQuery->scenario   = 'updateorders';
        $PdtManageModelQuery->attributes = $post;

        #数据验证
        if ( !$PdtManageModelQuery->validate())
        {
            $arrReturn = [
                'status' => 0,
                'info'   => $PdtManageModelQuery->errors
            ];
            return $this->renderJSON($arrReturn);
        }
        /*$newModel = $PdtManageModelQuery->Attributes;//print_r($newModel);
        $oldModel = $PdtManageModelQuery->oldAttributes;//print_r($oldModel);
        $arrayModel = array_diff_assoc($newModel,$oldModel);//print_r($arrayModel);
        $message = "管理员".$this->getAdminInfo('uname')."修改了产品配置类别表ID为".$this->post('id')."的排序数，";*/
        /**
         * 更新信息
         */
        if ($PdtManageModelQuery->update())
        {
            $arrReturn = [
                'status' => 1,
                'info'   => '排序数更改成功'
            ];
            /* foreach ($arrayModel as $key=>$value)
             {
                 $message .= $PdtManageModel->attributeLabels()[$key].':'.$oldModel[$key]."改为".$value.",";
             }
             $PdtLogModel->doadd($this->getAdminInfo('admin_id'), $this->getAdminInfo('uname'), $message);*/
        }
        else
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '未修改信息'
            ];
        }
        return $this->renderJSON($arrReturn);
    }

    /**
     * 删除数据处理方法
     */
    public function actionDel()
    {
        Yii::$app->request->isAjax || die('error');

        if ($this->post('id') == '')
            return $this->renderJSON(['status' => 0, 'info' => '操作失效']);

        $PdtManageModel = new PdtManage();

        if (is_array($this->post('id')))
        {
            foreach ($this->post('id') as $value)
            {
                $PdtManageRes = $PdtManageModel->getRowById($value);
                $array[]      = $PdtManageRes;
                if (empty($PdtManageRes))
                {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '删除失败,未知的产品配置'
                    ];
                    return $this->renderJSON($arrReturn);
                }
                $MemberPdtModel = new MemberPdt();
                $pdtmanageCount = $MemberPdtModel->find()->where('pdt_id=:pdt_id', [':pdt_id' => $value])->count();
                //echo $pdttypeCount;die();
                //判断配置下是否有产品
                if ($pdtmanageCount > 0)
                {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => 'ID为' . $value . '的产品配置下还有用户产品，请先处理用户产品'
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }
        }
        else
        {
            $PdtManageRes = $PdtManageModel->getRowById($this->post('id'));
            $array        = $PdtManageRes;
            if (empty($PdtManageRes))
            {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '删除失败,未知的产品配置'
                ];
                return $this->renderJSON($arrReturn);
            }
            $MemberPdtModel = new MemberPdt();
            $id             = $this->post('id');
            $pdtmanageCount = $MemberPdtModel->find()->where('pdt_id=:pdt_id', [':pdt_id' => $id])->count();
            //echo $pdttypeCount;die();
            //判断配置下是否有产品
            if ($pdtmanageCount > 0)
            {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '该产品配置下还有用户产品，请先处理用户产品'
                ];
                return $this->renderJSON($arrReturn);
            }
        }

        if ($PdtManageModel->del($this->post('id')) > 0)
        {
            $arrReturn = [
                'status' => 1,
                'info'   => '产品配置信息删除成功'
            ];
            LogWrite::AdminDelLog($this->post('id'), $array, $PdtManageModel->attributeLabels(), $PdtManageModel->tableNamealias());
        }
        else
        {
            $arrReturn = [
                'status' => 0,
                'info'   => '产品配置信息删除失败'
            ];
        }
        return $this->renderJSON($arrReturn);
    }
}
