<?php

use addons\VymCloud\common\models\CloudLogs;
use common\enums\AppEnum;
use common\helpers\ArrayHelper;
use common\helpers\Html;
use common\helpers\Url;
use yii\grid\ActionColumn;
use yii\grid\GridView;
use yii\widgets\ActiveForm;

/** @var yii\web\View $this */
/** @var yii\data\ActiveDataProvider $dataProvider */
/** @var common\models\base\SearchModel $searchModel */

$this->title                   = '操作日志管理';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="cloud-operation-log-index">
    <div class="box">
        <div class="box-header with-border">
            <h3 class="box-title"><?= Html::encode($this->title) ?></h3>
        </div>
        <div class="box-body">
            <?php $form = ActiveForm::begin(['action' => ['index'], 'method' => 'get', 'options' => ['class' => 'form-inline']]); ?>
            <div class="row">
                <?php if (Yii::$app->id == AppEnum::BACKEND): ?>
                <div class="col-sm-3">
                    <?= Html::dropDownList('merchant_id', Yii::$app->request->get('merchant_id'),
                        ArrayHelper::map(Yii::$app->services->merchant->findAllByCondition([]), 'id', 'title'), [
                        'class' => 'form-control',
                        'prompt' => '选择商户',
                    ]) ?>
                </div>
                <?php endif; ?>
                <div class="col-sm-3">
                    <?= Html::textInput('action', Yii::$app->request->get('action'), [
                        'class' => 'form-control',
                        'placeholder' => '操作描述',
                    ]) ?>
                </div>
                <div class="col-sm-3">
                    <?= Html::textInput('ip_address', Yii::$app->request->get('ip_address'), [
                        'class' => 'form-control',
                        'placeholder' => '用户IP地址',
                    ]) ?>
                </div>
                <div class="col-sm-3">
                    <?= Html::textInput('method', Yii::$app->request->get('method'), [
                        'class' => 'form-control',
                        'placeholder' => '操作方式',
                    ]) ?>
                </div>
            </div>
            <div class="row" style="margin-top: 10px;">
                <div class="col-sm-3">
                    <?= Html::submitButton('搜索', ['class' => 'btn btn-primary']) ?>
                    <?= Html::a('重置', ['index'], ['class' => 'btn btn-default']) ?>
                </div>
            </div>
            <?php ActiveForm::end(); ?>

            <div class="grid-view" style="margin-top: 20px;">
                <?= GridView::widget([
                    'dataProvider' => $dataProvider,
                    'tableOptions' => ['class' => 'table table-hover'],
                    'options' => ['class' => 'grid-view', 'style' => 'overflow:auto', 'id' => 'grid'],
                    'columns' => [
                        [
                            'class' => 'yii\grid\CheckboxColumn',
                            'name' => 'selection',
                        ],
                        'log_id',
                        [
                            'label'          => '所属商户',
                            'format'         => 'raw',
                            'visible'        => Yii::$app->id == AppEnum::BACKEND, // 只在总后台显示
                            'value'          => function ($model) {
                                if ($model->merchant && $mId = $model->merchant->id) {
                                    if ($mTitle = $model->merchant->title) {
                                        return Html::tag('span', $mTitle, ['class' => 'badge badge-primary']);
                                    }
                                    return Html::tag('span', "ID:{$mId}", ['class' => 'badge badge-primary']);
                                }
                                return Html::tag('span', '平台直属', ['class' => 'badge badge-secondary']);
                            },
                        ],
                        'user_id',
                        [
                            'attribute' => 'user_type',
                            'value' => function ($model) {
                                return $model->user_type == 1 ? '管理员' : '用户';
                            },
                        ],
                        'action',
                        'ip_address',
                        'method',
                        [
                            'attribute' => 'created_at',
                            'format' => ['date', 'php:Y-m-d H:i:s'],
                        ],
                        [
                            'class' => 'yii\grid\ActionColumn',
                            'template' => '{view} {delete}',
                            'buttons' => [
                                'view' => function ($url) {
                                    return Html::a('<span class="glyphicon glyphicon-eye-open"></span>', $url, [
                                        'title' => '查看详情',
                                        'class' => 'btn btn-default btn-sm',
                                    ]);
                                },
                                'delete' => function ($url) {
                                    return Html::a('<span class="glyphicon glyphicon-trash"></span>', $url, [
                                        'title' => '删除',
                                        'class' => 'btn btn-danger btn-sm',
                                        'data' => [
                                            'confirm' => '确定要删除这条记录吗？',
                                            'method' => 'post',
                                        ],
                                    ]);
                                },
                            ],
                        ],
                    ],
                ]); ?>
            </div>

            <div class="box-footer clearfix">
                <div class="btn-group">
                    <?= Html::button('批量删除', [
                        'class' => 'btn btn-danger',
                        'id' => 'batch-delete',
                    ]) ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$js = <<<JS
    // 批量删除
    $('#batch-delete').click(function() {
        var keys = $('#grid').yiiGridView('getSelectedRows');
        if (keys.length == 0) {
            alert('请选择要删除的记录');
            return;
        }
        if (confirm('确定要删除选中的记录吗？')) {
            $.post('batch-delete', {ids: keys}, function(response) {
                location.reload();
            });
        }
    });
JS;
$this->registerJs($js);
?>
