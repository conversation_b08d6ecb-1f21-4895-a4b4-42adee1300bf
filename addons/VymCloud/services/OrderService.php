<?php

namespace addons\VymCloud\services;

use addons\VymCloud\common\enums\ClusterEnum;
use addons\VymCloud\common\enums\IpsEnum;
use addons\VymCloud\common\enums\OrderEnum;
use addons\VymCloud\common\models\CloudCluster;
use addons\VymCloud\common\models\CloudIps;
use addons\VymCloud\common\models\CloudOrder;
use addons\VymCloud\common\models\CloudProduct;
use addons\VymCloud\common\models\CloudServer;
use addons\VymCloud\common\models\CloudSystem;
use common\helpers\ArrayHelper;
use common\helpers\EchantsHelper;
use common\helpers\StringHelper;
use Exception;
use Yii;
use yii\db\ActiveRecord;

/**
 * Class OrderService
 * 1. 业务订单的创建订单，获取支付链接
 * 2. 业务订单的完成支付，修改订单状态，并创建业务实例
 * 3. 业务订单的支付失败或超时，修改订单状态
 * 4. 业务订单的列表、查看、删除、退款，人工修改订单状态等基本功能
 */
class OrderService
{
    /**
     * 创建订单
     *
     * @param array $params
     *
     * @return array
     */
    public function createOrder(array $params)
    {
        try {
            $uid         = ArrayHelper::getValue($params, 'uid');
            $remark      = ArrayHelper::getValue($params, 'remark');
            $buy_cycle   = ArrayHelper::getValue($params, 'buy_cycle');
            $ssh_pass    = ArrayHelper::getValue($params, 'ssh_pass');
            $cluster_id  = ArrayHelper::getValue($params, 'cluster_id');
            $product_id  = ArrayHelper::getValue($params, 'product_id');
            $coupon_id   = ArrayHelper::getValue($params, 'coupon_id');
            $buy_detail  = ArrayHelper::getValue($params, 'buy_detail');
            $server_name = ArrayHelper::getValue($params, 'server_name');
            $system_name = ArrayHelper::getValue($params, 'system_name');

            //验证必要参数，给出具体的提示
            if (!$uid) {
                throw new Exception('用户不能为空');
            }
            //如果未定义密码，随机生成
            if (!$ssh_pass) {
                $ssh_pass = StringHelper::random(12);
                ArrayHelper::setValue($params, 'ssh_pass', $ssh_pass);
            }

            // 检查产品库存
            $product = CloudProduct::findOne($product_id);
            if (!$product) {
                throw new Exception('产品不存在');
            }

            // 检查库存是否充足（stock=0表示无限库存）
            if (!$product->hasStock()) {
                throw new Exception("产品库存不足，当前库存：{$product->stock}");
            }

            $priceRe = Yii::$app->vymCloudService->product->calculatePrice($product_id, $params);

            // 获取计算价格结果
            $detail      = ArrayHelper::getValue($priceRe, 'detail');
            $renew_mod   = ArrayHelper::getValue($priceRe, 'renew_mod');
            $renew_auto  = ArrayHelper::getValue($priceRe, 'renew_auto');
            $renew_price = ArrayHelper::getValue($priceRe, 'renew_price');
            unset($priceRe['detail']);

            $system_id = ArrayHelper::getValue($buy_detail, 'system_id');
            $system    = CloudSystem::find()->where(['id' => $system_id])->one();
            if (!$system) {
                throw new Exception('系统镜像不存在');
            }

            // 续费周期和续费价格赋值给server_detail，支付成功后，创建实例时要使用
            ArrayHelper::setValue($detail, 'server_name', $server_name);
            ArrayHelper::setValue($detail, 'system_name', ArrayHelper::getValue($system, 'title'));
            ArrayHelper::setValue($detail, 'remark', $remark);
            ArrayHelper::setValue($detail, 'renew_mod', $renew_mod);
            ArrayHelper::setValue($detail, 'renew_auto', $renew_auto);
            ArrayHelper::setValue($detail, 'renew_price', $renew_price);

            //获取集群ID
            $cluster = CloudCluster::find()->where(['id' => $cluster_id])->one();
            if (!$cluster) {
                throw new Exception('集群节点不存在');
            }
            if ($cluster->cluster_st === ClusterEnum::STATUS_DISABLED) {
                throw new Exception('集群节点已禁用');
            }
            // 获取集群下可用IP
            $ip_num = ArrayHelper::getValue($buy_detail, 'ip_num');
            $ips    = CloudIps::find()->where([
                'cluster_id' => $cluster_id,
                'is_use'     => IpsEnum::STATUS_AVAILABLE,
                'type'       => IpsEnum::TYPE_IP4,
                'is_inside'  => IpsEnum::NOT_INNER,
            ])->limit($ip_num)->all();
            if (count($ips) < $ip_num) {
                throw new Exception('集群节点下可用IP不足');
            }
            // 使用数据库事务确保数据一致性
            $transaction = Yii::$app->db->beginTransaction();

            try {
                //创建订单
                $order                  = new CloudOrder();
                $order->uid             = $uid;
                $order->buy_cycle       = $buy_cycle;
                $order->order_st        = OrderEnum::ORDER_STATUS_PENDING;
                $order->order_type      = OrderEnum::ORDER_TYPE_BUY;
                $order->order_no        = CloudOrder::createOrderNo();
                $order->payment_no      = CloudOrder::createOrderNo('CZ');
                $order->amount_pay      = ArrayHelper::getValue($priceRe, 'amount_pay');
                $order->amount_total    = ArrayHelper::getValue($priceRe, 'amount_total');
                $order->amount_coupon   = ArrayHelper::getValue($priceRe, 'amount_coupon');
                $order->amount_discount = ArrayHelper::getValue($priceRe, 'amount_discount');
                $order->coupon_id       = $coupon_id;
                $order->product_id      = $product_id;
                $order->server_detail   = $detail;
                $order->price_detail    = $priceRe;
                $order->remark          = $remark;

                //保存订单
                if (!$order->save()) {
                    throw new Exception('创建订单失败: ' . implode(', ', $order->getFirstErrors()));
                }

                //如果订单包含了优惠券，保存成功，则需要更改优惠券的使用状态
                if ($coupon_id) {
                    // TODO: 实现优惠券功能
                    // $coupon = Coupon::findOne(['key' => $coupon_id, 'uid' => $uid]);
                    // if ($coupon) {
                    //     $coupon->status = StatusEnum::DISABLED;
                    //     if (!$coupon->save()) {
                    //         throw new Exception('优惠券使用失败');
                    //     }
                    // }
                }

                $transaction->commit();

                return ['code' => 1, 'msg' => '订单创建成功', 'data' => $order];
            } catch (Exception $e) {
                $transaction->rollBack();
                throw $e;
            }
        } catch (Exception $e) {
            isset($transaction) && $transaction->rollBack();
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 创建续费订单
     *
     * @param int $serverId 服务器ID
     * @param int $months   续费月数
     *
     * @return array
     */
    public function createRenewOrder($serverId, $months)
    {
        try {
            // 获取服务器信息
            $server = CloudServer::findOne($serverId);
            if (!$server) {
                return ['code' => 0, 'msg' => '服务器不存在'];
            }

            // 检查续费金额
            if (!$server->renew_amount || $server->renew_amount <= 0) {
                return ['code' => 0, 'msg' => '服务器续费价格未设置'];
            }

            // 计算续费金额
            $totalAmount = $server->renew_amount * $months;

            // 计算新的到期时间
            $currentEndTime = $server->end_at;
            $newEndTime     = date('Y-m-d H:i:s', strtotime($currentEndTime . " +{$months} months"));
            $extend = ArrayHelper::getValue($server, 'extend');
            if($extend){
                if(is_string($extend)){
                    $extend = json_decode($extend, true);
                }
            }
            else {
                $extend = [];
            }
            $upgrade_results = ArrayHelper::getValue($extend, 'price_detail.upgrade_results');
            if(empty($upgrade_results)){
                return ['code' => 0, 'msg' => '服务器升级配置数据错误'];
            }

            // 构建续费订单详情
            $serverDetail = [
                'server_id'        => $server->id,
                'server_name'      => $server->name ?: $server->title,
                'renew_months'     => $months,
                'current_end_time' => $currentEndTime,
                'new_end_time'     => $newEndTime,
                'monthly_amount'   => $server->renew_amount,
                'total_amount'     => $totalAmount,
            ];

            // 构建价格详情
            $priceDetail = [
                'monthly_price'   => $server->renew_amount,
                'months'          => $months,
                'total_amount'    => $totalAmount,
                'amount_pay'      => $totalAmount,
                'amount_discount' => 0,
                'amount_coupon'   => 0,
                'upgrade_results' => $upgrade_results,
            ];

            $transaction = Yii::$app->db->beginTransaction();

            try {
                // 创建续费订单
                $order                  = new CloudOrder();
                $order->uid             = $server->uid;
                $order->buy_cycle       = $months;
                $order->order_st        = OrderEnum::ORDER_STATUS_PENDING;
                $order->order_type      = OrderEnum::ORDER_TYPE_RENEW;
                $order->order_no        = CloudOrder::createOrderNo();
                $order->payment_no      = CloudOrder::createOrderNo('XF');
                $order->amount_pay      = $totalAmount;
                $order->amount_total    = $totalAmount;
                $order->amount_coupon   = 0;
                $order->amount_discount = 0;
                $order->server_id       = $server->id;
                $order->server_detail   = $serverDetail;
                $order->price_detail    = $priceDetail;
                $order->remark          = "服务器续费 {$months} 个月";

                // 保存订单
                if (!$order->save()) {
                    throw new Exception('创建续费订单失败: ' . implode(', ', $order->getFirstErrors()));
                }

                $transaction->commit();

                return ['code' => 1, 'msg' => '续费订单创建成功', 'data' => $order];
            } catch (Exception $e) {
                $transaction->rollBack();
                throw $e;
            }
        } catch (Exception $e) {
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 更新订单状态
     *
     * @param     $params
     *
     * @return array
     */
    public function updateStatus($params)
    {
        try {
            $order_no   = ArrayHelper::getValue($params, 'order_no');
            $status     = ArrayHelper::getValue($params, 'status');
            $payment_no = ArrayHelper::getValue($params, 'payment_no');
            $amount     = ArrayHelper::getValue($params, 'amount'); //商户返回的金额

            // 使用数据库事务确保数据一致性
            $transaction = Yii::$app->db->beginTransaction();
            $order       = $this->findByOrderNo($order_no);
            // 判断订单是否存在
            if (!$order) {
                throw new Exception('订单不存在');
            }
            // 判断订单金额是否匹配，如果金额不匹配，则需要人工介入
            if ($amount != $order->amount_pay) {
                throw new Exception('订单金额不匹配');
            }
            // 判断订单状态是否允许修改, 只有待支付和支付失败状态的订单才能修改，失败可能需要重新处理
            if (!in_array($order->order_st, [OrderEnum::ORDER_STATUS_PENDING, OrderEnum::ORDER_STATUS_FAILED])) {
                throw new Exception('订单当前状态不允许修改');
            }
            if ($order->order_st == OrderEnum::ORDER_STATUS_REFUND) {
                throw new Exception('订单已退款，不允许再支付');
            }

            if ($status !== OrderEnum::PAYMENT_STATUS_PAID) {
                $order->order_st   = OrderEnum::ORDER_STATUS_FAILED;   // 失败
                $order->payment_st = OrderEnum::PAYMENT_STATUS_FAILED; // 失败
                if (!$order->save()) {
                    throw new Exception('订单状态更新失败');
                }
                throw new Exception('订单支付失败，订单状态为已失败', 1);
            }
            $order->order_st   = OrderEnum::ORDER_STATUS_PAID; // 已支付
            $order->payment_st = OrderEnum::PAYMENT_STATUS_PAID;
            $order->payment_at = StringHelper::intToDate(time());
            $order->payment_no = $payment_no;
            if (!$order->save()) {
                throw new Exception('订单状态更新失败');
            }

            // 根据订单类型处理不同的业务逻辑
            if ($order->order_type == OrderEnum::ORDER_TYPE_BUY) {
                // 购买订单：创建实例基础信息（同步）
                $cloudServerResult = Yii::$app->vymCloudService->server->serverCreate($order);
                if (ArrayHelper::getValue($cloudServerResult, 'status') != 1) {
                    throw new Exception(ArrayHelper::getValue($cloudServerResult, 'msg'));
                }

                // 更新产品销量和库存
                $product = CloudProduct::findOne($order->product_id);
                if ($product) {
                    // 增加销量
                    $product->sales = $product->sales + 1;

                    // 扣减库存（如果不是无限库存）
                    if ($product->stock > 0) {
                        $product->stock = $product->stock - 1;
                        // 如果库存减到0，设置为-1表示库存耗尽（避免与无限库存混淆）
                        if ($product->stock == 0) {
                            $product->stock = -1;
                        }
                    }

                    if (!$product->save()) {
                        throw new Exception('更新产品销量和库存失败: ' . implode(', ', $product->getFirstErrors()));
                    }
                }
            } elseif ($order->order_type == OrderEnum::ORDER_TYPE_RENEW) {
                // 续费订单：延长服务器到期时间
                $renewResult = $this->processRenewOrder($order);
                if (!$renewResult['success']) {
                    throw new Exception($renewResult['message']);
                }
            } elseif ($order->order_type == OrderEnum::ORDER_TYPE_UPGRADE) {
                // 升级订单：处理升级逻辑
                // TODO: 实现升级订单处理逻辑
                throw new Exception('升级订单处理功能待实现');
            }

            // 订单处理完成，立即返回成功
            $transaction->commit();
            return ['status' => 1, 'msg' => '订单支付成功，实例创建中', 'data' => $order];
        } catch (Exception $e) {
            isset($transaction) && $transaction->rollBack();
            return ['status' => $e->getCode(), 'msg' => $e->getMessage()];
        }
    }

    /**
     * 删除订单
     *
     * @param int $id
     *
     * @return array
     */
    public function deleteOrder($id)
    {
        try {
            $order = $this->findById($id);
            // 判断订单是否存在
            if (!$order) {
                throw new Exception('订单不存在');
            }
            // 判断订单状态是否允许删除，只有待支付和支付失败状态的订单才能删除
            if ($order->order_st != OrderEnum::ORDER_STATUS_PENDING && $order->order_st != OrderEnum::ORDER_STATUS_FAILED) {
                throw new Exception('订单当前状态不允许删除');
            }
            // 判断订单是否已支付
            if ($order->order_st == OrderEnum::ORDER_STATUS_PAID) {
                throw new Exception('订单已支付，不允许删除');
            }
            // 判断订单是否已创建实例
            if ($order->instance_id) {
                throw new Exception('订单已创建实例，不允许删除');
            }
            // 判断订单是否已退款
            if ($order->order_st == OrderEnum::ORDER_STATUS_REFUND) {
                throw new Exception('订单已退款，不允许删除');
            }
            // 删除订单,软删除
            $order->deleted_at = StringHelper::intToDate(time());
            if (!$order->save()) {
                throw new Exception('订单删除失败');
            }
            return ['code' => 1, 'msg' => '订单删除成功', 'data' => $order];
        } catch (Exception $e) {
            return ['code' => $e->getCode(), 'msg' => $e->getMessage()];
        }
    }

    /**
     * 取消订单
     *
     * @param int $id  订单ID
     * @param int $uid 用户ID
     *
     * @return array
     */
    public function cancelOrder($id, $uid)
    {
        try {
            $order = $this->findByWhere(['id' => $id, 'uid' => $uid]);
            // 判断订单是否存在
            if (!$order) {
                throw new Exception('订单不存在');
            }
            // 判断订单状态是否允许取消，只有待支付状态的订单才能取消
            if ($order->order_st != OrderEnum::ORDER_STATUS_PENDING) {
                throw new Exception('只能取消待支付的订单');
            }
            // 更新订单状态为已取消
            $order->order_st = OrderEnum::ORDER_STATUS_CANCELLED;
            if (!$order->save()) {
                throw new Exception('订单取消失败');
            }
            return ['status' => true, 'msg' => '订单取消成功', 'data' => $order];
        } catch (Exception $e) {
            return ['status' => false, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 成功的订单，退款订单，用户余额
     *
     * @param int $id
     *
     * @return array
     */
    public function refundOrder($id)
    {
        try {
            $order = $this->findById($id);
            // 判断订单是否存在
            if (!$order) {
                throw new Exception('订单不存在');
            }
            // 判断订单状态是否允许退款，只有已支付和已完成状态的订单才能退款
            if (!in_array($order->order_st, [OrderEnum::ORDER_STATUS_PAID, OrderEnum::ORDER_STATUS_COMPLETED])) {
                throw new Exception('订单当前状态不允许退款');
            }

            // 判断订单是否已退款
            if ($order->order_st == OrderEnum::ORDER_STATUS_REFUND) {
                throw new Exception('订单已退款，不允许再退款');
            }

            // 检查是否已有退款记录
            $existingRefund = \addons\VymCloud\common\models\CloudRefund::findOne(['order_id' => $order->id]);
            if ($existingRefund) {
                throw new Exception('该订单已有退款申请');
            }

            // 使用数据库事务确保数据一致性
            $transaction = Yii::$app->db->beginTransaction();

            try {
                // 创建退款记录
                $refund                = new \addons\VymCloud\common\models\CloudRefund();
                $refund->order_id      = $order->id;
                $refund->uid           = $order->uid;
                $refund->refund_amount = $order->amount_pay;
                $refund->refund_reason = '用户申请退款';
                $refund->refund_status = \addons\VymCloud\common\models\CloudRefund::STATUS_PENDING;

                if (!$refund->save()) {
                    throw new Exception('创建退款记录失败: ' . implode(', ', $refund->getFirstErrors()));
                }

                // 更新订单状态为退款中（等待处理）
                $order->order_st = OrderEnum::ORDER_STATUS_REFUND;
                if (!$order->save()) {
                    throw new Exception('订单状态更新失败');
                }

                // 退款时恢复产品销量和库存（如果是购买订单）
                if ($order->order_type == OrderEnum::ORDER_TYPE_BUY && $order->product_id) {
                    $product = CloudProduct::findOne($order->product_id);
                    if ($product) {
                        // 减少销量
                        if ($product->sales > 0) {
                            $product->sales = $product->sales - 1;
                        }

                        // 恢复库存（如果不是无限库存）
                        if ($product->stock != 0) {  // stock=0表示无限库存，不需要恢复
                            $product->stock = $product->stock + 1;
                        }

                        if (!$product->save()) {
                            throw new Exception('恢复产品库存失败: ' . implode(', ', $product->getFirstErrors()));
                        }
                    }
                }

                // 如果是余额支付，直接退回余额
                if ($order->payment_id == 'balance' || strpos($order->payment_no, 'BAL') === 0) {
                    $user = \common\models\member\Member::findOne($order->uid);
                    if ($user) {
                        $user->user_money += $order->amount_pay;
                        if (!$user->save()) {
                            throw new Exception('用户余额更新失败');
                        }

                        // 更新退款状态为已处理
                        $refund->refund_status = \addons\VymCloud\common\models\CloudRefund::STATUS_APPROVED;
                        $refund->processed_at  = date('Y-m-d H:i:s');
                        $refund->remark        = '余额支付自动退款';
                        $refund->save();
                    }
                }

                $transaction->commit();
                return ['code' => 1, 'msg' => '退款申请已提交，请等待处理', 'data' => $order];

            } catch (Exception $e) {
                $transaction->rollBack();
                throw $e;
            }

        } catch (Exception $e) {
            return ['code' => $e->getCode(), 'msg' => $e->getMessage()];
        }
    }

    /**
     * 根据ID获取订单
     *
     * @param $id
     * @param $is_array
     *
     * @return array|ActiveRecord|null
     */
    public function findById($id, $is_array = false)
    {
        $query = CloudOrder::find()->where(['id' => $id]);
        if ($is_array) {
            return $query->asArray()->one();
        }
        return $query->one();
    }

    /**
     * 根据订单号获取订单
     *
     * @param $order_no
     * @param $is_array
     *
     * @return ActiveRecord|array|null
     */
    public function findByOrderNo($order_no, $is_array = false)
    {
        $query = CloudOrder::find()->where(['order_no' => $order_no]);
        if ($is_array) {
            return $query->asArray()->one();
        }
        return $query->one();
    }

    /**
     * 根据条件获取订单
     *
     * @param $where
     * @param $is_array
     *
     * @return array|ActiveRecord|null
     */
    public function findByWhere($where, $is_array = false)
    {
        $query = CloudOrder::find()->where($where);
        if ($is_array) {
            return $query->asArray()->one();
        }
        return $query->one();
    }

    /**
     * 获取所有订单
     *
     * @param array  $where
     * @param string $select
     * @param string $orderBy
     * @param int    $limit
     * @param int    $offset
     *
     * @return array|ActiveRecord[]
     */
    public function findAll($where = [], $select = '*', $orderBy = 'id desc', $limit = 10, $offset = 0)
    {
        return CloudOrder::find()
                         ->select($select)
                         ->where($where)
                         ->orderBy($orderBy)
                         ->limit($limit)
                         ->offset($offset)
                         ->asArray()
                         ->all();
    }

    /**
     * 处理续费订单
     *
     * @param CloudOrder $order 续费订单
     *
     * @return array
     */
    private function processRenewOrder($order)
    {
        try {
            // 获取服务器信息
            $server = CloudServer::findOne($order->server_id);
            if (!$server) {
                return ['success' => false, 'message' => '关联的服务器不存在'];
            }

            // 获取续费详情
            $serverDetail = $order->server_detail;
            $renewMonths  = ArrayHelper::getValue($serverDetail, 'renew_months', 1);
            $newEndTime   = ArrayHelper::getValue($serverDetail, 'new_end_time');

            // 如果没有预计算的新到期时间，则重新计算
            if (!$newEndTime) {
                $currentEndTime = $server->end_at;
                $newEndTime     = date('Y-m-d H:i:s', strtotime($currentEndTime . " +{$renewMonths} months"));
            }

            // 更新服务器到期时间
            $server->end_at = $newEndTime;
            if (!$server->save()) {
                return ['success' => false, 'message' => '更新服务器到期时间失败: ' . implode(', ', $server->getFirstErrors())];
            }

            // 记录续费日志
            Yii::info("服务器续费成功 - 服务器ID: {$server->id}, 续费月数: {$renewMonths}, 新到期时间: {$newEndTime}", 'vymcloud.renew');

            return ['success' => true, 'message' => '续费处理成功'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    public function getStatistics($type)
    {
        $fields = [
            'count' => '下单笔数',
            'totalAmount' => '下单金额(万)',
            'payCount' => '支付笔数',
            'payTotalAmount' => '支付金额(万)',
        ];
        list($time, $format) = EchantsHelper::getFormatTime($type);
        return EchantsHelper::lineOrBarInTime(function ($start_time, $end_time, $formatting) {
            $query = CloudOrder::find()->select(['count(id) as count', 'round(sum(amount_pay)/10000,4)  as totalAmount', "DATE_FORMAT(created_at, '$formatting') as time"])
                    ->andWhere(['between', 'created_at', date('Y-m-d H:i:s', $start_time), date('Y-m-d H:i:s', $end_time)])
                    ->andWhere(['merchant_id' => Yii::$app->services->merchant->getNotNullId()])
                    ->groupBy(['time']);
            $allOrders = $query->asArray()->all();

            $paidOrders = $query
                ->andWhere(['in','order_st' ,[ OrderEnum::ORDER_STATUS_PAID,OrderEnum::ORDER_STATUS_COMPLETED]])->asArray()->all();

            // 将支付完成的订单数据合并到所有订单数据中
            $paidOrdersMap = [];
            foreach ($paidOrders as $paidOrder) {
                $paidOrdersMap[$paidOrder['time']] = [
                    'payCount' => $paidOrder['count'],
                    'payTotalAmount' => $paidOrder['totalAmount'],
                ];
            }

            foreach ($allOrders as &$order) {
                if (isset($paidOrdersMap[$order['time']])) {
                    $order['payCount'] = $paidOrdersMap[$order['time']]['payCount'];
                    $order['payTotalAmount'] = $paidOrdersMap[$order['time']]['payTotalAmount'];
                } else {
                    $order['payCount'] = 0;
                    $order['payTotalAmount'] = 0;
                }
            }

            return $allOrders;


        }, $fields, $time, $format);
    }
}
