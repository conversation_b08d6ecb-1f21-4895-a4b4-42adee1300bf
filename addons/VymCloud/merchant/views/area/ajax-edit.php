<?php

use yii\widgets\ActiveForm;
use common\helpers\Url;
use common\enums\StatusEnum;
use common\widgets\ueditor\UEditor;
use common\widgets\map\Map;
use yii\helpers\Html;

/** @var yii\web\View $this */
/** @var addons\VymCloud\common\models\CloudArea $model */
/** @var yii\widgets\ActiveForm $form */

$form = ActiveForm::begin([
    'id' => $model->formName(),
    'enableAjaxValidation' => true,
    'validationUrl' => Url::to(['ajax-edit', 'id' => $model['id']]),
    'fieldConfig' => [
        'template' => "<div class='row'><div class='col-sm-3 text-right'>{label}</div><div class='col-sm-8'>{input}\n{hint}\n{error}</div></div>",
    ]
]);
?>

<div class="modal-header">
    <h4 class="modal-title">基本信息</h4>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
</div>
<div class="modal-body p-3">
    <div class="card card-primary card-outline card-outline-tabs">
        <div class="card-header p-0 border-bottom-0">
            <ul class="nav nav-tabs" id="from-tab" role="tablist">
                <li class="nav-item">
                    <a class="nav-link no-loading active" id="basic-tab" data-toggle="pill" href="#basic-setting" role="tab" aria-controls="basic-setting" aria-selected="true">基础设置</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link no-loading" id="more-tab" data-toggle="pill" href="#more-setting" role="tab" aria-controls="more-setting" aria-selected="false">更多设置</a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="from-tabContent">
                <div class="tab-pane fade active show" id="basic-setting" role="tabpanel" aria-labelledby="basic-tab">
                    <?= $form->field($model, 'pid')->dropDownList($dropDownList) ?>
                    <?= $form->field($model, 'name')->textInput(['maxlength' => true]) ?>
                    <?= $form->field($model, 'name_hk')->textInput(['maxlength' => true]) ?>
                    <?= $form->field($model, 'name_en')->textInput(['maxlength' => true]) ?>
                    <?= $form->field($model, 'icon')->textInput(['maxlength' => true]) ?>
                    <?= $form->field($model, 'sort')->textInput() ?>
                    <?= $form->field($model, 'area_st')->radioList(\common\enums\StatusEnum::getMap()) ?>
                </div>
                <div class="tab-pane fade" id="more-setting" role="tabpanel" aria-labelledby="more-tab">
                    <?= $form->field($model, 'content')->widget(UEditor::class, [
                        'formData' => [
                            'drive' => 'local', // 默认本地 支持qiniu/oss/cos 上传
                            'poster' => false, // 上传视频时返回视频封面图，开启此选项需要安装 ffmpeg 命令
                            'thumb' => [['width' => 100, 'height' => 100,],],
                            'height' => 40,
                        ],
                    ]) ?>
                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
    <button class="btn btn-primary" type="submit">保存</button>
</div>
<?php ActiveForm::end(); ?>