<?php
namespace addons\VymDesen\backend\controllers;

use addons\VymDesen\backend\models\IdlePdt;
use addons\VymDesen\backend\models\ServiceOrder;
use addons\VymDesen\backend\controllers\BaseController;
use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\components\FinanceReport;
use addons\VymDesen\common\components\NotifyHandle;
use addons\VymDesen\common\models\Member\MemberPdt;
use addons\VymDesen\common\models\Pdt\PdtIp;
use addons\VymDesen\common\models\Pdt\PdtManageType;
use addons\VymDesen\common\models\Trade\Trade;
use addons\VymDesen\common\models\UserMember\UserMember;
use Yii;

/**
 * 控制器
 * <AUTHOR>
 *
 */
class TradeController extends BaseController {
	/**
	 * 订单列表页视图
	 * @return Ambigous <string, string>
	 */
    public function actionIndex() {
    	$TradeModel = new Trade();
    	
    	$TradeQueryModel = $TradeModel->find()->select($TradeModel->getListFields())->With('usermember'); 	
    	#创建搜索条件
    	$TradeModel->createSearchWhere($TradeQueryModel, $this->get());

    	$iCount = $TradeQueryModel->count();    	 
    	$oPage = DataHelper::getPage($iCount); 
    	
    	$arrTradeRes = $TradeQueryModel->offset($oPage->offset)->limit($oPage->limit)->orderBy('trade_id desc')->asArray()->all();  ##die(print_r($arrTradeRes));
    	return $this->render('index', ['arrRes'=>$arrTradeRes,'iCount'=>$iCount,'page'=>$oPage]);
    }
	
    /**
     * 订单详情页视图
     * @return Ambigous <string, string>
     */
    public function actionItem() {
    	$trade_orderid = $this->get('orderid');
    	 
    	#获取查询结果 这里字段基本都能用到 故此不加以限制
    	$arrTradeQueryRes = Trade::find()->where('trade_orderid=:trade_orderid', [':trade_orderid'=>$trade_orderid])->limit(1)->asArray()->one();//die(print_r($arrTradeQueryRes));
    	
    	#未找到对应订单编号不进行item表查询处理
    	if ( empty($arrTradeQueryRes) ) {
    		return $this->redirect(['/trade']);
    	}    
    	$trade_config = json_decode($arrTradeQueryRes['trade_config'],true);//print_r($extend);die();
		
    	$config = $trade_config['config'];

		$PdtManageTypeModel = new PdtManageType();
        if( isset($trade_config['server_typeid']) ) {
			$PdtManageTypeRes = $PdtManageTypeModel->find()->where(['type_id'=>$trade_config['server_typeid']])->asArray()->one();
		}else{
			$PdtManageTypeRes = [];
		}

    	return $this->render('item', ['arrTradeRes'=>$arrTradeQueryRes, 'config'=>$config,'trade_config'=>$trade_config,'PdtManageTypeRes'=>$PdtManageTypeRes]);
    }    
	
	/** 修改订单从未付款状态到后付款状态(手工单，线下支付单) **/	
	public function actionTradePay() {
		Yii::$app->request->isAjax || die('error'); 
		
		$MemberPdtModel = new MemberPdt();
		$trade_orderid = $this->post('orderid');
		
		#获取订单查询model
        $TradeQueryModel = Trade::find()->where('trade_orderid=:trade_orderid and trade_status="未支付"', [':trade_orderid'=>$trade_orderid])->limit(1)->one();
		
		#如果未找到对应订单编号 则终止执行
        if ( empty($TradeQueryModel) ){
            $arrReturn = [
                'status'=>0,
                'info'=>'未有对应的订单信息'
            ];
            return $this->renderJSON($arrReturn);
        }
		//生成服务ID
		$serviceid = DataHelper::createServiceid();
		$time = time();
		#设定付款数据
        $TradeQueryModel->trade_price_payment = $TradeQueryModel->trade_price_real;
        $TradeQueryModel->trade_time_pay = $time;
        $TradeQueryModel->trade_status = '后付款';
		$TradeQueryModel->serviceid = $serviceid;
		$TradeQueryModel->trade_type_pay = "balance";
		
		#获取用户个人数据，判断金额是否足够
		$UserQueryModel = UserMember::find()->where('u_id=:userid', [':userid' => $TradeQueryModel->member_id])->limit(1)->one();
		if(!$UserQueryModel) {
			$arrReturn = [
                'status'=>0,
                'info'=>'用户不存在'
            ];
            return $this->renderJSON($arrReturn);
		}
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		#扣款后用户余额
		$payfrontmoney = $UserQueryModel->balance;
		$afterPayMoney = $UserQueryModel->balance - $TradeQueryModel->trade_price_real;
		$UserQueryModel->balance = $afterPayMoney;
		#修改用户余额(后付款订单不用修改用户余额)
		#$saveUserInfo = $UserQueryModel->save();
		#修改订单状态
		$saveOrderInfo = $TradeQueryModel->save();
		
		///后续操作
		$service = new \common\components\PayCallbackService();
		$behavior = $TradeQueryModel->trade_type_do;
		$status = "等待确认";
		switch($behavior){
			case "新购":
				$behaviorname = "CreateServer";
				
				#记录财务报表
				$ReportResult = FinanceReport::Report_MachinePurchase($trade_orderid, $serviceid, $payfrontmoney, $afterPayMoney, '是');
				
				break;
			case "续费":
				$behaviorname = "ServerRenew";
				$status = "完成工单";
				break;
			case "变更配置":
				$behaviorname = "ServerUpgrade";
				break;
			case "更换机器":
				$behaviorname = "ReplaceMachine";
				break;
			default:
				$ReportResult = false;
		}
		
		if(!$ReportResult) {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'支付异常，新增记录异常'
			];
			return $this->renderJSON($arrReturn);
		}
		
		$result = $service->$behaviorname($TradeQueryModel->trade_orderid);	
			
		$trade_config = json_decode($TradeQueryModel->trade_config,true);//print_r($trade_config);die;
		$trade_config['ip'] = $trade_config['ip'];
		$trade_config['ip2'] = $trade_config['ip2'];	
		
		if( $TradeQueryModel->trade_type_do == "新购" ) {
			$arrconfig = [
				'cpu'=> "",
				'ram'=> "",
				'hdd'=> "",
				'configbandwidth'=> "",
				'ipnumber'=> "",
				'defense'=> "",
				'operatsystem'=> ""
			];
			$before_config['config'] = $arrconfig;
			$before_config['ip'] = [];
			$before_config['ip2'] = [];
		}else{
			$MmeMemberPdtQuery = $MemberPdtModel->findone(['unionid'=>$TradeQueryModel->unionid])->toArray();
			$before_config['config'] = json_decode($MmeMemberPdtQuery['config'],true);
			$before_config['ip'] = json_decode($MmeMemberPdtQuery['ip'],true);
			$before_config['ip2'] = json_decode($MmeMemberPdtQuery['ip2'],true);
		}
		#加入服务管理
		$ServiceOrderModel  = new ServiceOrder();		
		$ServiceOrderModel->serviceid = $serviceid;
		$ServiceOrderModel->unionid = $TradeQueryModel->unionid;
		$ServiceOrderModel->member_id = $TradeQueryModel->member_id;
		$ServiceOrderModel->member_name = $TradeQueryModel->member_name;
		$ServiceOrderModel->admin_id = $TradeQueryModel->admin_id;
		$ServiceOrderModel->admin_name = $TradeQueryModel->admin_name;
		$ServiceOrderModel->service_type_do = $TradeQueryModel->trade_type_do;
		$ServiceOrderModel->service_front_config = json_encode($before_config, JSON_UNESCAPED_UNICODE);
		$ServiceOrderModel->service_after_config = json_encode($trade_config, JSON_UNESCAPED_UNICODE);
		$ServiceOrderModel->service_time_start = $time;
		if($behavior == '续费') {
			$ServiceOrderModel->service_time_confirm_time = $time;
			$ServiceOrderModel->service_time_end = $time;
			$service_time_confirm_time = $time;
			$service_time_end = $time;
		} else {
			$ServiceOrderModel->service_time_confirm_time = null;
			$ServiceOrderModel->service_time_end = null;
			$service_time_confirm_time = null;
			$service_time_end = null;
		}
		$ServiceOrderModel->service_status = $status;
		$ServiceOrderModel->service_remark = "";
		#新增服务管理
		$ServiceOrderResult = $ServiceOrderModel->insert();
		
		#查询由后台发起的后付款订单是否为审核
		$finishPayOrder = Yii::$app->db->createCommand("select nv_type from notice_manage where nv_key = 'finish_pay_order'")->queryScalar();
		$notice_do_man = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		
		if($finishPayOrder == '审核') {
			$transaction->rollBack();
			#修改订单状态
			$dataContent[] = [
				'sql' => base64_encode("update trade set trade_price_payment='".$TradeQueryModel->trade_price_real."', trade_status='后付款', serviceid='".$serviceid."', trade_type_pay='balance' where trade_orderid = '".$TradeQueryModel->trade_orderid."'"),
			];
			
			#新增服务管理
			if($behavior == '续费') {
				$dataContent[] = [
					'sql' => base64_encode("insert into service_order (serviceid,unionid,member_id,member_name,admin_id,admin_name,service_type_do,service_front_config,service_after_config,service_time_start,service_time_confirm_time,service_time_end,service_status,service_audit) 
					values('".$serviceid."', '".$TradeQueryModel->unionid."', '".$TradeQueryModel->member_id."', '".$TradeQueryModel->member_name."', '".$TradeQueryModel->admin_id."', '".$TradeQueryModel->admin_name."', '".$TradeQueryModel->trade_type_do."', '".json_encode($before_config, JSON_UNESCAPED_UNICODE)."', '".json_encode($trade_config, JSON_UNESCAPED_UNICODE)."', '".$time."', '".$time."', '".$time."', '".$status."', 'Y')"),
					
				];
			} else {
				$dataContent[] = [
					'sql' => base64_encode("insert into service_order (serviceid,unionid,member_id,member_name,admin_id,admin_name,service_type_do,service_front_config,service_after_config,service_time_start,service_status,service_audit) 
					values('".$serviceid."', '".$TradeQueryModel->unionid."', '".$TradeQueryModel->member_id."', '".$TradeQueryModel->member_name."', '".$TradeQueryModel->admin_id."', '".$TradeQueryModel->admin_name."', '".$TradeQueryModel->trade_type_do."', '".json_encode($before_config, JSON_UNESCAPED_UNICODE)."', '".json_encode($trade_config, JSON_UNESCAPED_UNICODE)."', '".$time."', '".$status."', 'Y')"),
					
				];
			}
			
			
			$dataJson = json_encode($dataContent, JSON_UNESCAPED_UNICODE);
			
			NotifyHandle::FinishPayOrder_Notify('未支付', '后付款', $TradeQueryModel->member_id, $notice_do_man, $TradeQueryModel->unionid, $TradeQueryModel->trade_orderid, $dataJson);
			
			Yii::$app->db->createCommand("update trade set trade_audit = 'W' where trade_orderid = '".$TradeQueryModel->trade_orderid."'")->execute();
			
			$arrReturn = [
				'status'=>1,
				'info'=>'已提交已付款订单申请，等待审核'
			];
			
		} else {
			
			NotifyHandle::FinishPayOrder_Notify('未支付', '后付款', $TradeQueryModel->member_id, $notice_do_man, $TradeQueryModel->unionid, $TradeQueryModel->trade_orderid, null);
			
			if($saveUserInfo && $saveOrderInfo && $result && $ServiceOrderResult) {
				
				$transaction->commit();
				
				$arrReturn = [
					'status'=>1,
					'info'=>'付款完成'
				];
				
			} else {
				$transaction->rollBack();
				$arrReturn = [
					'status'=>0,
					'info'=>'系统异常'
				];
			}
		}
		return $this->renderJSON($arrReturn);
	}
    /**
     * 取消订单操作     
     */
    public function actionTradeCancel() {
        Yii::$app->request->isAjax || die('error');
    
        $arrReturn = [];
        $trade_orderid = $this->post('orderid');
        
        #获取订单查询model
        $TradeQueryModel = Trade::find()->where('trade_orderid=:trade_orderid and trade_status = "未支付"', [':trade_orderid'=>$trade_orderid])->limit(1)->one();//die(print_r($TradeQueryModel));
        
        #如果当前用户操作其他用户订单或是未找到对于订单编号 则终止执行
        if ( empty($TradeQueryModel) ) {
            $arrReturn = [
                'status' => 0,
                'info' => '未有对应的订单信息'
            ];
            return $this->renderJSON($arrReturn);
        }
		
        if( $TradeQueryModel->trade_status != "未支付") {
			$arrReturn = [
                'status' => 0,
                'info' => '错误的订单状态'
            ];
            return $this->renderJSON($arrReturn);
		}
		
		#实例
        $MemberPdtModel = new MemberPdt();
        $idleModel = new IdlePdt();
        $UserMemberModel = new UserMember();
        
        #开启事务
        $transaction = Yii::$app->db->beginTransaction();
        
		//取消订单时
		#查询由后台发起的取消订单是否为审核
		$cancelOrder = Yii::$app->db->createCommand("select nv_type from notice_manage where nv_key = 'cancel_order'")->queryScalar();
		$notice_do_man = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		$time = time();
        if( $TradeQueryModel->trade_type_do == "续费") {    #用户产品续费退单
		
        } elseif ( $TradeQueryModel->trade_type_do == "新购" ) {
            
        } elseif( $TradeQueryModel->trade_type_do == "变更配置" ){
            
        } elseif ( $TradeQueryModel->trade_type_do == "更换机器" ) {            
			
			//当未付款时，取消变更机器订单，只需要将产品库的状态修改  和选择的IP  状态修改
			#获取产品更换前数据
			$MemberPdtRes = $MemberPdtModel->findOne(['unionid'=>$TradeQueryModel->unionid]);
			if( empty($MemberPdtRes)) {
				$transaction->rollBack();
				$arrReturn = [
					'status'=>0,
					'info'=>'未知的用户产品'
				];
				return $this->renderJSON($arrReturn);
			}
			
			$trade_config = json_decode($TradeQueryModel->trade_config,true);
			// 因为是更换机器，所以存在产品中闲置产品的状态修改
			$IdlePdtModel = new IdlePdt(); 
			// 如果更换前是自有
			if( $MemberPdtRes->servicerprovider == 0 ) {
				$old_idle_id = $trade_config['old_idle_id'];
				if($cancelOrder == '审核') {
					$dataContent[] = [
						'sql' => base64_encode("update idle_pdt set status=1,attribute_id=2,update_time='".$time."' where id = '".$old_idle_id."'"),
					];
				} else {
					$oldidleRes = $idleModel->domodifystatus($old_idle_id, 2 , 1);
				}
			}

			// 如果更换后的是自有
			if( $trade_config['servicerprovider'] == 0) {
				$idle_id = $trade_config['idle_id'];
				//修改状态
				if($cancelOrder == '审核') {
					$dataContent[] = [
						'sql' => base64_encode("update idle_pdt set status=0,attribute_id=1,update_time='".$time."' where id = '".$idle_id."'"),
					];
				} else {
					$idleModel->domodifystatus($idle_id, 1 , 0);
				}
				
				//同时涉及IP改变也需从待定改回原来的状态
				$newIdleRes = $idleModel->find()->where(['id'=>$idle_id])->asArray()->one();
				
				$oldIP = json_decode($newIdleRes['ip2'],true);
				$ip2Array = $trade_config['ip2'];
				$PdtIpModel = new PdtIp();
				
				$bgIPArray = array_unique(array_merge($ip2Array,$oldIP));
				$result_bangding = array_diff($bgIPArray,$oldIP);   //原本需要改为使用中的
				$result_jiechu = array_diff($bgIPArray,$ip2Array);  //原本改为闲置的				
				//0-闲置 1-使用中 2-预留 3-待定 4-不可用
                if( !empty($result_bangding) ) {					
					if($cancelOrder == '审核') {
						$iplist = "";
						if(is_array($result_bangding)) {
							foreach($result_bangding as $vv) {
								$iplist .= '\''.$vv.'\',';
							}
							$iplist = substr($iplist, 0, -1);
							$dataContent[] = [
								'sql' => base64_encode("update pdt_ip set status = '0' where ip in(".$iplist.")")
							];							
						} else {
							$dataContent[] = [
								'sql' => base64_encode("update pdt_ip set status = '0' where ip='".$result_bangding."'")
							];
						}
					} else {
						$res1 = $PdtIpModel->modifyIPStatus($result_bangding,"0");
					}									
                }
				#
				if( !empty($result_jiechu) ) {					
					if($cancelOrder == '审核') {
						$iplist = "";
						if(is_array($result_jiechu)) {
							foreach($result_jiechu as $vv) {
								$iplist .= '\''.$vv.'\',';
							}
							$iplist = substr($iplist, 0, -1);
							$dataContent[] = [
								'sql' => base64_encode("update pdt_ip set status = '1' where ip in(".$iplist.")")
							];							
						} else {
							$dataContent[] = [
								'sql' => base64_encode("update pdt_ip set status = '1' where ip='".$result_jiechu."'")
							];
						}
					} else {
						$res2 = $PdtIpModel->modifyIPStatus($result_jiechu,"1");
					}									
                }				
			}
        } else {
            $arrReturn = [
                'status'=>0,
                'info'=>"未知的行为"
            ];
            return $this->renderJSON($arrReturn);
        }
        
        #订单状态值更改为-1(取消订单操作)
		$oldstatus = $TradeQueryModel->trade_status;
        $TradeQueryModel->trade_status = '已取消';
		
		if($cancelOrder == '审核') {
			$dataContent[] = [
				'sql' => base64_encode("update trade set trade_status='已取消' where trade_orderid = '".$TradeQueryModel->trade_orderid."'"),
			];
			
			$dataJson = json_encode($dataContent);
			
			NotifyHandle::CancelOrder_Notify($oldstatus, '已取消', $TradeQueryModel->member_id, $notice_do_man, $TradeQueryModel->unionid, $TradeQueryModel->trade_orderid, $dataJson);
			
			Yii::$app->db->createCommand("update trade set trade_audit = 'W' where trade_orderid = '".$TradeQueryModel->trade_orderid."'")->execute();
			
			$transaction->commit();
			$arrReturn = [
				'status'=>1,
				'info'=>'已提交取消订单申请，等待审核'
			];
		} else {
			NotifyHandle::CancelOrder_Notify($oldstatus, '已取消', $TradeQueryModel->member_id, $notice_do_man, $TradeQueryModel->unionid, $TradeQueryModel->trade_orderid, null);
			
			if ( $TradeQueryModel->update() ){
				$arrReturn = [
					'status'=>1,
					'info'=>'该订单取消成功'
				];             
				#提交事务
				$transaction->commit();    
				return $this->renderJSON($arrReturn);
			}else{
				#回滚数据
				$transaction->rollBack();
				$arrReturn = [
					'status'=>0,
					'info'=>'操作失败，请稍后再次尝试'
				];            
			} 
		}
		return $this->renderJSON($arrReturn);
    }
	
    /**
     * 修改支付方式
     */
    public function actionModifyPaytype() {
        Yii::$app->request->isAjax || die('error');
    
        $TradeMode = new Trade();
        $trade_orderid = trim($this->post('trade_no'));
        #获取订单查询model
        $TradeQueryRes = Trade::find()->where('trade_orderid=:trade_orderid', [':trade_orderid'=>$trade_orderid])->limit(1)->one();//die(print_r($TradeQueryModel));

        #未找到对应订单编号不进行item表查询处理
        if ( empty($TradeQueryRes) ) {
            $arrReturn = [
                'status'=>0,
                'info'=>'未知的订单'
            ];
            return $this->renderJSON($arrReturn);
        }  
		
        $old_type = $TradeQueryRes->trade_type_pay;
        
        $TradeQueryRes->trade_type_pay = $this->post('pay_type');

        $TradeQueryRes->trade_remark .= date("Y-m-d H:i:s",time())." 管理员 ".\yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username')." 更改了支付类型: ".$old_type." => ".$this->post('pay_type')."<br/>";
        if( $TradeQueryRes->update(false)){
            $arrReturn = [
                'status'=>1,
                'info'=>'修改成功'
            ];
        } else {
            $arrReturn = [
                'status'=>0,
                'info'=>'修改失败'
            ];
        } 
        return $this->renderJSON($arrReturn);
    }
    /**
     * 修改用户应付价格
     */
    public function actionModifyRealprice() {
        Yii::$app->request->isAjax || die('error');
        
        $TradeModel = new Trade();
		
        $trade_orderid = trim($this->post('trade_no'));
        #获取订单查询model
        $TradeQueryRes = Trade::find()->where('trade_orderid=:trade_orderid', [':trade_orderid'=>$trade_orderid])->limit(1)->one();//die(print_r($TradeQueryModel));
    
        #未找到对应订单编号不进行item表查询处理
        if ( empty($TradeQueryRes) ) {
            return $this->redirect(['/trade']);
        }
        if ( $TradeQueryRes['trade_status'] != "未支付") {
            $arrReturn = [
                'status'=>0,
                'info'=>'该订单不处于未支付状态,不能修改'
            ];
            return $this->renderJSON($arrReturn);
        }
		
		
		
        $old_price = $TradeQueryRes->trade_price_real;
        
        #进入场景
    	$TradeQueryRes->scenario = 'pricemodfiy';
    	$TradeQueryRes->attributes = $this->post();//die(print_r($TradeQueryRes));

    	$TradeQueryRes->trade_remark .= date("Y-m-d H:i:s",time())." 管理员 ".\yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'username')." 更改了价格: ".$old_price." => ".$this->post('trade_price_real')." , 原因：".$this->post('remark')."<br/>"; 	
    	
    	#数据验证
    	if (!$TradeQueryRes->validate()) {
    		$arrReturn = [
        		'status'=>0,
        		'info'=>"价格输入有误"
    		];    		 
    		return $this->renderJSON($arrReturn);
    	}
    	if( empty($this->post('remark')) ) {
    	    $arrReturn = [
    	        'status'=>0,
    	        'info'=>"备注原因不能为空"
    	    ];
    	    return $this->renderJSON($arrReturn);
    	}
		
		#查询由后台发起的修改订单价格是否为审核
		$updatetype = Yii::$app->db->createCommand("select nv_type from notice_manage where nv_key = 'update_order_price'")->queryScalar();
		$notice_do_man = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		if($updatetype == '审核') {
			#$this->post()   trade_price_real, remark, trade_no
			$dataContent[] = [
				'sql' => base64_encode("update trade set trade_price_real='".$this->post('trade_price_real')."', trade_remark='".$TradeQueryRes->trade_remark."' where trade_orderid = '".$TradeQueryRes->trade_orderid."'"),
			];
			
			$dataJson = json_encode($dataContent);
			
			NotifyHandle::UpdateOrderPrice_Notify($old_price, sprintf("%.2f", $this->post('trade_price_real')), $TradeQueryRes->member_id, $notice_do_man, $TradeQueryRes->unionid, $TradeQueryRes->trade_orderid, $dataJson);
			$arrReturn = [
				'status'=>1,
				'info'=>'操作完成，等待审核结果'
			];
			Yii::$app->db->createCommand("update trade set trade_audit = 'W' where trade_orderid = '".$TradeQueryRes->trade_orderid."'")->execute();
		} else {
			NotifyHandle::UpdateOrderPrice_Notify($old_price, sprintf("%.2f", $this->post('trade_price_real')), $TradeQueryRes->member_id, $notice_do_man, $TradeQueryRes->unionid, $TradeQueryRes->trade_orderid, null);
			
			if( $TradeQueryRes->update(false) ) {
				$arrReturn = [
					'status'=>1,
					'info'=>'修改成功'
				];
			} else {
				$arrReturn = [
					'status'=>0,
					'info'=>'修改失败'
				];
			}
		}
        return $this->renderJSON($arrReturn);
    }
	
	/*
	* 更改用户下单配置
	* 此操作只允许修改新购订单  并且
	*/
	public function actionModifyConfig()
	{
		Yii::$app->request->isAjax || die('error');
		$TradeModel = new Trade();
		
		$post = $this->post();
		$orderid = $post['orderid'];
		#获取订单信息
		$TradeRes = $TradeModel->find()->where(['trade_orderid'=>$post['orderid']])->asArray()->one();
		if( empty($TradeRes))
		{
			$arrReturn = [
				'status' => 0,
				'info' => "未找到对应的订单信息",
			];
			return $this->renderJSON($arrReturn);
		}
		if( $TradeRes['trade_type_do'] != "新购")
		{
			$arrReturn = [
				'status' => 0,
				'info' => "只允许新购订单更改订单配置",
			];
			return $this->renderJSON($arrReturn);
		}
		if( $TradeRes['trade_status'] != "未支付" )
		{
			$arrReturn = [
				'status' => 0,
				'info' => "只有未支付状态才能更改订单配置",
			];
			return $this->renderJSON($arrReturn);
		}
		
		$TradeConfig = json_decode($TradeRes['trade_config'],true);
		$config = $TradeConfig['config']; 
		if( $post['cpu'] == $config['cpu'] && $post['ram'] == $config['ram'] && $post['hdd'] == $config['hdd'] && $post['configbandwidth'] == $config['configbandwidth'] && $post['ipnumber'] == $config['ipnumber'] && $post['defense'] == $config['defense'] && $post['operatsystem'] == $config['operatsystem'] )
		{
			$arrReturn = [
				'status' => 0,
				'info' => "未更改配置",
			];
			return $this->renderJSON($arrReturn);
		}
		
		foreach( $post as $key => $value )
		{
			if($post[$key] != $config[$key]){
				$TradeConfig['config'][$key] = trim($value);
			}
		}
		
		$TradeQuery = $TradeModel->findOne($TradeRes['trade_id']);
		//$TradeQuery->trade_config = json_encode($TradeConfig);
		#查询由后台发起的修改订单价格是否为审核
		$updateconfig = Yii::$app->db->createCommand("select nv_type from notice_manage where nv_key = 'modify_order_config'")->queryScalar();
		$notice_do_man = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
		if($updateconfig == '审核') {
			$dataContent[] = [
				'sql' => base64_encode("update trade set trade_config='".json_encode($TradeConfig, JSON_UNESCAPED_UNICODE)."' where trade_orderid = '".$orderid."'"),
			];
			
			$dataJson = json_encode($dataContent, JSON_UNESCAPED_UNICODE);
			
			NotifyHandle::UpdateOrderConfig_Notify($TradeQuery->trade_config, json_encode($TradeConfig, JSON_UNESCAPED_UNICODE), $TradeQuery->member_id, $notice_do_man, $TradeQuery->unionid, $orderid, $dataJson);
			$arrReturn = [
				'status'=>1,
				'info'=>'操作完成，等待审核结果'
			];
			Yii::$app->db->createCommand("update trade set trade_audit = 'W' where trade_orderid = '".$orderid."'")->execute();
		} else {
			NotifyHandle::UpdateOrderConfig_Notify($TradeQuery->trade_config, json_encode($TradeConfig, JSON_UNESCAPED_UNICODE), $TradeQuery->member_id, $notice_do_man, $TradeQuery->unionid, $orderid, null);
			
			if( $TradeQuery->update() ){
				$arrReturn = [
					'status' => 1,
					'info' => "更改成功",
				];			
			} else {
				$arrReturn = [
					'status' => 0,
					'info' => "更改失败",
				];
			}
		}

		return $this->renderJSON($arrReturn);
		
	} 
	
	
	
}	
