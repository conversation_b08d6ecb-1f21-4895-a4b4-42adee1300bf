<?php

namespace addons\VymServer\merchant\controllers;

use addons\VymServer\common\models\Server;
use common\models\base\SearchModel;
use Yii;
use yii\data\ActiveDataProvider;
use addons\VymServer\merchant\controllers\BaseController;
use common\helpers\MerchantHelper;
use yii\db\Expression;
use yii\web\NotFoundHttpException;

/**
 * ServerController implements the CRUD actions for Server model.
 */
class ServerController extends BaseController
{
    /**
     * @var Server
     */
    public $modelClass = Server::class;

    /**
     * 首页
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionIndex()
    {
        $merId       = $this->getMerchantId();
        $beginTime   = Yii::$app->request->get('start_time', date('Y-m-d', strtotime("-10 day")));
        $endTime     = Yii::$app->request->get('end_time', date('Y-m-d', strtotime("+1 day")));
        $queryParams = $this->request->queryParams;
        $searchModel = new SearchModel([
            'model'                  => $this->modelClass,
            'scenario'               => 'default',
            'relations'              => ['idlePdt' => [], 'memberPdt' => ['ip'], 'member' => [], 'merchant' => []], // 关联IdlePdt, MemberPdt 表的所有字段
            'partialMatchAttributes' => ['memberPdt.ip', 'gateway', 'mask', 'desc'], // 模糊查询
            'defaultOrder'           => [
                'id' => SORT_DESC,
            ],
            'pageSize'               => $this->pageSize,
        ]);

        $dataProvider = $searchModel->search($queryParams);
        /** @var \yii\db\ActiveQuery $query */
        $query        = $dataProvider->query;
        $this->addQueryParam($dataProvider, 's');
        $query->andWhere(['IS NOT', 'mpdt_id', new Expression('NULL')]);

        // 自定义IP筛选逻辑，支持JSON格式的IP数据查询
        if (!empty($queryParams['SearchModel']['memberPdt.ip'])) {
            $ipSearch = trim($queryParams['SearchModel']['memberPdt.ip']);
            $query->andWhere([
                'or',
                ['like', 'member_pdt.ip', $ipSearch],
                ['like', 'member_pdt.ip', '"' . $ipSearch . '"'], // 匹配JSON格式中的准确IP
            ]);
        }
        // var_export($dataProvider->getModels());
        return $this->render($this->action->id, [
            'dataProvider' => $dataProvider,
            'searchModel'  => $searchModel,
            'queryParams'  => $queryParams,
            'beginTime'    => $beginTime, // 开始时间
            'endTime'      => $endTime, // 结束时间
        ]);
    }

    /**
     * Displays a single Server model.
     *
     * @param int $server_id Server ID
     *
     * @return string
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($server_id)
    {
        $model = $this->findModel($server_id);

        // 获取关联的 member_pdt 数据
        $memberPdtModel      = null;
        $config              = [];
        $userMemberModel     = null;
        $initialAccountModel = null;
        $appointOffModel     = null;
        $transferRecordModel = [];
        $pdtManageList       = [];

        if ($model->mpdt_id) {
            $memberPdtModel = \addons\VymDesen\common\models\Member\MemberPdt::find()
                                                                             ->with(['servertype', 'pdtroom', 'pdtcabinet', 'pdtmanage', 'pdttype', 'serverattribute', 'provider', 'trade', 'switch', 'usermember'])
                                                                             ->where(['id' => $model->mpdt_id])
                                                                             ->one();

            if ($memberPdtModel) {
                $config = json_decode($memberPdtModel->config, true) ?: [];

                $userMemberModel = \addons\VymDesen\common\models\UserMember\UserMember::findOne($memberPdtModel->user_id);

                $initialAccountModel = \addons\VymDesen\common\models\Member\InitialAccount::find()
                                                                                           ->where(['unionid' => $memberPdtModel->unionid])
                                                                                           ->one();

                $appointOffModel = \addons\VymDesen\common\models\Member\MemberPdtAppointOff::find()
                                                                                            ->where(['appoint_unionid' => $memberPdtModel->unionid])
                                                                                            ->one();

                $transferRecordModel = \addons\VymDesen\common\models\Member\BusinessDeleteRecord::find()
                                                                                                 ->where(['unionid' => $memberPdtModel->unionid])
                    // ->orderBy(['transfer_time' => SORT_DESC])
                                                                                                 ->all();

                $pdtManageList = \addons\VymDesen\common\models\Pdt\PdtManage::find()
                                                                             ->where(['pdt_type_id' => $memberPdtModel->servertype[0]->id ?? null])
                                                                             ->all();
            }
        }

        // 权限控制
        $adminId     = Yii::$app->user->identity->id ?? null;
        $configModel = \addons\VymDesen\common\models\AdminSystemConfig::find()
                                                                       ->where(['config_name' => 'business_default_myself'])
                                                                       ->one();
        $configUser  = explode(',', $configModel->config_value ?? '');
        $isAllow     = !in_array($adminId, $configUser);

        return $this->render('view', [
            'model'               => $model,
            'memberPdtModel'      => $memberPdtModel,
            'config'              => $config,
            'userMemberModel'     => $userMemberModel,
            'initialAccountModel' => $initialAccountModel,
            'appointOffModel'     => $appointOffModel,
            'transferRecordModel' => $transferRecordModel,
            'pdtManageList'       => $pdtManageList,
            'isAllow'             => $isAllow,
        ]);
    }

    /**
     * Updates an existing Server model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param int $server_id Server ID
     *
     * @return string|\yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($server_id)
    {
        $model = $this->findModel($server_id);

        if ($this->request->isPost && $model->load($this->request->post()) && $model->save()) {
            return $this->redirect(['view', 'server_id' => $model->server_id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }


    /**
     * Deletes an existing Server model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param int $server_id Server ID
     *
     * @return \yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($server_id)
    {
        $this->findModel($server_id)->delete();

        return $this->redirect(['index']);
    }
}
