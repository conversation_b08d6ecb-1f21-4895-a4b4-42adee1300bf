<?php

use common\helpers\Url;

$this->title = '首页';
$this->params['breadcrumbs'][] = ['label' => $this->title];
?>

<style>
    .info-box-number {
        font-size: 20px;
    }

    .info-box-content {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>

<div class="row">
    <div class="col-md-4 col-sm-6 col-xs-12">
        <div class="info-box">
            <div class="info-box-content p-md">
                <span class="info-box-number"><i class="icon ion-person-stalker blue"></i> <?= $memberCount ?></span>
                <span class="info-box-text">会员人数(个)</span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>

    <div class="col-md-4 col-sm-6 col-xs-12">
        <div class="info-box">
            <div class="info-box-content p-md">
                <span class="info-box-number"><i class="icon ion-card cyan"></i> <?= $memberAccount['user_money'] ?? 0 ?></span>
                <span class="info-box-text">会员剩余余额(元)</span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>
    <div class="col-md-4 col-sm-6 col-xs-12">
        <div class="info-box">
            <div class="info-box-content p-md">
                <span class="info-box-number"><i class="icon ion-ios-pulse orange"></i> <?= abs($memberAccount['consume_money'] ?? 0) ?? 0 ?></span>
                <span class="info-box-text">会员总消费(元)</span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>
    <div class="col-md-4 col-sm-6 col-xs-12">
        <div class="info-box">
            <div class="info-box-content p-md">
                <span class="info-box-number"><i class="fa fa-server red"></i> <?= $memberAccount['give_money'] ?? 0 ?></span>
                <span class="info-box-text">物理服务器(台)</span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>
    <div class="col-md-4 col-sm-6 col-xs-12">
        <div class="info-box">
            <div class="info-box-content p-md">
                <span class="info-box-number"><i class="fa fa-server magenta"></i> <?= $memberAccount['user_integral'] ?? 0 ?></span>
                <span class="info-box-text">7天到期物理服务器(台)</span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>
    <div class="col-md-4 col-sm-6 col-xs-12">
        <div class="info-box">
            <div class="info-box-content p-md">
                <span class="info-box-number"><i class="fa fa-server purple"></i> <?= $actionLogCount ?></span>
                <span class="info-box-text">3天到期物理服务器(台)</span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>
    <div class="col-md-4 col-sm-6 col-xs-12">
        <div class="info-box">
            <div class="info-box-content p-md">
                <span class="info-box-number"><i class="fa fa-cloud red"></i> <?= $memberAccount['give_money'] ?? 0 ?></span>
                <span class="info-box-text">云服务器(台)</span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>
    <div class="col-md-4 col-sm-6 col-xs-12">
        <div class="info-box">
            <div class="info-box-content p-md">
                <span class="info-box-number"><i class="fa fa-cloud magenta"></i> <?= $memberAccount['user_integral'] ?? 0 ?></span>
                <span class="info-box-text">7天到期云服务器(台)</span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>
    <div class="col-md-4 col-sm-6 col-xs-12">
        <div class="info-box">
            <div class="info-box-content p-md">
                <span class="info-box-number"><i class="fa fa-cloud purple"></i> <?= $actionLogCount ?></span>
                <span class="info-box-text">3天到期云服务器(台)</span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>
    <div class="col-md-4 col-sm-6 col-xs-12">
        <div class="info-box">
            <div class="info-box-content p-md">
                <span class="info-box-number"><i class="fa fa-envelope red"></i> <?= $memberAccount['give_money'] ?? 0 ?></span>
                <span class="info-box-text">未处理客户工单</span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>
    <div class="col-md-4 col-sm-6 col-xs-12">
        <div class="info-box">
            <div class="info-box-content p-md">
                <span class="info-box-number"><i class="fa fa-envelope magenta"></i> <?= $memberAccount['user_integral'] ?? 0 ?></span>
                <span class="info-box-text">未结束客户工单</span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>
    <div class="col-md-4 col-sm-6 col-xs-12">
        <div class="info-box">
            <div class="info-box-content p-md">
                <span class="info-box-number"><i class="fa fa-comments purple"></i> <?= $actionLogCount ?></span>
                <span class="info-box-text">用户反馈记录</span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>
    <div class="col-md-12 col-xs-12">
        <div class="box box-solid">
            <div class="box-header">
                <i class="fa fa-circle rf-circle" style="font-size: 8px"></i>
                <h3 class="box-title">物理服务器</h3>
            </div>
            <?= \common\widgets\echarts\Echarts::widget([
                'config' => [
                    'server' => Url::to(['server']),
                    'height' => '315px',
                ],
            ]) ?>
            <!-- /.box-body -->
        </div>
        <!-- /.box -->
    </div>
    <div class="col-md-12 col-xs-12">
        <div class="box box-solid">
            <div class="box-header">
                <i class="fa fa-circle rf-circle" style="font-size: 8px"></i>
                <h3 class="box-title">云服务器</h3>
            </div>
            <?= \common\widgets\echarts\Echarts::widget([
                'config' => [
                    'server' => Url::to(['cloud']),
                    'height' => '315px',
                ],
            ]) ?>
            <!-- /.box-body -->
        </div>
        <!-- /.box -->
    </div>
</div>
