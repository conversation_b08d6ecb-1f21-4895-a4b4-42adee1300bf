<?php

namespace addons\VymDesen\backend\controllers;

use addons\VymDesen\backend\models\UserAdmin\UserAdmin;
use addons\VymDesen\backend\controllers\BaseController;
use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\components\NewPipe\PipeLine;
use addons\VymDesen\common\components\pay\BillModel;
use addons\VymDesen\common\components\pay\PaymentOperation;
use addons\VymDesen\common\models\AdminSystemConfig;
use addons\VymDesen\common\models\Afterorder\AfterorderList;
use addons\VymDesen\common\models\Finance\FinanceFundflow;
use addons\VymDesen\common\models\Finance\FinanceManage;
use addons\VymDesen\common\models\Member\MemberPdt;
use addons\VymDesen\common\models\Member\MemberRecharge;
use addons\VymDesen\common\models\Payment\PaymentAccount;
use addons\VymDesen\common\models\Payment\PaymentAudit;
use addons\VymDesen\common\models\Payment\UserBill;
use addons\VymDesen\common\models\PayOrder\PaymentFlow;
use addons\VymDesen\common\models\PayOrder\PayorderDetail;
use addons\VymDesen\common\models\PayOrder\PayorderGeneral;
use addons\VymDesen\common\models\PayOrder\PayorderOriginal;
use addons\VymDesen\common\models\PayOrder\PayorderTrans;
use addons\VymDesen\common\models\PipeLine\PipelineList;
use addons\VymDesen\common\models\UserCredit\UserCredit;
use addons\VymDesen\common\models\UserCredit\UserCreditHistory;
use addons\VymDesen\common\models\UserMember\UserMember;
use addons\VymDesen\services\PaymentFlowService;
use common\exceptions\StatusException;
use moonland\phpexcel\Excel;
use Yii;

#付款相关

#工作流相关

#用户收支明细

/**
 * 支付流水信息 控制器
 *
 * <AUTHOR>
 *
 */
class PaymentFlowController extends BaseController
{
    #支付流水信息列表
    public function actionList()
    {

        $PaymentFlowModel = new PaymentFlow();
        $ConfigModel      = new AdminSystemConfig();
        $PaymentFlowQuery = $PaymentFlowModel->find();
        #创建搜索条件
        $PaymentFlowModel->createSearchWhere($PaymentFlowQuery, $this->get());

        #只看自己
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $configRes  = $ConfigModel->find()->where(['config_name' => 'pipe_default_myself'])->one();
        $configUser = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            $PaymentFlowQuery->andwhere(['admin_id' => $admin_id]);
        }

        #分页
        $iCount = $PaymentFlowQuery->count();
        $oPage  = DataHelper::getPage($iCount);

        $PayinfoList = $PaymentFlowQuery->offset($oPage->offset)->limit($oPage->limit)->orderBy('id desc')->asArray()->all();
        #获取客服
        $UserAdminModel         = new UserAdmin();
        $AdminSystemConfigModel = new AdminSystemConfig();
        $salesConfig            = $AdminSystemConfigModel->find()->where(['config_name' => 'order_default_myself'])->asArray()->one();
        $adminlist              = $UserAdminModel->find()->where(['in', 'admin_id', explode(',', $salesConfig['config_value'])])->asArray()->all();

        return $this->render('index', [
            'PayinfoList'  => $PayinfoList,
            'UserAdminRes' => $adminlist,
            'iCount'       => $iCount,
            'page'         => $oPage,
        ]);
    }

    #未支付列表
    public function actionUnpaidList()
    {

        $PaymentFlowModel = new PaymentFlow();
        $ConfigModel      = new AdminSystemConfig();
        $PaymentFlowQuery = $PaymentFlowModel->find()->where(['payment_settlement_status' => ['未支付']]);
        #创建搜索条件
        $PaymentFlowModel->createSearchWhere($PaymentFlowQuery, $this->get());

        #只看自己
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $configRes  = $ConfigModel->find()->where(['config_name' => 'pipe_default_myself'])->one();
        $configUser = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            $PaymentFlowQuery->andwhere(['admin_id' => $admin_id]);
        }

        #分页
        $iCount = $PaymentFlowQuery->count();
        $oPage  = DataHelper::getPage($iCount);

        $PayinfoList = $PaymentFlowQuery->offset($oPage->offset)->limit($oPage->limit)->orderBy('id desc')->asArray()->all();
        #获取客服
        $UserAdminModel         = new UserAdmin();
        $AdminSystemConfigModel = new AdminSystemConfig();
        $salesConfig            = $AdminSystemConfigModel->find()->where(['config_name' => 'order_default_myself'])->asArray()->one();
        $adminlist              = $UserAdminModel->find()->where(['in', 'admin_id', explode(',', $salesConfig['config_value'])])->asArray()->all();

        return $this->render('unpaid-list', [
            'PayinfoList'  => $PayinfoList,
            'UserAdminRes' => $adminlist,
            'iCount'       => $iCount,
            'page'         => $oPage,
        ]);
    }

    #已审核列表
    public function actionAuditedList()
    {

        $PaymentFlowModel = new PaymentFlow();
        $ConfigModel      = new AdminSystemConfig();
        $PaymentFlowQuery = $PaymentFlowModel->find()->where(['payment_audit_status' => ['已审核', '未通过', '无审核']]);
        #创建搜索条件
        $PaymentFlowModel->createSearchWhere($PaymentFlowQuery, $this->get());

        #只看自己
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $configRes  = $ConfigModel->find()->where(['config_name' => 'pipe_default_myself'])->one();
        $configUser = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            $PaymentFlowQuery->andwhere(['admin_id' => $admin_id]);
        }

        #分页
        $iCount = $PaymentFlowQuery->count();
        $oPage  = DataHelper::getPage($iCount);

        $PayinfoList = $PaymentFlowQuery->offset($oPage->offset)->limit($oPage->limit)->orderBy('id desc')->asArray()->all();
        #获取客服
        $UserAdminModel         = new UserAdmin();
        $AdminSystemConfigModel = new AdminSystemConfig();
        $salesConfig            = $AdminSystemConfigModel->find()->where(['config_name' => 'order_default_myself'])->asArray()->one();
        $adminlist              = $UserAdminModel->find()->where(['in', 'admin_id', explode(',', $salesConfig['config_value'])])->asArray()->all();

        return $this->render('audited-list', [
            'PayinfoList'  => $PayinfoList,
            'UserAdminRes' => $adminlist,
            'iCount'       => $iCount,
            'page'         => $oPage,
        ]);
    }

    #未审核列表
    public function actionUnauditedList()
    {

        $PaymentFlowModel = new PaymentFlow();
        $PaymentFlowQuery = $PaymentFlowModel->find()->where(['payment_audit_status' => '未审核', 'payment_settlement_status' => '已支付']);
        #创建搜索条件
        $PaymentFlowModel->createSearchWhere($PaymentFlowQuery, $this->get());
        #分页
        $iCount = $PaymentFlowQuery->count();
        $oPage  = DataHelper::getPage($iCount);

        $PayinfoList = $PaymentFlowQuery->offset($oPage->offset)->limit($oPage->limit)->orderBy('id desc')->asArray()->all();
        #获取客服
        $UserAdminModel         = new UserAdmin();
        $AdminSystemConfigModel = new AdminSystemConfig();
        $salesConfig            = $AdminSystemConfigModel->find()->where(['config_name' => 'order_default_myself'])->asArray()->one();
        $adminlist              = $UserAdminModel->find()->where(['in', 'admin_id', explode(',', $salesConfig['config_value'])])->asArray()->all();

        return $this->render('unaudited-list', [
            'PayinfoList'  => $PayinfoList,
            'UserAdminRes' => $adminlist,
            'iCount'       => $iCount,
            'page'         => $oPage,
        ]);
    }

    #查看流水详情，ajax
    public function actionLookFinanceDetail()
    {

        $payment_flow_no = $this->post("payment_flow_no");

        if (!$payment_flow_no) {
            $arrReturn = ['status' => 0, 'info' => '缺少流水单号'];
            return $this->renderJSON($arrReturn);
        }

        $PaymentFlowModel  = new PaymentFlow();
        $DetailModel       = new PayorderDetail();
        $OriginalModel     = new PayorderOriginal();
        $GeneralModel      = new PayorderGeneral();
        $PipelineListModel = new PipelineList();
        $AfterorderModel   = new AfterorderList();
        $HistoryModel      = new UserCreditHistory();
        $MemberPdtModel    = new MemberPdt();


        $paymentinfo = $PaymentFlowModel->find()->where(['payment_flow_no' => $payment_flow_no])->asArray()->one();

        if (!$paymentinfo) {
            $arrReturn = ['status' => 0, 'info' => '无效的流水单号'];
            return $this->renderJSON($arrReturn);
        }

        $orderlist = [];

        if ($paymentinfo['payment_flow_type'] == '用户充值') {
            #用户充值不用查询
        } else if ($paymentinfo['payment_flow_type'] == '挂账结算') {

            $historyIdList = explode(',', $paymentinfo['payment_credit_record_id']);

            $historyList = $HistoryModel->find()->joinwith('paymentflow')->where(["in", "credit_h_id", $historyIdList])->asArray()->all();

            foreach ($historyList as $key => $val) {

                $generalInfoList = $GeneralModel->find()->select('general_original_order')->where(['general_payorder_number' => $val['credit_payment_flow_id']])->groupBy('general_original_order')->asArray()->all();

                foreach ($generalInfoList as $k => $v) {
                    if (!in_array($v['general_original_order'], $orderlist)) {
                        $orderlist[] = $v['general_original_order'];
                    }
                }
            }

        } else {
            #订单支付

            $generalInfoList = $GeneralModel->find()->select('general_original_order')->where(['general_payorder_number' => $payment_flow_no])->groupBy('general_original_order')->asArray()->all();

            foreach ($generalInfoList as $k => $v) {
                if (!in_array($v['general_original_order'], $orderlist)) {
                    $orderlist[] = $v['general_original_order'];
                }
            }
        }

        $pipelist = $PipelineListModel->find()->joinwith('linetypes')->where(["in", "order_id", $orderlist])->asArray()->all();

        $data = [];

        foreach ($pipelist as $key => $val) {

            $hasRecord = false;
            $datakey   = null;

            foreach ($data as $kk => $vv) {
                if ($vv['line_id'] == $val['line_id']) {
                    $datakey   = $kk;
                    $hasRecord = true;
                    break;
                }
            }

            if (!$hasRecord) {

                $datakey = $data ? count($data) : 0;

                $data[] = [
                    'line_type'          => $val['linetypes']['line_type_name'],
                    'line_id'            => $val['line_id'],
                    'order_id'           => $val['order_id'],
                    'user_id'            => $val['user_id'],
                    'user_account'       => $val['user_account'],
                    'user_nickname'      => $val['user_nickname'],
                    'request_admin_id'   => $val['request_admin_id'],
                    'request_admin_name' => $val['request_admin_name'],
                    'line_status'        => $val['line_status'],
                    'local_data'         => [],
                ];
            }


            if ($val['linetypes']['line_type_name'] == '新购业务') {

                $detailList = $DetailModel->find()->where(['detail_original_order' => $val['order_id']])->asArray()->all();

                foreach ($detailList as $k => $v) {
                    $detailContent = json_decode($v['detail_content'], true);

                    $tempData                  = [];
                    $tempData['status']        = 0;
                    $tempData['info']          = '';
                    $tempData['payment_cycle'] = $detailContent['payment_cycle'];
                    $tempData['sell_price']    = $v['detail_price'];
                    $tempData['ip']            = [];

                    if (isset($detailContent['preset']) && $detailContent['preset'] != '') {
                        $tempData['status'] = 2;
                        $tempData['info']   = '已配置';

                        if (isset($detailContent['preset']['publish']) && $detailContent['preset']['publish'] == 1) {
                            $tempData['status'] = 2;
                            $tempData['info']   = '已配置，已发布';

                            if (isset($detailContent['preset']['test_id']) && $detailContent['preset']['test_id'] != '') {
                                $tempData['status'] = 1;
                                $tempData['info']   = '已处理完成';
                                #测试机发布不生成工单，直接记录IP
                                $tempData['ip'] = $detailContent['preset']['ip'];
                            } else {
                                #不是测试机，就要查询售后工单的处理结果
                                $afterOrderInfo = $AfterorderModel->find()->where(['line_id' => $val['line_id'], 'order_id' => $val['order_id'], 'detail_id' => $v['detail_id']])->asArray()->one();
                                if ($afterOrderInfo['ao_type'] == '已完成') {
                                    $tempData['status'] = 1;
                                    $tempData['info']   = '已处理完成';

                                    $afterorder_config = json_decode($afterOrderInfo['ao_request_content'], true);

                                    $tempData['ip'] = $afterorder_config['preset']['ip'];

                                } else if ($afterOrderInfo['ao_type'] == '未完成') {
                                    $tempData['status'] = 2;
                                    $tempData['info']   = '等待技术处理';
                                } else if ($afterOrderInfo['ao_type'] == '已取消') {
                                    $tempData['status'] = 0;
                                    $tempData['info']   = '工作任务已被取消';
                                }
                            }
                        } else {
                            $tempData['status'] = 2;
                            $tempData['info']   = '已配置，未发布';
                        }
                    } else {
                        $tempData['status'] = 0;
                        $tempData['info']   = '尚未配置';
                    }

                    $data[$datakey]['local_data'][] = $tempData;
                }

            } else if ($val['linetypes']['line_type_name'] == '续费业务') {

                #续费业务直接找订单详情就好了
                $detailList = $DetailModel->find()->where(['detail_original_order' => $val['order_id']])->asArray()->all();

                foreach ($detailList as $k => $v) {
                    $detailContent = json_decode($v['detail_content'], true);

                    $tempData                  = [];
                    $tempData['status']        = 1;      #续费默认完成
                    $tempData['info']          = '已处理完成';#续费默认完成
                    $tempData['payment_cycle'] = $detailContent['payment_cycle'];
                    $tempData['sell_price']    = $v['detail_price'];
                    $tempData['renew_num']     = $detailContent['renew_num'];
                    $tempData['end_time']      = date("Y-m-d", $detailContent['end_time']);
                    $tempData['ip']            = $detailContent['ip'];

                    $data[$datakey]['local_data'][] = $tempData;
                }

            } else if ($val['linetypes']['line_type_name'] == '变更配置-补款') {

                #变更配置一定会有一个工单，并且一定只有一个工单
                $afterOrderInfo = $AfterorderModel->find()->where(['line_id' => $val['line_id'], 'order_id' => $val['order_id']])->asArray()->one();

                $afterOrderDetail = json_decode($afterOrderInfo['ao_request_content'], true);

                $tempData = [];

                if ($afterOrderInfo['ao_type'] == '已完成') {
                    $tempData['status'] = 1;
                    $tempData['info']   = '已处理完成';
                } else if ($afterOrderInfo['ao_type'] == '未完成') {
                    $tempData['status'] = 2;
                    $tempData['info']   = '等待技术处理';
                } else if ($afterOrderInfo['ao_type'] == '已取消') {
                    $tempData['status'] = 0;
                    $tempData['info']   = '工作任务已被取消';
                }

                $pdtinfo                       = $MemberPdtModel->find()->where(['unionid' => $afterOrderDetail['unionid']])->asArray()->one();
                $tempData['old_sell_price']    = $pdtinfo['sell_price'];
                $tempData['modify_sell_price'] = $afterOrderDetail['modify_sell_price'];
                $tempData['ip']                = $afterOrderDetail['ip'];
                $tempData['modify']            = $afterOrderDetail['modifyData'];

                $data[$datakey]['local_data'][] = $tempData;

            } else if ($val['linetypes']['line_type_name'] == '更换机器-补款') {
                #更换机器一定会有一个工单，并且一定只有一个工单
                $afterOrderInfo = $AfterorderModel->find()->where(['line_id' => $val['line_id'], 'order_id' => $val['order_id']])->asArray()->one();

                $afterOrderDetail = json_decode($afterOrderInfo['ao_request_content'], true);

                $tempData = [];

                if ($afterOrderInfo['ao_type'] == '已完成') {
                    $tempData['status'] = 1;
                    $tempData['info']   = '已处理完成';
                } else if ($afterOrderInfo['ao_type'] == '未完成') {
                    $tempData['status'] = 2;
                    $tempData['info']   = '等待技术处理';
                } else if ($afterOrderInfo['ao_type'] == '已取消') {
                    $tempData['status'] = 0;
                    $tempData['info']   = '工作任务已被取消';
                }

                $tempData['old_sell_price']    = $afterOrderDetail['old_sell_price'];
                $tempData['modify_sell_price'] = $afterOrderDetail['sell_price'];

                if (isset($afterOrderDetail['is_exchangedip']) && $afterOrderDetail['is_exchangedip'] == 1) {
                    $tempData['is_exchangedip'] = 1;
                    $tempData['ip']             = $afterOrderDetail['frontData']['ip'];
                } else {
                    $tempData['is_exchangedip'] = 0;
                    $tempData['front_ip']       = $afterOrderDetail['frontData']['ip'];
                    $tempData['after_ip']       = $afterOrderDetail['afterData']['ip'];
                }

                $data[$datakey]['local_data'][] = $tempData;
            }
        }

        $arrReturn = ['status' => 1, 'info' => '查询完成', 'data' => $data];
        return $this->renderJSON($arrReturn);

    }

    #详情页
    public function actionDetails()
    {
        $get = $this->get();
        if (!$get['id']) {
            return $this->redirect(Yii::$app->request->referrer);
        }
        $PaymentFlowModel     = new PaymentFlow();
        $PayorderGeneralModel = new PayorderGeneral();
        $HistoryModel         = new UserCreditHistory();

        $PaymentFlowRes = $PaymentFlowModel->find()->where(['id' => $get['id']])->asArray()->one();
        if (empty($PaymentFlowRes)) {
            return $this->redirect(Yii::$app->request->referrer);
        }
        #获取支付单信息
        $general_payorder_List = explode(',', $PaymentFlowRes['payment_payorders']);
        #获取挂账单的信息
        $credit_id_list = explode(',', $PaymentFlowRes['payment_credit_record_id']);

        if ($general_payorder_List) {
            $GeneralList = $PayorderGeneralModel->find()->with('original')->where(['in', 'general_payorder', $general_payorder_List])->asArray()->all();
        } else {
            $GeneralList = null;
        }

        if ($credit_id_list) {
            $CreditList = $HistoryModel->find()->joinwith('credituser')->where(['in', 'credit_h_id', $credit_id_list])->asArray()->all();
        } else {
            $CreditList = null;
        }

        return $this->render('detail', [
            'PaymentFlowRes' => $PaymentFlowRes,
            'GeneralList'    => $GeneralList,
            'CreditList'     => $CreditList,
        ]);

    }

    #未支付流水支付页
    public function actionMergePayment()
    {

        $get = $this->get();
        if (!$get['flow_no']) {
            return $this->redirect(Yii::$app->request->referrer);
        }

        $PaymentFlowModel      = new PaymentFlow();
        $PayorderGeneralModel  = new PayorderGeneral();
        $PayorderOriginalModel = new PayorderOriginal();
        $PaymentAccountModel   = new PaymentAccount();

        $flow_no = $get['flow_no'];

        $PaymentFlowRes = $PaymentFlowModel->find()->where(['payment_flow_no' => $flow_no, 'payment_settlement_status' => '未支付'])->asArray()->one();
        if (empty($PaymentFlowRes)) {
            return $this->redirect(Yii::$app->request->referrer);
        }
        #
        #获取支付单信息
        $general_payorder_List = explode(',', $PaymentFlowRes['payment_payorders']);

        $PayorderGeneralList = $PayorderGeneralModel->find()->with('original')->where(['general_payorder' => $general_payorder_List])->asArray()->all();
        #总金额
        $payment_amount = array_sum(array_column($PayorderGeneralList, 'general_pay_money')); #付款金额

        $original_order_List  = array_unique(array_column($PayorderGeneralList, 'general_original_order')); #原始订单号数组
        $PayorderOriginalList = $PayorderOriginalModel->find()->with('general')->with('detail')->where(['order_id' => $original_order_List])->asArray()->all();

        $user_id = $PaymentFlowRes['user_id'];
        #能够合并付款肯定是一个用户，查询用户是否支持挂账
        $UserCreditModel = new UserCredit();
        $hasCredit       = $UserCreditModel->find()->where(['user_id' => $user_id, 'user_credit_status' => '启用'])->asArray()->one();

        $PaymentAccountList = $PaymentAccountModel->find()->asArray()->all();

        return $this->render('merge-payment', [
            'PaymentFlowRes'      => $PaymentFlowRes,
            'PayorderGeneralList' => $PayorderGeneralList,
            'payment_amount'      => $payment_amount,
            'hasCredit'           => $hasCredit,
            'PaymentAccountList'  => $PaymentAccountList,
        ]);
    }

    #线下付款模式
    public function actionOfflinePayment()
    {

        $PayorderGeneralModel = new PayorderGeneral();
        $PaymentAccountModel  = new PaymentAccount();
        $UserMemberModel      = new UserMember();
        $PaymentFlowModel     = new PaymentFlow();
        $PaymentAuditModel    = new PaymentAudit();
        $PipelineListModel    = new PipelineList();

        $post            = $this->post();
        $payment_flow_no = $post['flow_no'];

        if (empty($payment_flow_no)) {
            $arrReturn = ['status' => 0, 'info' => '异常的支付流水号'];
            return $this->renderJSON($arrReturn);
        }

        $PaymentFlowQuery = $PaymentFlowModel->find()->where(['payment_flow_no' => $payment_flow_no])->one();
        if (empty($PaymentFlowQuery)) {
            $arrReturn = ['status' => 0, 'info' => '未知的支付流水'];
            return $this->renderJSON($arrReturn);
        }

        if ($PaymentFlowQuery->payment_settlement_status != '未支付') {
            $arrReturn = ['status' => 0, 'info' => '支付流水状态异常'];
            return $this->renderJSON($arrReturn);
        }

        $payorder_no = explode(',', $PaymentFlowQuery->payment_payorders);

        $data = $post['data'];

        $PayorderGeneralList = $PayorderGeneralModel->find()->where(['general_payorder' => $payorder_no])->asArray()->all();
        if (count($PayorderGeneralList) != count($payorder_no)) {
            $arrReturn = ['status' => 0, 'info' => '存在付款单未查询到'];
            return $this->renderJSON($arrReturn);
        }

        foreach ($PayorderGeneralList as $key => $val) {

            if (!in_array($val['general_pay_lock'], ['等待支付'])) {
                $arrReturn = ['status' => 0, 'info' => '付款单状态异常：' . $val['general_payorder'] . '已支付或者已取消'];
                return $this->renderJSON($arrReturn);
            }
            if ($val['general_payorder_number'] != $payment_flow_no || $val['general_payorder_number'] == null || $val['general_payorder_number'] == '') {
                $arrReturn = ['status' => 0, 'info' => '付款单：' . $val['general_payorder'] . '已加入其他支付，请解除后重新合并'];
                return $this->renderJSON($arrReturn);
            }
        }

        $member_id_List = array_unique(array_column($PayorderGeneralList, 'general_payment_userid'));

        if (count($member_id_List) > 1) {
            $arrReturn = ['status' => 0, 'info' => '付款单存在多用户支付'];
            return $this->renderJSON($arrReturn);
        }

        $member_id = $member_id_List[0];

        #预赋值
        $total           = array_sum(array_column($PayorderGeneralList, 'general_pay_money')); #获取支付金额
        $payorder_no_str = implode(',', $payorder_no);
        $trans_time      = time();
        $payment_time    = strtotime($data['payment_time']);

        if ($total != $PaymentFlowQuery->payable_amount) {
            $arrReturn = ['status' => 0, 'info' => '付款单总金额与支付流水应付金额不一致'];
            return $this->renderJSON($arrReturn);
        }

        if ($data['payment_amount'] < $total) {
            $arrReturn = ['status' => 0, 'info' => '支付金额小于付款单应付金额'];
            return $this->renderJSON($arrReturn);
        }

        #支付方式
        $PaymentAccountRes = $PaymentAccountModel->find()->where(['id' => $data['payment_platform']])->asArray()->one();

        #开启事务
        $transaction = \Yii::$app->db->beginTransaction();

        #判定支付流水的支付是否需要审核
        $PaymentAuditRes = $PaymentAuditModel->find()->where(['payment_method' => '线下支付'])->asArray()->one();

        if ($PaymentAuditRes['is_audit'] == 'Y') {
            $PaymentFlowQuery->payment_audit_status = '未审核';
        } else {
            $PaymentFlowQuery->payment_audit_status = '无审核';
        }

        #更新支付流水信息
        $PaymentFlowQuery->payment_flow_type         = '订单支付';
        $PaymentFlowQuery->payment_mode              = '线下支付';
        $PaymentFlowQuery->payment_amount            = $data['payment_amount'];                                #支付金额
        $PaymentFlowQuery->payment_platform          = $PaymentAccountRes['another_name'];                     #支付平台
        $PaymentFlowQuery->payment_method            = '';                                                     #支付方式
        $PaymentFlowQuery->payment_trans_no          = $data['payment_trans_no'];                              #三方交易号
        $PaymentFlowQuery->payment_settlement_status = '已支付';                                                  #支付状态
        $PaymentFlowQuery->payment_successtime       = $payment_time;                                          #支付成功时间
        $PaymentFlowQuery->payment_callback_content  = '';                                                     #三方回调内容
        $PaymentFlowQuery->payment_callbacktime      = $trans_time;                                            #回调时间
        $PaymentFlowQuery->payment_updatetime        = time();

        if (!$PaymentFlowQuery->update()) {
            $transaction->rollBack();
            $arrReturn = ['status' => 0, 'info' => '流水信息更新失败'];
            return $this->renderJSON($arrReturn);
        }

        if ($PaymentFlowQuery->payment_audit_status == '无审核') {

            #当支付金额大于应付金额，退款余额
            if ($data['payment_amount'] > $total) {

                $UserMemberQuery = $UserMemberModel->find()->where(['u_id' => $member_id])->one();

                $front_balance = $UserMemberQuery->balance;
                $add_balance   = $data['payment_amount'] - $total;

                $UserMemberQuery->balance = $front_balance + $add_balance;

                if (!$UserMemberQuery->save()) {
                    $transaction->rollBack();
                    $arrReturn = ['status' => 0, 'info' => '用户余额更新失败', 'data' => ''];
                    return $this->renderJSON($arrReturn);
                }

                $after_balance = $UserMemberQuery->balance;

                #增加用户收支明细
                $FinanceFundflowModel                           = new FinanceFundflow();
                $FinanceFundflowModel->user_id                  = $UserMemberQuery->u_id;
                $FinanceFundflowModel->user_name                = $UserMemberQuery->username;
                $FinanceFundflowModel->admin_id                 = $UserMemberQuery->admin_id;
                $FinanceFundflowModel->admin_name               = $UserMemberQuery->admin_name;
                $FinanceFundflowModel->before_transaction_money = $front_balance;
                $FinanceFundflowModel->transaction_money        = $add_balance;
                $FinanceFundflowModel->after_transaction_money  = $after_balance;
                $FinanceFundflowModel->income_or_expense        = '收入';
                $FinanceFundflowModel->transaction_type         = '充值';
                $FinanceFundflowModel->transaction_mode         = '线下支付';
                $FinanceFundflowModel->transaction_channel      = $PaymentFlowQuery->payment_platform;
                $FinanceFundflowModel->unionid                  = '';
                $FinanceFundflowModel->flow_id                  = '';
                $FinanceFundflowModel->orderid                  = '';
                $FinanceFundflowModel->rechargeid               = '';
                $FinanceFundflowModel->payment_flow_no          = $payment_flow_no;
                $FinanceFundflowModel->line_id                  = '';
                $FinanceFundflowModel->transaction_description  = '线下支付款项溢出，余额返还';
                $FinanceFundflowModel->transaction_time         = $trans_time;

                $InsertFundflowRes = $FinanceFundflowModel->insert();
                if (!$InsertFundflowRes) {
                    $transaction->rollBack();
                    $arrReturn = ['status' => 0, 'info' => '用户收支明细新增失败'];
                    return $this->renderJSON($arrReturn);
                }
            }

        }

        #进行付款单操作
        $OperationRes = PaymentOperation::FollowUp_Operation($payment_flow_no);

        if ($OperationRes['status']) {
            $transaction->commit();
            $arrReturn = ['status' => 1, 'info' => '提交成功'];

        } else {
            $transaction->rollBack();
            $arrReturn = ['status' => 0, 'info' => $OperationRes['info']];

        }

        return $this->renderJSON($arrReturn);

    }

    #余额支付模式
    public function actionBalancePayment()
    {
        $post            = $this->post();
        $payment_flow_no = $post['flow_no'];
        $service = new PaymentFlowService();
        try{
            $service->balancePayment($payment_flow_no);
        }
        catch (StatusException $e) {
            return $this->renderJSON(['status' => $e->status, 'info' => $e->info]);
        }
        return $this->renderJSON(['status' => 0, 'info' => '余额支付成功']);
    }

    #挂账支付模式
    public function actionCreditPayment()
    {

        $PayorderGeneralModel  = new PayorderGeneral();
        $PayorderOriginalModel = new PayorderOriginal();
        $UserMemberModel       = new UserMember();
        $PaymentFlowModel      = new PaymentFlow();
        $PaymentAuditModel     = new PaymentAudit();
        $PipelineListModel     = new PipelineList();

        $post            = $this->post();
        $payment_flow_no = $post['flow_no'];

        if (empty($payment_flow_no)) {
            $arrReturn = ['status' => 0, 'info' => '异常的支付流水号'];
            return $this->renderJSON($arrReturn);
        }

        $PaymentFlowQuery = $PaymentFlowModel->find()->where(['payment_flow_no' => $payment_flow_no])->one();
        if (empty($PaymentFlowQuery)) {
            $arrReturn = ['status' => 0, 'info' => '未知的支付流水'];
            return $this->renderJSON($arrReturn);
        }

        if ($PaymentFlowQuery->payment_settlement_status != '未支付') {
            $arrReturn = ['status' => 0, 'info' => '支付流水状态异常'];
            return $this->renderJSON($arrReturn);
        }

        $payorder_no = explode(',', $PaymentFlowQuery->payment_payorders);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       #包含的付款单

        $PayorderGeneralList = $PayorderGeneralModel->find()->where(['general_payorder' => $payorder_no])->asArray()->all();
        if (count($PayorderGeneralList) != count($payorder_no)) {
            $arrReturn = ['status' => 0, 'info' => '存在付款单未查询到'];
            return $this->renderJSON($arrReturn);
        }

        foreach ($PayorderGeneralList as $key => $val) {
            if (!in_array($val['general_pay_lock'], ['等待支付'])) {
                $arrReturn = ['status' => 0, 'info' => '付款单状态异常：' . $val['general_payorder'] . '已支付或者已取消'];
                return $this->renderJSON($arrReturn);
            }

            if ($val['general_payorder_number'] != $payment_flow_no || $val['general_payorder_number'] == null || $val['general_payorder_number'] == '') {
                $arrReturn = ['status' => 0, 'info' => '付款单：' . $val['general_payorder'] . '已加入其他支付，请解除后重新合并'];
                return $this->renderJSON($arrReturn);
            }
        }

        $member_id_List = array_unique(array_column($PayorderGeneralList, 'general_payment_userid'));

        if (count($member_id_List) > 1) {
            $arrReturn = ['status' => 0, 'info' => '付款单存在多用户支付'];
            return $this->renderJSON($arrReturn);
        }

        $member_id = $member_id_List[0];

        #开启事务
        $transaction = \Yii::$app->db->beginTransaction();

        #预赋值
        $total           = array_sum(array_column($PayorderGeneralList, 'general_pay_money'));                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   #获取支付金额
        $payorder_no_str = implode(',', $payorder_no);
        $trans_time      = time();

        if ($total != $PaymentFlowQuery->payable_amount) {
            $transaction->rollBack();
            $arrReturn = ['status' => 0, 'info' => '付款单总金额与支付流水金额不一致'];
            return $this->renderJSON($arrReturn);
        }

        #更新支付流水信息	  #判定支付流水的支付是否需要审核
        $PaymentAuditRes = $PaymentAuditModel->find()->where(['payment_method' => '后付款'])->asArray()->one();

        if ($PaymentAuditRes['is_audit'] == 'Y') {
            $PaymentFlowQuery->payment_audit_status = '未审核';
        } else {
            $PaymentFlowQuery->payment_audit_status = '无审核';
        }

        $PaymentFlowQuery->payment_flow_type         = '订单支付';
        $PaymentFlowQuery->payment_mode              = '后付款';
        $PaymentFlowQuery->payment_amount            = $total;                                #金额
        $PaymentFlowQuery->payment_platform          = '后付款';                                 #支付平台
        $PaymentFlowQuery->payment_method            = '';                                    #支付方式
        $PaymentFlowQuery->payment_settlement_status = '已支付';                                 #支付状态
        $PaymentFlowQuery->payment_successtime       = $trans_time;                           #支付成功时间
        $PaymentFlowQuery->payment_callback_content  = '';                                    #三方回调内容
        $PaymentFlowQuery->payment_callbacktime      = $trans_time;                           #回调时间
        $PaymentFlowQuery->payment_updatetime        = time();

        if (!$PaymentFlowQuery->update()) {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '流水信息更新失败']);
        }

        #进行付款单操作
        $OperationRes = PaymentOperation::FollowUp_Operation($payment_flow_no);

        if (!$OperationRes['status']) {
            $transaction->rollBack();
            $arrReturn = ['status' => 0, 'info' => $OperationRes['info']];
            return $this->renderJSON($arrReturn);
        }

        #进行挂账记录操作
        $UserCreditModel = new UserCredit();
        $creditAccount   = $UserCreditModel->find()->where(['credit_id' => $member_id])->one();

        if (!$creditAccount) {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '挂账账户不存在']);
        }

        if ($creditAccount['user_credit_status'] == '禁用') {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '挂账账户已被禁用']);
        }

        if ($creditAccount['user_credit_money'] + $total > $creditAccount['user_credit_money_allow']) {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '可挂账余额不足以支付本次需要付款金额']);
        }

        $frontmoney = $creditAccount->user_credit_money;
        $nowmoney   = $total;

        $creditAccount->user_credit_money = $aftermoney = $creditAccount->user_credit_money + $total;

        if (!$creditAccount->save()) {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '挂账支付出现异常，请联系管理员']);
        }

        $HistoryModel = new UserCreditHistory();

        $HistoryModel->credit_id              = $creditAccount->credit_id;
        $HistoryModel->credit_front_cmoney    = $frontmoney;
        $HistoryModel->credit_now_cmoney      = $nowmoney;
        $HistoryModel->credit_after_cmoney    = $aftermoney;
        $HistoryModel->credit_payment_flow_id = $payment_flow_no;
        $HistoryModel->credit_paymoney        = $total;
        $HistoryModel->credit_time            = $trans_time;
        $HistoryModel->credit_pay_platform    = null;
        $HistoryModel->credit_pay_channel     = null;
        $HistoryModel->credit_pay_time        = null;
        $HistoryModel->credit_pay_status      = '等待支付';
        $HistoryModel->credit_pay_audit       = '未审核';
        $HistoryModel->credit_status          = '工作未完成';

        if ($HistoryModel->save()) {
            $transaction->commit();
            return $this->renderJSON(['status' => 0, 'info' => '挂账模式支付成功']);

        } else {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '加入到挂账消费记录出现异常']);

        }

    }

    #支付流水解除绑定支付 （将付款单恢复原始）
    public function actionUnbundlingMerger()
    {
        Yii::$app->request->isAjax || die('error');

        $PaymentFlowModel     = new PaymentFlow();
        $UserMemberModel      = new UserMember();
        $PayorderGeneralModel = new PayorderGeneral();
        $HistoryModel         = new UserCreditHistory();

        $Post = $this->post();
        if (!$Post['flow_no']) {
            return $this->renderJSON(['status' => 0, 'info' => '未知支付信息']);
        }
        $PaymentFlowQuery = $PaymentFlowModel->find()->where(['payment_flow_no' => $Post['flow_no']])->one();
        if (empty($PaymentFlowQuery)) {
            return $this->renderJSON(['status' => 0, 'info' => '未知支付信息']);
        }
        #预定值
        $PaymentFlowQuery->payment_settlement_status = '取消支付';

        $transaction = \Yii::$app->db->beginTransaction();

        if ($PaymentFlowQuery->payment_payorders) {

            $payorder_list = explode(',', $PaymentFlowQuery->payment_payorders);

            foreach ($payorder_list as $key => $val) {
                $PayorderGeneralQuery = $PayorderGeneralModel->find()->where(['general_payorder' => $val])->one();
                if (empty($PayorderGeneralQuery)) {
                    $transaction->rollBack();
                    return $this->renderJSON(['status' => 0, 'info' => '存在未知的付款单:' . $val]);
                }
                if ($PayorderGeneralQuery->general_pay_lock != '等待支付') {
                    $transaction->rollBack();
                    return $this->renderJSON(['status' => 0, 'info' => '付款单:' . $val . '的支付状态异常']);
                }

                $PayorderGeneralQuery->general_payorder_number = null;
                $PayorderGeneralQuery->general_pay_platform    = null;
                $PayorderGeneralQuery->general_pay_channel     = null;
                $PayorderGeneralQuery->general_locked_state    = '未锁定';

                if (!$PayorderGeneralQuery->update()) {
                    $transaction->rollBack();
                    return $this->renderJSON(['status' => 0, 'info' => '付款单信息更新失败']);
                    break;
                }
            }
        }

        if ($PaymentFlowQuery->payment_credit_record_id) {

            $credit_id_list = explode(',', $PaymentFlowQuery->payment_credit_record_id);

            $updateRes = $HistoryModel->updateAll(["credit_pay_status" => '等待支付', 'credit_pay_flowno' => '', 'credit_pay_channel' => ''], ['credit_h_id' => $credit_id_list]);

            if ($updateRes != count($credit_id_list)) {
                $transaction->rollBack();
                return $this->renderJSON(['status' => 0, 'info' => '挂账消费记录信息更新出现异常']);
            }

        }

        if ($PaymentFlowQuery->update()) {
            $transaction->commit();
            return $this->renderJSON(['status' => 1, 'info' => '解除合并支付成功']);
        } else {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '解除合并支付失败']);
        }

    }

    #审核支付信息
    public function actionPaymentAudit()
    {
        Yii::$app->request->isAjax || die('error');

        $PaymentFlowModel      = new PaymentFlow();
        $UserMemberModel       = new UserMember();
        $PayorderGeneralModel  = new PayorderGeneral();
        $PayorderOriginalModel = new PayorderOriginal();
        $PipelineListModel     = new PipelineList();
        $FinanceFundflowModel  = new FinanceFundflow();
        $HistoryModel          = new UserCreditHistory();
        $UserCreditModel       = new UserCredit();
        $UserBillModel         = new UserBill();

        $Post = $this->post();
        if (!$Post['id']) {
            return $this->renderJSON(['status' => 0, 'info' => '未知支付信息']);
        }
        $PaymentFlowQuery = $PaymentFlowModel->find()->where(['id' => $Post['id']])->one();
        if (empty($PaymentFlowQuery)) {
            return $this->renderJSON(['status' => 0, 'info' => '未知支付信息']);
        }

        if ($Post['is_right'] == 'Y') {
            if ($Post['payment_successtime'] == '' || $Post['payment_successtime'] == null) {
                return $this->renderJSON(['status' => 0, 'info' => '支付成功时间不完整']);
            }
        } else if ($Post['is_right'] == 'N') {
            if ($Post['reason_remark'] == '' || $Post['reason_remark'] == null) {
                return $this->renderJSON(['status' => 0, 'info' => '未通过需填写理由说明']);
            }
        }

        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        $trans_time = time();
        #固定值
        $PaymentFlowQuery->payment_auditor    = $this->getAdminInfo('uname');
        $PaymentFlowQuery->payment_audit_time = time();
        $PaymentFlowQuery->reason_remark      = $Post['reason_remark'];

        if ($Post['is_right'] == 'Y') {

            $PaymentFlowQuery->payment_audit_status = '已审核';
            #
            $PaymentFlowQuery->payment_successtime   = strtotime(date("Y-m-d H:i:s", strtotime($Post['payment_successtime'])));
            $PaymentFlowQuery->payment_trans_no      = $Post['payment_trans_no'];
            $PaymentFlowQuery->audit_time_difference = $PaymentFlowQuery->payment_audit_time - $PaymentFlowQuery->payment_successtime;

            #付款客户信息
            $UserMemberRes = $UserMemberModel->find()->where(['u_id' => $PaymentFlowQuery->user_id])->asArray()->one();

            #如果为充值
            if ($PaymentFlowQuery->payment_flow_type == '用户充值') {
                #用户充值记录
                $PaymentAccountModelNew = new PaymentAccount();
                $MemberRecharge         = new MemberRecharge;
                // $PaymentRes             = $PaymentAccountModelNew->find()->where(['another_name' => $PaymentFlowQuery->payment_platform])->asArray()->one();
                $code = $MemberRecharge->addWaterForCurrentMember(
                    $PaymentFlowQuery->payment_trans_no,
                    $PaymentFlowQuery->payable_amount,
                    null,
                    null,
                    $PaymentFlowQuery->user_id,
                    1
                );
                if (!$code) {
                    $transaction->rollBack();
                    $arrReturn = ['status' => 0, 'info' => '用户充值记录出现异常'];
                    return $this->renderJSON($arrReturn);
                }
                #更新余额
                #判定客户采用的什么支付模式 （在线支付。线下支付）

                if ($PaymentFlowQuery->payment_mode == '在线支付') {#在线支付已经将余额充值到客户账户
                    $payment_flow_no = $PaymentFlowQuery->payment_flow_no;

                    $CreateBill = BillModel::RechargeBalance($payment_flow_no, null);
                    if (!$CreateBill['status']) {
                        $transaction->rollBack();
                        $arrReturn = ['status' => 0, 'info' => $CreateBill['info']];
                        return $this->renderJSON($arrReturn);
                    }

                } else if ($PaymentFlowQuery->payment_mode == '线下支付') {
                    #线下支付则将充值金额  到客户账户
                    $UserMemberQuery = $UserMemberModel->find()->where(['u_id' => $PaymentFlowQuery->user_id])->one();

                    $front_amount = $UserMemberQuery->balance; #充值前金额

                    $UserMemberQuery->balance += $PaymentFlowQuery['payment_amount'];

                    $updateBalanceRes = $UserMemberQuery->update(false);

                    if (!$updateBalanceRes) {
                        $transaction->rollBack();
                        $arrReturn = ['status' => 0, 'info' => '用户余额更新失败'];
                        return $this->renderJSON($arrReturn);
                    }

                    $after_amount = $UserMemberQuery->balance;

                    $payment_flow_no = $PaymentFlowQuery['payment_flow_no'];
                    /* 需要给用户增加流水信息*/
                    $FinanceFundflowModel                           = new FinanceFundflow();
                    $FinanceFundflowModel->user_id                  = $PaymentFlowQuery->user_id;
                    $FinanceFundflowModel->user_name                = $PaymentFlowQuery->user_name;
                    $FinanceFundflowModel->admin_id                 = $PaymentFlowQuery->admin_id;
                    $FinanceFundflowModel->admin_name               = $PaymentFlowQuery->admin_name;
                    $FinanceFundflowModel->before_transaction_money = $front_amount;
                    $FinanceFundflowModel->transaction_money        = $PaymentFlowQuery->payment_amount;
                    $FinanceFundflowModel->after_transaction_money  = $after_amount;
                    $FinanceFundflowModel->income_or_expense        = '收入';
                    $FinanceFundflowModel->transaction_type         = '充值';
                    $FinanceFundflowModel->transaction_mode         = '线下支付';
                    $FinanceFundflowModel->transaction_channel      = $PaymentFlowQuery->payment_platform;
                    $FinanceFundflowModel->unionid                  = '';
                    $FinanceFundflowModel->flow_id                  = '';
                    $FinanceFundflowModel->orderid                  = '';
                    $FinanceFundflowModel->rechargeid               = '';
                    $FinanceFundflowModel->payment_flow_no          = $payment_flow_no;
                    $FinanceFundflowModel->line_id                  = '';
                    $FinanceFundflowModel->transaction_description  = '用户余额充值';
                    $FinanceFundflowModel->transaction_time         = $trans_time;

                    $InsertFundflowRes = $FinanceFundflowModel->insert();

                    if (!$InsertFundflowRes) {
                        $transaction->rollBack();
                        $arrReturn = ['status' => 0, 'info' => '用户收支明细新增失败', 'PaymentFlowQuery' => $PaymentFlowQuery->toArray(), 'error' => $FinanceFundflowModel->errors];
                        return $this->renderJSON($arrReturn);
                    }

                    $billinfo = $UserBillModel->find()->where(["bill_type" => "充值账单", "payment_flow_id" => $payment_flow_no])->orderBy("bill_id asc")->one();

                    $pipeinfo = $PipelineListModel->find()->where(["line_id" => $billinfo['line_id']])->one();
                    if ($pipeinfo) {
                        $pipeinfo->finance_status   = '已完成';
                        $pipeinfo->line_status      = '已完成';
                        $pipeinfo->line_finish_time = time();

                        if (!$pipeinfo->save()) {
                            $transaction->rollBack();
                            $arrReturn = ['status' => 0, 'info' => '修改工作任务出现异常'];
                            return $this->renderJSON($arrReturn);
                        }
                    }
                }
            } else if ($PaymentFlowQuery->payment_flow_type == '订单支付') {

                #获取流水中包含的付款单
                $payment_payorders = explode(',', $PaymentFlowQuery->payment_payorders);
                #获取所有付款单数据集合
                $PayOrderGeneralList = $PayorderGeneralModel->find()->where(['general_payorder' => $payment_payorders])->asArray()->all();
                #获取到所有订单号
                $original_orders = array_unique(array_column($PayOrderGeneralList, 'general_original_order'));
                #获取此次流水应支付金额
                $payment_total = array_sum(array_column($PayOrderGeneralList, 'general_pay_money'));

                #更新当前流水中付款单 将审核状态改为已审核
                $UpdateGeneralRes = $PayorderGeneralModel->UpdateAll(['general_payinfo_review' => '已审核'], ['general_payorder' => $payment_payorders]);#批量更新
                if ($UpdateGeneralRes != count($payment_payorders)) {
                    $transaction->rollBack();
                    $arrReturn = ['status' => 0, 'info' => '更新付款单支付审核失败'];
                    return $this->renderJSON($arrReturn);
                }

                $Refund_General_list = [];        #需要退款的付款单数据集合
                $Refund_Order_list   = [];        #需要退款的订单号集合
                $Cancel_line_list    = [];

                #支付方式为在线支付时，审核时，需要判定订单所属的工作流是否属于取消中
                if ($PaymentFlowQuery->payment_mode == '在线支付') {

                    #循环订单(  即循环工作任务)
                    foreach ($original_orders as $key => $val) {

                        #获取工作流
                        $PipelineListQuery = $PipelineListModel->find()->where(['order_id' => $val])->one();
                        if ($PipelineListQuery->line_status == '取消中') {

                            $Refund_Order_list[] = $val;
                            $Cancel_line_list[]  = $PipelineListQuery->line_id;

                            $hasTrue = true;

                            #获取到这个订单的所有付款单
                            $PayorderGeneralAll = $PayorderGeneralModel->find()->where(['general_original_order' => $PipelineListQuery->order_id])->asArray()->all();
                            foreach ($PayorderGeneralAll as $kk => $vv) {

                                if (in_array($vv['general_payorder'], $payment_payorders)) { #如果付款单在此次流水中
                                    $Refund_General_list[] = $PayorderGeneralAll[$kk];
                                } else { #如果不在此次流水中
                                    if ($vv['general_pay_lock'] == '支付完成' && $vv['general_payinfo_review'] == '未审核') {
                                        $hasTrue = false;
                                    }
                                }
                            }

                            if ($hasTrue) {
                                $PipelineListQuery->finance_status = '已完成';
                                if ($PipelineListQuery->afterorder_status == '已完成') {
                                    $PipelineListQuery->line_status      = '已取消';
                                    $PipelineListQuery->line_finish_time = time();
                                }

                                if (!$PipelineListQuery->update()) {
                                    $transaction->rollBack();
                                    $arrReturn = ['status' => 0, 'info' => '工作任务取消更新失败'];
                                    return $this->renderJSON($arrReturn);
                                }

                            }

                        }
                    }

                    #如果需要退款的不为空
                    if (!empty($Refund_General_list)) {

                        $refund_total     = array_sum(array_column($Refund_General_list, 'general_pay_money'));
                        $refund_payorders = array_column($Refund_General_list, 'general_payorder');

                        $UpdateGeneralRes = $PayorderGeneralModel->UpdateAll(['general_pay_lock' => '已退款'], ['general_payorder' => $refund_payorders]);
                        if ($UpdateGeneralRes != count($refund_payorders)) {
                            $transaction->rollBack();
                            $arrReturn = ['status' => 0, 'info' => '更新付款单支付状态失败'];
                            return $this->renderJSON($arrReturn);
                        }
                        #退款给用户
                        $UserMemberQuery = $UserMemberModel->find()->where(['u_id' => $PaymentFlowQuery->user_id])->one();
                        $front_balance   = $UserMemberQuery->balance;

                        $UserMemberQuery->balance += $refund_total;
                        if (!$UserMemberQuery->update()) {
                            $transaction->rollBack();
                            $arrReturn = ['status' => 0, 'info' => '用户余额退款失败'];
                            return $this->renderJSON($arrReturn);
                        }
                        $after_balance = $UserMemberQuery->balance;
                        #增加用户收支明细
                        $FinanceFundflowModel->user_id                  = $UserMemberQuery['u_id'];
                        $FinanceFundflowModel->user_name                = $UserMemberQuery['email'];
                        $FinanceFundflowModel->admin_id                 = $UserMemberQuery['admin_id'];
                        $FinanceFundflowModel->admin_name               = $UserMemberQuery['admin_name'];
                        $FinanceFundflowModel->before_transaction_money = $front_balance;
                        $FinanceFundflowModel->transaction_money        = $refund_total;
                        $FinanceFundflowModel->after_transaction_money  = $after_balance;
                        $FinanceFundflowModel->income_or_expense        = '收入';
                        $FinanceFundflowModel->transaction_type         = '订单取消退款';
                        $FinanceFundflowModel->transaction_mode         = '余额加款';
                        $FinanceFundflowModel->transaction_channel      = '用户余额';
                        $FinanceFundflowModel->unionid                  = '';
                        $FinanceFundflowModel->flow_id                  = '';
                        $FinanceFundflowModel->orderid                  = '';
                        $FinanceFundflowModel->rechargeid               = '';
                        $FinanceFundflowModel->payment_flow_no          = $PaymentFlowQuery->payment_flow_no;
                        $FinanceFundflowModel->line_id                  = '';
                        $FinanceFundflowModel->transaction_description  = '订单取消，款项余额返还';
                        $FinanceFundflowModel->transaction_time         = $trans_time;

                        $InsertFundflowRes = $FinanceFundflowModel->insert();
                        if (!$InsertFundflowRes) {
                            $transaction->rollBack();
                            return $this->renderJSON(['status' => 0, 'info' => '用户收支明细新增失败']);
                        }

                    }

                }

                #同时线下支付的流水 #增加用户收支明细
                if ($PaymentFlowQuery->payment_mode == '线下支付') {

                    $FinanceFundflowModel->user_id                  = $UserMemberRes['u_id'];
                    $FinanceFundflowModel->user_name                = $UserMemberRes['email'];
                    $FinanceFundflowModel->admin_id                 = $UserMemberRes['admin_id'];
                    $FinanceFundflowModel->admin_name               = $UserMemberRes['admin_name'];
                    $FinanceFundflowModel->before_transaction_money = $UserMemberRes['balance'];
                    $FinanceFundflowModel->transaction_money        = $payment_total;
                    $FinanceFundflowModel->after_transaction_money  = $UserMemberRes['balance'];
                    $FinanceFundflowModel->income_or_expense        = '支出';
                    $FinanceFundflowModel->transaction_type         = '消费';
                    $FinanceFundflowModel->transaction_mode         = '线下支付';
                    $FinanceFundflowModel->transaction_channel      = $PaymentFlowQuery->payment_platform;
                    $FinanceFundflowModel->unionid                  = '';
                    $FinanceFundflowModel->flow_id                  = '';
                    $FinanceFundflowModel->orderid                  = '';
                    $FinanceFundflowModel->rechargeid               = '';
                    $FinanceFundflowModel->payment_flow_no          = $PaymentFlowQuery->payment_flow_no;
                    $FinanceFundflowModel->line_id                  = '';
                    $FinanceFundflowModel->transaction_description  = '服务器产品订单支付';
                    $FinanceFundflowModel->transaction_time         = $trans_time;

                    $InsertFundflowRes = $FinanceFundflowModel->insert();
                    if (!$InsertFundflowRes) {
                        $transaction->rollBack();
                        return $this->renderJSON(['status' => 0, 'info' => '用户收支明细新增失败']);
                    }

                    #后续判断金额是否要退款( 当线下支付 金额大于 流水中付款单应付金额,此时多余的款项就需要加入用户余额)
                    if ($PaymentFlowQuery->payment_amount > $PaymentFlowQuery->payable_amount) {
                        $add_balance = $PaymentFlowQuery->payment_amount - $PaymentFlowQuery->payable_amount;

                        $UserMemberQuery = $UserMemberModel->find()->where(['u_id' => $PaymentFlowQuery->user_id])->one();

                        $front_balance = $UserMemberQuery->balance;

                        $UserMemberQuery->balance += $add_balance;  #增加用户余额

                        if (!$UserMemberQuery->update()) {
                            $transaction->rollBack();
                            $arrReturn = ['status' => 0, 'info' => '未知的流水类型'];
                            return $this->renderJSON($arrReturn);
                        }

                        $after_balance = $UserMemberQuery->balance;

                        #新增用户收支明细
                        $new_FinanceFundflowModel = new FinanceFundflow();

                        $new_FinanceFundflowModel->user_id                  = $UserMemberRes['u_id'];
                        $new_FinanceFundflowModel->user_name                = $UserMemberRes['email'];
                        $new_FinanceFundflowModel->admin_id                 = $UserMemberRes['admin_id'];
                        $new_FinanceFundflowModel->admin_name               = $UserMemberRes['admin_name'];
                        $new_FinanceFundflowModel->before_transaction_money = $front_balance;
                        $new_FinanceFundflowModel->transaction_money        = $add_balance;
                        $new_FinanceFundflowModel->after_transaction_money  = $after_balance;
                        $new_FinanceFundflowModel->income_or_expense        = '收入';
                        $new_FinanceFundflowModel->transaction_type         = '充值';
                        $new_FinanceFundflowModel->transaction_mode         = '线下支付';
                        $new_FinanceFundflowModel->transaction_channel      = $PaymentFlowQuery->payment_platform;
                        $new_FinanceFundflowModel->unionid                  = '';
                        $new_FinanceFundflowModel->flow_id                  = '';
                        $new_FinanceFundflowModel->orderid                  = '';
                        $new_FinanceFundflowModel->rechargeid               = '';
                        $new_FinanceFundflowModel->payment_flow_no          = $PaymentFlowQuery->payment_flow_no;
                        $new_FinanceFundflowModel->line_id                  = '';
                        $new_FinanceFundflowModel->transaction_description  = '线下支付款项溢出，余额返还';
                        $new_FinanceFundflowModel->transaction_time         = $trans_time;

                        $InsertFundflowRes = $new_FinanceFundflowModel->insert();
                        if (!$InsertFundflowRes) {
                            $transaction->rollBack();
                            return $this->renderJSON(['status' => 0, 'info' => '用户收支明细新增失败']);
                        }

                    }
                }

                #同时后付款的流水 #增加用户收支明细
                if ($PaymentFlowQuery->payment_mode == '后付款') {

                    $FinanceFundflowModel->user_id                  = $UserMemberRes['u_id'];
                    $FinanceFundflowModel->user_name                = $UserMemberRes['email'];
                    $FinanceFundflowModel->admin_id                 = $UserMemberRes['admin_id'];
                    $FinanceFundflowModel->admin_name               = $UserMemberRes['admin_name'];
                    $FinanceFundflowModel->before_transaction_money = $UserMemberRes['balance'];
                    $FinanceFundflowModel->transaction_money        = $payment_total;
                    $FinanceFundflowModel->after_transaction_money  = $UserMemberRes['balance'];
                    $FinanceFundflowModel->income_or_expense        = '支出';
                    $FinanceFundflowModel->transaction_type         = '消费';
                    $FinanceFundflowModel->transaction_mode         = '挂账支付';
                    $FinanceFundflowModel->transaction_channel      = $PaymentFlowQuery->payment_platform;
                    $FinanceFundflowModel->unionid                  = '';
                    $FinanceFundflowModel->flow_id                  = '';
                    $FinanceFundflowModel->orderid                  = '';
                    $FinanceFundflowModel->rechargeid               = '';
                    $FinanceFundflowModel->payment_flow_no          = $PaymentFlowQuery->payment_flow_no;
                    $FinanceFundflowModel->line_id                  = '';
                    $FinanceFundflowModel->transaction_description  = '服务器产品订单支付';
                    $FinanceFundflowModel->transaction_time         = $trans_time;

                    $InsertFundflowRes = $FinanceFundflowModel->insert();
                    if (!$InsertFundflowRes) {
                        $transaction->rollBack();
                        return $this->renderJSON(['status' => 0, 'info' => '用户收支明细新增失败']);
                    }
                }

                #判断所有付款单中订单是否支付完毕（排除取消的）
                if (!empty($Refund_Order_list)) {
                    $new_original_orders = array_diff($original_orders, $Refund_Order_list);
                } else {
                    $new_original_orders = $original_orders;
                }

                $orders_1 = [];
                foreach ($new_original_orders as $key => $val) {
                    #获取到每个订单下的所有付款单
                    $List                       = $PayorderGeneralModel->find()->where(['general_original_order' => $val])->asArray()->all();
                    $general_pay_lock_Arr       = array_column($List, 'general_pay_lock');                #获取订单下所有支付单支付情况
                    $general_payinfo_review_Arr = array_column($List, 'general_payinfo_review');          #获取订单下所有支付单审核情况

                    if (!array_intersect($general_pay_lock_Arr, ['等待支付', '已取消', '已退款'])) {            #如果找不到，代表所有支付单已经支付或者后付款挂账
                        if (!array_intersect($general_payinfo_review_Arr, ['未审核'])) {                        #如果找不到未审核 #代表所有支付单已经审核
                            $orders_1[] = $val;
                            $m          = true;
                        }
                    }
                }

                $lineid_list = [];
                $orders_2    = [];

                #$orders_1 就是订单完全支付完毕 并且已经审核的订单
                if (!empty($orders_1)) {

                    foreach ($orders_1 as $key => $val) {

                        $PipelineListQuery                 = $PipelineListModel->find()->where(['order_id' => $val])->one();
                        $PipelineListQuery->finance_status = '已完成';

                        if ($PipelineListQuery->afterorder_status == '已完成') {
                            $lineid_list[] = $PipelineListQuery->line_id;
                            $orders_2[]    = $val;

                            $PipelineListQuery->line_finish_time = time();  #完成时间
                        }

                        if (!$PipelineListQuery->save()) {
                            $transaction->rollBack();
                            $arrReturn = ['status' => 0, 'info' => '工作流信息更新失败'];
                            return $this->renderJSON($arrReturn);
                        }
                    }

                }

                #lineid_list 不为空代表 存在财务和工单都完成的单, 批量分发信息,完成工作流 (orders_2与lineid_list 相处于用一级别)
                if (!empty($lineid_list)) {
                    foreach ($lineid_list as $key => $val) {
                        $callbackdata = PipeLine::Operation_Distribute($val);
                        if (!$callbackdata['status']) {
                            $transaction->rollBack();
                            $arrReturn = ['status' => 0, 'info' => $callbackdata['info']];
                            return $this->renderJSON($arrReturn);
                        }
                    }
                }


            } else if ($PaymentFlowQuery->payment_flow_type == '挂账结算') {

                #获取此次流水应支付金额
                $payment_total = $PaymentFlowQuery->payment_amount;

                if ($PaymentFlowQuery->payment_mode == '余额支付') {
                    $creditGroup        = explode(',', $PaymentFlowQuery->payment_credit_record_id);
                    $updateCreditRecord = $HistoryModel->updateAll(['credit_pay_audit' => '已审核', 'credit_status' => '已结算'], ['credit_h_id' => $creditGroup]);

                    if ($updateCreditRecord != count($creditGroup)) {
                        $arrReturn = ['status' => 0, 'info' => '更新挂账记录出现异常'];
                        return $this->renderJSON($arrReturn);
                    }
                } else if ($PaymentFlowQuery->payment_mode == '线下支付') {
                    #新增用户收支明细

                    $creditGroup        = explode(',', $PaymentFlowQuery->payment_credit_record_id);
                    $updateCreditRecord = $HistoryModel->updateAll(['credit_pay_status' => '支付完成', 'credit_pay_audit' => '已审核', 'credit_status' => '已结算'], ['credit_h_id' => $creditGroup]);

                    if ($updateCreditRecord != count($creditGroup)) {
                        $arrReturn = ['status' => 0, 'info' => '更新挂账记录出现异常'];
                        return $this->renderJSON($arrReturn);
                    }

                    $FinanceFundflowModel->user_id                  = $UserMemberRes['u_id'];
                    $FinanceFundflowModel->user_name                = $UserMemberRes['email'];
                    $FinanceFundflowModel->admin_id                 = $UserMemberRes['admin_id'];
                    $FinanceFundflowModel->admin_name               = $UserMemberRes['admin_name'];
                    $FinanceFundflowModel->before_transaction_money = $UserMemberRes['balance'];
                    $FinanceFundflowModel->transaction_money        = $payment_total;
                    $FinanceFundflowModel->after_transaction_money  = $UserMemberRes['balance'];
                    $FinanceFundflowModel->income_or_expense        = '支出';
                    $FinanceFundflowModel->transaction_type         = '冲抵挂账';
                    $FinanceFundflowModel->transaction_mode         = '线下支付';
                    $FinanceFundflowModel->transaction_channel      = $PaymentFlowQuery->payment_platform;
                    $FinanceFundflowModel->unionid                  = '';
                    $FinanceFundflowModel->flow_id                  = '';
                    $FinanceFundflowModel->orderid                  = '';
                    $FinanceFundflowModel->rechargeid               = '';
                    $FinanceFundflowModel->payment_flow_no          = $PaymentFlowQuery->payment_flow_no;
                    $FinanceFundflowModel->line_id                  = '';
                    $FinanceFundflowModel->transaction_description  = '冲抵挂账金额';
                    $FinanceFundflowModel->transaction_time         = $trans_time;

                    $InsertFundflowRes = $FinanceFundflowModel->insert();
                    if (!$InsertFundflowRes) {
                        $transaction->rollBack();
                        return $this->renderJSON(['status' => 0, 'info' => '用户收支明细新增失败']);
                    }

                    #实付金额大于应付金额，溢出金额添加到余额
                    if ($PaymentFlowQuery->payment_amount > $PaymentFlowQuery->payable_amount) {
                        $add_balance = $PaymentFlowQuery->payment_amount - $PaymentFlowQuery->payable_amount;

                        $UserMemberQuery = $UserMemberModel->find()->where(['u_id' => $PaymentFlowQuery->user_id])->one();

                        $front_balance = $UserMemberQuery->balance;

                        $UserMemberQuery->balance += $add_balance;  #增加用户余额

                        if (!$UserMemberQuery->update()) {
                            $transaction->rollBack();
                            $arrReturn = ['status' => 0, 'info' => '未知的流水类型'];
                            return $this->renderJSON($arrReturn);
                        }

                        $after_balance = $UserMemberQuery->balance;

                        #新增用户收支明细
                        $new_FinanceFundflowModel = new FinanceFundflow();

                        $new_FinanceFundflowModel->user_id                  = $UserMemberRes['u_id'];
                        $new_FinanceFundflowModel->user_name                = $UserMemberRes['email'];
                        $new_FinanceFundflowModel->admin_id                 = $UserMemberRes['admin_id'];
                        $new_FinanceFundflowModel->admin_name               = $UserMemberRes['admin_name'];
                        $new_FinanceFundflowModel->before_transaction_money = $front_balance;
                        $new_FinanceFundflowModel->transaction_money        = $add_balance;
                        $new_FinanceFundflowModel->after_transaction_money  = $after_balance;
                        $new_FinanceFundflowModel->income_or_expense        = '收入';
                        $new_FinanceFundflowModel->transaction_type         = '充值';
                        $new_FinanceFundflowModel->transaction_mode         = '线下支付';
                        $new_FinanceFundflowModel->transaction_channel      = $PaymentFlowQuery->payment_platform;
                        $new_FinanceFundflowModel->unionid                  = '';
                        $new_FinanceFundflowModel->flow_id                  = '';
                        $new_FinanceFundflowModel->orderid                  = '';
                        $new_FinanceFundflowModel->rechargeid               = '';
                        $new_FinanceFundflowModel->payment_flow_no          = $PaymentFlowQuery->payment_flow_no;
                        $new_FinanceFundflowModel->line_id                  = '';
                        $new_FinanceFundflowModel->transaction_description  = '挂账金额线下支付溢出，转存为余额';
                        $new_FinanceFundflowModel->transaction_time         = $trans_time;

                        $InsertFundflowRes = $new_FinanceFundflowModel->insert();
                        if (!$InsertFundflowRes) {
                            $transaction->rollBack();
                            return $this->renderJSON(['status' => 0, 'info' => '用户收支明细新增失败']);
                        }
                    }
                } else if ($PaymentFlowQuery->payment_mode == '在线支付') {
                    #新增用户收支明细

                    $creditGroup        = explode(',', $PaymentFlowQuery->payment_credit_record_id);
                    $updateCreditRecord = $HistoryModel->updateAll(['credit_pay_status' => '支付完成', 'credit_pay_audit' => '已审核', 'credit_status' => '已结算'], ['credit_h_id' => $creditGroup]);

                    if ($updateCreditRecord != count($creditGroup)) {
                        $arrReturn = ['status' => 0, 'info' => '更新挂账记录出现异常'];
                        return $this->renderJSON($arrReturn);
                    }

                    $FinanceFundflowModel->user_id                  = $UserMemberRes['u_id'];
                    $FinanceFundflowModel->user_name                = $UserMemberRes['email'];
                    $FinanceFundflowModel->admin_id                 = $UserMemberRes['admin_id'];
                    $FinanceFundflowModel->admin_name               = $UserMemberRes['admin_name'];
                    $FinanceFundflowModel->before_transaction_money = $UserMemberRes['balance'];
                    $FinanceFundflowModel->transaction_money        = $payment_total;
                    $FinanceFundflowModel->after_transaction_money  = $UserMemberRes['balance'];
                    $FinanceFundflowModel->income_or_expense        = '支出';
                    $FinanceFundflowModel->transaction_type         = '冲抵挂账';
                    $FinanceFundflowModel->transaction_mode         = '在线支付';
                    $FinanceFundflowModel->transaction_channel      = $PaymentFlowQuery->payment_platform;
                    $FinanceFundflowModel->unionid                  = '';
                    $FinanceFundflowModel->flow_id                  = '';
                    $FinanceFundflowModel->orderid                  = '';
                    $FinanceFundflowModel->rechargeid               = '';
                    $FinanceFundflowModel->payment_flow_no          = $PaymentFlowQuery->payment_flow_no;
                    $FinanceFundflowModel->line_id                  = '';
                    $FinanceFundflowModel->transaction_description  = '冲抵挂账金额';
                    $FinanceFundflowModel->transaction_time         = $trans_time;

                    $InsertFundflowRes = $FinanceFundflowModel->insert();
                    if (!$InsertFundflowRes) {
                        $transaction->rollBack();
                        return $this->renderJSON(['status' => 0, 'info' => '用户收支明细新增失败']);
                    }

                    #实付金额大于应付金额，溢出金额添加到余额
                    if ($PaymentFlowQuery->payment_amount > $PaymentFlowQuery->payable_amount) {
                        $add_balance = $PaymentFlowQuery->payment_amount - $PaymentFlowQuery->payable_amount;

                        $UserMemberQuery = $UserMemberModel->find()->where(['u_id' => $PaymentFlowQuery->user_id])->one();

                        $front_balance = $UserMemberQuery->balance;

                        $UserMemberQuery->balance += $add_balance;  #增加用户余额

                        if (!$UserMemberQuery->update()) {
                            $transaction->rollBack();
                            $arrReturn = ['status' => 0, 'info' => '未知的流水类型'];
                            return $this->renderJSON($arrReturn);
                        }

                        $after_balance = $UserMemberQuery->balance;

                        #新增用户收支明细
                        $new_FinanceFundflowModel = new FinanceFundflow();

                        $new_FinanceFundflowModel->user_id                  = $UserMemberRes['u_id'];
                        $new_FinanceFundflowModel->user_name                = $UserMemberRes['email'];
                        $new_FinanceFundflowModel->admin_id                 = $UserMemberRes['admin_id'];
                        $new_FinanceFundflowModel->admin_name               = $UserMemberRes['admin_name'];
                        $new_FinanceFundflowModel->before_transaction_money = $front_balance;
                        $new_FinanceFundflowModel->transaction_money        = $add_balance;
                        $new_FinanceFundflowModel->after_transaction_money  = $after_balance;
                        $new_FinanceFundflowModel->income_or_expense        = '收入';
                        $new_FinanceFundflowModel->transaction_type         = '充值';
                        $new_FinanceFundflowModel->transaction_mode         = '在线支付';
                        $new_FinanceFundflowModel->transaction_channel      = $PaymentFlowQuery->payment_platform;
                        $new_FinanceFundflowModel->unionid                  = '';
                        $new_FinanceFundflowModel->flow_id                  = '';
                        $new_FinanceFundflowModel->orderid                  = '';
                        $new_FinanceFundflowModel->rechargeid               = '';
                        $new_FinanceFundflowModel->payment_flow_no          = $PaymentFlowQuery->payment_flow_no;
                        $new_FinanceFundflowModel->line_id                  = '';
                        $new_FinanceFundflowModel->transaction_description  = '挂账金额在线支付溢出，转存为余额';
                        $new_FinanceFundflowModel->transaction_time         = $trans_time;

                        $InsertFundflowRes = $new_FinanceFundflowModel->insert();
                        if (!$InsertFundflowRes) {
                            $transaction->rollBack();
                            return $this->renderJSON(['status' => 0, 'info' => '用户收支明细新增失败']);
                        }
                    }
                }

            } else {
                $arrReturn = ['status' => 0, 'info' => '未知的流水类型'];
                return $this->renderJSON($arrReturn);
            }

        } else if ($Post['is_right'] == 'N') {

            $payment_flow_no = $PaymentFlowQuery->payment_flow_no;

            #如果财务审核不通过
            $PaymentFlowQuery->payment_audit_status = '未通过';

            if ($PaymentFlowQuery->payment_flow_type == '用户充值') {

                #如果为充值  只存在 在线支付。线下支付两种模式
                if ($PaymentFlowQuery->payment_mode == '在线支付') {
                    #在线支付成功时，已经增加了用户余额，所以这里需扣除用户余额
                    $UserMemberQuery = $UserMemberModel->find()->where(['u_id' => $PaymentFlowQuery->user_id])->one();

                    $front_amount = $UserMemberQuery->balance;

                    if ($front_amount < $PaymentFlowQuery->payment_amount) {
                        $transaction->rollBack();
                        return $this->renderJSON(['status' => 0, 'info' => '当前用户余额小于需扣除金额']);
                    }

                    $UserMemberQuery->balance = $front_amount - $PaymentFlowQuery->payment_amount;

                    if (!$UserMemberQuery->save()) {
                        $transaction->rollBack();
                        return $this->renderJSON(['status' => 0, 'info' => '用户余额更新失败']);
                    }

                    $after_amount = $UserMemberQuery->balance;

                    #增加用户收支明细
                    $FinanceFundflowModel                           = new FinanceFundflow();
                    $FinanceFundflowModel->user_id                  = $PaymentFlowQuery->user_id;
                    $FinanceFundflowModel->user_name                = $PaymentFlowQuery->user_email;
                    $FinanceFundflowModel->admin_id                 = $PaymentFlowQuery->admin_id;
                    $FinanceFundflowModel->admin_name               = $PaymentFlowQuery->admin_name;
                    $FinanceFundflowModel->before_transaction_money = $front_amount;
                    $FinanceFundflowModel->transaction_money        = $PaymentFlowQuery->payment_amount;
                    $FinanceFundflowModel->after_transaction_money  = $after_amount;
                    $FinanceFundflowModel->income_or_expense        = '支出';
                    $FinanceFundflowModel->transaction_type         = '调账';
                    $FinanceFundflowModel->transaction_mode         = '余额扣除';
                    $FinanceFundflowModel->transaction_channel      = '用户余额';
                    $FinanceFundflowModel->unionid                  = '';
                    $FinanceFundflowModel->flow_id                  = '';
                    $FinanceFundflowModel->orderid                  = '';
                    $FinanceFundflowModel->rechargeid               = '';
                    $FinanceFundflowModel->payment_flow_no          = $payment_flow_no;
                    $FinanceFundflowModel->line_id                  = '';
                    $FinanceFundflowModel->transaction_description  = '支付信息有误，余额扣除';
                    $FinanceFundflowModel->transaction_time         = time();

                    $InsertFundflowRes = $FinanceFundflowModel->insert();

                    if (!$InsertFundflowRes) {
                        $transaction->rollBack();
                        return $this->renderJSON(['status' => 0, 'info' => '用户收支明细新增失败']);
                    }

                }

                if ($PaymentFlowQuery->payment_mode == '线下支付') {
                    #线下支付 充值 无需操作
                }

            }

            if ($PaymentFlowQuery->payment_flow_type == '订单支付') {

                if ($PaymentFlowQuery->payment_mode == '在线支付') {
                    $transaction->rollBack();
                    return $this->renderJSON(['status' => 0, 'info' => '订单支付类型，在线支付不能驳回']);
                }

                if ($PaymentFlowQuery->payment_mode == '余额支付') {
                    $transaction->rollBack();
                    return $this->renderJSON(['status' => 0, 'info' => '订单支付类型，余额支付不能驳回']);
                }

                #获取流水中包含的付款单
                $payment_payorders = explode(',', $PaymentFlowQuery->payment_payorders);

                #更新驳回流水中的付款单以及对应的订单信息
                foreach ($payment_payorders as $key => $val) {

                    $PayOrderGeneralQuery = $PayorderGeneralModel->find()->where(['general_payorder' => $payment_payorders])->one();

                    $PayOrderGeneralQuery->general_payorder_number = null;
                    $PayOrderGeneralQuery->general_pay_platform    = null;
                    $PayOrderGeneralQuery->general_pay_channel     = null;
                    $PayOrderGeneralQuery->general_pay_time        = null;
                    $PayOrderGeneralQuery->general_pay_lock        = '等待支付';
                    $PayOrderGeneralQuery->general_locked_state    = '未锁定';
                    $PayOrderGeneralQuery->general_payinfo_review  = '未审核';
                    $PayOrderGeneralQuery->general_callback_stream = null;

                    if (!$PayOrderGeneralQuery->update()) {
                        $transaction->rollBack();
                        return $this->renderJSON(['status' => 0, 'info' => '付款单信息更新失败']);
                    }

                    $general_original_order = $PayOrderGeneralQuery->general_original_order;

                    $PayorderOriginalQuery = $PayorderOriginalModel->find()->where(['order_id' => $general_original_order])->one();

                    $order_finish_pay = $PayorderOriginalQuery->order_finish_pay;

                    if ($order_finish_pay < $PayOrderGeneralQuery->general_pay_money) {
                        $transaction->rollBack();
                        return $this->renderJSON(['status' => 0, 'info' => '订单已付金额异常']);
                    }

                    $PayorderOriginalQuery->order_finish_pay = $order_finish_pay - $PayOrderGeneralQuery->general_pay_money;

                    if ($PayorderOriginalQuery->order_finish_pay == 0) {
                        $PayorderOriginalQuery->order_pay_status = '未支付';
                    } else {
                        $PayorderOriginalQuery->order_pay_status = '部分支付';
                    }

                    if (!$PayorderOriginalQuery->update()) {
                        $transaction->rollBack();
                        return $this->renderJSON(['status' => 0, 'info' => '订单信息更新失败']);
                    }

                }

                if ($PaymentFlowQuery->payment_mode == '线下支付') {

                }

                if ($PaymentFlowQuery->payment_mode == '后付款') {

                    #恢复挂账用户的挂账金额
                    $UserCreditQuery = $UserCreditModel->find()->where(['user_id' => $PaymentFlowQuery->user_id])->one();

                    $user_credit_money = $UserCreditQuery->user_credit_money;

                    if ($user_credit_money < $PaymentFlowQuery->payment_amount) {
                        $transaction->rollBack();
                        return $this->renderJSON(['status' => 0, 'info' => '用户挂账金额有异常']);
                    }

                    $UserCreditQuery->user_credit_money = $user_credit_money - $PaymentFlowQuery->payment_amount;

                    if (!$UserCreditQuery->update()) {
                        $transaction->rollBack();
                        return $this->renderJSON(['status' => 0, 'info' => '用户挂账金额恢复失败']);
                    }

                    #取消挂账记录
                    $HistoryListQuery = $HistoryModel->find()->where(['credit_payment_flow_id' => $payment_flow_no])->asArray()->all();
                    if (empty($HistoryListQuery)) {
                        $transaction->rollBack();
                        return $this->renderJSON(['status' => 0, 'info' => '用户挂账记录异常']);
                    }

                    $updateAll_res = $HistoryModel->updateAll(['credit_pay_status' => '已取消', 'credit_status' => '工作取消'], ['credit_payment_flow_id' => $payment_flow_no]);

                    if ($updateAll_res != count($HistoryListQuery)) {
                        $transaction->rollBack();
                        return $this->renderJSON(['status' => 0, 'info' => '用户挂账记录更新失败']);
                    }

                }

            }

            if ($PaymentFlowQuery->payment_flow_type == '挂账结算') {

                if ($PaymentFlowQuery->payment_mode == '在线支付') {
                    $transaction->rollBack();
                    return $this->renderJSON(['status' => 0, 'info' => '挂账结算类型，在线支付不能驳回']);
                }

                if ($PaymentFlowQuery->payment_mode == '余额支付') {
                    $transaction->rollBack();
                    return $this->renderJSON(['status' => 0, 'info' => '挂账结算类型，余额支付不能驳回']);
                }

                #剩下的就是线下支付
                $creditGroup = explode(',', $PaymentFlowQuery->payment_credit_record_id);

                $updateCreditRecord = $HistoryModel->updateAll(['credit_pay_flowno' => null, 'credit_pay_platform' => null, 'credit_pay_channel' => null, 'credit_pay_time' => null, 'credit_pay_status' => '等待支付', 'credit_pay_audit' => '未审核', 'credit_status' => '等待结算'], ['credit_h_id' => $creditGroup]);

                if ($updateCreditRecord != count($creditGroup)) {
                    $arrReturn = ['status' => 0, 'info' => '更新挂账记录出现异常'];
                    return $this->renderJSON($arrReturn);
                }

                #同时将挂账金额恢复回去
                $UserCreditQuery = $UserCreditModel->find()->where(['user_id' => $PaymentFlowQuery->user_id])->one();

                $user_credit_money = $UserCreditQuery->user_credit_money;

                $UserCreditQuery->user_credit_money = $user_credit_money + $PaymentFlowQuery->payable_amount;

                if (!$UserCreditQuery->update()) {
                    $transaction->rollBack();
                    return $this->renderJSON(['status' => 0, 'info' => '用户挂账金额恢复失败']);
                }

            }

        } else {
            return $this->renderJSON(['status' => 0, 'info' => '异常参数']);
        }

        #更新
        if ($PaymentFlowQuery->update()) {

            $SetAccountBill = BillModel::OffsetAccounts($PaymentFlowQuery->payment_flow_no);

            if (!$SetAccountBill["status"]) {
                $transaction->rollBack();
                return $this->renderJSON(['status' => 0, 'info' => $SetAccountBill["info"]]);
            }

            $transaction->commit();
            $arrReturn = ['status' => 1, 'info' => '审核成功'];
        } else {
            $transaction->rollBack();
            $arrReturn = ['status' => 0, 'info' => '审核提交失败'];
        }
        return $this->renderJSON($arrReturn);
    }

    #获取相关订单的详情信息
    public function actionGetOrderDetails()
    {
        Yii::$app->request->isAjax || die('error');
        $order_id = $_POST['order_id'];
        if (!$order_id) {
            $arrReturn = [
                'status' => 0,
                'info'   => "缺少订单号",
            ];
            return $this->renderJSON($arrReturn);
        }
        $PayorderOriginalModle = new PayorderOriginal();
        $PayorderGeneralModel  = new PayorderGeneral();
        $PayorderDetailModel   = new PayorderDetail();
        $MemberPdtModel        = new MemberPdt();

        $OriginalRes = $PayorderOriginalModle->find()->with('detail')->where(['order_id' => $order_id])->asArray()->one();

        $GeneralList = $PayorderGeneralModel->find()->where(['general_original_order' => $order_id])->asArray()->all();

        $DetailList = $PayorderDetailModel->find()->where(['detail_original_order' => $order_id])->asArray()->all();
        foreach ($DetailList as $key => $val) {
            $config = json_decode($val['detail_content'], true);

            if ($OriginalRes['order_type'] == '新购') {
                $DetailList[$key]['pdtname']      = Yii::$app->db->createCommand("select name from pdt_manage where id = '" . $config["pdt_id"] . "'")->queryScalar();
                $DetailList[$key]['servername']   = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '" . $config["server_type_id"] . "'")->queryScalar();
                $config['config']['defense']      = $config["config"]["defense"] ?: "N/A";
                $config['config']['operatsystem'] = $config["config"]["operatsystem"] ?: "N/A";
                $DetailList[$key]['config']       = $config['config'];
            } else if ($OriginalRes['order_type'] == '变更配置') {
                $Pdtinfo                        = $MemberPdtModel->find()->where(['unionid' => $config['unionid']])->asArray()->one();
                $iplist                         = json_decode($Pdtinfo['ip'], true);
                $DetailList[$key]['mainip']     = $iplist[0];
                $DetailList[$key]['pdtname']    = Yii::$app->db->createCommand("select name from pdt_manage where id = '" . $Pdtinfo["pdt_id"] . "'")->queryScalar();
                $DetailList[$key]['servername'] = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '" . $Pdtinfo["server_type_id"] . "'")->queryScalar();

                $payment_cycle = '';

                if ($Pdtinfo['payment_cycle'] == 1) {
                    $payment_cycle = '月付';
                } else if ($Pdtinfo['payment_cycle'] == 3) {
                    $payment_cycle = '季付';
                } else if ($Pdtinfo['payment_cycle'] == 6) {
                    $payment_cycle = '半年付';
                } else if ($Pdtinfo['payment_cycle'] == 12) {
                    $payment_cycle = '年付';
                } else {
                    $payment_cycle = $Pdtinfo['payment_cycle'] . '月付';
                }

                $DetailList[$key]['payment_cycle']     = $payment_cycle;
                $DetailList[$key]['old_sell_price']    = $Pdtinfo['sell_price'];
                $DetailList[$key]['modify_sell_price'] = $config['modify_sell_price'];

                foreach ($config['modifyData'] as $k => $v) {
                    if ($v['modify_type'] == 'pdt_id' || $v['modify_type'] == 'configbandwidth') {
                        unset($config['modifyData'][$k]);
                        continue;
                    } else {
                        if ($v['modify_type'] == 'hdd') {
                            $config['modifyData'][$k]['modify_type'] = '硬盘';
                        } else if ($v['modify_type'] == 'ram') {
                            $config['modifyData'][$k]['modify_type'] = '内存';
                        } else if ($v['modify_type'] == 'requirement_bandwidth') {
                            $config['modifyData'][$k]['modify_type'] = '带宽';
                        } else if ($v['modify_type'] == 'ipnumber') {
                            $config['modifyData'][$k]['modify_type'] = '可用IP数';
                        } else if ($v['modify_type'] == 'defense') {
                            $config['modifyData'][$k]['modify_type'] = '防御流量';
                        }
                    }
                }

                $DetailList[$key]['modify_data'] = $config['modifyData'];
            } else if ($OriginalRes['order_type'] == '更换机器') {
                $Pdtinfo                           = $MemberPdtModel->find()->where(['unionid' => $config['unionid']])->asArray()->one();#ip,配置，续费金额，补款。
                $DetailList[$key]['oldpdtname']    = Yii::$app->db->createCommand("select name from pdt_manage where id = '" . $Pdtinfo["pdt_id"] . "'")->queryScalar();
                $DetailList[$key]['oldservername'] = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '" . $Pdtinfo["server_type_id"] . "'")->queryScalar();
                $DetailList[$key]['newpdtname']    = Yii::$app->db->createCommand("select name from pdt_manage where id = '" . $config["pdt_id"] . "'")->queryScalar();
                $DetailList[$key]['newservername'] = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '" . $config["server_type_id"] . "'")->queryScalar();

                $pdtconfig = json_decode($Pdtinfo['config'], true);

                $DetailList[$key]['oldconfiginfo'] = $pdtconfig['cpu'] . ' / ' . $pdtconfig['ram'] . ' / ' . $pdtconfig['hdd'] . ' / ' . $pdtconfig['requirement_bandwidth'] . ' / ' . $pdtconfig['ipnumber'] . ' / ' . $pdtconfig['defense'] . ' / ' . $pdtconfig['operatsystem'];
                $DetailList[$key]['newconfiginfo'] = $config['config']['cpu'] . ' / ' . $config['config']['ram'] . ' / ' . $config['config']['hdd'] . ' / ' . $config['config']['requirement_bandwidth'] . ' / ' . $config['config']['ipnumber'] . ' / ' . $config['config']['defense'] . ' / ' . $config['config']['operatsystem'];


                $payment_cycle = '';

                if ($Pdtinfo['payment_cycle'] == 1) {
                    $payment_cycle = '月付';
                } else if ($Pdtinfo['payment_cycle'] == 3) {
                    $payment_cycle = '季付';
                } else if ($Pdtinfo['payment_cycle'] == 6) {
                    $payment_cycle = '半年付';
                } else if ($Pdtinfo['payment_cycle'] == 12) {
                    $payment_cycle = '年付';
                } else {
                    $payment_cycle = $Pdtinfo['payment_cycle'] . '月付';
                }

                $DetailList[$key]['oldsellprice'] = $Pdtinfo['sell_price'] . ' / ' . $payment_cycle;
                $DetailList[$key]['newsellprice'] = sprintf("%.2f", $config['sell_price']) . ' / ' . $payment_cycle;

                $DetailList[$key]['oldiplist'] = implode("<br/>", json_decode($Pdtinfo['ip'], true));
                $DetailList[$key]['newiplist'] = implode("<br/>", $config['ip']);

                $DetailList[$key]['paymoney'] = sprintf("%.2f", $config['difference_price']);
            } else if ($OriginalRes['order_type'] == '续费') {
                $config['config']['defense']      = $config["config"]["defense"] ? $config["config"]["defense"] : "N/A";
                $config['config']['operatsystem'] = $config["config"]["operatsystem"] ? $config["config"]["operatsystem"] : "N/A";
                $DetailList[$key]['config']       = $config['config'];
                $DetailList[$key]['sell_price']   = $config['sell_price'];
                $DetailList[$key]['renew_num']    = $config['renew_num'];
                $DetailList[$key]['ip']           = $config['ip'][0];
                $DetailList[$key]['end_time']     = date("Y-m-d", $config['end_time']);

                if ($config['payment_cycle'] == 1) {
                    $payment_cycle = '月付';
                } else if ($config['payment_cycle'] == 3) {
                    $payment_cycle = '季付';
                } else if ($config['payment_cycle'] == 6) {
                    $payment_cycle = '半年付';
                } else if ($config['payment_cycle'] == 12) {
                    $payment_cycle = '年付';
                } else {
                    $payment_cycle = $config['payment_cycle'] . '月付';
                }
                $DetailList[$key]['payment_cycle'] = $payment_cycle;
            }
        }

        $arrReturn = [
            'status'      => 1,
            'info'        => "查询完成",
            'GeneralList' => $GeneralList,
            'OriginalRes' => $OriginalRes,
            'DetailList'  => $DetailList,
        ];
        return $this->renderJSON($arrReturn);
    }

    #获取流水对应的交易号
    public function actionLookTransactionNo()
    {

        $payment_flow_no = $this->post("payment_flow_no");

        if (!$payment_flow_no) {
            $arrReturn = ['status' => 0, 'info' => '缺少流水单号'];
            return $this->renderJSON($arrReturn);
        }

        $PayorderTransModel = new PayorderTrans();

        $PayorderTransRes = $PayorderTransModel->find()->where(['order_number' => $payment_flow_no, 'payment_status' => 'Y'])->asArray()->one();

        unset($PayorderTransRes['payment_reponse']);

        $arrReturn = [
            'status' => 1,
            'info'   => '查询完成',
            'data'   => $PayorderTransRes,
        ];
        return $this->renderJSON($arrReturn);

    }

    #导出
    public function actionExport()
    {
        $FinanceManageModel = new FinanceManage();
        $FinanceManageQuery = $FinanceManageModel->getListAll([], $isObj = true);
        #创建搜索条件
        $FinanceManageModel->createSearchWhere($FinanceManageQuery, $this->get());

        $arrRes = $FinanceManageQuery->orderBy('trade_createtime desc')->asArray()->all();
        $models = [];
        foreach ($arrRes as $key => $value) {
            $models[$key]['id']                    = $value['id'];
            $models[$key]['user_name']             = $value['user_name'];
            $models[$key]['admin_name']            = $value['admin_name'];
            $models[$key]['trade_front_usermoney'] = $value['trade_front_usermoney'];
            $models[$key]['trade_money']           = $value['trade_money'];
            $models[$key]['trade_after_usermoney'] = $value['trade_after_usermoney'];
            $models[$key]['trade_imex']            = $value['trade_imex'];
            switch ($value['trade_type']) {
                case 'consume':
                    $models[$key]['trade_type'] = '用户消费';
                    break;
                case 'dummy':
                    $models[$key]['trade_type'] = '线下打款';
                    break;
                case 'recharge':
                    $models[$key]['trade_type'] = '用户充值';
                    break;
                case 'withdrawal':
                    $models[$key]['trade_type'] = '用户提现';
                    break;
                case 'order':
                    $models[$key]['trade_type'] = '交易补款';
                    break;
                case 'refund':
                    $models[$key]['trade_type'] = '交易退款';
                    break;
                case 'fee':
                    $models[$key]['trade_type'] = '手续费';
                    break;
                case 'other':
                    $models[$key]['trade_type'] = '其他原因';
                    break;
            }
            $models[$key]['trade_des']        = $value['trade_des'];
            $models[$key]['trade_createtime'] = date("Y-m-d H:i:s", $value['trade_createtime']);
            $models[$key]['trade_unionid']    = $value['trade_unionid'];
            $models[$key]['trade_orderid']    = $value['trade_orderid'];
            $models[$key]['serviceid']        = $value['serviceid'];
            $models[$key]['trade_point']      = $value['trade_point'] == 'N' ? '' : '有疑问标记';
            $models[$key]['is_audit']         = $value['is_audit'] == 'N' ? '' : '已审计';

        }

        ob_end_clean(); //解决ob缓存导致导出乱码的问题
        Excel::export([
            'models'   => $models,
            'fileName' => "流水信息列表_" . date("Y-m-d H:i:s", time()),
            'columns'  => ['id', 'user_name', 'admin_name', 'trade_front_usermoney', 'trade_money', 'trade_after_usermoney', 'trade_imex', 'trade_type', 'trade_des', 'trade_createtime', 'trade_unionid', 'trade_orderid', 'serviceid', 'trade_point', 'is_audit'],
            'headers'  => [
                'id'                    => '流水ID',
                'user_name'             => '用户账户',
                'admin_name'            => '客服名称',
                'trade_front_usermoney' => '交易前金额',
                'trade_money'           => '交易金额',
                'trade_after_usermoney' => '交易后金额',
                'trade_imex'            => '金额收支',
                'trade_type'            => '交易类型',
                'trade_des'             => '交易说明',
                'trade_createtime'      => '交易产生时间',
                'trade_unionid'         => '主机号',
                'trade_orderid'         => '订单号',
                'serviceid'             => '服务工单号',
                'trade_point'           => '是否疑问流水',
                'is_audit'              => '是否审计',
            ],
        ]);
    }
}
