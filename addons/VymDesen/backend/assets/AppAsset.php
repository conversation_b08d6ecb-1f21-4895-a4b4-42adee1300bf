<?php

namespace addons\VymDesen\backend\assets;

use common\helpers\AddonHelper;
use yii\web\AssetBundle;
use yii\web\View;

/**
 * 静态资源管理
 *
 * Class AppAsset
 * @package addons\VymDesen\backend\assets
 */
class AppAsset extends AssetBundle
{
    /**
     * @var string
     */
    public $sourcePath = '@addons/VymDesen/backend/resources/';

    public $css = [
        'ace/assets/css/bootstrap.min.css',
        'ace/assets/css/font-awesome.min.css',
        'ace/assets/css/ace.min.css',
        'ace/assets/css/ace-part2.min.css',
        'ace/assets/css/ace-rtl.min.css',
        'ace/assets/css/ace-skins.min.css',
        'ace/css/style.css',
        'css/select2.css',
        'js/layui/css/modules/layer/default/layer.css',
    ];

    public $js = [

    ];

    public $depends = [

    ];

    public function registerAssetFiles($view)
    {
        parent::registerAssetFiles($view);
        // 将 JavaScript 文件加载到 <head> 中
        $jsArray = [
            'ace/assets/js/ace-extra.min.js',
            'ace/assets/js/html5shiv.min.js',
            'ace/assets/js/respond.min.js',
            'ace/assets/js/jquery-2.1.4.min.js',
            'ace/assets/js/jquery-1.11.3.min.js',
            'ace/assets/js/jquery.mobile.custom.min.js',
            'ace/assets/js/chosen.jquery.min.js',
            'ace/assets/js/bootstrap.min.js',
            'ace/assets/js/bootstrap-tag.min.js',
            'ace/assets/js/jquery.dataTables.min.js',
            'ace/assets/js/jquery.dataTables.bootstrap.min.js',
            'ace/assets/js/ace-elements.min.js',
            'ace/assets/js/ace.min.js',
            'ace/widget/layer/layer.js',
            'js/jquery.spinner.js',
            'js/layui/layui.js',
            'js/layer-v2.1/layer/layer.js',
            'js/clipboard.min.js',
            'js/laydate-v1.1/laydate/laydate.js',
            'js/bootstrap-duallistbox.js',
            'js/bootstrap-multiselect.js',
            'js/select2.js',
        ];
        foreach ($jsArray as $js) {
            $view->registerJsFile(AddonHelper::file($js), ['position' => View::POS_HEAD]);
        }
        //全局Loading提示
        $view->registerJsFile('/resources/js/global-loading.js', ['position' => \yii\web\View::POS_HEAD]);
    }
}