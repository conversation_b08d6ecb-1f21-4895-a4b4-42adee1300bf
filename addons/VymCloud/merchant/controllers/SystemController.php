<?php

namespace addons\VymCloud\merchant\controllers;

use addons\VymCloud\common\models\CloudSystem;
use common\helpers\ArrayHelper;
use common\models\base\SearchModel;
use Yii;
use yii\web\NotFoundHttpException;

/**
 * SystemController
 */
class SystemController extends BaseController
{
    /**
     * @var CloudSystem
     */
    public $modelClass = CloudSystem::class;

    /**
     * 首页
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionIndex()
    {
        $searchModel = new SearchModel([
            'model'                  => $this->modelClass,
            'scenario'               => 'default',
            'partialMatchAttributes' => ['title', 'name', 'cluster_ids', 'mark'], // 模糊查询
            'pageSize'               => 0, // TreeGrid不使用分页，显示所有数据
            'defaultOrder'           => [
                'sort' => SORT_ASC,
                'id'   => SORT_DESC,
            ],
        ]);

        $dataProvider = $searchModel->search($this->request->queryParams);
        $this->addQueryParam($dataProvider);
        // TreeGrid需要禁用分页以正确显示树形结构
        $dataProvider->pagination = false;

        return $this->render($this->action->id, [
            'dataProvider' => $dataProvider,
            'searchModel'  => $searchModel,
            'clusterList'  => Yii::$app->vymCloudService->cluster->getDropDown(),
        ]);
    }


    /**
     * 编辑/创建 Ajax
     * @return array|mixed|string|\yii\web\Response
     * @throws \yii\base\ExitException
     * @throws \yii\db\Exception
     */
    public function actionAjaxEdit()
    {
        $id         = $this->request->get('id');
        $model      = $this->findModel($id);
        /** @var CloudSystem $model */
        $model->pid = $this->request->get('pid', null) ?? $model->pid; // 父id
        $post       = $this->request->post();
        $beforeClusterIds = null;
        if($model->pid == 0){
            $beforeClusterIds = $model->cluster_ids;
        }

        // ajax 校验
        $this->activeFormValidate($model);
        
        $clusterIds = ArrayHelper::getValue($post, 'CloudSystem.cluster_ids');
        $clusterIds && ArrayHelper::setValue($post, 'CloudSystem.cluster_ids', implode(',', $clusterIds));
        if ($model->load($post)) {
            $tmpIds = [];
            if($model->pid > 0){
                /** @var CloudSystem $parent */
                $parent = $this->findModel($model->pid);
                $curClusterIds = explode(',', $model->cluster_ids);
                $parentClusterIds = explode(',', $parent->cluster_ids);
                $tmpIds = array_unique(array_merge($parentClusterIds, $curClusterIds));
                if(empty(array_diff($tmpIds, $parentClusterIds))){
                    $tmpIds = [];
                }
            }
            else {
                //是根节点
                if($beforeClusterIds != $model->cluster_ids){
                    //集群id有变化
                    $beforeClusterIds = explode(',', $beforeClusterIds);
                    $curIds = explode(',', $model->cluster_ids);
                    //获取删除掉的集群id
                    $removedIds = array_unique(array_diff($beforeClusterIds, $curIds));
                    if(count($removedIds) > 0){
                        //获取所有子系统
                        $subSysArr = CloudSystem::find()->where(['pid' => $model->id])->select(['cluster_ids'])->asArray()->all();
                        $target = ArrayHelper::arrFind($subSysArr, function ($item) use ($removedIds){
                            $cluster_ids = explode(',', ArrayHelper::getValue($item, 'cluster_ids'));
                            //判断$removedIds和$cluster_ids是否有交集
                            $intersect = array_intersect($removedIds, $cluster_ids);
                            if (!empty($intersect)) {
                                return true;
                            }
                            return false;
                        });
                        if($target){
                            //存在交集
                            return $this->message('子系统中存在其他集群id,无法修改根节点的集群id!', $this->redirect($this->request->referrer), 'error');
                        }
                    }
                }
                $clusterIds = explode(',', $model->cluster_ids);
            }
            if(!empty($tmpIds)){
                $parent->cluster_ids = implode(',', $tmpIds);
                $transaction = Yii::$app->db->beginTransaction();
                try{
                    $res = $parent->save();
                    $res && $res = $model->save();
                    $res && $transaction->commit();
                }
                catch (\Exception $e){
                    $transaction->rollBack();
                    throw $e;
                }
            }
            else {
                $res = $model->save();
            }
            return $res ? $this->redirect($this->request->referrer)
                : $this->message($this->getError($model), $this->redirect($this->request->referrer), 'error');
        }

        return $this->renderAjax($this->action->id, [
            'model'       => $model,
            'clusterList' => Yii::$app->vymCloudService->cluster->getDropDown(),
            'systemList'  => Yii::$app->vymCloudService->system->getDropDownForEdit($id),
        ]);
    }

    /**
     * 删除
     *
     * @param $id
     *
     * @return mixed
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    public function actionDelete($id)
    {
        if ($this->findModel($id)->delete()) {
            return $this->message("删除成功", $this->redirect($this->request->referrer));
        }

        return $this->message("删除失败", $this->redirect($this->request->referrer), 'error');
    }

    /**
     * 批量删除
     */
    public function actionBatchDelete()
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            return $this->message("请选择要删除的记录", $this->redirect($this->request->referrer), 'error');
        }

        $transaction = $this->beginTransaction();
        try {
            $this->modelClass::deleteAll(['id' => $ids]);
            $transaction->commit();
            return $this->message("批量删除成功", $this->redirect($this->request->referrer));
        } catch (\Exception $e) {
            $transaction->rollBack();
            return $this->message("批量删除失败：" . $e->getMessage(), $this->redirect($this->request->referrer), 'error');
        }
    }
}

