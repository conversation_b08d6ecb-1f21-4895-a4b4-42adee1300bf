<?php

namespace addons\VymCloud\merchant\controllers;

use addons\VymCloud\common\models\CloudProduct;
use common\helpers\ArrayHelper;
use common\models\base\SearchModel;
use Yii;
use yii\db\Exception;
use yii\db\StaleObjectException;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * ProductController implements the CRUD actions for CloudProduct model.
 */
class ProductController extends BaseController
{

    /**
     * @var CloudProduct
     */
    public $modelClass = CloudProduct::class;


    /**
     * 首页
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionIndex()
    {
        $searchModel = new SearchModel([
            'model'                  => $this->modelClass,
            'scenario'               => 'default',
            'partialMatchAttributes' => ['title'], // 模糊查询
            'defaultOrder'           => [
                'product_id' => SORT_DESC,
            ],
            'pageSize'               => $this->pageSize,
        ]);

        $dataProvider = $searchModel->search($this->request->queryParams);
        // $dataProvider->query->andWhere(['status' => StatusEnum::ENABLED]);
        $this->addQueryParam($dataProvider);
        return $this->render($this->action->id, [
            'dataProvider' => $dataProvider,
            'searchModel'  => $searchModel,
            'clusterList'  => Yii::$app->vymCloudService->cluster->getDropDown(),
        ]);
    }

    /**
     * 新增/编辑
     *
     * @param int|null $product_id 套餐ID
     *
     * @return string|Response
     * @throws Exception
     */
    public function actionEdit($product_id = null)
    {
        $newModel = $this->getRequestParam();
        if(!$newModel){
            $model      = $this->findModel($product_id);
        }
        else {
            $model      = $newModel;
        }
        $post       = $this->request->post();
        $clusterIds = ArrayHelper::getValue($post, 'CloudProduct.cluster_ids');
        $clusterIds && ArrayHelper::setValue($post, 'CloudProduct.cluster_ids', implode(',', $clusterIds));
        if ($this->request->isPost && $model->load($post)) {
            $res = $model->save();
            if($res){
                $this->setRequestParam($model);
                return $this->message('操作成功', $this->redirect($this->request->referrer));
            }
            else {
                $this->setRequestParam($model);
                return $this->message($this->getError($model), $this->redirect($this->request->referrer), 'error');
            }
        }
        else {
            $this->setRequestParam(null);
        }

        return $this->render($this->action->id, [
            'model'       => $model,
            'clusterList' => Yii::$app->vymCloudService->cluster->getDropDown(),
        ]);
    }


    /**
     * 删除
     *
     * @param $id
     *
     * @return mixed
     * @throws \Throwable|StaleObjectException
     */
    public function actionDelete($id)
    {
        if ($this->findModel($id)->delete()) {
            return $this->message("删除成功", $this->redirect($this->request->referrer));
        }

        return $this->message("删除失败", $this->redirect($this->request->referrer), 'error');
    }
}
