<?php

use common\enums\AppEnum;
use common\helpers\MerchantHelper;
use common\widgets\webuploader\Files;
use yii\widgets\ActiveForm;
use common\helpers\Html;
use common\enums\StatusEnum;

$this->title = $model->isNewRecord ? '创建工单' : '编辑工单：' . $model->title;
$this->params['breadcrumbs'][] = ['label' => '工单管理', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

?>

<div class="row">
    <div class="col-lg-12">
        <div class="box">
            <div class="box-header with-border">
                <h3 class="box-title"><?= $this->title; ?></h3>
            </div>
            <?php $form = ActiveForm::begin([
                'fieldConfig' => [
//                    'template' => "<div class='row'><div class='row'><div class='col-sm-2 text-right'>{label}</div><div class='col-sm-10'>{input}\n{hint}\n{error}</div></div></div>",
                ],
                'options' => ['enctype' => 'multipart/form-data'],
            ]); ?>
            <div class="box-body">
                <?= $form->field($model, 'title')->textInput(['maxlength' => true]) ?>
                <?php if (Yii::$app->id == AppEnum::BACKEND): ?>
                    <?= MerchantHelper::merchantSelect($form, $model,['onchange' => 'loadCategories(this.value)']) ?>
                    <?= $form->field($model, 'category_id')->dropDownList([], ['prompt' => '请选择分类']) ?>
                <?php else: ?>
                    <?= $form->field($model, 'category_id')->dropDownList($categoryDropDownList, ['prompt' => '请选择分类']) ?>
                <?php endif; ?>

                <?= $form->field($model, 'priority')->radioList($priorityList) ?>
                <?= $form->field($model, 'content')->textarea(['rows' => 6]) ?>
                
                <div class="form-group">
                    <div class='col-sm-2 text-right'>
                        <label class="control-label">上传图片</label>
                    </div>
                    <div class='col-sm-10'>
                        <div id="image-upload-container">
                            <!-- 图片上传控件将在这里动态生成 -->
                            <?= Files::widget([
                                'name' => "images",
                                'type' => 'images',
                                'themeConfig'=>[
                                    'select'=>false
                                ],
                                'theme' => 'default',
                                'config' => [
                                    'pick' => [
                                        'multiple' => true,
                                    ],
                                    'fileNumLimit'=>6,
                                ]
                            ]) ?>
                        </div>
<!--                        <button type="button" id="add-image" class="btn btn-default btn-sm"><i class="fa fa-plus"></i> 添加图片</button>-->
                    </div>
                </div>
                
                <?php if (!$model->isNewRecord): ?>
                <?= $form->field($model, 'status')->dropDownList([
                    1 => '待处理',
                    2 => '处理中',
                    3 => '已解决',
                    4 => '已关闭',
                ]) ?>
                <?php endif; ?>
            </div>
            <div class="box-footer text-center">
                <button class="btn btn-primary" type="submit">保存</button>
                <span class="btn btn-white" onclick="history.go(-1)">返回</span>
            </div>
            <?php ActiveForm::end(); ?>
        </div>
    </div>
</div>
<script>
    function loadCategories(merchantId) {
        // 清空分类下拉框
        $('#ticket-category_id').empty().append('<option value="">请先选择商户</option>');

        if (!merchantId) return;
let url = '<?= \common\helpers\Url::to(['list-category']) ?>'
        $.getJSON(url, {merchant_id: merchantId}, function (data) {
            console.log(data)
            if(data.code == 200){
                let cate = data.data;
                for (const key in cate) {
                    $('#ticket-category_id').append(
                        $('<option></option>').val(key).text(cate[key])
                    );
                }
            }
            // data 格式：[{id: 1, name: '分类A'}, {id: 2, name: '分类B'}]

        });
    }
</script>
<?php
$js = <<<JS
// 添加图片上传控件
var imageCount = 0;
$('#add-image').click(function() {
    if (imageCount >= 5) {
        alert('最多上传5张图片');
        return;
    }
    
    var imageInput = '<div class="image-upload-item" style="margin-bottom:10px;">' +
                     '<input type="file" name="image[]" class="image-upload" accept="image/*">' +
                     '<button type="button" class="btn btn-danger btn-xs remove-image" style="margin-left:10px;">删除</button>' +
                     '<div class="image-preview" style="margin-top:5px;"></div>' +
                     '</div>';
    $('#image-upload-container').append(imageInput);
    imageCount++;
});

// 删除图片上传控件
$(document).on('click', '.remove-image', function() {
    $(this).closest('.image-upload-item').remove();
    imageCount--;
});

// 图片预览
$(document).on('change', '.image-upload', function() {
    var file = this.files[0];
    var preview = $(this).closest('.image-upload-item').find('.image-preview');
    
    if (file) {
        var reader = new FileReader();
        reader.onload = function(e) {
            preview.html('<img src="' + e.target.result + '" style="max-height:100px;max-width:100%;">');
        }
        reader.readAsDataURL(file);
    } else {
        preview.html('');
    }
});
JS;

$this->registerJs($js);
?>
