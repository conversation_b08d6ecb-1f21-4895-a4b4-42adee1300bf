<?php

use yii\helpers\Html;
use yii\helpers\Url;

$this->title = '控制台管理-系统设置-常规设置';
?>
<?php $this->beginBlock('content') ?>
<?php $node = \Yii::$app->session['auth']['node']; ?>

    <link rel="stylesheet" href="/css/bootstrap-multiselect.css"/>

    <style type="text/css">
        .help-text {
            height: 34px;
            line-height: 26px;
        }
        .table{
            width: 235px;
            margin-bottom: 0px;
            margin-top: 30px;
        }
        th{
            text-align: center;
        }
        .td input{
            width: 105px;
            text-align: center;
        }
        #off-ioc{
            right: 0px;
            top: 0px;
            margin-top: 6px;
            position: static;
        }
        textarea{
            height: 200px;
        }
    </style>

    <div class="row">
        <div class="col-xs-12">
            <form id="systemform" class="form-horizontal" onsubmit="return false;">
                <?php foreach ($ArrRes as $key => $val): ?>
                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right"><?php echo $val['config_alias'] ?></label>
                        <div class="col-sm-9">
                            <?php if (in_array($val['config_name'], ['is_must_certification'])): ?>
                                <select class="col-xs-1" name="<?php echo $val['config_name'] ?>">
                                    <option value="Y" <?php echo $val['config_value'] == 'Y' ? 'selected' : '' ?> >是
                                    </option>
                                    <option value="N" <?php echo $val['config_value'] == 'N' ? 'selected' : '' ?> >否
                                    </option>
                                </select>
                                <span class="help-inline help-text col-xs-12 col-sm-7">
								<span class="middle"><?php echo $val['config_remark'] ?></span>
							</span>
                            <?php elseif (in_array($val['config_name'], ['system_administrator', 'order_default_myself', 'afterorder_cando', 'payorder_default_myself', 'renew_default_myself', 'business_default_myself', 'pipe_default_myself', 'user_canuse_sales', 'front_canuse_sales', 'afterorder_worker', 'afterorder_manager'])): ?>
                                <?php $chooseIdlist = explode(",", $val['config_value']); ?>
                                <select multiple="multiple" name="" class="checkMore" data-placeholder="点击选择">
                                    <?php foreach ($Adminlist as $k => $v): ?>
                                        <option value="<?php echo $v['admin_id'] ?>" <?php echo in_array($v['admin_id'], $chooseIdlist) ? 'selected' : '' ?> ><?php echo $v['uname'] ?></option>
                                    <?php endforeach ?>
                                </select>

                                <input type="hidden" name="<?php echo $val['config_name'] ?>" class="checkTrue"/>

                                <span class="help-inline help-text col-xs-12 col-sm-7">
								<span class="middle"><?php echo $val['config_remark'] ?></span>
							</span>

                            <?php elseif (in_array($val['config_name'], ['notice_config'])): ?>
                                <input type="checkbox" name="notice_config[]" class="i-checks"
                                       value="notice" <?php echo in_array('notice', json_decode($val['config_value'],true)) ? 'checked' : '' ?>>
                                <span>站内信</span>
                                <input type="checkbox" name="notice_config[]"
                                       value="mobile" <?php echo in_array('mobile', json_decode($val['config_value'],true)) ? 'checked' : '' ?>>
                                <span>手机通知</span>
                                <input type="checkbox" name="notice_config[]"
                                       value="email" <?php echo in_array('email', json_decode($val['config_value'],true)) ? 'checked' : '' ?>>
                                <span>邮件通知</span>
                                </span>
                            <?php //elseif (in_array($val['config_name'], ['recommended_config'])): ?>
                            <!--    --><?php //$recommended_config = json_decode($val['config_value'],true)?>
                            <!--    <table class="table tableOL">-->
                            <!--        <th>时长【月】</th>-->
                            <!--        <th>比例</th>-->
                            <!--        --><?php //foreach ($recommended_config as $keyN => $valN): ?>
                            <!--            <tr class="table--><?//=$keyN?><!--">-->
                            <!--                <td style="width: 30px" class="td"><input name="recommended_config_key[]" type="text" class="col-xs-1" value="--><?//=$keyN?><!--" /></td>-->
                            <!--                <td class="td"><input name="recommended_config_val[]" type="text" class="col-xs-1" value="--><?//=$valN?><!--" /></td>-->
                            <!--                <td class="td"><a onclick="deleteVal('table'+--><?//=$keyN?>//)" id="off-ioc" class="select2-search-choice-close" tabindex="-1"></a></td>
                            //            </tr>
                            //        <?php //endforeach ?>
                            <!--    </table>-->
                            <!--    <span style="width: 230px" class="help-inline help-text col-xs-12 col-sm-7">-->
							<!--	    <span class="middle">--><?php //echo $val['config_remark'] ?><!--</span>-->
							<!--    </span>-->
                            <!--    <button type="button" class="btn btn-white btn-primary btn-bold" style="border-bottom-width:1px" onclick="addVal('table'+--><?//=$keyN?>//,'tableOL')">
                            //        <span class="ace-icon fa fa-plus-circle blue"></span>
                            //        添加
                            //    </button>
                            <?php elseif (in_array($val['config_name'], ['membership_level'])): ?>
                                <?php $recommended_config = json_decode($val['config_value'],true)?>
                                <span style="width: 230px;left: 5px;top: 2px;" class="help-inline help-text col-xs-12 col-sm-7">
								    <span class="middle"><?php echo json_decode($val['config_remark'],true)['desc'] ?></span>
							    </span>
                            <br>
                                <table class="table tableNE">
                                    <th>等级名称</th>
                                    <th>对应积分</th>
                                    <th>折扣券</th>
                                    <th>最低额度</th>
                                    <th>数量</th>
                                    <th>满减券</th>
                                    <th>最低额度</th>
                                    <th>数量</th>
                                    <th></th>
                                    <th>会员折扣</th>
                                    <?php $num = 1; foreach ($recommended_config as $keyN => $valN): ?>
                                        <?php $num++;?>
                                        <tr class="level<?=$num?>">
                                            <td style="width: 30px" class="td"><input name="membership_level_key[]" type="text" class="col-xs-1" value="<?=$keyN?>" /></td>
                                            <td class="td"><input style="width: 150px" name="membership_level_val[]" type="text" class="col-xs-1" value="<?=$valN?>" /></td>
                                            <td class="td" style="background-color: #FFF8DC"><input style="width: 50px" name="coupon_discount_val[]" type="text" class="col-xs-1" value="<?=json_decode($val['config_remark'],true)['coupon'][$keyN]['discount']['val']?>" /></td>
                                            <td class="td" style="background-color: #FFF8DC"><input style="width: 50px" name="coupon_discount_quota[]" type="text" class="col-xs-1" value="<?=json_decode($val['config_remark'],true)['coupon'][$keyN]['discount']['quota']?>" /></td>
                                            <td class="td" style="background-color: #FFF8DC"><input style="width: 35px" name="coupon_discount_number[]" type="text" class="col-xs-1" value="<?=json_decode($val['config_remark'],true)['coupon'][$keyN]['discount']['number']?>" /></td>
                                            <td class="td" style="background-color: #F0F8FF"><input style="width: 50px" name="coupon_minus_val[]" type="text" class="col-xs-1" value="<?=json_decode($val['config_remark'],true)['coupon'][$keyN]['minus']['val']?>" /></td>
                                            <td class="td" style="background-color: #F0F8FF"><input style="width: 50px" name="coupon_minus_quota[]" type="text" class="col-xs-1" value="<?=json_decode($val['config_remark'],true)['coupon'][$keyN]['minus']['quota']?>" /></td>
                                            <td class="td" style="background-color: #F0F8FF"><input style="width: 35px" name="coupon_minus_number[]" type="text" class="col-xs-1" value="<?=json_decode($val['config_remark'],true)['coupon'][$keyN]['minus']['number']?>" /></td>
                                            <td class="td"><a onclick="deleteVal('level'+<?=$num?>)" id="off-ioc" class="select2-search-choice-close" tabindex="-1"></a></td>
                                            <td class="td">
                                                <input style="width: 60px" name="membership_desc[]" type="text" class="col-xs-1" value="<?=empty(json_decode($val['config_remark'],true)['list'][$keyN])?'':json_decode($val['config_remark'],true)['list'][$keyN]?>" />
                                            </td>
                                        </tr>
                                    <?php endforeach ?>
                                </table>
                                <button type="button" class="btn btn-white btn-primary btn-bold" style="border-bottom-width:1px;left: 688px;top: 1.5px;position: absolute;" onclick="addVal('level'+<?=$num?>,'tableNE')">
                                    <span class="ace-icon fa fa-plus-circle blue"></span>
                                    添加
                                </button>
                            <?php elseif (in_array($val['config_name'], ['old_membership_level'])): ?>
                                <?php $old_recommended_config = json_decode($val['config_value'],true);?>
                                <span style="width: 230px;left: 5px;top: 2px;" class="help-inline help-text col-xs-12 col-sm-7">
								    <span class="middle"><?=$val['config_remark']?></span>
							    </span>
                                <br>
                                <table class="table">
                                    <th>等级名称</th>
                                    <th>折扣券</th>
                                    <th>最低额度</th>
                                    <th>数量</th>
                                    <th>满减券</th>
                                    <th>最低额度</th>
                                    <th>数量</th>
                                    <th>会员折扣</th>
                                    <tr>
                                        <td style="width: 30px" class="td"><input name="old_user[key]" type="text" class="col-xs-1" value="<?=$old_recommended_config['key']?>" /></td>
                                        <td class="td" style="background-color: #FFF8DC"><input style="width: 50px" name="old_user[coupon_discount_val]" type="text" class="col-xs-1" value="<?=$old_recommended_config['coupon_discount_val']?>" /></td>
                                        <td class="td" style="background-color: #FFF8DC"><input style="width: 50px" name="old_user[coupon_discount_quota]" type="text" class="col-xs-1" value="<?=$old_recommended_config['coupon_discount_quota']?>" /></td>
                                        <td class="td" style="background-color: #FFF8DC"><input style="width: 35px" name="old_user[coupon_discount_number]" type="text" class="col-xs-1" value="<?=$old_recommended_config['coupon_discount_number']?>" /></td>
                                        <td class="td" style="background-color: #F0F8FF"><input style="width: 50px" name="old_user[coupon_minus_val]" type="text" class="col-xs-1" value="<?=$old_recommended_config['coupon_minus_val']?>" /></td>
                                        <td class="td" style="background-color: #F0F8FF"><input style="width: 50px" name="old_user[coupon_minus_quota]" type="text" class="col-xs-1" value="<?=$old_recommended_config['coupon_minus_quota']?>" /></td>
                                        <td class="td" style="background-color: #F0F8FF"><input style="width: 35px" name="old_user[coupon_minus_number]" type="text" class="col-xs-1" value="<?=$old_recommended_config['coupon_minus_number']?>" /></td>
                                        <td class="td">
                                            <input style="width: 60px" name="old_user[discount]" type="text" class="col-xs-1" value="<?=$old_recommended_config['discount']?>" />
                                        </td>
                                    </tr>
                                </table>
                            <?php elseif (in_array($val['config_name'], ['membership_rules','commission_rules'])): ?>
                                <div class="row">
                                    <div class="vspace-12-sm"></div>
                                    <div class="col-xs-12 col-sm-8">
                                        <div class="form-group">
                                            <div class="col-sm-8">
                                                <?=addons\VymDesen\common\widgets\ueditor\Ueditor::widget(['id'=>$val['config_name'],'ucontent'=> $val['config_value'],'options'=>['initialFrameWidth' => 550]])?>
                                            </div>
                                        </div>
                                        <div class="space-10"></div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <input style="width: 115px;" type="text" class="col-xs-1" name="<?php echo $val['config_name'] ?>"
                                       value="<?php echo $val['config_value'] ?>"/>
                                <span class="help-inline help-text col-xs-12 col-sm-7">
								<span class="middle"><?php echo $val['config_remark'] ?></span>
							</span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach ?>
                <?php if (in_array("system-config/update", $node) || $this->params['is_administrator_user']): ?>
                    <div class="clearfix form-actions">
                        <div class="col-md-offset-3 col-md-9">
                            <button class="btn btn-info" type="submit" id="goSubmit">
                                <i class="ace-icon fa fa-check bigger-110"></i>
                                提 交
                            </button>

                            &nbsp; &nbsp;
                            <button class="btn" type="reset">
                                <i class="ace-icon fa fa-undo bigger-110"></i>
                                重 置
                            </button>
                        </div>
                    </div>
                <?php endif; ?>
            </form>
        </div>
    </div>
    
    <script type="text/javascript">
        function deleteVal(value){
            $('.'+value).empty()
        }
        var num = 1;
        function addVal(valT,nameN){
            num++;
            var keyB = "membership_level_key[]"
            var valB = "membership_level_val[]"
            if (nameN == "tableOL"){
                keyB = "recommended_config_key[]"
                valB = "recommended_config_val[]"
            }
            var classN = (valT+num);
            var dom = '<tr class="'+classN+'">\n' +
                '                                    <td style="width: 30px" class="td"><input name='+keyB+' type="text" class="col-xs-1" value="" /></td>\n' +
                '                                    <td class="td"><input name='+valB+' type="text" class="col-xs-1" value="" /></td>\n' +
                '                                    <td class="td"><a onclick="deleteVal(\''+classN+'\')" id="off-ioc" class="select2-search-choice-close" tabindex="-1"></a></td>\n' +
                '                                </tr>';
            var domNew = '<tr class="'+classN+'">\n' +
                '        <td style="width: 30px" class="td"><input name='+keyB+' type="text" class="col-xs-1" value="" /></td>\n' +
                '        <td class="td"><input style="width: 150px" name='+valB+' type="text" class="col-xs-1" value="" /></td>\n' +
                '        <td class="td" style="background-color: #FFF8DC"><input style="width: 50px" name="coupon_discount_val[]" type="text" class="col-xs-1" value="" /></td>\n' +
                '        <td class="td" style="background-color: #FFF8DC"><input style="width: 50px" name="coupon_discount_quota[]" type="text" class="col-xs-1" value="" /></td>\n' +
                '        <td class="td" style="background-color: #FFF8DC"><input style="width: 35px" name="coupon_discount_number[]" type="text" class="col-xs-1" value="" /></td>\n' +
                '        <td class="td" style="background-color: #F0F8FF"><input style="width: 50px" name="coupon_minus_val[]" type="text" class="col-xs-1" value="" /></td>\n' +
                '        <td class="td" style="background-color: #F0F8FF"><input style="width: 50px" name="coupon_minus_quota[]" type="text" class="col-xs-1" value="" /></td>\n' +
                '        <td class="td" style="background-color: #F0F8FF"><input style="width: 35px" name="coupon_minus_number[]" type="text" class="col-xs-1" value="" /></td>' +
                '        <td class="td"><a onclick="deleteVal(\''+classN+'\')" id="off-ioc" class="select2-search-choice-close" tabindex="-1"></a></td>\n' +
                '        <td class="td">\n' +
                '            <input style="width: 60px" name="membership_desc[]" type="text" class="col-xs-1" value="" />\n' +
                '        </td>\n' +
                '    </tr>';
            if (nameN == "tableOL"){
                $("."+nameN).append(dom)
            }else {
                $("."+nameN).append(domNew)
            }
        }
        $(function () {
            // $('.checkMore').css({'width': '400px', "float": "left"}).select2({allowClear: true});

            $('#goSubmit').click(function () {
                var url = '<?php echo Url::to(["system-config/update"]) ?>';
                layer.msg('正在提交...', {icon: 16, time: 0});


                for (var i = 0; i < $(".checkMore").length; i++) {
                    var checkmore = new Array();
                    $(".checkMore:eq(" + i + ") option:selected").each(function () {
                        checkmore.push($(this).val());
                    });

                    $(".checkMore:eq(" + i + ")").next(".checkTrue").val(checkmore.join(','));
                }

                $.post(url, $('#systemform').serialize(), function (e) {
                    if (e.data.status) {
                        layer.alert(e.data.info, {icon: 1}, function (index) {
                            window.location.href = '<?php echo Url::to(["system-config/user"])?>';
                        });
                    } else {
                        layer.alert(e.data.info, {icon: 7});
                        return;
                    }
                }, 'json');
                return false;
            });
        });


    </script>

<?php $this->endBlock(); ?>