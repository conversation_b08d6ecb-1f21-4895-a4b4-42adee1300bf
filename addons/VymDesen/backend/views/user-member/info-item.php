<?php
use addons\VymDesen\common\components\DataHelper;
use common\helpers\MemberHelper;
use yii\helpers\Url;
$this->title = '控制台管理-用户管理-用户账户详情';

?>
<?php $this->beginBlock('content') ?>
<?php $node = \Yii::$app->session['auth']['node'];?>
<div class="row">
    <div class="col-xs-12">
        <div class="clearfix">
            <div class="pull-right tableTools-container"></div>
        </div>
        <!-- div.table-responsive -->
        <!-- div.dataTables_borderWrap -->
        <div>
            <div class="col-sm-10 col-sm-offset-1">
                <!-- #section:pages/invoice -->
                <div class="widget-box transparent">
                    <div class="widget-header widget-header-large">
                        <h3 class="widget-title grey lighter">
                            <i class="ace-icon fa fa-leaf green"></i>
                            用户账户详情
                        </h3>
                    </div>
                    <div class="widget-body">
                        <div class="widget-main padding-24">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="row">
                                        <div class="col-xs-11 label label-lg label-success arrowed-in arrowed-right">
                                            <b>所属用户基本信息</b>
                                        </div>
                                    </div>
                                    <div>
                                        <ul class="list-unstyled  spaced">
                                            <li>
                                                <i class="ace-icon fa fa-caret-right green"></i>用户账号：
                                                <b class="red"><?php echo $UserMemberRes['username']?></b>
                                            </li>
                                            <li>
                                                <i class="ace-icon fa fa-caret-right green"></i>用户邮箱：
                                                <b class="red"><?php echo $UserMemberRes['email']?></b>
                                            </li>
                                            <li>
                                                <i class="ace-icon fa fa-caret-right green"></i>用户昵称：
                                                <b class="red"><?php echo $UserMemberRes['uname']?></b>
                                            </li>
                                            <li>
                                                <i class="ace-icon fa fa-caret-right green"></i>联系QQ：
                                                <b class="red"><?php echo $UserMemberRes['qq']?></b>
                                            </li>
                                            <li>
                                                <i class="ace-icon fa fa-caret-right green"></i>上次登录时间：
                                                <b class="red"><?php echo date('Y-m-d H:i:s', $UserMemberRes['last_time']) ?></b>
                                            </li>
                                            <li>
                                                <i class="ace-icon fa fa-caret-right green"></i>上次登录IP：
                                                <b class="red"><?php echo $UserMemberRes['last_ip'] ?></b>
                                            </li>
                                            <li>
                                                <i class="ace-icon fa fa-caret-right green"></i>注册时间：
                                                <b class="red"><?php echo date('Y-m-d H:i:s', $UserMemberRes['reg_time']) ?></b>
                                            </li>
                                            <li>
                                                <i class="ace-icon fa fa-caret-right green"></i>客户评级分类：
                                                <b class="red">
                                                    <?php if( $UserMemberRes['user_rating'] == "B"): ?>
                                                        B类小客户
                                                    <?php elseif( $UserMemberRes['user_rating'] == "B+"): ?>
                                                        B类大客户
                                                    <?php elseif( $UserMemberRes['user_rating'] == "A"): ?>
                                                        A类小客户
                                                    <?php elseif( $UserMemberRes['user_rating'] == "A+"): ?>
                                                        A类大客户
                                                    <?php endif;?>
                                                </b>
                                                <?php if(in_array("user-member/seting-userrating", $node) || $this->params['is_administrator_user']):?>
                                                    &nbsp;&nbsp;<a class="btn btn-xs btn-primary" href="javascript:void(0);" rel="" id="seting_rating">更改客户评级分类</a>
                                                <?php endif;?>
                                            </li>
                                            <li>
                                                <i class="ace-icon fa fa-caret-right green"></i>所属客服：
                                                <b class="red"><?php echo $UserMemberRes['admin_name']; ?></b>
                                            </li>
                                        </ul>
                                    </div>
                                </div><!-- /.col -->
                                <div class="col-sm-6">
                                    <div class="row">
                                        <div class="col-xs-11 label label-lg label-info arrowed-in arrowed-right">
                                            <b>用户实名认证信息</b>
                                        </div>
                                    </div>
                                    <div>
                                        <ul class="list-unstyled spaced">

                                            <li>
                                                <i class="ace-icon fa fa-caret-right blue"></i>真实姓名：
                                                <b class="red"><?php echo $UserMemberRes['truename']?> </b>
                                            </li>
                                            <li>
                                                <i class="ace-icon fa fa-caret-right green"></i>联系电话：
                                                <b class="red"><?php echo $UserMemberRes['mobile']?></b>
                                            </li>
                                            <li>
                                                <i class="ace-icon fa fa-caret-right green"></i>固定电话：
                                                <b class="red"><?php echo $UserMemberRes['telephone']?></b>
                                            </li>

                                            <li>
                                                <i class="ace-icon fa fa-caret-right green"></i>证件号码：
                                                <b class="red"><?php echo $UserMemberRes['idcard']?></b>
                                            </li>
                                            <li>
                                                <i class="ace-icon fa fa-caret-right blue"></i>实名地址：
                                                <b class="red"><?php echo $UserMemberRes['province'] ?><?php echo $UserMemberRes['city'] ?><?php echo $UserMemberRes['prefecture'] ?>
                                                </b>
                                            </li>
                                            <li>
                                                <i class="ace-icon fa fa-caret-right green"></i>联系地址：
                                                <b class="red"><?php echo $UserMemberRes['address'] ?></b>
                                            </li>
                                            <li>
                                                <i class="ace-icon fa fa-caret-right green"></i>公司名称：
                                                <b class="red"><?php echo $UserMemberRes['company'] ?></b>
                                            </li>

                                        </ul>
                                    </div>
                                </div><!-- /.col -->
                            </div><!-- /.row -->

                            <div style="margin-bottom: 10px;">
                                <div style="margin-bottom: 10px;font-size: 16px;">用户信息操作：</div>
                                <table class="">
                                    <thead>
                                    <tr>
                                        <td colspan="4">
                                            <?php if(in_array("user-member/item", $node) || $this->params['is_administrator_user']):?>
                                                <a class="btn btn-sm btn-primary" href="<?php echo Url::to(['item','u_id'=>$UserMemberRes['u_id']])?>">修改会员资料</a>
                                            <?php endif;?>
                                            <?php if(in_array("user-member/assign-customerservice", $node) || $this->params['is_administrator_user']):?>
                                                <a class="btn btn-sm btn-primary" href="<?php echo Url::to(['assign-customerservice','u_id'=>$UserMemberRes['u_id']])?>">分配客服</a>
                                            <?php endif;?>
                                            <?php if(in_array("user-member/changemoney", $node) || $this->params['is_administrator_user']):?>
                                                <a class="btn btn-sm btn-primary" href="<?php echo Url::to(['changemoney','id'=>$UserMemberRes['u_id']])?>">用户款项处理</a>
                                            <?php endif;?>
                                            <?php if(in_array("user-member/paymentway", $node) || $this->params['is_administrator_user']):?>
                                                <a class="btn btn-sm btn-primary" href="<?php echo Url::to(['paymentway','id'=>$UserMemberRes['u_id']])?>">用户支付通道</a>
                                            <?php endif;?>

                                            <!--<a class="btn btn-sm btn-primary"   href="javascript:;" onclick="window.open('<?php /*echo Yii::$app->params['frontDomain'].Url::to(['login/do-alogin', 'id'=>$UserMemberRes['u_id']])*/?>', '_blank')">进入会员中心</a>-->

                                            <?php if(in_array("user-member/download-protocol", $node) || $this->params['is_administrator_user']):?>
                                                <?php if($UserMemberRes['idcard'] || $UserMemberRes['company'] || $UserMemberRes['bankcard'] ): ?>
                                                    <a class="btn btn-sm btn-primary" href="<?php echo Url::to(['download-protocol','id'=>$UserMemberRes['u_id']])?>" >下载协议</a>
                                                <?php else:?>
                                                    <a class="btn btn-sm btn-primary" href="<?php echo Url::to(['download-protocol','id'=>$UserMemberRes['u_id']])?>" disabled="disabled">下载协议</a>
                                                <?php endif;?>
                                            <?php endif;?>

                                        </td>
                                    </tr></thead>
                                </table>
                            </div>

                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="row">
                                        <div class="col-xs-11 label label-lg label-info arrowed-in arrowed-right">
                                            <b>用户充值消费信息</b>
                                        </div>
                                    </div>
                                    <div>
                                        <ul class="list-unstyled spaced">
                                            <li>
                                                <i class="ace-icon fa fa-caret-right blue"></i>账户余额：
                                                <b class="red"><?php echo $UserMemberRes['balance'] ?>&nbsp;元</b>
                                            </li>
                                            <li>
                                                <i class="ace-icon fa fa-caret-right blue"></i>消费总额：
                                                <b class="red"><?php echo $UserMemberRes['price_total'] ?>&nbsp;元</b>
                                            </li>
                                            <?php if(in_array("finance-manage/index", $node) || $this->params['is_administrator_user']):?>
                                                <li>
                                                    <i class="ace-icon fa fa-caret-right blue"></i>充值记录：
                                                    <b class="red"><a class="blue" href="<?php echo Url::to(['finance-manage/index', 'user_name' =>$UserMemberRes['email']?: $UserMemberRes['username'], 'trade_type' =>'recharge'])?>" >查看充值记录</a></b>
                                                </li>
                                                <li>
                                                    <i class="ace-icon fa fa-caret-right blue"></i>消费记录：
                                                    <b class="red"><a class="blue" href="<?php echo Url::to(['finance-manage/index','user_name'=>$UserMemberRes['email']?: $UserMemberRes['username'],'trade_type'=>'consume'])?>" >查看消费记录</a></b>
                                                </li>
                                            <?php endif;?>
                                        </ul>
                                    </div>
                                </div><!-- /.col -->

                                <div class="col-sm-6">
                                    <div class="row">
                                        <div class="col-xs-11 label label-lg label-success arrowed-in arrowed-right">
                                            <b>用户产品与订单及工单</b>
                                        </div>
                                    </div>
                                    <div>
                                        <ul class="list-unstyled  spaced">
                                            <?php if(in_array("member-pdt/index", $node) || $this->params['is_administrator_user']):?>
                                                <li>
                                                    <i class="ace-icon fa fa-caret-right green"></i>用户产品列表：
                                                    <b class="red" style="text-indent:2em">
                                                        <a class="blue" href="<?php echo Url::to(['member-pdt/','user_name'=>MemberHelper::userAccount($UserMemberRes)])?>" >查看用户产品列表</a>
                                                    </b>
                                                </li>
                                            <?php endif;?>
                                            <?php if(in_array("trade/index", $node) || $this->params['is_administrator_user']):?>
                                                <li>
                                                    <i class="ace-icon fa fa-caret-right green"></i>用户订单列表：
                                                    <b class="red"><a class="blue" href="<?php echo Url::to(['trade-manage/trade-list','order_user_email'=> MemberHelper::userAccount($UserMemberRes)])?>" >查看用户订单列表</a> </b>
                                                </li>
                                            <?php endif;?>
                                            <?php if(in_array("aws/index", $node) || $this->params['is_administrator_user']):?>
                                                <li>
                                                    <i class="ace-icon fa fa-caret-right green"></i>用户工单列表：
                                                    <b class="red"><a class="blue" href="<?php echo Url::to(['aws/','member_id'=>$UserMemberRes['u_id']])?>" >查看用户工单列表</a></b>
                                                </li>
                                            <?php endif;?>
                                        </ul>
                                    </div>
                                </div><!-- /.col -->
                            </div><!-- /.row -->
                            <div class="space"></div>
                            <div>
                                <div style="margin-bottom: 10px;font-size: 16px;">子账户列表</div>
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <tr>
                                        <th class="center">账户</th>
                                        <th>名称</th>
                                        <th>QQ</th>
                                        <th>电话</th>
                                        <th>最后登录时间</th>
                                        <th>最后登录IP</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php foreach ($SonAccountRes as $value2):?>
                                        <tr>
                                            <td><?php echo $value2['email']?></td>
                                            <td><?php echo $value2['truename'] ?></td>
                                            <td><?php echo $value2['qq'] ?></td>
                                            <td><?php echo $value2['mobile'] ?></td>
                                            <td><?php echo date("Y-m-d H:i:s",$value2['last_time']) ?></td>
                                            <td><?php echo $value2['last_ip'] ?></td>
                                        </tr>
                                    <?php endforeach;?>
                                    </tbody>
                                </table>
                            </div>
                            <div>
                                <div style="margin-bottom: 10px;font-size: 16px;">推广列表</div>
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <tr>
                                        <th class="center">账户</th>
                                        <th>名称</th>
                                        <th>QQ</th>
                                        <th>电话</th>
                                        <th>最后登录时间</th>
                                        <th>最后登录IP</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php foreach ($TuiAccountRes as $value3):?>
                                        <tr>
                                            <td><?php echo $value3['email']?></td>
                                            <td><?php echo $value3['truename'] ?></td>
                                            <td><?php echo $value3['qq'] ?></td>
                                            <td><?php echo $value3['mobile'] ?></td>
                                            <td><?php echo date("Y-m-d H:i:s",$value3['last_time']) ?></td>
                                            <td><?php echo $value3['last_ip'] ?></td>
                                        </tr>
                                    <?php endforeach;?>
                                    </tbody>
                                </table>
                            </div>
                            <div>
                                <div style="margin-bottom: 10px;font-size: 16px;">用户常用地址</div>
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <tr>
                                        <th class="center">联系人</th>
                                        <th>手机号</th>
                                        <th>地址</th>
                                        <th>邮编</th>
                                        <th></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php foreach ($UserAddressRes as $value1):?>
                                        <tr>
                                            <td><?php echo $value1['contact_name']?></td>
                                            <td><?php echo $value1['mobile'] ?></td>
                                            <td><?php echo $value1['province']."&nbsp;".$value1['city']."&nbsp;".$value1['area']."&nbsp;".$value1['details'] ?></td>
                                            <td><?php echo $value1['postcode'] ?></td>
                                            <td><?php if( $value1['is_default'] == "Y"): ?>默认地址 <?php endif;?></td>
                                        </tr>
                                    <?php endforeach;?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="space-6"></div>
                            <div class="row">
                                <div class="col-sm-7 pull-left">
                                    <button class="btn btn-danger btn-sm" style="width:120px" onclick="window.history.back()">
                                        <i class="ace-icon fa fa-reply icon-only"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="hr hr8 hr-double hr-dotted"></div>
                        </div>
                    </div>
                </div>

                <!-- /section:pages/invoice -->
            </div>
        </div>
    </div>
</div>
<style type="text/css">
    .form-horizontal {padding-left: 30px;}
    .font-size-12 {font-size: 12px;}
    .ny-form .form-group {margin-bottom: 12px;line-height: 30px;}
    .form-horizontal .form-group {margin-right: -15px;margin-left: -15px;}
    .ny-form .ny-control-label {float: left;}
    .ny-control-label {width: 160px;padding-right: 0;text-align: right;color: #808080;}
    .ny-form-control {width:100px;display: inline-block;white-space: nowrap;color: #555;background-color: #fff;background-image: none;outline: none;}
    .ny-number-container {float: left;line-height: 1;}
    .number-input-box {width: 131px;}
    .number-input-box {float: left;position: relative;width: 100px;border-radius: 2px;}
    .alert-warn, .alert-error, .alert-success {padding: 7px 22px 5px 37px;background: url(img/uc/tip_icon_warn_16.png) no-repeat 10px 10px #fff3e5;
        border: 1px solid #ff8800;border-radius: 2px;color: #ff8800;font-size: 12px;line-height: 2em;}
    .margin-bottom-20 {margin-bottom: 20px;}
</style>
<!-- 设置用户评级 -->
<div class="bootbox modal fade bootbox-prompt in" id="seting_rating_modal" tabindex="-1" role="dialog" style="padding-right: 17px;">
    <div class="modal-dialog" style="">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
                    <font style="vertical-align: inherit;">
                        <font style="vertical-align: inherit;">×</font>
                    </font>
                </button>
                <h4 class="modal-title">
                    <font style="vertical-align: inherit;"><font style="vertical-align: inherit;">更改用户评级分类</font></font></h4>
            </div>
            <div class="modal-body">
                <div class="bootbox-body">
                    <div class="alert-warn margin-bottom-20">
                        A类客户表示为：老客户<br/>
                        B类客户表示为：新客户<br/>
                    </div>
                    <form id="seting_rating_Form" action="" class="bootbox-form form-horizontal ny-form ny-tab-container font-size-12 tab-group-undefined">
                        <div class="form-group">
                            <div class="ny-control-label">
                                更改为：
                            </div>
                            <div class="col-xs-8 ny-form-control">
                                <div class="ny-number-container">
								<span class="number-input-box">
									<select name="user_rating"  class="ny-input-reset ny-number-input">
										<option value="">  ————请选择———— </option>
										<option value="B" <?php if($UserMemberRes['user_rating'] == 'B'):?>selected='selected' <?php endif;?> >  B类小客户 </option>
										<option value="B+" <?php if($UserMemberRes['user_rating'] == 'B+'):?>selected='selected' <?php endif;?> > B类大客户 </option>
										<option value="A" <?php if($UserMemberRes['user_rating'] == 'A'):?>selected='selected' <?php endif;?> >  A类小客户 </option>
										<option value="A+" <?php if($UserMemberRes['user_rating'] == 'A+'):?>selected='selected' <?php endif;?> >  A类大客户 </option>
									</select>
								</span>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="u_id" value="<?php echo $UserMemberRes['u_id']?>">
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
                    <font style="vertical-align: inherit;">
                        <font style="vertical-align: inherit;">取消</font></font>
                </button>
                <button data-bb-handler="confirm" data-last="Finish" type="button" class="btn btn-primary" id="do_seting_rating">
                    <font style="vertical-align: inherit;"><font style="vertical-align: inherit;">确定</font></font>
                </button>
            </div>
        </div>
    </div>
</div>
<!-- 设置用户评级end -->
<script>

    $('#seting_rating').click(function(){
        $('#seting_rating_modal').modal('show');
        return ;
    });
    //设置用户评级
    $('#do_seting_rating').click(function(){
        var _this = $(this);
        layer.confirm('确定要更改为新的分类吗？',{icon:3,
            btn: ['确定','取消'] //按钮
        }, function(){
            var url = "<?php echo Url::to(['user-member/seting-userrating']) ?>";
            layer.msg('操作中...', {icon:16, time:0});
            $.post(url, $('#seting_rating_Form').serialize(), function(data){
                if ( data['data']['status'] ) {
                    layer.alert(data['data']['info'], {icon:1}, function(index){
                        layer.msg('正在更新数据...', {icon:16, time:0});
                        location.reload();
                    });
                } else {
                    layer.alert(data['data']['info'], {icon:7});
                }
            },'json');
        }, function(){

        });
        return false;
    });

</script>
<?php $this->endBlock(); ?>
