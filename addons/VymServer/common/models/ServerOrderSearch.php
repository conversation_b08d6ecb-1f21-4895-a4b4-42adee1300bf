<?php

namespace addons\VymServer\common\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;

/**
 * ServerOrderSearch represents the model behind the search form of `addons\VymServer\common\models\ServerOrder`.
 */
class ServerOrderSearch extends ServerOrder
{
    /**
     * 关联表的搜索字段
     */
    public $order_status;
    public $order_pay_status;
    public $created_at_start;
    public $created_at_end;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'uid', 'merchant_id', 'store_id', 'original_id', 'general_id', 'payment_id', 'goods_id', 'product_id'], 'integer'],
            [['original_no', 'general_no', 'payment_no', 'remark', 'created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['cart_data'], 'safe'],
            // 新增的搜索字段
            [['order_status', 'order_pay_status', 'created_at_start', 'created_at_end'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ServerOrder::find()
                            ->alias('s')
                            ->joinWith([
                                'original o',
                                'general g',
                                'paymentFlow p',
                                'product pd',
                                'goods gd',
                                'merchant m',
                            ]);

        // add conditions that should always apply here
        $dataProvider = new ActiveDataProvider([
            'query'      => $query,
            'sort'       => [
                'defaultOrder' => ['id' => SORT_DESC],
            ],
            'pagination' => [
                'pageSize' => 20,
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            's.id'          => $this->id,
            's.uid'         => $this->uid,
            's.merchant_id' => $this->merchant_id,
            's.store_id'    => $this->store_id,
            's.original_id' => $this->original_id,
            's.general_id'  => $this->general_id,
            's.payment_id'  => $this->payment_id,
            's.goods_id'    => $this->goods_id,
            's.product_id'  => $this->product_id,
        ]);

        $query->andFilterWhere(['like', 's.original_no', $this->original_no])
              ->andFilterWhere(['like', 's.general_no', $this->general_no])
              ->andFilterWhere(['like', 's.payment_no', $this->payment_no])
              ->andFilterWhere(['like', 's.remark', $this->remark]);

        // 关联表的筛选条件
        if (!empty($this->order_status)) {
            $query->andWhere(['o.order_status' => $this->order_status]);
        }

        if (!empty($this->order_pay_status)) {
            $query->andWhere(['o.order_pay_status' => $this->order_pay_status]);
        }

        // 时间范围筛选
        if (!empty($this->created_at_start)) {
            $query->andWhere(['>=', 's.created_at', $this->created_at_start . ' 00:00:00']);
        }

        if (!empty($this->created_at_end)) {
            $query->andWhere(['<=', 's.created_at', $this->created_at_end . ' 23:59:59']);
        }

        return $dataProvider;
    }
}