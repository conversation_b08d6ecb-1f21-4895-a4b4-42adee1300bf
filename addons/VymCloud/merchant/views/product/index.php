<?php

use addons\VymCloud\common\models\CloudProduct;
use common\enums\AppEnum;
use common\helpers\ArrayHelper;
use common\helpers\Html;
use yii\grid\GridView;
use yii\widgets\ActiveForm;

/** @var yii\web\View $this */
/** @var yii\data\ActiveDataProvider $dataProvider */
/** @var common\models\base\SearchModel $searchModel */
/** @var array $clusterList */

$this->title                   = '业务产品';
$this->params['breadcrumbs'][] = $this->title;

?>
<!-- Main content -->
<div class="container-fluid">

    <!-- 搜索筛选区域 -->
    <div class="card search-card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-search mr-1"></i>
                筛选条件
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <?php $form = ActiveForm::begin([
                'method'                 => 'get',
                'enableClientValidation' => false,
                'enableAjaxValidation'   => false,
                'options'                => ['class' => 'form-row'],
                'fieldConfig'            => [
                    'template'     => '<div class="input-group-prepend">{label}</div>{input}{error}',
                    'labelOptions' => ['class' => 'col-form-label-sm input-group-text'],
                    'options'      => ['class' => 'input-group col-sm-12 col-md-6 col-lg-3 col-lg-2 mb-2'],
                ],
            ]); ?>
            <?php if (Yii::$app->id == AppEnum::BACKEND): ?>
                <?= $form->field($searchModel, 'merchant_id')->dropDownList(
                    ArrayHelper::map(Yii::$app->services->merchant->findAllByCondition([]), 'id', 'title'), 
                    ['prompt' => '选择商户']
                )->label('所属商户') ?>
            <?php endif; ?>

            <?= $form->field($searchModel, 'title')->textInput(['placeholder' => '请输入产品名称',])->label('产品名称') ?>
            <?= $form->field($searchModel, 'cluster_ids')->dropDownList($clusterList, ['prompt' => '选择集群',])->label('所属集群') ?>
            <?= $form->field($searchModel, 'product_st')->dropDownList(CloudProduct::getStatusMap(), ['prompt' => '选择状态',])->label('套餐状态') ?>


            <div class="col-sm-12 col-md-6 col-lg-3">
                <?= Html::submitButton('<i class="fas fa-search"></i> 搜索', ['class' => 'btn btn-primary btn-md mr-2',]) ?>
                <?= Html::a('<i class="fas fa-redo"></i> 重置', ['index'], ['class' => 'btn btn-outline-secondary btn-md',]) ?>
            </div>
            <?php ActiveForm::end(); ?>
        </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-list mr-1"></i>
                <?= Html::encode($this->title) ?>
            </h3>
            <div class="card-tools">
                <?= Html::a('<i class="fas fa-plus"></i> 新增产品', ['edit'], [
                    'class' => 'btn btn-primary btn-sm',
                ]) ?>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <?= GridView::widget([
                    'dataProvider' => $dataProvider,
                    'tableOptions' => [
                        'class' => 'table table-hover rf-table mb-0',
                    ],
                    'columns'      => [
                        [
                            'attribute'      => 'product_id',
                            'headerOptions'  => ['class' => 'text-left'],
                            'contentOptions' => ['class' => 'text-left'],
                        ],
                        [
                            'label'          => '所属商户',
                            'headerOptions'  => ['style' => 'width: 120px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'visible'        => Yii::$app->id == AppEnum::BACKEND, // 只在总后台显示
                            'value'          => function ($model) {
                                if ($model->merchant && $mId = $model->merchant->id) {
                                    if ($mTitle = $model->merchant->title) {
                                        return Html::tag('span', $mTitle, ['class' => 'badge badge-primary']);
                                    }
                                    return Html::tag('span', "ID:{$mId}", ['class' => 'badge badge-primary']);
                                }
                                return Html::tag('span', '平台直属', ['class' => 'badge badge-secondary']);
                            },
                        ],
                        [
                            'attribute'     => 'title',
                            'headerOptions' => ['style' => 'width: 200px;'],
                            'format'        => 'raw',
                            'value'         => function ($model) {
                                return Html::tag('span', Html::encode($model->title), [
                                    'class' => 'font-weight-medium',
                                ]);
                            },
                        ],
                        [
                            'attribute'     => 'cluster_ids',
                            'label'         => '所属集群',
                            'headerOptions' => ['style' => 'width: 150px;'],
                            'format'        => 'raw',
                            'value'         => function ($model) use ($clusterList) {
                                if (!$model->cluster_ids) {
                                    return '<span class="text-muted">-</span>';
                                }

                                $tags = '';
                                foreach (explode(',', $model->cluster_ids) as $clusterId) {
                                    $cluster = $clusterList[$clusterId] ?? "集群ID{$clusterId}";
                                    $tags    .= Html::tag('span', $cluster, [
                                            'class' => 'badge badge-info mr-1',
                                        ]) . ' ';
                                }
                                return $tags;
                            },
                        ],
                        [
                            'attribute' => 'config_basic',
                            'label'     => '基本配置',
                            'format'    => 'raw',
                            'value'     => function ($model) {
                                $basic     = $model->config_basic;
                                $cpu       = ArrayHelper::getValue($basic, 'cpu');
                                $mem       = ArrayHelper::getValue($basic, 'mem');
                                $disk_os   = ArrayHelper::getValue($basic, 'disk_os');
                                $disk_data = ArrayHelper::getValue($basic, 'disk_data');
                                $ip_num    = ArrayHelper::getValue($basic, 'ip_num');
                                $bw        = ArrayHelper::getValue($basic, 'bw');
                                $def       = ArrayHelper::getValue($basic, 'def');

                                $html = "CPU内存：CPU:{$cpu}H / 内存: {$mem}GB<br/>";
                                $html .= "磁盘配置：系统盘:{$disk_os}GB";
                                if ($disk_data) {
                                    $html .= " / 数据盘:{$disk_data}GB<br/>";
                                } else {
                                    $html .= "<br/>";
                                }
                                $html .= "网络配置：IP数:{$ip_num}个 / 带宽:{$bw}Mbps";
                                $def && $html .= " / 防御：{$def}Gbps";

                                return $html;

                                $specs   = [];
                                $specs[] = "CPU: {$basic['cpu']}核";
                                $specs[] = "内存: {$basic['mem']}G";
                                $specs[] = "系统盘: {$basic['disk_os']}G";
                                if ($basic['ip_num']) $specs[] = "IP: {$basic['ip_num']}个";
                                if ($basic['bw']) $specs[] = "带宽: {$basic['bw']}Mbs";

                                return Html::tag('small', implode('<br>', $specs), [
                                    'class' => 'text-muted',
                                ]);
                            },
                        ],
                        [
                            'attribute'     => 'config_price',
                            'label'         => '价格配置',
                            'headerOptions' => ['style' => 'width: 120px;'],
                            'format'        => 'raw',
                            'value'         => function ($model) {
                                $prices = [];
                                foreach ($model->config_price as $config) {
                                    $prices[] = "{$config['month']}个月: ¥{$config['price']}";
                                }
                                return Html::tag('small', implode('<br>', $prices), [
                                    'class' => 'text-success',
                                ]);
                            },
                        ],
                        [
                            'attribute'      => 'stock',
                            'headerOptions'  => ['style' => 'width: 80px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                if ($model->stock == 0) {
                                    return '<span class="badge badge-success">无限</span>';
                                } elseif ($model->stock == -1) {
                                    return '<span class="badge badge-danger">售完</span>';
                                } else {
                                    return '<span class="badge badge-warning">' . $model->stock . '</span>';
                                }
                            },
                        ],
                        [
                            'attribute'      => 'sales',
                            'headerOptions'  => ['style' => 'width: 80px;'],
                            'contentOptions' => ['class' => 'text-center'],
                        ],
                        [
                            'attribute'      => 'product_st',
                            'label'          => '状态',
                            'headerOptions'  => ['style' => 'width: 80px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                return CloudProduct::getStatusHtml($model->product_st);
                            },
                        ],
                        [
                            'header'         => '操作',
                            'headerOptions'  => ['style' => 'width: 120px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $buttons   = [];
                                $buttons[] = Html::a('<i class="fas fa-edit"></i> 编辑', ['edit', 'product_id' => $model->product_id], [
                                    'class' => 'btn btn-sm btn-outline-primary',
                                    'title' => '编辑',
                                ]);
                                $buttons[] = Html::a('<i class="fas fa-trash"></i> 删除', ['delete', 'product_id' => $model->product_id], [
                                    'class'        => 'btn btn-sm btn-outline-danger',
                                    'title'        => '删除',
                                    'data-confirm' => '确定要删除这个产品吗？',
                                ]);
                                return implode(' ', $buttons);
                            },
                        ],
                    ],
                ]); ?>
            </div>
        </div>
    </div>

</div>