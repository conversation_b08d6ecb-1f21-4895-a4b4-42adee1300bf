<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\LinkPager;

$this->title = '控制台管理-付款单未审核支付信息';
?>
<?php $this->beginBlock('content') ?>
<?php $node = \Yii::$app->session['auth']['node'];?>

<style type="text/css">
    .form-horizontal {padding-left: 30px;}
    .font-size-12 {font-size: 12px;}
    .ny-form .form-group {margin-bottom: 12px;line-height: 30px;}
    .form-horizontal .form-group {margin-right: -15px;margin-left: -15px;}
    .ny-form .ny-control-label {float: left;}
    .ny-control-label {width: 135px;padding-right: 0;text-align: right;color: #808080;}
    .ny-form-control {display: inline-block;white-space: nowrap;color: #555;background-color: #fff;background-image: none;outline: none;}
    .ny-number-container {float: left;line-height: 1;}
    .number-input-box {width: 131px;}
    .number-input-box {float: left;position: relative;width: 100px;border-radius: 2px;}
    .alert-warn, .alert-error, .alert-success {padding: 7px 22px 5px 37px;background: url(img/uc/tip_icon_warn_16.png) no-repeat 10px 10px #fff3e5;
        border: 1px solid #ff8800;border-radius: 2px;color: #ff8800;font-size: 12px;line-height: 2em;}
    .margin-bottom-20 {margin-bottom: 20px;}
</style>
<div class="row">    			
	<div class="clearfix">
		<div class="pull-right tableTools-container"></div>
	</div>  
</div>  		
<div class="row" style="margin-bottom: 10px;">
	<form action="">
		<input type="hidden" name="r" value="payment-flow/unaudited-list"/>
		<div class="col-xs-12">
			<div style="float: left;margin-right: 6px;">
				<input type="text" class="form-control search-query" name="payment_transaction_no" value='<?php echo Html::encode(Yii::$app->controller->get('payment_transaction_no')) ?>'  placeholder="支付交易号">
			</div>
			<div style="float: left;margin-right: 6px;">
				<input type="text" class="form-control search-query" name="flow_no" value='<?php echo Html::encode(Yii::$app->controller->get('flow_no')) ?>'  placeholder="流水号">
			</div>
			<div style="float: left;margin-right: 6px;">
				<input type="text" class="form-control search-query" name="user_email" value='<?php echo Html::encode(Yii::$app->controller->get('user_email')) ?>'  placeholder="用户账户">
			</div>
			<div style="float: left;margin-right: 6px;">
				<input type="text" class="form-control search-query" name="user_name" value='<?php echo Html::encode(Yii::$app->controller->get('user_name')) ?>'  placeholder="用户名称">
			</div>
			<div style="float: left;margin-right: 6px;">                        
				<select class="form-control" name='admin_id' id="form-field-select-1" >
					<option value="">销售名称</option>
					<?php foreach ($UserAdminRes as $value): ?>
					<option value="<?php echo $value['admin_id'] ?>" rel="<?php echo $value['uname'] ?>"   
					<?php if ($value['admin_id'] == Html::encode(Yii::$app->controller->get('admin_id')) ): ?>
					 selected='selected'
					<?php endif ?>
					>
					<?php echo $value['rename'] ?></option>
					<?php endforeach ?>
				</select>
			</div>

			<div style="float: left;margin-right: 6px;">
				 <input type="text" class="" name="start_time" id="start_time"  value='<?php echo Html::encode(Yii::$app->controller->get('start_time')) ?>'  placeholder="支付时间" readonly> 至
			</div>
			<div style="float: left;margin-right: 6px;">
				 <input type="text" class="" name="end_time" id="end_time" value='<?php echo Html::encode(Yii::$app->controller->get('end_time')) ?>'  placeholder="支付时间" readonly>
			</div>							    
			<div>
				<button type="submit" class="btn btn-white btn-primary btn-bold" style="margin-left:10px">
					<span class="ace-icon fa fa-search icon-on-right bigger-110"></span>
					搜索
				</button>				
				<button type="button" class="btn btn-white btn-success btn-bold" style="margin-left:10px;" onclick="javascript:location.href='<?php echo Url::to(['payment-flow/unaudited-list']) ?>'">
					<span class="ace-icon fa  fa-refresh"></span>
					刷新
				</button>
			</div>								
		</div>							
	</form>
</div>

<form action="" id='checkbox'>
	<table id="simple-table" width="100%" class="table table-bordered table-hover">
		<thead>
			<tr>
				<th></th>
				<th>客户昵称</th>
				<th>客户账户</th>
				<th>销售名称</th>
				<th>流水行为</th>
				<th>流水号</th>
				<th>应收金额</th>
				<th>实收金额</th>
				<th>金额相等</th>
				<th>支付方式</th>
				<th>支付平台</th>
				<th>支付渠道</th>
				<th>三方交易号</th>
				<th>创建时间</th>
				<th>交易时间</th>
				<th>交易备注</th>
				<th>操作</th>
				
			</tr>
		</thead>
		<tbody>
			<?php foreach ($PayinfoList as $key => $val): ?>
			<tr>
				<?php if($val['payment_flow_type'] == '用户充值'):?>
					<td></td>
				<?php else:?>
					<td><a href="javascript:;" class="look_finance_detail" payment_flow_no="<?php echo $val['payment_flow_no']?>"><i class="fa fa-eye"></i> 查看信息</a></td>
				<?php endif;?>
				<td><?php echo $val['user_name']?></td>
				<td><?php echo $val['user_email']?></td>
				<td><?php echo $val['admin_name']?></td>
				<td>
					<?php if($val['payment_flow_type'] == '用户充值'):?>
						<label class="label label-primary"><?php echo $val['payment_flow_type']?></label>
					<?php elseif($val['payment_flow_type'] == '挂账结算'):?>
						<label class="label label-warning"><?php echo $val['payment_flow_type']?></label>
					<?php else:?>
						<label class="label label-default"><?php echo $val['payment_flow_type']?></label>
					<?php endif;?>
				</td>
				<td>
					<?php if( $val['payment_mode'] == '在线支付'):?>					
						<a href="javascript:;" class="look_transaction_no" payment_flow_no="<?php echo $val['payment_flow_no']?>"><?php echo $val['payment_flow_no']?></a>
					<?php else:?>
						<?php echo $val['payment_flow_no']?>
					<?php endif;?>
				</td>
				<td><?php echo $val['payable_amount']?></td>				
				<td><?php echo $val['payment_amount']?></td>
				<td><?php echo $val['payable_amount'] != $val['payment_amount'] ? '<label class="label label-danger">金额不等</label>' : '-'?></td>
				<td>
					<?php if( $val['payment_mode'] == '线下支付'):?>
							<span class='label label-warning'>线下支付</span>
					<?php elseif( $val['payment_mode'] == '余额支付'):?>
							<span class='label label-default'>余额支付</span>
					<?php elseif( $val['payment_mode'] == '在线支付'):?>
							<span class='label label-purple'>在线支付</span>
					<?php elseif( $val['payment_mode'] == '后付款'):?>
							<span class='label label-primary'>挂账支付</span>
					<?php endif;?>
				</td>
				<td><?php echo $val['payment_platform']?></td>
				<td><?php echo $val['payment_method']?></td>
				<td><?php echo $val['payment_trans_no']?></td>
				<td><?php echo date('Y-m-d H:i:s', $val['payment_creattime']) ?></td>
				<td><?php echo $val['payment_successtime'] ? date('Y-m-d H:i:s', $val['payment_successtime']) : '-'; ?></td>
				<td><a href="javascript:;" class="look_payment_remarks" title="<?php echo $val['payment_amount_remarks']?>" remarkData="<?php echo $val['payment_amount_remarks']?>">查看备注</a></td>
				<td>
					<?php if($val['payment_audit_status'] == '未审核'):?>
						<?php if(in_array("payment-flow/payment-audit", $node) || $this->params['is_administrator_user']):?>
							<a class="payment_audit btn btn-sm btn-primary" payment_id="<?php echo $val['id'];?>" trans_no="<?php echo $val['payment_trans_no'];?>" payment_amount="<?php echo $val['payment_amount']?>" successtime="<?php echo $val['payment_successtime'] ? date('Y-m-d H:i:s', $val['payment_successtime']) : '';?>" href="javascript:;"><i class="fa fa-gavel"></i> 审核</a>	
						<?php endif;?>
					<?php endif;?>
				</td>
			</tr>				
		<?php endforeach;?>
		</tbody>
	</table>	
	<div class="row" style="margin-top: 10px;">
		<div class="col-xs-6">
			<div class="dataTables_info" id="dynamic-table_info" role="status" aria-live="polite">共有<?php echo $iCount;?>条记录</div>
		</div>
		<div class="col-xs-6 pagination1" style="text-align: right;">
			<?php 
				echo LinkPager::widget([
					'pagination' => $page,
					'firstPageLabel'=>"首页",
					'prevPageLabel'=>'上一页',
					'nextPageLabel'=>'下一页',
					'lastPageLabel'=>'末页',
				]);
			?>
		</div>
	</div>
</form>

<!-- 审核 start -->
<div class="bootbox modal fade bootbox-prompt in" id="payment_audit_modal" tabindex="-1" role="dialog" style="padding-right: 17px;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="bootbox-close-button close" data-dismiss="modal" aria-hidden="true">
                    <font style="vertical-align: inherit;">
                        <font style="vertical-align: inherit;">×</font>
                    </font>
                </button>
                <h4 class="modal-title">
                <font style="vertical-align: inherit;"><font style="vertical-align: inherit;">支付信息审核</font></font></h4>
            </div>
            <div class="modal-body">
                <div class="bootbox-body">
					<div class="alert-warn margin-bottom-20">
						* 请核对金额、信息是否正确
					</div>
                    <form id="payment_audit_form" action="" class="bootbox-form form-horizontal ny-form ny-tab-container font-size-12 tab-group-undefined">  
						<div class="form-group">
							<div class="ny-control-label">金额：</div>
							<div class="col-xs-8 ny-form-control payment_amount"></div>
						</div>
						<div class="form-group">
            				<div class="ny-control-label">是否准确：</div>
            				<div class="col-xs-8 ny-form-control">
            					<div class="ny-number-container">
								   <label>
										<input name="is_right" type="radio" value="Y" checked="" class="ace">
										<span class="lbl"> 支付信息正确</span>
									</label>
									&nbsp;&nbsp;&nbsp;
									<label>
										<input name="is_right" type="radio" value="N" class="ace">
										<span class="lbl"> 支付信息有误</span>
									</label>
            					</div>
            				</div>
            			</div>  
            			<div class="form-group">
            				<div class="ny-control-label">交易号：</div>
            				<div class="col-xs-8 ny-form-control">
            					<div class="ny-number-container">
            					   <span class="number-input-box" style="width: 400px;">
									<input type="text" name="payment_trans_no" value = "" class="ny-input-reset ny-number-input col-xs-8"></span>
            					</div>
            				</div>
            			</div>  
						<div class="form-group">
            				<div class="ny-control-label">收付款时间：</div>
            				<div class="col-xs-8 ny-form-control">
            					<div class="ny-number-container">
            					   <span class="number-input-box" style="width: 400px;">
								   <input type="text" name="payment_successtime" value = "" class="ny-input-reset ny-number-input col-xs-8"></span>
            					</div>
            				</div>
            			</div>
            			<div class="form-group">
            				<div class="ny-control-label">备注：</div>
            				<div class="col-xs-8 ny-form-control">
            					<div class="ny-number-container">
            					   <span class="number-input-box" style="width: 400px;">
								   <input type="text" name="reason_remark" value="" class="ny-input-reset ny-number-input col-xs-8"></span></div>
            				</div>
            			</div>
            			<input type="hidden" name="id" value=""> 
            		</form>            		
                </div>
            </div>
            <div class="modal-footer">
                <button data-bb-handler="cancel" data-dismiss="modal" type="button" class="btn btn-default">
                    <font style="vertical-align: inherit;">
                        <font style="vertical-align: inherit;">取消</font></font>
                </button>
                <button data-bb-handler="confirm" data-last="Finish" type="button" class="btn btn-primary" id="payment_audit_go">
                    <font style="vertical-align: inherit;"><font style="vertical-align: inherit;">确定</font></font>
                </button>
            </div>
        </div>
    </div>
</div>
<!--审核 end -->

<div id="payment_remarks" style="display:none;">
	<div class="remarkbox" style="padding:20px;">
	</div>
</div>
<!--查看提交给第三方的订单号 -->
<div id="look_transaction_no" style="display:none;">
	<div class="order_transaction_no" style="padding:20px;">
	</div>
</div><!-- end-->

<style type="text/css">
	.ipbox{width:200px!important;display:inline-block;}
	.copyData{height:34px;line-height:34px;display:inline-block;}
</style>

<textarea id="copy" style="opacity:0"></textarea>

<!-- page specific plugin scripts -->


<script type="text/javascript">
	var start = {
		elem: '#start_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas){
			 end.min = datas; //开始日选好后，重置结束日的最小日期
			 end.start = datas //将结束日的初始值设定为开始日
		}
	};
	var end = {
		elem: '#end_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas){
			 end.min = datas; //开始日选好后，重置结束日的最小日期
			 end.start = datas //将结束日的初始值设定为开始日
		}
	};
	laydate(start);
	laydate(end);
	laydate.skin('molv');
	
	//查看备注
	$(".look_payment_remarks").click(function() {
		var remarks = $(this).attr("remarkData");
		$(".remarkbox").html(remarks);
		
		layer.open({
			type: 1, 
			area: ['500px'],
			title:"" || "查看支付备注",
			content: $("#payment_remarks"),
			maxmin: true, //最大化按钮
			anim:3, //动画
			btn: ['确定', '关闭'],
			yes: function(index, layero) {
				layer.close(index);
			}
		});
	});
		
	//查看详情
	$(".look_finance_detail").click(function() {
		
		$(".finance_detail").remove();
		if($(this).hasClass("open")) {
			//已打开，需要关闭
			$(".look_finance_detail").removeClass("open");
		} else {
			$(".look_finance_detail").removeClass('open');
			$(this).addClass('open');
			
			var _this = $(this);
			
			//未打开
			var payment_flow_no = $(this).attr("payment_flow_no");
			
			var url = "<?php echo Url::to(["payment-flow/look-finance-detail"])?>";
			
			$.post(url, {"payment_flow_no":payment_flow_no}, function(e) {
				
				if(e.data.status == 0) {
					layer.alert(e.data.info, {icon:7});
					return false;
				}
				
				var info = e.data.data;
				
				var datahtml = '<tr class="finance_detail"><td colspan="17"><table class="table table-bordered">';
				
				$.each(info, function(i, n) {
					
					datahtml += '<tr><td>'+n.line_type+'</td><td>'+n.line_status+'</td><td>用户：'+n.user_account+'&nbsp;&nbsp;<a href="javascript:;" class="copyData" copydata="'+n.user_account+'">复制</a></td><td>昵称：'+n.user_nickname+'</td><td>销售：'+n.request_admin_name+'</td><td><a class="blue" href="'+"<?php echo Url::to(['pipe-line/pipe-line-detail'])?>"+'?line_id='+n.line_id+'" >查看工作任务</a></td><td><a class="blue" href="'+"<?php echo Url::to(['trade-manage/trade-detail'])?>"+'&order_id='+n.order_id+'" >查看订单详情</a></td></tr>';
					
					if(n.line_type == '新购业务') {
						$.each(n.local_data, function(s, k) {
							
							datahtml += '<tr><td></td>';
							
							datahtml += '<td>';
							if(k.ip) {
								datahtml += '<input type="text" readonly class="form-control ipbox" value="'+k.ip[0]+'">&nbsp;&nbsp;<a href="javascript:;" class="copyData" copydata="'+k.ip[0]+'">复制</a>';	
							} else {
								datahtml += '<label class="label label-primary">暂时无法确定IP地址</label>';
							}
							
							datahtml += '</td>';
							
							datahtml += '<td>付费周期：';
							switch(k.payment_cycle) {
								case "1":
									datahtml += '月付';
									break;
								case "3":
									datahtml += '季付';
									break;
								case "6":
									datahtml += '半年付';
									break;
								case "12":
									datahtml += '年付';
									break;
								default:
									datahtml += k.payment_cycle+'月付';
							}
							datahtml += '</td>';
							
							datahtml += '<td>单价：'+k.sell_price+'</td>';
							
							if(k.status == 1) {
								datahtml += '<td colspan="3"><label class="label label-success">'+k.info+'</label></td>';
							} else if(k.status == 0) {
								datahtml += '<td colspan="3"><label class="label label-danger">'+k.info+'</label></td>';
							} else {
								datahtml += '<td colspan="3"><label class="label label-warning">'+k.info+'</label></td>';
							}
							
							datahtml += '</tr>';
							
						});
						
						
						
					} else if(n.line_type == '续费业务') {
						
						$.each(n.local_data, function(s, k) {
							datahtml += '<tr><td></td>';
							
							datahtml += '<td>';
							if(k.ip) {
								datahtml += '<input type="text" readonly class="form-control ipbox" value="'+k.ip[0]+'">&nbsp;&nbsp;<a href="javascript:;" class="copyData" copydata="'+k.ip[0]+'">复制</a>';
							} else {
								datahtml += '<label class="label label-primary">暂时无法确定IP地址</label>';
							}
							
							datahtml += '</td>';
							
							datahtml += '<td>付费周期：';
							switch(k.payment_cycle) {
								case "1":
									datahtml += '月付';
									break;
								case "3":
									datahtml += '季付';
									break;
								case "6":
									datahtml += '半年付';
									break;
								case "12":
									datahtml += '年付';
									break;
								default:
									datahtml += k.payment_cycle+'月付';
							}
							datahtml += '</td>';
							
							datahtml += '<td>单价：'+k.sell_price+'</td>';
							datahtml += '<td>续费数量：'+k.renew_num+'次</td>';
							datahtml += '<td>到期时间：'+k.end_time+'</td>';
							
							if(k.status == 1) {
								datahtml += '<td colspan="3"><label class="label label-success">'+k.info+'</label></td>';
							} else if(k.status == 0) {
								datahtml += '<td colspan="3"><label class="label label-danger">'+k.info+'</label></td>';
							} else {
								datahtml += '<td colspan="3"><label class="label label-warning">'+k.info+'</label></td>';
							}
							
							datahtml += '</tr>';
						});
					} else if(n.line_type == '更换机器-补款') {
						
						$.each(n.local_data, function(s, k) {
							
							if(k.is_exchangedip == 1) {
								
								datahtml += '<tr><td></td><td>';
								
								//需要互换IP，所以IP没有变动，
								if(k.ip) {
									datahtml += '<input type="text" readonly class="form-control ipbox" value="'+k.ip[0]+'">&nbsp;&nbsp;<a href="javascript:;" class="copyData" copydata="'+k.ip[0]+'">复制</a>';
								} else {
									datahtml += '<label class="label label-primary">暂时无法确定IP地址</label>';
								}
								datahtml += '</td>';
								
							} else {
								//不互换IP，那么就要有IP变更
								datahtml += '<tr><td><label class="label label-danger">IP地址有变动</label></td>';
								
								datahtml += '<td><label class="label label-success">原IP地址</label><br/>';
								
								if(k.front_ip) {
									$.each(k.front_ip, function(a, b) {
										datahtml += '<input type="text" readonly class="form-control ipbox" value="'+b+'">&nbsp;&nbsp;<a href="javascript:;" class="copyData" copydata="'+b+'">复制</a>';
									});	
								} else {
									datahtml += '<label class="label label-primary">暂时无法确定IP地址</label>';
								}
								
								datahtml += '</td>';
								
								datahtml += '<td>';
								datahtml += '<label class="label label-warning">变更后IP地址</label><br/>';
								if(k.after_ip) {
									$.each(k.after_ip, function(a, b) {
										datahtml += '<input type="text" readonly class="form-control ipbox" value="'+b+'">&nbsp;&nbsp;<a href="javascript:;" class="copyData" copydata="'+b+'">复制</a>';
									});	
								} else {
									datahtml += '<label class="label label-primary">暂时无法确定IP地址</label>';
								}
								datahtml += '</td>';
							}
							
							if(k.old_sell_price != k.modify_sell_price) {
								datahtml += '<td><label class="label label-danger">续费价变动</label><br/>'+k.old_sell_price+' <i class="fa fa-arrow-right"></i> '+k.modify_sell_price+'</td>';
							} else {
								datahtml += '<td>单价：'+k.old_sell_price+'</td>';
							}
							

							if(k.status == 1) {
								datahtml += '<td colspan="3"><label class="label label-success">'+k.info+'</label></td>';
							} else if(k.status == 0) {
								datahtml += '<td colspan="3"><label class="label label-danger">'+k.info+'</label></td>';
							} else {
								datahtml += '<td colspan="3"><label class="label label-warning">'+k.info+'</label></td>';
							}
							
							datahtml += '</tr>';
						});
						
						
					} else if(n.line_type == '变更配置-补款') {
						$.each(n.local_data, function(s, k) {
							
							datahtml += '<tr>';
							
							var ipchange = false;
							
							//变更配置更新了IP，IP就单独放
							$.each(k.modify, function(a, b) {
								if(b.modify_type == 'ip') {
									ipchange = true;
									
									datahtml += '<td><label class="label label-danger">IP地址有变动</label></td>';
									
									datahtml += '<td><label class="label label-success">原IP地址</label><br/>';
									
									$.each(b.modify_data.old_config, function(c, d) {
										datahtml += '<input type="text" readonly class="form-control ipbox" value="'+d+'">&nbsp;&nbsp;<a href="javascript:;" class="copyData" copydata="'+d+'">复制</a>';
									});	
									
									datahtml += '</td>';
									
									datahtml += '<td><label class="label label-warning">变更后IP地址</label><br/>';
									
									$.each(b.modify_data.new_config, function(c, d) {
										datahtml += '<input type="text" readonly class="form-control ipbox" value="'+d+'">&nbsp;&nbsp;<a href="javascript:;" class="copyData" copydata="'+d+'">复制</a>';
									});	
									
									datahtml += '</td>';
								}
							});
							
							if(!ipchange) {
								datahtml += '<td>';
								if(k.ip) {
									datahtml += '<input type="text" readonly class="form-control ipbox" value="'+k.ip[0]+'">&nbsp;&nbsp;<a href="javascript:;" class="copyData" copydata="'+k.ip[0]+'">复制</a>';
								} else {
									datahtml += '<label class="label label-primary">暂时无法确定IP地址</label>';
								}
								
								datahtml += '</td>';
							}
							
							
							datahtml += '<td><label class="label label-danger">配置变动</label><br/>';
							$.each(k.modify, function(a, b) {
								if(b.modify_type != 'ip') {
									var changeTitle = '';
									
									if(b.modify_type == 'hdd') {
										changeTitle = '硬盘：';
									} else if(b.modify_type == 'ram') {
										changeTitle = '内存：';
									} else if(b.modify_type == 'requirement_bandwidth') {
										changeTitle = '要求带宽：';
									} else if(b.modify_type == 'configbandwidth') {
										changeTitle = '实际带宽：';
									} else if(b.modify_type == 'ipnumber') {
										changeTitle = '可用IP数：';
									} else if(b.modify_type == 'defense') {
										changeTitle = '防御流量：';
									} else if(b.modify_type == 'operatsystem') {
										changeTitle = '操作系统：';
									}

									datahtml += changeTitle+b.modify_data.old_config+' <i class="fa fa-arrow-right"></i> '+b.modify_data.new_config+'<br/>';
								}								
							});
							
							if(k.old_sell_price != k.modify_sell_price) {
								datahtml += '<td><label class="label label-danger">续费价变动</label><br/>'+k.old_sell_price+' <i class="fa fa-arrow-right"></i> '+k.modify_sell_price+'</td>';
							} else {
								datahtml += '<td>单价：'+k.old_sell_price+'</td>';
							}
							
							if(k.status == 1) {
								datahtml += '<td colspan="3"><label class="label label-success">'+k.info+'</label></td>';
							} else if(k.status == 0) {
								datahtml += '<td colspan="3"><label class="label label-danger">'+k.info+'</label></td>';
							} else {
								datahtml += '<td colspan="3"><label class="label label-warning">'+k.info+'</label></td>';
							}
							
							datahtml += '</tr>';
						});
					}
				});
				
				datahtml += '</table></td></tr>';
				
				_this.parents('tr').after(datahtml);
				
				$(".copyData").click(function() {
					
					var copydata = $(this).attr("copydata");
					$("#copy").val(copydata);
					$("#copy").select();

                    if(copyinfo()) {
                        layer.alert("复制完成", {icon:1});
                    } else {
                        layer.alert("复制失败，可能由于浏览器不支持<br/>请使用：chrome浏览器或360浏览器(极速模式)", {icon:7});
                    }
				});

			}, "json");
		}
	});

    function copyinfo(){
        try {
            var clipboard = new ClipboardJS('.copyData', {
                text: function() {
                    return $("#copy").val();
                }
            });
            clipboard.on('error', function(e) {
                return false;
            });
            return true;
        } catch {
            return false;
        }
    }

	//查看提交给第三方的订单号	
	$(".look_transaction_no").click(function() {
		
		var payment_flow_no = $(this).attr("payment_flow_no");		
		var url = "<?php echo Url::to(['payment-flow/look-transaction-no']);?>";
		
		$.post(url, {'payment_flow_no':payment_flow_no}, function(e){
			if ( e['data']['status'] ) {
				var data = e['data']['data'];
				if( data != null) {
					$(".order_transaction_no").html(data.trans_number);
				} else {
					$(".order_transaction_no").html('');
				}
				
				layer.open({
					type: 1, 
					area: ['500px'],
					title:"" || "查看支付交易号",
					content: $("#look_transaction_no"),
					maxmin: true, //最大化按钮
					anim:3, //动画
					btn: ['确定', '关闭'],
					yes: function(index, layero) {
						layer.close(index);
					}
				});
				
			} else {
				layer.alert(e['data']['info'], {icon:7});
			}
		},'json');		
		
	});
	
	//审核modal
	$('.payment_audit').click(function() {
		var id = $(this).attr('payment_id');
		var payment_amount = $(this).attr('payment_amount');
		var payment_trans_no = $(this).attr('trans_no');
		var payment_successtime = $(this).attr('successtime');
		$("[name='id']").val(id);
		$("[name='payment_trans_no']").val(payment_trans_no);
		$("[name='payment_successtime']").val(payment_successtime);
		$('.payment_amount').html(payment_amount); 

		var load = layer.load(2);
		$('#payment_audit_modal').modal('show');
		layer.closeAll('loading');	
	});
	
	//审核操作
	$('#payment_audit_go').click(function() {
		var _this = $(this);
		
		var is_right = $("input[name='is_right']:checked").val();
		
		if( is_right == 'Y') {
			var tips = '确定支付信息无误，要通过此笔流水吗？';
		} else {
			var tips = '确定支付信息有误，要驳回此笔流水吗？';
		}
		
		layer.confirm(tips, {icon:3, btn: ['确定','取消']}, function() {
			
			var url = "<?php echo Url::to(['payment-audit'])?>";
			layer.msg('Loading...', {icon:16, time:0});
		    $.post(url, $('#payment_audit_form').serialize(), function(e){
				if ( e['data']['status'] ){
					layer.alert(e['data']['info'], {icon:1}, function(index){				   		  
						location.reload();
					});
				}else{
					layer.alert(e['data']['info'], {icon:7});
				}
			},'json');
		}, function(){
		   
		});

		return false;
	});

</script>
<?php $this->endBlock(); ?>
