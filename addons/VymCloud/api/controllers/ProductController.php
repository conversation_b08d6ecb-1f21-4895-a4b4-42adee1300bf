<?php

namespace addons\VymCloud\api\controllers;

use addons\VymCloud\common\enums\AreaEnum;
use addons\VymCloud\common\enums\ClusterEnum;
use addons\VymCloud\common\enums\ProductEnum;
use addons\VymCloud\common\models\CloudArea;
use addons\VymCloud\common\models\CloudCluster;
use addons\VymCloud\common\models\CloudProduct;
use addons\VymCloud\common\models\CloudSystem;
use api\controllers\OnAuthController;
use common\helpers\ArrayHelper;
use common\helpers\ResultHelper;
use Yii;
use yii\db\Expression;

/**
 * 云服务器产品API控制器
 *
 * Class ProductController
 * @package addons\VymCloud\api\controllers
 */
class ProductController extends OnAuthController
{
    public $modelClass = '';

    /**
     * 不用进行登录验证的方法
     */
    protected $authOptional = ['index', 'recommend', 'detail', 'areas', 'clusters', 'systems', 'calculate-price'];

    /**
     * 获取地域列表
     */
    public function actionAreas()
    {
        try {
            $query = CloudArea::find()
                                 ->where(['area_st' => AreaEnum::STATUS_ENABLED])
                                 ->orderBy(['sort' => SORT_ASC]);
            $this->fillQuery($query);
            $areaList = $query->asArray()->all();
            return $areaList;

        } catch (\Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 获取集群列表
     */
    public function actionClusters()
    {
        try {
            $areaId = $this->get('area_id');

            if (!$areaId) {
                return ResultHelper::json(422, '地域ID不能为空');
            }

            $query = CloudCluster::find()
                                       ->where(['cluster_st' => ClusterEnum::STATUS_ENABLED])
                                       ->andWhere(['area_id' => $areaId])
                                       ->orderBy(['sort' => SORT_ASC]);

            $this->fillQuery($query);
            $clusterList = $query->asArray()->all();

            // 处理JSON字段
            $jsonFields  = ['config_detail', 'extend_detail'];
            $clusterList = ArrayHelper::fieldJsonToArray($clusterList, $jsonFields);

            return $clusterList;

        } catch (\Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 获取云产品列表
     */
    public function actionIndex()
    {
        try {
            $page      = max(1, (int)$this->get('page', 1));
            $pageSize  = max(1, min(100, (int)$this->get('pageSize', 20)));
            $search    = $this->get('search');
            $clusterId = $this->get('cluster_id');

            $query = CloudProduct::find()
                                 ->where(['product_st' => ProductEnum::STATUS_ENABLED]);
            $this->fillQuery($query);

            // 集群筛选
            if ($clusterId) {
                $query->andWhere(['like', 'cluster_ids', $clusterId]);
            }

            // 搜索条件
            if ($search) {
                $query->andWhere([
                    'or',
                    ['like', 'title', $search],
                    ['like', 'desc', $search],
                ]);
            }

            $query->orderBy('product_id ASC');

            // 分页
            $total  = $query->count();
            $offset = ($page - 1) * $pageSize;

            $productList = $query
                ->offset($offset)
                ->limit($pageSize)
                ->asArray()
                ->all();

            // 处理JSON字段和字段映射
            $jsonFields  = [
                'config_price', 'config_cpu', 'config_mem', 'config_disk_os',
                'config_disk_data', 'config_ip', 'config_def', 'config_bw',
                'config_basic', 'config_extend',
            ];
            $productList = ArrayHelper::fieldJsonToArray($productList, $jsonFields);

            // 统一字段名映射，兼容前端期望的格式
            foreach ($productList as &$product) {
                $product['id']          = $product['product_id']; // 添加id字段
                $product['name']        = $product['title'];      // 添加name字段
                $product['description'] = $product['desc'];       // 添加description字段
            }

            return [
                'items'    => $productList,
                'page'     => $page,
                'pageSize' => $pageSize,
                'total'    => $total,
            ];

        } catch (\Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 获取推荐云产品
     */
    public function actionRecommend()
    {
        try {
            $limit = min(20, (int)$this->get('limit', 12));

            $productList = CloudProduct::find()
                                       ->where([
                                           'product_st' => ProductEnum::STATUS_ENABLED,
                                           'recommend'  => ProductEnum::RECOMMEND_YES,
                                       ])
                                       ->orderBy('sales DESC, product_id DESC')
                                       ->limit($limit)
                                       ->asArray()
                                       ->all();

            // 处理JSON字段
            $jsonFields  = [
                'config_price', 'config_cpu', 'config_mem', 'config_disk_os',
                'config_disk_data', 'config_ip', 'config_def', 'config_bw',
                'config_basic', 'config_extend',
            ];
            $productList = ArrayHelper::fieldJsonToArray($productList, $jsonFields);

            return [
                'items' => $productList,
                'total' => count($productList),
            ];

        } catch (\Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 获取云产品详情
     */
    public function actionDetail()
    {
        try {
            $id        = (int)$this->get('id');
            $productId = (int)$this->get('product_id');

            // 兼容两种参数名
            $id = $id ?: $productId;

            if (!$id) {
                return ResultHelper::json(422, '产品ID不能为空');
            }

            $product = CloudProduct::find()
                                   ->with('cluster')
                                   ->where(['product_id' => $id])
                                   ->asArray()
                                   ->one();

            if (!$product) {
                return ResultHelper::json(404, '产品不存在');
            }

            // 处理JSON字段
            $jsonFields = [
                'config_price', 'config_cpu', 'config_mem', 'config_disk_os',
                'config_disk_data', 'config_ip', 'config_def', 'config_bw',
                'config_basic', 'config_extend',
            ];
            $product    = ArrayHelper::fieldJsonToArray([$product], $jsonFields)[0];
            $cluster    = ArrayHelper::getValue($product, 'cluster');
            if ($cluster) {
                //过滤一下
                $product['cluster'] = [
                    'id'      => ArrayHelper::getValue($cluster, 'id'),
                    'name'    => ArrayHelper::getValue($cluster, 'name'),
                    'area_id' => ArrayHelper::getValue($cluster, 'area_id'),
                ];
            }

            return $product;

        } catch (\Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 获取系统模板列表
     */
    public function actionSystems()
    {
        try {
            $clusterId = $this->get('cluster_id');
            $productId = $this->get('product_id');

            if (!$clusterId) {
                return ResultHelper::json(422, '集群ID不能为空');
            }
            $query = CloudSystem::find()->select(['id', 'pid', 'title', 'mark', 'type', 'state', 'sort'])
                                     ->where(['state' => CloudSystem::STATUS_ENABLED])
                                     ->andWhere(new Expression('FIND_IN_SET(:cluster_id, cluster_ids)', ['cluster_id' => $clusterId]))
                                     ->orderBy(['sort' => SORT_ASC]);
            $this->fillQuery($query);
            $systemList = $query->asArray()->all();


            $ret = ArrayHelper::itemsMerge($systemList, 0, 'id', 'pid', 'children');
            return $ret;
        } catch (\Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }


    /**
     * 计算产品价格
     *
     * @return array|mixed
     */
    public function actionCalculatePrice()
    {
        try {
            $params    = $this->post();
            $productId = ArrayHelper::getValue($params, 'product_id');

            $priceRe = Yii::$app->vymCloudService->product->calculatePrice($productId, $params);

            return ResultHelper::json(200, '价格计算成功', [
                "amount_total"    => ArrayHelper::getValue($priceRe, 'amount_total'),
                "amount_pay"      => ArrayHelper::getValue($priceRe, 'amount_pay'),
                "amount_discount" => ArrayHelper::getValue($priceRe, 'amount_discount'),
                "amount_coupon"   => ArrayHelper::getValue($priceRe, 'amount_coupon'),
                "renew_mod"       => ArrayHelper::getValue($priceRe, 'renew_mod'),
                "renew_price"     => ArrayHelper::getValue($priceRe, 'renew_price'),
                "renew_auto"      => ArrayHelper::getValue($priceRe, 'renew_auto'),
            ]);
        } catch (\Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 计算价格
     */
    public function actionCalculatePrice2()
    {
        try {
            $params    = $this->post();
            $productId = ArrayHelper::getValue($params, 'product_id');

            if (!$productId) {
                return ResultHelper::json(422, '请选择产品');
            }

            // 获取产品信息
            $product = CloudProduct::findOne($productId);
            if (!$product) {
                return ResultHelper::json(422, '产品不存在');
            }

            // 解析配置
            $priceConfig    = ArrayHelper::getValue($product, 'config_price');
            $basicConfig    = ArrayHelper::getValue($product, 'config_basic');
            $configCpu      = ArrayHelper::getValue($product, 'config_cpu');
            $configMem      = ArrayHelper::getValue($product, 'config_mem');
            $configDiskOs   = ArrayHelper::getValue($product, 'config_disk_os');
            $configDiskData = ArrayHelper::getValue($product, 'config_disk_data');
            $configIp       = ArrayHelper::getValue($product, 'config_ip');
            $configBw       = ArrayHelper::getValue($product, 'config_bw');

            // 获取参数
            $billingCycle = ArrayHelper::getValue($params, 'period', ArrayHelper::getValue($params, 'billing_cycle', 1));
            $quantity     = ArrayHelper::getValue($params, 'quantity', 1);

            // 查找对应周期的价格配置
            $currentPriceConfig = null;
            if (is_array($priceConfig)) {
                foreach ($priceConfig as $config) {
                    if (($config['cycle'] ?? $config['month'] ?? 1) == $billingCycle) {
                        $currentPriceConfig = $config;
                        break;
                    }
                }
            }

            // 默认价格配置
            if (!$currentPriceConfig) {
                $currentPriceConfig = ['month' => $billingCycle, 'price' => 300, 'discount' => 1];
            }

            // 计算基础价格
            $originalPrice = floatval($currentPriceConfig['price'] ?? 300);
            $discount      = floatval($currentPriceConfig['discount'] ?? 1);
            $basePrice     = $originalPrice;

            // 获取基础配置值
            $basicCpu      = intval($basicConfig['cpu'] ?? 1);
            $basicMem      = intval($basicConfig['mem'] ?? 1);
            $basicDiskOs   = intval($basicConfig['disk_os'] ?? 40);
            $basicDiskData = intval($basicConfig['disk_data'] ?? 0);
            $basicIp       = intval($basicConfig['ip_num'] ?? 1);
            $basicBw       = intval($basicConfig['bw'] ?? 1);

            // 获取当前配置值
            $currentCpu      = ArrayHelper::getValue($params, 'cpu', $basicCpu);
            $currentMem      = ArrayHelper::getValue($params, 'memory', ArrayHelper::getValue($params, 'mem', $basicMem));
            $currentDiskOs   = ArrayHelper::getValue($params, 'disk_os', $basicDiskOs);
            $currentDiskData = ArrayHelper::getValue($params, 'disk_data', $basicDiskData);
            $currentIp       = ArrayHelper::getValue($params, 'ip_count', ArrayHelper::getValue($params, 'ip_num', $basicIp));
            $currentBw       = ArrayHelper::getValue($params, 'bandwidth', ArrayHelper::getValue($params, 'bw', $basicBw));

            // 计算配置加成价格
            $configPrices     = [];
            $totalConfigPrice = 0;

            // CPU加成
            if ($currentCpu > $basicCpu && is_array($configCpu)) {
                $cpuUpgradePrice     = floatval($configCpu['upgrade_price'] ?? 0);
                $cpuPrice            = ($currentCpu - $basicCpu) * $cpuUpgradePrice * $billingCycle;
                $configPrices['cpu'] = $cpuPrice;
                $totalConfigPrice    += $cpuPrice;
            } else {
                $configPrices['cpu'] = 0;
            }

            // 内存加成
            if ($currentMem > $basicMem && is_array($configMem)) {
                $memUpgradePrice        = floatval($configMem['upgrade_price'] ?? 0);
                $memPrice               = ($currentMem - $basicMem) * $memUpgradePrice * $billingCycle;
                $configPrices['memory'] = $memPrice;
                $totalConfigPrice       += $memPrice;
            } else {
                $configPrices['memory'] = 0;
            }

            // 系统盘加成
            if ($currentDiskOs > $basicDiskOs && is_array($configDiskOs)) {
                $diskOsUpgradePrice      = floatval($configDiskOs['upgrade_price'] ?? 0);
                $diskOsPrice             = ($currentDiskOs - $basicDiskOs) * $diskOsUpgradePrice * $billingCycle;
                $configPrices['disk_os'] = $diskOsPrice;
                $totalConfigPrice        += $diskOsPrice;
            } else {
                $configPrices['disk_os'] = 0;
            }

            // 数据盘加成
            if ($currentDiskData > $basicDiskData && is_array($configDiskData)) {
                $diskDataUpgradePrice      = floatval($configDiskData['upgrade_price'] ?? 0);
                $diskDataPrice             = ($currentDiskData - $basicDiskData) * $diskDataUpgradePrice * $billingCycle;
                $configPrices['disk_data'] = $diskDataPrice;
                $totalConfigPrice          += $diskDataPrice;
            } else {
                $configPrices['disk_data'] = 0;
            }

            // IP加成
            if ($currentIp > $basicIp && is_array($configIp)) {
                $ipUpgradePrice     = floatval($configIp['upgrade_price'] ?? 0);
                $ipPrice            = ($currentIp - $basicIp) * $ipUpgradePrice * $billingCycle;
                $configPrices['ip'] = $ipPrice;
                $totalConfigPrice   += $ipPrice;
            } else {
                $configPrices['ip'] = 0;
            }

            // 带宽加成
            if ($currentBw > $basicBw && is_array($configBw)) {
                $bwUpgradePrice            = floatval($configBw['upgrade_price'] ?? 0);
                $bwPrice                   = ($currentBw - $basicBw) * $bwUpgradePrice * $billingCycle;
                $configPrices['bandwidth'] = $bwPrice;
                $totalConfigPrice          += $bwPrice;
            } else {
                $configPrices['bandwidth'] = 0;
            }

            // 计算总价
            $singlePrice  = $basePrice + $totalConfigPrice;
            $monthlyPrice = $singlePrice / $billingCycle;
            $totalPrice   = $singlePrice * $quantity;

            $result = [
                'monthly'   => $monthlyPrice,
                'total'     => $totalPrice,
                'breakdown' => [
                    'base'      => $basePrice,
                    'cpu'       => $configPrices['cpu'],
                    'memory'    => $configPrices['memory'],
                    'disk_os'   => $configPrices['disk_os'],
                    'disk_data' => $configPrices['disk_data'],
                    'ip'        => $configPrices['ip'],
                    'bandwidth' => $configPrices['bandwidth'],
                ],
                'quantity'  => $quantity,
                'period'    => $billingCycle,
            ];

            return $result;

        } catch (\Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }
} 