<?php

namespace api\controllers;

use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\models\MloginLog;
use addons\VymDesen\common\models\UserMember\UserMember;
use api\modules\v1\forms\EmailForm;
use api\modules\v1\forms\EmailRegisterForm;
use api\modules\v1\forms\EmailResetForm;
use api\modules\v1\forms\MobileLogin;
use common\helpers\CacheHelper;
use common\helpers\StringHelper;
use common\traits\MemberTrait;
use services\common\MailerService;
use Yii;
use yii\captcha\CaptchaValidator;
use yii\web\Response;
use yii\web\NotFoundHttpException;
use common\helpers\ResultHelper;
use common\helpers\ArrayHelper;
use common\models\member\Member;
use common\enums\MemberTypeEnum;
use api\modules\v1\forms\LoginForm;
use api\modules\v1\forms\RegisterForm;
use api\modules\v1\forms\SmsCodeForm;
use api\modules\v1\forms\UpPwdForm;
use common\models\extend\SmsLog;

/**
 * 用户认证API控制器
 *
 * Class UserController
 * @package api\controllers
 */
class UserController extends OnAuthController
{

    use MemberTrait;

    public $modelClass = '';

    /**
     * 不用进行登录验证的方法
     */
    protected $authOptional = ['captcha','login', 'phone-login', 'doregister', 'sms-code', 'emailcode', 'forgot-password', 'reset-password', 'refresh'];


    public function actions()
    {
        return [
            'captcha'    => [
                'class'           => 'yii\captcha\CaptchaAction',
                'width' => 120, // 验证码图片宽度
                'height' => 40, // 验证码图片高度
                'minLength' => 4, // 验证码最小长度
                'maxLength' => 4, // 验证码最大长度
                'transparent' => false, // 是否透明背景
                'backColor' => 0xFFFFFF, // 背景颜色
                'foreColor' => 0x000000, // 字体颜色
            ],
        ];
    }

    public function beforeAction($action)
    {
        //图形验证码跳过json格式化
        if($action->id == 'captcha'){
            Yii::$app->params['triggerBeforeSend'] = false;
        }
        return parent::beforeAction($action); // TODO: Change the autogenerated stub
    }


    /**
     * 用户登录 无账号自动注册
     *
     * @return array|mixed
     * @throws NotFoundHttpException
     * @throws \yii\base\Exception
     */
    public function actionPhoneLogin()
    {
        if (!Yii::$app->user->isGuest) {
            //已登陆了
            // 返回前端期望的格式
            $userInfo = [];
            return ResultHelper::json(200, 'success', $userInfo);
        }

        $model = new MobileLogin();
        $model->attributes = Yii::$app->request->post();
        $model->group = 'default'; // 设置默认组别

        $code = CacheHelper::getCache(CacheHelper::buildKey('sms_code', $model->mobile));
        if (!$code || $code != $model->code) {
            return ResultHelper::json(422, '验证码错误');
        }
        CacheHelper::delCache(CacheHelper::buildKey('sms_code', $model->mobile));

        $user = $model->getUser();
        if (!$user) {
            $account = trim($model->mobile);

            if (!$account)
                return ResultHelper::json(422, "请输入注册账号");

            $usrMember = UserMember::findOne(['mobile' => $account]);;

            //两边无账号注册 或者 desen账号已绑定过的则直接注册
            if (!$usrMember || $this->isBindMember($usrMember)) {
                if($this->phoneRegister($account)){
                    return $this->login($model);
                }
            }

            if ($usrMember->status == UserMember::STATUS_DISABLE) {
                if ($usrMember->pid >= 0) {
                    return ResultHelper::json(422, "该账户已禁用，请联系主账号");
                } else {
                    return ResultHelper::json(422, "该账户已锁定，请联系管理员");
                }
            }

            try {
                $this->registerByUserMember($usrMember, "", false);
            } catch (\Exception $e) {
                return ResultHelper::json(422, $e->getMessage());
            }
        }
        return $this->login($model);

    }

    /**
     * 用户登录
     *
     * @return array|mixed
     * @throws NotFoundHttpException
     * @throws \yii\base\Exception
     */
    public function actionLogin()
    {
        if (!Yii::$app->user->isGuest) {
            //已登陆了
            // 返回前端期望的格式
            $userInfo = [];
            return ResultHelper::json(200, 'success', $userInfo);
        }

        $model = new LoginForm();
        $model->attributes = Yii::$app->request->post();
        $model->group = 'default'; // 设置默认组别

        $user = $model->getUser();
        if ($user) {
            return $this->login($model);

        } else {
            $account = trim($model->username);
            $password = trim($model->password);
            if (!$account)
                return ResultHelper::json(422, "请输入登录账号");

            if (!$password)
                return ResultHelper::json(422, "请输入账号密码");

            $usrMember = null;
            if (DataHelper::checkemail($account)) {
                $usrMember = UserMember::findOne(['email' => $account]);
            } else {
                $usrMember = UserMember::findOne(['username' => $account]);
            }

            if (!$usrMember) {
                return ResultHelper::json(422, "该账户不存在");
            }

            if ($usrMember->status == UserMember::STATUS_DISABLE) {
                if ($usrMember->pid >= 0) {
                    return ResultHelper::json(422, "该账户已禁用，请联系主账号");
                } else {
                    return ResultHelper::json(422, "该账户已锁定，请联系管理员");
                }
            }

            if (!Yii::$app->security->validatePassword($password, $usrMember->pwd)) {
                return ResultHelper::json(422, "账户密码错误");
            }

            //判断一下老账号是否已绑定，如果已绑定，则判断为此商户下该账号不存在。
            //desen账号，在多商户去登录。
            if($this->isBindMember($usrMember)){
                return ResultHelper::json(422, "账号不存在");
            }



            try {
                $this->registerByUserMember($usrMember, $password);
            } catch (\Exception $e) {
                return ResultHelper::json(422, $e->getMessage());
            }
            return $this->login($model);
        }

    }

    private function login($model)
    {
        if ($model->login()) {
            // 记录登录行为
            Yii::$app->services->member->lastLogin($model->getUser());
            // 记录行为日志
            Yii::$app->services->actionLog->create('login', '登录', 0, [], false);

            $user = $model->getUser();
            $userMember = UserMember::findOne($user->ds_uid);
            $MloginLogModel = new MloginLog();
            $MloginLogModel->record($user->ds_uid, ArrayHelper::getValue($userMember,"username"), DataHelper::getIP(), 1, '登录成功');

            $tokenData = Yii::$app->services->apiAccessToken->getAccessToken($model->getUser(), $model->group);

            // 返回前端期望的格式
            $userInfo = [
                "id" => ArrayHelper::getValue($tokenData, 'member.id'),
                "realName" => ArrayHelper::getValue($tokenData, 'member.realname'),
                "username" => ArrayHelper::getValue($tokenData, 'member.username'),
                "roles" => ["super"],
                'accessToken' => $tokenData['access_token'],
                'refreshToken' => $tokenData['refresh_token'] ?? '',
                'expirationTime' => $tokenData['expiration_time'] ?? 7200,
            ];
            return ResultHelper::json(200, 'success', $userInfo);
        }
        return ResultHelper::json(422, $this->getError($model));
    }

    /**
     * 用户注册
     *
     * @return array|mixed
     * @throws \yii\base\Exception
     */
    private function phoneRegister($mobile)
    {

        /** @var \services\member\MemberService */
        $service = Yii::$app->services->member;
        $userQuery = $service->findBy(
            ['username' => $mobile],
            ['mobile' => $mobile, 'merchant_id' => Yii::$app->services->merchant->getNotNullId()]
        );
        if ($userQuery) {
            throw new \Exception("账号已注册");
        }


        $transaction = Yii::$app->db->beginTransaction();
        try {
            $member = new Member();
            $member->type = MemberTypeEnum::MEMBER;

            $member->password_hash = "";

            $member->mobile = $mobile;
            $member->username = $mobile;
            $member->merchant_id = Yii::$app->services->merchant->getNotNullId();
            $member->store_id = $this->getStoreId();
            $service->set($member);
            $member->save(false);
            $this->registerUserMember($member, "");
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
        return true;
    }

    public function actionDoregister()
    {
        $model = new EmailRegisterForm();
        $data = Yii::$app->request->post();
        if (!$model->load($data, '') || !$model->validate()) {
            return ResultHelper::json(422, $this->getError($model));
        }


        $emailCode = $model->code;
        $cacheValue = CacheHelper::getCache(CacheHelper::buildKey('email_code', $model->email));
        if (!$cacheValue || $emailCode != $cacheValue) {
            return ResultHelper::json(422, '邮箱验证码错误');
        }


        CacheHelper::delCache(CacheHelper::buildKey('email_code', $model->email));
        /** @var \services\member\MemberService */
        $service = Yii::$app->services->member;
        $userQuery = $service->findBy(
            ['username' => $model->username],
            ['email' => $model->email,'merchant_id' => Yii::$app->services->merchant->getNotNullId()]);

        // 登录逻辑
        $userMember = new Member();
        if ($userQuery) {
            //用户存在
            if ($userQuery->email == $model->email) {
                return ResultHelper::json(422, '邮箱已占用');
            } else {
                return ResultHelper::json(422, '账号已占用');
            }
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $hash = Yii::$app->security->generatePasswordHash($model->password);
            $userMember->type = MemberTypeEnum::MEMBER;
            $userMember->password_hash = $hash;
            $userMember->email = $model->email;
            $userMember->username = $model->username;
            $userMember->merchant_id = Yii::$app->services->merchant->getNotNullId();
            $userMember->store_id = $this->getStoreId();
            $service->set($userMember);
            $userMember->save(false);
            $this->registerUserMember($userMember, $model->password);
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            return ResultHelper::json(422, $e->getMessage());
        }

        return ResultHelper::json(200, '注册成功',["code" => 200]);
    }

    /**
     * 获取用户信息
     *
     * @return array
     */
    public function actionInfo()
    {
        try {
            $user = Yii::$app->user->identity;
            if (!$user) {
                return ResultHelper::json(401, '用户未登录');
            }

            $member = Member::find()
                ->where(['id' => $user->member_id])
                ->with(['account'])
                ->asArray()
                ->one();

            if (!$member) {
                return ResultHelper::json(404, '用户不存在');
            }

            // 格式化用户信息，符合前端期望的格式
            $userInfo = [
                'userId' => (string)$member['id'],
                'username' => $member['username'] ?? '',
                'realName' => $member['realname'] ?? $member['nickname'] ?? $member['username'] ?? '用户',
                'avatar' => $member['head_portrait'] ?? '/resources/img/avatar.jpg',
                'roles' => ['user'], // 基础用户角色
                'desc' => '用户', // 用户描述
                'homePath' => '/dashboard', // 首页路径
                'token' => $user->access_token ?? '', // 访问令牌
                // 额外的用户信息
                'email' => $member['email'] ?? '',
                'mobile' => $member['mobile'] ?? '',
                'nickname' => $member['nickname'] ?? '',
                'status' => $member['status'] ?? 1,
                'created_at' => $member['created_at'] ?? 0,
                'last_time' => $member['last_time'] ?? 0,
                'last_ip' => $member['last_ip'] ?? '',
            ];

            return ResultHelper::json(200, 'success', $userInfo);
        } catch (\Exception $e) {
            return ResultHelper::json(500, '获取用户信息失败：' . $e->getMessage());
        }
    }

    /**
     * 更新用户信息
     *
     * @return array
     */
    public
    function actionUpdateProfile()
    {
        try {
            $user = Yii::$app->user->identity;
            if (!$user) {
                return ResultHelper::json(401, '用户未登录');
            }

            $member = Member::findOne($user->member_id);
            if (!$member) {
                return ResultHelper::json(404, '用户不存在');
            }

            $data = Yii::$app->request->post();
            $allowedFields = [
                'nickname', 'realname', 'head_portrait', 'qq', 'email',
                'birthday', 'gender', 'address',
            ];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $member->$field = $data[$field];
                }
            }

            if (!$member->save()) {
                return ResultHelper::json(422, $this->getError($member));
            }

            return ResultHelper::json(200, '更新成功');
        } catch (\Exception $e) {
            return ResultHelper::json(500, '更新失败：' . $e->getMessage());
        }
    }

    /**
     * 修改密码
     *
     * @return array
     */
    public
    function actionChangePassword()
    {
        try {
            $user = Yii::$app->user->identity;
            if (!$user) {
                return ResultHelper::json(401, '用户未登录');
            }

            $member = Member::findOne($user->member_id);
            if (!$member) {
                return ResultHelper::json(404, '用户不存在');
            }

            $data = Yii::$app->request->post();
            $oldPassword = $data['old_password'] ?? '';
            $newPassword = $data['new_password'] ?? '';
            $confirmPassword = $data['confirm_password'] ?? '';

            if (empty($oldPassword) || empty($newPassword) || empty($confirmPassword)) {
                return ResultHelper::json(422, '密码不能为空');
            }

            if ($newPassword !== $confirmPassword) {
                return ResultHelper::json(422, '两次输入的密码不一致');
            }

            if (!$member->validatePassword($oldPassword)) {
                return ResultHelper::json(422, '原密码错误');
            }

            $member->password_hash = Yii::$app->security->generatePasswordHash($newPassword);
            if (!$member->save()) {
                return ResultHelper::json(422, $this->getError($member));
            }

            return ResultHelper::json(200, '密码修改成功');
        } catch (\Exception $e) {
            return ResultHelper::json(500, '密码修改失败：' . $e->getMessage());
        }
    }

    /**
     * 获取短信验证码
     *
     * @return array|mixed
     * @throws \yii\web\UnprocessableEntityHttpException
     */
    public
    function actionSmsCode()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $model = new SmsCodeForm();
        $model->attributes = Yii::$app->request->post();

        if (!$model->validate()) {
            return ResultHelper::json(422, $this->getError($model));
        }
//图形验证
//        $caprcha = new CaptchaValidator();
//        $caprcha->captchaAction = 'vym-general/default/captcha';
//
//        if (!$caprcha->validate($model->captcha)) {
//            return $this->errorMsg('验证码输入有误');
//        }
        $code = rand(1000, 9999);
        // 开发模式下返回测试验证码
        if (YII_DEBUG) {

            $log = new SmsLog();
            $log = $log->loadDefaultValues();
            $log->attributes = [
                'mobile' => $model->mobile,
                'code' => $code,
                'member_id' => 0,
                'usage' => $model->usage,
                'error_code' => 200,
                'error_msg' => '测试',
                'error_data' => '',
            ];
            $log->save();
            //保存验证码到session或缓存
            CacheHelper::setCache(CacheHelper::buildKey('sms_code', $model->mobile), $code);
            return ResultHelper::json(200, '发送成功', ['code' => $code]);
        }
        // 发送短信
        try {
            $model->send($code);
            //保存验证码到session或缓存
            CacheHelper::setCache(CacheHelper::buildKey('sms_code', $model->mobile), $code);
            return ResultHelper::json(200, '发送成功');
        } catch (\Throwable $e) {
            return ResultHelper::json(400, '发送失败');
        }
    }

    public function actionEmailcode()
    {
        $model = new EmailForm();
        $data = Yii::$app->request->post();
        if (!$model->load($data, '') || !$model->validate()) {
            return ResultHelper::json(422, $this->getError($model));
        }
        /** @var \services\common\MailerService $mailerService */
        // $mailerService = Yii::$app->mailer;
        $mailerService = new MailerService();
        $code = rand(10000, 99999);
//        if (YII_DEBUG) {
//            CacheHelper::setCache(CacheHelper::buildKey('email_code', $model->email), $code . '');
//            return ResultHelper::json(200, "发送成功", ['code' => $code]);
//        }
        $result = $mailerService->send(null, $model->email, '邮箱验证码', 'registerCode', [
            'code' => $code,
        ]);
        if (!$result) {
            return ResultHelper::json(422, "发送失败");
        }
        CacheHelper::setCache(CacheHelper::buildKey('email_code', $model->email), $code . '');
        return ResultHelper::json(200, "发送成功");
    }

    /**
     * 忘记密码
     *
     * @return array
     */
    public
    function actionForgotPassword()
    {
        $data = Yii::$app->request->post();
        $email = $data['email'] ?? '';
        $mobile = $data['mobile'] ?? '';

        if (empty($email) && empty($mobile)) {
            return ResultHelper::json(422, '请输入邮箱或手机号');
        }

        // 查找用户
        $query = Member::find()->where(['type' => MemberTypeEnum::MEMBER]);
        if ($email) {
            $query->andWhere(['email' => $email]);
        } else {
            $query->andWhere(['mobile' => $mobile]);
        }

        $member = $query->one();
        if (!$member) {
            return ResultHelper::json(422, '用户不存在');
        }

        // TODO: 实现发送重置密码邮件或短信的逻辑
        // 这里可以生成重置token，发送邮件或短信

        return ResultHelper::json(200, '重置密码链接已发送');
    }

    /**
     * 重置密码
     *
     * @return array|mixed
     * @throws \yii\base\Exception
     */
    public
    function actionResetPassword()
    {
        $model = new EmailResetForm();
        $model->attributes = Yii::$app->request->post();

        if (!$model->validate()) {
            return ResultHelper::json(422, $this->getError($model));
        }

        $emailCode = $model->code;
        $cacheValue = CacheHelper::getCache(CacheHelper::buildKey('email_code', $model->email));
        if (!$cacheValue || $emailCode != $cacheValue) {
            return ResultHelper::json(422, '邮箱验证码错误');
        }


        CacheHelper::delCache(CacheHelper::buildKey('email_code', $model->email));

        $member = $model->getUser();
        $member->password_hash = Yii::$app->security->generatePasswordHash($model->password);

        if (!$member->save()) {
            return ResultHelper::json(422, $this->getError($member));
        }

        return ResultHelper::json(200, "重置成功",["code" => 200]);


//        $tokenData = Yii::$app->services->apiAccessToken->getAccessToken($member, $model->group);
//
//        // 返回前端期望的格式
//        return [
//            'accessToken' => $tokenData['access_token'],
//            'refreshToken' => $tokenData['refresh_token'] ?? '',
//            'expirationTime' => $tokenData['expiration_time'] ?? 7200,
//        ];
    }

    /**
     * 退出登录
     *
     * @return array
     */
    public
    function actionLogout()
    {
        if (Yii::$app->services->apiAccessToken->disableByAccessToken(Yii::$app->user->identity->access_token)) {
            return ResultHelper::json(200, '退出成功');
        }

        return ResultHelper::json(422, '退出失败');
    }

    /**
     * 刷新访问令牌
     *
     * @return array
     */
    public function actionRefresh()
    {
        try {
            $refreshToken = $this->post('refresh_token');
            $group = $this->post('group', 'default');

            if (empty($refreshToken)) {
                return ResultHelper::json(422, '刷新令牌不能为空');
            }

            // 验证刷新令牌
            $model = \common\models\api\AccessToken::findIdentityByRefreshToken($refreshToken, $group);
            if (!$model) {
                return ResultHelper::json(401, '刷新令牌无效或已过期');
            }

            // 验证令牌有效期
            if (Yii::$app->params['user.refreshTokenValidity'] == true) {
                $timestamp = (int)substr($refreshToken, strrpos($refreshToken, '_') + 1);
                $expire = Yii::$app->params['user.refreshTokenExpire'];

                if ($timestamp + $expire <= time()) {
                    return ResultHelper::json(401, '刷新令牌已过期，请重新登录');
                }
            }

            // 获取用户信息
            $member = $model->member;
            if (!$member) {
                return ResultHelper::json(404, '用户不存在');
            }

            // 生成新的访问令牌
            $tokenData = Yii::$app->services->apiAccessToken->getAccessToken($member, $group);

            return [
                'accessToken' => $tokenData['access_token'],
                'refreshToken' => $tokenData['refresh_token'] ?? '',
                'expirationTime' => $tokenData['expiration_time'] ?? 7200,
            ];

        } catch (\Exception $e) {
            return ResultHelper::json(500, '刷新令牌失败：' . $e->getMessage());
        }
    }


    /**
     * 获取用户权限码
     *
     * @return array
     */
    public function actionCodes()
    {
        try {
            $user = Yii::$app->user->identity;
            if (!$user) {
                return ResultHelper::json(401, '用户未登录');
            }

            // 这里可以根据用户的角色返回对应的权限码
            // 目前返回一个基础的权限码列表
            $codes = [];

            return $codes;
        } catch (\Exception $e) {
            return ResultHelper::json(500, '获取权限码失败：' . $e->getMessage());
        }
    }

} 