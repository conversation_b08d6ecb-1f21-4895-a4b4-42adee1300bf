import type { VbenFormSchema } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { h } from 'vue';

import { DownOutlined } from '@vben/icons';

import { Button, Dropdown, Menu, MenuItem, Tag, Tooltip } from 'ant-design-vue';

import { CloudApi } from '#/api';
import { $t } from '#/locales';

/**
 * 服务器状态标签组件
 */
export function ServerStatusTag({ status }: { status: CloudApi.ServerStatus }) {
  const statusConfig = {
    [CloudApi.ServerStatus.CREATING]: {
      color: 'blue',
      text: '创建中',
    },
    [CloudApi.ServerStatus.RUNNING]: {
      color: 'green',
      text: '运行中',
    },
    [CloudApi.ServerStatus.REINSTALL]: {
      color: 'orange',
      text: '重装中',
    },
    [CloudApi.ServerStatus.STOPPED]: {
      color: 'default',
      text: '已停止',
    },
    [CloudApi.ServerStatus.DELETED]: {
      color: 'red',
      text: '已删除',
    },
  };

  const config =
    statusConfig[status] || statusConfig[CloudApi.ServerStatus.STOPPED];

  return h(Tag, { color: config.color }, { default: () => config.text });
}

/**
 * 电源状态标签组件
 */
export function PowerStatusTag({ status }: { status: CloudApi.PowerStatus }) {
  const statusConfig = {
    [CloudApi.PowerStatus.RUNNING]: {
      color: 'green',
      text: '运行中',
    },
    [CloudApi.PowerStatus.STOPPED]: {
      color: 'default',
      text: '已停止',
    },
    [CloudApi.PowerStatus.PAUSED]: {
      color: 'orange',
      text: '已暂停',
    },
    [CloudApi.PowerStatus.SUSPENDED]: {
      color: 'blue',
      text: '已挂起',
    },
    [CloudApi.PowerStatus.UNKNOWN]: {
      color: 'red',
      text: '未知',
    },
  };

  const config =
    statusConfig[status] || statusConfig[CloudApi.PowerStatus.UNKNOWN];
  return h(Tag, { color: config.color }, { default: () => config.text });
}

/**
 * 自动续费标签组件
 */
export function AutoRenewTag({ autoRenew }: { autoRenew: number }) {
  if (autoRenew === 1) {
    return h(
      Tooltip,
      { title: $t('server.tooltip.autoRenewEnabled') },
      {
        default: () =>
          h(
            Tag,
            { color: 'green' },
            { default: () => $t('server.fields.auto_renew') },
          ),
      },
    );
  }
  return h(
    Tooltip,
    { title: $t('server.tooltip.autoRenewDisabled') },
    {
      default: () =>
        h(
          Tag,
          { color: 'default' },
          { default: () => $t('server.fields.auto_renew') },
        ),
    },
  );
}

/**
 * 配置信息显示组件
 */
export function ConfigInfo2({ detail }: { detail?: any }) {
  if (!detail) return '暂无配置信息';

  // 检查值是否有效（不为空、undefined、null）
  const hasValue = (value: any) =>
    value !== undefined && value !== null && value !== '';

  const configItems = [];

  // CPU配置
  if (hasValue(detail.config?.cpu)) {
    configItems.push(
      h('div', `${$t('server.fields.cpu')}: ${detail.config.cpu}H`),
    );
  }

  // 内存配置
  if (hasValue(detail.config?.mem)) {
    configItems.push(
      h('div', `${$t('server.fields.memory')}: ${detail.config.mem}GB`),
    );
  }

  // 系统盘配置
  if (hasValue(detail.config?.disk_os)) {
    configItems.push(h('div', `系统盘: ${detail.config.disk_os}GB`));
  }

  // 数据盘配置 - 只有当值存在且大于0时才显示
  if (hasValue(detail.config?.disk_data) && detail.config.disk_data > 0) {
    configItems.push(h('div', `数据盘: ${detail.config.disk_data}GB`));
  }

  // 带宽配置
  const bw =
    detail.config?.bw_down || detail.config?.bw_in || detail.config?.bw;
  if (hasValue(bw)) {
    configItems.push(h('div', `带宽: ${bw}Mbps`));
  }

  // IP数配置
  if (hasValue(detail.config?.ip_num)) {
    configItems.push(h('div', `IP数: ${detail.config.ip_num}个`));
  }

  return configItems.length > 0 ? h('div', configItems) : '暂无配置信息';
}

/**
 * 服务器详细配置信息显示组件（用于订单等页面）
 */
export function ConfigInfo({ detail }: { detail?: any }) {
  if (!detail) return '暂无配置信息';

  // 检查值是否有效（不为空、undefined、null）
  const hasValue = (value: any) =>
    value !== undefined && value !== null && value !== '';

  const configItems = [];

  // CPU内存配置 - 只有当CPU和内存都有值时才显示
  if (hasValue(detail.config?.cpu) && hasValue(detail.config?.mem)) {
    configItems.push(
      h('div', { class: 'text-left' }, [
        h('span', { class: 'font-medium' }, 'CPU内存：'),
        h('span', `CPU：${detail.config.cpu}H`),
        h('span', ` / 内存：${detail.config.mem}GB`),
      ]),
    );
  }

  // 硬盘配置 - 只有当系统盘有值时才显示
  if (hasValue(detail.config?.disk_os)) {
    const diskItems = [
      h('span', { class: 'font-medium' }, '硬盘配置：'),
      h('span', `系统盘：${detail.config.disk_os}GB`),
    ];

    // 数据盘有值且大于0时才显示
    if (hasValue(detail.config?.disk_data) && detail.config.disk_data > 0) {
      diskItems.push(h('span', ` / 数据盘：${detail.config.disk_data}GB`));
    }

    configItems.push(h('div', { class: 'text-left' }, diskItems));
  }

  // 网络配置 - 只有当IP数或带宽有值时才显示
  const bw =
    detail.config?.bw || detail.config?.bw_down || detail.config?.bw_in;
  if (hasValue(detail.config?.ip_num) || hasValue(bw)) {
    const networkItems = [h('span', { class: 'font-medium' }, '网络配置：')];

    if (hasValue(detail.config?.ip_num)) {
      networkItems.push(h('span', `IP数：${detail.config.ip_num}个`));
    }

    if (hasValue(bw)) {
      if (hasValue(detail.config?.ip_num)) {
        networkItems.push(h('span', ` / 带宽：${bw}Mbps`));
      } else {
        networkItems.push(h('span', `带宽：${bw}Mbps`));
      }
    }

    // 防御有值且大于0时才显示
    if (hasValue(detail.config?.def) && detail.config.def > 0) {
      networkItems.push(h('span', ` / 防御：${detail.config.def}Gbps`));
    }

    configItems.push(h('div', { class: 'text-left' }, networkItems));
  }

  return configItems.length > 0
    ? h('div', { class: 'text-left text-sm' }, configItems)
    : '暂无配置信息';
}

/**
 * 服务器状态信息显示组件（合并服务器状态和电源状态为一列）
 */
export function ServerStatusInfo({
  serverStatus,
  powerStatus,
}: {
  powerStatus: CloudApi.PowerStatus;
  serverStatus: CloudApi.ServerStatus;
}) {
  // 当服务器状态为"创建中"时，只显示服务器状态，隐藏电源状态
  const isCreating = serverStatus === CloudApi.ServerStatus.CREATING;

  const statusItems = [
    // 服务器状态 - 始终显示
    h('div', { class: 'flex items-center space-x-2' }, [
      h('span', { class: 'font-medium text-gray-600' }, '状态:'),
      ServerStatusTag({ status: serverStatus }),
    ]),
  ];

  // 只有在非创建中状态时才显示电源状态
  if (!isCreating) {
    statusItems.push(
      // 电源状态
      h('div', { class: 'flex items-center space-x-2' }, [
        h('span', { class: 'font-medium text-gray-600' }, '电源:'),
        PowerStatusTag({ status: powerStatus }),
      ]),
    );
  }

  return h('div', { class: 'text-left text-sm space-y-1' }, statusItems);
}

/**
 * 到期时间显示组件
 */
export function ExpiryTime({
  endTime,
}: {
  endTime?: string;
  serverId: number;
}) {
  if (!endTime) {
    return h('span', { style: { color: '#999' } }, '永久');
  }

  const now = Date.now();
  const expiry = new Date(endTime).getTime();
  const isExpiring = expiry - now < 7 * 24 * 60 * 60 * 1000; // 7天内到期
  const isExpired = expiry < now;

  const formatTime = new Date(expiry).toLocaleDateString();

  if (isExpired) {
    return h(
      Tooltip,
      { title: '已到期' },
      {
        default: () => h('span', { style: { color: '#ff4d4f' } }, formatTime),
      },
    );
  }

  if (isExpiring) {
    return h(
      Tooltip,
      { title: $t('server.tooltip.expiringWarning') },
      {
        default: () => h('span', { style: { color: '#faad14' } }, formatTime),
      },
    );
  }

  return h('span', formatTime);
}

/**
 * 表格搜索表单配置
 */
export const searchFormSchema: VbenFormSchema[] = [
  {
    component: 'Select',
    fieldName: 'status_server',
    label: '实例状态',
    componentProps: {
      allowClear: true,
      filterOption: true,
      showSearch: true,
      placeholder: $t('server.search.statusPlaceholder'),
      options: [
        {
          label: $t('server.status.server.running'),
          value: CloudApi.ServerStatus.RUNNING,
        },
        {
          label: $t('server.status.server.stopped'),
          value: CloudApi.ServerStatus.STOPPED,
        },
        {
          label: '重装中',
          value: CloudApi.ServerStatus.REINSTALL,
        },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'status_power',
    label: $t('server.fields.status_power'),
    componentProps: {
      allowClear: true,
      filterOption: true,
      showSearch: true,
      placeholder: $t('server.search.statusPlaceholder'),
      options: [
        {
          label: '运行中',
          value: CloudApi.PowerStatus.RUNNING,
        },
        {
          label: '已停止',
          value: CloudApi.PowerStatus.STOPPED,
        },
        {
          label: '已暂停',
          value: CloudApi.PowerStatus.PAUSED,
        },
        {
          label: '已挂起',
          value: CloudApi.PowerStatus.SUSPENDED,
        },
      ],
    },
  },
  {
    component: 'Input',
    fieldName: 'ip',
    label: $t('server.fields.ip'),
    componentProps: {
      placeholder: $t('server.search.ipPlaceholder'),
    },
  },
  {
    component: 'Input',
    fieldName: 'name',
    label: $t('server.fields.name'),
    componentProps: {
      placeholder: $t('server.search.namePlaceholder'),
    },
  },
  {
    component: 'RangePicker',
    fieldName: 'createTime',
    label: $t('server.fields.created_at'),
  },
];

/**
 * 表格列配置
 */
export function useColumns(
  onActionClick: (params: any) => void,
): VxeGridProps['columns'] {
  return [
    {
      field: 'id',
      title: $t('server.fields.id'),
      width: 75,
    },
    {
      field: 'name',
      title: $t('server.fields.name'),
      minWidth: 150,
      slots: {
        default: ({ row }: { row: CloudApi.Server }) => {
          return h('div', [
            h('div', { style: { fontWeight: 'bold' } }, row.name || row.title),
          ]);
        },
      },
    },
    {
      field: 'node',
      title: $t('server.fields.node'),
      width: 120,
      slots: {
        default: ({ row }: { row: CloudApi.Server }) => {
          return h('div', { class: 'text-left' }, [
            h('div', `集群: ${row.cluster_id || ''}`),
            h('div', `节点: ${row.node || ''}`),
            h(
              'div',
              { style: { fontSize: '12px', color: '#999' } },
              `VMID: ${row.vmid}`,
            ),
          ]);
        },
      },
    },
    {
      field: 'ip',
      title: $t('server.fields.ip'),
      width: 180,
      slots: {
        default: ({ row }: { row: CloudApi.Server }) => {
          const ipInfo = [];

          // 只有当IP地址存在且不为空时才显示
          if (row.ip && row.ip !== 'null' && row.ip.trim() !== '') {
            ipInfo.push(h('div', `主IP: ${row.ip}`));
          }

          // 只有当MAC地址存在且不为空时才显示
          if (row.mac && row.mac !== 'null' && row.mac.trim() !== '') {
            ipInfo.push(
              h('div', { class: 'text-xs text-gray-500' }, `MAC: ${row.mac}`),
            );
          }

          // 如果IP和MAC都没有，显示暂无信息
          if (ipInfo.length === 0) {
            ipInfo.push(h('div', { class: 'text-gray-400' }, '暂无IP信息'));
          }

          return h('div', { class: 'text-left' }, ipInfo);
        },
      },
    },
    {
      field: 'detail',
      title: $t('server.fields.config'),
      minWidth: 250,
      slots: {
        default: ({ row }: { row: CloudApi.Server }) => {
          return ConfigInfo({ detail: row.detail });
        },
      },
    },
    {
      field: 'status',
      title: '状态信息',
      width: 120,
      slots: {
        default: ({ row }: { row: CloudApi.Server }) => {
          return ServerStatusInfo({
            serverStatus: row.status_server,
            powerStatus: row.status_power,
          });
        },
      },
    },
    {
      field: 'end_at',
      title: $t('server.fields.end_at'),
      width: 120,
      slots: {
        default: ({ row }: { row: CloudApi.Server }) => {
          return ExpiryTime({ endTime: row.end_at, serverId: row.id });
        },
      },
    },
    {
      field: 'created_at',
      title: $t('server.fields.created_at'),
      width: 160,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return cellValue ? new Date(cellValue).toLocaleString() : '';
      },
    },
    {
      title: $t('page.operation'),
      width: 140,
      fixed: 'right',
      slots: {
        default: ({ row }: { row: CloudApi.Server }) => {
          const actions = [];

          // 查看详情 - 始终显示
          actions.push(
            h(
              'a',
              {
                onClick: () => onActionClick({ code: 'view', row }),
                class: 'pl-3',
              },
              $t('server.actions.view'),
            ),
          );

          // 当服务器状态为"创建中"时，隐藏所有操作按钮
          const isCreating =
            row.status_server === CloudApi.ServerStatus.CREATING;

          if (!isCreating) {
            actions.push(
              h(
                Dropdown,
                {
                  placement: 'bottomRight',
                  class: 'p-1',
                },
                {
                  default: () =>
                    h(
                      Button,
                      {
                        type: 'link',
                        class: 'ant-dropdown-link text-blue-500',
                        onClick: (e: Event) => e.preventDefault(),
                      },
                      {
                        default: () => ['更多', h(DownOutlined)],
                      },
                    ),
                  overlay: () =>
                    h(
                      Menu,
                      {},
                      {
                        default: () => {
                          const menuItems = [];

                          // 电源操作 - 根据电源状态显示不同操作
                          if (
                            row.status_power === CloudApi.PowerStatus.STOPPED
                          ) {
                            menuItems.push(
                              h(
                                MenuItem,
                                { key: 'start' },
                                {
                                  default: () =>
                                    h(
                                      'a',
                                      {
                                        onClick: () =>
                                          onActionClick({ code: 'start', row }),
                                        style: { color: '#52c41a' },
                                      },
                                      $t('server.actions.start'),
                                    ),
                                },
                              ),
                            );
                          } else {
                            menuItems.push(
                              h(
                                MenuItem,
                                { key: 'stop' },
                                {
                                  default: () =>
                                    h(
                                      'a',
                                      {
                                        onClick: () =>
                                          onActionClick({ code: 'stop', row }),
                                        style: { color: '#ff4d4f' },
                                      },
                                      $t('server.actions.stop'),
                                    ),
                                },
                              ),
                            );
                          }

                          // 重启操作
                          menuItems.push(
                            h(
                              MenuItem,
                              { key: 'restart' },
                              {
                                default: () =>
                                  h(
                                    'a',
                                    {
                                      onClick: () =>
                                        onActionClick({ code: 'restart', row }),
                                      style: { color: '#faad14' },
                                    },
                                    $t('server.actions.restart'),
                                  ),
                              },
                            ),
                            h(
                              MenuItem,
                              { key: 'console' },
                              {
                                default: () =>
                                  h(
                                    'a',
                                    {
                                      onClick: () =>
                                        onActionClick({ code: 'console', row }),
                                    },
                                    $t('server.actions.console'),
                                  ),
                              },
                            ),
                            h(
                              MenuItem,
                              { key: 'reset_password' },
                              {
                                default: () =>
                                  h(
                                    'a',
                                    {
                                      onClick: () =>
                                        onActionClick({
                                          code: 'reset_password',
                                          row,
                                        }),
                                    },
                                    '重置密码',
                                  ),
                              },
                            ),
                            h(
                              MenuItem,
                              { key: 'reinstall' },
                              {
                                default: () =>
                                  h(
                                    'a',
                                    {
                                      onClick: () =>
                                        onActionClick({
                                          code: 'reinstall',
                                          row,
                                        }),
                                    },
                                    $t('server.actions.reinstall'),
                                  ),
                              },
                            ),
                          );

                          return menuItems;
                        },
                      },
                    ),
                },
              ),
            );
          }

          return h(
            'div',
            { class: 'flex flex-wrap gap-2 items-center' },
            actions,
          );
        },
      },
    },
  ];
}

/**
 * 重置密码表单配置
 */
export const resetPasswordFormSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'password',
    label: $t('server.modal.resetPassword.newPassword'),
    componentProps: {
      type: 'password',
      placeholder: $t('server.modal.resetPassword.passwordPlaceholder'),
    },
    help: $t('server.modal.resetPassword.autoGenerate'),
  },
];

/**
 * 重装系统表单配置
 */
export const reinstallFormSchema: VbenFormSchema[] = [
  {
    component: 'Select',
    fieldName: 'system_id',
    label: $t('server.modal.reinstall.selectSystem'),
    componentProps: {
      placeholder: $t('server.modal.reinstall.selectSystem'),
    },
  },
];
