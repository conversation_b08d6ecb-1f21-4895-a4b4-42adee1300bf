<?php

use addons\VymCloud\common\enums\OrderEnum;
use common\helpers\Html;
use common\helpers\ArrayHelper;
use yii\widgets\DetailView;

/** @var yii\web\View $this */
/** @var addons\VymCloud\common\models\CloudOrder $model */

$this->title                   = '订单详情 - ' . $model->order_no;
$this->params['breadcrumbs'][] = ['label' => '业务订单', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);

// 获取状态信息
$orderStatusMap   = OrderEnum::getMap();
$paymentStatusMap = OrderEnum::getPaymentStatus();
$orderTypeMap     = OrderEnum::getOrderType();

$orderStatus   = $orderStatusMap[$model->order_st] ?? '未知';
$paymentStatus = $paymentStatusMap[$model->payment_st] ?? '未知';
$orderType     = $orderTypeMap[$model->order_type] ?? '未知';

// 判断是否可以支付
$canPay = $model->order_st == OrderEnum::ORDER_STATUS_PENDING;
// 判断是否已支付
$isPaid = $model->order_st == OrderEnum::ORDER_STATUS_PAID || $model->order_st == OrderEnum::ORDER_STATUS_COMPLETED;
// 判断是否可以退款
$canRefund = $isPaid && $model->order_st != OrderEnum::ORDER_STATUS_REFUND;
// 判断是否有业务实例
$hasServer = $isPaid && $model->server_id && $model->server_id > 0;


// 解析JSON字段
$serverDetail = [];
$priceDetail = [];

if ($model->server_detail) {
    if (is_string($model->server_detail)) {
        $serverDetail = json_decode($model->server_detail, true) ?: [];
    } elseif (is_array($model->server_detail)) {
        $serverDetail = $model->server_detail;
    }
}

if ($model->price_detail) {
    if (is_string($model->price_detail)) {
        $priceDetail = json_decode($model->price_detail, true) ?: [];
    } elseif (is_array($model->price_detail)) {
        $priceDetail = $model->price_detail;
    }
}
?>

<div class="row">
    <div class="col-md-12">
        <div class="box">
            <div class="box-header with-border">
                <h3 class="box-title"><?= Html::encode($this->title) ?></h3>
                <div class="box-tools pull-right">
                    <?= Html::a('<i class="fa fa-arrow-left"></i> 返回列表', ['index'], ['class' => 'btn btn-default btn-sm']) ?>
                </div>
            </div>
            <div class="box-body">

                <!-- 订单状态和操作按钮 -->
                <div class="row">
                    <div class="col-md-8">
                        <h4>订单状态</h4>
                        <p>
                            <span class="label label-<?= OrderEnum::getColors()[$model->order_st] ?? 'default' ?> label-lg">
                                <?= $orderStatus ?>
                            </span>
                            <span class="label label-<?= $model->payment_st == OrderEnum::PAYMENT_STATUS_PAID ? 'success' : 'warning' ?> label-lg"
                                  style="margin-left: 10px;">
                                <?= $paymentStatus ?>
                            </span>
                        </p>
                    </div>
                    <div class="col-md-4 text-right">
                        <?php if ($canPay): ?>
                            <?= Html::a('<i class="fa fa-credit-card"></i> 余额支付', ['balance-pay', 'id' => $model->id], [
                                'class' => 'btn btn-success btn-sm',
                                'data'  => [
                                    'confirm' => '确认使用余额支付吗？',
                                    'method'  => 'post',
                                ],
                            ]) ?>
                            <?= Html::a('<i class="fa fa-globe"></i> 在线支付', ['online-pay', 'id' => $model->id], [
                                'class'  => 'btn btn-primary btn-sm',
                                'target' => '_blank',
                            ]) ?>
                        <?php endif; ?>

                        <?php if ($hasServer): ?>
                            <?= Html::a('<i class="fa fa-server"></i> 查看业务', ['/vym-cloud/server/view', 'id' => $model->server_id], [
                                'class' => 'btn btn-info btn-sm',
                                'title' => '查看服务器实例详情 #' . $model->server_id,
                            ]) ?>
                        <?php endif; ?>

                        <?php if ($canRefund): ?>
                            <?= Html::a('<i class="fa fa-undo"></i> 申请退款', ['refund', 'id' => $model->id], [
                                'class' => 'btn btn-warning btn-sm',
                                'data'  => [
                                    'confirm' => '确认申请退款吗？',
                                    'method'  => 'post',
                                ],
                            ]) ?>
                        <?php endif; ?>
                    </div>
                </div>

                <hr>

                <!-- 订单基本信息 -->
                <div class="row">
                    <div class="col-md-6">
                        <h4>订单信息</h4>
                        <?= DetailView::widget([
                            'model'      => $model,
                            'options'    => ['class' => 'table table-striped table-bordered detail-view'],
                            'attributes' => [
                                'id:text:订单ID',
                                'order_no:text:订单号',
                                'payment_no:text:支付单号',
                                [
                                    'attribute' => 'order_type',
                                    'label'     => '订单类型',
                                    'value'     => $orderType,
                                ],
                                [
                                    'attribute' => 'buy_cycle',
                                    'label'     => '购买周期',
                                    'value'     => $model->buy_cycle ? $model->buy_cycle . '个月' : '未设置',
                                ],
                                'uid:text:会员ID',
                                [
                                    'attribute' => 'created_at',
                                    'label'     => '创建时间',
                                    'value'     => $model->created_at ? date('Y-m-d H:i:s', strtotime($model->created_at)) : '未知',
                                ],
                                [
                                    'attribute' => 'payment_at',
                                    'label'     => '支付时间',
                                    'value'     => $model->payment_at ? date('Y-m-d H:i:s', strtotime($model->payment_at)) : '未支付',
                                ],
                            ],
                        ]) ?>
                    </div>

                    <div class="col-md-6">
                        <h4>金额信息</h4>
                        <?= DetailView::widget([
                            'model'      => $model,
                            'options'    => ['class' => 'table table-striped table-bordered detail-view'],
                            'attributes' => [
                                [
                                    'attribute' => 'amount_total',
                                    'label'     => '订单总金额',
                                    'value'     => '¥' . number_format($model->amount_total, 2),
                                ],
                                [
                                    'attribute' => 'amount_pay',
                                    'label'     => '应付金额',
                                    'value'     => '¥' . number_format($model->amount_pay, 2),
                                ],
                                [
                                    'attribute' => 'amount_discount',
                                    'label'     => '折扣金额',
                                    'value'     => '¥' . number_format($model->amount_discount, 2),
                                ],
                                [
                                    'attribute' => 'amount_coupon',
                                    'label'     => '优惠券金额',
                                    'value'     => '¥' . number_format($model->amount_coupon, 2),
                                ],
                                [
                                    'attribute' => 'payment_amount',
                                    'label'     => '实际支付金额',
                                    'value'     => '¥' . number_format($model->payment_amount, 2),
                                ],
                                [
                                    'attribute' => 'payment_id',
                                    'label'     => '支付方式ID',
                                    'value'     => $model->payment_id ?: '未设置',
                                ],
                            ],
                        ]) ?>
                    </div>
                </div>

                <!-- 配置详情 -->
                <div class="row">
                    <div class="col-md-6">
                        <h4>配置详情</h4>
                        <?php if (!empty($serverDetail)): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <?php
                                    // 定义字段的中文映射和格式化
                                    $fieldMap = [
                                        'cpu'         => 'CPU核心',
                                        'mem'         => '内存',
                                        'bw'          => '带宽',
                                        'def'         => '防御',
                                        'disk_os'     => '系统盘',
                                        'disk_data'   => '数据盘',
                                        'ip_num'      => 'IP数量',
                                        'server_name' => '服务器名称',
                                        'cluster_id'  => '集群ID',
                                        'product_id'  => '产品ID',
                                        'renew_mod'   => '续费周期',
                                        'renew_auto'  => '自动续费',
                                        'area_id'     => '地域ID',
                                        'system_id'   => '系统ID',
                                        'system_name' => '操作系统',
                                        'renew_price' => '续费价格',

                                        //续费
                                        'server_id' => '服务器ID',
                                        'new_end_time' => '续费后过期时间',
                                        'renew_months' => '续费周期',
                                        'total_amount' => '续费价格',
                                        'monthly_amount' => '续费单价',
                                        'current_end_time' => '当前过期时间',
                                    ];

                                    // 格式化数值的函数
                                    $formatValue = function ($key, $value) {
                                        if (is_array($value) || is_object($value)) {
                                            return '详见扩展配置';
                                        }

                                        switch ($key) {
                                            case 'cpu':
                                                return $value . '核';
                                            case 'mem':
                                                return $value . 'GB';
                                            case 'bw':
                                                return $value . 'Mbps';
                                            case 'def':
                                                return $value . 'G';
                                            case 'disk_os':
                                            case 'disk_data':
                                                return $value ? $value . 'GB' : '未配置';
                                            case 'ip_num':
                                                return $value . '个';
                                            case 'renew_months':
                                            case 'renew_mod':
                                                return $value . '个月';
                                            case 'renew_auto':
                                                return $value ? '是' : '否';
                                            case 'total_amount':
                                            case 'monthly_amount':
                                            case 'renew_price':
                                                return '¥' . number_format($value, 2);
                                            default:
                                                return Html::encode($value);
                                        }
                                    };

                                    // 优先显示config中的配置
                                    $config = ArrayHelper::getValue($serverDetail, 'config', []);
                                    if (!empty($config)) {
                                        foreach ($fieldMap as $key => $label) {
                                            $value = ArrayHelper::getValue($config, $key);
                                            if ($value !== null && !is_array($value) && !is_object($value)) {
                                                ?>
                                                <tr>
                                                    <td width="120"><strong><?= $label ?></strong></td>
                                                    <td><?= $formatValue($key, $value) ?></td>
                                                </tr>
                                                <?php
                                            }
                                        }
                                    }

                                    // 显示server_detail根级别的字段
                                    foreach ($fieldMap as $key => $label) {
                                        $value = ArrayHelper::getValue($serverDetail, $key);
                                        if ($value !== null && !is_array($value) && !is_object($value) && $key !== 'config') {
                                            ?>
                                            <tr>
                                                <td width="120"><strong><?= $label ?></strong></td>
                                                <td><?= $formatValue($key, $value) ?></td>
                                            </tr>
                                            <?php
                                        }
                                    }

                                    // 显示其他未映射的字段（排除特殊字段）
                                    foreach ($serverDetail as $key => $value) {
                                        if (!isset($fieldMap[$key]) && !is_array($value) && !is_object($value) &&
                                            !in_array($key, ['config', 'extend'])) {
                                            ?>
                                            <tr>
                                                <td width="120"><strong><?= Html::encode($key) ?></strong></td>
                                                <td><?= Html::encode($value) ?></td>
                                            </tr>
                                            <?php
                                        }
                                    }
                                    ?>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">暂无配置详情</p>
                        <?php endif; ?>
                    </div>

                    <div class="col-md-6">
                        <h4>扩展配置</h4>
                        <?php
                        // 获取扩展配置数据
                        $extendData = ArrayHelper::getValue($serverDetail, 'config.extend', []);
                        if (empty($extendData)) {
                            $extendData = ArrayHelper::getValue($serverDetail, 'extend', []);
                        }

                        if (!empty($extendData)): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <?php
                                    if (is_string($extendData)) {
                                        $extendData = json_decode($extendData, true) ?: [];
                                    }

                                    if (is_array($extendData)):
                                        // 定义扩展配置的中文映射
                                        $extendFieldMap = [
                                            'cpu_numa'       => 'CPU NUMA',
                                            'cpu_type'       => 'CPU类型',
                                            'disk_ssd'       => 'SSD硬盘',
                                            'disk_type'      => '硬盘类型',
                                            'disk_cache'     => '硬盘缓存',
                                            'disk_scsihw'    => 'SCSI硬件',
                                            'disk_iothread'  => 'IO线程',
                                            'io_ops_rd'      => '读取IOPS',
                                            'io_ops_wr'      => '写入IOPS',
                                            'io_ops_rd_max'  => '最大读取IOPS',
                                            'io_ops_wr_max'  => '最大写入IOPS',
                                            'io_mbps_rd'     => '读取带宽(MB/s)',
                                            'io_mbps_wr'     => '写入带宽(MB/s)',
                                            'io_mbps_rd_max' => '最大读取带宽(MB/s)',
                                            'io_mbps_wr_max' => '最大写入带宽(MB/s)',
                                            'net_queues'     => '网络队列',
                                            'net_firewall'   => '防火墙',
                                            'net_pri_name'   => '内网网桥',
                                            'net_pub_name'   => '公网网桥',
                                            'basic_agent'    => 'QEMU Agent',
                                            'basic_bios'     => 'BIOS类型',
                                            'basic_machine'  => '机器类型',
                                        ];

                                        foreach ($extendData as $key => $value):
                                            // 跳过一些内部字段
                                            if (in_array($key, ['extend', 'price_detail'])) continue;

                                            $label = ArrayHelper::getValue($extendFieldMap, $key, $key);
                                            $displayValue = $value;

                                            // 特殊值处理
                                            if (in_array($key, ['basic_agent', 'net_firewall', 'disk_iothread'])) {
                                                $displayValue = $value ? '启用' : '禁用';
                                            } elseif (is_array($value)) {
                                                $displayValue = json_encode($value, JSON_UNESCAPED_UNICODE);
                                            }
                                            ?>
                                            <tr>
                                                <td width="120"><strong><?= Html::encode($label) ?></strong></td>
                                                <td><?= Html::encode($displayValue) ?></td>
                                            </tr>
                                        <?php
                                        endforeach;
                                    endif;
                                    ?>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">暂无扩展配置</p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 价格详情 -->
                <?php if (!empty($priceDetail)): ?>
                    <div class="row">
                        <div class="col-md-12">
                            <h4>费用明细</h4>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <tr>
                                        <th width="150">配置项目</th>
                                        <th width="100">当前配置</th>
                                        <th width="100">升级数量</th>
                                        <th width="100">升级单价</th>
                                        <th width="100">升级总价</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php
                                    // 定义配置项的中文映射
                                    $configMap = [
                                        'cpu'       => 'CPU核心',
                                        'mem'       => '内存',
                                        'bw'        => '带宽',
                                        'def'       => '防御',
                                        'disk_os'   => '系统盘',
                                        'disk_data' => '数据盘',
                                        'ip_num'    => 'IP数量',
                                    ];

                                    $totalUpgrade = 0; // 总升级费用

                                    // 获取升级结果数据
                                    $upgradeResults = ArrayHelper::getValue($priceDetail, 'upgrade_results', []);

                                    if (!empty($upgradeResults)):
                                        foreach ($upgradeResults as $key => $upgradeData):
                                            if (!is_array($upgradeData)) continue;

                                            $title = ArrayHelper::getValue($upgradeData, 'title', $configMap[$key] ?? $key);
                                            $unit = ArrayHelper::getValue($upgradeData, 'unit', '');
                                            $upgradeNum = ArrayHelper::getValue($upgradeData, 'upgrade_num', 0);
                                            $upgradePrice = ArrayHelper::getValue($upgradeData, 'upgrade_price', 0);
                                            $upgradeTotal = $upgradeNum * $upgradePrice;
                                            $totalUpgrade += $upgradeTotal;

                                            // 获取基础配置
                                            $baseConfig = ArrayHelper::getValue($serverDetail, "config.{$key}",
                                                         ArrayHelper::getValue($serverDetail, $key, 0));
                                            ?>
                                            <tr>
                                                <td><strong><?= Html::encode($title) ?></strong></td>
                                                <td><?= Html::encode($baseConfig . $unit) ?></td>
                                                <td><?= $upgradeNum > 0 ? '+' . $upgradeNum : '无升级' ?></td>
                                                <td><?= $upgradePrice > 0 ? '¥' . number_format($upgradePrice, 2) : '免费' ?></td>
                                                <td><?= $upgradeTotal > 0 ? '¥' . number_format($upgradeTotal, 2) : '¥0.00' ?></td>
                                            </tr>
                                        <?php
                                        endforeach;
                                    else:
                                        ?>
                                        <tr>
                                            <td colspan="5" class="text-center text-muted">暂无升级配置明细</td>
                                        </tr>
                                    <?php endif; ?>
                                    </tbody>
                                    <tfoot>
                                    <tr class="info">
                                        <td colspan="4"><strong>升级费用小计</strong></td>
                                        <td><strong>¥<?= number_format($totalUpgrade, 2) ?></strong></td>
                                    </tr>
                                    <?php
                                    $amountTotal = ArrayHelper::getValue($priceDetail, 'amount_total', $model->amount_total);
                                    if ($amountTotal): ?>
                                        <tr class="success">
                                            <td colspan="4"><strong>订单总金额</strong></td>
                                            <td><strong>¥<?= number_format($amountTotal, 2) ?></strong></td>
                                        </tr>
                                    <?php endif; ?>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- 备注信息 -->
                <?php if ($model->remark): ?>
                    <div class="row">
                        <div class="col-md-12">
                            <h4>备注信息</h4>
                            <div class="well">
                                <?= Html::encode($model->remark) ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

            </div>
        </div>
    </div>
</div>

<style>
    .label-lg {
        font-size: 14px;
        padding: 6px 12px;
    }
</style>
