<?php

namespace addons\VymCloud\merchant\controllers;

use addons\VymCloud\common\models\DefaultModel;
use Yii;
use common\controllers\AddonsController;
use common\traits\MerchantTrait;

/**
 * 商户端基础控制器
 *
 * Class BaseController
 * @package addons\VymCloud\merchant\controllers
 */
class BaseController extends AddonsController
{
    use MerchantTrait;
    /**
     * @var string
     */
    public $layout = "@addons/VymCloud/merchant/views/layouts/main";

    /**
     * @var DefaultModel
     */
    public $modelClass = DefaultModel::class;

    /**
     * @var int|string|null
     */
    public $merchantId;


    /**
     * 初始化
     */
    public function init()
    {
        parent::init();

        // 获取当前商户ID
        $this->merchantId = Yii::$app->user->getId();
    }

    /**
     * 返回模型
     *
     * @param $id
     *
     * @return \yii\db\ActiveRecord
     */
    protected function findModel($id)
    {
        //获取模型ID
        $primaryKey = $this->modelClass::primaryKey()[0];
        /* @var $model \yii\db\ActiveRecord */
        if (empty($id) || empty($model = $this->modelClass::find()->where([$primaryKey => $id])->one())) {
            $model = new $this->modelClass;
            return $model->loadDefaultValues();
        }

        return $model;
    }
}
