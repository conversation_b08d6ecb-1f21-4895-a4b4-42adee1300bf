<?php

use common\enums\AppEnum;
use common\helpers\ArrayHelper;
use common\widgets\webuploader\Files;
use yii\widgets\DetailView;
use common\helpers\Html;
use common\helpers\Url;
use common\enums\StatusEnum;

$this->title = '工单详情：' . $model->title;
$this->params['breadcrumbs'][] = ['label' => '工单管理', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

// 获取回复列表
$replies = $model->getRepliesList();

?>

<div class="row">
    <div class="col-md-8">
        <!-- 工单内容 -->
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title">工单内容</h3>
            </div>
            <div class="box-body">
                <h4><?= Html::encode($model->title) ?></h4>
                <div class="ticket-info">
                    <span class="label label-info"><?= $model->category ? $model->category->title : '未分类' ?></span>
                    <?php
                    $priorityLabels = [
                        1 => '<span class="label label-info">低优先级</span>',
                        2 => '<span class="label label-warning">中优先级</span>',
                        3 => '<span class="label label-danger">高优先级</span>',
                    ];
                    echo $priorityLabels[$model->priority] ?? '';
                    
                    $statusLabels = [
                        1 => '<span class="label label-info">待处理</span>',
                        2 => '<span class="label label-warning">处理中</span>',
                        3 => '<span class="label label-success">已解决</span>',
                        4 => '<span class="label label-default">已关闭</span>',
                    ];
                    echo $statusLabels[$model->status] ?? '';
                    ?>
                    <span class="text-muted pull-right"><?= Yii::$app->formatter->asDatetime($model->created_at) ?></span>
                </div>
                <hr>
                <div class="ticket-content">
                    <?= nl2br(Html::encode($model->content)) ?>
                </div>
                
                <?php if ($model->getImagesList()): ?>
                <div class="ticket-images">
                    <hr>
                    <h5><i class="fa fa-image"></i> 附件图片</h5>
                    <div class="row">
                        <?php foreach ($model->getImagesList() as $image): ?>
                        <div class="col-md-3">
                            <a href="<?= $image ?>" target="_blank">
                                <img src="<?= $image ?>" class="img-thumbnail" style="max-height: 150px;">
                            </a>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- 回复列表 -->
        <?php if (!empty($replies)): ?>
        <div class="box box-success">
            <div class="box-header with-border">
                <h3 class="box-title">回复记录 (<?= count($replies) ?>)</h3>
            </div>
            <div class="box-body">
                <?php foreach ($replies as $reply): ?>
                <div class="post">
                    <div class="user-block">
                        <img class="img-circle img-bordered-sm" src="<?= !empty($reply['user_type']) && $reply['user_type'] == 2 ? '/resources/img/profile.jpg' : '/resources/img/user.jpg' ?>" alt="用户头像">
                        <span class="username">
                            <?= Html::encode($reply['user_name'] ?? ($reply['user_type'] == 2 ? '管理员' : '客户')) ?>
                            <?= $reply['user_type'] == 2 ? '<span class="label label-success">管理员</span>' : '<span class="label label-info">客户</span>' ?>
                        </span>
                        <span class="description">回复于 - <?= Yii::$app->formatter->asDatetime($reply['created_at']) ?></span>
                    </div>
                    <p><?= nl2br(Html::encode($reply['content'])) ?></p>
                    
                    <?php if (!empty($reply['images'])): ?>
                    <div class="row">
                        <?php  $images = is_string($reply['images'])?json_decode($reply['images'],true): $reply['images'];
                        foreach ($images as $image): ?>
                        <div class="col-md-3">
                            <a href="<?= $image ?>" target="_blank">
                                <img src="<?= $image ?>" class="img-thumbnail" style="max-height: 150px;">
                            </a>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
                <hr>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- 回复表单 -->
        <?php if ($model->status < 4): ?>
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">回复工单</h3>
            </div>
            <div class="box-body">
                <form id="reply-form">
                    <div class="form-group">
                        <textarea id="reply-content" class="form-control" rows="5" placeholder="请输入回复内容..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>上传图片</label>
                        <div id="image-upload-container">
                            <!-- 图片上传控件将在这里动态生成 -->
                            <?= Files::widget([
                                'name' => "images",
                                'type' => 'images',
                                'themeConfig'=>[
                                    'select'=>false
                                ],
                                'theme' => 'default',
                                'config' => [
                                    'pick' => [
                                        'multiple' => true,
                                    ],
                                    'fileNumLimit'=>6,
                                ]
                            ]) ?>
                        </div>
<!--                        <button type="button" id="add-image" class="btn btn-default btn-sm"><i class="fa fa-plus"></i> 添加图片</button>-->
                    </div>
                    <div class="form-group">
                        <button type="button" id="submit-reply" class="btn btn-primary">提交回复</button>
                    </div>
                </form>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <div class="col-md-4">
        <!-- 工单信息 -->
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">工单信息</h3>
            </div>
            <div class="box-body">
                <?= DetailView::widget([
                    'model' => $model,
                    'attributes' => [
                        'id',
                        [
                            'label'          => '所属商户',
                            'format'         => 'raw',
                            'visible'        => Yii::$app->id == AppEnum::BACKEND, // 只在总后台显示
                            'value'          => function ($model) {
                                $merchantId    = ArrayHelper::getValue($model->merchant,"id");
                                $merchantTitle =  ArrayHelper::getValue($model->merchant,"title","未设置"); //$model->merchant->title ?? '未设置';
                                $str           = "未关联商户";
                                if ($merchantId) {
                                    $str = "ID: {$merchantId}";
                                    if ($merchantTitle) {
                                        $str .= "<br/>名称: {$merchantTitle}";
                                    }
                                }
                                return $str;
                            },
                        ],
                        [
                            'attribute' => 'category_id',
                            'value' => $model->category ? $model->category->title : '未分类',
                        ],
                        [
                            'attribute' => 'priority',
                            'value' => $model->getPriorityLabel(),
                        ],
                        [
                            'attribute' => 'status',
                            'value' => $model->getStatusLabel(),
                        ],
                        [
                            'attribute' => 'user_id',
                            'value' => $model->user ? $model->user->username : $model->user_id,
                        ],
                        [
                            'attribute' => 'admin_id',
                            'value' => $model->admin_id > 0?($model->admin ? $model->admin->username :$model->admin_id)  : '未分配',
                        ],
                        'reply_count',
                        [
                            'attribute' => 'last_reply_time',
                            'format' => ['date', 'php:Y-m-d H:i:s'],
                            'value' => $model->last_reply_time > 0 ? $model->last_reply_time : null,
                        ],
                        [
                            'attribute' => 'satisfaction',
                            'value' => $model->getSatisfactionLabel(),
                        ],
                        'ip',
                        [
                            'attribute' => 'created_at',
                            'format' => ['date', 'php:Y-m-d H:i:s'],
                        ],
                        [
                            'attribute' => 'updated_at',
                            'format' => ['date', 'php:Y-m-d H:i:s'],
                        ],
                    ],
                ]) ?>
            </div>
        </div>
        
        <!-- 工单操作 -->
        <div class="box box-warning">
            <div class="box-header with-border">
                <h3 class="box-title">工单操作</h3>
            </div>
            <div class="box-body">
                <div class="btn-group btn-group-justified">
                    <?php if ($model->status != 1): ?>
                    <a href="javascript:;" class="btn btn-info" onclick="updateStatus(1)">标为待处理</a>
                    <?php endif; ?>
                    
                    <?php if ($model->status != 2): ?>
                    <a href="javascript:;" class="btn btn-warning" onclick="updateStatus(2)">标为处理中</a>
                    <?php endif; ?>
                </div>
                <br>
                <div class="btn-group btn-group-justified">
                    <?php if ($model->status != 3): ?>
                    <a href="javascript:;" class="btn btn-success" onclick="updateStatus(3)">标为已解决</a>
                    <?php endif; ?>
                    
                    <?php if ($model->status != 4): ?>
                    <a href="javascript:;" class="btn btn-default" onclick="updateStatus(4)">关闭工单</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$replyUrl = Url::to(['reply', 'id' => $model->id]);


$js = <<<JS
// 添加图片上传控件
var imageCount = 0;
$('#add-image').click(function() {
    if (imageCount >= 5) {
        alert('最多上传5张图片');
        return;
    }
    
    var imageInput = '<div class="image-upload-item" style="margin-bottom:10px;">' +
                     '<input type="file" name="image[]" class="image-upload" accept="image/*">' +
                     '<button type="button" class="btn btn-danger btn-xs remove-image" style="margin-left:10px;">删除</button>' +
                     '<div class="image-preview" style="margin-top:5px;"></div>' +
                     '</div>';
    $('#image-upload-container').append(imageInput);
    imageCount++;
});

// 删除图片上传控件
$(document).on('click', '.remove-image', function() {
    $(this).closest('.image-upload-item').remove();
    imageCount--;
});

// 图片预览
$(document).on('change', '.image-upload', function() {
    var file = this.files[0];
    var preview = $(this).closest('.image-upload-item').find('.image-preview');
    
    if (file) {
        var reader = new FileReader();
        reader.onload = function(e) {
            preview.html('<img src="' + e.target.result + '" style="max-height:100px;max-width:100%;">');
        }
        reader.readAsDataURL(file);
    } else {
        preview.html('');
    }
});

// 提交回复
$('#submit-reply').click(function() {
    var content = $('#reply-content').val();
    if (!content) {
        alert('请输入回复内容');
        return;
    }
    
    // 收集图片
    // var images = [];
    // $('.image-upload').each(function() {
    //     if (this.files && this.files[0]) {
    //         // 这里应该上传图片并获取URL，为了简化，我们假设直接获取了URL
    //         // 实际应用中，需要先上传图片，然后获取URL
    //         // 这里仅作为示例，实际实现需要根据项目的文件上传机制来处理
    //         var imageUrl = '/uploads/temp/' + this.files[0].name;
    //         images.push(imageUrl);
    //     }
    // });
    
    let images = $('input[name="images[]"]').map(function() {
            return this.value;
        }).get();
    
    // 发送回复请求
    $.ajax({
        url: '{$replyUrl}',
        type: 'POST',
        data: {
            content: content,
            images: images
        },
        dataType: 'json',
        success: function(response) {
            if (response.code == 200) {
                alert(response.message);
                // 刷新页面
                window.location.reload();
            } else {
                alert(response.message);
            }
        },
        error: function() {
            alert('网络错误，请重试');
        }
    });
});


JS;

$this->registerJs($js);
?>
<script>
    // 更新工单状态
    function updateStatus(status) {
        var statusText = '';
        switch(status) {
            case 1: statusText = '待处理'; break;
            case 2: statusText = '处理中'; break;
            case 3: statusText = '已解决'; break;
            case 4: statusText = '已关闭'; break;
        }

        if (!confirm('确定要将工单状态更改为' + statusText + '吗？')) {
            return;
        }

        let statusUrl ='<?= Url::to(['status', 'id' => $model->id])?>';

        $.ajax({
            url: statusUrl,
            type: 'POST',
            data: {
                status: status
            },
            dataType: 'json',
            success: function(response) {
                if (response.code == 200) {
                    alert(response.message);
                    // 刷新页面
                    window.location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('网络错误，请重试');
            }
        });
    }
</script>
