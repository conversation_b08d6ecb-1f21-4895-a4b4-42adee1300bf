<?php

use addons\VymCloud\common\enums\ServerEnum;
use common\helpers\Html;
use common\helpers\ArrayHelper;
use common\helpers\Url;

/** @var yii\web\View $this */
/** @var addons\VymCloud\common\models\CloudServer $model */

$this->title                   = $model->name ?: ('服务器 #' . $model->id);
$this->params['breadcrumbs'][] = ['label' => '业务实例', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);

// 获取服务器详细配置
$detail = $model->detail ? (is_string($model->detail) ? json_decode($model->detail, true) : $model->detail) : [];
$extend = $model->extend ? (is_string($model->extend) ? json_decode($model->extend, true) : $model->extend) : [];

// 状态信息
$serverStatusMap = ServerEnum::getMap();
$powerStatusMap  = ServerEnum::getPowerStatusMap();

$serverStatus = $serverStatusMap[$model->status_server] ?? '未知';
$powerStatus  = $powerStatusMap[$model->status_power] ?? '未知';
?>

<style>
    /* 自定义样式 */
    .server-detail-container {
        margin: 0;
    }

    .server-header {
        background: #fff;
        color: #333;
        padding: 20px;
        border-radius: 8px 8px 0 0;
        margin-bottom: 0;
    }

    .server-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
    }

    .server-subtitle {
        opacity: 0.9;
        margin: 0;
    }

    .main-content {
        background: #fff;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 25px;
        min-height: 500px;
    }

    .info-card {
        background: #fff;
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
    }

    .info-card h4 {
        margin-top: 0;
        margin-bottom: 15px;
        color: #333;
        font-weight: 600;
        padding-bottom: 8px;
    }

    .info-row {
        display: flex;
        margin-bottom: 12px;
        align-items: center;
    }

    .info-label {
        min-width: 120px;
        color: #666;
        font-weight: 500;
    }

    .info-value {
        color: #333;
        flex: 1;
    }


    .login-info {
        background: #fff8e1;
        border: 1px solid #ffcc02;
        border-radius: 6px;
        padding: 15px;
        margin-top: 15px;
    }

    .card-header-with-btn {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid #f0f0f0;
    }

    .card-header-with-btn h4 {
        margin: 0;
        color: #333;
        font-weight: 600;
    }

    .ip-detail-item {
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        margin-bottom: 15px;
        background: #fafbfc;
    }

    .ip-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 15px;
        background: #fff;
        border-bottom: 1px solid #e3e6f0;
        border-radius: 6px 6px 0 0;
    }

    .ip-address {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .ip-address strong {
        font-size: 16px;
        color: #333;
    }

    .ip-actions {
        display: flex;
        gap: 5px;
    }

    .ip-config {
        padding: 15px;
    }

    .ip-config .row {
        margin-bottom: 0;
    }

    .ip-config .col-md-3 {
        margin-bottom: 10px;
    }

    .ip-config small.text-muted {
        font-size: 11px;
        text-transform: uppercase;
        font-weight: 600;
        letter-spacing: 0.5px;
    }

    .ip-config span {
        font-size: 13px;
        color: #333;
        font-weight: 500;
    }

    .server-header-with-actions {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 20px;
    }

    .server-info {
        flex: 1;
    }

    .server-actions {
        flex-shrink: 0;
        margin-top: 5px;
        text-align: right;
    }

    .server-actions .btn {
        margin-left: 5px;
        margin-bottom: 5px;
        font-size: 12px;
        padding: 6px 12px;
        white-space: nowrap;
    }

    .server-actions .btn:first-child {
        margin-left: 0;
    }

    @media (max-width: 768px) {
        .server-header-with-actions {
            flex-direction: column;
            gap: 15px;
        }

        .server-actions {
            text-align: left;
        }

        .server-actions .btn {
            margin-left: 0;
            margin-right: 5px;
        }
    }
</style>

<div class="server-detail-container">
    <!-- 服务器头部信息 -->
    <div class="server-header">
        <div class="server-header-with-actions">
            <div class="server-info">
                <h1 class="server-title">
                    <i class="fa fa-server"></i>
                    <?= Html::encode($this->title) ?>
                </h1>
                <p class="server-subtitle">
                    编号/别名：#<?= $model->id ?> / <?= Html::encode($model->name ?: 'MyServer') ?> |
                    地域/可用区：香港 / <?= Html::encode($model->cluster->name ?? 'PVE3') ?>
                </p>
            </div>
            <div class="server-actions">
                <!--判断状态，如果是创建中可以点击创建-->
                <?php if ($model->status_server == ServerEnum::SERVER_CREATING) : ?>
                    <?= Html::button('<i class="fa fa-play"></i> 创建实例', ['class' => 'btn btn-primary', 'onclick' => 'createServer(' . $model->id . ')']) ?>
                <?php else: ?>
                    <?= Html::button('<i class="fa fa-terminal"></i> 远程连接', ['class' => 'btn btn-primary', 'onclick' => 'remoteConnect(' . $model->id . ')']) ?>
                    <?= Html::button('<i class="fa fa-play"></i> 开机', ['class' => 'btn btn-success', 'onclick' => 'powerAction(' . $model->id . ', "start")']) ?>
                    <?= Html::button('<i class="fa fa-stop"></i> 关机', ['class' => 'btn btn-warning', 'onclick' => 'powerAction(' . $model->id . ', "stop")']) ?>
                    <?= Html::button('<i class="fa fa-retweet"></i> 重启', ['class' => 'btn btn-info', 'onclick' => 'powerAction(' . $model->id . ', "restart")']) ?>

                    <?= Html::button('<i class="fa fa-reply-all"></i> 重装系统', ['class' => 'btn btn-danger', 'onclick' => 'reinstallSystem(' . $model->id . ')']) ?>
                    <?= Html::button('<i class="fa fa-key"></i> 重置密码', ['class' => 'btn btn-warning', 'onclick' => 'resetPassword(' . $model->id . ')']) ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
        <div class="row">
            <!-- 左侧：云服务器信息 -->
            <div class="col-lg-8">
                <!-- 登录信息 -->
                <div class="info-card">
                    <div class="card-header-with-btn">
                        <h4><i class="fa fa-lock"></i> 登录信息</h4>
                    </div>
                    <div class="login-info">
                        <div class="info-row">
                            <div class="info-label">系统登录：</div>
                            <div class="info-value">
                                <?= Html::encode($model->ip ?: '************74') ?>
                                <?= Html::button('<i class="fa fa-copy"></i>', ['class' => 'btn btn-xs btn-default copy-btn', 'title' => '复制']) ?>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">端口：</div>
                            <div class="info-value">
                                <span id="ssh-port"><?= ArrayHelper::getValue($extend, 'ssh_port', '3287') ?></span>
                                <?= Html::button('<i class="fa fa-copy"></i>', ['class' => 'btn btn-xs btn-default copy-btn', 'data-copy' => 'ssh-port', 'title' => '复制端口']) ?>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">账号：</div>
                            <div class="info-value">
                                <span id="ssh-user"><?= Html::encode(ArrayHelper::getValue($extend, 'ssh_user', 'Administrator')) ?></span>
                                <?= Html::button('<i class="fa fa-copy"></i>', ['class' => 'btn btn-xs btn-default copy-btn', 'data-copy' => 'ssh-user', 'title' => '复制账号']) ?>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">密码：</div>
                            <div class="info-value">
                                        <span id="ssh-pass"
                                              style="display: none;"><?= Html::encode(ArrayHelper::getValue($extend, 'ssh_pass', '********')) ?></span>
                                <span id="ssh-pass-hidden">********</span>
                                <?= Html::button('<i class="fa fa-eye"></i>', ['class' => 'btn btn-xs btn-warning', 'id' => 'show-password-btn', 'title' => '显示密码']) ?>
                                <?= Html::button('<i class="fa fa-copy"></i>', ['class' => 'btn btn-xs btn-default copy-btn', 'data-copy' => 'ssh-pass', 'title' => '复制密码', 'style' => 'display: none;', 'id' => 'copy-password-btn']) ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="info-card">
                    <div class="card-header-with-btn">
                        <h4><i class="fa fa-info-circle"></i> 服务器信息</h4>
                    </div>
                    <div class="info-row">
                        <div class="info-label">运行状态：</div>
                        <div class="info-value">
                            <i class="fa fa-circle" style="color: #28a745;"></i> 运行中
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">操作系统：</div>
                        <div class="info-value">
                            <i class="fab fa-windows"></i> Windows Server 2012 64bit
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">vCPU：</div>
                        <div class="info-value">
                            <strong><?= ArrayHelper::getValue($detail, 'config.cpu') ?>H</strong>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">内存：</div>
                        <div class="info-value">
                            <strong><?= ArrayHelper::getValue($detail, 'config.mem') ?>GB</strong>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">系统盘：</div>
                        <div class="info-value">
                            <strong><?= ArrayHelper::getValue($detail, 'config.disk_os') ?>GB</strong>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">数据盘：</div>
                        <div class="info-value">
                            <strong><?= ArrayHelper::getValue($detail, 'config.disk_data') ?>GB</strong>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">公网IP：</div>
                        <div class="info-value">
                            <strong><?= ArrayHelper::getValue($detail, 'config.ip_num') ?>个</strong>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">带宽：</div>
                        <div class="info-value">
                            <strong><?= ArrayHelper::getValue($detail, 'config.bw') ?>Mbps</strong>
                            <?= Html::button('<i class="fa fa-arrow-up"></i> 升级带宽', ['class' => 'btn btn-success btn-sm inline-btn', 'onclick' => 'upgradeBandwidth(' . $model->id . ')']) ?>
                            <p style="color: #666; font-size: 12px; margin-top: 5px;">1Mbps = 128k/s</p>
                        </div>
                    </div>
                    <?php if (ArrayHelper::getValue($detail, 'config.def') > 0): ?>
                        <div class="info-row">
                            <div class="info-label">防御：</div>
                            <div class="info-value">
                                <strong><?= ArrayHelper::getValue($detail, 'config.def') ?>Gbps</strong>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 右侧：付费信息 -->
            <div class="col-lg-4">
                <div class="info-card">
                    <div class="card-header-with-btn">
                        <h4><i class="fa fa-hdd-o"></i> IP详情</h4>
                        <?= Html::button('<i class="fa fa-shopping-cart"></i> 增购IP', ['class' => 'btn btn-primary btn-sm', 'onclick' => 'purchaseIP(' . $model->id . ')']) ?>
                    </div>

                    <!-- IP详细信息列表 -->
                    <?php
                    // 获取服务器的所有IP地址
                    $serverIps = $model->ips;
                    // 显示所有关联的IP地址及其详细配置
                    foreach ($serverIps as $ipRecord):
                        ?>
                        <div class="ip-detail-item">
                            <div class="ip-header">
                                <div class="ip-address">
                                    <strong><?= Html::encode($ipRecord->ip) ?></strong>
                                    <?php if ($ipRecord->is_main): ?>
                                        <span class="label label-primary">主IP</span>
                                    <?php else: ?>
                                        <span class="label label-default">辅助IP</span>
                                    <?php endif; ?>
                                    <?php if ($ipRecord->is_use): ?>
                                        <span class="label label-success">正常</span>
                                    <?php else: ?>
                                        <span class="label label-warning">未使用</span>
                                    <?php endif; ?>
                                </div>
                                <div class="ip-actions">
                                    <?= Html::button('<i class="fa fa-copy"></i> 复制', ['class' => 'btn btn-xs btn-default copy-ip-btn', 'data-ip' => $ipRecord->ip, 'title' => '复制IP']) ?>
                                    <?php if (!$ipRecord->is_main): ?>
                                        <?= Html::button('<i class="fa fa-trash"></i> 释放', ['class' => 'btn btn-xs btn-danger', 'onclick' => 'releaseIP(' . $model->id . ', ' . $ipRecord->id . ')', 'title' => '释放IP']) ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="ip-config">
                                <div class="row">
                                    <div class="col-md-3">
                                        <small class="text-muted">子网掩码</small><br>
                                        <span><?= Html::encode($ipRecord->mask ?: '*************') ?></span>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">网关</small><br>
                                        <span><?= Html::encode($ipRecord->gateway ?: '************') ?></span>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">DNS</small><br>
                                        <span><?= Html::encode($ipRecord->dns ?: '***************') ?></span>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">VLAN</small><br>
                                        <span><?= $ipRecord->vlan ?: 'N/A' ?></span>
                                    </div>
                                </div>
                                <?php if ($ipRecord->desc): ?>
                                    <div class="row" style="margin-top: 10px;">
                                        <div class="col-md-12">
                                            <small class="text-muted">备注</small><br>
                                            <span><?= Html::encode($ipRecord->desc) ?></span>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <!-- MAC地址信息（如果没有IP记录时显示） -->
                    <?php if (empty($serverIps) && $model->mac): ?>
                        <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee;">
                            <small class="text-muted">服务器MAC地址：</small>
                            <strong><?= Html::encode($model->mac) ?></strong>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="info-card">
                    <div class="card-header-with-btn">
                        <h4><i class="fa fa-hdd-o"></i> 硬盘详情</h4>
                        <?= Html::button('<i class="fa fa-plus"></i> 增加数据盘', ['class' => 'btn btn-success btn-sm', 'onclick' => 'addDataDisk(' . $model->id . ')']) ?>
                    </div>
                    <table class="table table-bordered table-striped">
                        <thead>
                        <tr>
                            <th>类型</th>
                            <th>编号</th>
                            <th>大小</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>系统盘</td>
                            <td>disk-1</td>
                            <td><?= ArrayHelper::getValue($detail, 'config.disk_os', 40) ?>G</td>
                            <td><?= Html::button('扩容', ['class' => 'btn btn-sm btn-primary', 'onclick' => 'expandSystemDisk(' . $model->id . ')']) ?></td>
                        </tr>
                        <?php if (ArrayHelper::getValue($detail, 'config.disk_data')): ?>
                            <tr>
                                <td>数据盘</td>
                                <td>disk-2</td>
                                <td><?= ArrayHelper::getValue($detail, 'config.disk_data') ?>G</td>
                                <td><?= Html::button('扩容', ['class' => 'btn btn-sm btn-primary', 'onclick' => 'expandDataDisk(' . $model->id . ')']) ?></td>
                            </tr>
                        <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <div class="info-card">
                    <div class="card-header-with-btn">
                        <h4><i class="fa fa-credit-card"></i> 付费信息</h4>
                        <?= Html::button('<i class="fa fa-money-bill"></i> 手动续费', ['class' => 'btn btn-sm btn-primary', 'onclick' => 'manualRenew(' . $model->id . ')']) ?>
                    </div>
                    <div class="info-row">
                        <div class="info-label">创建时间：</div>
                        <div class="info-value"><?= $model->created_at ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">续费价格（月）：</div>
                        <div class="info-value">¥<?= $model->renew_amount ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">续费时间：</div>
                        <div class="info-value"><?= $model->end_at ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">自动续费：</div>
                        <div class="info-value">
                            <?= $model->renew_auto ? Html::tag('span', '已开启', ['class' => 'badge badge-success']) : Html::tag('span', '已关闭', ['class' => 'badge badge-danger']) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
</div>

<script>
    $(document).ready(function () {
        // 显示/隐藏密码
        $('#show-password-btn').click(function () {
            var passwordSpan = $('#ssh-pass');
            var hiddenSpan = $('#ssh-pass-hidden');
            var copyBtn = $('#copy-password-btn');

            if (passwordSpan.is(':visible')) {
                // 隐藏密码
                passwordSpan.hide();
                hiddenSpan.show();
                copyBtn.hide();
                $(this).html('<i class="fa fa-eye"></i>').attr('title', '显示密码');
            } else {
                // 显示密码
                passwordSpan.show();
                hiddenSpan.hide();
                copyBtn.show();
                $(this).html('<i class="fa fa-eye-slash"></i>').attr('title', '隐藏密码');
            }
        });

        // 复制功能
        $('.copy-btn').click(function () {
            var copyTarget = $(this).data('copy');
            var text = '';

            if (copyTarget) {
                text = $('#' + copyTarget).text();
            } else {
                text = $(this).prev().text();
            }

            // 使用现代复制API
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function () {
                    showMessage('已复制到剪贴板', 'success');
                });
            } else {
                // 后备方案
                var tempInput = $('<input>');
                $('body').append(tempInput);
                tempInput.val(text).select();
                document.execCommand('copy');
                tempInput.remove();
                showMessage('已复制到剪贴板', 'success');
            }
        });

        // 复制IP功能
        $('.copy-ip-btn').click(function () {
            var ip = $(this).data('ip');

            // 使用现代复制API
            if (navigator.clipboard) {
                navigator.clipboard.writeText(ip).then(function () {
                    showMessage('IP地址已复制到剪贴板', 'success');
                });
            } else {
                // 后备方案
                var tempInput = $('<input>');
                $('body').append(tempInput);
                tempInput.val(ip).select();
                document.execCommand('copy');
                tempInput.remove();
                showMessage('IP地址已复制到剪贴板', 'success');
            }
        });
    });

    // 显示消息的函数
    function showMessage(message, type) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 250px;">' +
            '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
            '<span aria-hidden="true">&times;</span>' +
            '</button>' +
            message +
            '</div>';

        $('body').append(alertHtml);

        // 3秒后自动关闭
        setTimeout(function () {
            $('.alert').fadeOut(function () {
                $(this).remove();
            });
        }, 3000);
    }

    // 发送AJAX请求的通用函数
    function sendRequest(url, data, successCallback) {
        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            dataType: 'json',
            beforeSend: function () {
                // 显示加载状态
                showMessage('操作执行中...', 'success');
            },
            success: function (response) {
                if (response.code === 1) {
                    showMessage(response.msg || '操作成功', 'success');
                    if (successCallback) {
                        successCallback(response);
                    }
                } else {
                    showMessage(response.msg || '操作失败', 'error');
                }
            },
            error: function () {
                // showMessage('网络错误，请稍后重试', 'error');
            }
        });
    }

    // === 概览页面功能 ===

    // 远程连接
    function remoteConnect(serverId) {
        sendRequest('<?= Url::to(['/vym-cloud/server/remote-connect']) ?>', {
            id: serverId
        }, function (response) {
            if (response.data && response.data.consoleUrl) {
                window.open(response.data.consoleUrl, '_blank');
            }
        });
    }

    // 创建服务器
    function createServer(serverId) {
        sendRequest('<?= Url::to(['/vym-cloud/server/create-server']) ?>', {
            id: serverId,
        }, function (response) {
            // 操作成功后可以更新页面状态
            setTimeout(function () {
                // location.reload();
            }, 2000);
        });
    }

    // 电源操作（开机、关机、重启）
    function powerAction(serverId, action) {
        var actionNames = {
            'start': '开机',
            'stop': '关机',
            'restart': '重启'
        };

        var actionName = actionNames[action] || action;

        if (action === 'stop' && !confirm('确定要关机吗？这将中断服务。')) {
            return;
        }

        sendRequest('<?= Url::to(['/vym-cloud/server/power-action']) ?>', {
            id: serverId,
            action: action
        }, function (response) {
            // 操作成功后可以更新页面状态
            setTimeout(function () {
                location.reload();
            }, 2000);
        });
    }

    // 重装系统
    function reinstallSystem(serverId) {
        if (!confirm('重装系统将清除所有数据，确定要继续吗？')) {
            return;
        }

        // 这里应该打开一个模态框选择系统
        window.open('<?= Url::to(['/vym-cloud/server/reinstall']) ?>?id=' + serverId, '_blank', 'width=800,height=600');
    }

    // 重置密码
    function resetPassword(serverId) {
        var newPassword = prompt('请输入新密码（留空则自动生成）:');
        if (newPassword === null) {
            return; // 用户取消
        }

        sendRequest('<?= Url::to(['/vym-cloud/server/reset-password']) ?>', {
            id: serverId,
            password: newPassword
        }, function (response) {
            if (response.data && response.data.password) {
                alert('新密码：' + response.data.password);
            }
            setTimeout(function () {
                location.reload();
            }, 1000);
        });
    }

    // 升级配置
    function upgradeConfig(serverId) {
        window.open('<?= Url::to(['/vym-cloud/server/upgrade']) ?>?id=' + serverId, '_blank', 'width=1000,height=700');
    }

    // 手动续费
    function manualRenew(serverId) {
        var currentEndTime = '<?= $model->end_at ?>';
        var renewAmount = <?= $model->renew_amount ?>;

        var content = '<div style="padding: 20px;">' +
            '<div class="form-group">' +
            '<label>当前到期时间：</label>' +
            '<div style="color: #666; margin-bottom: 15px;">' + currentEndTime + '</div>' +
            '</div>' +
            '<div class="form-group">' +
            '<label for="renew_months">续费时长：</label>' +
            '<select id="renew_months" class="form-control" onchange="calculateRenewPrice()">' +
            '<option value="1">1个月</option>' +
            '<option value="3" selected>3个月</option>' +
            '<option value="6">6个月</option>' +
            '<option value="12">12个月</option>' +
            '</select>' +
            '</div>' +
            '<div class="form-group">' +
            '<label>续费金额：</label>' +
            '<div id="renew_total_amount" style="color: #e74c3c; font-size: 18px; font-weight: bold;">¥' + (renewAmount * 3).toFixed(2) + '</div>' +
            '</div>' +
            '</div>';

        layer.open({
            type: 1,
            title: '服务器续费确认',
            area: ['450px', '380px'],
            content: content,
            btn: ['确认续费', '取消'],
            yes: function (index, layero) {
                var months = $('#renew_months').val();
                if (!months) {
                    layer.msg('请选择续费时长', { icon: 2 });
                    return false;
                }

                layer.close(index);
                // 创建续费订单
                createRenewOrder(serverId, months);
            },
            success: function () {
                // 延迟执行初始化计算，确保DOM已渲染
                setTimeout(function () {
                    calculateRenewPrice();
                }, 100);
            }
        });
    }

    // 计算续费价格和新到期时间
    function calculateRenewPrice() {
        var months = parseInt($('#renew_months').val());
        var renewAmount = <?= $model->renew_amount ?>;
        var currentEndTime = '<?= $model->end_at ?>';

        // 更新续费金额
        var totalAmount = renewAmount * months;
        $('#renew_total_amount').text('¥' + totalAmount.toFixed(2));

        // 计算新的到期时间
        if (currentEndTime && months) {
            try {
                // 简化日期解析 - 直接使用JavaScript Date构造函数
                var currentDate = new Date(currentEndTime);

                // 如果解析失败，尝试替换连字符为斜杠
                if (isNaN(currentDate.getTime())) {
                    currentDate = new Date(currentEndTime.replace(/-/g, '/'));
                }

                // 如果还是失败，显示错误
                if (isNaN(currentDate.getTime())) {
                    $('#new_end_time').text('日期格式错误');
                    return;
                }

                // 添加月份
                var newDate = new Date(currentDate);
                newDate.setMonth(newDate.getMonth() + months);

                // 格式化新的到期时间 - 使用兼容性更好的方法
                function pad(num) {
                    return num < 10 ? '0' + num : num;
                }

                var year = newDate.getFullYear();
                var month = pad(newDate.getMonth() + 1);
                var day = pad(newDate.getDate());
                var hours = pad(newDate.getHours());
                var minutes = pad(newDate.getMinutes());
                var seconds = pad(newDate.getSeconds());

                var newEndTime = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;

                $('#new_end_time').text(newEndTime);

            } catch (e) {
                $('#new_end_time').text('计算错误');
            }
        } else {
            $('#new_end_time').text('参数错误');
        }
    }

    // 创建续费订单
    function createRenewOrder(serverId, months) {
        sendRequest('<?= Url::to(['/vym-cloud/server/create-renew-order']) ?>', {
            id: serverId,
            months: months
        }, function (response) {
            if (response.code === 1 && response.data && response.data.order_id) {
                // 跳转到支付页面
                window.location.href = '<?= Url::to(['/vym-cloud/order/online-pay']) ?>?id=' + response.data.order_id;
            } else {
                layer.msg(response.msg || '续费订单创建失败', { icon: 2 });
            }
        });
    }

    // === 网络页面功能 ===

    // 释放IP
    function releaseIP(serverId, ipId) {
        if (!confirm('确定要释放这个IP地址吗？释放后将无法恢复。')) {
            return;
        }

        sendRequest('<?= Url::to(['/vym-cloud/server/release-ip']) ?>', {
            id: serverId,
            ip_id: ipId
        }, function (response) {
            setTimeout(function () {
                location.reload();
            }, 1000);
        });
    }

    // 增购IP
    function purchaseIP(serverId) {
        var content = `
            <div style="padding: 20px;">
                <div class="form-group">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold;">选择增购数量：</label>
                    <select id="ip_count" class="form-control" style="width: 100%; padding: 8px;">
                        <option value="1">1个IP</option>
                        <option value="2">2个IP</option>
                        <option value="3">3个IP</option>
                        <option value="5">5个IP</option>
                        <option value="10">10个IP</option>
                    </select>
                </div>
                <div style="background: #d9edf7; border: 1px solid #bce8f1; color: #31708f; padding: 10px; border-radius: 4px; margin-top: 15px;">
                    <i class="fa fa-info-circle"></i> IP地址购买后立即分配，费用按剩余时间计算
                </div>
            </div>
        `;

        layer.open({
            type: 1,
            title: '增购IP地址',
            area: ['400px', '280px'],
            content: content,
            btn: ['确认购买', '取消'],
            yes: function (index, layero) {
                var ipCount = $('#ip_count').val();
                if (!ipCount) {
                    layer.msg('请选择IP数量', { icon: 2 });
                    return false;
                }

                layer.close(index);
                sendRequest('<?= Url::to(['/vym-cloud/server/purchase-ip']) ?>', {
                    id: serverId,
                    ip_count: ipCount
                });
            }
        });
    }

    // 升级带宽
    function upgradeBandwidth(serverId) {
        var currentBw = <?= ArrayHelper::getValue($detail, 'bw', 5) ?>;

        // 生成带宽选项
        var bandwidthOptions = '<option value="">请选择带宽</option>';
        for (var i = currentBw + 5; i <= 100; i += 5) {
            bandwidthOptions += '<option value="' + i + '">' + i + ' Mbps</option>';
        }

        var content = `
            <div style="padding: 20px;">
                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold;">当前带宽：${currentBw} Mbps</label>
                </div>
                <div class="form-group">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold;">选择新带宽：</label>
                    <select id="bandwidth" class="form-control" style="width: 100%; padding: 8px;">
                        ${bandwidthOptions}
                    </select>
                </div>
                <div style="background: #d9edf7; border: 1px solid #bce8f1; color: #31708f; padding: 10px; border-radius: 4px; margin-top: 15px;">
                    <i class="fa fa-info-circle"></i> 带宽升级后立即生效，只能升级不能降级
                </div>
            </div>
        `;

        layer.open({
            type: 1,
            title: '升级带宽',
            area: ['400px', '320px'],
            content: content,
            btn: ['确认升级', '取消'],
            yes: function (index, layero) {
                var bandwidth = $('#bandwidth').val();
                if (!bandwidth) {
                    layer.msg('请选择带宽', { icon: 2 });
                    return false;
                }
                if (parseInt(bandwidth) <= currentBw) {
                    layer.msg('新带宽必须大于当前带宽', { icon: 2 });
                    return false;
                }

                layer.close(index);
                sendRequest('<?= Url::to(['/vym-cloud/server/upgrade-bandwidth']) ?>', {
                    id: serverId,
                    bandwidth: bandwidth
                });
            }
        });
    }

    // === 硬盘页面功能 ===

    // 增加数据盘
    function addDataDisk(serverId) {
        var content = `
            <div style="padding: 20px;">
                <div class="form-group">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold;">数据盘大小（GB）：</label>
                    <input type="number" id="disk_size" class="form-control" value="100" min="10" max="2000"
                           style="width: 100%; padding: 8px;" placeholder="请输入磁盘大小">
                </div>
                <div style="background: #d9edf7; border: 1px solid #bce8f1; color: #31708f; padding: 10px; border-radius: 4px; margin-top: 15px;">
                    <i class="fa fa-info-circle"></i> 数据盘大小范围：10GB - 2000GB，添加后立即生效
                </div>
            </div>
        `;

        layer.open({
            type: 1,
            title: '增加数据盘',
            area: ['400px', '280px'],
            content: content,
            btn: ['确认添加', '取消'],
            yes: function (index, layero) {
                var diskSize = $('#disk_size').val();
                if (!diskSize || isNaN(diskSize) || diskSize < 10) {
                    layer.msg('请输入有效的磁盘大小（最小10GB）', { icon: 2 });
                    return false;
                }

                layer.close(index);
                sendRequest('<?= Url::to(['/vym-cloud/server/add-data-disk']) ?>', {
                    id: serverId,
                    size: diskSize
                }, function (response) {
                    setTimeout(function () {
                        location.reload();
                    }, 2000);
                });
            }
        });
    }

    // 系统盘扩容
    function expandSystemDisk(serverId) {
        var currentSize = <?= ArrayHelper::getValue($detail, 'disk_os', 40) ?>;

        var content = `
            <div style="padding: 20px;">
                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold;">当前系统盘：${currentSize} GB</label>
                </div>
                <div class="form-group">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold;">新系统盘大小（GB）：</label>
                    <input type="number" id="new_size" class="form-control" value="${currentSize + 20}" min="${currentSize + 1}" max="2000"
                           style="width: 100%; padding: 8px;" placeholder="请输入新的磁盘大小">
                </div>
                <div style="background: #d9edf7; border: 1px solid #bce8f1; color: #31708f; padding: 10px; border-radius: 4px; margin-top: 15px;">
                    <i class="fa fa-info-circle"></i> 系统盘只能扩容不能缩容，扩容后立即生效
                </div>
            </div>
        `;

        layer.open({
            type: 1,
            title: '系统盘扩容',
            area: ['400px', '320px'],
            content: content,
            btn: ['确认扩容', '取消'],
            yes: function (index, layero) {
                var newSize = $('#new_size').val();
                if (!newSize || isNaN(newSize) || newSize <= currentSize) {
                    layer.msg('新大小必须大于当前大小（' + currentSize + 'GB）', { icon: 2 });
                    return false;
                }

                layer.close(index);
                sendRequest('<?= Url::to(['/vym-cloud/server/expand-system-disk']) ?>', {
                    id: serverId,
                    size: newSize
                }, function (response) {
                    setTimeout(function () {
                        location.reload();
                    }, 2000);
                });
            }
        });
    }

    // 数据盘扩容
    function expandDataDisk(serverId) {
        var content = `
            <div style="padding: 20px;">
                <div class="form-group">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold;">选择数据盘：</label>
                    <select id="disk_id" class="form-control" style="width: 100%; padding: 8px; margin-bottom: 15px;">
                        <option value="scsi1">数据盘1 (scsi1)</option>
                        <option value="scsi2">数据盘2 (scsi2)</option>
                        <option value="scsi3">数据盘3 (scsi3)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold;">新数据盘大小（GB）：</label>
                    <input type="number" id="new_size" class="form-control" value="200" min="10" max="2000"
                           style="width: 100%; padding: 8px;" placeholder="请输入新的磁盘大小">
                </div>
                <div style="background: #d9edf7; border: 1px solid #bce8f1; color: #31708f; padding: 10px; border-radius: 4px; margin-top: 15px;">
                    <i class="fa fa-info-circle"></i> 数据盘只能扩容不能缩容，扩容后立即生效
                </div>
            </div>
        `;

        layer.open({
            type: 1,
            title: '数据盘扩容',
            area: ['400px', '360px'],
            content: content,
            btn: ['确认扩容', '取消'],
            yes: function (index, layero) {
                var diskId = $('#disk_id').val();
                var newSize = $('#new_size').val();

                if (!diskId) {
                    layer.msg('请选择要扩容的数据盘', { icon: 2 });
                    return false;
                }
                if (!newSize || isNaN(newSize) || newSize < 10) {
                    layer.msg('请输入有效的磁盘大小（最小10GB）', { icon: 2 });
                    return false;
                }

                layer.close(index);
                sendRequest('<?= Url::to(['/vym-cloud/server/expand-data-disk']) ?>', {
                    id: serverId,
                    disk_id: diskId,
                    size: newSize
                }, function (response) {
                    setTimeout(function () {
                        location.reload();
                    }, 2000);
                });
            }
        });
    }
</script>
