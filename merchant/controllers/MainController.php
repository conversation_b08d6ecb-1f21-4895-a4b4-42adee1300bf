<?php

namespace merchant\controllers;

use common\helpers\ResultHelper;
use Yii;

/**
 * 主控制器
 *
 * Class MainController
 * @package merchant\controllers
 * <AUTHOR> <<EMAIL>>
 */
class MainController extends BaseController
{
    /**
     * @var string
     */
    public $layout = '@backend/views/layouts/main';

    /**
     * 系统首页
     *
     * @return string
     */
    public function actionIndex()
    {
        // 触发主题切换
        !Yii::$app->params['isMobile'] && Yii::$app->services->theme->autoSwitcher();
        // 设置为 AJAX 关闭掉 DEBUG 显示
        YII_DEBUG && Yii::$app->request->headers->set('X-Requested-With', 'XMLHttpRequest');

        return $this->renderPartial('@backend/views/theme/' . Yii::$app->params['theme']['layout'] . '/index', [

        ]);
    }

    /**
     * 子框架默认主页
     *
     * @return string
     */
    public function actionHome()
    {
        return $this->render($this->action->id, [
            'memberCount' => Yii::$app->services->member->getCountByType(),
            'memberAccount' => Yii::$app->services->memberAccount->getSumByType(),
            'actionLogCount' => Yii::$app->services->actionLog->getCount(),
        ]);
    }

    public function actionServer($type)
    {
        $data = Yii::$app->vymServerService->order->getStatistics($type);
        return ResultHelper::json(200, '获取成功', $data);

    }

    public function actionCloud($type)
    {
        $data = Yii::$app->vymCloudService->order->getStatistics($type);
        return ResultHelper::json(200, '获取成功', $data);

    }
}
