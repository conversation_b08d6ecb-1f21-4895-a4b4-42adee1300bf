<?php

namespace addons\VymCloud\common\models;

use common\behaviors\MerchantStoreBehavior;
use common\helpers\StringHelper;
use common\models\member\Member;
use common\models\merchant\Merchant;
use Yii;

/**
 * This is the model class for table "{{%addon_cloud_order}}".
 *
 * @property int         $id
 * @property int         $uid               会员ID
 * @property int|null    $merchant_id       商户id
 * @property int|null    $store_id          店铺id
 * @property int|null    $order_st          订单状态
 * @property string      $order_no          订单号
 * @property int         $order_type        订单类型，1-购买，2-续费，3-升级
 * @property int|null    $buy_cycle         购买周期/月
 * @property float|null  $amount_total      原价总金额
 * @property float|null  $amount_pay        支付总价
 * @property float|null  $amount_discount   会员折扣金额
 * @property float|null  $amount_coupon     优惠券金额
 * @property float|null  $payment_amount    付款金额
 * @property string      $payment_no        付款订单号
 * @property int|null    $payment_id        付款方式ID
 * @property int|null    $payment_st        付款状态
 * @property string|null $remark            备注
 * @property string|null $coupon_id         优惠券ID
 * @property int|null    $product_id        产品ID
 * @property int|null    $server_id         实例业务ID
 * @property array       $server_detail     配置详情
 * @property array       $price_detail      价格详情
 * @property string|null $payment_at        付款时间
 * @property string|null $created_at        创建时间
 * @property string|null $updated_at        更新时间
 * @property string|null $deleted_at        删除时间
 */
class CloudOrder extends \yii\db\ActiveRecord
{

    use MerchantStoreBehavior;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%addon_cloud_order}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['remark', 'coupon_id', 'payment_at', 'created_at', 'updated_at', 'deleted_at'], 'default', 'value' => null],
            [['payment_st', 'server_id'], 'default', 'value' => 0],
            [['merchant_id', 'store_id'], 'default', 'value' => 0],
            [['amount_total', 'amount_pay', 'amount_discount', 'amount_coupon', 'payment_amount'], 'default', 'value' => 0.00],
            [['uid', 'order_st', 'order_type', 'payment_id', 'product_id', 'server_id', 'buy_cycle'], 'integer'],
            [['order_no', 'payment_no', 'server_detail', 'price_detail'], 'required'],
            [['amount_total', 'amount_pay', 'amount_discount', 'amount_coupon'], 'number'],
            [['remark', 'coupon_id'], 'string'],
            [['server_detail', 'price_detail', 'created_at', 'updated_at', 'payment_at', 'deleted_at'], 'safe'],
            [['order_no', 'payment_no'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'              => 'ID',
            'uid'             => '会员帐号',
            'merchant_id'     => '商户ID',
            'store_id'        => '店铺ID',
            'order_st'        => '订单状态',
            'order_no'        => '订单号',
            'order_type'      => '订单类型',
            'buy_cycle'       => '购买周期/月',
            'amount_total'    => '原价总金额',
            'amount_pay'      => '支付总价',
            'amount_discount' => '会员折扣金额',
            'amount_coupon'   => '优惠券金额',
            'payment_amount'  => '付款金额',
            'payment_no'      => '付款订单号',
            'payment_id'      => '付款方式',
            'payment_st'      => '付款状态',
            'payment_at'      => '付款时间',
            'remark'          => '备注',
            'coupon_id'       => '优惠券ID',
            'product_id'      => '产品ID',
            'server_id'       => '实例业务',
            'server_detail'   => '配置详情',
            'price_detail'    => '价格详情',
            'created_at'      => '创建时间',
            'updated_at'      => '更新时间',
            'deleted_at'      => '删除时间',
        ];
    }

    /**
     * @param bool $insert
     *
     * @return bool
     */
    public function beforeSave($insert)
    {
        $this->created_at = StringHelper::intToDate($this->created_at);
        $this->updated_at = StringHelper::intToDate(time());

        return parent::beforeSave($insert);
    }

    /**
     * 关联产品
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(CloudProduct::class, ['product_id' => 'product_id']);
    }

    /**
     * 关联会员
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMember()
    {
        return $this->hasOne(Member::class, ['id' => 'uid']);
    }

    /**
     * 关联商户
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }

    /**
     * 生成唯一订单号
     *
     * @param string $prefix 前缀
     *
     * @return string 订单号
     */
    public static function createOrderNo($prefix = 'CZ')
    {
        return $prefix . date('YmdHis') . rand(10000, 99999);
    }
}
