<?php

use yii\widgets\ActiveForm;
use common\helpers\Url;
use common\enums\StatusEnum;
use common\widgets\ueditor\UEditor;
use common\widgets\map\Map;
use yii\helpers\Html;

/** @var yii\web\View $this */
/** @var addons\VymServer\common\models\ServerArea $model */
/** @var yii\widgets\ActiveForm $form */

$form = ActiveForm::begin([
    'id' => $model->formName(),
    'enableAjaxValidation' => true,
    'validationUrl' => Url::to(['ajax-edit', 'id' => $model['id']]),
    'fieldConfig' => [
        'template' => "<div class='row'><div class='col-sm-3 text-right'>{label}</div><div class='col-sm-8'>{input}\n{hint}\n{error}</div></div>",
    ],
]);
?>

<div class="modal-header">
    <h4 class="modal-title">基本信息</h4>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
            aria-hidden="true">×</span></button>
</div>
<div class="modal-body p-3">
    <div class="card card-primary card-outline card-outline-tabs">
        <div class="card-header p-0 border-bottom-0">
        </div>
        <div class="card-body">
            <?= $form->field($model, 'regional_id')->dropDownList($dropDownList) ?>
            <?= $form->field($model, 'name')->textInput(['maxlength' => true]) ?>
            <?= $form->field($model, 'icon')->textInput(['maxlength' => true]) ?>
            <?= $form->field($model, 'sort')->textInput() ?>
            <?= $form->field($model, 'area_st')->radioList(\common\enums\StatusEnum::getMap()) ?>
        </div>
        <!-- /.card -->
    </div>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
    <button class="btn btn-primary" type="submit">保存</button>
</div>
<?php ActiveForm::end(); ?>