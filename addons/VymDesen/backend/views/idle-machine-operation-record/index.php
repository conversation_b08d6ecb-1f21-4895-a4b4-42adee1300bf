<?php
use yii\helpers\Html;
use yii\widgets\LinkPager;
use yii\helpers\Url;

$this->title = '控制台管理-自有机器电源与交换机操作记录';
?>
<?php $this->beginBlock('content') ?>
<div class="row">	
	<div class="clearfix">
		<div class="pull-right tableTools-container"></div>
	</div>
</div>
<div class="row" style="margin-bottom: 10px;">
	<form action="">
	<input type="hidden" name="r" value="idle-machine-operation-record">
		<div class="col-xs-12">
			<div style="float: left;margin-right: 6px;">
				 <input type="text" class="form-control search-query" name="operator" value='<?php echo Html::encode(Yii::$app->controller->get('operator')) ?>'  placeholder="操作人">
			</div>
			<div style="float: left;margin-right: 6px;">
				 <input type="text" class="form-control search-query" name="operation_type" value='<?php echo Html::encode(Yii::$app->controller->get('operation_type')) ?>'  placeholder="操作类型">
			</div>
			<div style="float: left;margin-right: 6px;">
				 <input type="text" class="form-control search-query" name="ipmi_ip" value='<?php echo Html::encode(Yii::$app->controller->get('ipmi_ip')) ?>'  placeholder="IPMI地址">
			</div>
			<div style="float: left;margin-right: 6px;">
				 <input type="text" class="form-control search-query" name="ip" value='<?php echo Html::encode(Yii::$app->controller->get('ip')) ?>'  placeholder="IP地址">
			</div>
			<div style="float: left;margin-right: 6px;">
				<select class="form-control" name='operator_role' id="form-field-select-1" >
					<option value="">操作人角色</option>
					<option value="管理员" <?php if ('管理员' == Html::encode(Yii::$app->controller->get('operator_role')) ): ?>selected='selected'<?php endif ?>>管理员</option>
					<option value="客户" <?php if ('客户' == Html::encode(Yii::$app->controller->get('operator_role')) ): ?>selected='selected'<?php endif ?>>客户</option>
				</select>
			</div>
			
			<div style="float: left;margin-right: 6px;">
				<input type="text"  name="time" id="start_time" value='<?php echo Html::encode(Yii::$app->controller->get('time')) ?>'  placeholder="开始时间" readonly>
				至
			</div>							 
			<div style="float: left;margin-right: 6px;">
				 <input type="text" name="time_end" id="end_time" value='<?php echo Html::encode(Yii::$app->controller->get('time_end')) ?>'  placeholder="结束时间" readonly>
			</div>
			
		<div>
			<button type="submit" class="btn btn-white btn-primary btn-bold">
				<span class="ace-icon fa fa-search icon-on-right bigger-110"></span>
				搜索
			</button>
			<button type="button" class="btn btn-white btn-success btn-bold" style="margin-left:10px;" onclick="javascript:location.href='<?php echo Url::to(['idle-machine-operation-record']) ?>'">
				<span class="ace-icon fa  fa-refresh"></span>
				刷新
			</button>
		</div>
		</div>
	</form>
</div>
<form action="" id='checkbox'>
<table id="simple-table" width="100%" class="table table-striped table-bordered table-hover">
<thead>
	<tr>
		<th>日志编号</th>
		<th>IPMI地址</th>
		<th>IP地址</th>
		<th>操作类型</th>
		<th>操作人</th>
		<th>操作人角色</th>
		<th class="hidden-480"><i class="ace-icon fa fa-clock-o bigger-110 hidden-480"></i>操作时间</th>													
	</tr>
</thead>
<tbody>
	<?php foreach ($arrRes as $key => $value): ?>
	<tr>
		<td><?php echo $value['id']?></td>
		<td><?php echo $value['ipmi_ip']?></td>
		<td>
			<?php $ipArr = json_decode($value['ip'], true);
				echo implode('<br/>', $ipArr);
			
			?>
			
		
		</td>
		<td><?php echo $value['operation_type']?></td>
		<td><?php echo $value['operator']?></td>				
		<td><?php echo $value['operator_role']?></td>	
		<td><?php echo date('Y-m-d H:i:s',$value['operation_time'])?></td>		
	</tr>
	<?php endforeach ?>						
</tbody>
</table>
<div class="row" style="margin-top: 10px;">
	<div class="col-xs-6">
		<div class="dataTables_info" id="dynamic-table_info" role="status" aria-live="polite">每页<?php echo $pageSize?>条记录，共有<?php echo $iCount;?>条记录</div>
	</div>
	<div class="col-xs-6 pagination1" style="text-align: right;">
		<?php 
			echo LinkPager::widget([
				'pagination' => $page,
				'firstPageLabel'=>"首页",
				'prevPageLabel'=>'上一页',
				'nextPageLabel'=>'下一页',
				'lastPageLabel'=>'末页',
			]);
		?>
	</div>
</div>
</form>
<script type="text/javascript">
	if('ontouchstart' in document.documentElement) document.write("<script src='/js/jquery.mobile.custom.js'>"+"<"+"/script>");
</script>

<!-- page specific plugin scripts -->


<!-- inline scripts related to this page -->
<script type="text/javascript">
	var start = {
		elem: '#start_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas){
			 end.min = datas; //开始日选好后，重置结束日的最小日期
			 end.start = datas //将结束日的初始值设定为开始日
		}
	};
	var end = {
		elem: '#end_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas){
			 end.min = datas; //开始日选好后，重置结束日的最小日期
			 end.start = datas //将结束日的初始值设定为开始日
		}
	};
	laydate(start);
	laydate(end);
	laydate.skin('molv');
</script>
<script type="text/javascript">
	
</script>
<?php $this->endBlock(); ?>
