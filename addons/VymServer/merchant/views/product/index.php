<?php

use addons\VymServer\common\enums\ProductEnum;
use addons\VymServer\common\models\ServerProduct;
use common\enums\AppEnum;
use common\helpers\ArrayHelper;
use common\helpers\Html;
use common\helpers\MerchantHelper;
use yii\grid\GridView;
use yii\widgets\ActiveForm;

/** @var yii\web\View $this */
/** @var yii\data\ActiveDataProvider $dataProvider */
/** @var common\models\base\SearchModel $searchModel */
/** @var array $clusterList */

$this->title                   = '业务产品';
$this->params['breadcrumbs'][] = $this->title;
?>
<!-- Main content -->
<div class="container-fluid">

    <!-- 搜索筛选区域 -->
    <div class="card search-card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-search mr-1"></i>
                筛选条件
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <?php $form = ActiveForm::begin([
                'method'                 => 'get',
                'enableClientValidation' => false,
                'enableAjaxValidation'   => false,
                'options'                => ['class' => 'form-row'],
                'fieldConfig'            => [
                    'template'     => '<div class="input-group-prepend">{label}</div>{input}{error}',
                    'labelOptions' => ['class' => 'col-form-label-sm input-group-text'],
                    'options'      => ['class' => 'input-group col-sm-12 col-md-6 col-lg-3 col-lg-2 mb-2'],
                ],
            ]); ?>
            <!-- 商户筛选 -->
            <?php if (Yii::$app->id == AppEnum::BACKEND): ?>
                <?= MerchantHelper::merchantSelect($form, $searchModel) ?>
            <?php endif; ?>
            <?= $form->field($searchModel, 'name')->textInput(['placeholder' => '产品名称',])->label('产品名称') ?>

            <?= $form->field($searchModel, 'status')->dropDownList(ProductEnum::getStatusMap(), ['prompt' => '选择状态',])->label('产品状态') ?>

            <?= $form->field($searchModel, 'recommend')->dropDownList(ProductEnum::getRecommendMap(), ['prompt' => '选择推荐',])->label('是否推荐') ?>

            <div class="col-sm-12 col-md-6 col-lg-3">
                <?= Html::submitButton('<i class="fas fa-search"></i> 搜索', ['class' => 'btn btn-primary btn-md mr-2',]) ?>
                <?= Html::a('<i class="fas fa-redo"></i> 重置', ['index'], ['class' => 'btn btn-outline-secondary btn-md',]) ?>
            </div>
            <?php ActiveForm::end(); ?>
        </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-list mr-1"></i>
                <?= Html::encode($this->title) ?>
            </h3>
            <div class="card-tools">
                    <?= Html::linkButton(['#'], '查看操作说明', [
                        'class'       => "btn btn-danger btn-sm",
                        'data-toggle' => 'modal',
                        'data-target' => '#operationInstructionsModal',
                    ]); ?>
                    <?= Html::linkButton(['#'], '一键同步产品', [
                        'class'       => "btn btn-outline-danger btn-sm",
                        'data-toggle' => 'modal',
                        'data-target' => '#syncProductModal',
                    ]); ?>
                    <?= Html::linkButton(['#'], '导出业务产品', [
                        'class'       => "btn btn-outline-primary btn-sm",
                        'data-toggle' => 'modal',
                        'data-target' => '#exportProductModal',
                    ]); ?>
                    <?= Html::linkButton(['#'], '导入业务产品', [
                        'class'       => "btn btn-outline-success btn-sm",
                        'data-toggle' => 'modal',
                        'data-target' => '#importProductModal',
                    ]); ?>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <?= GridView::widget([
                    'dataProvider' => $dataProvider,
                    'tableOptions' => [
                        'class' => 'table table-hover rf-table mb-0',
                    ],
                    'columns'      => [
                        [
                            'attribute'      => 'id',
                            'headerOptions'  => ['class' => 'text-left'],
                            'contentOptions' => ['class' => 'text-left'],
                        ],
                        [
                            'attribute'     => 'name',
                            'headerOptions' => ['style' => 'width: 200px;'],
                            'format'        => 'raw',
                            'value'         => function ($model) {
                                return Html::tag('span', Html::encode($model->name), [
                                    'class' => 'font-weight-medium',
                                ]);
                            },
                        ],
                        [
                            'header'    => "默认配置",
                            'attribute' => 'config_basic',
                            'format'    => 'raw',
                            'filter'    => false,
                            'value'     => function ($model) {
                                $pdtConfig = $model->getPdtConfig($model->detail);

                                $cpuName       = ArrayHelper::getValue($pdtConfig, 'cpu_name');
                                $ramName       = ArrayHelper::getValue($pdtConfig, 'ram_name');
                                $hddName       = ArrayHelper::getValue($pdtConfig, 'hdd_name');
                                $ipnumberName  = ArrayHelper::getValue($pdtConfig, 'ipnumber_name');
                                $bandwidthName = ArrayHelper::getValue($pdtConfig, 'bandwidth_name');
                                $systemName    = ArrayHelper::getValue($pdtConfig, 'system_name');
                                $defenseName   = ArrayHelper::getValue($pdtConfig, 'defense_name');
                                $cardName      = ArrayHelper::getValue($pdtConfig, 'card_name');
                                //硬件配置
                                $hardware = [
                                    'cpu'  => $cpuName,
                                    'ram'  => $ramName,
                                    'hdd'  => $hddName,
                                    'card' => $cardName,
                                ];
                                $network  = [
                                    'bandwidth' => $bandwidthName,
                                    'ipnumber'  => $ipnumberName,
                                    'defense'   => $defenseName,
                                ];
                                //移除空值或NULL
                                $hardware    = array_filter($hardware, function ($value) {
                                    return $value !== '' && $value !== null;
                                });
                                $hardwareStr = implode(' / ', $hardware);
                                $network     = array_filter($network, function ($value) {
                                    return $value !== '' && $value !== null;
                                });
                                $networkStr  = implode(' / ', $network);

                                // $configStr = "服务器分类：{$typeName} <br/>";
                                // $configStr .= "服务器机房：{$roomName} <br/>";
                                // $configStr .= "产品配置类别：{$productName} <br/>";
                                $configStr = "硬件配置：{$hardwareStr} <br/>";
                                $configStr .= "网络配置：{$networkStr} <br/>";
                                $configStr .= "操作系统：{$systemName}";
                                return $configStr;
                            },
                        ],
                        [
                            'header'        => "价格",
                            'attribute'     => 'config_price',
                            'format'        => 'raw',
                            'filter'        => false,
                            'headerOptions' => ['class' => 'col-md-2'],
                            'value'         => function ($model) {
                                $html = $model->price_cost ? "成本价：{$model->price_cost}元<br/>" : '成本价：<span class="text-danger">未设置</span><br/>';
                                $html .= $model->price_month ? "月售价：{$model->price_month}元<br/>" : '月售价：<span class="text-danger">未设置</span><br/>';
                                $html .= $model->price_quarter ? "季度价：{$model->price_quarter}元<br/>" : '季度价：<span class="text-danger">未设置</span><br/>';
                                $html .= $model->price_half ? "半年价：{$model->price_half}元<br/>" : '半年价：<span class="text-danger">未设置</span><br/>';
                                $html .= $model->price_year ? "年度价：{$model->price_year}元<br/>" : '年度价：<span class="text-danger">未设置</span><br/>';
                                return $html;
                            },
                        ],
                        [
                            'attribute'      => 'status',
                            'label'          => '状态',
                            'headerOptions'  => ['style' => 'width: 100px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                return ProductEnum::getStatusHtml($model->status);
                            },
                        ],
                        [
                            'attribute'      => 'recommend',
                            'label'          => '推荐',
                            'headerOptions'  => ['style' => 'width: 100px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                return ProductEnum::getRecommendHtml($model->recommend);
                            },
                        ],
                        [
                            'header'         => '操作',
                            'headerOptions'  => ['style' => 'width: 120px;'],
                            'contentOptions' => ['class' => 'text-center'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $buttons   = [];
                                $buttons[] = Html::a('<i class="fas fa-edit"></i> 编辑', ['edit', 'id' => $model->id], [
                                    'class' => 'btn btn-sm btn-outline-primary',
                                    'title' => '编辑',
                                ]);
                                $buttons[] = Html::a('<i class="fas fa-trash"></i> 删除', ['delete', 'id' => $model->id], [
                                    'class'        => 'btn btn-sm btn-outline-danger',
                                    'title'        => '删除',
                                    'data-confirm' => '确定要删除这个产品吗？',
                                ]);
                                return implode(' ', $buttons);
                            },
                        ],
                    ],
                ]); ?>
            </div>
        </div>
    </div>

    <!--操作说明-->
    <?= $this->render('modal/operationInstructions') ?>
    <!--一键同步产品-->
    <?= $this->render('modal/syncProduct', ['model' => $dataProvider]) ?>
    <!--导出业务产品-->
    <?= $this->render('modal/exportProduct', ['model' => $dataProvider]) ?>
    <!--导入业务产品-->
    <?= $this->render('modal/importProduct', ['model' => $dataProvider]) ?>

</div>