<?php

use yii\helpers\Url;
use addons\VymDesen\common\components\DataHelper;

$this->title = '控制台管理 - 业务退款流程';
?>
<?php $this->beginBlock('content') ?>
<style type="text/css">
	*{margin:0;padding:0;}
	ul,li{list-style:none;margin:0;padding:0;}
	a{text-decoration:none;}
	
	.clearfix{clear:both;}
	
	.info-title{height:40px;width:100%;line-height:50px;font-weight:bold;border-bottom:2px solid rgba(232,233,234,1);margin-bottom:10px;}
	.info-box{margin:20px 0px;padding:0 20px;margin-top:30px;}
	.info-box .customertable{border:none;}
	.info-box .customertable tr{border:none;}
	.info-box .customertable tr td{padding:4px;}
	.info-box .customertable tr:first-child td{border:none;}
	
	.info-box .visitor{padding:5px 10px;float:left;background:#438eb9;border-radius:29px;-webkit-border-radius:29px;color:#fff;margin-right:15px;}
	.info-box .visitor div{float:left;text-align:center;}
	.info-box .visitor .visit_name{margin-right:5px;}
	.info-box .visitor .visit_count{margin-left:5px;}
	
	.table tr td input{height:30px;line-height:30px;}
	.table tr td .fact_price{width:80px;}
	.table tr td .trade_num{width:40px;}
	
	.updatebox{margin-bottom:10px;}
	.updatebox .total-box{float:left;display:inline-block;margin-right:30px;}
	.updatebox .total-box div{float:left;height:35px;line-height:35px;display:block;}
	.updatebox .total-box input{float:left;height:35px;line-height:35px;}
	.updatebox .total-box .tips{color:#f00;width:100%;}
	.main_price_total, .main_price_fact, .main_pay_money, .main_pay_remark{margin-right:20px;font-size:20px;line-height:30px!important;}
	.main_price_total{color:#2b7dbc;}
	.main_price_fact{color:rgba(221, 55, 81, 1)}
	
	.money_green{color:#00CC00!important}
	.money_red{color:red!important}
	
	.verify_btn, .refund_btn{display:block;padding:10px 10px;text-align:center;float:right;margin-right:20px;}
	.verify_btn:link ,.refund_btn:link{color: #fff;}
	.verify_btn:visited ,.refund_btn:visited{color: #fff;}
	.verify_btn:hover ,.refund_btn:hover{color: #fff;text-decoration:none;}
	.verify_btn:active ,.refund_btn:active{color: #fff;}
	.verify_success{background:#82af6f;color:#fff;}
	.verify_fail{background:#d15b47;color:#fff;}
	
	.verify_result_success{color:#82af6f!important;}
	.verify_result_fail{color:#d15b47!important;}
	
	.idle_server{background:#82af6f;color:#fff;padding:3px 5px;margin-right:10px;}
	.supplier_server{background:#f89406;color:#fff;padding:3px 5px;margin-right:10px;}
	
	.float_table{border:none;}
	.float_table tr td{height:40px;line-height:40px!important;}
	.float_table tr td:first-child{text-align:right;width:150px;}
	.float_table tr td textarea{line-height:24px;}
	
	.config_item div{display:inline-block;}
	.config_item .config_title{width:90px;margin-right:10px;}
	.config_item .config_content{}
	.refund_btn{float:left!important;}
	
	.title_tr{background:#f5f5f5;font-weight:bold;}
</style>
<div class="clearfix">
	<div class="pull-right tableTools-container"></div>
</div>

<?php 
	$adminRole = Yii::$app->session['auth']['roles'];
	$adminId = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
?>

<div id="mainContainer">
	<div class="info-title">基本信息</div>
	<div class="info-box">
		<table class="table customertable">
			<tr>
				<td>流程编号</td>
				<td>流程类型</td>
				<td>用户昵称</td>
				<td>销售名称</td>
				<td>财务审核 / 审核人</td>
				<td>操作审核 / 审核人</td>
				<td>流程状态</td>
			</tr>
			<tr>
				<td><?php echo $FlowRes['flow_id']?></td>
				<td><?php echo $FlowRes['flow_name']?></td>
				<td><?php echo $FlowRes['flow_username'];?></td>
				<td><?php echo $FlowRes['flow_admin_name'];?></td>
				<td><?php echo $FlowRes['flow_money_auditor_name'] ? $FlowRes['flow_money_audit_status'].' / '.$FlowRes['flow_money_auditor_name'] : $FlowRes['flow_money_audit_status'].' / --'?></td>
				<td><?php echo $FlowRes['flow_operate_auditor_name'] ? $FlowRes['flow_operate_audit_status'].' / '.$FlowRes['flow_operate_auditor_name'] : $FlowRes['flow_operate_audit_status'].' / --'?></td>
				<td><?php echo $FlowRes['flow_status'];?></td>
			</tr>		
		</table>
	</div>
	<div class="info-title">查看记录</div>
	<div class="info-box">
		<?php $visit_count = json_decode($FlowRes['flow_lookcount'], true);?>
		<?php foreach($visit_count as $key => $val):?>
			<div class="visitor"><div class="visit_name"><?php echo $val['admin_name']?></div><div>/</div><div class="visit_count"><?php echo $val['admin_count'];?>次</div></div>
		<?php endforeach;?>
		
		<div class="clearfix"></div>
	</div>
	<div class="info-title">财务审核</div>
	<div class="info-box">
		<table class="table money-item table-bordered">
			<?php $moneyinfo = json_decode($FlowRes['flow_money_info'], true);?>
			<tr class="title_tr">
				<td colspan="6">业务退款信息明细</td>
			</tr>
			<tr>
				<td>退款金额</td>
				<td>退款业务</td>
				<td>业务服务商</td>
				<td>业务配置</td>
				<td>业务IP</td>
				<td>退款原因</td>
			</tr>
			<tr>
				<td><?php echo $moneyinfo['money_num'];?></td>
				<td><?php echo $FlowDetailRes['flow_unionid'];?></td>
				<?php $config = json_decode($FlowDetailRes['flow_after_config'], true);?>
				<td>
					<?php 
						$html = '';
						$pdtinfo = Yii::$app->db->createCommand("select * from member_pdt where unionid = '".$FlowDetailRes['flow_unionid']."'")->queryOne();
						$server_name = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$pdtinfo['server_type_id']."'")->queryScalar();
						$pdt_name = Yii::$app->db->createCommand("select name from pdt_manage where id='".$pdtinfo['pdt_id']."'")->queryScalar();
						
						if($pdtinfo['idle_id']) {
							#自有服务器
							$html .= '<span class="idle_server">自有服务器</span>';
						} else {
							if($pdtinfo['provider_id']) {
								$provider = Yii::$app->db->createCommand("select name from provider where id = '".$config['provider_id']."'")->queryScalar();
								$html .= '<div class="supplier_server">供应商 / '.$provider.'</div>';
							} else {
								$html .= '<div class="supplier_server">供应商</div>';
							}
							
						}
						
						$html .= '<br/><br/>'.$server_name.'<br/>'.$pdt_name;
						echo $html;
					?>
				</td>
				<td>
					<?php 
						$configArr = json_decode($FlowDetailRes['flow_front_config'], true);
					?>
					<div class="config_item"><div class="config_title">CPU</div><div class="config_content"><?php echo $configArr['cpu']?></div></div>
					<div class="config_item"><div class="config_title">内存</div><div class="config_content"><?php echo $configArr['ram']?></div></div>
					<div class="config_item"><div class="config_title">硬盘</div><div class="config_content"><?php echo $configArr['hdd']?></div></div>
					<div class="config_item"><div class="config_title">实际带宽</div><div class="config_content"><?php echo $configArr['configbandwidth']?></div></div>
					<div class="config_item"><div class="config_title">要求带宽</div><div class="config_content"><?php echo $configArr['requirement_bandwidth']?></div></div>
					<div class="config_item"><div class="config_title">IP数</div><div class="config_content"><?php echo $configArr['ipnumber']?></div></div>
					<div class="config_item"><div class="config_title">防御流量</div><div class="config_content"><?php echo $configArr['defense']?></div></div>
					<div class="config_item"><div class="config_title">操作系统</div><div class="config_content"><?php echo $configArr['operatsystem']?></div></div>
				</td>
				<td>
					<?php 
						$ip = json_decode($FlowDetailRes['flow_ip'], true);
					?>
					<?php echo count($ip) > 1 ? $ip[0].'<br/><a href="javascript:;" class="lookallip">查看全部IP</a>' : $ip[0];?>
					<input type="hidden" class="allipbox" value="<?php echo implode(',', $ip);?>"/>
				</td>
				<td colspan="2"><?php echo $moneyinfo['refund_reason'];?></td>
			</tr>
			<?php if($FlowRes['flow_money_audit_status'] == '等待审核'):?>
				<?php if(DataHelper::search_array($adminRole, explode(',', $FlowRes['flowrole']['wr_money_verify_group']))):?>
					<tr>
						<td colspan="6"><a href="javascript:;" class="verify_btn money_success verify_success">同意退款</a><a href="javascript:;" class="verify_btn money_fail verify_fail">驳回退款</a></td>
					</tr>
				<?php endif;?>
			<?php elseif($FlowRes['flow_money_audit_status'] == '通过'):?>
				<tr>
					<td colspan="6">
						审核结果：<span class="verify_result_success">同意退款</span>
						<br/>
						审核时间：<?php echo date("Y-m-d H:i", $FlowRes['flow_money_auditor_time'])?>
						<br/>
						审核人：<?php echo $FlowRes['flow_money_auditor_name'];?>
						<br/>
						审核理由：<?php echo $FlowRes['flow_money_auditor_reason']?>
					</td>
				</tr>
			<?php elseif($FlowRes['flow_money_audit_status'] == '驳回'):?>
				<tr>
					<td colspan="6">
						审核结果：<span class="verify_result_fail">驳回退款</span>
						<br/>
						审核时间：<?php echo date("Y-m-d H:i", $FlowRes['flow_money_auditor_time'])?>
						<br/>
						审核人：<?php echo $FlowRes['flow_money_auditor_name'];?>
						<br/>
						审核理由：<?php echo $FlowRes['flow_money_auditor_reason']?>
					</td>
				</tr>
			<?php elseif($FlowRes['flow_money_audit_status'] == '无需审核'):?>
				<tr>
					<td colspan="6">
						<span style="color:red">*注：当前操作无法设定为 <无需审核>, 请先 <中止流程> 再联系管理员设定权限后重新提起</span>
					</td>
				</tr>
			<?php endif;?>
		</table>
		<div class="clearfix"></div>
	</div>
	<!--返回列表  返回前页-->
	<div class="row" style="float: right;margin-right: 30px;margin-bottom: 20px;">								
		<div class="pull-right">
			<button class="btn btn-warning btn-sm" style="width:120px" onclick="window.history.back()">
				<i class="ace-icon fa fa-reply icon-only"></i>&nbsp;返回上一页
			</button> 
			<button class="btn btn-info  btn-sm" style="width:150px" onclick="location.href='<?php echo Url::to(['waiting-list'])?>'">
				<i class="ace-icon fa fa-reply icon-only"></i>&nbsp;返回待处理列表
			</button> 
		</div>
	</div>
	
</div>
<div id="LookIPlist" style="display:none;"></div>


<script type="text/javascript">
$(function() {
	
	
	//查看所有IP
	$(".lookallip").click(function() {
		var allIp = $(this).next('.allipbox').val();
		var ipArray = allIp.split(',');
		
		$("#LookIPlist").html('');
		
		for(var i in ipArray) {
			$("#LookIPlist").append('&nbsp;&nbsp;' + ipArray[i] + '<br/>');
		}

		layer.open({
			type: 1, 
			area: ['400px', '300px'],
			title:"" || "查看所有IP",
			content: $("#LookIPlist"),
			maxmin: true, //最大化按钮
			anim:3, //动画
			btn: ['确定', '关闭'],
			yes: function(index, layero) {
				layer.close(index);
			} 
		});
	});
	
	
	//审核金额
	$(".verify_btn").click(function(e) {
		if($(this).hasClass('money_success')) {
			//金额审核通过，直接提交后台
			var index = layer.confirm("确定这笔款项已经收到了吗？", {btn: ['确定', '取消'], title:"金额审核再次确认"}, function() {
				layer.close(index);
				
				var loading = layer.load(1, {shade: [0.7, '#fff']});
				var url = "<?php echo Url::to(["work-flow/verify-money-result"])?>";
				var flow_id = "<?php echo $FlowRes['flow_id']?>";
				$.post(url, {"result":"Y", "flow_id":flow_id}, function(e) {
					layer.close(loading);
					if(e.data.status == 1) {
						layer.alert("审核结果处理完成", {icon:1}, function() {
							window.location.reload();
						});
					} else {
						layer.alert(e.data.info, {icon:7});
					}
				}, "json");
			});
			
		} else if($(this).hasClass('money_fail')) {
			//金额审核驳回，填写原因
			layer.prompt({title: '请写下驳回理由', formType: 2}, function(text, index) {
				layer.close(index);
				
				var loading = layer.load(1, {shade: [0.7, '#fff']});
				var url = "<?php echo Url::to(["work-flow/verify-money-result"])?>";
				var flow_id = "<?php echo $FlowRes['flow_id']?>";
				
				$.post(url, {"result":"N", "flow_id":flow_id, "reason":text}, function(e) {
					layer.close(loading);
					if(e.data.status == 1) {
						layer.alert("审核结果处理完成", {icon:1}, function() {
							window.location.reload();
						});
					} else {
						layer.alert(e.data.info, {icon:7});
					}
				}, "json");
			});
			
			
		} else {
			layer.alert("参数错误", {icon:2});
			return false;
		}
	});
	
});
function checkRate(number) {
　　var re = /^[0-9]+.?[0-9]*$/; 
　　if (!re.test(number)) {
　　　　return false;
　　} else {
		return true;
	}
}
</script>
<?php $this->endBlock(); ?>
