<?php
use yii\helpers\Url;
use yii\helpers\Html;
use addons\VymDesen\common\components\DataHelper;
$this->title = '控制台管理 - 工作记录 - 工作记录详情 - 预配置产品';
?>
<?php $this->beginBlock('content') ?>
<style type="text/css">
	*{margin:0;padding:0;}
	ul,li{list-style:none;margin:0;padding:0;}
	a{text-decoration:none;}
	.clearfix{clear:both;}

	.info-title{height:40px;width:100%;line-height:50px;font-weight:bold;border-bottom:2px solid rgba(232,233,234,1);margin-bottom:10px;}
	.info-box{margin:40px 0px;padding:0 20px;margin-top:30px;}
	.info-box .customertable{border:none;width:700px;}
	.info-box .customertable tr{border:none;}
	.info-box .customertable tr td{border:none;padding:4px;}
	.info-box .customertable tr td:nth-child(odd){width:100px;text-align:right;}

	.info-box .orderPreInfo{border-bottom:2px solid rgba(232,233,234,1);padding-bottom:20px;margin-bottom:20px;}

	.info-box .chooseBox{height:50px;}
	.info-box .chooseBox .check_provider{float:left;display:block;height:40px;padding:0px 10px;border:1px #6FB3E0 solid;text-align:center;line-height:40px;border-radius:3px;-webkit-border-radius:3px;margin-right:20px;margin-top:5px;text-decoration:none;color:#6FB3E0;}
	.info-box .chooseBox .active{background:#6FB3E0!important;color:#fff!important;}

	.table{margin-top:20px;}
	.table .table-title{width:138px;text-align:right;}
	.table .table-sm-info{width:200px;}
	.table tr td{line-height:30px!important;}
	.table tr td input{height:30px;line-height:30px;}
	.table tr td .ipinput{height:30px;line-height:30px;text-align:left;margin-right:10px;margin-bottom:10px;}
	.table tr td .fact_price{width:80px;}
	.table tr td .trade_num{width:40px;}

	.chooseTestServer{padding:0 20px;background:#87b87f !important;color: #ffffff;display:block;text-decoration:none;float:left;cursor:pointer;}
	.updateConfig{padding:0 20px;display:block;background:#9585bf !important;color:#fff;text-decoration:none;cursor:pointer;float:left;}

	.updatebox{margin-bottom:10px;}
	.updatebox .total-box{float:left;display:inline-block;margin-right:30px;}
	.updatebox .total-box div{float:left;height:35px;line-height:35px;display:block;}
	.updatebox .total-box input{float:left;height:35px;line-height:35px;}
	.updatebox .total-box .tips{color:#f00;width:100%;}
	.main_price_total, .main_price_fact, .main_pay_money, .main_pay_remark{margin-right:20px;font-size:20px;line-height:30px!important;}
	.main_price_total{color:#2b7dbc;}
	.main_price_fact{color:rgba(221, 55, 81, 1)}

	.off{display:none;}

	.updateConfigTable tr td:first-child{text-align:right;width:150px;}
	.updateConfigTable tr td:last-child input{width:230px;}
	.updateConfigTable td{border-top:none!important;}
	.updateConfigTable td input{height:40px;line-height:40px;}

	.addIp{float:left;display:block;height:30px;line-height:30px;padding:0 15px;background:#87b87f !important;color:#fff;text-align:center;cursor:pointer;margin-right:20px;text-decoration:none;border:none;outline:none;}
	.ipmodel{margin-right:30px;float:left;margin-bottom:10px;}
	.ipmodel input{float:left;}
	.ipmodel a{display:block;float:left;width:30px;height:30px;text-align:center;margin-left:5px;background:#d9534f;color:#fff;}

	.submitbox{height:40px;line-height:40px;}
	.submitbox a{height:40px;padding:0 15px;display:block;float:left;text-align:center;text-decoration:none;background:#2b7dbc;color:#fff;margin-right:20px;border-radius:5px;-webkit-border-radius:5px;}
	.submitbox a:last-child{background:#a0a0a0;}

	.remark{width:100%;height:96px;resize:none;border:none;}

</style>

<!-- 修改配置详情 -->
<div id="updateSupplierConfig" class="off">
	<table class="table updateConfigTable">
		<tr>
			<td>CPU：</td>
			<td><input type="text" name="" class="uc_cpu" value="" /></td>
		</tr>
		<tr>
			<td>内存大小：</td>
			<td><input type="text" name="" class="uc_ram" value="" /></td>
		</tr>
		<tr>
			<td>硬盘大小：</td>
			<td><input type="text" name="" class="uc_hdd" value="" /></td>
		</tr>
		<tr>
			<td>带宽大小：</td>
			<td><input type="text" name="" class="uc_bandwidth" value="" /></td>
		</tr>
		<tr>
			<td>IP可用数：</td>
			<td><input type="text" name="" class="uc_ipnumber" value="" /></td>
		</tr>
		<tr>
			<td>防御流量：</td>
			<td><input type="text" name="" class="uc_defense" value="" /></td>
		</tr>
		<tr>
			<td>操作系统：</td>
			<td><input type="text" name="" class="uc_system" value="" /></td>
		</tr>
	</table>
</div>


<div id="mainContainer">
	<div class="info-title">选择配置</div>
	<div class="info-box">
		<div class="orderPreInfo">
			<?php
            $html = '';
				$needBandwidth = '';
				$targetConfig = json_decode($DetailInfo['detail_content'], true);
				$orderpdtname = Yii::$app->db->createCommand("select name from pdt_manage where id = '".$targetConfig["pdt_id"]."'")->queryScalar();
				$orderservername = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '".$targetConfig["server_type_id"]."'")->queryScalar();

				$payment = '';
				switch($targetConfig['payment_cycle']) {
					case "1":
						$payment = '月付';
						break;
					case "3":
						$payment = '季付';
						break;
					case "6":
						$payment = '半年付';
						break;
					case "12":
						$payment = '年付';
						break;
					default:
						$payment = '月付';
				}

            $needBandwidth     = $targetConfig['config']['configbandwidth'];
            $orderdefense      = $targetConfig['config']['defense'] ? $targetConfig['config']['defense'] : 'N/A';
            $orderoperatsystem = $targetConfig['config']['operatsystem'] ? $targetConfig['config']['operatsystem'] : 'N/A';
            $arr               = [$orderservername, $orderpdtname, $payment];
            $html              = '分类：' . implode('/', array_filter($arr));
            is_numeric($targetConfig['config']['ipnumber']) && $targetConfig['config']['ipnumber'] = $targetConfig['config']['ipnumber'] . '个';
            $html              .= '<br/><span class="notranslate">配置：</span>' . $targetConfig['config']['cpu'] . ' / ' . $targetConfig['config']['ram'] . ' / ' . $targetConfig['config']['hdd'] . ' / ' . $targetConfig['config']['configbandwidth'] . ' / ' . $targetConfig['config']['ipnumber'] . ' / ' . $orderdefense . ' / ' . $orderoperatsystem . '<br/><br/><span class="notranslate">备注</span>：' . $DetailInfo['detail_remark'] . '</td>';
            echo $html;
			?>
		</div>

		<div class="chooseBox"><a href="javascript:;" class="check_provider <?php echo $nowprovider == 0 ? ' active ' : ''?> " data-id="0">选择自有配置</a><a href="javascript:;" class="check_provider <?php echo $nowprovider == 1 ? ' active ' : ''?> " data-id="1">选择供应商配置</a></div>
		<input type="hidden" name="testid" class="idle_testid"  value=""/>
		<input type="hidden" name="testid" class="supplier_testid"  value=""/>
		<!-- 自有 -->
		<table class="table table-bordered idle-model <?php echo $nowprovider == 0 ? '' : ' off '?> ">
			<tr>
				<td class="table-title">选择测试机：</td>
				<td colspan="7"><div class="chooseTestServer" provider="0">选择测试服务器</div></td>
			</tr>
			<tr>
				<td class="table-title">服务器分类：</td>
				<td class="table-sm-info">
					<select name="server_type_id" class="idle_servertype">
						<option value="">请选择服务器分类</option>
						<?php foreach($pdt_type_list as $key => $val):?>
							<option value="<?php echo $val['type_id']?>" <?php echo $val['type_id'] == $server_type_id ? 'selected' : ''?> ><?php echo $val['type_name'];?></option>
						<?php endforeach;?>
					</select>
				</td>
				<td class="table-title">选择机房：</td>
				<td class="table-sm-info">
					<select name="room_id" class="idle_room">
						<option value="">请选择机房</option>
						<?php foreach($room_list as $key => $val):?>
							<?php if($val['provider'] == 0):?>
								<option value="<?php echo $val['id']?>" ><?php echo $val['name'];?></option>
							<?php endif;?>
						<?php endforeach;?>
					</select>
				</td>
				<td class="table-title">选择产品配置：</td>
				<td class="">
					<input type="text" name="" value="" class="idle_pdts" readonly />
					<input type="hidden" name="idle_id" class="idle_id" value="" >
				</td>
			</tr>
			<tr>
				<td class="table-title">配置详情：</td>
				<td colspan="5" class="idle_config"></td>
			</tr>
			<tr>
				<td class="table-title">要求带宽：</td>
				<td class="table-sm-info"><input type="text" name="" value="<?php echo $needBandwidth;?>" class="idle_needband"></td>
				<td class="table-title">实际带宽：</td>
				<td class="table-sm-info idle_trueband" colspan="3"></td>
			</tr>
			<tr>
				<td class="table-title">IP地址：</td>
				<td colspan="5" class="idle_iplist">
				</td>
			</tr>
			<tr id="server_id_self">
				<td class="table-title">供应商产品ID：</td>
				<td colspan="5">
					<input type="text" name="" class=".server_id_self" value="" placeholder="填写供应商产品ID" />
				</td>
			</tr>
			<tr>
				<td class="table-title">生效时间：</td>
				<td colspan="7">
					<input type="text" value="" class="idle_starttime" id="idle_starttime" readonly placeholder="选择生效时间" />
				</td>
			</tr>
            <tr>
                <td class="table-title">选择安装系统：</td>
                <td colspan="5">
                    <select name="operating_system" class="idle_operating_system">
                        <option value="">请选择重装操作系统</option>
                        <?php foreach($Operatsystemlist as $key => $val):?>
                            <option value="<?php echo $val['name']?>"><?php echo $val['name'];?></option>
                        <?php endforeach;?>
                    </select>
                </td>
            </tr>
			<tr>
				<td class="table-title">备注：</td>
				<td colspan="5"><textarea class="remark idle_remark" placeholder="如果有事项备注，请写在这里"></textarea></td>
			</tr>
		</table>

		<!-- 供应商 -->
		<table class="table table-bordered supplier-model <?php echo $nowprovider == 1 ? '' : ' off '?> ">
			<tr>
				<td class="table-title">选择测试机：</td>
				<td colspan="7"><div class="chooseTestServer" provider="1">选择测试服务器</div></td>
			</tr>
			<tr>
				<td class="table-title">服务器分类：</td>
				<td class="table-sm-info">
					<select name="server_type_id" class="supplier_servertype">
						<option value="">请选择服务器分类</option>
						<?php foreach($pdt_type_list as $key => $val):?>
							<option value="<?php echo $val['type_id']?>" <?php echo $val['type_id'] == $server_type_id ? 'selected' : ''?> ><?php echo $val['type_name'];?></option>
						<?php endforeach;?>
					</select>
				</td>
				<td class="table-title">选择产品配置：</td>
				<td class="table-sm-info">
					<select name="pdt_id" class="supplier_pdttype" stid="">

					</select>
				</td>
				<td class="table-title">选择机房：</td>
				<td class="table-sm-info">
					<select name="room_id" class="supplier_room">

					</select>
				</td>
				<td class="table-title">选择供应商：</td>
				<td>
					<select name="provider" class="supplier_provider">

					</select>
				</td>
			</tr>
			<tr>
				<td class="table-title">配置详情：</td>
				<td colspan="4" class="supplier_config"></td>
				<td colspan="4"><div class="updateConfig">点击修改配置</div></td>
			</tr>
			<tr>
				<td class="table-title">要求带宽：</td>
				<td class="table-sm-info"><input type="text" name="" value="<?php echo $needBandwidth;?>" class="supplier_needband"></td>
				<td class="table-title">实际带宽：</td>
				<td class="table-sm-info supplier_realband" colspan="5"></td>
			</tr>
			<tr>
				<td class="table-title">IP地址：</td>
				<td colspan="7" class="supplier_ipbox">
					<button class="addIp">增加IP</button>
				</td>
			</tr>
			<tr>
				<td class="table-title">供应商产品ID：</td>
				<td colspan="5">
					<input type="text" name="" class="server_id_provider" value="" placeholder="填写供应商产品ID" />
				</td>
			</tr>
			<tr>
				<td class="table-title">生效时间：</td>
				<td colspan="7">
					<input type="text" value="" class="supplier_starttime" id="supplier_starttime" readonly placeholder="选择生效时间" />
				</td>
			</tr>
			<tr id="supplierAccountInfo">
				<td class="table-title">机器账号密码</td>
				<td colspan="7">
					<input name="supplier_account" type="text" value="" placeholder="账号">&nbsp;
					<input name="supplier_pass" type="text" value="" placeholder="密码">&nbsp;
					<input name="supplier_port" type="text" value="" placeholder="端口">&nbsp;
				</td>
			</tr>
            <tr>
                <td class="table-title">选择安装系统：</td>
                <td colspan="5">
                    <select name="operating_system" class="supplier_operating_system">
                        <option value="">请选择重装操作系统</option>
                        <?php foreach($Operatsystemlist as $key => $val):?>
                            <option value="<?php echo $val['name']?>"><?php echo $val['name'];?></option>
                        <?php endforeach;?>
                    </select>
                </td>
            </tr>
			<tr>
				<td class="table-title">备注：</td>
				<td colspan="7"><textarea class="remark supplier_remark" placeholder="如果有事项备注，请写在这里"></textarea></td>
			</tr>
		</table>
		<div class="submitbox">
			<a class="blue" href="javascript:;" id="submitNowConfig">提交当前配置</a>
			<a class="blue" href="<?php echo Url::to(['pipe-line/pipe-line-detail', 'line_id' => $line_id])?>">返回上级菜单</a>
		</div>
	</div>
</div>


<script type="text/javascript">
	var supplier_config = {};
	var isTestServer = false;
	var idle_starttime = {
		elem: '#idle_starttime',
		format: 'YYYY-MM-DD',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas){

		}
	};
	laydate(idle_starttime);
	laydate.skin('molv');
	$('#idle_starttime').val(laydate.now(0, 'YYYY-MM-DD'));

	var supplier_starttime = {
		elem: '#supplier_starttime',
		format: 'YYYY-MM-DD',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas){

		}
	};
	laydate(supplier_starttime);
	laydate.skin('molv');

	$('#supplier_starttime').val(laydate.now(0, 'YYYY-MM-DD'));

	$(function() {

		/* 切换自有服务器还是供应商 */
		$(".check_provider").click(function() {
			var provider = $(this).attr('data-id');
			$(".check_provider").removeClass('active');
			$(this).addClass('active');

			if(provider == 0) {
				$(".supplier-model").addClass('off');
				$(".idle-model").removeClass('off');
			} else {
				//初始化一开始就触发更新产品配置类别
				$(".supplier_servertype").change();
				//获取供应商和供应商机房
				var url = "<?php echo Url::to(['pipe-line/get-supplier-info'])?>";
				$.post(url, {}, function(e) {
					var roomHtml = '<option value="">请选择所属机房</option>';
					for(var i in e.data.PdtRoomList) {
						roomHtml += '<option value="'+e.data.PdtRoomList[i].id+'">'+e.data.PdtRoomList[i].name+'</option>';
					}
					$(".supplier_room").html(roomHtml);

					var providerHtml = '<option value="">请选择所属供应商</option>';
					for(var i in e.data.ProviderList) {
						providerHtml += '<option value="'+e.data.ProviderList[i].id+'">'+e.data.ProviderList[i].name+'</option>';
					}
					$(".supplier_provider").html(providerHtml);
				}, "json");

				$(".supplier-model").removeClass('off');
				$(".idle-model").addClass('off');
			}
		});

		//自有服务器切换机房，服务器分类触发
		$(".idle_servertype, .idle_room").change(function() {
			$(".idle_pdts").val('');
			$(".idle_config").html('');
			$(".idle_trueband").html('');
			$(".idle_iplist").html('');
			$(".idle_testid").val('');
		});

		//自有列表选择产品
		$(".idle_pdts").click(function() {
			var room_id = $('.idle_room option:selected').val();
			var server_type_id = $('.idle_servertype option:selected').val();
			var servicerprovider = <?php echo $nowprovider;?>;
			var url = '<?php echo Url::to(['pipe-line/select-idle'])?>';


			if(room_id == '' || server_type_id == ''){
				layer.alert("请先选择服务器分类和机房", {icon:7});
				return false;
			}

			var idleurl = url+"?room_id="+room_id+"&servicerprovider="+servicerprovider+"&server_type_id="+server_type_id;

			// 正常打开
			layer.open({
				type: 2,
				area: ['1020px', '600px'],
				title:"" || "选择闲置服务器",
				content: idleurl,
				maxmin: true, //最大化按钮
				anim:3, //动画
				btn: ['确定', '关闭'],
				yes: function(index, layero){ //或者使用btn1
					var iframeWin = layero.find('iframe')[0];//得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
					var body = $(iframeWin).contents();
					var radio = $(body).find(".checkIdle:checked");

					/* 赋值自有id和名称 */

					$(".idle_id").val(radio.val());
					$(".idle_pdts").val(radio.data("name"));
					$(".idle_testid").val('');

					var id = radio.val();

					var url = "<?php echo Url::to(['pipe-line/select-idle-info']) ?>";
					$.post(url, {id:id}, function(e) {
						var result = e.data;
						result.config.operatsystem = result.config.operatsystem ? result.config.operatsystem : 'N/A';
						result.config.defense = result.config.defense ? result.config.defense : 'N/A';
						var configHtml = result.config.cpu+' / '+result.config.ram+' / '+result.config.hdd+' / '+result.config.configbandwidth+' / '+result.config.ipnumber+' / '+result.config.defense+' / '+result.config.operatsystem;
						$(".idle_config").html(configHtml);//配置详情
						$(".idle_trueband").html(result.config.configbandwidth);//实际带宽

						var ipHtml = '';
						for(var i in result.ip) {
							ipHtml += '<input type="text" name="idle_ips[]" value="'+result.ip[i]+'" class="ipinput" disabled >';
						}

						$(".idle_iplist").html(ipHtml);
						//server_id_self
						if(result.data.servicerprovider && result.data.servicerprovider != '0'){
							//是供应商服务器
							$("#server_id_self").css('display', '');
							$("#server_id_self input").val(result.data.server_id + '');
						}
						else {
							//自有
							$("#server_id_self").css('display', 'none');
							$("#server_id_self input").val('');
						}
					},'json');

					layer.close(index);
				},
				cancel: function(index) {

				}
			});
		});
		//更换配置类别清空testid...
		$(".supplier_pdttype").change(function() {
			if(isTestServer == false) {
				if($(".supplier_testid").val()){
					$(".addIp").css('display', '');
					$(".supplier_ipbox").children('.ipmodel').remove();
					$(".supplier_room").val("");
					$(".supplier_provider").val("");
				}
			}
			$(".supplier_testid").val('');
		});

		//供应商切换服务器分类
		$(".supplier_servertype").change(function() {
			$(".supplier_room").val("");
			$(".supplier_provider").val("");
			$(".supplier_realband").html("");
			$(".supplier_config").val("");
			$(".supplier_ipbox").children('.ipmodel').remove();
			$(".supplier_testid").val('');
			$(".addIp").css('display', '');
			var url = "<?php echo Url::to(['pipe-line/servertype-change'])?>";
			var server_type_id = $(this).val();
			if(!server_type_id) {
				return false;
			}

			$.post(url, {"id":server_type_id}, function(e) {
				var pdttypeHtml = '<option value="">请选择产品配置类别</option>';
				for(var i in e.data) {
					pdttypeHtml += '<option value="'+e.data[i].id+'">'+e.data[i].name+'</option>';
				}
				$(".supplier_pdttype").html(pdttypeHtml);
				$(".supplier_pdttype").attr("stid", server_type_id);

				//配置类别触发切换，去搜索配置并赋值
				$(".supplier_pdttype").change(function() {
					$(".supplier_realband").html("");
					$(".supplier_config").val("");
					var pdttypeid = $(this).val();
					if(!pdttypeid) {
						return false;
					}
                    if(supplier_config && supplier_config.cpu){
                        $(".supplier_config").html(supplier_config.cpu + ' / ' + supplier_config.ram + ' / ' + supplier_config.hdd + ' / ' + supplier_config.configbandwidth + ' / ' + supplier_config.ipnumber + ' / ' + supplier_config.defense + ' / ' + supplier_config.operatsystem);
                        $(".uc_cpu").val(supplier_config.cpu);
                        $(".uc_ram").val(supplier_config.ram);
                        $(".uc_hdd").val(supplier_config.hdd);
                        $(".uc_bandwidth").val(supplier_config.requirement_bandwidth);
                        $(".uc_ipnumber").val(supplier_config.ipnumber);
                        $(".uc_defense").val(supplier_config.defense);
                        $(".uc_system").val(supplier_config.operatsystem);
                        $(".supplier_realband").html(supplier_config.configbandwidth);
                    }else{
                        var pdturl = "<?php echo Url::to(['pipe-line/pdttype-info'])?>";
                        $.post(pdturl, {"id":pdttypeid}, function(o) {
                            $(".supplier_config").html(o.data.cpu+' / '+o.data.ram+' / '+o.data.hdd+' / '+o.data.bandwidth+' / '+o.data.ipnumber+' / '+o.data.defense+' / '+o.data.system);
                            $(".uc_cpu").val(o.data.cpu);
                            $(".uc_ram").val(o.data.ram);
                            $(".uc_hdd").val(o.data.hdd);
                            $(".uc_bandwidth").val(o.data.bandwidth);
                            $(".uc_ipnumber").val(o.data.ipnumber);
                            $(".uc_defense").val(o.data.defense);
                            $(".uc_system").val(o.data.system);
                            $(".supplier_realband").html(o.data.bandwidth);
                        }, "json");
                    }
				});
			}, "json");
		});

		//打开修改配置的弹出层
		$(".updateConfig").click(function() {
			var pdt_id = $(".supplier_pdttype").val();
			var server_type_id = $(".supplier_servertype").val();
			if(!pdt_id || !server_type_id) {
				layer.alert("必须选择服务器分类和产品配置", {icon:7});
				return false;
			}
			layer.open({
				type: 1,
				area: ['500px', '500px'],
				title:"" || "修改机器配置",
				content: $("#updateSupplierConfig"),
				maxmin: true, //最大化按钮
				anim:3, //动画
				btn: ['确定', '取消'],
				yes: function(index, layero) {
					var cpu = $(".uc_cpu").val();
					var ram = $(".uc_ram").val();
					var hdd = $(".uc_hdd").val();
					var bandwidth = $(".uc_bandwidth").val();
					var ipnumber = $(".uc_ipnumber").val();
					var defense = $(".uc_defense").val();
					var system = $(".uc_system").val();
					var configHtml = cpu+' / '+ram+' / '+hdd+' / '+bandwidth+' / '+ipnumber+' / '+defense+' / '+system;
					$(".supplier_config").html(configHtml);
					$(".supplier_realband").html($(".uc_bandwidth").val());
					layer.close(index);
				},
				cancel: function(index) {

				}
			});
		});

		//实际带宽变动事件 uc_bandwidth
		$('.uc_bandwidth').bind('input propertychange', function() {
			$(".supplier_realband").html($(this).val());
		});
		//新增供应商IP
		$(".addIp").click(function() {
			$(".supplier_ipbox").append('<div class="ipmodel"><input type="text" name="" value="" /><a href="javascript:;" class="removeIp"><i class="ace-icon glyphicon glyphicon-remove"></i></a></div>');
			$(".removeIp").click(function() {
				$(this).parent('.ipmodel').remove();
			});
		});

		//选择测试机
		$(".chooseTestServer").click(function() {
			var provider = $(this).attr('provider');
			var title = '';
			if(provider == 0) {
				title = '选择测试服务器（自有）';
			} else {
				title = '选择测试服务器（供应商）';
			}
			// 正常打开
			layer.open({
				type: 2,
				area: ['1020px', '600px'],
				title:"" || title,
				content: "<?php echo Url::to(['pipe-line/select-test'])?>"+"?servicerprovider="+provider,
				maxmin: true, //最大化按钮
				anim:3, //动画
				btn: ['确定', '关闭'],
				yes: function(index, layero){ //或者使用btn1
					var iframeWin = layero.find('iframe')[0];//得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
					var body = $(iframeWin).contents();
					var radio = $(body).find(".checkTest:checked");
					var testid = radio.val();


					var url = "<?php echo Url::to(['pipe-line/select-test-info'])?>";

					$.post(url, {"testid":testid, "provider":provider}, function(e) {

						if(provider == 0) {
							var lindx = layer.load(1, {
								shade: [0.7,'#fff'] //0.1透明度的白色背景
							});
							//自有服务器测试机选择
							$(".idle_id").val(e.data.data.idle_id);
							$(".idle_servertype").val(e.data.data.server_type_id).change();
							$(".idle_room").val(e.data.data.room_id).change();
							$(".idle_pdts").val(e.data.data.idlepdt[0].idle_name);
							var idle_config = JSON.parse(e.data.data.config);
							$(".idle_config").html(idle_config.cpu+' / '+idle_config.ram+' / '+idle_config.hdd+' / '+idle_config.configbandwidth+' / '+idle_config.requirement_bandwidth+' / '+idle_config.ipnumber+' / '+idle_config.defense+' / '+idle_config.operatsystem);
							$(".idle_needband").val(idle_config.requirement_bandwidth);
							$(".idle_trueband").html(idle_config.configbandwidth);

							var ipArr = JSON.parse(e.data.data.ip);
							var iplist = '';
							for(var i in ipArr) {
								iplist += '<input type="text" name="idle_ips[]" value="'+ipArr[i]+'" class="ipinput" disabled >'
							}
							$(".idle_iplist").html(iplist);
							layer.close(lindx);
							$(".idle_testid").val(testid);
						} else {
							//供应商服务器测试机选择
                            supplier_config = JSON.parse(e.data.data.config);

                            console.log('supplier_config',supplier_config)

                            $(".supplier_servertype").val(e.data.data.server_type_id).change();
                            var lindx = layer.load(1, { shade: [0.7,'#fff'] });//0.1透明度的白色背景
                            isTestServer = true;
                            var timeOut = setInterval(function() {
                                if($(".supplier_pdttype").attr("stid") == e.data.data.server_type_id) {
                                    $(".supplier_pdttype").val(e.data.data.pdt_id).change();
                                    $(".supplier_testid").val(testid);
                                    isTestServer = false;
                                    layer.close(lindx);
                                    clearInterval(timeOut);
                                }
                            }, 200);


                            $(".supplier_needband").val(supplier_config.requirement_bandwidth);
                            $(".supplier_realband").html(supplier_config.configbandwidth);

							$(".supplier_room").val(e.data.data.room_id);
							$(".supplier_provider").val(e.data.data.provider_id);
							$('input[name="supplier_account"]').val(e.data.data.account_name);
							$('input[name="supplier_pass"]').val(e.data.data.account_pwd);
							$('input[name="supplier_port"]').val(e.data.data.account_port);
							$("#supplierAccountInfo").show();

							$(".addIp").css('display', 'none');
							//初始化ip列表
							$(".supplier_ipbox").children('.ipmodel').remove();
							var ipArr = JSON.parse(e.data.data.ip);
							var supplier_iplist = '';
							for(var i in ipArr) {
								supplier_iplist += '<div class="ipmodel"><input type="text" name="" value="'+ipArr[i]+'" readonly/></div>'
							}
							//写入ip列表并重新绑定事件
							$(".addIp").after(supplier_iplist);
							$(".removeIp").click(function() {
								$(this).parent('.ipmodel').remove();
							});
						}
					},'json');

					layer.close(index);

				},
				cancel: function(index) {

				}
			});
		});

		//提交当前配置，通过上面模块选择的provider判断
		$("#submitNowConfig").click(function() {
			var provider = $(".check_provider.active").attr('data-id');
			var url = "<?php echo Url::to(['pipe-line/submit-pre-config'])?>";
			var line_id = "<?php echo $line_id?>";
			var detail_id = "<?php echo $DetailInfo['detail_id']?>";
			var data = {};
			if(provider == 0) {
				//自有配置提交

				//服务器分类
				if(!$(".idle_servertype").val()) {
					layer.alert("请选择服务器分类", {icon:7});
					return false;
				} else {
					data.server_type_id = $(".idle_servertype").val();
				}

				//机房选择
				if(!$(".idle_room").val()) {
					layer.alert("请选择机房", {icon:7});
					return false;
				} else {
					data.room_id = $(".idle_room").val();
				}

				//产品库
				if(!$(".idle_id").val()) {
					layer.alert("请选择产品配置", {icon:7});
					return false;
				} else {
					data.idle_id = $(".idle_id").val();
				}

				//客户要求带宽
				if(!$(".idle_needband").val()) {
					layer.alert("请填写客户要求带宽", {icon:7});
					return false;
				} else {
					data.idle_needband = $(".idle_needband").val();
				}

				console.log('____',$("#server_id_self").css('display'), $("#server_id_self input").val())
				if($("#server_id_self").css('display') != 'none' && !$("#server_id_self input").val()){
					layer.alert("请填写供应商IP", {icon:7});
					return false;
				}
				else {
					data.server_id = $("#server_id_self input").val();
				}

				data.test_id = $(".idle_testid").val();
				data.remark = $(".idle_remark").val();
				data.start_date = $("#idle_starttime").val();
                data.operating_system = $(".idle_operating_system").val();

			} else if(provider == 1) {
				//供应商配置提交

				if(!$(".supplier_servertype").val()) {
					layer.alert("请选择服务器分类", {icon:7});
					return false;
				} else {
					data.server_type_id = $(".supplier_servertype").val();
				}

				if(!$(".supplier_pdttype").val()) {
					layer.alert("请选择产品配置", {icon:7});
					return false;
				} else {
					data.pdt_id = $(".supplier_pdttype").val();
				}

				if(!$(".supplier_room").val()) {
					layer.alert("请选择机房", {icon:7});
					return false;
				} else {
					data.room_id = $(".supplier_room").val();
				}

				if(!$(".supplier_provider").val()) {
					layer.alert("请选择供应商", {icon:7});
					return false;
				} else {
					data.provider_id = $(".supplier_provider").val();
				}

				if(!$(".supplier_needband").val()) {
					layer.alert("请填写客户要求带宽", {icon:7});
					return false;
				} else {
					data.supplier_needband = $(".supplier_needband").val();
				}

				if(!$(".supplier_realband").html()) {
					layer.alert("当前配置没有实际带宽，请更换配置或技术管理", {icon:7});
					return false;
				} else {
					data.supplier_realband = $(".supplier_realband").html();
				}

				//获取IP列表
				data.iplist = {};
				for(var i=0; i<$(".supplier_ipbox > .ipmodel").length; i++) {
					data.iplist[i] = $(".supplier_ipbox > .ipmodel:eq("+i+") > input").val();
				}

				if($.isEmptyObject(data.iplist)) {
					layer.alert("请填写供应商IP", {icon:7});
					return false;
				}

				//获取配置
				data.config = {};
				if(!$(".uc_cpu").val()) {
					layer.alert("请填写配置中的 <CPU> 选项", {icon:7});
					return false;
				} else {
					data.config.cpu = $(".uc_cpu").val();
				}

				if(!$(".uc_ram").val()) {
					layer.alert("请填写配置中的 <内存大小> 选项", {icon:7});
					return false;
				} else {
					data.config.ram = $(".uc_ram").val();
				}

				if(!$(".uc_hdd").val()) {
					layer.alert("请填写配置中的 <硬盘大小> 选项", {icon:7});
					return false;
				} else {
					data.config.hdd = $(".uc_hdd").val();
				}

				if(!$(".uc_bandwidth").val()) {
					layer.alert("请填写配置中的 <带宽大小> 选项", {icon:7});
					return false;
				} else {
					data.config.configbandwidth = $(".uc_bandwidth").val();
				}

				if(!$(".uc_ipnumber").val()) {
					layer.alert("请填写配置中的 <IP可用数> 选项", {icon:7});
					return false;
				} else {
					data.config.ipnumber = $(".uc_ipnumber").val();
				}

				if(!$(".uc_defense").val()) {
					layer.alert("请填写配置中的 <防御流量> 选项", {icon:7});
					return false;
				} else {
					data.config.defense = $(".uc_defense").val();
				}

				if(!$(".uc_system").val()) {
					layer.alert("请填写配置中的 <操作系统> 选项", {icon:7});
					return false;
				} else {
					data.config.operatsystem = $(".uc_system").val();
				}

				if(!$('.server_id_provider').val()){
					layer.alert("请填写配置中的 <产品ID> 选项", {icon:7});
					return false;
				}
				else {
					data.server_id = $('.server_id_provider').val();
				}

				data.test_id = $(".supplier_testid").val();
				data.remark = $(".supplier_remark").val();
				data.start_date = $("#supplier_starttime").val();
                data.operating_system = $(".supplier_operating_system").val();

				var supplier_account = $('input[name="supplier_account"]').val();
				var supplier_pass = $('input[name="supplier_pass"]').val();
				var supplier_port = $('input[name="supplier_port"]').val();

				if(!supplier_account || !supplier_pass || !supplier_port) {
					layer.alert("供应商机器，必须填写账号密码", {icon:7});
					return false;
				}

				data.supplier_account = supplier_account;
				data.supplier_pass = supplier_pass;
				data.supplier_port = supplier_port;


			} else {
				layer.alert("未知的模块类型", {icon:7});
				return false;
			}

			layer.confirm("确定要提交你的预配置产品吗? 提交之后仍可修改。", {icon:3,
				btn: ['确定','取消'] //按钮
			}, function() {
				var loading = layer.load(1, {shade: [0.7, '#fff']});
				$.post(url, {"detail_id":detail_id, "line_id":line_id, "data":data, "provider":provider}, function(e) {
					layer.close(loading);
					if(e.data.status == 1) {
						layer.alert("预配置机器完成，点击确认查看详情", {icon:1}, function(eee) {
							window.location.href="<?php echo Url::to(['pipe-line/pipe-line-detail'])?>"+'?line_id='+line_id;
						});
					} else {
						layer.alert(e.data.info, {icon:7});
					}
				}, "json");
			});

		});

	});
</script>
<?php $this->endBlock(); ?>
