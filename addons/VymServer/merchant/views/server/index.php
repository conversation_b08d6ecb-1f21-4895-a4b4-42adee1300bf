<?php

use common\enums\AppEnum;
use common\helpers\ArrayHelper;
use common\helpers\Html;
use common\helpers\MerchantHelper;
use common\helpers\Url;
use yii\grid\GridView;
use yii\widgets\ActiveForm;

/** @var yii\web\View $this */
/** @var yii\data\ActiveDataProvider $dataProvider */
/** @var common\models\base\SearchModel $searchModel */
/** @var array $queryParams 搜索参数 */
/** @var array $clusterList 集群列表 */

$this->title                   = '业务实例';
$this->params['breadcrumbs'][] = $this->title;
?>
<!-- Main content -->
<div class="container-fluid">

    <!-- 搜索筛选区域 -->
    <div class="card search-card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-search mr-1"></i>
                筛选条件
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <?php $form = Html::commonFilterForm();
            ?>
            <!-- 商户筛选 -->
            <?php if (Yii::$app->id == AppEnum::BACKEND): ?>
                <?= MerchantHelper::merchantSelect($form, $searchModel) ?>
            <?php endif; ?>
            <?= $form->field($searchModel, 'user_id')->textInput(['placeholder' => '用户ID',])->label('用户ID') ?>

            <?= $form->field($searchModel, 'memberPdt.ip')->textInput(['placeholder' => 'IP地址'])->label('IP地址') ?>

            <div class="col-sm-12 col-md-6 col-lg-3">
                <?= Html::submitButton('<i class="fas fa-search"></i> 搜索', ['class' => 'btn btn-primary btn-md mr-2',]) ?>
                <?= Html::a('<i class="fas fa-redo"></i> 重置', ['index'], ['class' => 'btn btn-outline-secondary btn-md',]) ?>
            </div>
            <?php ActiveForm::end(); ?>
        </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-server mr-1"></i>
                <?= Html::encode($this->title) ?>
            </h3>
            <div class="card-tools">
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <?= GridView::widget([
                    'dataProvider' => $dataProvider,
                    'tableOptions' => [
                        'class' => 'table table-hover rf-table mb-0',
                    ],
                    'columns'      => [

                        ['attribute' => 'id', 'headerOptions' => ['class' => 'col-md-1']],
                        [
                            'label'          => '所属商户',
                            'headerOptions'  => ['class' => 'col-md-1'],
                            'contentOptions' => ['class' => 'text-left'],
                            'format'         => 'raw',
                            'visible'        => Yii::$app->id == AppEnum::BACKEND, // 只在总后台显示
                            'value'         => function ($model) {
                                if ($model->merchant && $mId = $model->merchant->id) {
                                    if ($mTitle = $model->merchant->title) {
                                        return Html::tag('span', $mTitle, ['class' => 'badge badge-primary']);
                                    }
                                    return Html::tag('span', "ID:{$mId}", ['class' => 'badge badge-primary']);
                                }
                                return Html::tag('span', '平台直属', ['class' => 'badge badge-secondary']);
                            },
                        ],
                        [
                            'attribute'      => 'user_id',
                            'label'          => '用户信息',
                            'headerOptions'  => ['style' => 'width: 120px;'],
                            'contentOptions' => ['class' => 'text-left'],
                            'format'         => 'raw',
                            'value'          => function ($model) {
                                $uid       = $model->user_id;
                                $userName  = '';
                                $userEmail = '';
                                // 从 paymentFlow 中获取用户信息
                                if ($model->member) {
                                    $userName  = $model->member->username ?? '';
                                    $userEmail = $model->member->email ?? '';
                                }

                                $userInfo = "UID: {$uid}";
                                if ($userName) {
                                    $userInfo .= "<br/>用户: {$userName}";
                                }
                                if ($userEmail) {
                                    $userInfo .= "<br/>邮箱: {$userEmail}";
                                }
                                return $userInfo;
                            },
                        ],
                        [
                            'attribute' => 'IP地址',
                            'filter'    => false,
                            'format'    => 'raw',
                            'value'     => function ($model) {
                                $memberPdt = $model->memberPdt;
                                if (empty($memberPdt)) {
                                    return '--';
                                }
                                $ips   = ArrayHelper::getValue($memberPdt, 'ip');
                                $ips   = json_decode($ips, true);
                                $ipStr = implode(' / ', $ips);
                                return $ipStr;
                            },
                        ],
                        [
                            'attribute' => '基础信息',
                            'filter'    => false,
                            'format'    => 'raw',
                            'value'     => function ($model) {
                                $memberPdt = $model->memberPdt;
                                if (empty($memberPdt)) {
                                    return '--';
                                }
                                $typeName    = ArrayHelper::getValue($memberPdt, 'servertype.0.type_name', '--');
                                $roomName    = ArrayHelper::getValue($memberPdt, 'pdtroom.0.name', '--');
                                $productName = ArrayHelper::getValue($memberPdt, 'pdtmanage.0.name', '--');

                                $configStr = "分类：{$typeName} <br/>";
                                $configStr .= "机房：{$roomName} <br/>";
                                $configStr .= "套餐：{$productName} <br/>";
                                return $configStr;
                            },
                        ],
                        [
                            'attribute' => '配置信息',
                            'filter'    => false,
                            'format'    => 'raw',
                            'value'     => function ($model) {
                                $memberPdt = $model->memberPdt;
                                if (empty($memberPdt)) {
                                    return '--';
                                }
                                $config = json_decode($memberPdt->config, true);

                                $cpuName       = ArrayHelper::getValue($config, 'cpu');
                                $ramName       = ArrayHelper::getValue($config, 'ram');
                                $hddName       = ArrayHelper::getValue($config, 'hdd');
                                $ipnumberName  = ArrayHelper::getValue($config, 'ipnumber');
                                $bandwidthName = ArrayHelper::getValue($config, 'configbandwidth');
                                $systemName    = ArrayHelper::getValue($config, 'operatsystem');
                                $defenseName   = ArrayHelper::getValue($config, 'defense');
                                $cardName      = ArrayHelper::getValue($config, 'card');
                                //硬件配置
                                $hardware = [
                                    'cpu'  => $cpuName,
                                    'ram'  => $ramName,
                                    'hdd'  => $hddName,
                                    'card' => $cardName,
                                ];
                                $network  = [
                                    'bandwidth' => $bandwidthName,
                                    'ipnumber'  => $ipnumberName,
                                    'defense'   => $defenseName,
                                ];
                                //移除空值或NULL
                                $hardware    = array_filter($hardware, function ($value) {
                                    return $value !== '' && $value !== null;
                                });
                                $hardwareStr = implode(' / ', $hardware);
                                $network     = array_filter($network, function ($value) {
                                    return $value !== '' && $value !== null;
                                });
                                $networkStr  = implode(' / ', $network);

                                $configStr = "硬件配置：{$hardwareStr} <br/>";
                                $configStr .= "网络配置：{$networkStr} <br/>";
                                $configStr .= "操作系统：{$systemName}";
                                return $configStr;
                            },
                        ],
                        [
                            'attribute' => '售价(元)',
                            'filter'    => false,
                            'format'    => 'raw',
                            'value'     => function ($model) {
                                $memberPdt = $model->memberPdt;
                                return ArrayHelper::getValue($memberPdt, 'sell_price', '--');
                            },
                        ],

                        [
                            'attribute' => '起止时间',
                            'filter'    => false,
                            'format'    => 'raw',
                            'value'     => function ($model) {
                                $memberPdt    = $model->memberPdt;
                                $startTime    = ArrayHelper::getValue($memberPdt, 'start_time');
                                $endTime      = ArrayHelper::getValue($memberPdt, 'end_time');
                                $startTimeStr = !empty($startTime) ? date('Y-m-d', $startTime) : '--';
                                $endTimeStr   = !empty($endTime) ? date('Y-m-d', $endTime) : '--';
                                return "起租时间：{$startTimeStr} <br/>到期时间：{$endTimeStr}";
                            },
                        ],
                        [
                            'attribute' => '机器状态',
                            'filter'    => false,
                            'format'    => 'raw',
                            'value'     => function ($model) {
                                $memberPdt = $model->memberPdt;
                                if (empty($memberPdt)) {
                                    return '--';
                                }
                                $status = ArrayHelper::getValue($memberPdt, 'status');

                                // 检查是否有预约下架
                                $hasPreOff = Yii::$app->db->createCommand("select * from member_pdt_appoint_off where appoint_unionid = :unionid")
                                                          ->bindValue(':unionid', $memberPdt->unionid)
                                                          ->queryOne();

                                if ($hasPreOff) {
                                    return '<span class="badge badge-primary">预约下架 ' . date("m-d", $hasPreOff['appoint_time']) . '</span>';
                                }

                                switch ($status) {
                                    case 1:
                                        return '<span class="badge badge-success">正常</span>';
                                    case -1:
                                        return '<span class="badge badge-danger">已删除</span>';
                                    case 0:
                                        return '<span class="badge badge-warning">未开通</span>';
                                    case -2:
                                        return '<span class="badge badge-warning">退款中</span>';
                                    case 2:
                                        return '<span class="badge badge-warning">变配中</span>';
                                    case 3:
                                        return '<span class="badge badge-warning">换机中</span>';
                                    case 4:
                                        return '<span class="badge badge-warning">换IP中</span>';
                                    case 5:
                                        return '<span class="badge badge-warning">过户中</span>';
                                    case 6:
                                        return '<span class="badge badge-warning">关机下架中</span>';
                                    case 99:
                                        return '<span class="badge badge-warning">工单处理中</span>';
                                    default:
                                        return '<span class="badge badge-secondary">未知</span>';
                                }
                            },
                        ],
                        [
                            'attribute' => '租用状态',
                            'filter'    => false,
                            'format'    => 'raw',
                            'value'     => function ($model) {
                                $memberPdt = $model->memberPdt;
                                $endTime   = ArrayHelper::getValue($memberPdt, 'end_time');

                                if (empty($endTime)) {
                                    return '<span class="badge badge-secondary">--</span>';
                                }

                                $endDate        = date("Y-m-d", $endTime);
                                $currentDate    = date("Y-m-d");
                                $todayTimestamp = strtotime($currentDate);
                                $endTimestamp   = strtotime($endDate);

                                if ($endTimestamp < $todayTimestamp) {
                                    return '<span class="badge badge-danger">已到期</span>';
                                } elseif ($endTimestamp == $todayTimestamp) {
                                    return '<span class="badge badge-warning">今日到期</span>';
                                } elseif ($endTimestamp >= $todayTimestamp + 86400 && $endTimestamp <= $todayTimestamp + 86400 * 3) {
                                    return '<span class="badge badge-warning">即将到期</span>';
                                } else {
                                    return '<span class="badge badge-success">正常使用</span>';
                                }
                            },
                        ],
                        [
                            'header' => "操作",
                            'format' => 'raw',
                            'value'  => function ($model) {
                                return Html::a(
                                    '<i class="fas fa-eye"></i> 详情',
                                    ['view', 'server_id' => $model->id],
                                    ['class' => 'btn btn-sm btn-outline-info', 'title' => '查看详细配置']
                                );
                            },
                        ],
                    ],
                ]); ?>
            </div>
        </div>
    </div>

</div>