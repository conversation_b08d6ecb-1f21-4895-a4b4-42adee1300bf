<?php

namespace addons\VymCloud\common\models;

use common\behaviors\MerchantStoreBehavior;
use common\models\member\Member;
use common\models\merchant\Merchant;
use Yii;

/**
 * This is the model class for table "{{%addon_cloud_refund}}".
 *
 * @property int         $id
 * @property int         $order_id       订单ID
 * @property int         $uid            会员ID
 * @property int|null    $merchant_id    商户id
 * @property int|null    $store_id       店铺id
 * @property float       $refund_amount  退款金额
 * @property string|null $refund_reason  退款原因
 * @property int         $refund_status  退款状态：0-待处理，1-已处理，-1-已拒绝
 * @property int|null    $processed_by   处理人ID
 * @property string|null $processed_at   处理时间
 * @property string|null $remark         备注
 * @property string|null $created_at     创建时间
 * @property string|null $updated_at     更新时间
 */
class CloudRefund extends \yii\db\ActiveRecord
{
    use MerchantStoreBehavior;

    // 退款状态常量
    const STATUS_PENDING  = 0;   // 待处理
    const STATUS_APPROVED = 1;   // 已处理
    const STATUS_REJECTED = -1;  // 已拒绝

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%addon_cloud_refund}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['order_id', 'uid', 'refund_amount'], 'required'],
            [['order_id', 'uid', 'refund_status', 'processed_by'], 'integer'],
            [['refund_amount'], 'number'],
            [['refund_reason'], 'string', 'max' => 500],
            [['remark'], 'string'],
            [['processed_at', 'created_at', 'updated_at'], 'safe'],
            [['refund_status'], 'default', 'value' => self::STATUS_PENDING],
            [['merchant_id', 'store_id'], 'default', 'value' => 0],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'            => 'ID',
            'order_id'      => '订单ID',
            'uid'           => '会员ID',
            'merchant_id'   => '商户ID',
            'store_id'      => '店铺ID',
            'refund_amount' => '退款金额',
            'refund_reason' => '退款原因',
            'refund_status' => '退款状态',
            'processed_by'  => '处理人ID',
            'processed_at'  => '处理时间',
            'remark'        => '备注',
            'created_at'    => '创建时间',
            'updated_at'    => '更新时间',
        ];
    }

    /**
     * 获取退款状态列表
     * @return array
     */
    public static function getStatusMap()
    {
        return [
            self::STATUS_PENDING  => '待处理',
            self::STATUS_APPROVED => '已处理',
            self::STATUS_REJECTED => '已拒绝',
        ];
    }

    /**
     * 获取退款状态颜色
     * @return array
     */
    public static function getStatusColors()
    {
        return [
            self::STATUS_PENDING  => 'warning',
            self::STATUS_APPROVED => 'success',
            self::STATUS_REJECTED => 'danger',
        ];
    }

    /**
     * 获取关联订单
     * @return \yii\db\ActiveQuery
     */
    public function getOrder()
    {
        return $this->hasOne(CloudOrder::class, ['id' => 'order_id']);
    }

    /**
     * 获取会员信息
     * @return \yii\db\ActiveQuery
     */
    public function getMember()
    {
        return $this->hasOne(Member::class, ['id' => 'uid']);
    }

    /**
     * 关联商户
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }
}
