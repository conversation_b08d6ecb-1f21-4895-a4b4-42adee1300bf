<?php

namespace common\behaviors;

use common\models\merchant\Merchant;
use Yii;
use yii\behaviors\BlameableBehavior;
use yii\db\ActiveRecord;

/**
 * Trait MerchantBehavior
 * @package common\components
 */
trait MerchantBehavior
{
    /**
     * @return array
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors[] = [
            'class' => BlameableBehavior::class,
            'attributes' => [
                ActiveRecord::EVENT_BEFORE_INSERT => ['merchant_id'],
            ],
            'value' => Yii::$app->services->merchant->getNotNullId(),
        ];

        return $behaviors;
    }

    /**
     * 商户
     * @return \yii\db\ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }
}