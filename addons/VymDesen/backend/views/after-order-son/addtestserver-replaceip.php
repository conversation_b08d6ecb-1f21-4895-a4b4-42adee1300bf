<?php
use yii\helpers\Url;
use yii\helpers\Html;

$this->title = '控制台管理 - 售后工单 - 售后工单详情 - 更换IP子单';
?>

<?php $this->beginBlock('content') ?>
<style type="text/css">
 .disabled{display:none}
</style>
<link rel="stylesheet" href="/css/layui.css"  media="all">
<div class="row">
	<div class="col-xs-12">   
        <form id='form' class="form-horizontal" method='post' role="form">
        <input type="hidden" name='ao_id' value="<?php echo $ao_id; ?>">
		<input type="hidden" name='room_id' value="<?php echo $room_id; ?>">
        <div class="form-group">
            <label class="col-sm-3 control-label no-padding-right" >服务器提供商</label>
    		<div class="col-sm-2" style="margin-top: 5px;"> 
			<?php if($servicerprovider == 0 ):?>
				<label>
					<input name="servicerprovider"   checked="checked" value="0" type="radio" class="ace">
					<span class="lbl"> 自有机器</span>
				</label>
			<?php elseif($servicerprovider == 1 ):?>
				<label>
					<input name="servicerprovider"   checked="checked"  value="1" type="radio" class="ace">
					<span class="lbl"> 供应商机器</span>
				</label> 
			<?php endif;?>				
    		</div>
	    </div>
		
		<style type="text/css">
			.checkfont{font-size:0px;}
			.iplittlebox{float:left;display:inline-block;font-size:12px;margin-right:10px;margin-bottom:10px;}
			.iplittlebox input{height:31px;line-height:31px;}
		</style>
		
        <div class="form-group <?php if($servicerprovider == 1):?> disabled<?php endif;?>" id= "ips_have_div">
    	    <label class="col-sm-3 control-label no-padding-right" > IP地址</label>
        	<div class="col-sm-5" id="InputsWrapper1" class="checkfont">
            	<div class="iplittlebox" >
            		<button type="button" id="IPlibraryselection" class="btn btn-xs btn-info btn-success" style="width: 214px;"><i class="ace-icon glyphicon glyphicon-plus">IP库选择</i></button>
            	</div>
            	<div id="iplistji1">
					<?php if($servicerprovider == 1):?>
				  
					<?php elseif($servicerprovider == 0):?>
				  
					<?php if(!empty($ip)):?>
				  
						<?php foreach ($ip as $value):?>
                		<div class="iplittlebox"><input value="<?=$value?>" type="text" name="ips[]" readonly><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button></div>
						<?php endforeach;?>
					<?php endif;?>
					 
            	 <?php endif;?>
        	   </div>
            </div>
        </div>
		
        <!-- 供应商IP -->
        <div class="form-group <?php if($servicerprovider == 0):?> disabled<?php endif;?>" id= "ips_provider_div">
    	    <label class="col-sm-3 control-label no-padding-right" > IP地址</label>
        	<div class="col-sm-5" id="InputsWrapper" class="checkfont">
            	<div class="iplittlebox" >
            		<input type="text" name='' readonly id="provider_ips" placeholder="IP段形式如：127.0.0.1-2" value=''>            		
            		<button type="button" id="AddMoreFileBox" class="btn btn-xs btn-info btn-success" ><i class="ace-icon glyphicon glyphicon-plus"></i></button>
            	</div>
            	<div id="iplistji">
            	<?php if($servicerprovider == 1):?>
                	<?php if(!empty($ip)):?>
                    	<?php foreach ($ip as $value):?>
                    		<div class="iplittlebox"><input value="<?=$value?>" type="text" name="ips[]"><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button></div>
                    	<?php endforeach;?>
                	<?php endif;?>
            	<?php elseif($IdlePdtRes['servicerprovider'] == 0):?>
				
            	<?php endif;?>
            	</div>            	
        	</div>
        </div>
		
        <div class="clearfix form-actions">
            <div class="col-md-offset-3 col-md-9">            	
            	<button id="go" class="btn btn-info" type="submit">
            		<i class="ace-icon fa fa-check bigger-110"></i>
            		提交数据
            	</button>
                &nbsp; &nbsp; &nbsp;
            	<button class="btn btn-danger" type="button" style="width:120px" onclick="window.history.back()">
            		<i class="ace-icon fa fa-reply icon-only"></i>
            		返回上一页
            	</button> 
            </div>
        </div>        
        <div class="hr hr-24"></div>        
		</form>
    </div>
 </div>
 <style type="text/css">
	.changeSelectModel{}
	.selectmodelbox{margin-left:20px;margin-right:20px;border-bottom:1px #ccc solid;padding-bottom:10px;padding-top:10px;}
	.selectmodle{display:inline-block;padding:0 20px;height:40px;line-height:40px;text-align:center;cursor:pointer;border:1px #ccc solid;}
	.selectmodelbox .active{background:#6fb3e0 !important;color:#fff;border:1px #6fb3e0 solid;}
	.result_title{height:40px;padding:10px 0;margin-top:20px;}
	.ipresult table{border-collapse:collapse;width:80%;margin-bottom:40px;}
	.ipresult table td{height:30px;padding:0 5px;text-align:center;line-height:30px;border:1px #ccc solid;}
	.ipresult table td:nth-child(1){width:120px;}
	.ipresult table td:nth-child(2){text-align:left;}
	.setiplist{margin:20px auto;}
	.setiplist li{display:inline-block;height:30px;padding:0 10px;border:1px #ccc solid;text-align:center;line-height:30px;margin-right:5px;margin-bottom:5px;}
 </style>
<!--IP库 Modal -->
<div id="IPlibraryselectionshow" class="modal">
	<div class="modal-dialog" style="min-width: 1200px;">
		<div class="modal-content">
			<div id="modal-wizard-container">	
				<div class="modal-header">					
					<h4 class="widget-title">IP库列表：</h4>					
				</div>
				<div class="selectmodelbox">
					<div class="changeTitleSingle selectmodle active">非连贯IP添加</div>
					<div class="changeTitleMore selectmodle">添加IP段</div>
				</div>
				<div class="changeSelectModelSingle">
					<div class="row" style="">
						<div class="col-xs-12" style="margin-left:20px;margin-top: 10px;">  
							<div style="float: left;margin-right: 6px;">   
								<select class="form-control" name='class_id' id="class_id" >
									<option value="">所属IP分类</option>
									<?php foreach ($IpClassList as $value): ?>
									<option value="<?php echo $value['class_id'] ?>" rel="<?php echo $value['class_name'] ?>"   
									<?php if ($value['class_id'] == Html::encode(Yii::$app->controller->get('class_id')) ): ?>
									 selected='selected'
									 <?php endif ?>	
									><?php echo $value['class_name'] ?></option>
									<?php endforeach ?>
								</select>
							</div>
							<div style="float: left;margin-right: 6px;">   
								<select class="form-control" name='network_gateway' id="network_gateway" >
									<option value="">所属IP网段</option>
									<?php foreach ($IpNetworkList as $value): ?>
									<option value="<?php echo $value['gateway'] ?>" rel="<?php echo $value['network'] ?>"   
									<?php if ($value['gateway'] == Html::encode(Yii::$app->controller->get('network_gateway')) ): ?>
									 selected='selected'
									 <?php endif ?>	
									><?php echo $value['network'] ?></option>
									<?php endforeach ?>
								</select>
							</div>
							
							<div style="float: left;margin-right: 6px;">   
								<input type="text" class="form-control search-query" name="ifame_select_ip" value='<?php echo Html::encode(Yii::$app->controller->get('ip')) ?>'  placeholder="IP">
							</div>
							<div>
								<button type="submit" id="ifame_select_ip_submit" class="btn btn-white btn-primary" style="margin-left:15px">
									<span class="fa fa-search"></span>
									搜索
								</button>        	 
							 </div>
					   </div>
					</div>
					<div class="modal-body step-content" style="padding-top:0px;">
						<div class="step-pane active" style="min-height:70px" data-step="1">
							<form action="" id='checkbox1'>
							   <table class="layui-hide" id="iplist"></table>
							</form>	
						</div>
					</div>
				</div>
				<div class="changeSelectModelMore" style="display:none">
					<div class="row">
						
						<div class="col-xs-12" style="margin-left:20px;margin-top: 10px;">
							<div style="float: left;margin-right: 6px;">   
								<select class="form-control" name='more_class_id' id="more_class_id" >
									<option value="">所属IP分类</option>
									<?php foreach ($IpClassList as $value): ?>
									<option value="<?php echo $value['class_id'] ?>" rel="<?php echo $value['class_name'] ?>"   
									<?php if ($value['class_id'] == Html::encode(Yii::$app->controller->get('class_id')) ): ?>
									 selected='selected'
									 <?php endif ?>	
									><?php echo $value['class_name'] ?></option>
									<?php endforeach ?>
								</select>
							</div>
							<div style="float: left;margin-right: 6px;">   
								<select class="form-control" name='more_network_gateway' id="more_network_gateway" >
									<option value="">所属IP网段</option>
									<?php foreach ($IpNetworkList as $value): ?>
									<option value="<?php echo $value['gateway'] ?>" rel="<?php echo $value['network'] ?>"   
									<?php if ($value['gateway'] == Html::encode(Yii::$app->controller->get('network_gateway')) ): ?>
									 selected='selected'
									 <?php endif ?>	
									><?php echo $value['network'] ?></option>
									<?php endforeach ?>
								</select>
							</div>
							<div style="float: left;margin-right: 6px;">												 
								<!--<input style="display:inline-block;width:150px;"type="text" class="form-control search-query" name="ifame_more_select_ip" value='<?php echo Html::encode(Yii::$app->controller->get('ip')) ?>'  placeholder=" 例：***********-10">-->
								<textarea rows="5"  style="display:inline-block;min-width:350px;" name="ifame_more_select_ip" placeholder="IP段以127.0.0.1-7形式填写，多个换行或回车"><?php echo Html::encode(Yii::$app->controller->get('ip')) ?></textarea>
							
							</div>
							<div>
								<button type="submit" id="ifame_select_ipmore_submit" class="btn btn-white btn-primary" style="margin-left:15px">
									<span class="fa fa-search"></span>
									搜索
								</button>
							</div>
					   </div>
					   
				   </div>
				   <div class="modal-body step-content" style="padding-top:0px;">
						<div class="result_title">搜索结果详情：</div>
						<div class="ipresult">
							<table cellspacing="0" cellpadding="0" border="0" class="setipinfo">
							
							</table>
						</div>
				   </div>
				</div>
				<input type="hidden" class="submit_ip_value" value="">
			</div>
		</div>
		<div class="modal-footer wizard-actions">			
			<button class="btn btn-success btn-sm btn-next" id="selectip" data-dismiss="modal">确定
				<i class="ace-icon fa fa-arrow-right icon-on-right"></i>
			</button>
			<button class="btn btn-danger btn-sm pull-left" data-dismiss="modal"><i class="ace-icon fa fa-times"></i>
				取消
			</button>
		</div>
	</div>
</div>
<script type="text/html" id="statusTpl">
{{#  if(d.status === '0'){ }}
<span style="color: #d15b47;">闲置</span>
{{# } }}
{{#  if(d.status === '1'){ }}
<span style="color: #82af6f;">使用中</span>
{{#  } }}
{{#  if(d.status === '2'){ }}
<span style="color: #f89406;">预留</span>
{{# } }}
</script>
<script src="/js/typeahead.jquery.js"></script>
<script src="/js/ace/elements.typeahead.js"></script>
<script src="/js/layui.js" charset="utf-8"></script>
<script type="text/javascript">
	var itemType = 'single';
	$(function() {
		//选项卡
		$(".selectmodle").click(function() {
			$(".selectmodle").removeClass("active");
			$(this).addClass("active");
			if($(this).hasClass("changeTitleSingle")) {
				$(".changeSelectModelMore").fadeOut(400, function() {
					$(".changeSelectModelMore").css("display","none");
					$(".changeSelectModelSingle").fadeIn(200).css("display","block");
				});
				itemType = 'single';
			} else {
				$(".changeSelectModelSingle").fadeOut(400, function() {
					$(".changeSelectModelSingle").css("display","none");
					$(".changeSelectModelMore").fadeIn(200).css("display","block");
				});
				itemType = 'more';
			}
			$(".submit_ip_value").val('');
		});
	});
	var room_id = "<?php echo $room_id; ?>";
	//IP分类change
	$('[name="class_id"]').change(function(){
		var class_id = $("#class_id").val();
		var url = "<?php echo Url::to(['ip-class/ajax-getnetwork'])?>";
		var load = layer.load(2);
		$.post(url, {class_id:class_id,room_id:room_id}, function(data){
			if ( data['data']['status'] ) {
				$("[name='network_gateway']").empty();
				$("[name='network_gateway']").append('<option value="">所属IP网段</option>');
				$.each(data['data']['data'], function(key, val){					
					$("[name='network_gateway']").append('<option value="'+val['gateway']+'" rel="'+val['network']+'">'+val['network']+'</option>');
				});		
				layer.closeAll('loading');
			} else {
				layer.alert(data['data']['info'], {icon:7});
				return;
			}
			
		},'json');
	});
	
	$('[name="more_class_id"]').change(function(){
		var class_id = $("#more_class_id").val();
		var url = "<?php echo Url::to(['ip-class/ajax-getnetwork'])?>";
		var load = layer.load(2);
		$.post(url, {class_id:class_id,room_id:room_id}, function(data){
			if ( data['data']['status'] ) {
				$("[name='more_network_gateway']").empty();
				$("[name='more_network_gateway']").append('<option value="">所属IP网段</option>');
				$.each(data['data']['data'], function(key, val){					
					$("[name='more_network_gateway']").append('<option value="'+val['gateway']+'" rel="'+val['network']+'">'+val['network']+'</option>');
				});		
				layer.closeAll('loading');
			} else {
				layer.alert(data['data']['info'], {icon:7});
				return;
			}
			
		},'json');
	});
	
	//提交选择
	$('#go').click(function(){
		var url = '<?php echo Url::to(["after-order-son/addtestserver-replaceip"]) ?>';
		layer.msg('正在提交...', {icon:16, time:0});
		$(".middle").empty();
		$.post(url, $('#form').serialize(), function(data){
			if ( data['data']['status'] ) {
				layer.alert(data['data']['info'], {icon:1}, function(index){				   		  
					window.location.href='<?php echo Url::to(["after-order/detail", "ao_id"=>$ao_id]);?>';
				});
			} else {
				if ( typeof data['data']['info'] === 'string' ) {
					layer.alert(data['data']['info'], {icon:7});
					return ;
				}
				$.each(data['data']['info'], function(key, val){			
					layer.alert('信息填写有误', {icon:7});			
					$("[name='"+key+"']").next('span').html('<i class="ace-icon fa fa-exclamation-circle bigger-110">'+val[0]+'</i>');    			
				});
			}		
		}, 'json');	
		return false;
	});
	
	//显示IP库
	$('#IPlibraryselection').click(function(){ 
		layui.use('table', function(){
			var table = layui.table;	  
			table.render({
				elem: '#iplist',
				url:'<?php echo Url::to(['member-pdt/ajax-iplist'])?>',
				where: {room_id:room_id},
				limit:"15",
				cols: [[
				  {type:'checkbox'}
				  ,{field:'id', width:'100',title: 'ID', sort: true}
				  ,{field:'ip', width:'150', title: 'IP地址'}
				  ,{field:'vlan', width:'80',title: 'Vlan', sort: true}
				  ,{field:'mask', width:'150', title: '掩码'}    		      
				  ,{field:'gateway', width:'150', title: '网关'} 
				  ,{field:'status',width:'100', title: '状态', sort: true,templet: '#statusTpl'}   	
				  ,{field:'remarks', title: '备注'}       		      
				]],
				page: true,		   
			});
		});
		$('#IPlibraryselectionshow').modal('show');
		return ;
	});

	//根据IP查询
	$('#ifame_select_ip_submit').click(function() { 
		var selip = $("[name='ifame_select_ip']").val();
		var class_id = $("#class_id").val();
		var gateway = $("#network_gateway").val();
		layui.use('table', function(){
			  var table = layui.table;	  
			  table.render({
				elem: '#iplist',
				url:'<?php echo Url::to(['member-pdt/ajax-iplist'])?>',
				where: {room_id:room_id,ip:selip,class_id:class_id,gateway:gateway},
				limit:"15",    		   
				cols: [[
				  {type:'checkbox'}
				  ,{field:'id', width:'100',title: 'ID', sort: true}
				  ,{field:'ip', width:'150', title: 'IP地址'}
				  ,{field:'vlan', width:'80',title: 'Vlan', sort: true}
				  ,{field:'mask', width:'150', title: '掩码'}    		      
				  ,{field:'gateway', width:'150', title: '网关'} 
				  ,{field:'status', width:'100', title: '状态', sort: true,templet: '#statusTpl'}   	
				  ,{field:'remarks', title: '备注'}      		      
				]],
				page: true,		   
			 });
		});
		//$('#IPlibraryselectionshow').modal('show');
		return ;
	});

	//根据IP段查询
	$("#ifame_select_ipmore_submit").click(function() {
		var select_ip = $('[name="ifame_more_select_ip"]').val(); //IP段
		
		var class_id = $("#more_class_id").val();
		var gateway = $("#more_network_gateway").val();
		
		var url = "<?php echo Url::to(['member-pdt/ajax-iplist-morenew'])?>";
		
		if(!class_id) {
			layer.alert("所属IP分类必须选择", {icon:7});
			return false;
		}
		if( !select_ip && !gateway) {
			layer.alert("所属IP网段和IP段必选其中一个条件", {icon:7});
			return false;
		}
		//alert(select_ip);
		//return false;
		$(".setipinfo").html('');
		
		$.post(url, {"room_id":room_id, "class_id":class_id,"gateway":gateway, "select_ip":select_ip}, function(e) {
			if( e.data.status) {
				if(e.data.datas.allcount > 0) {
					var html = '<tr><td>区段搜索的IP</td><td>'+e.data.datas.searchip+'</td></tr><tr><td>总计IP数</td><td>'+e.data.datas.allcount+'</td></tr><tr><td>即将分配IP数</td><td>'+(e.data.datas.canuse.length)+'个</td></tr><tr><td>不可用IP数</td><td>'+(e.data.datas.allcount - e.data.datas.canuse.length)+'个</td></tr>';
					html += '<tr><td>不可用IP列表</td><td><table class="setiplist"><ul class="setiplist">';
					$.each(e.data.datas.notuse, function(i, n) {
						html += '<li>'+n+'</li>';
					});
					html += '</ul></table></td></tr>';
					$(".setipinfo").html(html);
					if(e.data.datas.allcount - e.data.datas.canuse.length > 0) {
						$(".submit_ip_value").val('');
					} else {
						$(".submit_ip_value").val(e.data.datas.searchip);
					}					
				} else {
					$(".setipinfo").html('无数据');
				}
			} else {
				layer.alert(e.data.info, {icon:7});
				return;
			}
			
			
		}, "json");
	});


	//选择好IP
	$('#selectip').click(function() {
		if(itemType == 'single') {
			var table = layui.table;
			var checkStatus = table.checkStatus('iplist');
			data = checkStatus.data;
			var url = "<?php echo Url::to(['member-pdt/get-selectip']) ?>";
			var load = layer.load(2);
			//var oldIP= document.getElementById("have_ips").value;
			//var oldIP = $('input[name="havp_ip"]').value();
			$.post(url, {data:JSON.stringify(data),ips:""}, function(data){           	
				//document.getElementById("ips").value = data['data'];
				$.each(data['data'], function(key, val){
					$("#iplistji1").append('<div class="iplittlebox"><input value="'+val+'" type="text" readonly="readonly" name="ips[]"/><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button><div>');			
				});	        		
				layer.closeAll('loading');        	
			},'json');   
		} else {
			var pace = $(".submit_ip_value").val();
			if(!pace) {
				layer.alert("必须查询后或者没有不可用IP才可以提交", {icon:7});
				return false;
			}
			if( pace.indexOf(",") != -1 ) { //则为多段IP段
				iparr = pace.split(",");
				$.each(iparr, function(key, val){
					$("#iplistji1").append('<div class="iplittlebox"><input value="'+val+'" type="text" readonly="readonly" name="ips[]"/><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button><div>');
				});	
			} else {
				$("#iplistji1").append('<div class="iplittlebox"><input value="'+pace+'" type="text" readonly="readonly" name="ips[]"/><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button><div>');
			}		
		}
	});

</script>  
<script type="text/javascript">
	$(document).ready(function() {
		var MaxInputs    = 1000; //maximum input boxes allowed
		var InputsWrapper  = $("#iplistji"); //Input boxes wrapper ID
		var AddButton    = $("#AddMoreFileBox"); //Add button ID
		var x = InputsWrapper.length; //initlal text box count
		var FieldCount=1; //to keep track of text box added
		$(AddButton).click(function (e) //on add input button click
		{
			if(x <= MaxInputs) //max input box allowed
			{
			  FieldCount++; //text box added increment
			  //add input box
			  $(InputsWrapper).append('<div class="iplittlebox"><input value="" type="text" name="ips[]"/><button type="button" style="margin-left: 4px;margin-bottom: 3px;" class="removeclass btn btn-xs btn-danger"><i class="ace-icon glyphicon glyphicon-remove"></i></button><div>');
			  x++; //text box increment
			}
			return false;
		});
		$("body").on("click",".removeclass", function(e){ //user click on remove text                
			$(this).parent('div').remove(); //remove text box                   
			return false;
		})
	});
</script>   

<?php $this->endBlock(); ?>
