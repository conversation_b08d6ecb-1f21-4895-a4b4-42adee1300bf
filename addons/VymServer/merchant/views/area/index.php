<?php

use addons\VymServer\common\enums\AreaEnum;
use addons\VymServer\common\models\ServerArea;
use common\enums\AppEnum;
use common\helpers\Html;
use common\helpers\MerchantHelper;
use common\helpers\Url;
use jianyan\treegrid\TreeGrid;
use yii\grid\ActionColumn;
use yii\grid\GridView;
use yii\widgets\ActiveForm;

/** @var yii\web\View $this */
/** @var yii\data\ActiveDataProvider $dataProvider */
/** @var common\models\base\SearchModel $searchModel */

$this->title = '业务地域';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="container-fluid">
    <?php if (Yii::$app->id == AppEnum::BACKEND): ?>
    <div class="card search-card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-search mr-1"></i>
                筛选条件
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <?php $form = Html::commonFilterForm() ?>
            <!-- 商户筛选 -->
            <?= MerchantHelper::merchantSelect($form, $searchModel) ?>
            <div class="col-sm-12 col-md-6 col-lg-3">
                <?= Html::submitButton('<i class="fas fa-search"></i> 搜索', ['class' => 'btn btn-primary btn-md mr-2',]) ?>
                <?= Html::a('<i class="fas fa-redo"></i> 重置', ['index'], ['class' => 'btn btn-outline-secondary btn-md',]) ?>
            </div>
            <?php ActiveForm::end(); ?>
        </div>
    </div>
    <?php endif; ?>
    <div class="card">
        <div class="box">
            <div class="box-header">
                <h3 class="box-title"><?= Html::encode($this->title); ?></h3>
                <div class="box-tools">
                    <?= Html::create(['ajax-edit'], '创建', [
                        'data-toggle' => 'modal',
                        'data-target' => '#ajaxModal',
                        'class' => 'btn btn-primary btn-sm no-loading'
                    ]); ?>
                </div>
            </div>
            <div class="box-body table-responsive">
                <?= GridView::widget([
                    'dataProvider' => $dataProvider,
                    // 重新定义分页样式
                    'tableOptions' => [
                        'class' => 'table table-hover rf-table',
                        'fixedNumber' => 2,
                        'fixedRightNumber' => 1,
                    ],
                    'columns' => [
                        'id',
                        [
                            'attribute' => 'name',
                            'format' => 'raw',
                            'value' => function ($model) {
                            return Html::tag('span', $model->name, [
                                'class' => 'm-l-sm',
                            ]);
                        },
                        ],
                        [
                            'header' => '继承来源区域',
                            'attribute' => 'name',
                            'format' => 'raw',
                            'value' => function ($model) {
                            $regional_id = $model->regional->id;
                            $regional_name = $model->regional->regional_name;
                            return "{$regional_name}(ID:$regional_id)";
                        },
                        ],
                        [
                            'attribute' => 'sort',
                            'format' => 'raw',
                            'headerOptions' => ['class' => 'col-md-1'],
                            'value' => function ($model) {
                            return Html::sort($model->sort);
                        },
                        ],
                        [
                            'attribute' => 'area_st',
                            'format' => 'raw',
                            'headerOptions' => ['class' => 'col-md-1'],
                            'value' => function ($model) {
                            return AreaEnum::getStatusHtml($model->area_st);
                        },
                            'filter' => Html::activeDropDownList(
                                $searchModel,
                                'area_st',
                                AreaEnum::getStatusMap(),
                                [
                                    'prompt' => '全部',
                                    'class' => 'form-control',
                                ]
                            ),
                        ],
                        [
                            'header' => "操作",
                            'class' => 'yii\grid\ActionColumn',
                            'template' => '{edit} {status} {delete}',
                            'buttons' => [
                                'edit' => function ($url, $model, $key) {
                                return Html::edit(['ajax-edit', 'id' => $model->id], '编辑', [
                                    'data-toggle' => 'modal',
                                    'data-target' => '#ajaxModal',
                                    'class' => 'btn btn-primary btn-sm no-loading'
                                ]);
                            },
                                'delete' => function ($url, $model, $key) {
                                return Html::delete(['delete', 'id' => $model->id]);
                            },
                            ],
                        ],
                    ],
                ]); ?>
            </div>
        </div>
    </div>
</div>