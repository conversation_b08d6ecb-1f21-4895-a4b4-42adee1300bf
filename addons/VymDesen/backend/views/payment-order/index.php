<?php

use common\helpers\ArrayHelper;
use common\helpers\MemberHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\LinkPager;

$this->title = '控制台管理-付款单列表';
?>
<?php $this->beginBlock('content') ?>
<?php $node = \Yii::$app->session['auth']['node'];?>
<div class="row">    			
	<div class="clearfix">
		<div class="pull-right tableTools-container"></div>
	</div>  
</div>
<style>
    .input-group{padding-bottom:5px;}
</style>
<div class="row" style="margin-bottom: 10px;">
    <div class="col-xs-12">
	<form class="form-search form-inline" action="">
        <input type="hidden" name="r" value="payment-order/list"/>
        <div class="input-group">
            <span class="input-group-addon">交易号</span>
            <input type="text" class="form-control search-query" name="flow_no" value="<?php echo Html::encode(Yii::$app->controller->get('flow_no')) ?>" placeholder="交易号" />
        </div>
        <div class="input-group">
            <span class="input-group-addon">订单号</span>
            <input type="text" class="form-control search-query" name="order_id"  value="<?php echo Html::encode(Yii::$app->controller->get('order_id')) ?>" placeholder="订单号" />
        </div>
        <div class="input-group">
            <span class="input-group-addon">付款单号</span>
            <input type="text" class="form-control search-query" name="general_payorder" value='<?php echo Html::encode(Yii::$app->controller->get('general_payorder')) ?>' placeholder="付款单号"/>
        </div>
        <div class="input-group">
            <span class="input-group-addon">用户账户</span>
            <input type="text" class="form-control search-query" name="user_email" value='<?php echo Html::encode(Yii::$app->controller->get('user_email')) ?>'  placeholder="用户账户" />
        </div>
        <div class="input-group">
            <span class="input-group-addon">用户名称</span>
            <input type="text" class="form-control search-query" name="user_name" value='<?php echo Html::encode(Yii::$app->controller->get('user_name')) ?>'  placeholder="用户名称" />
        </div>
        <div class="input-group">
            <span class="input-group-addon">销售名称</span>
            <select class="form-control" name='admin_id'>
                <option value="">销售名称</option>
                <?php $UserAdminRes = isset($UserAdminRes) ? $UserAdminRes:[]; foreach ($UserAdminRes as $value): ?>
                    <option value="<?php echo $value['admin_id'] ?>" rel="<?php echo $value['uname'] ?>"
                        <?php if ($value['admin_id'] == Html::encode(Yii::$app->controller->get('admin_id')) ): ?>
                            selected='selected'
                        <?php endif ?>
                    >
                        <?php echo $value['rename'] ?></option>
                <?php endforeach ?>
            </select>
        </div>
        <div class="input-group">
            <span class="input-group-addon">支付状态</span>
            <select class="form-control" name='general_pay_lock'>
                <option value="">支付状态</option>
                <option value="等待支付" <?php echo Html::encode(Yii::$app->controller->get('general_pay_lock')) == '等待支付' ? "selected=selected" : "" ?>>等待支付</option>
                <option value="支付完成" <?php echo Html::encode(Yii::$app->controller->get('general_pay_lock')) == '支付完成' ? "selected=selected" : "" ?>>支付完成</option>
                <option value="已退款" <?php echo Html::encode(Yii::$app->controller->get('general_pay_lock')) == '已退款' ? "selected=selected" : "" ?>>已退款</option>
                <option value="已取消" <?php echo Html::encode(Yii::$app->controller->get('general_pay_lock')) == '已取消' ? "selected=selected" : "" ?>>已取消</option>
            </select>
        </div>

        <div class="input-daterange input-group">
            <span class="input-group-addon">支付时间</span>
            <input type="text" class="form-control" name="start_time" id="start_time"  value='<?php echo Html::encode(Yii::$app->controller->get('start_time')) ?>'  placeholder="支付时间" readonly/>
            <span class="input-group-addon"><i class="fa fa-exchange"></i></span>
            <input type="text" class="form-control" name="end_time" id="end_time" value='<?php echo Html::encode(Yii::$app->controller->get('end_time')) ?>'  placeholder="支付时间" readonly/>
        </div>
        <div class="input-group">
            <span class="input-group-btn">
                <button type="submit" class="btn btn-white btn-primary btn-bold">
                    <span class="ace-icon fa fa-search icon-on-right bigger-110"></span>
                    搜索
                </button>
                <button type="button" class="btn btn-white btn-success btn-bold" style="margin-left:5px;" onclick="javascript:location.href='<?php echo Url::to(['payment-order/list']) ?>'">
                    <span class="ace-icon fa  fa-refresh"></span>
                    刷新
                </button>
            </span>
        </div>
	</form>
    </div>
</div>
<form action="" id='checkbox'>
	<table id="simple-table" width="100%" class="table table-bordered table-hover">
		<thead>
			<tr>
				<th colspan="14">
					<?php if(in_array("payment-order/merge-payorderlist", $node) || $this->params['is_administrator_user']):?>
						<a  href="javascript:void(0)" id="go_pay" class="btn btn-primary btn-sm" style="margin-left:10px;"><span class="ace-icon fa fa-share "></span>合并所选项付款</a>
					<?php endif;?>
				</th>
			</tr>
			<tr>
				<th class="center">
					<label class="pos-rel">
						<input type="checkbox" class="ace" />
						<span class="lbl"></span>
					</label>
				</th> 
				<th>客户昵称</th>
				<th>客户账户</th>
				<th>销售名称</th>
				<th>支付交易号</th>
				<th>付款单号</th>
				<th>所属订单</th>
				<th>付款金额</th>
				<th>支付方式</th>
				<th>三方交易号</th>				
				<th>支付时间</th>
				<th>创建时间</th>
				<th>状态</th>
				<th class="hidden-480">操作</th>				
			</tr>
		</thead>
		<tbody>
			<?php foreach ($PayorderGeneralList as $key => $val): ?>
				<td class="center">
					<?php if(in_array($val['general_pay_lock'], ['等待支付'])):?>
						<?php if($val['general_payorder_number'] == '' || $val['general_payorder_number'] == null):?>
						<label class="pos-rel">
							<input type="checkbox" name="payorder_no[]" value="<?php echo $val['general_payorder'] ?>" class="ace">
							<span class="lbl"></span>
						</label>						
						<?php endif;?>
					<?php endif;?>
				</td>
				<td><?php echo 
					ArrayHelper::getPresent($val, 'original.order_user_nickname', function ($v){
						return MemberHelper::userNick($v);
					}, 'original.userMember')
				?></td>
				<td><?php echo 
					ArrayHelper::getPresent($val, 'original.order_user_email', function ($v){
						return MemberHelper::userAccount($v, false);
					}, 'original.userMember')
				?></td>
				<td><?php echo $val['original']['order_admin_name']?></td>
				<td><?php echo $val['general_payorder_number']?></td>
				<td><?php echo $val['general_payorder']?></td>
				<td><?php echo $val['general_original_order']?></td>
				<td><?php echo $val['general_pay_money']?></td>
				<td><?php echo $val['general_pay_platform']?></td>
				<td><?php echo $val['general_callback_stream']?></td>				
				<td><?php echo $val['general_pay_time'] ? date('Y-m-d H:i:s', $val['general_pay_time']) : ''; ?></td>
				<td><?php echo date('Y-m-d H:i:s', $val['general_create_time']) ?></td>
				<td>
					<?php if( $val['general_pay_lock'] == '等待支付'):?>
							<span class='label label-warning'><?php echo $val['general_pay_lock']; ?></span>
					<?php elseif( $val['general_pay_lock'] == '后付款'):?>
							<span class='label label-pink'><?php echo $val['general_pay_lock']; ?></span>
					<?php elseif( $val['general_pay_lock'] == '已取消'):?>
							<span class='label label-danger'><?php echo $val['general_pay_lock']; ?></span>
					<?php elseif( $val['general_pay_lock'] == '支付完成'):?>
							<span class='label label-success'><?php echo $val['general_pay_lock']; ?></span>
					<?php endif;?>
					<?php if($val['general_payment_userid'] != $val['original']['order_user_id']):?>							
							<span class="label label-warning Designated-User" data-useremail="<?php 
								echo MemberHelper::userAccount(ArrayHelper::getValue($val, 'user'));
							?>" data-username="<?php 
								echo MemberHelper::userNick(ArrayHelper::getValue($val, 'user'));
							?>">指定单</span>
						<?php endif;?>
				</td>
				<td class="hidden-480">		
				
					<?php if(in_array("payment-order/details", $node) || $this->params['is_administrator_user']):?>
						<a class="blue" href="<?php echo Url::to(['details', 'payorder_no' => $val['general_payorder']])?>">详情</a>
					<?php endif?>
					
					<?php if( in_array($val['general_pay_lock'], ['等待支付', '后付款'])):?>
						<?php if($val['general_payorder_number'] == '' || $val['general_payorder_number'] == null):?>
						
							<?php if(in_array("payment-order/designated-payment", $node) || $this->params['is_administrator_user']):?>
								&nbsp;&nbsp;<a href="javascript:;" class="blue Designated-Payment" data-id="<?php echo $val['general_id']?>" data-money="<?php echo $val['general_pay_money']?>" data-payorder="<?php echo $val['general_payorder']?>">指定付款</a>
							<?php endif?>
						
						<?php else:?>
							<?php if(in_array("payment-flow/unpaid-list", $node) || $this->params['is_administrator_user']):?>
								&nbsp;&nbsp;<a class="blue" href="<?php echo Url::to(['payment-flow/unpaid-list', 'flow_no' => $val['general_payorder_number']]);?>">立即支付</a>
							<?php endif?>
							
						<?php endif?>
					<?php endif?>
				</td>
			</tr>				
		<?php endforeach;?>
		</tbody>						  
	</table>	
	<div class="row" style="margin-top: 10px;">
		<div class="col-xs-6">
			<div class="dataTables_info" id="dynamic-table_info" role="status" aria-live="polite">共有<?php echo $iCount;?>条记录</div>
		</div>
		<div class="col-xs-6 pagination1" style="text-align: right;">
			<?php 
				echo LinkPager::widget([
					'pagination' => $page,
					'firstPageLabel'=>"首页",
					'prevPageLabel'=>'上一页',
					'nextPageLabel'=>'下一页',
					'lastPageLabel'=>'末页',
				]);
			?>
		</div>
	</div>
</form>	
<style>
.float_table{border:none;}
.float_table tr td{height:40px;line-height:40px!important;}
.float_table tr td:first-child{text-align:right;width:150px;}
.float_table tr td input,textarea{width:180px;}
.float_table tr td select{width:300px;}
.float_table tr td textarea{line-height:24px;}
.table tr td input{height:30px;line-height:30px;}
.table tr td .fact_price{width:80px;}
.table tr td .trade_num{width:40px;}
</style>

<!-- 指定用户支付 -->
<div id="Designated_modal" style="display:none;">
	<table class="table float_table">
		<tr>
			<td><span style="color:red">*</span>付款单：</td>
			<td style="color:red" class="designated_payorder"></td>
		</tr>
		<tr>
			<td><span style="color:red">*</span>金额：</td>
			<td style="color:red" class="designated_money"></td>
		</tr>
		<tr>
			<td><span style="color:red">*</span>指定用户账户：</td>
			<td><input type="text" class="designated_useremail" value="" placeholder="例：<EMAIL>" style="width: 240px;"></td>
		</tr>
		<input type="hidden" class="designated_general_id" value="">
	</table>
</div>

<!-- page specific plugin scripts -->


<script type="text/javascript">
	var start = {
		elem: '#start_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas){
			 end.min = datas; //开始日选好后，重置结束日的最小日期
			 end.start = datas //将结束日的初始值设定为开始日
		}
	};
	var end = {
		elem: '#end_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas){
			 end.min = datas; //开始日选好后，重置结束日的最小日期
			 end.start = datas //将结束日的初始值设定为开始日
		}
	};
	laydate(start);
	laydate(end);
	laydate.skin('molv');
	//去支付
	$('#go_pay').click(function() {
		var _this = $(this);		
		if ( $("[name='payorder_no[]']:checked").length <= 0 ) {
			layer.alert('请至少选择一条付款单', {icon:7});
			return false;
		}
		var payorder_no =[];
		var num = $("[name='payorder_no[]']:checked").length;
		$('input[name="payorder_no[]"]:checked').each(function(){
			payorder_no.push($(this).val());
		});		
		var payorders = payorder_no.join([',']);//console.log(payorders);return false;
		layer.confirm('您一共选择了'+num+'条付款单', {icon:3,
			btn: ['确定','取消'] //按钮
		}, function(){
			//验证所选付款单是否为同一个客户支付
			var url = "<?php echo Url::to(['payment-user']);?>";
			$.post(url, {"payorder_no":payorder_no}, function(e) {
				if(e.data.status == 0) {
					layer.alert(e.data.info, {icon:7});
				} else {
					window.location.href="<?php echo Url::to(['merge-payorderlist'])?>"+"?payorder_no="+payorders;
					return;
				}
			}, "json");
			
		}, function(){
		   
		});
		return false;
	});
	
	$(".Designated-User").mouseover(function() {
		var user_email = $(this).attr('data-useremail');
		var user_name = $(this).attr('data-username');
		layer.tips('指定用户账户为：'+user_email+'，昵称：'+user_name, this, {
		  tips: [1, "#000"],
		});
	});
	
	//指定用户支付
	$('.Designated-Payment').click(function(){
		var general_id = $(this).attr("data-id");
		var general_money = $(this).attr("data-money");
		var general_payorder = $(this).attr("data-payorder");
		
		$(".designated_payorder").html(general_payorder);
		$(".designated_money").html(general_money);		
		$(".designated_general_id").val(general_id);
		$(".designated_useremail").val('');
		
		layer.open({
			type: 1, 
			area: ['500px', '300px'],
			title:"" || "指定用户支付付款单",
			content: $("#Designated_modal"),
			maxmin: false, //最大化按钮
			anim:3, //动画
			btn: ['确定', '关闭'],
			yes: function(index, layero) {				
				var general_id = $(".designated_general_id").val();
				var designated_useremail = $(".designated_useremail").val();				
				if(!designated_useremail) {
					layer.alert("必须选择指定用户", {icon:7});
					return false;
				}
				var url = "<?php echo Url::to(['payment-order/designated-payment'])?>";
				$.post(url, {"general_id":general_id, "user_email":designated_useremail}, function(e) {
					layer.close(index);
					if(e.data.status == 0) {
						layer.alert(e.data.info, {icon:7});
					} else {
						layer.alert(e.data.info, {icon:1}, function() {
							location.reload();
						});
					}
				}, "json");
				
			}
		});
	});
	
	jQuery(function($) {			
		//And for the first simple table, which doesn't have TableTools or dataTables
		//select/deselect all rows according to table header checkbox
		var active_class = 'active';
		$('#simple-table > thead > tr > th input[type=checkbox]').eq(0).on('click', function(){
			var th_checked = this.checked;//checkbox inside "TH" table header				
			$(this).closest('table').find('tbody > tr').each(function(){
				var row = this;
				if(th_checked) $(row).addClass(active_class).find('input[type=checkbox]').eq(0).prop('checked', true);
				else $(row).removeClass(active_class).find('input[type=checkbox]').eq(0).prop('checked', false);
			});
		});				
		//select/deselect a row when the checkbox is checked/unchecked
		$('#simple-table').on('click', 'td input[type=checkbox]' , function(){
			var $row = $(this).closest('tr');
			if($row.is('.detail-row ')) return;
			if(this.checked) $row.addClass(active_class);
			else $row.removeClass(active_class);
		});			
		/***************/
		$('.show-details-btn').on('click', function(e) {
			e.preventDefault();
			$(this).closest('tr').next().toggleClass('open');
			$(this).find(ace.vars['.icon']).toggleClass('fa-angle-double-down').toggleClass('fa-angle-double-up');
		});
		
		function questionClick(obj) {
			var id = obj.attr('financeid');
			var point = obj.attr('financepoint');
			var _this = obj;
			
			var url = "<?php echo Url::to(['question'])?>";
			$.post(url, {"id":id, "point":point}, function(e) {
				if(point == 'Y') {
					_this.parents('tr:eq(0)').css("background","#FFFFFF");
					_this.attr('financepoint', 'N');
					_this.html('疑问标记');
					
				} else {
					_this.parents('tr:eq(0)').css("background","#FFF68F");
					_this.attr('financepoint', 'Y');
					_this.html('撤销标记');
				}
				/*
				$(".hasQuestion").click(function() {
					alert(123);
					questionClick($(this));
				});*/
				
			}, "json");
		}
		
		//疑问标记
		$(".hasQuestion").click(function() {		
			questionClick($(this));
		});		
	});	

</script>
<?php $this->endBlock(); ?>
