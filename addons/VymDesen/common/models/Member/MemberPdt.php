<?php

namespace addons\VymDesen\common\models\Member;

use addons\VymDesen\common\traits\MemberPdtTrait;
use Yii;
use addons\VymDesen\common\models\Pdt\PdtManage;
use addons\VymDesen\common\models\Pdt\PdtRoomMange;
use addons\VymDesen\common\models\UserMember\UserMember;
use addons\VymDesen\backend\models\UserAdmin\UserAdmin;
use addons\VymDesen\common\models\Pdt\PdtCabinetManage;
use addons\VymDesen\common\models\Pdt\PdtType;
use addons\VymDesen\common\models\Trade\Trade;
use addons\VymDesen\common\models\Server\ServerAttribute;
use addons\VymDesen\backend\models\Provider;
use addons\VymDesen\common\models\Pdt\SwitchManage;
use addons\VymDesen\common\models\Pdt\PdtManageType;
use addons\VymDesen\common\models\PipeLine\PipelineList;

use addons\VymDesen\backend\models\IdlePdt;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "member_pdt".
 *
 * @property integer $id
 * @property integer $server_type_id
 * @property integer $room_id
 * @property integer $cabinet_id
 * @property integer $pdt_id
 * @property integer $property
 * @property integer $type_id
 * @property integer $attribute_id
 * @property integer $admin_id
 * @property integer $user_id
 * @property string  $user_name
 * @property string  $bandwidth
 * @property string  $real_bandwidth
 * @property string  $switch_location
 * @property string  $switch_port
 * @property integer $occupies_position
 * @property string  $unionid
 * @property string  $sell_price
 * @property string  $cost_price
 * @property string  $upgrade_cost_price
 * @property integer $payment_cycle
 * @property integer $start_time
 * @property integer $end_time
 * @property string  $remark
 * @property string  $ipmi_ip
 * @property string  $ipmi_name
 * @property string  $ipmi_pwd
 * @property integer $status       0 未开通   1 正常  2  升级中  3  机器更换中  4更换IP -1 删除   -2 退款中
 * @property string  $config
 * @property string  $ip
 * @property string  $ip2
 * @property string  $trade_no
 * @property string  $servicerprovider
 * @property string  $provider_id
 * @property string  $reserved     预留字段
 * @property string  $note         配置说明
 * @property integer $update_time  修改时间
 * @property integer $audit_status 审核状态
 * @property integer $is_auto      自动续费
 * @property integer $server_id      供应商服务器id
 * @property integer $ext_data      供应商服务器其他数据
 */
class MemberPdt extends \yii\db\ActiveRecord
{
    use MemberPdtTrait;

    public $fields = [];

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'member_pdt';
    }

    public static function tableNamealias()
    {
        return '用户产品表';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                [
                    'user_id',
                    'server_type_id',
                    'room_id',
                    'cabinet_id',
                    'pdt_id',
                    'property',
                    'type_id',
                    'attribute_id',
                    'admin_id',
                    'status',
                    'idle_id',
                    'audit_status',
                    'switch_location',
                    'provider_id',
                    'switch_port',
                    'delete_time',
                ],
                'integer',
            ],
            [
                [
                    'unionid',
                    'user_name',
                    'ipmi_name',
                    'ipmi_pwd',
                    'trade_no',
                ],
                'string',
                'max' => 50,
            ],
            [
                [
                    'remark',
                    'ipmi_ip',
                    'real_bandwidth',
                    'bandwidth',
                ],
                'string',
                'max' => 255,
            ],
            [
                'config',
                'string',
            ],
            [
                [
                    'unionid',
                    'ipmi_name',
                ],
                'trim',
            ],
            [
                'occupies_position',
                'string',
            ],
            [
                'room_id',
                'required',
                'message' => '未选择产品所属机房',
            ],
            [
                'server_type_id',
                'required',
                'message' => '未选择服务器分类',
            ],
            [
                'idle_id',
                'required',
                'message' => '未选择产品库',
                'on'      => [
                    'memberpdtmodify_have',
                    'memberpdtadd_have',
                    'machine_allocation_have',
                ],
            ],
            [
                'cabinet_id',
                'required',
                'message' => '未选择产品所属机柜',
                'on'      => [
                    'memberpdtmodify_have',
                    'memberpdtadd_have',
                    'machine_allocation_have',
                ],
            ],
            [
                'pdt_id',
                'required',
                'message' => '未选择产品所属产品配置类别',
            ],
            [
                'property',
                'required',
                'message' => '未选择产品产权',
                'on'      => [
                    'memberpdtmodify_have',
                    'memberpdtadd_have',
                    'machine_allocation_have',
                ],
            ],
            //['type_id', 'required', 'message'=>'未选择产品属性'],
            [
                'attribute_id',
                'required',
                'message' => '未选择服务器状态属性',
            ],
            [
                'admin_id',
                'required',
                'message' => '未选择负责管理人',
            ],
            [
                'provider_id',
                'required',
                'message' => '未选择供应商',
                'on'      => [
                    'memberpdtmodify_provider',
                    'memberpdtadd_provider',
                    'memberpdtassigned_provider',
                ],
            ],
            [
                'user_id',
                'required',
                'message' => '未选择产品所属用户',
            ],
            [
                'bandwidth',
                'required',
                'message' => '客户要求带宽不能为空',
                'on'      => [
                    'memberpdtassigned_have',
                    'memberpdtassigned_provider',
                    'memberpdtmodify_have',
                    'memberpdtadd_have',
                    'memberpdtmodify_provider',
                    'memberpdtadd_provider',
                ],
            ],
            [
                'real_bandwidth',
                'required',
                'message' => '客户实际带宽不能为空',
                'on'      => [
                    'memberpdtassigned_have',
                    'memberpdtassigned_provider',
                    'memberpdtmodify_have',
                    'memberpdtadd_have',
                    'memberpdtmodify_provider',
                    'memberpdtadd_provider',
                ],
            ],

            [
                'switch_location',
                'required',
                'message' => '未选择交换机位置',
                'on'      => [
                    'memberpdtmodify_have',
                    'memberpdtadd_have',
                    'machine_allocation_have',
                ],
            ],

            [
                'switch_port',
                'required',
                'message' => '交换机端口不能为空',
                'on'      => [
                    'memberpdtmodify_have',
                    'memberpdtadd_have',
                    'machine_allocation_have',
                ],
            ],
            //['switch_port', 'number', 'message'=>'交换机端口必须是数字','on' =>['memberpdtmodify_have','memberpdtadd_have','machine_allocation_have']],
            //['unionid', 'required', 'message'=>'主机名称不能为空'],
            //[['unionid'], 'unique', 'message'=>'该主机名称已存在'],
            [
                'sell_price',
                'required',
                'message' => '销售价格不能为空',
            ],
            [
                'cost_price',
                'number',
            ],
            [
                'cost_price',
                'required',
                'message' => '成本价格不能为空',
                'on'      => [
                    'memberpdtassigned_provider',
                    'memberpdtmodify_have',
                    'memberpdtadd_have',
                    'memberpdtmodify_provider',
                    'memberpdtadd_provider',
                ],
            ],
            [
                [
                    'sell_price',
                    'cost_price',
                ],
                'number',
                'message' => '价格必须是数字',
                'on'      => [
                    'memberpdtmodify_have',
                    'memberpdtadd_have',
                    'memberpdtmodify_provider',
                    'memberpdtadd_provider',
                ],
            ],
            [
                'upgrade_cost_price',
                'number',
                'message' => '价格必须是数字',
            ],
            [
                'payment_cycle',
                'required',
                'message' => '付款周期不能为空',
            ],
            [
                'payment_cycle',
                'number',
                'message' => '付款周期必须是数字',
            ],
            // ['start_time', 'required', 'message'=>'产品开始时间不能为空'],
            //['end_time', 'required', 'message'=>'产品结束时间不能为空'],
            //[['end_time','start_time'], 'number', 'message'=>'时间格式错误'],
            //['ipmi_ip', 'required', 'message'=>'IPMI IP地址不能为空'],
            //['ipmi_name', 'required', 'message'=>'IPMI账户不能为空'],
            //['ipmi_pwd', 'required', 'message'=>'IPMI密码不能为空'],
            [
                'update_time',
                'integer',
            ],
            [
                'servicerprovider',
                'required',
                'message' => '未选择服务器提供商',
            ],

            ///[['remark','note'], 'required', 'message'=>'备注信息不能为空','on'=>['modifyremarks']],
            [
                [
                    'ip',
                    'ip2',
                ],
                'string',
            ],
            [
                'reserved',
                'number',
            ],

        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id'                 => '产品ID',
            'idle_id'            => '闲置产品ID',
            'server_type_id'     => '服务器分类ID',
            'room_id'            => '机房ID',
            'cabinet_id'         => '机柜ID',
            'pdt_id'             => '产品配置类别ID',
            'property'           => '服务器产品产权',
            'type_id'            => '产品属性ID',
            'attribute_id'       => '服务器状态属性ID',
            'admin_id'           => '管理员ID',
            'user_id'            => '产品所属用户ID',
            'user_name'          => '产品所属用户名',
            'bandwidth'          => '客户要求带宽',
            'real_bandwidth'     => '实际带宽',
            'switch_location'    => '交换机位置',
            'switch_port'        => '交换机端口',
            'occupies_position'  => '所占机位',
            'unionid'            => 'Unionid',
            'sell_price'         => '销售价格',
            'cost_price'         => '基础成本价格',
            'upgrade_cost_price' => '升级成本价格',
            'payment_cycle'      => '付款周期',
            'start_time'         => '服务开始时间',
            'end_time'           => '服务结束时间',
            'remark'             => '备注',
            'ipmi_ip'            => 'IPMI地址',
            'ipmi_name'          => 'IPMI用户名',
            'ipmi_pwd'           => 'IPMI密码',
            'config'             => '产品配置',
            'ip'                 => '服务器IP',
            'ip2'                => 'IP集合',
            'trade_no'           => '订单号',
            'servicerprovider'   => '服务器提供商',
            'provider_id'        => '供应商ID',
            'note'               => '配置说明',
            'status'             => '状态',
            'updte_time'         => '修改时间',
            'reserved'           => '预留字段',
            'update_time'        => '修改时间',
            'audit_status'       => '审核状态',
            'is_auto'            => '自动续费',
            'delete_time'        => '删除时间',
        ];
    }

    public function scenarios()
    {
        return array_merge(parent::scenarios(), [
            'modify'                      => [
                'ipmi_ip',
                'sell_price',
                'payment_cycle',
                'cost_price',
                'upgrade_cost_price',
                'remark',
                'note',
                'server_id',
                'ext_data'
            ],
            'update_time'                 => [
                'start_time',
                'end_time',
            ],
            //后台添加用户产品  自有且选择了用户
            'memberpdtadd_have'           => [
                'room_id',
                'servicerprovider',
                'server_type_id',
                'provider_id',
                'cabinet_id',
                'pdt_id',
                'property',
                'type_id',
                'attribute_id',
                'admin_id',
                'user_id',
                'user_name',
                'bandwidth',
                'real_bandwidth',
                'switch_location',
                'switch_port',
                'occupies_position',
                'ip',
                'ip2',
                'unionid',
                'sell_price',
                'cost_price',
                'upgrade_cost_price',
                'payment_cycle',
                'start_time',
                'end_time',
                'ipmi_ip',
                'ipmi_name',
                'ipmi_pwd',
                'config',
                'remark',
                'note',
                'trade_no',
                'idle_id',
                'audit_status',
            ],
            ////后台修改用户产品  自有且选择了用户
            'memberpdtmodify_have'        => [
                'room_id',
                'servicerprovider',
                'server_type_id',
                'provider_id',
                'cabinet_id',
                'pdt_id',
                'property',
                'type_id',
                'attribute_id',
                'admin_id',
                'user_id',
                'user_name',
                'bandwidth',
                'real_bandwidth',
                'switch_location',
                'switch_port',
                'occupies_position',
                'ip',
                'ip2',
                'unionid',
                'sell_price',
                'cost_price',
                'upgrade_cost_price',
                'payment_cycle',
                'start_time',
                'end_time',
                'ipmi_ip',
                'ipmi_name',
                'ipmi_pwd',
                'config',
                'remark',
                'note',
                'trade_no',
                'idle_id',
                'audit_status',
            ],
            //后台添加用户产品  供应商所有且选择了用户
            'memberpdtadd_provider'       => [
                'room_id',
                'servicerprovider',
                'server_type_id',
                'provider_id',
                'cabinet_id',
                'pdt_id',
                'property',
                'type_id',
                'attribute_id',
                'admin_id',
                'user_id',
                'user_name',
                'bandwidth',
                'real_bandwidth',
                'switch_location',
                'switch_port',
                'occupies_position',
                'ip',
                'ip2',
                'unionid',
                'sell_price',
                'cost_price',
                'upgrade_cost_price',
                'payment_cycle',
                'start_time',
                'end_time',
                'ipmi_ip',
                'ipmi_name',
                'ipmi_pwd',
                'config',
                'remark',
                'note',
                'trade_no',
                'idle_id',
                'audit_status',
            ],
            //后台修改用户产品  供应商所有且选择了用户
            'memberpdtmodify_provider'    => [
                'room_id',
                'servicerprovider',
                'server_type_id',
                'provider_id',
                'cabinet_id',
                'pdt_id',
                'property',
                'type_id',
                'attribute_id',
                'admin_id',
                'user_id',
                'user_name',
                'bandwidth',
                'real_bandwidth',
                'switch_location',
                'switch_port',
                'occupies_position',
                'ip',
                'ip2',
                'unionid',
                'sell_price',
                'cost_price',
                'upgrade_cost_price',
                'payment_cycle',
                'start_time',
                'end_time',
                'ipmi_ip',
                'ipmi_name',
                'ipmi_pwd',
                'config',
                'remark',
                'note',
                'trade_no',
                'idle_id',
                'audit_status',
            ],
            //后台分配用户产品  自有
            'memberpdtassigned_have'      => [
                'room_id',
                'servicerprovider',
                'server_type_id',
                'provider_id',
                'cabinet_id',
                'pdt_id',
                'property',
                'type_id',
                'attribute_id',
                'admin_id',
                'user_id',
                'user_name',
                'bandwidth',
                'real_bandwidth',
                'switch_location',
                'switch_port',
                'occupies_position',
                'ip',
                'ip2',
                'unionid',
                'sell_price',
                'payment_cycle',
                'start_time',
                'end_time',
                'ipmi_ip',
                'ipmi_name',
                'ipmi_pwd',
                'config',
                'remark',
                'note',
                'trade_no',
                'idle_id',
                'audit_status',
                'status',
            ],
            //后台分配用户产品  供应商
            'memberpdtassigned_provider'  => [
                'room_id',
                'servicerprovider',
                'server_type_id',
                'provider_id',
                'cabinet_id',
                'pdt_id',
                'property',
                'type_id',
                'attribute_id',
                'admin_id',
                'user_id',
                'user_name',
                'bandwidth',
                'real_bandwidth',
                'switch_location',
                'switch_port',
                'occupies_position',
                'ip',
                'ip2',
                'unionid',
                'sell_price',
                'payment_cycle',
                'start_time',
                'end_time',
                'ipmi_ip',
                'ipmi_name',
                'ipmi_pwd',
                'config',
                'remark',
                'note',
                'trade_no',
                'idle_id',
                'audit_status',
                'status',
            ],
            //前台用户自己购买
            'memberpdtuseradd'            => [
                'room_id',
                'servicerprovider',
                'server_type_id',
                'provider_id',
                'cabinet_id',
                'pdt_id',
                'admin_id',
                'user_id',
                'user_name',
                'unionid',
                'sell_price',
                'payment_cycle',
                'start_time',
                'end_time',
                'config',
                'remark',
                'trade_no',
                'ip',
                'ip2',
                'type_id',
                'property',
                'audit_status',
            ],
            //机器更换  自有
            'machine_allocation_have'     => [
                'room_id',
                'servicerprovider',
                'server_type_id',
                'provider_id',
                'cabinet_id',
                'pdt_id',
                'property',
                'type_id',
                'attribute_id',
                'admin_id',
                'user_id',
                'user_name',
                'switch_location',
                'switch_port',
                'occupies_position',
                'ip',
                'ip2',
                'ipmi_ip',
                'ipmi_name',
                'ipmi_pwd',
                'config',
                'remark',
                'note',
                'trade_no',
                'idle_id',
                'audit_status',
            ],
            //机器更换  供应商
            'machine_allocation_provider' => [
                'room_id',
                'servicerprovider',
                'server_type_id',
                'provider_id',
                'cabinet_id',
                'pdt_id',
                'property',
                'type_id',
                'attribute_id',
                'admin_id',
                'user_id',
                'user_name',
                'switch_location',
                'switch_port',
                'occupies_position',
                'ip',
                'ip2',
                'ipmi_ip',
                'ipmi_name',
                'ipmi_pwd',
                'config',
                'remark',
                'note',
                'trade_no',
                'idle_id',
                'audit_status',
            ],
            //更改备注
            'modifyremarks'               => [
                'remark',
                'note',
            ],
            'modifystatus'                => ['status'],
        ]);
    }

    /**
     * 获取列表数据(联合查询)
     */
    public function getListAll(array $condition = [], $isObj = false)
    {
        empty($this->fields) && $this->fields = [
            '*',
        ];
        //$model = $this->find()->select($this->fields)->where($condition)->orderBy('end_time desc, id desc');

        $model = $this->find()->select($this->fields)->With('servertype')->With('pdtroom')->With('pdtcabinet')->
        With('pdtmanage')->With('pdttype')->With('serverattribute')->With('provider')->With('trade')->With('switch')->
        With('usermember')->With('useradmin')->where($condition)->orderBy('id desc');
        if ($isObj)
            return $model;
        return $model->asArray()->all();
    }

    /**
     * 关联订单
     */
    public function getServertype()
    {
        return $this->hasMany(PdtManageType::className(), ['type_id' => 'server_type_id']);
    }

    /**
     * 关联订单
     */
    public function getTrade()
    {
        return $this->hasMany(Trade::className(), ['trade_orderid' => 'trade_no']);
    }

    public function getPipeline()
    {
        return $this->hasOne(PipelineList::className(), ['order_id' => 'trade_no']);
    }

    /**
     * 关联产品数据库表
     *  第一个参数为要关联的字表模型类名称，
     *第二个参数指定 通过子表的pdt_id 去关联主表的 id 字段
     */
    public function getPdtmanage()
    {
        return $this->hasMany(PdtManage::className(), ['id' => 'pdt_id']);
    }

    /**
     * 关联机房
     *  第一个参数为要关联的字表模型类名称，
     *第二个参数指定 通过子表的room_id 去关联主表的 id 字段
     */
    public function getPdtroom()
    {
        return $this->hasMany(PdtRoomMange::className(), ['id' => 'room_id']);
    }

    public function getPdttype()
    {
        return $this->hasMany(PdtType::className(), ['id' => 'type_id']);
    }

    /**
     * 关联服务器状态属性
     */
    public function getServerattribute()
    {
        return $this->hasMany(ServerAttribute::className(), ['id' => 'attribute_id']);
    }

    /**
     * 关联供应商
     */
    public function getProvider()
    {
        return $this->hasMany(Provider::className(), ['id' => 'provider_id']);
    }

    /**
     * 关联交换机
     */
    public function getSwitch()
    {
        return $this->hasMany(SwitchManage::className(), ['id' => 'switch_location']);
    }

    /**
     * 关联闲置产品
     */
    public function getIdlepdt()
    {
        return $this->hasMany(IdlePdt::className(), ['id' => 'idle_id']);//->select(['id','idle_name']);
    }

        /**
     * 关联闲置产品
     */
    public function getIdlepdtone()
    {
        return $this->hasOne(IdlePdt::className(), ['id' => 'idle_id']);//->select(['id','idle_name']);
    }

    /**
     * 关联机柜
     *  第一个参数为要关联的字表模型类名称，
     *第二个参数指定 通过子表的room_id 去关联主表的 id 字段
     */
    public function getPdtcabinet()
    {
        return $this->hasMany(PdtCabinetManage::className(), ['id' => 'cabinet_id']);
    }

    /**
     * 关联用户
     *  第一个参数为要关联的字表模型类名称，
     *第二个参数指定 通过子表的room_id 去关联主表的 id 字段
     */
    public function getUsermember()
    {
        return $this->hasMany(UserMember::className(), ['u_id' => 'user_id']);
    }

    /**
     * 关联用户
     *  第一个参数为要关联的字表模型类名称，
     *第二个参数指定 通过子表的room_id 去关联主表的 id 字段
     */
    public function getUseradmin()
    {
        return $this->hasMany(UserAdmin::className(), ['admin_id' => 'admin_id']);
    }

    /**
     * 获取即将到期列表数据
     *
     * @param array  $fields
     * @param array  $condition
     * @param string $isObj
     *
     * @return unknown
     */
    public function getExpiringListAll(array $condition = [], $isObj = false)
    {
        empty($this->fields) && $this->fields = [
            "*",
        ];

        $model = $this->find()->select($this->fields)->
        where('status=1 and end_time=:time', [':time' => time() + 7 * 24 * 3600])->
        andWhere($condition);

        if ($isObj)
            return $model;

        return $model->asArray()->all();
    }

    /**
     * 获取当前可使用列表数据(结束时间大于当前时间+7天)
     *
     * @param array  $fields
     * @param array  $condition
     * @param string $isObj
     *
     * @return unknown
     */
    public function getAllowListAll(array $condition = [], $isObj = false)
    {
        empty($this->fields) && $this->fields = [
            "*",
        ];

        $model = $this->find()->select($this->fields)->
        where('status=:status', [':status' => 1])->
        andWhere($condition);

        if ($isObj)
            return $model;

        return $model->asArray()->all();
    }

    /**
     * 获取已经过期列表数据
     *
     * @param array  $fields
     * @param array  $condition
     * @param string $isObj
     *
     * @return unknown
     */
    public function getOverListAll(array $condition = [], $isObj = false)
    {
        empty($this->fields) && $this->fields = [
            "*",
        ];

        $model = $this->find()->select($this->fields)->where('end_time<:time', [':time' => time()])->andWhere($condition);

        if ($isObj)
            return $model;

        return $model->asArray()->all();
    }

    /**
     * 创建搜索条件
     *
     * @param unknown $query
     * @param unknown $params
     */
    public function createSearchWhere(&$query, $params,$serchAccout = false)
    {
        /* if ( isset($params['ip']) && !empty($params['ip']) )
         {
             $query->andwhere('locate(:ip2, ip2)', [':ip2'=>trim($params['ip'])]);
         }*/
        if (ArrayHelper::getValue($params, 'idle_id')) {
            $query->andwhere(['idle_id' => ArrayHelper::getValue($params, 'idle_id')]);
        }
        if (isset($params['query_mode']) && !empty($params['query_mode'])) {
            if ($params['query_mode'] == "vague_query") {
                $query->andwhere('locate(:ip2, ip2)', [':ip2' => trim($params['ip'])]);
            } elseif ($params['query_mode'] == "exact_query") {
                if (isset($params['ip']) && !empty($params['ip'])) {
                    $query->andwhere('locate(:ip2, ip2)', [':ip2' => '"' . trim($params['ip'] . '"')]);
                    //$query->andWhere(new Expression('FIND_IN_SET(:ip2, ip2)'), [':ip2'=>trim($params['ip'])]);
                }
            }
        } else {
            if (isset($params['ip']) && !empty($params['ip'])) {
                //$query->andwhere('locate(:ip2, ip2)', [':ip2'=>'"'.trim($params['ip'].'"')]);
                $query->andwhere('locate(:ip2, ip2)', [':ip2' => trim($params['ip'])]);
            }
        }
        if (isset($params['user_nickname']) && !empty($params['user_nickname'])) {
            $UserMemberModel = new UserMember();
            $UserMemberList  = $UserMemberModel->find()->select('u_id')->where(['like', 'uname', trim($params['user_nickname']),])->asArray()->all();
            if (!empty($UserMemberList)) {
                $userid_Arr = array_column($UserMemberList, 'u_id');
                $query->andwhere([
                    'in',
                    'user_id',
                    $userid_Arr,
                ]);
            } else {
                $query->andwhere('user_id = "" ');
            }
        }

        if (isset($params['qq']) && !empty($params['qq'])) {
            $UserMemberModel = new UserMember();
            $UserMemberList  = $UserMemberModel->find()->select('u_id')->where(['like', 'qq', trim($params['qq']),])->asArray()->all();
            if (!empty($UserMemberList)) {
                $userid_Arr = array_column($UserMemberList, 'u_id');
                $query->andwhere([
                    'in',
                    'user_id',
                    $userid_Arr,
                ]);
            } else {
                $query->andwhere('user_id = "" ');
            }
        }

        if (isset($params['unionid']) && !empty($params['unionid'])) {
            $query->andwhere('locate(:unionid, unionid)', [':unionid' => trim($params['unionid'])]);
        }
        if (isset($params['ipmi_ip']) && !empty($params['ipmi_ip'])) {
            $query->andwhere('locate(:ipmi_ip, ipmi_ip)', [':ipmi_ip' => trim($params['ipmi_ip'])]);
        }
        if (isset($params['user_name']) && !empty($params['user_name'])) {
            if($serchAccout){
                $UserMemberModel = new UserMember();
                $UserMemberList  = $UserMemberModel->find()
                    ->select('u_id')
                    ->where(['like', 'username', trim($params['user_name'])])
                    ->orWhere(['like', 'email', trim($params['user_name'])])
                    ->orWhere(['like', 'mobile', trim($params['user_name'])])
                    ->asArray()->all();
                if (!empty($UserMemberList)) {
                    $userid_Arr = array_column($UserMemberList, 'u_id');
                    $query->andwhere([
                        'in',
                        'user_id',
                        $userid_Arr,
                    ]);
                } else {
                    $query->andwhere('user_id = "" ');
                }
            }else{
                $query->andwhere('locate(:user_name, user_name)', [':user_name' => trim($params['user_name'])]);
            }

        }
        if (isset($params['bandwidth']) && !empty($params['bandwidth'])) {
            $query->andwhere('locate(:bandwidth, bandwidth)', [':bandwidth' => trim($params['bandwidth'])]);
        }
        if (isset($params['server_type_id']) && !empty($params['server_type_id'])) {
            $query->andwhere(':server_type_id = server_type_id', [':server_type_id' => trim($params['server_type_id'])]);
        }
        if (isset($params['room_id']) && !empty($params['room_id'])) {
            $query->andwhere(':room_id = room_id', [':room_id' => trim($params['room_id'])]);
        }
        if (isset($params['cabinet_id']) && !empty($params['cabinet_id'])) {
            $query->andwhere(':cabinet_id = cabinet_id', [':cabinet_id' => trim($params['cabinet_id'])]);
        }
        if (isset($params['admin_id']) && !empty($params['admin_id'])) {
            $query->andwhere(':admin_id = admin_id', [':admin_id' => trim($params['admin_id'])]);
        }
        if (isset($params['user_id']) && !empty($params['user_id'])) {
            $query->andwhere(':user_id = user_id', [':user_id' => trim($params['user_id'])]);
        }
        if (isset($params['pdt_id']) && !empty($params['pdt_id'])) {
            $query->andwhere(':pdt_id = pdt_id', [':pdt_id' => trim($params['pdt_id'])]);
        }
        if (isset($params['attribute_id']) && !empty($params['attribute_id'])) {
            $query->andwhere(':attribute_id = attribute_id', [':attribute_id' => trim($params['attribute_id'])]);
        }
        if (isset($params['start_time']) && !empty($params['start_time']) && strtotime($params['start_time']) != false) {
            $query->andWhere([
                '>=',
                'start_time',
                strtotime($params['start_time']),
            ]);
        }
        if (isset($params['time']) && !empty($params['time']) && strtotime($params['time']) != false) {
            $query->andWhere([
                '>=',
                'end_time',
                strtotime($params['time']),
            ]);
        }
        if (isset($params['end_time']) && !empty($params['end_time']) && strtotime($params['end_time']) != false) {
            $query->andWhere([
                '<=',
                'end_time',
                strtotime($params['end_time']),
            ]);
        }

        if (isset($params['status']) && in_array($params['status'], [0, 1, 2, 3, 4, -2, -1,]) && $params['status'] !== '') {
            $query->andWhere(['status' => $params['status']]);
        }
        if (isset($params['audit_status']) && in_array($params['audit_status'], [
                0,
                1,
            ]) && $params['audit_status'] !== '') {
            $query->andWhere(['audit_status' => $params['audit_status']]);
        }
        if (isset($params['endtype']) && in_array($params['endtype'], [1, 2, 3, 4, 5, 6]) && $params['endtype'] !== '') {
            if ($params['endtype'] == 1) {
                $query->andWhere([
                    '<',
                    'end_time',
                    time(),
                ]);
            }
            if ($params['endtype'] == 2) {
                $query->andWhere([
                    '<',
                    'end_time',
                    time() + 7 * 24 * 3600,
                ]);
            }
            if ($params['endtype'] == 3) {
                $query->andWhere([
                    '<',
                    'end_time',
                    time() + 30 * 24 * 3600,
                ]);
            }
            if ($params['endtype'] == 4) {
                $query->andWhere([
                    '>=',
                    'start_time',
                    time() - 7 * 24 * 3600,
                ]);
            }
            if ($params['endtype'] == 5) {
                $query->andWhere([
                    '>=',
                    'start_time',
                    time() - 30 * 24 * 3600,
                ]);
            }
        }

        return;
    }

    /**
     * 通过id 获取一条指定数据
     */
    public function getRowById($id, $condition = [], $isObj = false)
    {
        if ($isObj)
            return $this->findOne($id);

        empty($this->fields) && $this->fields = [
            '*',
        ];

        return $this->find()->select($this->fields)->With('idlepdt')->With('provider')->With('switch')->where('id=:id', [':id' => $id])->andWhere($condition)->limit(1)->asArray()->one();
    }

    /**
     * 通过id(关联查询)获取一条指定数据
     */
    public function getRelationById($id, $condition = [], $isObj = false)
    {
        if ($isObj)
            return $this->findOne($id);

        empty($this->fields) && $this->fields = [
            '*',
        ];

        return $this->find()->select($this->fields)->With('pdtroom')->With('pdtmanage')->With('pdttype')->
        where('id=:id', [':id' => $id])->andWhere($condition)->limit(1)->asArray()->one();
    }

    /**
     * 后台添加用户产品处理方法
     *
     * @return boolean
     */
    public function doadd($status)
    {
        #状态默认为0  表示未审核  1为使用中  -1 为下架  2为闲置中
        $this->status       = $status;
        $this->audit_status = "0";
        return $this->insert($runValidation = false);
    }

    /**
     * 后台添加用户产品后审核
     *
     * @return boolean
     */
    public function doReviewed($id)
    {
        $query = $this->findOne($id);

        #状态默认为0  表示未审核     1为正常使用        
        //$query->status = 1;
        $query->audit_status = 1;
        return $query->update($runValidation = false);
    }

    /**
     * 后台添加用户产品下架
     *
     * @return boolean
     */
    public function doshutdown($id)
    {
        $query = $this->findOne($id);

        #状态默认为0  表示未审核     1为正常使用 -1为下架
        $query->status = -1;

        return $query->update($runValidation = false);
    }

    /**
     * 续费更新 修改时间，更改周期和价格
     */
    public function Renew($id, $renew_num)
    {
        $unit       = "Month";
        $QueryModel = $this->find()->where('id=:id', [':id' => $id])->limit(1)->one();

        if ($QueryModel->payment_cycle == 1) {
            $expire = $renew_num * 1;
        } elseif ($QueryModel->payment_cycle == 3) {
            $expire = $renew_num * 3;
        } elseif ($QueryModel->payment_cycle == 6) {
            $expire = $renew_num * 6;
        } elseif ($QueryModel->payment_cycle == 12) {
            $expire = $renew_num * 12;
        }
        //$QueryModel->payment_cycle = $expire;
        //$QueryModel->sell_price = $sell_price;
        //$QueryModel->cost_price = $cost_price * $expire;

        $QueryModel->end_time = strtotime("+$expire " . $unit, $QueryModel->end_time);

        $QueryModel->status = '1';

        return $QueryModel->update($runValidation = false);
    }

    /**
     * 续费更新 修改时间，更改周期和价格
     */
    public function RenewServer($id, $user_id, $renew_num, &$QueryModel = null)
    {
        $unit = "Month";
        empty($QueryModel) && $QueryModel = $this->find()->where('id=:id and user_id=:user_id', [
            ':id'      => $id,
            ':user_id' => $user_id,
        ])->limit(1)->one();

        if ($QueryModel->payment_cycle == 1) {
            $expire = $renew_num * 1;
        } elseif ($QueryModel->payment_cycle == 3) {
            $expire = $renew_num * 3;
        } elseif ($QueryModel->payment_cycle == 6) {
            $expire = $renew_num * 6;
        } elseif ($QueryModel->payment_cycle == 12) {
            $expire = $renew_num * 12;
        }
        //$QueryModel->sell_price = $sell_price;
        //$QueryModel->cost_price = $cost_price * $expire;
        $QueryModel->end_time = strtotime("+$expire " . $unit, $QueryModel->end_time);

        $QueryModel->status = '1';

        return $QueryModel->update(false);
    }

    /**
     * 删除
     *
     * @param unknown $id
     *
     * @return boolean
     */
    public function del($id)
    {
        //多选删除
        if (is_array($id))
            return $this->deleteAll(['id' => $id]);

        $query = $this->find()->where('id=:id', [':id' => $id])->limit(1)->one();

        if (empty($query))
            return false;

        return $query->delete();

    }

    /*
    *新版
    */
    public function synchronized_updates($id, $query)
    {
        $model = $this->findOne($id);
        //$model->servicerprovider = $query['servicerprovider'];
        $model->provider_id    = $query['provider_id'];
        $model->server_type_id = $query['server_type_id'];
        //$model->room_id = $query['room_id'];
        //$model->cabinet_id = $query['cabinet_id'];
        $model->pdt_id   = $query['pdt_id'];
        $model->property = $query['property'];
        $model->type_id  = $query['type_id'];
        //$model->attribute_id = $query['attribute_id'];
        $model->switch_location   = $query['switch_location'];
        $model->switch_port       = $query['switch_port'];
        $model->occupies_position = $query['occupies_position'];
        $model->ipmi_ip           = $query['ipmi_ip'];
        $model->ipmi_name         = $query['ipmi_name'];
        $model->ipmi_pwd          = $query['ipmi_pwd'];
        $model->config            = $query['config'];
        $model->ip                = $query['ip'];
        $model->ip2               = $query['ip2'];
        if (isset($query['real_bandwidth'])) {
            $model->real_bandwidth = $query['real_bandwidth'];
        }

        $model->update_time = time();

        return $model->update(false);
    }

    public function doupdate($id, $query)
    {
        $model = $this->findOne($id);
        //$model->servicerprovider = $query['servicerprovider'];
        $model->provider_id    = $query['provider_id'];
        $model->server_type_id = $query['server_type_id'];
        //$model->room_id = $query['room_id'];
        $model->cabinet_id = $query['cabinet_id'];
        $model->pdt_id     = $query['pdt_id'];
        $model->property   = $query['property'];
        if (isset($query['type_id'])) {
            $model->type_id = $query['type_id'];
        }
        //$model->attribute_id = $query['attribute_id'];
        $model->switch_location   = $query['switch_location'];
        $model->switch_port       = $query['switch_port'];
        $model->occupies_position = $query['occupies_position'];
        $model->ipmi_ip           = $query['ipmi_ip'];
        $model->ipmi_name         = $query['ipmi_name'];
        $model->ipmi_pwd          = $query['ipmi_pwd'];
        $model->config            = $query['config'];

        if (isset($query['ip'])) {
            $model->ip = $query['ip'];
        }
        if (isset($query['ip2'])) {
            $model->ip2 = $query['ip2'];
        }
        if (isset($query['real_bandwidth'])) {
            $model->real_bandwidth = $query['real_bandwidth'];
        }

        $model->update_time = time();

        return $model->update(false);
    }

    public function doupdateandstatus($id, $query, $status)
    {
        $model             = $this->findOne($id);
        $model->attributes = $query;

        $model->status      = $status;
        $model->update_time = time();
        //print_r($model);die();
        return $model->update(false);
    }

    public function findWithIdle($id){
        return $this->find()->where(['id' => $id])->with('idlepdtone')->one();
    }
}
