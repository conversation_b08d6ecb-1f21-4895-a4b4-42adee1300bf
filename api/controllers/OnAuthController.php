<?php

namespace api\controllers;

use addons\VymDesen\common\models\UserMember\UserMember;
use common\enums\AppEnum;
use common\models\member\Member;
use common\traits\MerchantTrait;
use Exception;
use Yii;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;
use common\enums\StatusEnum;
use common\helpers\ArrayHelper;
use common\helpers\MerchantHelper;
use common\helpers\ResultHelper;
use yii\web\UnauthorizedHttpException;

/**
 * 需要授权登录访问基类
 *
 * Class OnAuthController
 * @package api\controllers
 * @property yii\db\ActiveRecord|yii\base\Model $modelClass
 * <AUTHOR> <<EMAIL>>
 */
class OnAuthController extends ActiveController
{
    use MerchantTrait;

    /**
     * @var \yii\console\Request|\yii\web\Request
     */
    private $_request;

    /**
     * 当前用户ID
     *
     * @var $_uid
     */
    protected $_uid;


    /**
     *
     * 商户id
     * @var $_merchantId
     */
    protected $_merchantId;


    /**
     * 当前user_member_id
     * @var $_mid
     */
    protected $_mid = null;

    protected $user = null;

    public function init()
    {
        parent::init();
        $this->_request = Yii::$app->request;
    }

    public function beforeAction($action)
    {
        $before = parent::beforeAction($action);
        if (!Yii::$app->user->isGuest) {
            $this->_uid        = ArrayHelper::getValue(Yii::$app->user->identity, "member_id");
            $this->_merchantId = ArrayHelper::getValue(Yii::$app->user->identity, "merchant_id");
            //设置商户ID
            Yii::$app->services->merchant->setId($this->_merchantId);
        }
        else {
            //未登陆时,也需要获取商户ID
            $this->_merchantId = $this->buildMerIdByDomain();
        }
        return $before;
    }

    /**
     * 根据域名获取并设置商户ID
     */
    public function buildMerIdByDomain(){
        $merId = $this->getMerIdByDomain();
        //赋值参数
        Yii::$app->params['merchantId'] = $merId;
        Yii::$app->services->merchant->setId($merId);
        return $merId;
    }

    /**
     * @return bool
     */
    public function isPost()
    {
        return $this->_request->getIsPost();
    }


    /**
     * @param null $key
     * @param null $default
     *
     * @return array|mixed
     */
    public function post($key = null, $default = null)
    {
        return $this->_request->post($key, $default);
    }

    /**
     * @param null $key
     * @param null $default
     *
     * @return array|mixed
     */
    public function get($key = null, $default = null)
    {
        return $this->_request->get($key, $default);
    }

    /**
     * @param null $key
     * @param null $default
     *
     * @return array|string
     */
    public function header($key = null, $default = null)
    {
        return $this->_request->getHeaders()->get($key, $default);
    }

    /**
     * @return array
     */
    public function actions()
    {
        $actions = parent::actions();
        // 注销系统自带的实现方法
        unset($actions['index'], $actions['update'], $actions['create'], $actions['view'], $actions['delete']);
        // 自定义数据indexDataProvider覆盖IndexAction中的prepareDataProvider()方法
        // $actions['index']['prepareDataProvider'] = [$this, 'indexDataProvider'];
        return $actions;
    }

    public function runAction($actionID, $params = [])
    {
        try {
            return parent::runAction($actionID, $params);
        } catch (\Throwable $e) {
            if($e instanceof UnauthorizedHttpException){

                return ResultHelper::json(401,"登录信息失效");
            }else{
                $retData = [
                    'code' => 500,
                    'msg' => $e->getMessage(),
                ];
            }

            if(YII_DEBUG) {
                $retData['stack'] = $e->getTraceAsString();
            }
            // 统一处理异常
            // return $this->asJson($retData);
            return ResultHelper::json(500, $e->getMessage(), $retData);
        }
    }

    /**
     * 首页
     *
     * @return ActiveDataProvider
     */
    public function actionIndex()
    {
        return new ActiveDataProvider([
            'query'      => $this->modelClass::find()
                                             ->where(['status' => StatusEnum::ENABLED])
                                             ->andFilterWhere(['merchant_id' => $this->getMerchantId()])
                                             ->orderBy('id desc')
                                             ->asArray(),
            'pagination' => [
                'pageSize'     => $this->pageSize,
                'validatePage' => false,// 超出分页不返回data
            ],
        ]);
    }

    /**
     * 创建
     *
     * @return mixed|\yii\db\ActiveRecord
     */
    public function actionCreate()
    {
        /* @var $model \yii\db\ActiveRecord */
        $model              = new $this->modelClass();
        $model->attributes  = Yii::$app->request->post();
        $model->member_id   = Yii::$app->user->identity->member_id;
        $model->merchant_id = Yii::$app->user->identity->merchant_id;
        if (!$model->save()) {
            return ResultHelper::json(422, $this->getError($model));
        }

        return $model;
    }

    /**
     * 更新
     *
     * @param $id
     *
     * @return mixed|\yii\db\ActiveRecord
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id)
    {
        $model             = $this->findModel($id);
        $model->attributes = Yii::$app->request->post();
        if (!$model->save()) {
            return ResultHelper::json(422, $this->getError($model));
        }

        return $model;
    }

    /**
     * 删除
     *
     * @param $id
     *
     * @return bool
     * @throws NotFoundHttpException
     */
    public function actionDelete($id)
    {
        $model         = $this->findModel($id);
        $model->status = StatusEnum::DELETE;

        return $model->save();
    }

    /**
     * 单个显示
     *
     * @param $id
     *
     * @return \yii\db\ActiveRecord
     * @throws NotFoundHttpException
     */
    public function actionView($id)
    {
        return $this->findModel($id);
    }

    /**
     * @param $id
     *
     * @return \yii\db\ActiveRecord
     * @throws NotFoundHttpException
     */
    protected function findModel($id)
    {
        /* @var $model \yii\db\ActiveRecord */
        if (empty($id) || !($model = $this->modelClass::find()->where([
                'id'     => $id,
                'status' => StatusEnum::ENABLED,
            ])->andFilterWhere(['merchant_id' => $this->getMerchantId()])->one())) {
            throw new NotFoundHttpException('请求的数据不存在');
        }

        return $model;
    }

    protected function getUserMemberId()
    {
        if (null === $this->_mid) {
            $user = $this->getUser();
            if($user){
                $this->_mid = $user->ds_uid;
            }else{
                $this->_mid = 0;
            }
        }
        return $this->_mid;
    }

    public function getUser(){
        if($this->user == null){
            $this->user = Member::find()->where(["id"=>$this->_uid])->one();
        }
        return $this->user;
    }

    public function errorMsg(string $msg){
        return ResultHelper::json(422, $msg);
    }

    public function success($data, $msg = ''){
        return ResultHelper::json(200, $msg , $data);
    }

    public function getUserName(){
        if(Yii::$app->user->isGuest){
            return '';
        }
        $user = Yii::$app->user->identity;
        return ArrayHelper::getValue($user, 'member_id');
    }

    public function getUid(){
        return $this->_uid;
    }

    /**
     * 根据域名获取商户ID
     */
    public function getMerIdByDomain($domain = null){
        return MerchantHelper::getMerchantIdByDomain(AppEnum::MERCHANT, $domain);
    }

    public function getProxy() {
        return $this;
    }

    public function isAdminForProxy(){
        return false;
    }

    public function getUidForProxy() {
        return $this->getUserMemberId();
    }

}
