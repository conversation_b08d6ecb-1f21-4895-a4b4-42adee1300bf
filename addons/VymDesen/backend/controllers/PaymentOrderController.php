<?php

namespace addons\VymDesen\backend\controllers;

use addons\VymDesen\backend\models\UserAdmin\UserAdmin;
use addons\VymDesen\backend\controllers\BaseController;
use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\components\pay\BillModel;
use addons\VymDesen\common\components\pay\PaymentOperation;
use addons\VymDesen\common\models\AdminSystemConfig;
use addons\VymDesen\common\models\Finance\FinanceFundflow;
use addons\VymDesen\common\models\Payment\PaymentAccount;
use addons\VymDesen\common\models\Payment\PaymentAudit;
use addons\VymDesen\common\models\Payment\PaymentWay;
use addons\VymDesen\common\models\PayOrder\PaymentFlow;
use addons\VymDesen\common\models\PayOrder\PayorderDetail;
use addons\VymDesen\common\models\PayOrder\PayorderGeneral;
use addons\VymDesen\common\models\PayOrder\PayorderOriginal;
use addons\VymDesen\common\models\PipeLine\PipelineList;
use addons\VymDesen\common\models\UserCommission;
use addons\VymDesen\common\models\UserCredit\UserCredit;
use addons\VymDesen\common\models\UserCredit\UserCreditHistory;
use addons\VymDesen\common\models\UserIntegral;
use addons\VymDesen\common\models\UserMember\UserMember;
use common\helpers\ArrayHelper as HelpersArrayHelper;
use common\models\member\Member;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;

#工作流相关

#用户收支明细

/**
 * 用户 付款单列表 控制器
 * <AUTHOR>
 *
 */
class PaymentOrderController extends BaseController
{

    #付款单列表
    public function actionList()
    {

        $PayorderGeneralModel = new PayorderGeneral();
        $ConfigModel          = new AdminSystemConfig();
        $UserMemberModel      = new UserMember();

        $PayorderGeneralQuery = $PayorderGeneralModel->find()
            ->joinwith(['original'=> function($query){$query->with(['userMember']);}])
            ->with(['user'])
            ->where('order_status != "待审核"')->andwhere('order_status != "已取消"');
        #创建搜索条件

        $searchQuery = $this->get();

        if (!ArrayHelper::getValue($searchQuery, 'general_pay_lock')) {
            $searchQuery['general_pay_lock'] = '等待支付';
        }

        $PayorderGeneralModel->createSearchWhere($PayorderGeneralQuery, $searchQuery);

        #只看自己
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $configRes  = $ConfigModel->find()->where(['config_name' => 'payorder_default_myself'])->one();
        $configUser = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {

            #查找当前销售所包含的用户
            $userlist    = $UserMemberModel->find()->where(['admin_id' => $admin_id, 'status' => 1])->asArray()->all();
            $userid_list = array_column($userlist, 'u_id');

            $PayorderGeneralQuery->andwhere(['or', ['order_admin_id' => $admin_id], ['in', 'general_payment_userid', $userid_list]]);
        }

        #分页
        $iCount = $PayorderGeneralQuery->count();
        $oPage  = DataHelper::getPage($iCount);

        $PayorderGeneralList = $PayorderGeneralQuery->orderBy('general_create_time DESC')->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();
        #获取客服
        $UserAdminModel         = new UserAdmin();
        $AdminSystemConfigModel = new AdminSystemConfig();
        $salesConfig            = $AdminSystemConfigModel->find()->where(['config_name' => 'order_default_myself'])->asArray()->one();
        $adminlist              = $UserAdminModel->find()->where(['in', 'admin_id', explode(',', $salesConfig['config_value'])])->asArray()->all();

        #print_r($PayorderGeneralList);exit;
        return $this->render('index', [
            'PayorderGeneralList' => $PayorderGeneralList,
            'UserAdminRes'        => $adminlist,
            'iCount'              => $iCount,
            'page'                => $oPage
        ]);
    }

    #后付款列表
    public function actionPaylaterList()
    {

        $PayorderGeneralModel = new PayorderGeneral();
        $PayorderGeneralQuery = $PayorderGeneralModel->find()->with('original')->where(['general_pay_lock' => '后付款']);
        #创建搜索条件
        $PayorderGeneralModel->createSearchWhere($PayorderGeneralQuery, $this->get());
        #分页
        $iCount = $PayorderGeneralQuery->count();
        $oPage  = DataHelper::getPage($iCount);

        $PayorderGeneralList = $PayorderGeneralQuery->orderBy('general_create_time DESC')->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();
        #获取客服
        $UserAdminModel         = new UserAdmin();
        $AdminSystemConfigModel = new AdminSystemConfig();
        $salesConfig            = $AdminSystemConfigModel->find()->where(['config_name' => 'order_default_myself'])->asArray()->one();
        $adminlist              = $UserAdminModel->find()->where(['in', 'admin_id', explode(',', $salesConfig['config_value'])])->asArray()->all();

        #print_r($PayorderGeneralList);exit;
        return $this->render('paylater-list', [
            'PayorderGeneralList' => $PayorderGeneralList,
            'UserAdminRes'        => $adminlist,
            'iCount'              => $iCount,
            'page'                => $oPage
        ]);
    }

    #详情页
    public function actionDetails()
    {
        $get = $this->get();
        if (!$get['payorder_no']) {
            return $this->redirect(Yii::$app->request->referrer);
        }
        $PaymentFlowModel      = new PaymentFlow();
        $PayorderGeneralModel  = new PayorderGeneral();
        $PayorderOriginalModel = new PayorderOriginal();
        $PayorderDetailModel   = new PayorderDetail();

        #如果是销售的权限，那么只能销售自己看
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'payorder_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        $PayorderGeneralRes = $PayorderGeneralModel->find()->where(['general_payorder' => $get['payorder_no']])->asArray()->one();
        if (empty($PayorderGeneralRes)) {
            return $this->redirect(Yii::$app->request->referrer);
        }

        $original_order = $PayorderGeneralRes['general_original_order'];
        #获取原始订单（包含详情）
        $OriginalRes = $PayorderOriginalModel->find()->with('detail')->where(['order_id' => $original_order])->asArray()->one();

        if (in_array($admin_id, $configUser)) {
            if ($OriginalRes['order_admin_id'] != $admin_id) {
                return $this->redirect(["/payment-order/list"]);
            }
        }

        #获取到所属的支付流水
        if ($PayorderGeneralRes['general_payorder_number'] != '' && $PayorderGeneralRes['general_payorder_number'] != null) {
            $PaymentFlowRes = $PaymentFlowModel->find()->where(['payment_flow_no' => $PayorderGeneralRes['general_payorder_number']])->asArray()->one();
        } else {
            $PaymentFlowRes = [];
        }

        return $this->render('detail', [
            'PayorderGeneralRes' => $PayorderGeneralRes,
            'OriginalRes'        => $OriginalRes,
            'PaymentFlowRes'     => $PaymentFlowRes,

        ]);

    }

    #多付款单付款
    public function actionMergePayorderlist()
    {

        $payorder_no = $this->get('payorder_no');
        if (!$payorder_no) {
            return $this->redirect(Url::to(['list']));
        }

        $GeneralModel        = new PayorderGeneral();
        $OriginalModel       = new PayorderOriginal();
        $PaymentAccountModel = new PaymentAccount();
        $PaymentAccountList  = $PaymentAccountModel->find()->asArray()->all();
        #支付单信息
        $orderidList = explode(",", $payorder_no);
        $GeneralList = $GeneralModel->find()->joinwith('original')->where(['in', 'general_payorder', $orderidList])->asArray()->all();

        $user_id = null;

        foreach ($GeneralList as $key => $val) {

            #只取第一个用户，并判断后面的指定付款用户是否是同一个人
            if (!$user_id) {
                $user_id = $val['general_payment_userid'];
            } else {
                if ($val['general_payment_userid'] != $user_id) {
                    return $this->redirect(Url::to(['list']));
                }
            }

            if (!in_array($val['general_pay_lock'], ['等待支付'])) {
                return $this->redirect(Url::to(['list']));
            }

            if ($val['general_payorder_number'] != '' || $val['general_payorder_number'] != null || $val['general_locked_state'] == '已锁定') {
                return $this->redirect(Url::to(['list']));
            }

        }

        #如果是销售的权限，那么只能销售合并支付
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'payorder_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            $UseMemberModel = new UserMember();
            $UseMemberRes   = $UseMemberModel->find()->where(['u_id' => $user_id])->asArray()->one();
            if ($UseMemberRes['admin_id'] != $admin_id) {
                return $this->redirect(["/payment-order/list"]);
            }
        }

        #能够合并付款肯定是一个用户，查询用户是否支持挂账

        $UserCreditModel = new UserCredit();

        $hasCredit = $UserCreditModel->find()->where(['user_id' => $user_id, 'user_credit_status' => '启用'])->asArray()->one();

        $orderTotal = array_sum(array_column($GeneralList, 'general_pay_money'));

        return $this->render('merge-payorderlist', [
            'GeneralList'        => $GeneralList,
            'orderTotal'         => sprintf("%.2f", $orderTotal),
            'payorder_no'        => $payorder_no,
            'PaymentAccountList' => $PaymentAccountList,
            'hasCredit'          => $hasCredit
        ]);
    }

    #线下付款
    public function actionOfflinePayment()
    {
        $PayorderOriginalModel = new PayorderOriginal();
        $PayorderGeneralModel  = new PayorderGeneral();
        $PaymentAccountModel   = new PaymentAccount();
        $UserMemberModel       = new UserMember();
        $PaymentFlowModel      = new PaymentFlow();
        $PaymentAuditModel     = new PaymentAudit();

        $post        = $this->post();
        $payorder_no = $post['payorder_no']; #付款单集合

        if (empty($payorder_no)) {
            $arrReturn = ['status' => 0, 'info' => '未选择付款单'];
            return $this->renderJSON($arrReturn);
        }

        $data = $post['data'];

        $PayorderGeneralList = $PayorderGeneralModel->find()->where(['general_payorder' => $payorder_no])->asArray()->all();
        if (count($PayorderGeneralList) != count($payorder_no)) {
            $arrReturn = ['status' => 0, 'info' => '存在付款单未查询到'];
            return $this->renderJSON($arrReturn);
        }

        foreach ($PayorderGeneralList as $key => $val) {
            if (!in_array($val['general_pay_lock'], ['等待支付'])) {
                $arrReturn = ['status' => 0, 'info' => '付款单状态异常：' . $val['general_payorder'] . '已支付或者已取消'];
                return $this->renderJSON($arrReturn);
            }

            if ($val['general_payorder_number'] != '' || $val['general_payorder_number'] != null || $val['general_locked_state'] == '已锁定') {
                $arrReturn = ['status' => 0, 'info' => '付款单：' . $val['general_payorder'] . '已加入其他支付，请解除重新合并'];
                return $this->renderJSON($arrReturn);
            }
        }

        $member_id_List = array_unique(array_column($PayorderGeneralList, 'general_payment_userid'));
        if (count($member_id_List) > 1) {
            $arrReturn = ['status' => 0, 'info' => '付款单存在多用户支付'];
            return $this->renderJSON($arrReturn);
        }

        $member_id = $member_id_List[0];

        #预赋值
        $total           = array_sum(array_column($PayorderGeneralList, 'general_pay_money')); #获取支付金额
        $payorder_no_str = implode(',', $payorder_no);
        $trans_time      = time();
        $payment_time    = strtotime($data['payment_time']); #echo $payment_time;exit;

        if ($data['payment_amount'] < $total) {
            $arrReturn = ['status' => 0, 'info' => '支付金额小于付款单应付金额'];
            return $this->renderJSON($arrReturn);
        }

        #支付方式
        $PaymentAccountRes = $PaymentAccountModel->find()->where(['id' => $data['payment_platform']])->asArray()->one();

        if (empty($PaymentAccountRes)) {
            $arrReturn = ['status' => 0, 'info' => '未知的支付方式'];
            return $this->renderJSON($arrReturn);
        }

        $transaction = \Yii::$app->db->beginTransaction();

        #生成一个支付流水单号
        $payment_flow_no = DataHelper::createPaymentFlowNO();
        $sign            = md5($payorder_no_str . time() . uniqid());

        $UserMemberQuery = $UserMemberModel->find()->where(['u_id' => $member_id])->one();

        #判定支付流水的支付是否需要审核
        $PaymentAuditRes = $PaymentAuditModel->find()->where(['payment_method' => '线下支付'])->asArray()->one();

        #新增支付流水信息
        if ($PaymentAuditRes['is_audit'] == 'Y') {
            $PaymentFlowModel->payment_audit_status = '未审核';
        } else {
            $PaymentFlowModel->payment_audit_status = '无审核';
        }

        $PaymentFlowModel->payment_flow_type         = '订单支付';
        $PaymentFlowModel->payment_mode              = '线下支付';
        $PaymentFlowModel->payment_flow_no           = $payment_flow_no;                        #支付流水号
        $PaymentFlowModel->payment_payorders         = $payorder_no_str;                    #付款单集合
        $PaymentFlowModel->payable_amount            = $total;                                #应付金额
        $PaymentFlowModel->payment_amount            = $data['payment_amount'];                #金额（这里金额应该用打款金额）
        $PaymentFlowModel->payment_amount_remarks    = $data['payment_remark'];        #金额款项说明
        $PaymentFlowModel->payment_platform          = $PaymentAccountRes['another_name'];    #支付平台
        $PaymentFlowModel->payment_method            = '';                                    #支付方式
        $PaymentFlowModel->payment_trans_no          = $data['payment_trans_no'];            #三方交易号
        $PaymentFlowModel->payment_creattime         = $trans_time;                        #创建时间
        $PaymentFlowModel->payment_settlement_status = '已支付';                    #
        $PaymentFlowModel->payment_successtime       = $payment_time;                        #支付成功时间
        $PaymentFlowModel->payment_callback_content  = '';                            #三方回调内容
        $PaymentFlowModel->payment_callbacktime      = $trans_time;                        #回调时间
        $PaymentFlowModel->admin_id                  = $UserMemberQuery->admin_id;
        $PaymentFlowModel->admin_name                = $UserMemberQuery->admin_name;
        $PaymentFlowModel->user_id                   = $UserMemberQuery->u_id;
        $PaymentFlowModel->user_name                 = $UserMemberQuery->uname;
        $PaymentFlowModel->user_email                = $UserMemberQuery->email;
        $PaymentFlowModel->link_effective_time       = time();
        $PaymentFlowModel->link_sign                 = $sign;

        $InsertPaymentFlow = $PaymentFlowModel->insert();

        if (!$InsertPaymentFlow) {
            $transaction->rollBack();
            $arrReturn = ['status' => 0, 'info' => '提交失败', 'data' => ''];
            return $this->renderJSON($arrReturn);
        }

        if ($PaymentFlowModel->payment_audit_status == '无审核') {

            #当支付金额大于应付金额，退款余额
            if ($data['payment_amount'] > $total) {

                $front_balance = $UserMemberQuery->balance;
                $add_balance   = $data['payment_amount'] - $total;

                $UserMemberQuery->balance = $front_balance + $add_balance;

                if (!$UserMemberQuery->save()) {
                    $transaction->rollBack();
                    $arrReturn = ['status' => 0, 'info' => '用户余额更新失败', 'data' => ''];
                    return $this->renderJSON($arrReturn);
                }

                $after_balance = $UserMemberQuery->balance;

                #增加用户收支明细
                $FinanceFundflowModel                           = new FinanceFundflow();
                $FinanceFundflowModel->user_id                  = $UserMemberQuery->u_id;
                $FinanceFundflowModel->user_name                = $UserMemberQuery->email;
                $FinanceFundflowModel->admin_id                 = $UserMemberQuery->admin_id;
                $FinanceFundflowModel->admin_name               = $UserMemberQuery->admin_name;
                $FinanceFundflowModel->before_transaction_money = $front_balance;
                $FinanceFundflowModel->transaction_money        = $add_balance;
                $FinanceFundflowModel->after_transaction_money  = $after_balance;
                $FinanceFundflowModel->income_or_expense        = '收入';
                $FinanceFundflowModel->transaction_type         = '充值';
                $FinanceFundflowModel->transaction_mode         = '线下支付';
                $FinanceFundflowModel->transaction_channel      = $PaymentFlowModel->payment_platform;
                $FinanceFundflowModel->unionid                  = '';
                $FinanceFundflowModel->flow_id                  = '';
                $FinanceFundflowModel->orderid                  = '';
                $FinanceFundflowModel->rechargeid               = '';
                $FinanceFundflowModel->payment_flow_no          = $payment_flow_no;
                $FinanceFundflowModel->line_id                  = '';
                $FinanceFundflowModel->transaction_description  = '线下支付款项溢出，余额返还';
                $FinanceFundflowModel->transaction_time         = $trans_time;

                $InsertFundflowRes = $FinanceFundflowModel->insert();
                if (!$InsertFundflowRes) {
                    $transaction->rollBack();
                    $arrReturn = ['status' => 0, 'info' => '用户收支明细新增失败'];
                    return $this->renderJSON($arrReturn);
                }
            }

        }

        #进行付款单操作
        $OperationRes = PaymentOperation::FollowUp_Operation($payment_flow_no);

        if ($OperationRes['status']) {
            $transaction->commit();
            $arrReturn = ['status' => 1, 'info' => '提交成功'];

        } else {
            $transaction->rollBack();
            $arrReturn = ['status' => 0, 'info' => $OperationRes['info']];

        }

        return $this->renderJSON($arrReturn);

    }

    #后付款模式（金额挂账）
    public function actionAfterPayment()
    {

        $PayorderOriginalModel = new PayorderOriginal();
        $PayorderGeneralModel  = new PayorderGeneral();
        $UserMemberModel       = new UserMember();
        $PaymentFlowModel      = new PaymentFlow();
        $UserCreditModel       = new UserCredit();
        $PaymentAuditModel     = new PaymentAudit();
        $PipelineListModel     = new PipelineList();

        $post        = $this->post();
        $payorder_no = $post['payorder_no'];
        $credit_id   = $post['credit_id'];

        if (empty($payorder_no)) {
            return $this->renderJSON(['status' => 0, 'info' => '未选择付款单']);
        }

        if (empty($credit_id)) {
            return $this->renderJSON(['status' => 0, 'info' => '缺少挂账账户信息']);
        }

        $PayorderGeneralList = $PayorderGeneralModel->find()->where(['general_payorder' => $payorder_no])->asArray()->all();

        if (count($PayorderGeneralList) != count($payorder_no)) {
            return $this->renderJSON(['status' => 0, 'info' => '存在付款单未查询到']);
        }

        foreach ($PayorderGeneralList as $key => $val) {

            if (!in_array($val['general_pay_lock'], ['等待支付'])) {
                return $this->renderJSON(['status' => 0, 'info' => '付款单状态异常：' . $val['general_payorder'] . '已支付或者已取消']);
            }

            if ($val['general_payorder_number'] != '' || $val['general_payorder_number'] != null || $val['general_locked_state'] == '已锁定') {
                return $this->renderJSON(['status' => 0, 'info' => '付款单：' . $val['general_payorder'] . '已加入其他支付，请解除重新合并']);
            }
        }

        $member_id_List = array_unique(array_column($PayorderGeneralList, 'general_payment_userid'));

        if (count($member_id_List) > 1) {
            $arrReturn = ['status' => 0, 'info' => '付款单存在多用户支付'];
            return $this->renderJSON($arrReturn);
        }

        $member_id = $member_id_List[0];

        #预赋值
        $total           = array_sum(array_column($PayorderGeneralList, 'general_pay_money')); #获取支付金额
        $payorder_no_str = implode(',', $payorder_no);
        $sign            = md5($payorder_no_str . time() . uniqid());
        $trans_time      = time();
        $payment_time    = time();

        #生成一个支付流水单号
        $payment_flow_no = DataHelper::createPaymentFlowNO();

        $transaction = \Yii::$app->db->beginTransaction();

        $UserMemberRes = $UserMemberModel->find()->where(['u_id' => $member_id])->asArray()->one();

        #新增加入支付流水
        $PaymentAuditRes = $PaymentAuditModel->find()->where(['payment_method' => '后付款'])->asArray()->one();

        if ($PaymentAuditRes['is_audit'] == 'Y') {
            $PaymentFlowModel->payment_audit_status = '未审核';
        } else {
            $PaymentFlowModel->payment_audit_status = '无审核';
        }

        $PaymentFlowModel->payment_flow_type         = '订单支付';
        $PaymentFlowModel->payment_mode              = '后付款';
        $PaymentFlowModel->payment_flow_no           = $payment_flow_no;                        #这里其实未提交，但也还是生成一个
        $PaymentFlowModel->payment_payorders         = $payorder_no_str;                            #付款单集合
        $PaymentFlowModel->payable_amount            = $total;                                #应付金额
        $PaymentFlowModel->payment_amount            = $total;                                #支付金额（为实际应付款金额）
        $PaymentFlowModel->payment_platform          = '挂账支付';                            #支付平台
        $PaymentFlowModel->payment_method            = '';                                    #支付方式
        $PaymentFlowModel->payment_trans_no          = '';                                    #三方交易号
        $PaymentFlowModel->payment_creattime         = $trans_time;                        #创建时间
        $PaymentFlowModel->payment_settlement_status = '已支付';                    #支付状态
        $PaymentFlowModel->payment_successtime       = $trans_time;                                #支付成功时间
        $PaymentFlowModel->payment_callback_content  = '';                            #三方回调内容
        $PaymentFlowModel->payment_callbacktime      = '';                                #回调时间
        $PaymentFlowModel->admin_id                  = $UserMemberRes['admin_id'];
        $PaymentFlowModel->admin_name                = $UserMemberRes['admin_name'];
        $PaymentFlowModel->user_id                   = $UserMemberRes['u_id'];
        $PaymentFlowModel->user_name                 = $UserMemberRes['uname'];
        $PaymentFlowModel->user_email                = $UserMemberRes['email'];
        $PaymentFlowModel->link_effective_time       = time();
        $PaymentFlowModel->link_sign                 = $sign;

        $insertPaymentFlow = $PaymentFlowModel->insert();

        if (!$insertPaymentFlow) {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '挂账支付生成支付流水出现异常']);
        }

        #进行付款单操作
        $OperationRes = PaymentOperation::FollowUp_Operation($payment_flow_no);

        if (!$OperationRes['status']) {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => $OperationRes['info']]);
        }
        #
        $creditAccount = $UserCreditModel->find()->where(['credit_id' => $credit_id])->one();

        if (!$creditAccount) {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '挂账账户不存在']);
        }

        if ($creditAccount['user_credit_status'] == '禁用') {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '挂账账户已被禁用']);
        }

        if ($creditAccount['user_credit_money'] + $total > $creditAccount['user_credit_money_allow']) {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '可挂账余额不足以支付本次需要付款金额']);
        }

        $frontmoney = $creditAccount->user_credit_money;
        $nowmoney   = $total;

        $creditAccount->user_credit_money = $aftermoney = $creditAccount->user_credit_money + $total;

        if (!$creditAccount->save()) {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '挂账支付出现异常，请联系管理员']);
        }

        $HistoryModel = new UserCreditHistory();

        $HistoryModel->credit_id              = $creditAccount->credit_id;
        $HistoryModel->credit_front_cmoney    = $frontmoney;
        $HistoryModel->credit_now_cmoney      = $nowmoney;
        $HistoryModel->credit_after_cmoney    = $aftermoney;
        $HistoryModel->credit_payment_flow_id = $payment_flow_no;
        $HistoryModel->credit_paymoney        = $total;
        $HistoryModel->credit_time            = $trans_time;
        $HistoryModel->credit_pay_platform    = null;
        $HistoryModel->credit_pay_channel     = null;
        $HistoryModel->credit_pay_time        = null;
        $HistoryModel->credit_pay_status      = '等待支付';
        $HistoryModel->credit_pay_audit       = '未审核';
        $HistoryModel->credit_status          = '工作未完成';

        if (!$HistoryModel->save()) {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '加入到挂账消费记录出现异常']);
        }

        $transaction->commit();
        $arrReturn = ['status' => 1, 'info' => '付款单后付款模式支付成功'];

        return $this->renderJSON($arrReturn);
    }

    #余额支付模式
    public function actionBalancePayment()
    {

        $PaymentAccountModel   = new PaymentAccount();
        $PaymentWayModel       = new PaymentWay();
        $PayorderGeneralModel  = new PayorderGeneral();
        $PayorderOriginalModel = new PayorderOriginal();
        $UserMemberModel       = new UserMember();
        $PaymentFlowModel      = new PaymentFlow();
        $PaymentAuditModel     = new PaymentAudit();
        $PipelineListModel     = new PipelineList();

        $post        = $this->post();
        $payorder_no = $post['payorder_no'];

        $PayorderGeneralList = $PayorderGeneralModel->find()->joinwith('original')->where(['general_payorder' => $payorder_no])->asArray()->all();

        if (count($PayorderGeneralList) != count($payorder_no)) {
            return $this->renderJSON(['status' => 0, 'info' => '存在未知的付款单']);
        }

        foreach ($PayorderGeneralList as $key => $val) {

            if (!in_array($val['general_pay_lock'], ['等待支付'])) {
                return $this->renderJSON(['status' => 0, 'info' => '付款单状态异常']);
            }

            if ($val['general_payorder_number'] != '' || $val['general_payorder_number'] != null || $val['general_locked_state'] == '已锁定') {
                return $this->renderJSON(['status' => 0, 'info' => '付款单已在其他支付流水中']);
            }
        }

        $member_id_List = array_unique(array_column($PayorderGeneralList, 'general_payment_userid'));

        if (count($member_id_List) > 1) {
            $arrReturn = ['status' => 0, 'info' => '付款单存在多用户支付'];
            return $this->renderJSON($arrReturn);
        }

        $member_id = $member_id_List[0];

        #预赋值
        $total           = array_sum(array_column($PayorderGeneralList, 'general_pay_money')); #获取支付金额
        $payorder_no_str = implode(',', $payorder_no);
        #生成一个支付流水号
        $payment_flow_no = DataHelper::createPaymentFlowNO();
        $sign            = md5($payorder_no_str . time() . uniqid()); #echo $sign;exit;
        $trans_time      = time();

        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        #用户余额
        $UserMemberQuery = $UserMemberModel->find()->where(['u_id' => $member_id])->one();
        $front_balance   = $UserMemberQuery->balance;
        if ($front_balance < $total) {
            return $this->renderJSON(['status' => 0, 'info' => '用户余额不足']);
        }

        $UserMemberQuery->balance = $front_balance - $total;
        $UpdateUserMemberRes      = $UserMemberQuery->update();

        if (!$UpdateUserMemberRes) {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '用户余额更新失败']);
        }

        $after_balance = $UserMemberQuery->balance;

        #新增支付流水信息
        #判定支付流水的支付是否需要审核
        $PaymentAuditRes = $PaymentAuditModel->find()->where(['payment_method' => '余额支付'])->asArray()->one();
        if ($PaymentAuditRes['is_audit'] == 'Y') {
            $PaymentFlowModel->payment_audit_status = '未审核';
        } else {
            $PaymentFlowModel->payment_audit_status = '无审核';
        }

        $PaymentFlowModel->payment_flow_type         = '订单支付';
        $PaymentFlowModel->payment_mode              = '余额支付';
        $PaymentFlowModel->payment_flow_no           = $payment_flow_no;                        #这里其实未提交，但也还是生成一个
        $PaymentFlowModel->payment_payorders         = $payorder_no_str;                    #付款单集合
        $PaymentFlowModel->payable_amount            = $total;                                #应付金额
        $PaymentFlowModel->payment_amount            = $total;                                #金额
        $PaymentFlowModel->payment_platform          = '余额抵扣';                            #支付平台
        $PaymentFlowModel->payment_method            = '余额支付';                            #支付方式
        $PaymentFlowModel->payment_trans_no          = '';                                    #三方交易号
        $PaymentFlowModel->payment_creattime         = $trans_time;                        #创建时间
        $PaymentFlowModel->payment_settlement_status = '已支付';
        $PaymentFlowModel->payment_successtime       = $trans_time;                        #支付成功时间
        $PaymentFlowModel->payment_callback_content  = '';                            #三方回调内容
        $PaymentFlowModel->payment_callbacktime      = $trans_time;                        #回调时间

        $PaymentFlowModel->admin_id            = $UserMemberQuery->admin_id;
        $PaymentFlowModel->admin_name          = $UserMemberQuery->admin_name;
        $PaymentFlowModel->user_id             = $UserMemberQuery->u_id;
        $PaymentFlowModel->user_name           = $UserMemberQuery->uname;
        $PaymentFlowModel->user_email          = $UserMemberQuery->email;
        $PaymentFlowModel->link_effective_time = time();
        $PaymentFlowModel->link_sign           = $sign;

        $InsertPaymentFlow = $PaymentFlowModel->insert();
        if (!$InsertPaymentFlow) {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '支付流水新增失败']);
        }

        #进行付款单操作
        $OperationRes = PaymentOperation::FollowUp_Operation($payment_flow_no);

        if (!$OperationRes['status']) {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => $OperationRes['info'], 'OperationRes' => $OperationRes]);
        }

        #增加用户收支明细
        $FinanceFundflowModel                           = new FinanceFundflow();
        $FinanceFundflowModel->user_id                  = $UserMemberQuery->u_id;
        $FinanceFundflowModel->user_name                = $UserMemberQuery->email;
        $FinanceFundflowModel->admin_id                 = $UserMemberQuery->admin_id;
        $FinanceFundflowModel->admin_name               = $UserMemberQuery->admin_name;
        $FinanceFundflowModel->before_transaction_money = $front_balance;
        $FinanceFundflowModel->transaction_money        = $total;
        $FinanceFundflowModel->after_transaction_money  = $after_balance;
        $FinanceFundflowModel->income_or_expense        = '支出';
        $FinanceFundflowModel->transaction_type         = '消费';
        $FinanceFundflowModel->transaction_mode         = '余额支付';
        $FinanceFundflowModel->transaction_channel      = '用户余额';
        $FinanceFundflowModel->unionid                  = '';
        $FinanceFundflowModel->flow_id                  = '';
        $FinanceFundflowModel->orderid                  = '';
        $FinanceFundflowModel->rechargeid               = '';
        $FinanceFundflowModel->payment_flow_no          = $payment_flow_no;
        $FinanceFundflowModel->line_id                  = '';
        $FinanceFundflowModel->transaction_description  = '服务器产品订单支付';
        $FinanceFundflowModel->transaction_time         = $trans_time;

        $InsertFundflowRes = $FinanceFundflowModel->insert();

        if (!$InsertFundflowRes) {
            $transaction->rollBack();
            return $this->renderJSON(['status' => 0, 'info' => '用户收支明细新增失败']);
        } else {

            if ($PaymentAuditRes['is_audit'] == 'N') {
                $SetAccountBill = BillModel::OffsetAccounts($payment_flow_no);

                if (!$SetAccountBill["status"]) {
                    $transaction->rollBack();
                    return $this->renderJSON(['status' => 0, 'info' => $SetAccountBill["info"]]);
                }
            }
            $UserIntegral = new UserIntegral();
            #提取用户佣金
            foreach ($PayorderGeneralList as $item){
                UserCommission::add(ArrayHelper::getValue($item,'general_original_order'));
                #用户算积分
                $UserIntegral ->census(ArrayHelper::getValue($item,'general_original_order'));
            }

            $transaction->commit();
            return $this->renderJSON(['status' => 1, 'info' => '余额支付成功']);
        }

    }

    #生成付款链接( 其实就是加入支付流水)
    public function actionGeneratePaymentlink()
    {

        $PayorderOriginalModel = new PayorderOriginal();
        $PayorderGeneralModel  = new PayorderGeneral();
        $PaymentFlowModel      = new PaymentFlow();
        $UserMemberModel       = new UserMember();
        $PaymentAuditModel     = new PaymentAudit();

        $post        = $this->post();
        $payorder_no = $post['payorder_no']; #print_r($payorder_no);exit;
        if (empty($payorder_no)) {
            $arrReturn = ['status' => 0, 'info' => '未选择付款单'];
            return $this->renderJSON($arrReturn);
        }
        #事务
        $transaction = \Yii::$app->db->beginTransaction();

        #判定付款单
        $PayorderGeneralList = $PayorderGeneralModel->find()->where(['general_payorder' => $payorder_no])->asArray()->all();
        if (count($PayorderGeneralList) != count($payorder_no)) {
            $arrReturn = ['status' => 0, 'info' => '存在未知的付款单'];
            return $this->renderJSON($arrReturn);
        }

        $orderlist = [];

        foreach ($PayorderGeneralList as $key => $val) {
            if (!in_array($val['general_pay_lock'], ['等待支付'])) {
                $arrReturn = ['status' => 0, 'info' => '付款单状态异常：' . $val['general_payorder'] . '已支付或者已取消'];
                return $this->renderJSON($arrReturn);
            }
            if ($val['general_payorder_number'] != '' || $val['general_payorder_number'] != null || $val['general_locked_state'] == '已锁定') {
                $arrReturn = ['status' => 0, 'info' => '付款单：' . $val['general_payorder'] . '已加入其他支付，请解除重新合并'];
                return $this->renderJSON($arrReturn);
            }

            if (!in_array($val['general_original_order'], $orderlist)) {
                $orderlist[] = $val['general_original_order'];
            }
        }

        #判定付款人
        $member_id_List = array_unique(array_column($PayorderGeneralList, 'general_payment_userid'));
        if (count($member_id_List) > 1) {
            $arrReturn = ['status' => 0, 'info' => '付款单存在多用户支付'];
            return $this->renderJSON($arrReturn);
        }
        $member_id     = $member_id_List[0];
        $UserMemberRes = $UserMemberModel->find()->where(['u_id' => $member_id])->asArray()->one();

        #如果是销售的权限，那么只能销售合并支付
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'payorder_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            if ($UserMemberRes['admin_id'] != $admin_id) {
                $arrReturn = ['status' => 0, 'info' => '您不属于所选付款单所属客户的专属客服，无法操作'];
                return $this->renderJSON($arrReturn);
            }
        }

        #付款单号集合  用逗号链接  payorder_no
        $payorders  = implode(',', $payorder_no);
        $total      = array_sum(array_column($PayorderGeneralList, 'general_pay_money')); #获取支付金额
        $trans_time = time();
        #生成一个支付流水单号
        $payment_flow_no = DataHelper::createPaymentFlowNO();
        $sign            = md5($payorders . time() . uniqid()); #echo $sign;exit;

        #批量更新付款单的信息
        $updateAllRes = $PayorderGeneralModel->updateAll(['general_payorder_number' => $payment_flow_no, 'general_locked_state' => '已锁定'], ['general_payorder' => $payorder_no]);

        #更新订单为已锁定
        $PayorderOriginalModel->updateAll(['order_update_status' => '已锁定'], ['order_id' => $orderlist]);

        #新增
        $PaymentFlowModel->payment_audit_status      = '未审核';
        $PaymentFlowModel->payment_flow_type         = '订单支付';
        $PaymentFlowModel->payment_mode              = '在线支付';
        $PaymentFlowModel->payment_flow_no           = $payment_flow_no;                        #这里其实未提交，但也还是生成一个
        $PaymentFlowModel->payment_payorders         = $payorders;                            #付款单集合
        $PaymentFlowModel->payable_amount            = $total;                                #应付金额
        $PaymentFlowModel->payment_platform          = ''; #支付平台
        $PaymentFlowModel->payment_method            = '';                                    #支付方式
        $PaymentFlowModel->payment_trans_no          = '';                                    #三方交易号
        $PaymentFlowModel->payment_creattime         = $trans_time;                        #创建时间
        $PaymentFlowModel->payment_settlement_status = '未支付';                    #支付状态
        $PaymentFlowModel->payment_successtime       = '';                                #支付成功时间
        $PaymentFlowModel->payment_callback_content  = '';                            #三方回调内容
        $PaymentFlowModel->payment_callbacktime      = '';                                #回调时间
        $PaymentFlowModel->admin_id                  = $UserMemberRes['admin_id'];
        $PaymentFlowModel->admin_name                = $UserMemberRes['admin_name'];
        $PaymentFlowModel->user_id                   = $UserMemberRes['u_id'];
        $PaymentFlowModel->user_name                 = $UserMemberRes['uname'];
        $PaymentFlowModel->user_email                = $UserMemberRes['email'];
        $PaymentFlowModel->link_effective_time       = time() + (Yii::$app->params['link_effective_time']);
        $PaymentFlowModel->link_sign                 = $sign;
        $insertPaymentFlow                           = $PaymentFlowModel->insert();

        $frontUrl    = Yii::$app->params['frontWebDomain'];
        $paymentlink = $frontUrl . "/pay?order_no={$payment_flow_no}";
        //$paymentlink = $frontUrl . Url::to(['cashier', 'flow_no' => $payment_flow_no, 'paySign' => $sign]);

        if ($insertPaymentFlow) {
            $transaction->commit();
            $arrReturn = ['status' => 1, 'info' => '付款链接创建成功', 'url' => $paymentlink];
        } else {
            $transaction->rollBack();
            $arrReturn = ['status' => 0, 'info' => '付款链接生成失败'];
        }
        return $this->renderJSON($arrReturn);
    }

    #更新付款链接
    public function actionUpdatePaymentlink()
    {
        Yii::$app->request->isAjax || die('error');
        $post = $this->post();
        if (empty($post)) {
            $arrReturn = ['status' => 0, 'info' => '提交参数为空'];
            return $this->renderJSON($arrReturn);
        }
        $PaymentFlowModel = new PaymentFlow();

        $flow_no          = $post['flow_no'];
        $PaymentFlowQuery = $PaymentFlowModel->find()->where(['payment_flow_no' => $flow_no])->one();
        if (empty($PaymentFlowQuery)) {
            $arrReturn = ['status' => 0, 'info' => '未知的支付流水'];
            return $this->renderJSON($arrReturn);
        }

        #$payment_payorders = $PaymentFlowQuery->payment_payorders;
        #$sign = md5($payment_payorders.time().uniqid()); 			#echo $sign;exit;

        $PaymentFlowQuery->link_effective_time = time() + 86400;
        #$PaymentFlowQuery->link_sign = $sign;

        if ($PaymentFlowQuery->save()) {
            $arrReturn = ['status' => 1, 'info' => '付款链接更新成功'];
        } else {
            $arrReturn = ['status' => 0, 'info' => '付款链接更新失败'];
        }
        return $this->renderJSON($arrReturn);

    }

    #指定用户付款
    public function actionDesignatedPayment()
    {
        Yii::$app->request->isAjax || die('error');
        $post = $this->post();
        if (empty($post)) {
            $arrReturn = ['status' => 0, 'info' => '提交参数为空'];
            return $this->renderJSON($arrReturn);
        }

        $PayorderGeneralModel = new PayorderGeneral();
        $UserMemberModel      = new UserMember();
        $general_id           = $post['general_id'];
        $user_email           = $post['user_email'];
        if (strpos($user_email, ' ')) {
            $arrReturn = ['status' => 0, 'info' => '输入内容不可包含空格'];
            return $this->renderJSON($arrReturn);
        }
        $PayorderGeneralQuery = $PayorderGeneralModel->find()->where(['general_id' => $general_id])->one();
        if (empty($PayorderGeneralQuery)) {
            $arrReturn = ['status' => 0, 'info' => '未知的付款单'];
            return $this->renderJSON($arrReturn);
        }

        if (!in_array($PayorderGeneralQuery->general_pay_lock, ['等待支付'])) {
            $arrReturn = ['status' => 0, 'info' => '付款单已支付或已取消'];
            return $this->renderJSON($arrReturn);
        }
        if ($PayorderGeneralQuery->general_locked_state == '已锁定' || $PayorderGeneralQuery->general_payorder_number != '') {
            $arrReturn = ['status' => 0, 'info' => '付款单已被加入流水，请先解除合并'];
            return $this->renderJSON($arrReturn);
        }

        $member = Member::find()->where(['email' => $user_email])->orWhere(['username' => $user_email])->with('userMember')->one();
        if(!$member){
            $UserMemberRes = $UserMemberModel->find()->where(['email' => $user_email, 'status' => 1])->asArray()->one();
        }
        else {
            $UserMemberRes = HelpersArrayHelper::getValue($member, 'userMember');
        }
        
        if ($UserMemberRes['balance'] == 0) {
            $arrReturn = ['status' => 0, 'info' => '付款账户余额不能为零'];
            return $this->renderJSON($arrReturn);
        }
        #如果是销售的权限，那么只能销售合并支付
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'payorder_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            if ($UserMemberRes['admin_id'] != $admin_id) {
                $arrReturn = ['status' => 0, 'info' => '您不属于此付款单所属客户的专属客服，无法操作'];
                return $this->renderJSON($arrReturn);
            }
        }

        if (empty($UserMemberRes)) {
            $arrReturn = ['status' => 0, 'info' => '所选指定用户不存在或账户已被禁用'];
            return $this->renderJSON($arrReturn);
        }
        if ($UserMemberRes['u_id'] == $PayorderGeneralQuery->general_payment_userid) {
            $arrReturn = ['status' => 0, 'info' => '所选指定用户与原用户一致'];
            return $this->renderJSON($arrReturn);
        }
        $PayorderGeneralQuery->general_payment_userid = $UserMemberRes['u_id'];
        if ($PayorderGeneralQuery->update()) {
            $arrReturn = ['status' => 1, 'info' => '指定用户成功'];
        } else {
            $arrReturn = ['status' => 0, 'info' => '指定用户失败'];
        }
        return $this->renderJSON($arrReturn);

    }

    #ajax 获取支付单 支付金额总和
    public function actionAjaxPaymentamount()
    {
        Yii::$app->request->isAjax || die('error');

        $PayorderGeneralModel = new PayorderGeneral();

        $post        = $this->post();
        $payorder_no = $post['payorder_no'];

        $PayorderGeneralList = $PayorderGeneralModel->find()->where(['general_payorder' => $payorder_no])->asArray()->all();
        #$general_pay_lock = array_column($PayorderGeneralList, 'general_pay_lock');

        $pay_money = array_sum(array_column($PayorderGeneralList, 'general_pay_money'));
        $data      = [
            'payment_amount' => $pay_money,
        ];
        $arrReturn = ['status' => 1, 'info' => '获取成功', 'data' => $data];
        return $this->renderJSON($arrReturn);


    }

    #ajax 判定付款是否为同一个支付用户
    public function actionPaymentUser()
    {
        Yii::$app->request->isAjax || die('error');
        $PayorderGeneralModel = new PayorderGeneral();

        $post        = $this->post();
        $payorder_no = $post['payorder_no'];

        $PayorderGeneralList = $PayorderGeneralModel->find()->where(['general_payorder' => $payorder_no])->asArray()->all();
        $payment_userid_list = array_unique(array_column($PayorderGeneralList, 'general_payment_userid'));
        if (empty($payment_userid_list)) {
            $arrReturn = ['status' => 0, 'info' => '不存在付款用户'];
            return $this->renderJSON($arrReturn);
        } else {
            if (count($payment_userid_list) > 1) {
                $arrReturn = ['status' => 0, 'info' => '存在多个付款用户'];
                return $this->renderJSON($arrReturn);
            }
        }
        $arrReturn = ['status' => 1, 'info' => '符合要求'];
        return $this->renderJSON($arrReturn);
    }

    public function actionMergePay()
    {
        $GeneralModel = new PayorderGeneral();
        $post         = $this->post();
        $payorder_no  = $post['payorder_no'];
        $payorders    = implode(',', $payorder_no);
        $GeneralList  = $GeneralModel->find()->where(['general_payorder' => $payorder_no])->asArray()->one();
        foreach ($GeneralList as $key => $val) {
            if (!in_array($val['general_pay_lock'], ['等待支付'])) {

            }
        }

    }


}
