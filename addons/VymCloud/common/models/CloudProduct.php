<?php

namespace addons\VymCloud\common\models;

use addons\VymServer\common\enums\ProductEnum;
use common\behaviors\MerchantStoreBehavior;
use common\helpers\Html;
use common\models\merchant\Merchant;
use Yii;
use yii\validators\EachValidator;

/**
 * This is the model class for table "{{%addon_cloud_product}}".
 *
 * @property int         $product_id       套餐ID
 * @property int         $product_st       套餐状态，0-下架，1-上架
 * @property int|null    $merchant_id      商户id
 * @property int|null    $store_id         店铺id
 * @property string      $title            简体名称
 * @property string      $title_hk         繁体名称
 * @property string      $title_en         英文名称
 * @property string      $desc             简体描述
 * @property string|null $desc_hk          繁体描述
 * @property string|null $desc_en          英文描述
 * @property int         $cluster_ids      所属集群
 * @property string      $config_price     价格配置
 * @property string      $config_cpu       CPU配置
 * @property string      $config_mem       内存配置
 * @property string      $config_disk_os   系统盘配置
 * @property string      $config_disk_data 数据盘配置
 * @property string      $config_ip        IP配置
 * @property string      $config_def       防御流量配置
 * @property string      $config_bw        带宽配置
 * @property string      $config_basic     基本配置
 * @property string      $config_extend    更多配置
 * @property int|null    $stock            库存,0-无限库存,-1-库存耗尽,>0-剩余库存数量
 * @property int|null    $sales            销量
 * @property string      $recommend        是否为推荐
 * @property string|null $created_at       创建时间
 * @property string|null $updated_at       更新时间
 * @property string|null $deleted_at       删除时间
 */
class CloudProduct extends \yii\db\ActiveRecord
{
    use MerchantStoreBehavior;

    // 产品类型
    const TYPE_VPS   = 1;                 // VPS
    const TYPE_CLOUD = 2;                 // 云服务器

    // 产品状态
    const STATUS_DISABLED = 0;            // 下架
    const STATUS_ENABLED  = 1;            // 上架

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%addon_cloud_product}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['desc_hk', 'desc_en', 'created_at', 'updated_at', 'deleted_at'], 'default', 'value' => null],
            [['desc'], 'default', 'value' => ''],
            [['recommend'], 'default', 'value' => 'N'],
            [['sales'], 'default', 'value' => 0],
            [['merchant_id', 'store_id'], 'default', 'value' => 0],
            [['title', 'desc', 'stock', 'sales', 'product_st', 'cluster_ids',], 'required'],
            [['product_st', 'stock', 'sales'], 'integer'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['title', 'title_hk', 'title_en', 'desc', 'desc_hk', 'desc_en',], 'string', 'max' => 255],
            [['config_price', 'config_cpu', 'config_mem', 'config_disk_os', 'config_disk_data', 'config_ip', 'config_def', 'config_bw', 'config_basic', 'config_extend'], 'safe'], // 添加对新字段的验证规则
            [['config_price'], 'validatePriceDetail'],
            ['recommend', 'in', 'range' => array_keys(ProductEnum::getRecommendMap())],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'product_id'       => '套餐ID',
            'product_st'       => '套餐状态',
            'merchant_id'      => '商户ID',
            'store_id'         => '店铺ID',
            'title'            => '名称',
            'title_hk'         => '繁体名称',
            'title_en'         => '英文名称',
            'desc'             => '描述',
            'desc_hk'          => '繁体描述',
            'desc_en'          => '英文描述',
            'cluster_ids'      => '所属集群',
            'config_price'     => '价格配置',
            'config_cpu'       => 'CPU配置',
            'config_mem'       => '内存配置',
            'config_disk_os'   => '系统盘配置',
            'config_disk_data' => '数据盘配置',
            'config_ip'        => 'IP配置',
            'config_def'       => '防御流量配置',
            'config_bw'        => '带宽配置',
            'config_basic'     => '基本配置',
            'config_extend'    => '更多配置',
            'stock'            => '库存',
            'sales'            => '销量',
            'recommend'        => '推荐',
            'created_at'       => '创建时间',
            'updated_at'       => '更新时间',
            'deleted_at'       => '删除时间',
        ];
    }

    public function getCluster()
    {
        return $this->hasOne(CloudCluster::class, ['id' => 'cluster_ids']);
    }

    /**
     * 获取状态列表
     *
     * @return array|string
     */
    public static function getStatusMap($status = null)
    {
        $statusArr = [
            self::STATUS_ENABLED  => '上架',
            self::STATUS_DISABLED => '下架',
        ];
        if ($status === null) {
            return $statusArr;
        }
        return $statusArr[$status] ?? '未知';
    }

    /**
     * 获取产品类型列表
     *
     * @return array
     */
    public static function getTypeMap()
    {
        return [
            self::TYPE_VPS   => 'VPS',
            self::TYPE_CLOUD => '云服务器',
        ];
    }


    /**
     * 是否标签
     *
     * @param int $status
     *
     * @return mixed
     */
    public static function getStatusHtml(int $status)
    {
        $listBut = [
            self::STATUS_ENABLED  => Html::tag('span', self::getStatusMap($status), [
                'class' => "label label-outline-success label-sm",
            ]),
            self::STATUS_DISABLED => Html::tag('span', self::getStatusMap($status), [
                'class' => "label label-outline-danger label-sm",
            ]),
        ];

        return $listBut[$status] ?? '';
    }

    public function validatePriceDetail($attribute, $params)
    {
        foreach ($this->$attribute as $index => $item) {
            $price    = $item['price'] ?? '';
            $month    = $item['month'] ?? '';
            $discount = $item['discount'] ?? '';
            if (!$price || !is_numeric($price)) {
                $this->addError("{$attribute}[{$index}][price]", "价格配置中价格不能为空，且必须为数字");
            }
            if (!$month || !is_numeric($month)) {
                $this->addError("{$attribute}[{$index}][month]", "价格配置中月数不能为空，且必须为数字");
            }
            if ($discount === null || !is_numeric($discount) || $discount < 0) {
                $this->addError("{$attribute}[{$index}][discount]", "价格配置中折扣不能为空，且必须为数字");
            }
        }
    }

    /**
     * 检查库存是否充足
     *
     * @return bool
     */
    public function hasStock()
    {
        // 库存为0表示无限库存
        if ($this->stock == 0) {
            return true;
        }

        // 库存为-1表示库存耗尽，其他正数表示有库存
        return $this->stock > 0;
    }

    /**
     * 获取剩余库存数量
     *
     * @return int|string 返回剩余库存数量，无限库存返回'无限'，库存耗尽返回0
     */
    public function getRemainingStock()
    {
        // 库存为0表示无限库存
        if ($this->stock == 0) {
            return '无限';
        }

        // 库存为-1表示库存耗尽
        if ($this->stock == -1) {
            return 0;
        }

        return max(0, $this->stock);
    }

    /**
     * 是否为无限库存
     *
     * @return bool
     */
    public function isUnlimitedStock()
    {
        return $this->stock == 0;
    }

    /**
     * 是否库存耗尽
     *
     * @return bool
     */
    public function isOutOfStock()
    {
        return $this->stock == -1;
    }

    /**
     * 扣减库存
     *
     * @param int $quantity 扣减数量，默认为1
     *
     * @return bool
     */
    public function decreaseStock($quantity = 1)
    {
        // 无限库存不需要扣减
        if ($this->stock == 0) {
            return true;
        }

        // 检查库存是否足够
        if ($this->stock < $quantity) {
            return false;
        }

        $this->stock = $this->stock - $quantity;

        // 如果库存减到0，设置为-1表示库存耗尽
        if ($this->stock == 0) {
            $this->stock = -1;
        }

        return $this->save();
    }

    /**
     * 增加库存
     *
     * @param int $quantity 增加数量，默认为1
     *
     * @return bool
     */
    public function increaseStock($quantity = 1)
    {
        // 无限库存不需要增加
        if ($this->stock == 0) {
            return true;
        }

        // 如果当前是库存耗尽状态(-1)，则从0开始增加
        if ($this->stock == -1) {
            $this->stock = 0;
        }

        $this->stock = $this->stock + $quantity;
        return $this->save();
    }

    /**
     * 获取库存状态描述
     *
     * @return string
     */
    public function getStockStatusText()
    {
        if ($this->stock == 0) {
            return '无限库存';
        } elseif ($this->stock == -1) {
            return '库存耗尽';
        } else {
            return "剩余 {$this->stock} 件";
        }
    }

    /**
     * 关联商户
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }

}
