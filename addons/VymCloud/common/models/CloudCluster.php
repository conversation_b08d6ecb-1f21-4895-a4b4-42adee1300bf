<?php

namespace addons\VymCloud\common\models;

use common\behaviors\MerchantStoreBehavior;
use common\helpers\Html;
use common\models\merchant\Merchant;
use Yii;

/**
 * This is the model class for table "{{%addon_cloud_cluster}}".
 *
 * @property int         $id
 * @property int|null    $area_id        地域ID
 * @property int|null    $merchant_id    商户id
 * @property int|null    $store_id       店铺id
 * @property string      $name           标题
 * @property string|null $name_hk        繁体名称
 * @property string|null $name_en        英文名称
 * @property string      $desc           简体描述
 * @property string|null $desc_hk        繁体描述
 * @property string|null $desc_en        英文描述
 * @property string|null $ip             IP
 * @property int|null    $cluster_st     状态 1启用 0禁用
 * @property string|null $username
 * @property string|null $password
 * @property string|null $realm
 * @property string|null $port
 * @property string      $type           类型
 * @property string      $config_detail  集群配置
 * @property string      $extend_detail  其他详情
 * @property int|null    $max_vmid       记录最大VMID
 * @property int|null    $is_ssl         SSL验证
 * @property int|null    $is_novnc       安装novnc
 * @property int|null    $stock          库存
 * @property int|null    $sales          销量
 * @property int|null    $api_id         接口ID
 * @property int|null    $api_cluster_id 接口集群ID
 * @property int|null    $sort           排序
 * @property string|null $created_at     创建时间
 * @property string|null $updated_at     更新时间
 * @property string|null $deleted_at     删除时间
 */
class CloudCluster extends \yii\db\ActiveRecord
{
    use MerchantStoreBehavior;

    const STATUS_HIDDEN   = 2;
    const STATUS_ENABLED  = 1;
    const STATUS_DISABLED = 0;


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%addon_cloud_cluster}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name_hk', 'name_en', 'desc_hk', 'desc_en', 'ip', 'username', 'password', 'realm', 'port', 'created_at', 'updated_at', 'deleted_at'], 'default', 'value' => null],
            [['sort'], 'default', 'value' => 0],
            [['desc'], 'default', 'value' => ''],
            [['cluster_st'], 'default', 'value' => 1],
            [['merchant_id', 'store_id'], 'default', 'value' => 0],
            [['area_id', 'cluster_st', 'max_vmid', 'is_ssl', 'is_novnc', 'stock', 'sales', 'sort'], 'integer'],
            [['name', 'area_id', 'cluster_st'], 'required'],
            [['password', 'type'], 'string'],
            [['config_detail', 'extend_detail', 'created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['name', 'name_hk', 'name_en', 'desc', 'desc_hk', 'desc_en'], 'string', 'max' => 255],
            [['ip', 'username', 'realm', 'port'], 'string', 'max' => 200],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'            => 'ID',
            'area_id'       => '地域',
            'merchant_id'   => '商户ID',
            'store_id'      => '店铺ID',
            'name'          => '标题',
            'name_hk'       => '繁体名称',
            'name_en'       => '英文名称',
            'desc'          => '描述',
            'desc_hk'       => '繁体描述',
            'desc_en'       => '英文描述',
            'type'          => '类型',
            'ip'            => 'IP地址',
            'cluster_st'    => '状态',
            'username'      => '用户名',
            'password'      => '密码',
            'realm'         => '领域',
            'port'          => '连接端口',
            'config_detail' => '集群配置',
            'extend_detail' => '其他详情',
            'max_vmid'      => '最大VMID',
            'is_ssl'        => 'SSL验证',
            'is_novnc'      => '安装novnc',
            'stock'         => '库存',
            'sales'         => '销量',
            'sort'          => '排序',
            'created_at'    => '创建时间',
            'updated_at'    => '更新时间',
            'deleted_at'    => '删除时间',
        ];
    }


    /**
     * 获取区域
     *
     * @return \yii\db\ActiveQuery
     */
    public function getArea()
    {
        return $this->hasOne(CloudArea::class, ['id' => 'area_id']);
    }

    /**
     * 获取服务器列表
     *
     * @return \yii\db\ActiveQuery
     */
    public function getServers()
    {
        return $this->hasMany(CloudServer::class, ['cluster_id' => 'id']);
    }


    /**
     * 获取状态列表
     *
     * @return array|string
     */
    public static function getStatusMap($status = null)
    {
        $statusArr = [
            self::STATUS_ENABLED  => '启动',
            self::STATUS_DISABLED => '禁用',
            self::STATUS_HIDDEN   => '隐藏',
        ];
        if ($status === null) {
            return $statusArr;
        }
        return $statusArr[$status] ?? '未知';
    }

    /**
     * 是否标签
     *
     * @param int $status
     *
     * @return mixed
     */
    public static function getStatusHtml(int $status)
    {
        $listBut = [
            self::STATUS_ENABLED  => Html::tag('span', self::getStatusMap($status), [
                'class' => "label label-outline-success label-sm",
            ]),
            self::STATUS_DISABLED => Html::tag('span', self::getStatusMap($status), [
                'class' => "label label-outline-danger label-sm",
            ]),
            self::STATUS_HIDDEN   => Html::tag('span', self::getStatusMap($status), [
                'class' => "label label-outline-warning label-sm",
            ]),
        ];

        return $listBut[$status] ?? '';
    }

    /**
     * 关联商户
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }
}
