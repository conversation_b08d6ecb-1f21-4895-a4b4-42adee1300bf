<?php

namespace addons\VymCloud\merchant\controllers;

use addons\VymCloud\common\models\CloudIps;
use addons\VymCloud\common\models\CloudServer;
use addons\VymCloud\common\models\CloudSystem;
use common\models\base\SearchModel;
use Exception;
use Yii;
use yii\web\NotFoundHttpException;

/**
 * ServerController implements the CRUD actions for CloudServer model.
 */
class ServerController extends BaseController
{


    /**
     * @var CloudServer
     */
    public $modelClass = CloudServer::class;

    /**
     * 首页
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionIndex()
    {

        $beginTime   = Yii::$app->request->get('start_time', date('Y-m-d', strtotime("-10 day")));
        $endTime     = Yii::$app->request->get('end_time', date('Y-m-d', strtotime("+1 day")));
        $queryParams = $this->request->queryParams;
        $searchModel = new SearchModel([
            'model'                  => $this->modelClass,
            'scenario'               => 'default',
            'relations'              => ['member' => []], // 关联memebr 表的所有字段
            'partialMatchAttributes' => ['ip', 'gateway', 'mask', 'desc'], // 模糊查询
            'defaultOrder'           => [
                'id' => SORT_DESC,
            ],
            'pageSize'               => $this->pageSize,
        ]);

        $dataProvider = $searchModel->search($queryParams);
        $this->addQueryParam($dataProvider, 's');
        return $this->render($this->action->id, [
            'dataProvider' => $dataProvider,
            'searchModel'  => $searchModel,
            'queryParams'  => $queryParams,
            'clusterList'  => Yii::$app->vymCloudService->cluster->getDropDown(),
            'beginTime'    => $beginTime, // 开始时间
            'endTime'      => $endTime, // 结束时间
        ]);
    }

    /**
     * Displays a single CloudServer model.
     *
     * @param int $id Server ID
     *
     * @return string
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Updates an existing CloudServer model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param int $server_id Server ID
     *
     * @return string|\yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($server_id)
    {
        $model = $this->findModel($server_id);

        if ($this->request->isPost && $model->load($this->request->post()) && $model->save()) {
            return $this->redirect(['view', 'server_id' => $model->server_id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing CloudServer model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param int $server_id Server ID
     *
     * @return \yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($server_id)
    {
        $this->findModel($server_id)->delete();

        return $this->redirect(['index']);
    }

    public function actionCreateServer()
    {
        try {
            $id     = $this->request->post('id');
            $server = $this->findModel($id);
            if (!$server) {
                throw new Exception('服务器实例不存在');
            }
            $result = Yii::$app->vymCloudService->server->instanceCreate($id);
            if ($result['status']) {
                Yii::$app->session->setFlash('success', $result['msg'] ?? '服务器创建中');
            } else {
                Yii::$app->session->setFlash('error', $result['msg'] ?? '服务器创建失败');
            }
            return $this->redirect(['view', 'id' => $id]);
        } catch (\Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
            return $this->redirect(['index']);
        }
    }

    /**
     * 远程连接
     */
    public function actionRemoteConnect()
    {
        $id    = $this->request->post('id');
        $model = $this->findModel($id);

        try {
            $result = Yii::$app->vymCloudService->server->instanceRemoteControl($model->id);

            if ($result['status'] == 1) {
                return $this->asJson([
                    'code' => 1,
                    'msg'  => '获取控制台地址成功',
                    'data' => ['consoleUrl' => $result['data']['consoleUrl']],
                ]);
            } else {
                return $this->asJson(['code' => 0, 'msg' => $result['msg']]);
            }
        } catch (Exception $e) {
            return $this->asJson(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 电源操作
     */
    public function actionPowerAction()
    {
        $id     = $this->request->post('id');
        $action = $this->request->post('action');
        $model  = $this->findModel($id);

        try {
            $result = Yii::$app->vymCloudService->provider->serverHandler($model, $action);

            if ($result['success']) {
                return $this->asJson(['code' => 1, 'msg' => '操作执行成功']);
            } else {
                return $this->asJson(['code' => 0, 'msg' => $result['message']]);
            }
        } catch (Exception $e) {
            return $this->asJson(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 添加数据盘
     */
    public function actionAddDataDisk()
    {
        $id    = $this->request->post('id');
        $size  = $this->request->post('size');
        $model = $this->findModel($id);

        try {
            // 构建磁盘配置参数
            $diskConfig = [
                'size' => $size . 'G',
                'type' => 'data',
            ];

            $result = Yii::$app->vymCloudService->server->instanceDiskDataAdd($model->id, $diskConfig);

            if ($result['status'] == 1) {
                return $this->asJson(['code' => 1, 'msg' => '数据盘添加成功']);
            } else {
                return $this->asJson(['code' => 0, 'msg' => $result['msg']]);
            }
        } catch (Exception $e) {
            return $this->asJson(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 系统盘扩容
     */
    public function actionExpandSystemDisk()
    {
        $id    = $this->request->post('id');
        $size  = $this->request->post('size');
        $model = $this->findModel($id);

        try {
            $result = Yii::$app->vymCloudService->server->instanceDiskOsExpand($model->id, $size);

            if ($result['status'] == 1) {
                return $this->asJson(['code' => 1, 'msg' => '系统盘扩容成功']);
            } else {
                return $this->asJson(['code' => 0, 'msg' => $result['msg']]);
            }
        } catch (Exception $e) {
            return $this->asJson(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 数据盘扩容
     */
    public function actionExpandDataDisk()
    {
        $id    = $this->request->post('id');
        $size  = $this->request->post('size');
        $model = $this->findModel($id);

        try {
            // 数据盘扩容需要指定磁盘ID，这里假设是第一个数据盘
            $diskId = $this->request->post('disk_id', 'scsi1'); // 默认第一个数据盘

            $result = Yii::$app->vymCloudService->server->instanceDiskDataExpand($model->id, $diskId, $size);

            if ($result['status'] == 1) {
                return $this->asJson(['code' => 1, 'msg' => '数据盘扩容成功']);
            } else {
                return $this->asJson(['code' => 0, 'msg' => $result['msg']]);
            }
        } catch (Exception $e) {
            return $this->asJson(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 生成随机密码
     */
    private function generateRandomPassword($length = 12)
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        return substr(str_shuffle($chars), 0, $length);
    }

    /**
     * 重置密码
     *
     * @param int $id
     *
     * @return \yii\web\Response
     */
    public function actionResetPassword($id)
    {
        try {
            $server = $this->findModel($id, true);

            $result = Yii::$app->vymCloudService->multiCloudServer->resetPassword($id);

            if ($result['success']) {
                Yii::$app->session->setFlash('success', '密码重置成功，新密码：' . $result['password']);
            } else {
                Yii::$app->session->setFlash('error', $result['message'] ?? '密码重置失败');
            }

            return $this->redirect(['view', 'id' => $id]);
        } catch (\Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
            return $this->redirect(['index']);
        }
    }

    /**
     * 重装系统
     *
     * @param int $id
     * @param int $systemId
     *
     * @return \yii\web\Response
     */
    public function actionReinstall($id, $systemId)
    {
        try {
            $server = $this->findModel($id, true);

            $result = Yii::$app->vymCloudService->multiCloudServer->reinstallSystem($id, $systemId);

            if ($result['success']) {
                Yii::$app->session->setFlash('success', '系统重装任务已提交');
            } else {
                Yii::$app->session->setFlash('error', $result['message'] ?? '系统重装失败');
            }

            return $this->redirect(['view', 'id' => $id]);
        } catch (\Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
            return $this->redirect(['index']);
        }
    }

    /**
     * 获取控制台地址
     *
     * @param int $id
     *
     * @return \yii\web\Response
     */
    public function actionConsole($id)
    {
        try {
            $server = $this->findModel($id, true);

            $result = Yii::$app->vymCloudService->server->instanceRemoteControl($id);

            if ($result['status'] == 1 && !empty($result['data']['consoleUrl'])) {
                return $this->redirect($result['data']['consoleUrl']);
            } else {
                Yii::$app->session->setFlash('error', '获取控制台地址失败');
                return $this->redirect(['view', 'id' => $id]);
            }
        } catch (\Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
            return $this->redirect(['index']);
        }
    }

    /**
     * 升级带宽 (AJAX)
     */
    public function actionUpgradeBandwidth()
    {
        $id        = $this->request->post('id');
        $bandwidth = $this->request->post('bandwidth');

        if (!$id) {
            return $this->asJson(['code' => 0, 'msg' => '服务器ID不能为空']);
        }

        if (!$bandwidth || $bandwidth <= 0) {
            return $this->asJson(['code' => 0, 'msg' => '带宽值不能为空或小于等于0']);
        }

        try {
            $model = $this->findModel($id, true);

            // TODO: 实现带宽升级逻辑，创建升级订单
            // 这里可以调用相应的服务来处理带宽升级

            return $this->asJson(['code' => 1, 'msg' => '带宽升级功能开发中，请联系客服']);
        } catch (Exception $e) {
            return $this->asJson(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 增购IP (AJAX)
     */
    public function actionPurchaseIp()
    {
        $id      = $this->request->post('id');
        $ipCount = $this->request->post('ip_count', 1);

        if (!$id) {
            return $this->asJson(['code' => 0, 'msg' => '服务器ID不能为空']);
        }

        if (!$ipCount || $ipCount <= 0) {
            return $this->asJson(['code' => 0, 'msg' => 'IP数量不能为空或小于等于0']);
        }

        try {
            $model = $this->findModel($id, true);

            // TODO: 实现IP增购逻辑，创建增购订单
            // 这里可以调用相应的服务来处理IP增购

            return $this->asJson(['code' => 1, 'msg' => 'IP增购功能开发中，请联系客服']);
        } catch (Exception $e) {
            return $this->asJson(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 创建续费订单 (AJAX)
     */
    public function actionCreateRenewOrder()
    {
        $id     = $this->request->post('id');
        $months = $this->request->post('months');

        if (!$id) {
            return $this->asJson(['code' => 0, 'msg' => '服务器ID不能为空']);
        }

        if (!$months || $months <= 0) {
            return $this->asJson(['code' => 0, 'msg' => '续费月数不能为空或小于等于0']);
        }

        // 验证续费月数是否在允许范围内
        $allowedMonths = [1, 3, 6, 12];
        if (!in_array($months, $allowedMonths)) {
            return $this->asJson(['code' => 0, 'msg' => '续费月数不在允许范围内']);
        }

        try {
            $model = $this->findModel($id, true);

            // 检查服务器状态是否允许续费
            if ($model->status_server == -1) {
                return $this->asJson(['code' => 0, 'msg' => '服务器已删除，无法续费']);
            }

            // 创建续费订单
            $result = Yii::$app->vymCloudService->order->createRenewOrder($model->id, $months);

            if ($result['code'] == 1) {
                return $this->asJson([
                    'code' => 1,
                    'msg'  => '续费订单创建成功',
                    'data' => ['order_id' => $result['data']->id],
                ]);
            } else {
                return $this->asJson(['code' => 0, 'msg' => $result['msg']]);
            }
        } catch (Exception $e) {
            return $this->asJson(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 续费页面
     */
    public function actionRenew($id)
    {
        try {
            $model = $this->findModel($id, true);

            return $this->render('renew', [
                'model' => $model,
            ]);
        } catch (Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
            return $this->redirect(['index']);
        }
    }
}
