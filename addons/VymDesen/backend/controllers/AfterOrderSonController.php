<?php
namespace addons\VymDesen\backend\controllers;

use addons\VymDesen\backend\models\CustomPartitionSetting;
use addons\VymDesen\backend\models\IdlePdt;
use addons\VymDesen\backend\models\IpManagement\PdtIpClass;
use addons\VymDesen\backend\models\IpManagement\PdtIpNetwork;
use addons\VymDesen\backend\models\IpManagement\SupplierIp;
use addons\VymDesen\backend\models\TestServer;
use addons\VymDesen\backend\models\UserAdmin\UserAdmin;
use addons\VymDesen\backend\controllers\BaseController;
use addons\VymDesen\common\components\Automation\MachineOperation;
use addons\VymDesen\common\components\CloudBootApi;
use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\components\NewPipe\AfterOrderSon;
use addons\VymDesen\common\components\NewPipe\TrailAndNotice;
use addons\VymDesen\common\models\Afterorder\AfterorderList;
use addons\VymDesen\common\models\Afterorder\AfterorderSonlist;
use addons\VymDesen\common\models\Afterorder\AfterorderSonlistTrial;
use addons\VymDesen\common\models\CloudSystemModel;
use addons\VymDesen\common\models\CloudSystemModelClass;
use addons\VymDesen\common\models\Member\MemberPdt;
use addons\VymDesen\common\models\PayOrder\PayorderDetail;
use addons\VymDesen\common\models\Pdt\PdtCabinetManage;
use addons\VymDesen\common\models\Pdt\PdtIp;
use addons\VymDesen\common\models\Pdt\PdtManage;
use addons\VymDesen\common\models\Pdt\PdtManageType;
use addons\VymDesen\common\models\Pdt\PdtRoomMange;
use addons\VymDesen\common\models\Pdt\SwitchLine;
use addons\VymDesen\common\models\Pdt\SwitchManage;
use addons\VymDesen\common\models\PipeLine\PipelineList;
use addons\VymDesen\common\models\PipeLine\PipelineTypes;
use addons\VymDesen\common\models\ServerDorecord;
use Yii;
use yii\db\Expression;
use yii\helpers\Url;

#基础设置，基础用户类

#产品相关类

#工作流相关

#工单相关

#订单相关
#IP网段 IP分类
#自有机器 系统类

#

#自动化

class AfterOrderSonController extends BaseController {
	
	#新购  重新分配子单页面 / 重新分配机器提交
	public function actionPurchaseReplaceMachine() {
		
		$AfterorderListModel = new AfterorderList();
		$AfterorderSonlistModel = new AfterorderSonlist();
		$AfterorderSonlistTrialModel = new AfterorderSonlistTrial();
		
		$PipelineListModel = new PipelineList();
		
		$IdlePdtModel = new IdlePdt();
		$TestServerModel = new TestServer();
		
		$PdtIpModel = new PdtIp();
		$SupplierIpModel = new SupplierIp();

		$PdtManageModel  = new PdtManage();
		$PdtManageTypeModel = new PdtManageType();
		$PdtRoomMangeModel = new PdtRoomMange();
		
		if ( Yii::$app->request->post() ) {
			
			Yii::$app->request->isAjax || die('error');
		
			$ao_id = $this->post('ao_id');
			$provider = $this->post('provider');
			$data = $this->post('data');
			
			if(!$ao_id || !$data || !in_array($provider, [0, 1])) {
				$arrReturn = ['status'=>0, 'info'=>'参数异常'];
				return $this->renderJSON($arrReturn);
			}
			
			#开启事务
			$transaction = Yii::$app->db->beginTransaction(); 
			
			#查询工单
			$AfterorderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->one();
			
			if(in_array($AfterorderInfo['ao_type'], ['已完成','已取消'])) {
				$arrReturn = ['status' => 0,'info' => '工单状态已完成或已取消'];
				return $this->renderJSON($arrReturn);
			}
			#检测是否有子单未完成或取消
			$AfterorderSonlist = $AfterorderSonlistModel->find()->where(['ao_id' => $ao_id])->asArray()->all();
			
			$check_son_status = true;
			foreach( $AfterorderSonlist as $key=>$val) {
			 	if($val['ao_s_status'] == '未完成') {
				 	$check_son_status = false;
					break;
				}
			}
			if( !$check_son_status) {
				$arrReturn = ['status'=>0, 'info'=>'存在子单未完成'];
				return $this->renderJSON($arrReturn);
			}
			
			#查询工作流
			$Lineinfo = $PipelineListModel->find()->joinwith('linetypes')->where(['line_id' => $AfterorderInfo->line_id])->one();
			
			#配置
			$oldData = json_decode($AfterorderInfo->ao_request_content, true);
			
			$test_id = $data['test_id'];
			if( $test_id && isset($test_id) ) {
				$TestServerQuery = $TestServerModel->find()->where(['id' => $test_id])->one();
				if( empty($TestServerQuery) ) {
					$arrReturn = ['status' => 0,'info' => '未知的测试机'];
					return $this->renderJSON($arrReturn);
				}
				if( $TestServerQuery->status != 0 ) {
					$arrReturn = ['status' => 0,'info' => '测试机状态不允许选择'];
					return $this->renderJSON($arrReturn);
				}
				$TestServerQuery->status = 3;
				if( !$TestServerQuery->save() ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0,'info' => '测试状态更新失败'];
					return $this->renderJSON($arrReturn);
				}
			}
				
			if($provider == 0) {
				#自有服务器查询自有ID
				if( !$test_id ) {
					$IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $data['idle_id']])->one();
					if( $IdlePdtQuery->status != 0) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '自有机器状态异常'];
						return $this->renderJSON($arrReturn);
					}
					$IdlePdtQuery->status = 3;
					if( !$IdlePdtQuery->save() ) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '自有机器状态更新失败'];
						return $this->renderJSON($arrReturn);
					}
				} else {
					$IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $data['idle_id']])->one();
				}
				$afterConfig['servicerprovider'] = 0;
				$afterConfig['server_type_id'] = $data['server_typeid'];
				$afterConfig['room_id'] = $IdlePdtQuery['room_id'];
				$afterConfig['pdt_id'] = $IdlePdtQuery['pdt_id'];
				
				$afterConfig['config'] = json_decode($IdlePdtQuery['config'], true);
				$afterConfig['config']['requirement_bandwidth'] = $data['idle_needband'];
				
				$afterConfig['test_id'] = $data['test_id'];
				$afterConfig['idle_id'] = $data['idle_id'];
				$afterConfig['provider_id'] = '';
				
				$afterConfig['ip'] = json_decode($IdlePdtQuery['ip'], true);
				$afterConfig['ip2'] = json_decode($IdlePdtQuery['ip2'], true);
				$afterConfig['start_date'] = $data['start_date'];
				
				$afterConfig['config_remark'] = $data['config_remark'];
				#登录账密
				$afterConfig['install_account'] = $IdlePdtQuery['re_username'];
				$afterConfig['install_pass'] = $IdlePdtQuery['re_password'];
				$afterConfig['install_port'] = $IdlePdtQuery['re_port'];
				
			} else {
				
				#供应商服务器，所有数据均由前台传过来，直接赋值写入
				$afterConfig['servicerprovider'] = 1;
				$afterConfig['server_type_id'] = $data['server_typeid'];
				$afterConfig['room_id'] = $data['room_id'];
				$afterConfig['pdt_id'] = $data['pdt_id'];
				
				$afterConfig['config'] = $data['config'];
				$afterConfig['config']['requirement_bandwidth'] = $data['supplier_needband'];
			
				$afterConfig['idle_id'] = '';
				$afterConfig['test_id'] = $data['test_id'];				
				$afterConfig['provider_id'] = $data['provider_id'];	
				
				$afterConfig['config_remark'] = $data['config_remark'];
				$afterConfig['start_date'] = $data['start_date'];
				
				if(!$data['iplist']) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info' => '供应商IP为必填选项'];
					return $this->renderJSON($arrReturn);
				}
				$afterConfig['ip'] = DataHelper::dotrim($data['iplist']);
				$afterConfig['ip2'] = [];
				
				#解析拆分IP段
				$Retuen_IPArray = DataHelper::splitIP($data['iplist']);
				if( !$Retuen_IPArray['status'] ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info'=> $Retuen_IPArray['info']];
					return $this->renderJSON($arrReturn);
				}
				
				$afterConfig['ip2'] = $Retuen_IPArray['data'];
				
				#选择了测试机，供应商的IP就不用入库，因为已经入了一次
				if( !isset($afterConfig['test_id']) || !$afterConfig['test_id'] ) {
							
					$CheckRes = DataHelper::detect_supplierip($afterConfig['ip2']);#如果不是测试，需要先判断是否能用		
					if( $CheckRes['status'] == 0 ) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => $CheckRes['info']];
						return $this->renderJSON($arrReturn);
					}
					$insert_ip = $afterConfig['ip2'];
					$insert_ip_Res = $SupplierIpModel->add_peration($insert_ip, $afterConfig['provider_id'], $afterConfig['room_id'], '3');
					if( count($insert_ip_Res) != count($insert_ip) ) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '供应商IP入库失败'];
						return $this->renderJSON($arrReturn);
					}
					
				}
				
				$afterConfig['install_account'] = $data['supplier_account'];
				$afterConfig['install_pass'] = $data['supplier_pass'];
				$afterConfig['install_port'] = $data['supplier_port'];
			}
			#print_r($afterConfig);print_r($oldData); exit;
			
			$frontConfig = $oldData['preset'];
			
			$ao_s_content['replace_front'] = $frontConfig;
			$ao_s_content['replace_after'] = $afterConfig;
			
			#查询是否需要审核
			$typeInfo = $AfterorderSonlistTrialModel->find()->where(['sonlist_type' => '重新分配机器'])->one();
			
			$time = time();
			
			#生成子单
			$AfterorderSonlistModel->ao_id = $AfterorderInfo->ao_id;
			$AfterorderSonlistModel->ao_s_type = '重新分配机器';
			$AfterorderSonlistModel->ao_s_content = json_encode($ao_s_content, JSON_UNESCAPED_UNICODE);
			$AfterorderSonlistModel->ao_s_trial_status = $typeInfo->sonlist_istrial == 'Y' ? '等待审核' : '无需审核';
			$AfterorderSonlistModel->ao_s_status = '未完成';
			$AfterorderSonlistModel->ao_s_request_time = $time;
			$AfterorderSonlistModel->ao_s_finish_time = null;
			
			if(!$AfterorderSonlistModel->save()) {
				$transaction->rollBack();
				$arrReturn = [
					'status'=>0,
					'info'=>'创建子工单时出现异常，请联系管理员'
				];
				return $this->renderJSON($arrReturn);
			}
			
			#创建子单审核通知
			$res = TrailAndNotice::createSonTrail($AfterorderInfo->ao_id, $AfterorderSonlistModel->ao_s_sonlist_id);
			
			$transaction->commit();
			$arrReturn = [
				'status'=>1,
				'info'=>'操作完成'
			];
			return $this->renderJSON($arrReturn);
			
		} else {
			$ao_id = $this->get('ao_id');
			if(!$ao_id) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			
			$AfterOrderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
			if( empty($AfterOrderInfo) ) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			
			$detailConfig = json_decode($AfterOrderInfo['ao_request_content'], true);
			
			$server_typeid = $detailConfig['server_typeid'];
			$pdt_id = $detailConfig['pdt_id'];
			
			#获取机房
			$roomRes = $PdtRoomMangeModel->find()->asArray()->all();
			$nowprovider = 0;		
			
			$PdtManageList = $PdtManageModel->find()->where(new Expression('FIND_IN_SET(:pdt_type_id, pdt_type_id)'))->addParams([':pdt_type_id' => $server_typeid])->asArray()->all();
			
			#服务器分类			
			$PdtManageTypeRes = $PdtManageTypeModel->find()->asArray()->all();
			
			return $this->render('purchase_replace_machine', [
				'AfterOrderInfo' => $AfterOrderInfo,
				'room_list' => $roomRes,
				'nowprovider' => $nowprovider,
				'pdt_list' => $PdtManageList,
				'pdt_type_list' => $PdtManageTypeRes,
				'server_typeid' => $server_typeid,
				'pdt_id' => $pdt_id
			]);
		}
		
	}
	
	#新购 更换IP子单页面 / 更换IP提交
	public function actionPurchaseReplaceip() {
		
		$AfterorderListModel = new AfterorderList();
		$AfterorderSonlistModel = new AfterorderSonlist();
		$AfterorderSonlistTrialModel = new AfterorderSonlistTrial();
		
		$PipelineListModel = new PipelineList();
		
		$MemberPdtModel = new MemberPdt();
		$IdlePdtModel = new IdlePdt();
		$TestServerModel = new TestServer();
		
		$PdtIpModel = new PdtIp();
		$SupplierIpModel = new SupplierIp();
		$SwitchLineModel = new SwitchLine();
		
		$PdtManageModel  = new PdtManage();
		$PdtManageTypeModel = new PdtManageType();
		$PdtRoomMangeModel = new PdtRoomMange();
		
		if ( Yii::$app->request->post() ) {
			
			Yii::$app->request->isAjax || die('error');
		
			$post = $this->post();
			$ao_id = $this->post('ao_id');

			if(!$ao_id) {
				$arrReturn = ['status' => 0, 'info'=>'参数异常'];
				return $this->renderJSON($arrReturn);
			}

			#开启事务
			$transaction = Yii::$app->db->beginTransaction(); 
			
			#查询工单
			$AfterorderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->one();
			
			if(in_array($AfterorderInfo['ao_type'], ['已完成','已取消']) ) {
				$arrReturn = ['status' => 0,'info' => '工单状态已完成或已取消'];
				return $this->renderJSON($arrReturn);
			}
			#检测是否有子单未完成或取消
			$AfterorderSonlist = $AfterorderSonlistModel->find()->where(['ao_id' => $ao_id])->asArray()->all();
			
			$check_son_status = true;
			foreach( $AfterorderSonlist as $key=>$val) {
			 	if($val['ao_s_status'] == '未完成') {
				 	$check_son_status = false;
					break;
				}
			}
			
			if( !$check_son_status) {
				$arrReturn = ['status'=>0, 'info'=>'存在子单未完成'];
				return $this->renderJSON($arrReturn);
			}
			
			#开始验证IP 
			$IpArr = DataHelper::dotrim($post['ips']);			
			$ipArray = array_filter(array_unique($IpArr));
			
			if( empty($IpArr)) {
				return $this->renderJSON(['status' => 0,'info' => 'IP地址未选择或未填写']);
			}
			
			#拆分IP
			$Retuen_IPArray = DataHelper::splitIP($ipArray);
			if( !$Retuen_IPArray['status'] ) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0,'info'=> $Retuen_IPArray['info']];
				return $this->renderJSON($arrReturn);
			}			
			$ipArray2 = $Retuen_IPArray['data'];	

			#配置
			$ao_request_content = json_decode($AfterorderInfo->ao_request_content, true);	
			$preset = $ao_request_content['preset'];
			
			$servicerprovider = $preset['servicerprovider'];			
			$idle_id = $preset['idle_id'];
			
			if( isset($preset['test_id']) && $preset['test_id'] !='' ) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0,'info'=> '测试机不支持此项操作'];
				return $this->renderJSON($arrReturn);
			}
			
			if( $servicerprovider == 0) {
				$IdlePdtRes = $IdlePdtModel->find()->where(['id' => $idle_id])->asArray()->one();
				
				$Original_ip1 = json_decode($IdlePdtRes['ip'], true);
				$Original_ip2 = json_decode($IdlePdtRes['ip2'], true);
				
				$room_id = $IdlePdtRes['room_id'];
				$ipmi_ip = $IdlePdtRes['ipmi_ip'];
				
			} else {
				$Original_ip1 = $preset['ip'];
				$Original_ip2 = $preset['ip2'];
				$room_id = $preset['room_id'];
				$provider_id = $preset['provider_id'];
				$ipmi_ip = '';
				
			}
			
			#判断IP是否有变化 (用提交的IP和上一次IP做对比)			
			$bgIPArray = array_unique(array_merge($ipArray2, $Original_ip2));

			$result_insert = array_diff($bgIPArray,	$Original_ip2);
			$result_remove = array_diff($bgIPArray,	$ipArray2);

			if( empty($result_insert) && empty($result_remove) ) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0, 'info' => 'IP地址未改变'];
				return $this->renderJSON($arrReturn);
			}
			
			#当在为自有时。判断IP所属机房是否与产品的机房一致
			if( $servicerprovider == 0 ) {
				$PdtIpAll = $PdtIpModel->find()->select('room_id, line_type_id, vlan')->where(['in','ip',$ipArray2])->asArray()->all();
				$temp = array_column($PdtIpAll, 'room_id');			
				$temp = array_unique($temp); 			#去掉重复的字符串,也就是重复的一维数组
				$temp = array_values($temp); 			#查询排序
				if(count($temp) > 1 ) {
					$arrReturn = ['status' => 0,'info' => 'IP中有属于其他机房的IP地址'];
					return $this->renderJSON($arrReturn);
				} else if( count($temp) == 1) {
					if($temp[0] != $room_id ){
						$arrReturn = ['status' => 0,'info' => 'IP所属机房与机器所属机房不一致'];
						return $this->renderJSON($arrReturn);
					}
				}
				#判断线路类型
				$line_typeList = array_unique(array_column($PdtIpAll, 'line_type_id'));				
				if( count($line_typeList) > 1 ) {
					$arrReturn = ['status' => 0,'info' => '所选IP与其他IP的线路类型不匹配'];
					return $this->renderJSON($arrReturn);
				}
				
				$SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtRes['switch_location'], 'line_type_id' => $line_typeList[0]])->asArray()->one();
				if( empty($SwitchLineList) ) {
					$arrReturn = ['status' => 0,'info' => '机器对应的交换机线路中未有IP的线路类型'];
					return $this->renderJSON($arrReturn);
				}

				#将新增的ip 改为待定状态
				if( !empty($result_insert) ) {
					$updateAll_res = $PdtIpModel->updateAll(['status' => 3], ['ip'=>$result_insert]);
					if( $updateAll_res != count($result_insert) ) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '新增的IP状态更新失败'];
						return $this->renderJSON($arrReturn);
					}
				}

			} else if( $servicerprovider == 1) {#供应商
				#将新增的供应商 ip 入库，状态为3
				if( !empty($result_insert) ) {
					
					#检测新增的供应商IP  是否可用					
					$CheckRes = DataHelper::detect_supplierip($result_insert);
					if( $CheckRes['status'] == 0 ) {
						$transaction->rollBack();
						return $this->renderJSON(['status' => 0, 'info' => $CheckRes['info']]);
					}
					
					#入库
					$insert_Res = $SupplierIpModel->add_peration($result_insert, $provider_id, $room_id, '3');
					if( $insert_Res != count($result_insert) ) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '供应商IP入库失败'];
						return $this->renderJSON($arrReturn);
					}
				}
			}
			
			#增加子单
			#查询是否需要审核
			$typeInfo = $AfterorderSonlistTrialModel->find()->where(['sonlist_type' => '更换IP'])->one();			
			
			$new_content['servicerprovider'] = $servicerprovider;
			$new_content['room_id'] = $room_id;
			$new_content['idle_id'] = $idle_id;
			$new_content['ipmi_ip'] = $ipmi_ip;
			
			$new_content['replace_front']['ip'] = $Original_ip1;
			$new_content['replace_front']['ip2'] = $Original_ip2;
			
			$new_content['replace_after']['ip'] = $ipArray;
			$new_content['replace_after']['ip2'] = $ipArray2;
			
			$time = time();
			
			#生成子单
			$AfterorderSonlistModel->ao_id = $ao_id;
			$AfterorderSonlistModel->ao_s_type = '更换IP';
			$AfterorderSonlistModel->ao_s_content = json_encode($new_content, JSON_UNESCAPED_UNICODE);
			$AfterorderSonlistModel->ao_s_trial_status = $typeInfo->sonlist_istrial == 'Y' ? '等待审核' : '无需审核';
			$AfterorderSonlistModel->ao_s_status = '未完成';
			$AfterorderSonlistModel->ao_s_request_time = $time;
			$AfterorderSonlistModel->ao_s_finish_time = null;
			
			if( !$AfterorderSonlistModel->insert() ) {
				$transaction->rollBack();
				$arrReturn = [
					'status'=>0,
					'info'=>'创建子工单时出现异常，请联系管理员'
				];
				
			} else {
				
				#创建子单审核通知
				TrailAndNotice::createSonTrail($AfterorderInfo->ao_id, $AfterorderSonlistModel->ao_s_sonlist_id);
				
				$transaction->commit();
				$arrReturn = [
					'status'=>1,
					'info'=>'子单提交成功'
				];
			}
			return $this->renderJSON($arrReturn);
			
		} else {
			$ao_id = $this->get('ao_id');
			if(!$ao_id) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			
			$AfterOrderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
			if( empty($AfterOrderInfo) ) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			
			$ao_request_content = json_decode($AfterOrderInfo['ao_request_content'], true);
			
			$presetRes = $ao_request_content['preset'];
			
			$servicerprovider = $presetRes['servicerprovider'];
			
			if( $servicerprovider == 0) {
				$IdlePdtRes = $IdlePdtModel->find()->where(['id' => $presetRes['idle_id']])->asArray()->one();
				
				$ip = json_decode($IdlePdtRes['ip'], true);
				$room_id = $IdlePdtRes['room_id'];
				
			} else {
				$ip = $presetRes['ip'];
				$room_id = $presetRes['room_id'];
			}
			#获取IP分类
			$PdtIpClassModel = new PdtIpClass();
			$IpClassList = $PdtIpClassModel->find()->asArray()->all();
			#网段
			$PdtIpNetworkModel = new PdtIpNetwork();
			$IpNetworkList = $PdtIpNetworkModel->find()->where(['room_id' => $room_id])->asArray()->all();
			
			return $this->render('purchase-replaceip', [
				'ip' => $ip,
				'servicerprovider' => $servicerprovider,
				'room_id' => $room_id,
				'ao_id' => $ao_id,
				'IpClassList' => $IpClassList,
				'IpNetworkList' => $IpNetworkList
			]);
		}
		
	}
	
	#新增测试机 更换IP页面 / 更换IP提交
	public function actionAddtestserverReplaceip() {
		$AfterorderListModel = new AfterorderList();
		$AfterorderSonlistModel = new AfterorderSonlist();
		$AfterorderSonlistTrialModel = new AfterorderSonlistTrial();
		
		$PipelineListModel = new PipelineList();
		
		$MemberPdtModel = new MemberPdt();
		$IdlePdtModel = new IdlePdt();
		$TestServerModel = new TestServer();
		
		$PdtIpModel = new PdtIp();
		$SupplierIpModel = new SupplierIp();
		$SwitchLineModel = new SwitchLine();
		
		$PdtManageModel  = new PdtManage();
		$PdtManageTypeModel = new PdtManageType();
		$PdtRoomMangeModel = new PdtRoomMange();
		
		if ( Yii::$app->request->post() ) {
			
			Yii::$app->request->isAjax || die('error');
		
			$post = $this->post();
			$ao_id = $this->post('ao_id');

			if(!$ao_id) {
				$arrReturn = ['status' => 0, 'info'=>'参数异常'];
				return $this->renderJSON($arrReturn);
			}

			#开启事务
			$transaction = Yii::$app->db->beginTransaction(); 
			
			#查询工单
			$AfterorderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->one();
			
			if(in_array($AfterorderInfo['ao_type'], ['已完成','已取消']) ) {
				$arrReturn = ['status' => 0,'info' => '工单状态已完成或已取消'];
				return $this->renderJSON($arrReturn);
			}
			#检测是否有子单未完成或取消
			$AfterorderSonlist = $AfterorderSonlistModel->find()->where(['ao_id' => $ao_id])->asArray()->all();
			
			$check_son_status = true;
			foreach( $AfterorderSonlist as $key=>$val) {
			 	if($val['ao_s_status'] == '未完成') {
				 	$check_son_status = false;
					break;
				}
			}
			
			if( !$check_son_status) {
				$arrReturn = ['status'=>0, 'info'=>'存在子单未完成'];
				return $this->renderJSON($arrReturn);
			}
				
			
			#开始验证IP 
			$IpArr = DataHelper::dotrim($post['ips']);			
			$ipArray = array_filter(array_unique($IpArr));
			
			if( empty($IpArr)) {
				return $this->renderJSON(['status' => 0,'info' => 'IP地址未选择或未填写']);
			}			
			
			#拆分IP
			$Retuen_IPArray = DataHelper::splitIP($ipArray);
			if( !$Retuen_IPArray['status'] ) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0,'info'=> $Retuen_IPArray['info']];
				return $this->renderJSON($arrReturn);
			}			
			$ipArray2 = $Retuen_IPArray['data'];	

			#配置
			$ao_request_content = json_decode($AfterorderInfo->ao_request_content, true);
			
			$servicerprovider = $ao_request_content['servicerprovider'];			
			$idle_id = $ao_request_content['idle_id'];
			
			if( $servicerprovider == 0) {
				$IdlePdtRes = $IdlePdtModel->find()->where(['id' => $idle_id])->asArray()->one();
				
				$Original_ip1 = json_decode($IdlePdtRes['ip'], true);
				$Original_ip2 = json_decode($IdlePdtRes['ip2'], true);
				
				$room_id = $IdlePdtRes['room_id'];
				$ipmi_ip = $IdlePdtRes['ipmi_ip'];
				
			} else {
				$Original_ip1 = $ao_request_content['ip'];
				$Original_ip2 = $ao_request_content['ip2'];
				$room_id = $ao_request_content['room_id'];
				$provider_id = $ao_request_content['provider_id'];
				$ipmi_ip = '';
				
			}
			
			#判断IP是否有变化 (用提交的IP和上一次IP做对比)			
			$bgIPArray = array_unique(array_merge($ipArray2, $Original_ip2));

			$result_insert = array_diff($bgIPArray,	$Original_ip2);
			$result_remove = array_diff($bgIPArray,	$ipArray2);

			if( empty($result_insert) && empty($result_remove) ) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0, 'info' => 'IP地址未改变'];
				return $this->renderJSON($arrReturn);
			}
			
			#当在为自有时。判断IP所属机房是否与产品的机房一致
			if( $servicerprovider == 0 ) {
				$PdtIpAll = $PdtIpModel->find()->select('room_id, line_type_id, vlan')->where(['in','ip',$ipArray2])->asArray()->all();
				$temp = array_column($PdtIpAll, 'room_id');			
				$temp = array_unique($temp); 			#去掉重复的字符串,也就是重复的一维数组
				$temp = array_values($temp); 			#查询排序
				if(count($temp) > 1 ) {
					$arrReturn = ['status' => 0,'info' => 'IP中有属于其他机房的IP地址'];
					return $this->renderJSON($arrReturn);
				} else if( count($temp) == 1) {
					if($temp[0] != $room_id ){
						$arrReturn = ['status' => 0,'info' => 'IP所属机房与机器所属机房不一致'];
						return $this->renderJSON($arrReturn);
					}
				}
				#判断线路类型
				$line_typeList = array_unique(array_column($PdtIpAll, 'line_type_id'));				
				if( count($line_typeList) > 1 ) {
					$arrReturn = ['status' => 0,'info' => '所选IP与其他IP的线路类型不匹配'];
					return $this->renderJSON($arrReturn);
				}
				
				$SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtRes['switch_location'], 'line_type_id' => $line_typeList[0]])->asArray()->one();
				if( empty($SwitchLineList) ) {
					$arrReturn = ['status' => 0,'info' => '机器对应的交换机线路中未有IP的线路类型'];
					return $this->renderJSON($arrReturn);
				}

				#将新增的ip 改为待定状态
				if( !empty($result_insert) ) {
					$updateAll_res = $PdtIpModel->updateAll(['status' => 3], ['ip'=>$result_insert]);
					if( $updateAll_res != count($result_insert) ) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '新增的IP状态更新失败'];
						return $this->renderJSON($arrReturn);
					}
				}

			} else if( $servicerprovider == 1) {#供应商
				#将新增的供应商 ip 入库，状态为3
				if( !empty($result_insert) ) {
					
					#检测新增的供应商IP  是否可用					
					$CheckRes = DataHelper::detect_supplierip($result_insert);
					if( $CheckRes['status'] == 0 ) {
						$transaction->rollBack();
						return $this->renderJSON(['status' => 0, 'info' => $CheckRes['info']]);
					}
					
					#入库
					$insert_Res = $SupplierIpModel->add_peration($result_insert, $provider_id, $room_id, '3');
					if( $insert_Res != count($result_insert) ) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '供应商IP入库失败'];
						return $this->renderJSON($arrReturn);
					}
				}
			}
			
			#增加子单
			#查询是否需要审核
			$typeInfo = $AfterorderSonlistTrialModel->find()->where(['sonlist_type' => '更换IP'])->one();			
			
			$new_content['servicerprovider'] = $servicerprovider;
			$new_content['room_id'] = $room_id;
			$new_content['idle_id'] = $idle_id;
			$new_content['ipmi_ip'] = $ipmi_ip;
			
			$new_content['replace_front']['ip'] = $Original_ip1;
			$new_content['replace_front']['ip2'] = $Original_ip2;
			
			$new_content['replace_after']['ip'] = $ipArray;
			$new_content['replace_after']['ip2'] = $ipArray2;
			
			$time = time();
			
			#生成子单
			$AfterorderSonlistModel->ao_id = $ao_id;
			$AfterorderSonlistModel->ao_s_type = '更换IP';
			$AfterorderSonlistModel->ao_s_content = json_encode($new_content, JSON_UNESCAPED_UNICODE);
			$AfterorderSonlistModel->ao_s_trial_status = $typeInfo->sonlist_istrial == 'Y' ? '等待审核' : '无需审核';
			$AfterorderSonlistModel->ao_s_status = '未完成';
			$AfterorderSonlistModel->ao_s_request_time = $time;
			$AfterorderSonlistModel->ao_s_finish_time = null;
			
			if( !$AfterorderSonlistModel->insert() ) {
				$transaction->rollBack();
				$arrReturn = [
					'status'=>0,
					'info'=>'创建子工单时出现异常，请联系管理员'
				];
				
			} else {
				
				#创建子单审核通知
				TrailAndNotice::createSonTrail($AfterorderInfo->ao_id, $AfterorderSonlistModel->ao_s_sonlist_id);
				
				$transaction->commit();
				$arrReturn = [
					'status'=>1,
					'info'=>'子单提交成功'
				];
			}
			return $this->renderJSON($arrReturn);
			
		} else {
			$ao_id = $this->get('ao_id');
			if(!$ao_id) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			
			$AfterOrderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
			if( empty($AfterOrderInfo) ) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			
			$ao_request_content = json_decode($AfterOrderInfo['ao_request_content'], true);

			$servicerprovider = $ao_request_content['servicerprovider'];
			
			if( $servicerprovider == 0) {
				$IdlePdtRes = $IdlePdtModel->find()->where(['id' => $ao_request_content['idle_id']])->asArray()->one();
				
				$ip = json_decode($IdlePdtRes['ip'], true);
				$room_id = $IdlePdtRes['room_id'];
				
			} else {
				$ip = $ao_request_content['ip'];
				$room_id = $ao_request_content['room_id'];
			}
			#获取IP分类
			$PdtIpClassModel = new PdtIpClass();
			$IpClassList = $PdtIpClassModel->find()->asArray()->all();
			#网段
			$PdtIpNetworkModel = new PdtIpNetwork();
			$IpNetworkList = $PdtIpNetworkModel->find()->where(['room_id' => $room_id])->asArray()->all();
			
			return $this->render('addtestserver-replaceip', [
				'ip' => $ip,
				'servicerprovider' => $servicerprovider,
				'room_id' => $room_id,
				'ao_id' => $ao_id,
				'IpClassList' => $IpClassList,
				'IpNetworkList' => $IpNetworkList
			]);
		}
	}
	
	#更换机器 重新分配子单页面 / 重新分配机器提交
	public function actionBusinessReplaceMachine() {
		
		$AfterorderListModel = new AfterorderList();
		$AfterorderSonlistModel = new AfterorderSonlist();
		$AfterorderSonlistTrialModel = new AfterorderSonlistTrial();
		
		$PipelineListModel = new PipelineList();
		
		$MemberPdtModel = new MemberPdt();
		$IdlePdtModel = new IdlePdt();
		$TestServerModel = new TestServer();
		
		$PdtIpModel = new PdtIp();
		$SupplierIpModel = new SupplierIp();
		$SwitchLineModel = new SwitchLine();
		
		$PdtManageModel  = new PdtManage();
		$PdtManageTypeModel = new PdtManageType();
		$PdtRoomMangeModel = new PdtRoomMange();
		
		if ( Yii::$app->request->post() ) {
			
			Yii::$app->request->isAjax || die('error');
		
			$ao_id = $this->post('ao_id');
			$provider = $this->post('provider');
			$data = $this->post('data');

			if(!$ao_id || !$data || !in_array($provider, [0, 1])) {
				$arrReturn = [
					'status'=>0,
					'info'=>'参数异常'
				];
				return $this->renderJSON($arrReturn);
			}
			
			#开启事务
			$transaction = Yii::$app->db->beginTransaction(); 
			
			#查询工单
			$AfterorderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->one();
			
			if(in_array($AfterorderInfo['ao_type'], ['已完成','已取消']) ) {
				$arrReturn = ['status' => 0,'info' => '工单状态已完成或已取消'];
				return $this->renderJSON($arrReturn);
			}
			#检测是否有子单未完成或取消
			$AfterorderSonlist = $AfterorderSonlistModel->find()->where(['ao_id' => $ao_id])->asArray()->all();
			
			$check_son_status = true;
			foreach( $AfterorderSonlist as $key=>$val) {
			 	if($val['ao_s_status'] == '未完成') {
				 	$check_son_status = false;
					break;
				}
			}
			
			if( !$check_son_status) {
				$arrReturn = ['status'=>0, 'info'=>'存在子单未完成'];
				return $this->renderJSON($arrReturn);
			}
			#查询工作流
			#$Lineinfo = $PipelineListModel->find()->joinwith('linetypes')->where(['line_id' => $AfterorderInfo->line_id])->one();
			
			#配置
			$request_content = json_decode($AfterorderInfo->ao_request_content, true);
			
			$use_original_ip = $request_content['use_original_ip'];		#是否使用原IP，只根据第一次提交的为主	
			$is_exchangedip = $request_content['is_exchangedip'];  		#代表着是否已经更换了IP
			
			$frontData = $request_content['frontData'];
			
			if( $use_original_ip == 1) {
				
				if( $is_exchangedip == 1) {
					
					$frontData = $request_content['afterData'];  #如果已经对换了IP，那么子单重新分配机器，就应该是上一次更换后的机器
					$IdlePdtRes = $IdlePdtModel->find()->where(['id' => $frontData['idle_id']])->asArray()->one();
					$frontData['ip'] = json_decode($IdlePdtRes['ip'], true);
					$frontData['ip2'] = json_decode($IdlePdtRes['ip2'], true);
					
				} else {
					if( $frontData['servicerprovider'] == 0) {
						$frontData = $request_content['frontData'];	 #如果没有对换IP，那么子单重新分配机器，应该是工单中的原机器
					} else {
						$frontData = $request_content['afterData'];	 #如果没有对换IP，那么子单重新分配机器，应该是工单中的原机器
					}
				}
				
			} else {
				$frontData = $request_content['afterData'];
			}

			#每次更换，都是以工单里面的 为主
			$test_id = $data['test_id'];
						
			#获取原机器信息（因为要与原机器比对 使用原IP问题）
			$unionid = $request_content['unionid'];
			
			$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $unionid])->asArray()->one();
			if( $MemberPdtRes['status'] == -1 || $MemberPdtRes['end_time'] < time() ) {
				$arrReturn = ['status' => 0,'info' => '原机器已被删除或已到期'];
				return $this->renderJSON($arrReturn);
			}
			$Original_ip = json_decode($MemberPdtRes['ip'], true);
			$Original_ip2 = json_decode($MemberPdtRes['ip2'], true);
			
			if( $test_id && isset($test_id) ) {
				$TestServerQuery = $TestServerModel->find()->where(['id' => $test_id])->one();
				if( empty($TestServerQuery) ) {
					$arrReturn = ['status' => 0,'info' => '未知的测试机'];
					return $this->renderJSON($arrReturn);
				}
				if( $TestServerQuery->status != 0 ) {
					$arrReturn = ['status' => 0,'info' => '测试机状态不允许选择'];
					return $this->renderJSON($arrReturn);
				}
				$TestServerQuery->status = 4;
				if( !$TestServerQuery->save() ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0,'info' => '测试状态更新失败'];
					return $this->renderJSON($arrReturn);
				}
			}
				
			if($provider == 0) {#如果选用的为自有服务器			
				
				if( !$test_id ) {#如果不是测试机
					$IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $data['idle_id']])->one(); #查询出选择的自有机器信息
					if( $IdlePdtQuery->status != 0) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0,'info' => '自有机器状态异常'];
						return $this->renderJSON($arrReturn);
					}
					$IdlePdtQuery->status = 4;
					if( !$IdlePdtQuery->save() ) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0,'info' => '自有机器状态更新失败'];
						return $this->renderJSON($arrReturn);
					}
				} else {
					$IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $data['idle_id']])->one(); #查询出选择的自有机器信息
				}
				
				$afterConfig['provider'] = $provider;
				$afterConfig['servicerprovider'] = 0;
				$afterConfig['server_type_id'] = $data['server_type_id'];
				$afterConfig['room_id'] = $IdlePdtQuery['room_id'];
				$afterConfig['pdt_id'] = $IdlePdtQuery['pdt_id'];
				
				$afterConfig['config'] = json_decode($IdlePdtQuery['config'], true);
				$afterConfig['config']['requirement_bandwidth'] = $data['requirement_bandwidth'];
				
				$afterConfig['test_id'] = $data['test_id'];
				$afterConfig['idle_id'] = $data['idle_id'];
				$afterConfig['provider_id'] = '';
				$afterConfig['ipmi_ip'] = $IdlePdtQuery['ipmi_ip'];
				
				$afterConfig['ip'] = json_decode($IdlePdtQuery['ip'], true);
				$afterConfig['ip2'] = json_decode($IdlePdtQuery['ip2'], true);
				/*if( $use_original_ip == 1) {
					$afterConfig['ip'] = $Original_ip;
					$afterConfig['ip2'] = $Original_ip2;
					#$afterConfig['should_ip'] = json_decode($IdlePdtQuery['ip'], true);  #如果使用原IP，本来的IP
				} else {
					$afterConfig['ip'] = json_decode($IdlePdtQuery['ip'], true);
					$afterConfig['ip2'] = json_decode($IdlePdtQuery['ip2'], true);
					
					$afterConfig['should_ip'] = [];					#如果不使用原IP，本来的IP
				}*/
				#print_r($afterConfig);exit;
				$afterConfig['config_remark'] = $data['remark'];
				#登录账密
				$afterConfig['install_account'] = $IdlePdtQuery['re_username'];
				$afterConfig['install_pass'] = $IdlePdtQuery['re_password'];
				$afterConfig['install_port'] = $IdlePdtQuery['re_port'];				
				#
				$check_room_id = $afterConfig['room_id'];
				
			} else {
				$afterConfig['provider'] = $provider;
				#供应商服务器，所有数据均由前台传过来，直接赋值写入
				$afterConfig['servicerprovider'] = 1;
				$afterConfig['server_type_id'] = $data['server_type_id'];
				$afterConfig['room_id'] = $data['room_id'];
				$afterConfig['pdt_id'] = $data['pdt_id'];
				
				$afterConfig['config'] = $data['config'];
				$afterConfig['config']['requirement_bandwidth'] = $data['requirement_bandwidth'];
			
				$afterConfig['idle_id'] = '';
				$afterConfig['ipmi_ip'] = '';
				$afterConfig['test_id'] = $data['test_id'];				
				$afterConfig['provider_id'] = $data['provider_id'];	
				
				$afterConfig['config_remark'] = $data['remark'];
				
				$check_provider_id = $afterConfig['provider_id'];
				$check_room_id = $afterConfig['room_id'];
				
				$iplist = DataHelper::dotrim($data['iplist']);
				#如果使用原IP ，供应商可以不提交IP
				if( $use_original_ip != 1) {
					if( empty($iplist) || !$iplist) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '供应商IP为必填选项'];
						return $this->renderJSON($arrReturn);
					}
				}
				
				#解析拆分IP段
				$Retuen_IPArray = DataHelper::splitIP($iplist);
				if( !$Retuen_IPArray['status'] ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info'=> $Retuen_IPArray['info']];
					return $this->renderJSON($arrReturn);
				}
				
				$IpArray1 = $iplist;
				$IpArray2 = $Retuen_IPArray['data'];
				
				/*if( $use_original_ip == 1) {
					$afterConfig['ip'] = $Original_ip;
					$afterConfig['ip2'] = $Original_ip2;
					$afterConfig['should_ip'] = $IpArray1;  #如果使用原IP，本来的IP
				} else {
					$afterConfig['ip'] = $IpArray1;
					$afterConfig['ip2'] = $IpArray2;					
					$afterConfig['should_ip'] = []; 		#如果不使用原IP，本来的IP
				}*/
				$afterConfig['ip'] = $IpArray1;
				$afterConfig['ip2'] = $IpArray2;	
				
				$afterConfig['install_account'] = $data['install_account'];
				$afterConfig['install_pass'] = $data['install_pass'];
				$afterConfig['install_port'] = $data['install_port'];

				if( !$afterConfig['test_id'] ) {
					if( $use_original_ip == 0) {  #只有不使用原IP 才会入库
						$insert_ip = $afterConfig['ip2'];

						$CheckRes = DataHelper::detect_supplierip($insert_ip);			#需要先判断是否能用		
						if( $CheckRes['status'] == 0 ) {
							$transaction->rollBack();
							$arrReturn = ['status' => 0, 'info' => $CheckRes['info']];
							return $this->renderJSON($arrReturn);
						}						
						#入库
						$insert_ip_Res = $SupplierIpModel->add_peration($insert_ip, $afterConfig['provider_id'], $afterConfig['room_id'], '3');
						if( count($insert_ip_Res) != count($insert_ip) ) {
							$transaction->rollBack();
							$arrReturn = ['status' => 0, 'info' => '供应商IP入库失败'];
							return $this->renderJSON($arrReturn);
						}
					}			
				}
			}

			if( $use_original_ip == 1) { #如果要使用原IP  #需要验证 机房  供应商这些是否一致
				
				if( $MemberPdtRes['servicerprovider'] != $provider) {#但如果提供商之前和现在不是一致的
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info' => '原机器服务提供者与所选机器不一致，不能选择使用之前IP！'];
					return $this->renderJSON($arrReturn);
				} else {
					#如果提供商为一致的
					if( $provider == 0) {
						#如果为自有的，判断机房是否一致
						if( $check_room_id != $MemberPdtRes['room_id'] ) {
							$transaction->rollBack();
							$arrReturn = ['status' => 0, 'info' => '更换机器前后机房不一致，不能选择使用原IP'];
							return $this->renderJSON($arrReturn);
						}
						#验证IP线路与交换机线路
						#判定一下 现在的IP是否的Vlan 是符合 #获取机器交换机对应线路的Vlan
						$check_ip = $Original_ip2;
						if( !empty($check_ip) ) {
							$PdtIpAll = $PdtIpModel->find()->select('room_id, line_type_id,vlan')->where(['in','ip', $check_ip])->asArray()->all();
							$line_typeList = array_unique(array_column($PdtIpAll, 'line_type_id')); 
							
							if( count($line_typeList) > 1 ) {
								$transaction->rollBack();
                                $arrReturn = ['status' => 0, 'info' => '原IP与其他IP的线路类型不匹配'];
								return $this->renderJSON($arrReturn);
							}							
							#用更换后的自有机器的交换机查询线路							
							#获取机器对应交换机的线路信息
							$SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtQuery['switch_location'], 'line_type_id' => $line_typeList[0]])->asArray()->one();
							if( empty($SwitchLineList) ) {
								$transaction->rollBack();
                                $arrReturn = ['status' => 0, 'info' => '所选机器对应的交换机线路中未有IP的线路类型'];
								return $this->renderJSON($arrReturn);
							}
						}						
					} else {
						#如果为供应商的。判断供应商是否一致						
						if( $check_provider_id != $MemberPdtRes['provider_id'] || $check_room_id != $MemberPdtRes['room_id']) {
							$transaction->rollBack();
							$arrReturn = ['status' => 0, 'info' => '选择使用原IP，但更换机器前后机房或供应商不一致！'];
							return $this->renderJSON($arrReturn);
						}
					}
				}
			}
			
			#print_r($afterConfig);print_r($frontData); exit;			
			#合成
			$ao_s_content['unionid'] = $MemberPdtRes['unionid'];
			$ao_s_content['testid'] = $test_id;
			$ao_s_content['user_id'] = $MemberPdtRes['user_id'];
			$ao_s_content['replace_front'] = $frontData;
			$ao_s_content['replace_after'] = $afterConfig;
			
			$ao_s_content['use_original_ip'] = $use_original_ip;
			
			
			#查询是否需要审核
			$typeInfo = $AfterorderSonlistTrialModel->find()->where(['sonlist_type' => '重新分配机器'])->one();
			
			$time = time();
			
			#生成子单
			$AfterorderSonlistModel->ao_id = $AfterorderInfo->ao_id;
			$AfterorderSonlistModel->ao_s_type = '重新分配机器';
			$AfterorderSonlistModel->ao_s_content = json_encode($ao_s_content, JSON_UNESCAPED_UNICODE);
			$AfterorderSonlistModel->ao_s_trial_status = $typeInfo->sonlist_istrial == 'Y' ? '等待审核' : '无需审核';
			$AfterorderSonlistModel->ao_s_status = '未完成';
			$AfterorderSonlistModel->ao_s_request_time = $time;
			$AfterorderSonlistModel->ao_s_finish_time = null;
			
			if(!$AfterorderSonlistModel->save()) {
				$transaction->rollBack();
				$arrReturn = [
					'status'=>0,
					'info'=>'创建子工单时出现异常，请联系管理员'
				];
				return $this->renderJSON($arrReturn);
			}
			
			#创建子单审核通知
			$res = TrailAndNotice::createSonTrail($AfterorderInfo->ao_id, $AfterorderSonlistModel->ao_s_sonlist_id);
			
			$transaction->commit();
			$arrReturn = [
				'status'=>1,
				'info'=>'操作完成'
			];
			return $this->renderJSON($arrReturn);
			
		} else {
			$ao_id = $this->get('ao_id');
			if(!$ao_id) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			
			$AfterOrderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
			if( empty($AfterOrderInfo) ) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			
			$request_content = json_decode($AfterOrderInfo['ao_request_content'], true);
			
			$server_type_id = $request_content['frontData']['server_type_id'];
			$pdt_id =$request_content['frontData']['pdt_id'];
			$use_original_ip = $request_content['use_original_ip'];
			
			#获取机房
			$roomRes = $PdtRoomMangeModel->find()->asArray()->all();
			$nowprovider = $request_content['frontData']['provider'];
			
			$PdtManageList = $PdtManageModel->find()->where(new Expression('FIND_IN_SET(:pdt_type_id, pdt_type_id)'))->addParams([':pdt_type_id' => $server_type_id])->asArray()->all();
			
			#服务器分类
			$PdtManageTypeRes = $PdtManageTypeModel->find()->asArray()->all();
			
			return $this->render('business_replace_machine', [
				'AfterOrderInfo' => $AfterOrderInfo,
				'room_list' => $roomRes,
				'nowprovider' => $nowprovider,
				'pdt_list' => $PdtManageList,
				'pdt_type_list' => $PdtManageTypeRes,
				'server_type_id' => $server_type_id,
				'pdt_id' => $pdt_id,
				'use_original_ip' => $use_original_ip,
			]);
		}
		
	}
	
	#更换机器 更换IP页面 / 更换IP提交
	public function actionReplacemachineReplaceip() {
		$AfterorderListModel = new AfterorderList();
		$AfterorderSonlistModel = new AfterorderSonlist();
		$AfterorderSonlistTrialModel = new AfterorderSonlistTrial();
		
		$PipelineListModel = new PipelineList();
		
		$MemberPdtModel = new MemberPdt();
		$IdlePdtModel = new IdlePdt();
		$TestServerModel = new TestServer();
		
		$PdtIpModel = new PdtIp();
		$SupplierIpModel = new SupplierIp();
		$SwitchLineModel = new SwitchLine();
		
		$PdtManageModel  = new PdtManage();
		$PdtManageTypeModel = new PdtManageType();
		$PdtRoomMangeModel = new PdtRoomMange();
		
		if ( Yii::$app->request->post() ) {
			
			Yii::$app->request->isAjax || die('error');
		
			$post = $this->post();
			$ao_id = $this->post('ao_id');

			if(!$ao_id) {
				$arrReturn = ['status' => 0, 'info'=>'参数异常'];
				return $this->renderJSON($arrReturn);
			}

			#开启事务
			$transaction = Yii::$app->db->beginTransaction(); 
			
			#查询工单
			$AfterorderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->one();
			
			if(in_array($AfterorderInfo['ao_type'], ['已完成','已取消']) ) {
				return $this->renderJSON(['status' => 0,'info' => '工单状态已完成或已取消']);
			}
			
			#检测是否有子单未完成或取消
			$AfterorderSonlist = $AfterorderSonlistModel->find()->where(['ao_id' => $ao_id])->asArray()->all();
			
			$check_son_status = true;
			foreach( $AfterorderSonlist as $key=>$val) {
			 	if($val['ao_s_status'] == '未完成') {
				 	$check_son_status = false;
					break;
				}
			}
			
			if( !$check_son_status) {
				return $this->renderJSON(['status'=>0, 'info'=>'存在子单未完成']);
			}
			
			#开始验证IP 
			$IpArr = DataHelper::dotrim($post['ips']);			
			$ipArray = array_filter(array_unique($IpArr));
			
			if( empty($IpArr)) {
				return $this->renderJSON(['status' => 0,'info' => 'IP地址未选择或未填写']);
			}
			
			#拆分IP
			$Retuen_IPArray = DataHelper::splitIP($ipArray);
			if( !$Retuen_IPArray['status'] ) {
				$transaction->rollBack();
				return $this->renderJSON(['status' => 0,'info'=> $Retuen_IPArray['info']]);
			}			
			$ipArray2 = $Retuen_IPArray['data'];	

			#配置
			$ao_request_content = json_decode($AfterorderInfo->ao_request_content, true);
			
			if($ao_request_content['use_original_ip'] == 1) {
				$transaction->rollBack();
				return $this->renderJSON(['status' => 0,'info'=> '选择使用原IP不允许更换IP']);
			}
			
			$servicerprovider = $ao_request_content['afterData']['servicerprovider'];			
			$idle_id = $ao_request_content['afterData']['idle_id'];
			
			if( $servicerprovider == 0) {
				$IdlePdtRes = $IdlePdtModel->find()->where(['id' => $idle_id])->asArray()->one();
				
				$Original_ip1 = json_decode($IdlePdtRes['ip'], true);
				$Original_ip2 = json_decode($IdlePdtRes['ip2'], true);
				
				$room_id = $IdlePdtRes['room_id'];
				$ipmi_ip = $IdlePdtRes['ipmi_ip'];
				
			} else {
				$Original_ip1 = $ao_request_content['afterData']['ip'];
				$Original_ip2 = $ao_request_content['afterData']['ip2'];
				
				$room_id = $ao_request_content['afterData']['room_id'];
				$provider_id = $ao_request_content['afterData']['provider_id'];
				$ipmi_ip = '';
				
			}
			
			#判断IP是否有变化 (用提交的IP和上一次IP做对比)			
			$bgIPArray = array_unique(array_merge($ipArray2, $Original_ip2));

			$result_insert = array_diff($bgIPArray,	$Original_ip2);
			$result_remove = array_diff($bgIPArray,	$ipArray2);

			if( empty($result_insert) && empty($result_remove) ) {
				$transaction->rollBack();
				return $this->renderJSON(['status' => 0, 'info' => 'IP地址未改变']);
			}
			
			#当在为自有时。判断IP所属机房是否与产品的机房一致
			if( $servicerprovider == 0 ) {
				$PdtIpAll = $PdtIpModel->find()->select('room_id, line_type_id, vlan')->where(['in','ip',$ipArray2])->asArray()->all();
				$temp = array_column($PdtIpAll, 'room_id');			
				$temp = array_unique($temp); 			#去掉重复的字符串,也就是重复的一维数组
				$temp = array_values($temp); 			#查询排序
				if(count($temp) > 1 ) {
					$transaction->rollBack();
					return $this->renderJSON(['status' => 0,'info' => 'IP中有属于其他机房的IP地址']);
					
				} else if( count($temp) == 1) {
					
					if($temp[0] != $room_id ) {
						$transaction->rollBack();
						return $this->renderJSON(['status' => 0,'info' => 'IP所属机房与机器所属机房不一致']);
					}
					
				}
				#判断线路类型
				$line_typeList = array_unique(array_column($PdtIpAll, 'line_type_id'));				
				if( count($line_typeList) > 1 ) {
					$transaction->rollBack();
					return $this->renderJSON(['status' => 0,'info' => '所选IP与其他IP的线路类型不匹配']);
				}
				
				$SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtRes['switch_location'], 'line_type_id' => $line_typeList[0]])->asArray()->one();
				if( empty($SwitchLineList) ) {
					$transaction->rollBack();
					return $this->renderJSON(['status' => 0,'info' => '机器对应的交换机线路中未有IP的线路类型']);
				}

				#将新增的ip 改为待定状态
				if( !empty($result_insert) ) {
					$updateAll_res = $PdtIpModel->updateAll(['status' => 3], ['ip'=>$result_insert]);
					if( $updateAll_res != count($result_insert) ) {
						$transaction->rollBack();
						return $this->renderJSON(['status' => 0, 'info' => '新增的IP状态更新失败']);
					}
				}

			} else if( $servicerprovider == 1) {#供应商
				#将新增的供应商 ip 入库，状态为3
				if( !empty($result_insert) ) {
					
					#检测新增的供应商IP  是否可用					
					$CheckRes = DataHelper::detect_supplierip($result_insert);
					if( $CheckRes['status'] == 0 ) {
						$transaction->rollBack();
						return $this->renderJSON(['status' => 0, 'info' => $CheckRes['info']]);
					}
					
					#入库
					$insert_Res = $SupplierIpModel->add_peration($result_insert, $provider_id, $room_id, '3');
					if( $insert_Res != count($result_insert) ) {
						$transaction->rollBack();
						return $this->renderJSON(['status' => 0, 'info' => '供应商IP入库失败']);
					}
				}
			}
			
			#增加子单
			#查询是否需要审核
			$typeInfo = $AfterorderSonlistTrialModel->find()->where(['sonlist_type' => '更换IP'])->one();			
			
			$new_content['servicerprovider'] = $servicerprovider;
			$new_content['room_id'] = $room_id;
			$new_content['idle_id'] = $idle_id;
			$new_content['ipmi_ip'] = $ipmi_ip;
			
			$new_content['replace_front']['ip'] = $Original_ip1;
			$new_content['replace_front']['ip2'] = $Original_ip2;
			
			$new_content['replace_after']['ip'] = $ipArray;
			$new_content['replace_after']['ip2'] = $ipArray2;
			
			$time = time();
			#print_r($new_content);exit;
			#生成子单
			$AfterorderSonlistModel->ao_id = $ao_id;
			$AfterorderSonlistModel->ao_s_type = '更换IP';
			$AfterorderSonlistModel->ao_s_content = json_encode($new_content, JSON_UNESCAPED_UNICODE);
			$AfterorderSonlistModel->ao_s_trial_status = $typeInfo->sonlist_istrial == 'Y' ? '等待审核' : '无需审核';
			$AfterorderSonlistModel->ao_s_status = '未完成';
			$AfterorderSonlistModel->ao_s_request_time = $time;
			$AfterorderSonlistModel->ao_s_finish_time = null;
			
			if( !$AfterorderSonlistModel->insert() ) {
				$transaction->rollBack();
				$arrReturn = ['status'=>0, 'info'=>'创建子工单时出现异常，请联系管理员'];
				
			} else {
				
				#创建子单审核通知
				TrailAndNotice::createSonTrail($AfterorderInfo->ao_id, $AfterorderSonlistModel->ao_s_sonlist_id);
				
				$transaction->commit();
				$arrReturn = ['status'=>1, 'info'=>'子单提交成功'];
			}
			
			return $this->renderJSON($arrReturn);
			
		} else {
			
			$ao_id = $this->get('ao_id');
			
			if(!$ao_id) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			
			$AfterOrderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
			if( empty($AfterOrderInfo) ) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			
			$ao_request_content = json_decode($AfterOrderInfo['ao_request_content'], true);

			$servicerprovider = $ao_request_content['afterData']['servicerprovider'];
			
			if( $servicerprovider == 0) {
				$IdlePdtRes = $IdlePdtModel->find()->where(['id' => $ao_request_content['afterData']['idle_id']])->asArray()->one();
				
				$ip = json_decode($IdlePdtRes['ip'], true);
				$room_id = $IdlePdtRes['room_id'];
				
			} else {
				$ip = $ao_request_content['afterData']['ip'];
				$room_id = $ao_request_content['afterData']['room_id'];
			}
			
			#获取IP分类
			$PdtIpClassModel = new PdtIpClass();
			$IpClassList = $PdtIpClassModel->find()->asArray()->all();
			#网段
			$PdtIpNetworkModel = new PdtIpNetwork();
			$IpNetworkList = $PdtIpNetworkModel->find()->where(['room_id' => $room_id])->asArray()->all();
			
			return $this->render('replacemachine-replaceip', [
				'ip' => $ip,
				'servicerprovider' => $servicerprovider,
				'room_id' => $room_id,
				'ao_id' => $ao_id,
				'IpClassList' => $IpClassList,
				'IpNetworkList' => $IpNetworkList
			]);
		}
		
	}
	
	#变更配置 更换IP子单页面 / 子单提交
	public function actionChangeconfigReplaceip() {
		$AfterorderListModel = new AfterorderList();
		$AfterorderSonlistTrialModel = new AfterorderSonlistTrial();
		$AfterorderSonlistModel = new AfterorderSonlist();
		$PipelineListModel = new PipelineList();
		$MemberPdtModel = new MemberPdt();
		$PipelineTypesModel = new PipelineTypes();
		$PdtIpClassModel = new PdtIpClass();
		$PdtIpNetworkModel = new PdtIpNetwork();
		$PdtIpModel = new PdtIp();
		$SupplierIpModel = new SupplierIp();
		$IdlePdtModel = new IdlePdt();
		$SwitchLineModel = new SwitchLine();
		#提交
		if ( Yii::$app->request->post() ) {
			
			$post = $this->post();
			$ao_id = $post['ao_id'];
			
			$AfterOrderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
			
			if(in_array($AfterOrderInfo['ao_type'], ['已完成','已取消'])) {
				$arrReturn = ['status' => 0,'info' => '工单状态已完成或已取消'];
				return $this->renderJSON($arrReturn);
			}
			#检测是否有子单未完成或取消
			$AfterorderSonlist = $AfterorderSonlistModel->find()->where(['ao_id' => $ao_id])->asArray()->all();
			
			$check_son_status = true;
			foreach( $AfterorderSonlist as $key=>$val) {
			 	if($val['ao_s_status'] == '未完成') {
				 	$check_son_status = false;
					break;
				}
			}
			if( !$check_son_status) {
				$arrReturn = ['status'=>0, 'info'=>'存在子单未完成'];
				return $this->renderJSON($arrReturn);
			}
			
			#开启事务
			$transaction = Yii::$app->db->beginTransaction();
			
			#开始验证IP 
			$IpArr = DataHelper::dotrim($post['ips']);
			if( empty($IpArr)) {
				return $this->renderJSON(['status' => 0,'info' => 'IP地址未选择或未填写']);
			}
			
			$ipArray = array_filter(array_unique($IpArr));	#处理的得到的IP数组1   #去重 去空
			
			#拆分IP
			$Retuen_IPArray = DataHelper::splitIP($ipArray);
			if( !$Retuen_IPArray['status'] ) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0,'info'=> $Retuen_IPArray['info']
				];
				return $this->renderJSON($arrReturn);
			}
			
			$ipArray2 = $Retuen_IPArray['data'];	
				
			$request_content = json_decode($AfterOrderInfo['ao_request_content'], true);
			
			$unionid = $request_content['unionid'];
			$isneed_replaceip = $request_content['isneed_replaceip'];
			
			$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $unionid])->asArray()->one();
			
			$Original_ip = json_decode($MemberPdtRes['ip2']);
			$servicerprovider = $MemberPdtRes['servicerprovider'];
			$room_id = $MemberPdtRes['room_id'];
			
			$modifyData = $request_content['modifyData'];
			
			
			if( isset($isneed_replaceip) && $isneed_replaceip == 1) { #表示有需要更换IP
			
			 	$last_ips = DataHelper::filter_by_value($modifyData, 'modify_type', 'ip');
				$last_ip1 = $last_ips['modify_data']['new_config'];
				
				$Retuen_IPArray = DataHelper::splitIP($last_ip1);
				$last_ip2 = $Retuen_IPArray['data'];
				
				#判断IP是否有变化 (用提交的IP和上一次IP做对比)
				$bgIPArray = array_unique(array_merge($last_ip2, $ipArray2, $Original_ip)); 
				
				$new_bgIPArray_1 = array_unique(array_merge($last_ip2, $Original_ip));
				$new_bgIPArray_2 = array_unique(array_merge($ipArray2, $Original_ip));
				
				$new_bgIPArray_3 = array_unique(array_merge($ipArray2, $last_ip2));

				$result_insert = array_diff($bgIPArray,	$new_bgIPArray_1);
				$result_remove = array_diff($bgIPArray,	$new_bgIPArray_2);
				
				if( empty($result_remove) ) { #如果为空，可能因为只是减少了IP
					$result_remove = array_diff($new_bgIPArray_2, $ipArray2);
				}
				
			} else {

				$last_ip1 = json_decode($MemberPdtRes['ip']);
				$last_ip2 = json_decode($MemberPdtRes['ip2']);
				
				#判断IP是否有变化 (用提交的IP和上一次IP做对比)
				
				$bgIPArray = array_unique(array_merge($ipArray2, $Original_ip));
				
				$result_insert = array_diff($bgIPArray,	$Original_ip);
				$result_remove = array_diff($bgIPArray,	$ipArray2);
			
			}

			if( empty($result_insert) && empty($result_remove) ) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0, 'info' => 'IP地址未改变'];
				return $this->renderJSON($arrReturn);
			}
			
			#当在为自有时。判断IP所属机房是否与产品的机房一致
			if( $servicerprovider == 0 ) {
				$PdtIpAll = $PdtIpModel->find()->select('room_id, line_type_id, vlan')->where(['in','ip',$ipArray2])->asArray()->all();
				$temp = array_column($PdtIpAll, 'room_id');			
				$temp = array_unique($temp); #去掉重复的字符串,也就是重复的一维数组
				$temp = array_values($temp); #查询排序
				if(count($temp) > 1 ) {
					$arrReturn = ['status' => 0,'info' => 'IP中有属于其他机房的IP地址'];
					return $this->renderJSON($arrReturn);
				} else if( count($temp) == 1) {
					if($temp[0] != $room_id ){
						$arrReturn = ['status' => 0,'info' => 'IP所属机房与机器所属机房不一致'];
						return $this->renderJSON($arrReturn);
					}
				}
				#判断线路类型
				$line_typeList = array_unique(array_column($PdtIpAll, 'line_type_id'));				
				if( count($line_typeList) > 1 ) {
					$arrReturn = ['status' => 0,'info' => '所选IP与其他IP的线路类型不匹配'];
					return $this->renderJSON($arrReturn);
				}
				
				#获取当前自有机器对应交换机的线路信息
				$IdlePdtRes = $IdlePdtModel->find()->where(['id' => $MemberPdtRes['idle_id']])->asArray()->one();
				
				$SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtRes['switch_location'], 'line_type_id' => $line_typeList[0]])->asArray()->one();
				if( empty($SwitchLineList) ) {
					$arrReturn = ['status' => 0,'info' => '机器对应的交换机线路中未有IP的线路类型'];
					return $this->renderJSON($arrReturn);
				}
				
				#将新增的ip 改为待定状态
				if( !empty($result_insert) ) {
					$updateAll_res = $PdtIpModel->updateAll(['status' => 3],['ip'=>$result_insert]);
					if( $updateAll_res != count($result_insert) ) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '新增的IP状态更新失败'];
						return $this->renderJSON($arrReturn);
					}
				}

			} else if( $servicerprovider == 1) {#供应商
				#将新增的供应商 ip 入库，状态为3
				if( !empty($result_insert) ) {
					#检测新增的供应商IP  是否可用					
					$CheckRes = DataHelper::detect_supplierip($result_insert);
					if( $CheckRes['status'] == 0 ) {
						$transaction->rollBack();
						return $this->renderJSON(['status' => 0, 'info' => $CheckRes['info']]);
					}
					#入库
					$insert_Res = $SupplierIpModel->add_peration($result_insert, $MemberPdtRes['provider_id'], $MemberPdtRes['room_id'], '3');
					if( $insert_Res != count($result_insert) ) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '供应商IP入库失败'];
						return $this->renderJSON($arrReturn);
					}
				}
			}
			
			#增加子单
			#查询是否需要审核
			$typeInfo = $AfterorderSonlistTrialModel->find()->where(['sonlist_type' => '更换IP'])->one();
			
			$new_content['unionid'] = $unionid;
			$new_content['servicerprovider'] = $servicerprovider;
			$new_content['room_id'] = $room_id;
			$new_content['ipmi_ip'] = $MemberPdtRes['ipmi_ip'];
			
			$new_content['replace_front']['ip'] = $last_ip1;
			$new_content['replace_front']['ip2'] = $last_ip2;
			
			$new_content['replace_after']['ip'] = $ipArray;
			$new_content['replace_after']['ip2'] = $ipArray2;
			
			$time = time();
			
			#生成子单
			$AfterorderSonlistModel->ao_id = $ao_id;
			$AfterorderSonlistModel->ao_s_type = '更换IP';
			$AfterorderSonlistModel->ao_s_content = json_encode($new_content, JSON_UNESCAPED_UNICODE);
			$AfterorderSonlistModel->ao_s_trial_status = $typeInfo->sonlist_istrial == 'Y' ? '等待审核' : '无需审核';
			$AfterorderSonlistModel->ao_s_status = '未完成';
			$AfterorderSonlistModel->ao_s_request_time = $time;
			$AfterorderSonlistModel->ao_s_finish_time = null;
			if( !$AfterorderSonlistModel->insert() ) {
				$transaction->rollBack();
				$arrReturn = [
					'status'=>0,
					'info'=>'创建子工单时出现异常，请联系管理员'
				];
				
			} else {
				
				#创建子单审核通知
				TrailAndNotice::createSonTrail($ao_id, $AfterorderSonlistModel->ao_s_sonlist_id);
				
				$transaction->commit();
				$arrReturn = [
					'status'=>1,
					'info'=>'子单提交成功'
				];
			}
			return $this->renderJSON($arrReturn);
			
		} else {
			#页面
			$ao_id = $this->get('ao_id');
			if(!$ao_id) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			
			$AfterOrderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
			if(in_array($AfterOrderInfo['ao_type'], ['已完成','已取消'])) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			$request_content = json_decode($AfterOrderInfo['ao_request_content'], true);
			
			#$PipelineListRes = $PipelineListModel->find()->where(['line_id' => $AfterOrderInfo['line_id']])->asArray()->one();
			#$PipelineTypesRes = $PipelineTypesModel->find()->where(['line_type_id' => $PipelineListRes['line_type_id']])->asArray()->one();
			#$line_type_name = $PipelineTypesRes['line_type_name'];
			

			$unionid = $request_content['unionid'];
			$servicerprovider = $request_content['servicerprovider'];
			$room_id = $request_content['room_id'];
			$ipmi_ip = $request_content['ipmi_ip'];

			if(yii\helpers\ArrayHelper::getValue($request_content,'isneed_replaceip') == 1) {
				$last_ips = DataHelper::filter_by_value($request_content['modifyData'], 'modify_type', 'ip');
		
				$ip = $last_ips['modify_data']['new_config'];
				
			} else {
				$ip = $request_content['ip'];
			}
			#IP分类
			$IpClassList = $PdtIpClassModel->find()->asArray()->all();
			#网段
			$IpNetworkList = $PdtIpNetworkModel->find()->where(['room_id' => $room_id])->asArray()->all();
			return $this->render('changeconfig-replaceip', [
				'ao_id' => $ao_id,
				'IpClassList' => $IpClassList,
				'IpNetworkList' => $IpNetworkList,
				'servicerprovider' => $servicerprovider,
				'room_id' => $room_id,
				'ip' => $ip,
				
			]);
			
			
			
		}
	}
	
	#修改测试机 更换IP 子单页面 / 子单提交
	public function actionUpdatetestReplaceip() {
		$AfterorderListModel = new AfterorderList();
		$AfterorderSonlistTrialModel = new AfterorderSonlistTrial();
		$AfterorderSonlistModel = new AfterorderSonlist();
		$PipelineListModel = new PipelineList();
		
		$TestServerModel = new TestServer();
		$MemberPdtModel = new MemberPdt();
		$PipelineTypesModel = new PipelineTypes();
		$PdtIpClassModel = new PdtIpClass();
		$PdtIpNetworkModel = new PdtIpNetwork();
		$PdtIpModel = new PdtIp();
		$SupplierIpModel = new SupplierIp();
		$IdlePdtModel = new IdlePdt();
		$SwitchLineModel = new SwitchLine();
		#提交
		if ( Yii::$app->request->post() ) {
			
			$post = $this->post();
			$ao_id = $post['ao_id'];
			
			$AfterOrderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
			
			if(in_array($AfterOrderInfo['ao_type'], ['已完成','已取消'])) {
				$arrReturn = ['status' => 0,'info' => '工单状态已完成或已取消'];
				return $this->renderJSON($arrReturn);
			}
			#检测是否有子单未完成或取消
			$AfterorderSonlist = $AfterorderSonlistModel->find()->where(['ao_id' => $ao_id])->asArray()->all();
			
			$check_son_status = true;
			foreach( $AfterorderSonlist as $key=>$val) {
			 	if($val['ao_s_status'] == '未完成') {
				 	$check_son_status = false;
					break;
				}
			}
			if( !$check_son_status) {
				$arrReturn = ['status'=>0, 'info'=>'存在子单未完成'];
				return $this->renderJSON($arrReturn);
			}
			
			#开启事务
			$transaction = Yii::$app->db->beginTransaction();
			
			#开始验证IP 
			$IpArr = DataHelper::dotrim($post['ips']);
			if( empty($IpArr)) {
				return $this->renderJSON(['status' => 0,'info' => 'IP地址未选择或未填写']);
			}
			
			$ipArray = array_filter(array_unique($IpArr));	#处理的得到的IP数组1   #去重 去空
			
			#拆分IP
			$Retuen_IPArray = DataHelper::splitIP($ipArray);
			if( !$Retuen_IPArray['status'] ) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0,'info'=> $Retuen_IPArray['info']];
				return $this->renderJSON($arrReturn);
			}
			
			$ipArray2 = $Retuen_IPArray['data'];	
				
			$request_content = json_decode($AfterOrderInfo['ao_request_content'], true);
			
			$test_id = $request_content['test_id'];
			$isneed_replaceip = $request_content['isneed_replaceip'];
			
			$TestServerRes = $TestServerModel->find()->where(['id' => $test_id])->asArray()->one();
			
			$Original_ip = json_decode($TestServerRes['ip2']);
			$servicerprovider = $TestServerRes['servicerprovider'];
			$room_id = $TestServerRes['room_id'];
			
			$modifyData = $request_content['modifyData'];
			
			if( isset($isneed_replaceip) && $isneed_replaceip == 1) { #表示有需要更换IP
			 	$last_ips = DataHelper::filter_by_value($modifyData, 'modify_type', 'ip');
				$last_ip1 = $last_ips['modify_data']['new_config'];
				
				$Retuen_IPArray = DataHelper::splitIP($last_ip1);
				$last_ip2 = $Retuen_IPArray['data'];	
				
				#判断IP是否有变化 (用提交的IP和上一次IP做对比)
				$bgIPArray = array_unique(array_merge($last_ip2, $ipArray2, $Original_ip)); 
				
				$new_bgIPArray_1 = array_unique(array_merge($last_ip2, $Original_ip));
				$new_bgIPArray_2 = array_unique(array_merge($ipArray2, $Original_ip));
				
				$result_insert = array_diff($bgIPArray,	$new_bgIPArray_1);
				$result_remove = array_diff($bgIPArray,	$new_bgIPArray_2);
				
				if( empty($result_remove) ) { #如果为空，可能因为只是减少了IP
					$result_remove = array_diff($new_bgIPArray_2, $ipArray2);
				}
				
			} else {
				$last_ip1 = json_decode($TestServerRes['ip']);
				$last_ip2 = json_decode($TestServerRes['ip2']);
				
				#判断IP是否有变化 (用提交的IP和上一次IP做对比)
				
				$bgIPArray = array_unique(array_merge($ipArray2, $Original_ip));
				
				$result_insert = array_diff($bgIPArray,	$Original_ip);
				$result_remove = array_diff($bgIPArray,	$ipArray2);
			
			}
			
			
			if( empty($result_insert) && empty($result_remove) ) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0, 'info' => 'IP地址未改变'];
				return $this->renderJSON($arrReturn);
			}
			
			#当在为自有时。判断IP所属机房是否与产品的机房一致
			if( $servicerprovider == 0 ) {
				$PdtIpAll = $PdtIpModel->find()->select('room_id, line_type_id, vlan')->where(['in','ip',$ipArray2])->asArray()->all();
				$temp = array_column($PdtIpAll, 'room_id');			
				$temp = array_unique($temp); #去掉重复的字符串,也就是重复的一维数组
				$temp = array_values($temp); #查询排序
				if(count($temp) > 1 ) {
					$arrReturn = ['status' => 0,'info' => 'IP中有属于其他机房的IP地址'];
					return $this->renderJSON($arrReturn);
				} else if( count($temp) == 1) {
					if($temp[0] != $room_id ){
						$arrReturn = ['status' => 0,'info' => 'IP所属机房与机器所属机房不一致'];
						return $this->renderJSON($arrReturn);
					}
				}
				
				#判断线路类型
				$line_typeList = array_unique(array_column($PdtIpAll, 'line_type_id'));				
				if( count($line_typeList) > 1 ) {
					$arrReturn = ['status' => 0,'info' => '所选IP与其他IP的线路类型不匹配'];
					return $this->renderJSON($arrReturn);
				}
				
				#获取当前自有机器对应交换机的线路信息
				$IdlePdtRes = $IdlePdtModel->find()->where(['id' => $TestServerRes['idle_id']])->asArray()->one();
				
				$SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtRes['switch_location'], 'line_type_id' => $line_typeList[0]])->asArray()->one();
				if( empty($SwitchLineList) ) {
					$arrReturn = ['status' => 0,'info' => '机器对应的交换机线路中未有IP的线路类型'];
					return $this->renderJSON($arrReturn);
				}
				
				#将新增的ip 改为待定状态
				if( !empty($result_insert) ) {
					$updateAll_res = $PdtIpModel->updateAll(['status' => 3],['ip'=>$result_insert]);
					if( $updateAll_res != count($result_insert) ) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '新增的IP状态更新失败'];
						return $this->renderJSON($arrReturn);
					}
				}

			} else if( $servicerprovider == 1) {#供应商
				#将新增的供应商 ip 入库，状态为3
				if( !empty($result_insert) ) {
					#检测新增的供应商IP  是否可用					
					$CheckRes = DataHelper::detect_supplierip($result_insert);
					if( $CheckRes['status'] == 0 ) {
						$transaction->rollBack();
						return $this->renderJSON(['status' => 0, 'info' => $CheckRes['info']]);
					}
					#入库
					$insert_Res = $SupplierIpModel->add_peration($result_insert, $TestServerRes['provider_id'], $TestServerRes['room_id'], '3');
					if( $insert_Res != count($result_insert) ) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '供应商IP入库失败'];
						return $this->renderJSON($arrReturn);
					}
				}
			}
			
			#增加子单
			#查询是否需要审核
			$typeInfo = $AfterorderSonlistTrialModel->find()->where(['sonlist_type' => '更换IP'])->one();
			
			$new_content['test_id'] = $test_id;
			$new_content['servicerprovider'] = $servicerprovider;
			$new_content['room_id'] = $room_id;
			$new_content['ipmi_ip'] = $TestServerRes['ipmi_ip'];
			
			$new_content['replace_front']['ip'] = $last_ip1;
			$new_content['replace_front']['ip2'] = $last_ip2;
			
			$new_content['replace_after']['ip'] = $ipArray;
			$new_content['replace_after']['ip2'] = $ipArray2;
			
			$time = time();
			
			#生成子单
			$AfterorderSonlistModel->ao_id = $ao_id;
			$AfterorderSonlistModel->ao_s_type = '更换IP';
			$AfterorderSonlistModel->ao_s_content = json_encode($new_content, JSON_UNESCAPED_UNICODE);
			$AfterorderSonlistModel->ao_s_trial_status = $typeInfo->sonlist_istrial == 'Y' ? '等待审核' : '无需审核';
			$AfterorderSonlistModel->ao_s_status = '未完成';
			$AfterorderSonlistModel->ao_s_request_time = $time;
			$AfterorderSonlistModel->ao_s_finish_time = null;
			if( !$AfterorderSonlistModel->insert() ) {
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info' => '创建子工单时出现异常，请联系管理员'
				];
				
			} else {
				
				#创建子单审核通知
				TrailAndNotice::createSonTrail($ao_id, $AfterorderSonlistModel->ao_s_sonlist_id);
				
				$transaction->commit();
				$arrReturn = [
					'status'=>1,
					'info'=>'子单提交成功'
				];
			}
			return $this->renderJSON($arrReturn);
			
		} else {
			#页面
			$ao_id = $this->get('ao_id');
			if(!$ao_id) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			
			$AfterOrderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
			if(in_array($AfterOrderInfo['ao_type'], ['已完成','已取消'])) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			$request_content = json_decode($AfterOrderInfo['ao_request_content'], true);			
			
			$test_id = $request_content['test_id'];
			$servicerprovider = $request_content['servicerprovider'];
			$room_id = $request_content['room_id'];
			$ipmi_ip = $request_content['ipmi_ip'];

			if( $request_content['isneed_replaceip'] == 1) {
				$last_ips = DataHelper::filter_by_value($request_content['modifyData'], 'modify_type', 'ip');		
				$ip = $last_ips['modify_data']['new_config'];
				
			} else {
				$ip = $request_content['ip'];
			}
			#IP分类
			$IpClassList = $PdtIpClassModel->find()->asArray()->all();
			#网段
			$IpNetworkList = $PdtIpNetworkModel->find()->where(['room_id' => $room_id])->asArray()->all();
			return $this->render('updatetest-replaceip', [
				'ao_id' => $ao_id,
				'IpClassList' => $IpClassList,
				'IpNetworkList' => $IpNetworkList,
				'servicerprovider' => $servicerprovider,
				'room_id' => $room_id,
				'ip' => $ip,
				
			]);
			
			
			
		}
		
	}
	
	#更换IP（未用）
	public function actionReplaceIpold() {
		$AfterorderListModel = new AfterorderList();
		$AfterorderSonlistTrialModel = new AfterorderSonlistTrial();
		$AfterorderSonlistModel = new AfterorderSonlist();
		$PipelineListModel = new PipelineList();
		$MemberPdtModel = new MemberPdt();
		$PipelineTypesModel = new PipelineTypes();
		$PdtIpClassModel = new PdtIpClass();
		$PdtIpNetworkModel = new PdtIpNetwork();
		$PdtIpModel = new PdtIp();
		$SupplierIpModel = new SupplierIp();
		$IdlePdtModel = new IdlePdt();
		$SwitchLineModel = new SwitchLine();
		#提交
		if ( Yii::$app->request->post() ) {
			$post = $this->post();
			$ao_id = $post['ao_id'];
			$AfterOrderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
			if(in_array($AfterOrderInfo['ao_type'], ['已完成','已取消'])) {
				$arrReturn = [
					'status' => 0,
					'info' => '工单状态已完成或已取消'
				];
				return $this->renderJSON($arrReturn);
			}
			#开启事务
			$transaction = Yii::$app->db->beginTransaction();
			#开始验证IP
			$IpArr = DataHelper::dotrim($post['ips']);
			if( empty($IpArr)) {
				return $this->renderJSON([
					'status' => 0,
					'info' => 'IP地址未选择或未填写'
				]);
			}			
			$ipArray = array_filter(array_unique($IpArr));	#处理的得到的IP数组1   #去重 去空
			#拆分IP
			$Retuen_IPArray = DataHelper::splitIP($ipArray);
			if( !$Retuen_IPArray['status'] ) {
				$transaction->rollBack();
				$arrReturn = [
					'status' => 0,
					'info'=> $Retuen_IPArray['info']
				];
				return $this->renderJSON($arrReturn);
			}
			$ipArray2 = $Retuen_IPArray['data'];
			#判断IP组2是否正常
			if( !empty($ipArray2) ) {
				foreach ($ipArray2 as $value) {
					if(!filter_var($value, FILTER_VALIDATE_IP)){
						$arrReturn = [
							'status' => 0,
							'info' => $value.'为不合法的IP地址'
						];
						return $this->renderJSON($arrReturn);
					}
				}
			}		
				
			$request_content = json_decode($AfterOrderInfo['ao_request_content'], true);
			
			$PipelineListRes = $PipelineListModel->find()->where(['line_id' => $AfterOrderInfo['line_id']])->asArray()->one(); 
			$PipelineTypesRes = $PipelineTypesModel->find()->where(['line_type_id' => $PipelineListRes['line_type_id']])->asArray()->one();
			$line_type_name = $PipelineTypesRes['line_type_name'];
			if( $line_type_name == '更换IP') {
				#
				
				$unionid = $request_content['unionid'];
				$last_ip = $request_content['afterData']['ip2']; 				#上一次的IP				
				$Original_ip = $request_content['frontData']['ip2'];			#获取最原始的IP
				$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $unionid])->asArray()->one();
				$servicerprovider = $MemberPdtRes['servicerprovider'];
				$room_id = $MemberPdtRes['room_id'];
			
				#判断IP是否有变化 (用提交的IP和最原始的IP做对比)
				$hasChangeIP_1 = false;
				foreach($ipArray2 as $k => $v) {
					if(!in_array($v, $Original_ip)) {
						$hasChangeIP_1 = true;
						break;
					}				
				}
				foreach($Original_ip as $k => $v) {
					if(!in_array($v, $ipArray2)) {
						$hasChangeIP_1 = true;
						break;
					}
				}
				if(!$hasChangeIP_1) {
					#如果没有更换IP
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => 'IP地址与原始IP未做更换'
					];
					return $this->renderJSON($arrReturn);
				}
				#判断此次提交的IP与上一次的IP是否有改变
				$hasChangeIP_2 = false;
				foreach($ipArray2 as $k => $v) {
					if(!in_array($v, $last_ip)) {
						$hasChangeIP_2 = true;
						break;
					}				
				}
				foreach($last_ip as $k => $v) {
					if(!in_array($v, $ipArray2)) {
						$hasChangeIP_2 = true;
						break;
					}
				}
				if(!$hasChangeIP_2) {
					$transaction->rollBack();
					$arrReturn = [
						'status' => 0,
						'info' => 'IP地址与上一次提交的IP未做更换'
					];
					return $this->renderJSON($arrReturn);
				}
				
				#当在为自有时。判断IP所属机房是否与产品的机房一致
				if( $servicerprovider == 0 ) {
					$PdtIpAll = $PdtIpModel->find()->select('room_id, line_type_id, vlan')->where(['in','ip',$ipArray2])->asArray()->all();
					$temp = array_column($PdtIpAll, 'room_id');			
					$temp = array_unique($temp); #去掉重复的字符串,也就是重复的一维数组
					$temp = array_values($temp); #查询排序
					if(count($temp) > 1 ) {
						$arrReturn = [
							'status' => 0,
							'info' => 'IP中有属于其他机房的IP地址'
						];
						return $this->renderJSON($arrReturn);
					} else if( count($temp) == 1) {
						if($temp[0] != $room_id ){
							$arrReturn = [
								'status' => 0,
								'info' => 'IP所属机房与机器所属机房不一致'
							];
							return $this->renderJSON($arrReturn);
						}
					}				
					#判断线路类型
					$line_typeList = array_unique(array_column($PdtIpAll, 'line_type_id'));				
					if( count($line_typeList) > 1 ) {
						$arrReturn = [
							'status' => 0,
							'info' => '所选IP与其他IP的线路类型不匹配'
						];
						return $this->renderJSON($arrReturn);
					}
					#获取机器对应交换机的线路信息
					$IdlePdtRes = $IdlePdtModel->find()->where(['id' => $MemberPdtRes['idle_id']])->asArray()->one();					
					$SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtRes['switch_location'], 'line_type_id' => $line_typeList[0]])->asArray()->one();
					if( empty($SwitchLineList) ) {
						$arrReturn = [
							'status' => 0,
							'info' => '机器对应的交换机线路中未有IP的线路类型'
						];
						return $this->renderJSON($arrReturn);
					}
					#检测完毕 就将新加的IP改为待定(将上次的和原始IP合并，在与新提交的IP对比)
					$bgIPArray = array_unique(array_merge($last_ip,$Original_ip));
					$new_bgIPArray = array_unique(array_merge($last_ip,$Original_ip,$ipArray2));
					
					$result_bangding = array_diff($new_bgIPArray,$bgIPArray); 	#新增IP  改为待定（需要改为闲置的就暂时无需修改，等审核通过在改）		
					#$result_jiechu = array_diff($new_bgIPArray,$ipArray2); 	#需要改为闲置的
					#新的ip需要绑定的 改为待定
					if( !empty($result_bangding) ) {
						$res1 = $PdtIpModel->updateAll(['status' => 3],['ip'=>$result_bangding]);
						if( $res1 != count($result_bangding) ) {
							$transaction->rollBack();
							$arrReturn = [
								'status' => 0,
								'info' => '要绑定的IP状态更新失败'
							];
							return $this->renderJSON($arrReturn);
						}
					}
					#
					#同时修改自有机器的IP以及Vlan  待定
					#
				} else if( $servicerprovider == 1) {#供应商将					
					#检测完毕 就将新加的IP改为待定(将上次的和原始IP合并，在与新提交的IP对比)
					$bgIPArray = array_unique(array_merge($last_ip,$Original_ip));
					$new_bgIPArray = array_unique(array_merge($last_ip,$Original_ip,$ipArray2));					
					$result_insert = array_diff($new_bgIPArray,$bgIPArray); 	#新增IP入库（需要删除的等审核再删除）		
					if( !empty($result_insert) ) {
						##检测新增的IP 供应商的  要做判断，看是否可用			
						
						$CheckRes = DataHelper::detect_supplierip($result_insert);
						if( $CheckRes['status'] == 0 ) {
							$transaction->rollBack();
							return $this->renderJSON([
								'status' => 0,
								'info' => $CheckRes['info']
							]);
						}
						$insertRes = $SupplierIpModel->add_peration($result_insert, $MemberPdtRes['provider_id'], $MemberPdtRes['room_id'], '3');
						if( count($insertRes) != count($result_insert) ) {
							$transaction->rollBack();
							$arrReturn = [
							   'status' => 0,
							   'info' => '供应商IP入库失败'
							];
							return $this->renderJSON($arrReturn);
						}
					}
				}
				#增加子单
				#查询是否需要审核
				$typeInfo = $AfterorderSonlistTrialModel->find()->where(['sonlist_type' => '更换IP'])->one();
				
				$new_content = $request_content;
				$new_content['replace_front']['frontData'] = $new_content['frontData'];
				$new_content['replace_front']['afterData'] = $new_content['afterData'];
				
				$new_content['replace_after']['frontData'] = $new_content['frontData'];
				$new_content['replace_after']['afterData']['ip'] = $ipArray;
				$new_content['replace_after']['afterData']['ip2'] = $ipArray2;
				unset($new_content['frontData']);
				unset($new_content['afterData']);
				
				$time = time();
				
				#生成子单
				$AfterorderSonlistModel->ao_id = $ao_id;
				$AfterorderSonlistModel->ao_s_type = '更换IP';
				$AfterorderSonlistModel->ao_s_content = json_encode($new_content, JSON_UNESCAPED_UNICODE);
				$AfterorderSonlistModel->ao_s_trial_status = $typeInfo->sonlist_istrial == 'Y' ? '等待审核' : '无需审核';
				$AfterorderSonlistModel->ao_s_status = '未完成';
				$AfterorderSonlistModel->ao_s_request_time = $time;
				$AfterorderSonlistModel->ao_s_finish_time = null;
				if( !$AfterorderSonlistModel->insert() ) {
					$transaction->rollBack();
					$arrReturn = [
						'status'=>0,
						'info'=>'创建子工单时出现异常，请联系管理员'
					];
				} else {
					
					#创建子单审核通知
					TrailAndNotice::createSonTrail($ao_id, $AfterorderSonlistModel->ao_s_sonlist_id);
					
					$transaction->commit();
					$arrReturn = [
						'status'=>1,
						'info'=>'子单提交成功'
					];
				}
				return $this->renderJSON($arrReturn);
				
			} else {
				$arrReturn = [
					'status' => 0,
					'info' => '不支持的工作流类型'
				];
				return $this->renderJSON($arrReturn);
			}
			
		} else {
			#页面
			$ao_id = $this->get('ao_id');
			if(!$ao_id) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			
			$AfterOrderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
			if(in_array($AfterOrderInfo['ao_type'], ['已完成','已取消'])) {
				return $this->redirect(Url::to(['after-order/process-order']));
			}
			$request_content = json_decode($AfterOrderInfo['ao_request_content'], true);
			
			$PipelineListRes = $PipelineListModel->find()->where(['line_id' => $AfterOrderInfo['line_id']])->asArray()->one();
			$PipelineTypesRes = $PipelineTypesModel->find()->where(['line_type_id' => $PipelineListRes['line_type_id']])->asArray()->one();
			$line_type_name = $PipelineTypesRes['line_type_name'];
			
			if( $line_type_name == '更换IP') {
				$unionid = $request_content['unionid'];
				$servicerprovider = $request_content['servicerprovider'];
				$room_id = $request_content['room_id'];
				$ip = $request_content['afterData']['ip'];
				#IP分类
				$IpClassList = $PdtIpClassModel->find()->asArray()->all();
				#网段
				$IpNetworkList = $PdtIpNetworkModel->find()->where(['room_id' => $room_id])->asArray()->all();
				return $this->render('replace-ip', [
					'ao_id' => $ao_id,
					'IpClassList' => $IpClassList,
					'IpNetworkList' => $IpNetworkList,
					'servicerprovider' => $servicerprovider,
					'room_id' => $room_id,
					'ip' => $ip,
					
				]);
			}
			
			
			
		}
		
	}
	
	#重装系统 子单提交
	public function actionReloadSystem() {
		Yii::$app->request->isAjax || die('异常访问');
		
		$CloudSystemModel = new CloudSystemModel();
		$CloudSystemModelClassModel = new CloudSystemModelClass();
		$IdlePdtModel = new IdlePdt();
		$CabinetModel = new PdtCabinetManage();
		$ServerRecordModel = new ServerDorecord();
		$SwitchManageModel = new SwitchManage();
		$CustomPartitionSettingModel = new CustomPartitionSetting();
		
		$post = $this->post();
		
		#开启事务
        $transaction = \Yii::$app->db->beginTransaction();		
		
		$CloudSystemRes = $CloudSystemModel->find()->where(['cloud_system_id'=>$post['cloudsystem']])->asArray()->one(); 
		if( empty($CloudSystemRes)) {
			$arrReturn = ['status' => 0, 'info' => "未知的模板信息"];
			return $this->renderJSON($arrReturn);
		}
		$SystemModelClassRes = $CloudSystemModelClassModel->find()->where(['class_id' => $CloudSystemRes['class_id']])->asArray()->one();
		if( empty($SystemModelClassRes)) {
			$arrReturn = ['status' => 0, 'info' => "未知的模板分类"];
			return $this->renderJSON($arrReturn);		
		}
		
		#如果选择为自定义分区类
		if($SystemModelClassRes['custom_partition'] == 'Y') {
			$type = $post['partition_type'];
			$file_system_type = $post['file_system_type'];
			$swap_size = $post['swap_partition_size'];
			$root_size = $post['root_partition_size'];
			$home_size = $post['home_partition_size'];
			$www_size = $post['www_partition_size'];
			$data_size = $post['data_partition_size'];
			
			#判断参数
			if(!in_array($type, ['LVM', 'Stardard'])) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0, 'info' => "分区类型错误"];
				return $this->renderJSON($arrReturn);
			}
			
			if(!in_array($file_system_type, ['ext4', 'xfs'])) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0, 'info' => "文件系统类型错误"];
				return $this->renderJSON($arrReturn);
			}
			
			if(!$swap_size) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0, 'info' => "swap分区大小必须填写"];
				return $this->renderJSON($arrReturn);	
			} else {
				if(!preg_match("/^[1-9][0-9]*$/",$swap_size)) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info' => "swap分区大小填写有误"];
					return $this->renderJSON($arrReturn);
				} else {
					if( $swap_size < 2 || $swap_size > 32) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => "swap分区大小值范围在2-32之间"];
						return $this->renderJSON($arrReturn);
					}
				}
				$swap_size = strval($swap_size * 1024);
			}
			
			if(!$root_size) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0, 'info' => "根分区大小必须填写"];
				return $this->renderJSON($arrReturn);	
			} else {
				if($root_size == 'all') {
					$root_size = 'all';
				} else {
					if(!preg_match("/^[1-9][0-9]*$/",$swap_size)) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => "根分区大小填写有误"];
						return $this->renderJSON($arrReturn);
					}
					$root_size = strval($root_size * 1024);					
				}
			}
			
			if($home_size) {
				if($home_size == 'all') {
					$home_size = 'all';
				} else {
					if(!preg_match("/^[1-9][0-9]*$/",$home_size)) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => "Home分区大小填写有误"];
						return $this->renderJSON($arrReturn);
					}
					$home_size = strval($home_size * 1024);
				}
			} else {
				$home_size = '0';
			}			
			
			if($www_size) {
				if($www_size == 'all') {
					$www_size = 'all';
				} else {
					if(!preg_match("/^[1-9][0-9]*$/",$www_size)) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0,'info' => "WWW分区大小填写有误"];
						return $this->renderJSON($arrReturn);
					}
					$www_size = strval($www_size * 1024);
				}
			} else {
				$www_size = '0';
			}
			
			if($data_size) {
				if($data_size == 'all') {
					$data_size = 'all';
				} else {
					if(!preg_match("/^[1-9][0-9]*$/",$data_size)) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => "Data分区大小填写有误"];
						return $this->renderJSON($arrReturn);
					}
					$data_size = strval($data_size * 1024);
				}
			} else {
				$data_size = '0';
			}
			
			#boot  home  www  data中只能存在一个all
			$check_arr = [$root_size, $home_size, $www_size, $data_size];
			$value_num = array_count_values($check_arr);
			if($value_num['all'] > 1) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0, 'info' => "只能有一个参数值为all"];
				return $this->renderJSON($arrReturn);
			}
			
			#赋值到数组中			
			$custom_partition['type'] = $type;
			$custom_partition['file_system_type'] = $file_system_type;
			$custom_partition['swap_size'] = $swap_size;
			$custom_partition['root_size'] = $root_size;
			$custom_partition['home_size'] = $home_size;
			$custom_partition['www_size'] = $www_size;
			$custom_partition['data_size'] = $data_size;
		} else {
			$custom_partition = [];
		}
		
		$AfterorderListModel = new AfterorderList();
		$PipelineListModel = new PipelineList();
		$AfterorderSonlistModel = new AfterorderSonlist();
		
		$ao_id = $post['ao_id'];
		$AfterorderListRes = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
		if( empty($AfterorderListRes) ) {
			$arrReturn = ['status' => 0,'info' => "未知的工单"];
			return $this->renderJSON($arrReturn);
		}
		
		if($AfterorderListRes['ao_type'] != '未完成') {
			$arrReturn = ['status' => 0, 'info' => "工单已被关闭！请联系相应的发起人了解详细情况"];
			return $this->renderJSON($arrReturn);
		}
		
		$ao_request_content = json_decode($AfterorderListRes['ao_request_content'], true);
		
		#查询出工单对应的工作流
		$PipelineListRes = $PipelineListModel->find()->with('linetypes')->where(['line_id' => $AfterorderListRes['line_id']])->asArray()->one();
		
		$request_content = json_decode($PipelineListRes['event_request_content'], true);
		$line_type_name = $PipelineListRes['linetypes']['line_type_name'];
		
		if($line_type_name == '新购业务') {			
			$servicerprovider = $ao_request_content['preset']['servicerprovider'];
			$idle_id = $ao_request_content['preset']['idle_id'];
			
		} else if($line_type_name == '新增测试机') {
			$servicerprovider = $request_content['servicerprovider'];
			$idle_id = $request_content['idle_id'];

		} else {
			$arrReturn = ['status' => 0,'info' => "不支持的工作流类型"];
			return $this->renderJSON($arrReturn);
		}
		
		$IdlePdtRes = $IdlePdtModel->find()->With('switch')->where(['id' => $idle_id])->asArray()->one();
		
		if( empty($IdlePdtRes)) {
			$transaction->rollBack();
			$arrReturn = ['status' => 0, 'info' => "未知的机器"];
			return $this->renderJSON($arrReturn);		
		}
		
		$Sn = $IdlePdtRes['sn_code'];
		if( $Sn == "" || $Sn == null) {
			$transaction->rollBack();
			$arrReturn = ['status' => 0, 'info' => "机器SN码为空"];
			return $this->renderJSON($arrReturn);
		}
		#获取机柜信息
		$cabinet_id = $IdlePdtRes['cabinet_id'];
		if( $cabinet_id == "" || $cabinet_id == null){
			$transaction->rollBack();
			$arrReturn = ['status' => 0,'info' => "该机器未选择机柜信息"];
			return $this->renderJSON($arrReturn);	
		}
		$CabinetRes = $CabinetModel->find()->where(['id'=>$cabinet_id])->asArray()->one();
		if( empty($CabinetRes) ){
			$transaction->rollBack();
			$arrReturn = ['status' => 0,'info' => "未知的机柜信息"];
			return $this->renderJSON($arrReturn);	
		} else {
			$cloudboot_api = $CabinetRes['cloudboot_api'];
			$cloudboot_key = $CabinetRes['cloudboot_key'];
			if( $cloudboot_api == "" || $cloudboot_api == null){
				$transaction->rollBack();
				$arrReturn = ['status'=>0, 'info'=>'对应机柜未填写云装机API地址'];
				return $this->renderJSON($arrReturn);          
			}
			if( $cloudboot_key == "" || $cloudboot_key == null){
				$transaction->rollBack();
				$arrReturn = ['status'=>0, 'info'=>'对应机柜未填写云装机密钥'];
				return $this->renderJSON($arrReturn);          
			}
		}
		#IPMI地址
		$ipmi_ip = $IdlePdtRes['ipmi_ip'];
		$ipmi_name = $IdlePdtRes['ipmi_name'];
		$ipmi_pwd = $IdlePdtRes['ipmi_pwd']; 		#echo $ipmi_name;echo $ipmi_pwd;exit;
		if( $ipmi_ip == "" || $ipmi_ip == null ) {
			$transaction->rollBack();
			$arrReturn = ['status'=>0, 'info'=>'该自有服务器未填写IPMI IP地址'];
            return $this->renderJSON($arrReturn);
		} else {
			if(!filter_var($ipmi_ip, FILTER_VALIDATE_IP)) {
				$transaction->rollBack();
				$arrReturn = ['status'=>0, 'info'=> $ipmi_ip.'为不合法的IP地址'];
				return $this->renderJSON($arrReturn);
			}
		}		
		if( $ipmi_name == "" || $ipmi_name == null || $ipmi_pwd == "" || $ipmi_pwd == null){
			$transaction->rollBack();
			$arrReturn = ['status'=>0, 'info'=> 'IPMI 账户名或者密码未设置'];
			return $this->renderJSON($arrReturn);
		}
				
		#判定子单是否需要审核
		$AfterorderSonlistTrialModel = new AfterorderSonlistTrial();
		$AfterorderSonlistTrialRes = $AfterorderSonlistTrialModel->find()->where(['sonlist_type' => '重装系统'])->asArray()->one();
		$sonlist_istrial = $AfterorderSonlistTrialRes['sonlist_istrial'];
		
		#集合数组
		$custom_partition['sn_code'] = $Sn;
		
		$data['idle_id'] = $idle_id;
		$data['ipmi_ip'] = $ipmi_ip;
		$data['sn_code'] = $Sn;
		$data['class_category'] = $SystemModelClassRes['class_category'];		
		$data['system_id'] = $CloudSystemRes['cloud_system_id'];
		$data['cloud_system_name'] = $CloudSystemRes['cloud_system_name'];
		$data['class_id'] = $SystemModelClassRes['class_id'];
		$data['class_name'] = $SystemModelClassRes['class_name'];
		$data['custom_partition'] = $custom_partition;

		#生成子单
		$AfterorderSonlistModel->ao_id = $ao_id;
		$AfterorderSonlistModel->ao_s_type = '重装系统';
		$AfterorderSonlistModel->ao_s_content = json_encode($data, JSON_UNESCAPED_UNICODE);
		$AfterorderSonlistModel->ao_s_trial_status = $sonlist_istrial == 'Y' ? '等待审核' : '无需审核';
		$AfterorderSonlistModel->ao_s_status = '未完成';
		$AfterorderSonlistModel->ao_s_request_time = time();
		$AfterorderSonlistModel->ao_s_finish_time = null;
		
		if( $AfterorderSonlistModel->insert() ) {
			$transaction->commit();
			$arrReturn = ['status' => 1, 'info'=> '提交子单成功'];		
		} else {
			$transaction->rollBack();
			$arrReturn = ['status'=>0, 'info'=> '加入申请失败，请稍后重试'];		
		}
		
		return $this->renderJSON($arrReturn);
	}
	
	#审核子单
	public function actionTrailSonOrder() {
		Yii::$app->request->isAjax || die('error');
		
		$trailSonid = $this->post('trail_id');
		$trailResult = $this->post('trail_result');
		$trailRemark = $this->post('trail_remark');
		
		$admin_id = $this->getAdminInfo('admin_id');
		$admin_name = $this->getAdminInfo('uname');
		
		if(!$trailSonid || !$trailResult || !in_array($trailResult, ['通过', '拒绝'])) {
			$arrReturn = [
				'status' => 0,
				'info' => '参数异常'
			];
			return $this->renderJSON($arrReturn);
		}
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$AfterorderSonlistModel = new AfterorderSonlist();
		$AfterorderListModel = new AfterorderList();
		
		$soninfo = $AfterorderSonlistModel->find()->where(['ao_s_sonlist_id' => $trailSonid])->one();

		$afterorder_info = $AfterorderListModel->find()->where(['ao_id' => $soninfo->ao_id])->one();
		
		if($afterorder_info->ao_type != '未完成') {
			
			$arrReturn = ['status' => 0, 'info' => "工单已被关闭！请联系相应的发起人了解详细情况"];
			return $this->renderJSON($arrReturn);
		}
		
		#审核了不论通过与否都应当取消审核的通知
		TrailAndNotice::confirmSonTrail($afterorder_info->line_id, $soninfo->ao_id, $trailSonid);
		#
		$backRes = AfterOrderSon::SubAudit($trailSonid, $trailResult); #print_r($backRes);exit;
		if( !$backRes['status'] ) {
			$arrReturn = ['status' => 0,'info' => $backRes['info']];
			return $this->renderJSON($arrReturn);
		}

		$soninfo->ao_s_trial_admin_id = $admin_id;
		$soninfo->ao_s_trial_admin_name = $admin_name;
		$soninfo->ao_s_trial_remark = $trailRemark;
		$soninfo->ao_s_trial_status = $trailResult;
		$soninfo->ao_s_trial_time = time();
		
		if($trailResult == '拒绝') {
			$soninfo->ao_s_status = '已取消';
		}
		
		if($soninfo->save()) {
			$transaction->commit();
			$arrReturn = [
				'status'=>1,
				'info'=>'审核完成'
			];
			return $this->renderJSON($arrReturn);
		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'审核出现异常，请联系管理员'
			];
			return $this->renderJSON($arrReturn);
		}
	}
	
	#完成/ 取消 子单
	public function actionSonOrderOperation() {
		Yii::$app->request->isAjax || die('error');
		
		$trailSonid = $this->post('trail_id');
		$operation_result = $this->post('operation_result');
		
		$admin_id = $this->getAdminInfo('admin_id');
		$admin_name = $this->getAdminInfo('uname');

		if(!$trailSonid || !$operation_result || !in_array($operation_result, ['完成', '取消'])) {
			$arrReturn = ['status' => 0, 'info' => '参数异常'];
			return $this->renderJSON($arrReturn);
		}
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$AfterorderSonlistModel = new AfterorderSonlist();
		$AfterorderListModel = new AfterorderList();

		$soninfo = $AfterorderSonlistModel->find()->where(['ao_s_sonlist_id' => $trailSonid])->one();
		
		$afterorder_info = $AfterorderListModel->find()->where(['ao_id' => $soninfo->ao_id])->one();
		
		if($afterorder_info->ao_type != '未完成') {
			$arrReturn = ['status' => 0, 'info' => "工单已被关闭！请联系相应的发起人了解详细情况"];
			return $this->renderJSON($arrReturn);
		}
		
		$backRes = AfterOrderSon::SubOperation($trailSonid, $operation_result);
		if( !$backRes['status'] ) {
			$arrReturn = ['status' => 0,'info' => $backRes['info']];
			return $this->renderJSON($arrReturn);
		}

		$soninfo->ao_s_finish_time = time(); #结束时间
		
		if($operation_result == '取消') {
			
			#审核了不论通过与否都应当取消审核的通知
			$res = TrailAndNotice::confirmSonTrail($afterorder_info->line_id, $soninfo->ao_id, $trailSonid);
			
			$soninfo->ao_s_status = '已取消';
			$message = '子工单已取消';
		} else {
			$soninfo->ao_s_status = '已完成';
			$message = '子工单已完成';
		}
		
		if($soninfo->save()) {
			$transaction->commit();
			$arrReturn = [
				'status'=>1,
				'info'=>$message
			];
			return $this->renderJSON($arrReturn);
		} else {
			$transaction->rollBack();
			$arrReturn = [
				'status'=>0,
				'info'=>'子工单操作出现异常'
			];
			return $this->renderJSON($arrReturn);
		}		
	}

	#重装系统 立即重装/ 取消安装 (单独一个方法)	
	public function actionReloadsystemOperation() {
		
		$trailSonid = $this->post('trail_id');
		$operation_result = $this->post('operation_result');
		
		$admin_id = $this->getAdminInfo('admin_id');
		$admin_name = $this->getAdminInfo('uname');

		if(!$trailSonid || !$operation_result || !in_array($operation_result, ['确认', '取消'])) {
			$arrReturn = ['status' => 0, 'info' => '参数异常'];
			return $this->renderJSON($arrReturn);
		}
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$AfterorderSonlistModel = new AfterorderSonlist();
		$AfterorderListModel = new AfterorderList();
		#实例化  重装相关对象
		$IdlePdtModel = new IdlePdt();
		$CabinetModel = new PdtCabinetManage();
		$SwitchManageModel = new SwitchManage();
		$ServerRecordModel = new ServerDorecord();
		$CloudSystemModel = new CloudSystemModel();
		$CloudSystemModelClassModel = new CloudSystemModelClass();
		$PdtIpModel = new PdtIp();
		$SwitchLineModel = new SwitchLine();
		$CustomPartitionSettingModel = new CustomPartitionSetting();
		#查询出子单
		$soninfo = $AfterorderSonlistModel->find()->where(['ao_s_sonlist_id' => $trailSonid])->one();
		#查询出主单
		$afterorder_info = $AfterorderListModel->find()->where(['ao_id' => $soninfo['ao_id']])->one();
		
		if($afterorder_info->ao_type != '未完成') {
			$arrReturn = ['status' => 0, 'info' => "工单已被关闭！请联系相应的发起人了解详细情况"];
			return $this->renderJSON($arrReturn);
		}
		
		$ao_s_content = json_decode($soninfo['ao_s_content'], true);
		$time = time();
		$system_id = $ao_s_content['system_id'];
		$idle_id = $ao_s_content['idle_id'];
		
		if( $operation_result == '确认') {		
			
			#获取系统模板
			$CloudSystemRes = $CloudSystemModel->find()->where(['cloud_system_id' => $system_id])->asArray()->one(); 
			if( empty($CloudSystemRes)) {
				$arrReturn = ['status' => 0, 'info' => "未知的模板信息"];
				return $this->renderJSON($arrReturn);		
			}
			#获取系统模板分类
			$CloudSystemClassRes = $CloudSystemModelClassModel->find()->where(['class_id' => $CloudSystemRes['class_id']])->asArray()->one(); 
			if( empty($CloudSystemClassRes)) {
				$arrReturn = ['status' => 0, 'info' => "未知的模板分类信息"];
				return $this->renderJSON($arrReturn);		
			}
						
			#获取自有机器信息
			$IdlePdtRes = $IdlePdtModel->find()->With('switch')->where(['id' => $idle_id])->asArray()->one();
			if( empty($IdlePdtRes)) {
				$arrReturn = ['status' => 0, 'info' => "未知的机器"];
				return $this->renderJSON($arrReturn);		
			}
			
			#交换机信息
			$SwitchRes = $SwitchManageModel->find()->where(['id'=>$IdlePdtRes['switch_location']])->asArray()->one();
			
			if( $CloudSystemClassRes['class_category'] == '重装系统') {
				#获取IP
				$IpList = json_decode($IdlePdtRes['ip2'], true);
				if( !empty($IpList) ) {
					$PdtIpAll = $PdtIpModel->find()->select('room_id, line_type_id,vlan')->where(['in','ip',$IpList])->asArray()->all();
					$temp = array_column($PdtIpAll, 'room_id');				
					$temp = array_values(array_unique($temp)); #去掉重复的字符串,也就是重复的一维数组
					
					if( count($temp) > 1 ) {
						$arrReturn = ['status' => 0, 'info' => 'IP中有属于其他机房的IP地址'];
						return $this->renderJSON($arrReturn);
					} else if( count($temp) == 1) {
						if($temp[0] != $IdlePdtRes['room_id'] ){
							$arrReturn = ['status'=>0, 'info'=> 'IP所属机房与自有机器所属机房不一致'];
							return $this->renderJSON($arrReturn);
						}
					}
					
					$line_typeList = array_unique(array_column($PdtIpAll, 'line_type_id'));		

					if( count($line_typeList) > 1 ) {
						$arrReturn = ['status' => 0, 'info' => 'IP集合中有多种线路类型'];
						return $this->renderJSON($arrReturn);
					}
					
					#获取机器对应交换机的线路信息
					$SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtRes['switch_location'], 'line_type_id' => $line_typeList[0]])->asArray()->one();
					if( empty($SwitchLineList) ) {
						$arrReturn = ['status' => 0, 'info' => '机器对应的交换机线路中未有IP的线路类型'];
						return $this->renderJSON($arrReturn);
					}
					#如果对应的交换机线路的Vlan 与机器的Vlan 不一致，将更新vlan
					if( $SwitchLineList['switch_vlan'] != $IdlePdtRes['vlan']) {
						$vlan = trim($SwitchLineList['switch_vlan']);
						$IdlePdtQuery = $IdlePdtModel->findone($idle_id);
						$IdlePdtQuery->vlan = $vlan;
						$IdlePdtQuery->update();
					} else {
						$vlan = trim($SwitchLineList['switch_vlan']);
					}
				} else {
					$vlan = '';
				}
				
				#接口修改交换机的VLan			
				if( $vlan && is_numeric($vlan) && $vlan >= 2 && $vlan <= 4096) {

					$SwitchAction = 1;
					$Svar = $vlan;
					$mac = $IdlePdtRes['mac_addr'];
					if( $mac == '' || $mac == null) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => "自有机器中Mac地址为空"];
						return $this->renderJSON($arrReturn);
					}
					#判定交换机是否开启了源防护
					if( $SwitchRes['source_check'] == 'Y') {
						$Svar_str = [];
						#如果IP大于等于50
						if( count($IpList) <= 50 ) {
							if(count($IpList) <= 10) {
								$ips = implode(' ', $IpList);
								$Svar_str[] = "ip-address ".$ips." mac-address ".$mac;
							} else {
								while(true) {
									$ips_array = [];
									
									for($i=0; $i<count($IpList); $i++) {
										$ips_array[] = $IpList[$i];
										if(count($ips_array) == 10) {
											#每当满10条ip后执行操作
											$ips = implode(' ', $ips_array);
											$Svar_str[] = "ip-address ".$ips." mac-address ".$mac;
											$ips_array = [];
										}

										if($i + 1 == count($IpList)) {
											#当执行到最后一条的时候，执行操作
											$ips = implode(' ', $ips_array);
											$Svar_str[] = "ip-address ".$ips." mac-address ".$mac;
											break;
										}						
									}
									break;
								}
							}
						} else {
							#如果IP数大于50
							$IpList = json_decode($IdlePdtRes['ip'], true);
							#将IP段和单IP分出来
							while(true) {
								$ips_array = []; #IP段集合
								$ips_array2 = []; #单IP集合
								for($i=0; $i<count($IpList); $i++) {
									if( strpos($IpList[$i], '-') !== false) {
										#将IP段拆以 .  拆分开，然后将最后一段以 - 分开
										$ip_mods = explode('.', trim($IpList[$i]));
										$lastip_mod = explode('-', trim($ip_mods[3]));									
										$IPArr['start'] = trim($ip_mods[0]).'.'.trim($ip_mods[1]).'.'.trim($ip_mods[2]).'.'.$lastip_mod[0];
										$IPArr['end'] = trim($ip_mods[0]).'.'.trim($ip_mods[1]).'.'.trim($ip_mods[2]).'.'.$lastip_mod[1];
										
										$command = $IPArr['start']." to ".$IPArr['end'];
										$ips_array[] = $command;
									} else {
										$ips_array2[] = $IpList[$i];
									}
								}
								break;
							}

							#print_r($ips_array);print_r($ips_array2);exit;
							if( !empty($ips_array) ) {
								$data = [];
								for($i=0; $i<count($ips_array); $i++) {
									$data[] = $ips_array[$i];
									if( count($data) == 4) {
										#每当满4条ip段后
										$ips = implode(' ', $data);
										$Svar_str[] = "ip-address ".$ips." mac-address ".$mac;
										$data = [];
									}
									if($i + 1 == count($ips_array)) {
										#当执行到最后一条的时候
										$ips = implode(' ', $data);
										$Svar_str[] = "ip-address ".$ips." mac-address ".$mac;
										break;
									}
								}
							}
							#print_r($ips_array2);exit;
							if( !empty($ips_array2) ){
								$data = [];
								for($i=0; $i<count($ips_array2); $i++) {
									$data[] = $ips_array2[$i];
									if( count($data) == 10) {
										#每当满4条ip段后
										$ips = implode(' ', $data);
										$Svar_str[] = "ip-address ".$ips." mac-address ".$mac;
										$data = [];
									}
									if($i + 1 == count($ips_array2)) {		
										#当执行到最后一条的时候
										$ips = implode(' ', $data);
										$Svar_str[] = "ip-address ".$ips." mac-address ".$mac;
										break;
									}
								}
							}
						}
						#print_r($Svar_str);exit;
						if( !empty($Svar_str) ) {
							if( $Svar_str[0] != '' && $Svar_str[0] != null && isset($Svar_str[0]) ) {
								$Svar2 = $Svar_str[0];
							} else {
								$Svar2 = null;
							}
							if( $Svar_str[1] != '' && $Svar_str[1] != null && isset($Svar_str[1]) ) {
								$Svar3 = $Svar_str[1];
							} else {
								$Svar3 = null;
							}
							if( $Svar_str[2] != '' && $Svar_str[2] != null && isset($Svar_str[2]) ) {
								$Svar4 = $Svar_str[2];
							} else {
								$Svar4 = null;
							}
							if( $Svar_str[3] != '' && $Svar_str[3] != null && isset($Svar_str[3]) ) {
								$Svar5 = $Svar_str[3];
							} else {
								$Svar5 = null;
							}
							if( $Svar_str[4] != '' && $Svar_str[4] != null && isset($Svar_str[4]) ) {
								$Svar6 = $Svar_str[4];
							} else {
								$Svar6 = null;
							}
						} else {
							$Svar2 = null;
							$Svar3 = null;
							$Svar4 = null;
							$Svar5 = null;
							$Svar6 = null;
						}
						
						
					} else {
						$Svar2 = null;
						$Svar3 = null;
						$Svar4 = null;
						$Svar5 = null;
						$Svar6 = null;
					}
					
				} else {
					$SwitchAction = 0;
					$Svar = null;
					$Svar2 = null;
					$Svar3 = null;
					$Svar4 = null;
					$Svar5 = null;
					$Svar6 = null;
				}
			} else {
				$SwitchAction = 0;
				$Svar = null;
				$Svar2 = null;
				$Svar3 = null;
				$Svar4 = null;
				$Svar5 = null;
				$Svar6 = null;
			}
			
			$Sn = $IdlePdtRes['sn_code'];
			if( $Sn == "" || $Sn == null){
				$arrReturn = ['status' => 0, 'info' => 'SN码为空'];
				return $this->renderJSON($arrReturn);
			}
			
			#获取机柜信息
			$cabinet_id = $IdlePdtRes['cabinet_id'];
			if( $cabinet_id == "" || $cabinet_id == null) {
				$arrReturn = ['status' => 0, 'info' => '该机器未选择机柜信息'];
				return $this->renderJSON($arrReturn);	
			} 
			$CabinetRes = $CabinetModel->find()->where(['id'=>$cabinet_id])->asArray()->one();
			if( empty($CabinetRes) ) {
				$arrReturn = ['status' => 0, 'info' => '未知的机柜信息'];
				return $this->renderJSON($arrReturn);	
			}
			$cloudboot_api = $CabinetRes['cloudboot_api'];
			$cloudboot_key = $CabinetRes['cloudboot_key'];
			if( $cloudboot_api == "" || $cloudboot_api == null){
				$arrReturn = ['status'=>0, 'info'=>'对应机柜未填写云装机API地址'];
				return $this->renderJSON($arrReturn);          
			}
			if( $cloudboot_key == "" || $cloudboot_key == null){
				$arrReturn = ['status'=>0, 'info'=>'对应机柜未填写云装机密钥'];
				return $this->renderJSON($arrReturn);          
			}#机柜信息END
			
			#
			$ipmi_ip = $IdlePdtRes['ipmi_ip'];
			$ipmi_name = $IdlePdtRes['ipmi_name'];
			$ipmi_pwd = $IdlePdtRes['ipmi_pwd']; 
			if( $ipmi_ip == "" || $ipmi_ip == null ) {
				$arrReturn = ['status'=>0, 'info'=>'该自有服务器未填写IPMI IP地址'];
				return $this->renderJSON($arrReturn);
			}
			if(!filter_var($ipmi_ip, FILTER_VALIDATE_IP)) {
				$arrReturn = ['status'=>0, 'info'=> $ipmi_ip.'为不合法的IP地址'];
				return $this->renderJSON($arrReturn);
			}					
			if( $ipmi_name == "" || $ipmi_name == null || $ipmi_pwd == "" || $ipmi_pwd == null){
				$arrReturn = ['status'=>0, 'info'=> 'IPMI 账户名或者密码未设置'];
				return $this->renderJSON($arrReturn);
			}

			#获取交换机IP和端口			
			$SwitchIp = $SwitchRes['ip'];
			$SwitchPort = $IdlePdtRes['switch_port'];
			#
			$SwitchSshPort = $SwitchRes['login_port'];
			$SwitchUsername = $SwitchRes['login_user'];
			$SwitchPassword = $SwitchRes['login_password'];
			
			#获取重装信息
			$Hostname = $IdlePdtRes['ipmi_ip'];
			$Ip = $IdlePdtRes['ipmi_ip'];
			$NetworkID = substr(trim($Ip),0,strrpos(trim($Ip),'.')); 
			$NetworkID = str_replace('.','',$NetworkID);   				##echo $NetworkID;die;		
			$SystemID = $CloudSystemRes['cloud_system_id'];
			$OsID = $SystemID;
			#记录操作日志记录		。将之前的记录改为取消			
			$recordRes = $ServerRecordModel->find()->where(['do_idleid' => $IdlePdtRes['id'], 'do_status' => '处理中'])->one(); 
			if( !empty($recordRes) ) {
				$recordRes->do_status = '取消处理';
				$result = $recordRes->save();
				if( !$result ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info' => '机器之前重装系统记录取消更新失败'];
					return $this->renderJSON($arrReturn);
				}
			}
			
			#如果选择为自定义分区类，将分区设置新增到数据库中
			if($CloudSystemClassRes['custom_partition'] == 'Y') {
				$custom_partition = $ao_s_content['custom_partition'];
				$PartitionSettingQuery = $CustomPartitionSettingModel->find()->where(['sn_code'=>$Sn])->one();
				if( $PartitionSettingQuery) {	#更新					
					$PartitionSettingQuery->type = $custom_partition['type'];
					$PartitionSettingQuery->file_system_type = $custom_partition['file_system_type'];
					$PartitionSettingQuery->swap_size = $custom_partition['swap_size'];
					$PartitionSettingQuery->root_size = $custom_partition['root_size'];
					$PartitionSettingQuery->home_size = $custom_partition['home_size'];
					$PartitionSettingQuery->www_size = $custom_partition['www_size'];				
					$PartitionSettingQuery->data_size = $custom_partition['data_size'];				
					$PartitionSettingQuery->update_time = time();
					
					$updatePartitionSetting = $PartitionSettingQuery->update();
					if( !$updatePartitionSetting) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info'=> '更新自定义分区设置信息失败'];
						return $this->renderJSON($arrReturn);
					}
				} else {#新增					
					$CustomPartitionSettingModel->sn_code = $Sn;
					$CustomPartitionSettingModel->type = $custom_partition['type'];
					$CustomPartitionSettingModel->file_system_type = $custom_partition['file_system_type'];
					$CustomPartitionSettingModel->swap_size = $custom_partition['swap_size'];
					$CustomPartitionSettingModel->root_size = $custom_partition['root_size'];
					$CustomPartitionSettingModel->home_size = $custom_partition['home_size'];
					$CustomPartitionSettingModel->www_size = $custom_partition['www_size'];				
					$CustomPartitionSettingModel->data_size = $custom_partition['data_size'];				
					$CustomPartitionSettingModel->create_time = time();
					
					$insertPartitionSetting = $CustomPartitionSettingModel->insert();
					if( !$insertPartitionSetting) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info'=> '新增自定义分区设置信息失败'];
						return $this->renderJSON($arrReturn);
					}
				}			
			}
			
			#重新记录此次记录
			$ServerRecordModel->do_adminid = $this->getAdminInfo('admin_id');
			$ServerRecordModel->do_idleid = $IdlePdtRes['id'];
			$ServerRecordModel->do_sn = $IdlePdtRes['sn_code'];
			$ServerRecordModel->do_operate = $CloudSystemClassRes['class_category'];
			$ServerRecordModel->do_explain = $CloudSystemRes['cloud_system_name'];
			$ServerRecordModel->do_status = '处理中';
			$do_response['Title'] = $CloudSystemClassRes['class_category']."信息获取完毕，开始请求接口";
			$ServerRecordModel->do_response = json_encode($do_response);
			$ServerRecordModel->do_loading = 8;
			$ServerRecordModel->do_create_time = time();
			$ServerRecordModel->do_submit_mode = 'work_order';
			$ServerRecordModel->do_submit_id = $trailSonid;
			
			$insertRes = $ServerRecordModel->insert();
			if( !$insertRes ) {
				$transaction->rollBack();
				$arrReturn = ['status' => 0, 'info' => "操作记录新增失败"];
				return $this->renderJSON($arrReturn);
			}			
			$server_record_id = $ServerRecordModel->rid;
			
			//请求重装接口
			$ReloadSystemRequestRes = CloudBootApi::osinstall_batchAdd($cloudboot_api,$Sn,$Hostname,$Ip,$NetworkID,$OsID,$SystemID,$ipmi_name,$ipmi_pwd,$SwitchIp,$SwitchSshPort,$SwitchUsername,$SwitchPassword,$SwitchPort,$cloudboot_key,$SwitchAction,$Svar,$Svar2,$Svar3,$Svar4,$Svar5,$Svar6);//print_r($ReloadSystemRequestRes);die;
				
			if( $ReloadSystemRequestRes['Status'] == "success") { #发送成功
				#更新记录
				$ServerRecordQuery = $ServerRecordModel->find()->where(['do_idleid' => $IdlePdtRes['id'], 'do_status' => '处理中'])->one();
				$do_response['Title'] = $CloudSystemClassRes['class_category']."请求返回成功，开始执行";
				$ServerRecordQuery->do_response = json_encode($do_response);
				$ServerRecordQuery->do_loading = 10;
				$ServerRecordQuery->do_doing_time = time();
				$Res = $ServerRecordQuery->save();
				
				$ao_s_content['submit_reload'] = 1;
				$ao_s_content['server_record_id'] = $server_record_id;				
				
				$soninfo['ao_s_content'] = json_encode($ao_s_content, JSON_UNESCAPED_UNICODE);
				if( !$soninfo->save() ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0,'info' => "子单信息更新失败"];
				}
				if( $Res ) {
					$transaction->commit();
					$arrReturn = ['status' => 1, 'info' => "提交成功，系统即将开始执行命令"];
				} else {
					$transaction->rollBack();
					$arrReturn = ['status' => 0,'info' => "记录更新失败"];
				}
				
			} else {
				$transaction->rollBack();
				if( $ReloadSystemRequestRes['Message'] ) {
					$message = $ReloadSystemRequestRes['Message'];
				} else {
					$message = '提交安装失败';
				}
				$arrReturn = ['status' => 0, 'info' => $message];
			}
			return $this->renderJSON($arrReturn);			
			
		} else if($operation_result == '取消') {
			
			$submit_reload = $ao_s_content['submit_reload'];
			$server_record_id = $ao_s_content['server_record_id'];
			
			if( isset($submit_reload) && $submit_reload == 1 ) {
				$IdlePdtRes = $IdlePdtModel->find()->With('switch')->With('pdtcabinet')->where(['id'=>$idle_id])->asArray()->one();	
				if( empty($IdlePdtRes) ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info' => "未知的机器"];
					return $this->renderJSON($arrReturn);		
				}
				
				$Sn = $IdlePdtRes['sn_code'];
				
				#获取记录操作日志记录
				$recordQuery = $ServerRecordModel->find()->where(['do_idleid' => $idle_id, 'rid' => $server_record_id])->one();
				if( !empty($recordQuery) ) {
					if( in_array($recordQuery->do_status, ['处理成功', '处理失败', '取消处理']) ) {
					 	$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '无法取消，因为指令已执行完毕'];
						return $this->renderJSON($arrReturn);		
					}
					
					$recordQuery->do_status = '取消处理';
					if( !$recordQuery->save() ) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => "机器安装记录更新失败"];
						return $this->renderJSON($arrReturn);	
					}
					
					#更新子单信息
					$ao_s_content['submit_reload'] = 0;
					$soninfo['ao_s_content'] = json_encode($ao_s_content, JSON_UNESCAPED_UNICODE);
					if( !$soninfo->save()) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0,'info' => "子单信息更新失败"];
						return $this->renderJSON($arrReturn);	
					}
					
					$cloudboot_api = $IdlePdtRes['pdtcabinet'][0]['cloudboot_api'];
					$cloudboot_key = $IdlePdtRes['pdtcabinet'][0]['cloudboot_key'];
					
					#请求取消重装接口 
					$CancelRes = CloudBootApi::osinstall_cancelReload($cloudboot_api,$Sn,$cloudboot_key);		#print_r($CancelRes);die;
					
					if( $CancelRes['Status'] == 'success') {						
						$transaction->commit();
						$arrReturn = ['status'=>1, 'info'=>'取消成功'];	
					} else {
						$transaction->rollBack();
						$arrReturn = ['status'=>0, 'info'=> $CancelRes['Message']];		
					}
					return $this->renderJSON($arrReturn);	
				} else {
					$transaction->rollBack();
					$arrReturn = ['status'=>0, 'info'=> '记录异常'];		
				}
			} else {
				#未提交重装指令
				$transaction->rollBack();
				$arrReturn = ['status'=>0, 'info'=> '未提交过安装'];		
			}					
				
			return $this->renderJSON($arrReturn);
			
		} else {
			$arrReturn = ['status' => 0, 'info' => '异常操作提交'];
			
		}
		return $this->renderJSON($arrReturn);
	}
	
	#更换机器，子单 互换IP
	public function actionSonExchangedip() {
		$AfterorderListModel = new AfterorderList();
		$AfterorderSonlistModel = new AfterorderSonlist();
		$PipelineTypesModel = new PipelineTypes();
		$PipelineListModel = new PipelineList();
		$PayorderDetailModel = new PayorderDetail();
		$UserAdminModel = new UserAdmin();
		$IdlePdtModel = new IdlePdt();
		$PdtIpModel = new PdtIp();
		$SwitchLineModel = new SwitchLine();
		$MemberPdtModel = new MemberPdt();
		$TestServerModel = new  TestServer();
		
		$post = $this->post();
		$ao_sonid = $post['ao_sonid'];
		
		$AfterorderSonQuery = $AfterorderSonlistModel->find()->where(['ao_s_sonlist_id' => $ao_sonid])->one();
		if( empty($AfterorderSonQuery) ) {
			$arrReturn = ['status' => 0, 'info' => "未知的子单"];
			return $this->renderJSON($arrReturn);
		}
		if($AfterorderSonQuery->ao_s_status != '未完成') {
			$arrReturn = ['status' => 0, 'info' => "子单状态异常"];
			return $this->renderJSON($arrReturn);
		}
		
		$AfterOrderQuery = $AfterorderListModel->find()->where(["ao_id" => $AfterorderSonQuery->ao_id])->one();
		
		if( empty($AfterOrderQuery) ) {
			$arrReturn = ['status' => 0, 'info' => "未知的工单"];
			return $this->renderJSON($arrReturn);
		}
		if($AfterOrderQuery->ao_type != '未完成') {
			$arrReturn = ['status' => 0, 'info' => "工单状态异常"];
			return $this->renderJSON($arrReturn);
		}
		
		#开启事务
		$transaction = Yii::$app->db->beginTransaction();
		
		$ao_s_content = json_decode($AfterorderSonQuery->ao_s_content, true); #子单内容		
		
		$use_original_ip = $ao_s_content['use_original_ip'];
		$is_exchangedip = $ao_s_content['is_exchangedip'];
		
		$time = time();
		$replace_front = $ao_s_content['replace_front'];
		$replace_after = $ao_s_content['replace_after'];
		$unionid = $ao_s_content['unionid'];		
			
		if( $use_original_ip != 1){
			$arrReturn = ['status' => 0, 'info' => "要求并未使用原IP"];
			return $this->renderJSON($arrReturn);
		}
		
		if( $is_exchangedip == 1) {
			$arrReturn = ['status' => 0, 'info' => "已经提交过一次对换IP"];
			return $this->renderJSON($arrReturn);
		}
		
		$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $unionid])->asArray()->one();
		
		#必须更换前后都为自有，才会有这个操作
		if( $replace_front['servicerprovider'] == $replace_after['servicerprovider'] && $replace_after['servicerprovider'] == 0) {
			
			if( !isset($replace_after['idle_id']) ||  $replace_after['idle_id'] == '' || $replace_after['idle_id'] == null) {
				$arrReturn = ['status' => 0, 'info' => '未知自有机器'];					
				return $this->renderJSON($arrReturn);
			}
			
			$front_IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $replace_front['idle_id']])->one();#更换前的自有机器信息对象
			$after_IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $replace_after['idle_id']])->one();#更换后的自有机器信息对象
			
			#Start IP线路验证
			#验证更换后的机器配置的IP是否与之前的机器匹配
			if( !empty(json_decode($after_IdlePdtQuery->ip2, true)) ) {
				$PdtIpAll1 = $PdtIpModel->find()->select('room_id, line_type_id, vlan')->where(['in', 'ip', json_decode($after_IdlePdtQuery->ip2, true)])->asArray()->all();					
				$temp1 = array_column($PdtIpAll1, 'room_id');					
				$temp1 = array_unique($temp1);
				$temp1 =  array_values($temp1);
				if(count($temp1) > 1 ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info' => '更换后的自有机器原绑定的IP存在多机房'];
					return $this->renderJSON($arrReturn);
				} else if( count($temp1) == 1) {
					if($temp1[0] != $front_IdlePdtQuery->room_id ){
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '更换后的自有机器原绑定的IP所属机房与更换前的自有机器绑定IP所属机房不一致'];
						return $this->renderJSON($arrReturn);
					}
				}
				$line_typeList1 = array_column($PdtIpAll1, 'line_type_id');					
				$line_typeList1 = array_unique($line_typeList1);
				if( count($line_typeList1) > 1 ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info' => '所选IP与其他IP的线路类型不匹配'];
					return $this->renderJSON($arrReturn);
				}
				#获取机器对应交换机的线路信息
				$SwitchLineList1 = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $front_IdlePdtQuery->switch_location, 'line_type_id' => $line_typeList1[0]])->asArray()->one();
				if( empty($SwitchLineList1) ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info' => '更换前机器对应的交换机线路中未有更换后机器IP的线路类型'];
					return $this->renderJSON($arrReturn);
				}
			}
			
			#第二个验证
			if( !empty(json_decode($front_IdlePdtQuery->ip2, true)) ) {
				$PdtIpAll2 = $PdtIpModel->find()->select('room_id, line_type_id, vlan')->where(['in', 'ip', json_decode($front_IdlePdtQuery->ip2, true)])->asArray()->all();
				$temp2 = array_column($PdtIpAll2, 'room_id');
				$temp2 = array_unique($temp2);
				$temp2 =  array_values($temp2);
				if(count($temp2) > 1 ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info' => '更换前的自有机器原绑定的IP存在多机房'];
					return $this->renderJSON($arrReturn);
				} else if( count($temp2) == 1) {
					if($temp2[0] != $after_IdlePdtQuery->room_id ) {
						$transaction->rollBack();
						$arrReturn = ['status' => 0, 'info' => '更换后的自有机器原绑定的IP所属机房与更换前的自有机器绑定IP所属机房不一致'
						];
						return $this->renderJSON($arrReturn);
					}
				}
				
				$line_typeList2 = array_unique(array_column($PdtIpAll2, 'line_type_id'));
				
				if( count($line_typeList2) > 1 ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info' => '所选IP与其他IP的线路类型不匹配'];
					return $this->renderJSON($arrReturn);
				}
				#获取机器对应交换机的线路信息
				/*$SwitchLineList2 = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $after_IdlePdtQuery->switch_location, 'line_type_id' => $line_typeList2[0]])->asArray()->one();
				if( empty($SwitchLineList2) ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info' => '更换后机器对应的交换机线路中未有更换前机器IP的线路类型'];
					return $this->renderJSON($arrReturn);
				}*/
			}					
			#End IP线路验证	
			
			$front_ip = $front_IdlePdtQuery->ip;
			$front_ip2 = $front_IdlePdtQuery->ip;
			
			$after_ip = $after_IdlePdtQuery->ip;
			$after_ip2 = $after_IdlePdtQuery->ip2;
			
			$front_IdlePdtQuery->ip = $after_ip;
			$front_IdlePdtQuery->ip2 = $after_ip2;
			if( !$SwitchLineList1) {
				$front_IdlePdtQuery->vlan = $SwitchLineList1['switch_vlan'];
			}
			
			$after_IdlePdtQuery->ip = $front_ip;
			$after_IdlePdtQuery->ip2 = $front_ip2;
			if( !$SwitchLineList2) {
				$after_IdlePdtQuery->vlan = $SwitchLineList2['switch_vlan'];
			}
			
			if( isset($replace_front['test_id']) && $replace_front['test_id'] ) { #如果之前选择的为测试机
				$front_TestServerQuery = $TestServerModel->find()->where(['id' => $replace_front['test_id']])->one();
				$front_TestServerQuery->ip = $after_ip;
				$front_TestServerQuery->ip2 = $after_ip2;
				if( !$front_TestServerQuery->update() ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info' => '测试机IP更新失败1'];
					return $this->renderJSON($arrReturn);
				}
			}
			
			if( isset($replace_after['test_id']) && $replace_after['test_id'] ) { #如果之后选择的为测试机
				$after_TestServerQuery = $TestServerModel->find()->where(['id' => $replace_after['test_id']])->one();
				$after_TestServerQuery->ip = $front_ip;
				$after_TestServerQuery->ip2 = $front_ip2;
				if( !$after_TestServerQuery->update() ) {
					$transaction->rollBack();
					$arrReturn = ['status' => 0, 'info' => '测试机IP更新失败'];
					return $this->renderJSON($arrReturn);
				}
			}
			
			
			
			$ao_s_content['is_exchangedip'] = 1;			
			$AfterorderSonQuery->ao_s_content = json_encode($ao_s_content, JSON_UNESCAPED_UNICODE);
			
			if( $front_IdlePdtQuery->update() && $after_IdlePdtQuery->update() && $AfterorderSonQuery->update() ) {
				$transaction->commit();
				$arrReturn = ['status' => 1, 'info' => 'IP对换成功'];
			} else {
				$transaction->rollBack();
				$arrReturn = ['status' => 0, 'info' => 'IP对换失败'];
			}
			return $this->renderJSON($arrReturn);
		} else { 
			#如果是测试机就需要操作  将原IP换到测试机上？？？
			
			
			
			$transaction->rollBack();
			$arrReturn = ['status' => 0, 'info' => '供应商机器不执行这个操作'];
		}
		return $this->renderJSON($arrReturn);
		
	}
	
	#重置IP
	public function actionResetIp() {
		
		$AfterorderListModel = new AfterorderList();
		$AfterorderSonlistTrialModel = new AfterorderSonlistTrial();
		$AfterorderSonlistModel = new AfterorderSonlist();

		$MemberPdtModel = new MemberPdt();
		$TestServerModel = new TestServer();

		#提交
		if ( Yii::$app->request->post() ) {
			
			$post = $this->post();
			$ao_id = $post['ao_id'];
			$AfterOrderInfo = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
			if(in_array($AfterOrderInfo['ao_type'], ['已完成','已取消'])) {				
				$arrReturn = ['status' => 0, 'info' => '工单状态已完成或已取消'];
				return $this->renderJSON($arrReturn);
			}
			
			$request_content = json_decode($AfterOrderInfo['ao_request_content'], true);
			
			if( $request_content['servicerprovider'] != 0) {
				$arrReturn = ['status' => 0, 'info' => '供应商机器不支持此类操作'];
				return $this->renderJSON($arrReturn);	
			}
			
			if( isset($request_content['unionid']) && $request_content['unionid'] != '') {
				$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $request_content['unionid']])->asArray()->one();				
				
				$Original_ip = json_decode($MemberPdtRes['ip2'], true);
				$Original_ip1 = json_decode($MemberPdtRes['ip'], true);
				
				$new_content['unionid'] = $request_content['unionid'];
				
			} else if( isset($request_content['test_id']) && $request_content['test_id'] != '') {
				$TestServerRes = $TestServerModel->find()->where(['id' => $request_content['test_id']])->asArray()->one();				
			
				$Original_ip = json_decode($TestServerRes['ip2'], true);
				$Original_ip1 = json_decode($TestServerRes['ip'], true);
				
				$new_content['test_id'] = $request_content['test_id'];
			} else {
				$arrReturn = ['status' => 0, 'info' => '未知的参数'];
				return $this->renderJSON($arrReturn);
			}
			
			#开启事务
			$transaction = Yii::$app->db->beginTransaction();
			#开始验证IP
			$IpArr = DataHelper::dotrim($post['ips']);
			if( !empty($IpArr) ) {
				$ipArray = array_filter(array_unique($IpArr));	#处理的得到的IP数组1   #去重 去空
				#拆分IP
				$Retuen_IPArray = DataHelper::splitIP($ipArray);
				if( !$Retuen_IPArray['status'] ) {
					$arrReturn = ['status' => 0, 'info'=> $Retuen_IPArray['info']];
					return $this->renderJSON($arrReturn);
				}
				
				$ipArray2 = $Retuen_IPArray['data'];			
				
				$diff_arr = array_diff($ipArray2, $Original_ip);
				
				if( !empty($diff_arr) ) {
					$arrReturn = ['status' => 0, 'info'=> '存在IP不属于原IP'];
					return $this->renderJSON($arrReturn);
				}
								
				$bgIPArray = array_unique(array_merge($ipArray2, $Original_ip));
				$remove_ip = array_diff($bgIPArray,$ipArray2);					

				if( empty($remove_ip) ) {
					$arrReturn = ['status' => 0, 'info'=> '未重置IP'];
					return $this->renderJSON($arrReturn);
				}
				
			} else {
				$ipArray = [];  #空
			}
			
			#增加子单
			#查询是否需要审核
			$typeInfo = $AfterorderSonlistTrialModel->find()->where(['sonlist_type' => '重置IP'])->one();
			
			$new_content['ipmi_ip'] = $request_content['ipmi_ip'];
			$new_content['replace_front']['ip'] = $Original_ip1;
			$new_content['replace_after']['ip'] = $ipArray;
			
			$time = time();
			
			#生成子单
			$AfterorderSonlistModel->ao_id = $ao_id;
			$AfterorderSonlistModel->ao_s_type = '重置IP';
			$AfterorderSonlistModel->ao_s_content = json_encode($new_content, JSON_UNESCAPED_UNICODE);
			$AfterorderSonlistModel->ao_s_trial_status = $typeInfo->sonlist_istrial == 'Y' ? '等待审核' : '无需审核';
			$AfterorderSonlistModel->ao_s_status = '未完成';
			$AfterorderSonlistModel->ao_s_request_time = $time;
			$AfterorderSonlistModel->ao_s_finish_time = null;
			if( !$AfterorderSonlistModel->insert() ) {
				$transaction->rollBack();
				$arrReturn = [
					'status'=>0,
					'info'=>'创建子工单时出现异常，请联系管理员'
				];
			} else {
				
				#创建子单审核通知
				TrailAndNotice::createSonTrail($ao_id, $AfterorderSonlistModel->ao_s_sonlist_id);
				
				$transaction->commit();
				$arrReturn = [
					'status'=>1,
					'info'=>'子单提交成功'
				];
			}
			return $this->renderJSON($arrReturn);
			
			
			
			
		} else {
			$arrReturn = ['status' => 0,'info' => '异常操作提交'];
			return $this->renderJSON($arrReturn);
		}
		
	}

	#提交修改带宽设定 子单
	public function actionSetBandwidth() {
		
		$AfterorderListModel = new AfterorderList();
		$AfterorderSonlistTrialModel = new AfterorderSonlistTrial();
		$AfterorderSonlistModel = new AfterorderSonlist();

		$MemberPdtModel = new MemberPdt();
		$IdlePdtModel = new IdlePdt();
		$TestServerModel = new TestServer();
		
		$post = $this->post();
		
		$ao_id = $post['ao_id'];
		#查询出主单
		$afterorder_info = $AfterorderListModel->find()->where(['ao_id' => $post['ao_id']])->asArray()->one();
		if( empty($afterorder_info) ) {
			$arrReturn = ['status' => 0, 'info' => "未知的工单"];
			return $this->renderJSON($arrReturn);
		}
		
		$transaction = Yii::$app->db->beginTransaction();
		
		$ao_request_content = json_decode($afterorder_info['ao_request_content'], true);
		
		if( isset($ao_request_content['unionid']) && $ao_request_content['unionid'] != '') {
			$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $ao_request_content['unionid']])->asArray()->one();
			if( empty($MemberPdtRes)) {
				$arrReturn = ['status' => 0, 'info' => "业务机器信息不存在"];
				return $this->renderJSON($arrReturn);
			}
			if( $MemberPdtRes['servicerprovider'] == 1) {
				$arrReturn = ['status' => 0, 'info' => "供应商机器不能进行此项操作"];
				return $this->renderJSON($arrReturn);
			}
			$idle_id = $MemberPdtRes['idle_id'];
			$ipmi_ip = $MemberPdtRes['ipmi_ip'];
			
		} else if( isset($ao_request_content['idle_id']) && $ao_request_content['idle_id'] != '') {
			$IdlePdtRes = $IdlePdtModel->find()->where(['id' => $ao_request_content['idle_id']])->asArray()->one();
			if( empty($IdlePdtRes)) {
				$arrReturn = ['status' => 0, 'info' => "自有机器信息不存在"];
				return $this->renderJSON($arrReturn);
			}
			if( $IdlePdtRes['servicerprovider'] == 1) {
				$arrReturn = ['status' => 0, 'info' => "供应商机器不能进行此项操作"];
				return $this->renderJSON($arrReturn);
			}
			$idle_id = $IdlePdtRes['id'];
			$ipmi_ip = $IdlePdtRes['ipmi_ip'];
			
		} else if( isset($ao_request_content['test_id']) && $ao_request_content['test_id'] != '') {
			$TestServerRes = $TestServerModel->find()->where(['id' => $ao_request_content['test_id']])->asArray()->one();
			if( empty($TestServerRes)) {
				$arrReturn = ['status' => 0, 'info' => "测试机信息不存在"];
				return $this->renderJSON($arrReturn);
			}
			if( $TestServerRes['servicerprovider'] == 1) {
				$arrReturn = ['status' => 0, 'info' => "供应商机器不能进行此项操作"];
				return $this->renderJSON($arrReturn);
			}
			$idle_id = $TestServerRes['idle_id'];
			$ipmi_ip = $TestServerRes['ipmi_ip'];
		}
		
		#判定子单是否需要审核
		$AfterorderSonlistTrialRes = $AfterorderSonlistTrialModel->find()->where(['sonlist_type' => '带宽设定'])->asArray()->one();
		$sonlist_istrial = $AfterorderSonlistTrialRes['sonlist_istrial'];
		
		$data['idle_id'] = $idle_id;
		$data['ipmi_ip'] = $ipmi_ip;
		
		$data['speed_type'] = $post['speed_type'];
		$data['old_speed_limit'] = $post['old_speed_limit'];
		$data['new_speed_limit'] = $post['speed_policy_select'];

		#生成子单
		$AfterorderSonlistModel->ao_id = $ao_id;
		$AfterorderSonlistModel->ao_s_type = '带宽设定';
		$AfterorderSonlistModel->ao_s_content = json_encode($data, JSON_UNESCAPED_UNICODE);
		$AfterorderSonlistModel->ao_s_trial_status = $sonlist_istrial == 'Y' ? '等待审核' : '无需审核';
		$AfterorderSonlistModel->ao_s_status = '未完成';
		$AfterorderSonlistModel->ao_s_request_time = time();
		$AfterorderSonlistModel->ao_s_finish_time = null;
		
		if( $AfterorderSonlistModel->insert() ) {
			$transaction->commit();
			$arrReturn = ['status' => 1, 'info'=> '提交子单成功'];		
		} else {
			$transaction->rollBack();
			$arrReturn = ['status'=>0, 'info'=> '加入申请失败，请稍后重试'];		
		}
		
		return $this->renderJSON($arrReturn);
		
	}
	
	#带宽设定 提交
	public function actionSetBandwidthOperation() {
		
		$trailSonid = $this->post('trail_id');
		
		$admin_id = $this->getAdminInfo('admin_id');
		$admin_name = $this->getAdminInfo('uname');

		if(!$trailSonid) {
			$arrReturn = ['status' => 0, 'info' => '参数异常'];
			return $this->renderJSON($arrReturn);
		}
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 
		
		$AfterorderSonlistModel = new AfterorderSonlist();
		$AfterorderListModel = new AfterorderList();
		$IdlePdtModel = new IdlePdt();
		
		#查询出子单
		$soninfo = $AfterorderSonlistModel->find()->where(['ao_s_sonlist_id' => $trailSonid])->one();
		#查询出主单
		$afterorder_info = $AfterorderListModel->find()->where(['ao_id' => $soninfo['ao_id']])->one();
		
		if($afterorder_info->ao_type != '未完成') {
			$arrReturn = ['status' => 0, 'info' => "工单已被关闭！请联系相应的发起人了解详细情况"];
			return $this->renderJSON($arrReturn);
		}
		
		$time = time();
		
		$ao_s_content = json_decode($soninfo['ao_s_content'], true);
		
		$idle_id = $ao_s_content['idle_id'];
		$speed_type = $ao_s_content['speed_type'];
		$new_speed_limit = $ao_s_content['new_speed_limit'];
		
		$ao_s_content['submit_update'] = 1;
		$soninfo->ao_s_content = json_encode($ao_s_content, JSON_UNESCAPED_UNICODE);
		$soninfo->ao_s_status = '已完成';
		$soninfo->ao_s_finish_time = $time;
		
		if( !$soninfo->update() ) {
			$transaction->rollBack();
			$arrReturn = ['status' => 0, 'info' => '子单更新失败'];
			return $this->renderJSON($arrReturn);
		}
		
		$IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $idle_id])->one();
		
		$config = json_decode($IdlePdtQuery->config, true);		
		$config['configbandwidth'] = $new_speed_limit;		
		$IdlePdtQuery->config = json_encode($config, JSON_UNESCAPED_UNICODE);
		
		if( !$IdlePdtQuery->update() ) {
			$transaction->rollBack();
			$arrReturn = ['status' => 0, 'info' => '自有机器信息更新失败'];
			return $this->renderJSON($arrReturn);
		}		
		
		$BackRes = MachineOperation::UpdateSwitchSpeedlimit($idle_id, $speed_type, $new_speed_limit);
		
		if($BackRes['status'] != 1) {
			$transaction->rollBack();
			$arrReturn = ['status' => 0, 'info' => $BackRes['info']];			
		} else {
			$transaction->commit();
			$arrReturn = ['status' => 1, 'info' => '操作成功'];
		}
		return $this->renderJSON($arrReturn);
		
	}
	
	#获取交换机配置
	public function actionGetSwitchConfig() {
		
		$ao_id = $this->post('ao_id');
		
		$admin_id = $this->getAdminInfo('admin_id');
		$admin_name = $this->getAdminInfo('uname');
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 

		$AfterorderListModel = new AfterorderList();
		$MemberPdtModel = new MemberPdt();
		$IdlePdtModel = new IdlePdt();
		$TestServerModel = new TestServer();

		#查询出主单
		$afterorder_info = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
		
		$ao_request_content = json_decode($afterorder_info['ao_request_content'], true);
		
		if( isset($ao_request_content['unionid']) && $ao_request_content['unionid'] != '') {
			$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $ao_request_content['unionid']])->asArray()->one();
			if( empty($MemberPdtRes)) {
				$arrReturn = ['status' => 0, 'info' => "业务机器信息不存在"];
				return $this->renderJSON($arrReturn);
			}
			if( $MemberPdtRes['servicerprovider'] == 1) {
				$arrReturn = ['status' => 0, 'info' => "供应商机器不能进行此项操作"];
				return $this->renderJSON($arrReturn);
			}
			$idle_id = $MemberPdtRes['idle_id'];
			
		} else if( isset($ao_request_content['idle_id']) && $ao_request_content['idle_id'] != '') {
			$IdlePdtRes = $IdlePdtModel->find()->where(['id' => $ao_request_content['idle_id']])->asArray()->one();
			if( empty($IdlePdtRes)) {
				$arrReturn = ['status' => 0, 'info' => "自有机器信息不存在"];
				return $this->renderJSON($arrReturn);
			}
			if( $IdlePdtRes['servicerprovider'] == 1) {
				$arrReturn = ['status' => 0, 'info' => "供应商机器不能进行此项操作"];
				return $this->renderJSON($arrReturn);
			}
			$idle_id = $IdlePdtRes['id'];
			
		} else if( isset($ao_request_content['test_id']) && $ao_request_content['test_id'] != '') {
			$TestServerRes = $TestServerModel->find()->where(['id' => $ao_request_content['test_id']])->asArray()->one();
			if( empty($TestServerRes)) {
				$arrReturn = ['status' => 0, 'info' => "测试机信息不存在"];
				return $this->renderJSON($arrReturn);
			}
			if( $TestServerRes['servicerprovider'] == 1) {
				$arrReturn = ['status' => 0, 'info' => "供应商机器不能进行此项操作"];
				return $this->renderJSON($arrReturn);
			}
			$idle_id = $TestServerRes['idle_id'];
			
		}

		$BackRes = MachineOperation::GetSwitchConfig($idle_id);
		
		if($BackRes['status'] == 1) {
			$arrReturn = ['status' => 1, 'info' => '获取成功', 'data' => $BackRes['data']];
		} else {
			$arrReturn = ['status' => 0, 'info' => $BackRes['info']];
		}
		return $this->renderJSON($arrReturn);
	}

	#获取限速策略
	public function actionGetSpeedstrategy() {
		
		$ao_id = $this->post('ao_id');
		
		$admin_id = $this->getAdminInfo('admin_id');
		$admin_name = $this->getAdminInfo('uname');
		
		#开启事务
        $transaction = Yii::$app->db->beginTransaction(); 

		$AfterorderListModel = new AfterorderList();
		$MemberPdtModel = new MemberPdt();
		$IdlePdtModel = new IdlePdt();
		$TestServerModel = new TestServer();

		#查询出主单
		$afterorder_info = $AfterorderListModel->find()->where(['ao_id' => $ao_id])->asArray()->one();
		
		$ao_request_content = json_decode($afterorder_info['ao_request_content'], true);

		if( isset($ao_request_content['unionid']) && $ao_request_content['unionid'] != '') {
			$MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $ao_request_content['unionid']])->asArray()->one();
			if( empty($MemberPdtRes)) {
				$arrReturn = ['status' => 0, 'info' => "业务机器信息不存在"];
				return $this->renderJSON($arrReturn);
			}
			if( $MemberPdtRes['servicerprovider'] == 1) {
				$arrReturn = ['status' => 0, 'info' => "供应商机器不能进行此项操作"];
				return $this->renderJSON($arrReturn);
			}
			$idle_id = $MemberPdtRes['idle_id'];
			
		} else if( isset($ao_request_content['idle_id']) && $ao_request_content['idle_id'] != '') {
			$IdlePdtRes = $IdlePdtModel->find()->where(['id' => $ao_request_content['idle_id']])->asArray()->one();
			if( empty($IdlePdtRes)) {
				$arrReturn = ['status' => 0, 'info' => "自有机器信息不存在"];
				return $this->renderJSON($arrReturn);
			}
			if( $IdlePdtRes['servicerprovider'] == 1) {
				$arrReturn = ['status' => 0, 'info' => "供应商机器不能进行此项操作"];
				return $this->renderJSON($arrReturn);
			}
			$idle_id = $IdlePdtRes['id'];
			
		} else if( isset($ao_request_content['test_id']) && $ao_request_content['test_id'] != '') {
			$TestServerRes = $TestServerModel->find()->where(['id' => $ao_request_content['test_id']])->asArray()->one();
			if( empty($TestServerRes)) {
				$arrReturn = ['status' => 0, 'info' => "测试机信息不存在"];
				return $this->renderJSON($arrReturn);
			}
			if( $TestServerRes['servicerprovider'] == 1) {
				$arrReturn = ['status' => 0, 'info' => "供应商机器不能进行此项操作"];
				return $this->renderJSON($arrReturn);
			}
			$idle_id = $TestServerRes['idle_id'];
			
		}

		$BackRes = MachineOperation::GetSpeedstrategy($idle_id);
		
		if($BackRes['status'] == 1) {
			$arrReturn = ['status' => 1, 'info' => '获取成功', 'data' => $BackRes['data']];
		} else {
			$arrReturn = ['status' => 0, 'info' => $BackRes['info']];
		}
		return $this->renderJSON($arrReturn);
	}


















}