<?php

namespace common\helpers;

use common\enums\AppEnum;
use common\models\common\Config;
use yii\web\JsExpression;
use common\models\merchant\Merchant;
use Exception;
use Yii;
use yii\db\ActiveQuery;
use yii\db\Expression;

/**
 * Class MerchantHelper
 * @package common\helpers
 * <AUTHOR> <<EMAIL>>
 */
class MerchantHelper
{
    const ONE = 'one';
    const TWO = 'two';

    /**
     * @param Merchant $merchant
     * @return string
     */
    public static function html($merchant)
    {
        if (empty($merchant)) {
            return '';
        }

        $url = Url::toRoute(['/merchants/merchant/view', 'id' => $merchant->id]);
        $url = '#';
        $name = '商户名: ' . Html::encode($merchant['title']);
        $hideName = Html::encode(StringHelper::textNewLine($merchant->title, 6, 1)[0]);
        $cover = Html::img(ImageHelper::defaultHeaderPortrait($merchant->cover), [
            'class' => 'img-circle elevation-1',
            'width' => '35',
            'height' => '35',
        ]);

        $toggle = [];
        $toggle[] = '商户ID: ' . $merchant->id;
        $toggle[] = $name;
        $toggle[] = '联系方式: ' . (!empty($merchant['mobile']) ? StringHelper::hideStr($merchant['mobile'], 3, 4) : '-');
        $toggle = "<div class='text-left'>" . implode('<br>', $toggle) . "</div>";

        return '<div class="text-center" href="' . $url . '">' . $cover . '<a class="users-list-name pt-1" data-toggle="tooltip" data-placement="bottom" data-html="true" title="' . $toggle . '" href="javascript: void(0)">' . $hideName . '</a></div>';
    }

    /**
     * @param $searchModel
     * @param $model
     * @return array
     */
    public static function gridView($searchModel, $label = '所属商户', $relevancy = 'merchant', $default = '无')
    {
        return [
            'label' => $label,
            'attribute' => 'merchant_id',
            'headerOptions' => ['class' => 'col-md-1 text-align-center'],
            'contentOptions' => ['class' => 'text-align-center'],
            'filter' => \kartik\select2\Select2::widget([
                'name' => 'SearchModel[merchant_id]',
                'initValueText' => '', // set the initial display text
                'options' => ['placeholder' => '请输入店铺名称'],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 2,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return '等待中...'; }"),
                    ],
                    'ajax' => [
                        'url' => Url::to(['/merchant/select2']),
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) { 
                                return {q:params.term}; 
                        }'),
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(city) { return city.text; }'),
                    'templateSelection' => new JsExpression('function (city) { return city.text; }'),
                ],
            ]),
            'value' => function ($model) use ($relevancy, $default) {
                if (empty($model->$relevancy)) {
                    return $default;
                }

                return self::html($model->$relevancy);
            },
            'format' => 'raw',
        ];
    }

    /**
     * 根据域名获取商户ID
     * @param $appId 应用id
     * @param $domain
     */
    public static function getMerchantIdByDomain($appId = AppEnum::MERCHANT, $domain = null)
    {
        if (!$domain) {
            //获取域名
            $domain = Yii::$app->request->hostInfo;
        }
        $configName = 'web_domain';
        $query = Config::find();
        $query->select(['c.id', 'c.app_id', 'c.name'])
            ->from(['c' => $query->modelClass::tableName()])
            ->where(['c.app_id' => $appId, 'name' => $configName])
            ->innerJoinWith([
                'value' => function (ActiveQuery $query) use ($domain) {
                    $query->where(new Expression("find_in_set(:domain, data)"))
                        ->from(["v" => $query->modelClass::tableName()])
                        ->addParams([":domain" => $domain]);
                }
            ]);
        // $sql = $result->createCommand()->getRawSql();
        $result = $query->asArray()->one();
        if (empty($result)) {
            //当前网站域名未绑定商户
            // throw new Exception('当前网站域名未绑定商户');
            return 0;
        }
        return ArrayHelper::getValue($result, 'value.merchant_id');
    }

    /**
     * 商户筛选
     * @param mixed $form
     * @param mixed $searchModel
     */
    public static function merchantSelect($form, $searchModel,$options=[])
    {
        return $form->field($searchModel, 'merchant_id')->dropDownList(
            ArrayHelper::map(Yii::$app->services->merchant->findAllByCondition([]), 'id', 'title'),
           ArrayHelper::merge(['prompt' => '选择商户'],$options)
        )->label('所属商户');
    }
    /**
     * 判断是否为有效商户ID
     */
    public static function isValidMerId($merId){
        return $merId !== null && $merId !== '';
    }

}
