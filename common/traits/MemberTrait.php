<?php

namespace common\traits;

use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\helpers\Hashids;
use addons\VymDesen\common\models\Payment\PaymentWay;
use addons\VymDesen\common\models\UserCredit\UserCredit;
use addons\VymDesen\common\models\UserMember\UserMember;
use common\enums\MemberTypeEnum;
use common\helpers\ArrayHelper;
use common\models\member\Member;
use Exception;
use Yii;

trait MemberTrait
{
    /**
     *
     * 多商户注册时邮箱和手机号查找UserMember, 老账号UserMember已绑定则新注册一个UserMember
     *
     * @param UserMember|null $user
     * @param Member|null $member
     * @return bool
     *
     */

    private function dealUser(?UserMember $user, ?Member $member): bool
    {
        if ($user && $member) {

            $exists = $this->isBindMember($user);
            if($exists){
               return false;
            }

            $member->ds_uid = $user->u_id;
            $member->save(false);
            return true;
        }
        return false;
    }

    /**
     *
     * 老账号登录，自动注册新账号体系
     *
     * @param UserMember $userMember
     * @param            $password
     * @param            $isEmail
     *
     * @return void
     * @throws \yii\base\Exception
     * @throws \yii\db\Exception
     */
    protected function registerByUserMember(UserMember $userMember, $password, $isEmail = true)
    {
        try {
            $member =  $this->isBindMember($userMember);
            if ($member) {
                throw new Exception('已关联账号错误，请联系管理人员');
            }
            $username = $userMember->username;
            $email    = $userMember->email;
            $mobile   = $userMember->mobile;
            $exists   = Member::find()
                              ->filterWhere(['username' => $username])
                              ->orFilterWhere(['email' => $email])
                              ->orFilterWhere(['mobile' => $mobile])
                              ->exists();
            if ($exists) {
                throw new Exception('账号未知错误，请联系管理人员');
            }
            $transaction = Yii::$app->db->beginTransaction();

            $member                = new Member();
            $member->username      = $username ?: ($isEmail ? $email : $mobile);
            $member->ds_uid          = $userMember->u_id;
            $member->email         = $email;
            $member->mobile        = $mobile;
            $member->password      = $password;
            $member->type          = MemberTypeEnum::MEMBER;
            $member->merchant_id   = Yii::$app->services->merchant->getNotNullId();
            $member->password_hash = Yii::$app->security->generatePasswordHash($password);

            if (!$member->save()) {
                throw new Exception("账号关联错误，请联系管理人员");
            }
//            $mid             = $member->id;
//            $userMember->mid = $mid;
//            if (!$userMember->save()) {
//                throw new Exception("账号关联错误，请联系管理人员");
//            }
            $transaction->commit();
        } catch (Exception $e) {
            isset($transaction) && $transaction->rollBack();
            throw new Exception($e->getMessage());
        }
    }


    protected function isBindMember(UserMember $user): bool
    {
        return Member::find()->where(['ds_uid' => $user->u_id,'type'=>MemberTypeEnum::MEMBER])->exists();
    }

    /**
     * 注册关联老账号
     *
     * @param Member $model
     * @param        $password
     * @param        $admin_id
     * @param        $admin_name
     *
     * @return void
     * @throws \Throwable
     * @throws \yii\base\Exception
     */
    protected function registerUserMember(Member $model, $password, $admin_id = null, $admin_name = null)
    {

//        $merchantId = Yii::$app->services->merchant->getNotNullId();
//        //新注册 加上商户id 以区别多商户相同username
        $username = $model->username;
        $email    = $model->email;
        $mobile   = $model->mobile;

        //先判断用户账号相同禁止注册
        $user = UserMember::find()->where(['username' => $username])->one();
        if ($user) {
            throw new Exception('该账号已经存在');
        }

        //手机和邮箱存在则关联
        if ($email) {
            $user = UserMember::find()->where(['email' => $email])->one();
            if ($this->dealUser($user, $model)) {
                return;
            }
        }

        if ($mobile) {
            $user = UserMember::find()->where(['mobile' => $mobile])->one();
            if ($this->dealUser($user, $model)) {
                return;
            }
        }


        $user           = new UserMember();
        $user->uname = $username;
        $user->username = $username;
//新账号体系注册 UserMember不记录手机和邮箱
//        $user->mobile     = $mobile;
//        $user->email      = $email;
        $user->admin_id   = $admin_id ? $admin_id : (ArrayHelper::getValue(Yii::$app->params, 'ascription_admin_id', 99));
        $user->admin_name = $admin_name ? $admin_name : (ArrayHelper::getValue(Yii::$app->params, 'ascription_admin_name', 'Berry'));
        $user->status     = UserMember::STATUS_ENABLE;
        $user->salt       = Yii::$app->security->generateRandomString();
        $user->pwd        = Yii::$app->security->generatePasswordHash($password);
        $user->mobile     = $mobile;
        $user->reg_time   = time();

        if ($user->insert()) {
            $u_id = Yii::$app->db->getLastInsertID();

            //生成邀请码
            $hashids = Hashids::instance(6);
            $user->invite_code = $hashids->encode($u_id);
            $user->update();

            //把注册用户放入支付通道
            $paymentWay = new PaymentWay();
            $paymentWay->checkUserPay($u_id);
            //生成挂账账户
            $result = UserCredit::initAccount($u_id);
            $model->ds_uid = $u_id;
            $model->save();


        }
    }

}