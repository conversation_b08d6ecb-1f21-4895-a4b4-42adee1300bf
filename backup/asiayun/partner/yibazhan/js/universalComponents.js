const is_client = $('meta[property="is:client"]').attr('content') == 'wap';
// 调用
$(document).ready(function () {
    is_client ? m_setupNavigationTabs() : setupNavigationTabs();
    initializeActivity();
});


//提示错误
function Msgerror(msg) {
    cocoMessage.error(1000, msg);
}


function activityRules(rulesId) {
    var rulesContentId = '#rulesContent' + rulesId;
    var rulesDiv = $(rulesContentId).html();
    var obj = {
        type: "slideFromTop",
        close: "true",
        title: "活动规则",
        content: rulesDiv,
        btn: ["我知道了", ''],
        callBack1: function () {
        },
    };
    method.msg_layer(obj);
}

function PopUpReminder(type, key = 'rrttss', title, butt = '查看详情', url = null) {

    if (type == 1 && localStorage.getItem(key)) {
        return;
    }
    if (type == 2) {
        localStorage.removeItem(key);
    }
    var rulesContentId = '#notificationmsg';
    var rulesDiv = $(rulesContentId).html();
    var obj = {
        type: "slideFromTop",
        close: "true",
        title: title,
        content: rulesDiv,
        btn: [butt, '关闭',],
        callBack1: function () {
            localStorage.setItem(key, 'true');
            if (url) {
                location.href = url;
            } else {
                method.msg_close();
            }
        },
        callBack2: function () {
            localStorage.setItem(key, 'true');
            method.msg_close();
        },
    };
    method.msg_layer(obj);
}


function manageAnnouncement(type, key = 'rrttss') {
    var $toggleBtn = $('.latest-announcement-toggle');
    if (type == 1 && localStorage.getItem(key)) {
        $toggleBtn.hide()
        return;
    }
 
    var $announcement = $('.latest-announcement-panel');

    var $closeBtn = $('.latest-announcement-close');
    var $noMoreBtn = $('.latest-announcement-no-more');
    var $buttclose = $('.latest-announcement-buttclose');

    function openAnnouncement() {
        $announcement.addClass('visible');
    }

    function closeAnnouncement() {
        localStorage.setItem(key, 'true');
        $announcement.removeClass('visible');
        $toggleBtn.css('opacity', 1);
    }

    $closeBtn.click(function () {
        closeAnnouncement();
    });

    $toggleBtn.click(function () {
        openAnnouncement();
        $toggleBtn.css('opacity', 0);
    });

    $noMoreBtn.click(function () {
        localStorage.setItem(key, 'true');
        if ($(this).data('url')) {
            window.open($(this).data('url'));
        }
        closeAnnouncement();
    });

    $buttclose.click(function () {
        closeAnnouncement();
    });

    if (!localStorage.getItem(key)) {
        openAnnouncement();
    } else {
        $toggleBtn.css('opacity', 1);
    }
}


function activityError(title = '失败提醒', msg, url = false, butt = '确定') {
    var obj = {
        type: "slideFromTop",
        close: "true",
        title: title,
        content: msg,
        btn: [butt, ''],
        callBack1: function () {
            if (url) setTimeout(function () {
                location.href = url;
            }, 1000);
        },
    };
    method.msg_layer(obj);
}


//平滑滚动
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        var target = document.getElementById(this.getAttribute('href').substring(1));
        if (target) {
            target.scrollIntoView({behavior: 'smooth'});
        }
    });
});

//公司地图
function initMap(customData) {
    // 使用解构赋值从customData中提取所需数据
    const {coordinates, logo, name, address} = customData;

    // 初始化地图
    const map = new AMap.Map('atlas', {
        rotateEnable: false,
        pitchEnable: false,
        zoom: 17,
        pitch: 65,
        rotation: 45,
        viewMode: '3D', // 开启3D视图,默认为关闭
        zooms: [2, 20],
        center: coordinates.split(',')
    });

    // 添加控件
    const controlBar = new AMap.ControlBar({position: {right: '10px', top: '10px'}});
    controlBar.addTo(map);

    const toolBar = new AMap.ToolBar({position: {right: '40px', top: '110px'}});
    toolBar.addTo(map);

    // 创建标记
    const companyMarker = new AMap.Marker({
        position: coordinates.split(','),
        map: map
    });

    // 创建信息窗体
    const infoWindow = new AMap.InfoWindow({
        content: `<div class="info-window">
                    <img src="${logo}" alt="公司图片" class="info-img"/>
                    <h4 class="info-title">${name}</h4>
                    <p class="info-address">${address}</p>
                  </div>`,
        offset: new AMap.Pixel(0, -30)
    });

    // 打开信息窗体
    infoWindow.open(map, companyMarker.getPosition());
}


//移动端滑动组件
/**
 * 参数：selector 选择器 例如：'.scroll-container'(父元素)
 * 使用方法：new SwipeScrollBlocker('.scroll-container')
 */
class SwipeScrollBlocker {
    constructor(selector) {
        this.element = $(selector);
        this.wrapper = this.element.parent();
        this.startX = 0;
        this.startY = 0;
        this.startTransform = 0;
        this.isScrolling = undefined;
        this.initEvents();
    }

    initEvents() {
        //为selector添加transition和white-space过渡效果
        this.element.css({'transition': 'transform 0.3s', 'white-space': 'nowrap'});
        this.element.parent().css({'touch-action': 'pan-x'});

        this.element.on('touchstart', (event) => this.handleTouchStart(event))
            .on('touchmove', (event) => this.handleTouchMove(event));
    }

    getTransformX() {
        const transformMatrix = this.element.css('transform');
        const matches = transformMatrix.match(/matrix\((\d+,\s?){4}(-?\d+)/);
        return matches ? parseInt(matches[2], 10) : 0;
    }

    calculateLimits() {
        const wrapperWidth = this.wrapper.width();
        const elementWidth = this.element[0].scrollWidth;
        const maxTransform = 0;
        const minTransform = wrapperWidth - elementWidth;
        return {maxTransform, minTransform: Math.min(minTransform, 0)}; // 确保minTransform不为正值
    }

    handleTouchStart(event) {
        const touches = event.originalEvent.touches[0];
        this.startX = touches.pageX;
        this.startY = touches.pageY;
        this.startTransform = this.getTransformX();
        this.isScrolling = undefined;
    }

    handleTouchMove(event) {

        const touches = event.originalEvent.touches[0];
        const deltaX = touches.pageX - this.startX;

        if (this.isScrolling === undefined) {
            const deltaY = touches.pageY - this.startY;
            this.isScrolling = Math.abs(deltaY) > Math.abs(deltaX);
        }

        if (!this.isScrolling) {
            event.preventDefault();
            let newTransform = this.startTransform + deltaX;
            const {maxTransform, minTransform} = this.calculateLimits();
            newTransform = Math.max(Math.min(newTransform, maxTransform), minTransform);
            this.element.css('transform', `translateX(${newTransform}px)`);
        }
    }
}

//新闻页
$(document).ready(() => {
    const $news = $('.newsPage_left_list');
    if (!$news.length) return;

    const $newsList = $('.newsPage_left_list ul');
    let maxWidth = $newsList.width();
    let totalWidth = 0;
    let itemsToMove = []; // 用于保存需要移动到下拉菜单的元素

    // 计算并收集需要移动的li元素
    $newsList.find('li').each(function () {
        totalWidth += $(this).outerWidth(true);
        if (totalWidth > maxWidth) {
            // 此时这个元素超出了容器宽度，应该被移动到下拉菜单
            itemsToMove.push(this); // 将元素本身推入数组
            //删除已经移动的元素
            $(this).remove();
        }
        $newsList.css('opacity', 1);
    });

    if (itemsToMove.length > 0) {
        const $dropdownArrow = $('<div id="dropdownArrow"></div>').appendTo($news);
        const $dropdownContent = $('<div id="dropdownContent"></div>').appendTo($news);
        let isOverDropdown = false;

        const showDropdown = () => {
            $dropdownArrow.addClass('active');
            $dropdownContent.addClass('active').empty();

            // 添加之前保存的元素到dropdownContent中
            itemsToMove.forEach(item => {
                $dropdownContent.append($(item).clone());
            });
        };

        const hideDropdown = () => {
            if (!isOverDropdown) {
                $dropdownArrow.removeClass('active');
                $dropdownContent.removeClass('active').empty();
            }
        };

        $dropdownArrow.mouseenter(() => {
            showDropdown();
        }).mouseleave(() => {
            setTimeout(hideDropdown, 200);
        });

        $dropdownContent.mouseenter(() => {
            isOverDropdown = true;
        }).mouseleave(() => {
            isOverDropdown = false;
            setTimeout(hideDropdown, 200);
        });
    }
});


// 新闻内容页
function newsContent(customData) {
    const treeStructure = () => {
        const contentArea = $('.news-txt-content');
        const headings = contentArea.find('h1, h2, h3, h4, h5, h6');
        if (!headings.length) return;

        let minLevel = 6;
        const catalogBox = $('<ul class="catalogbox"></ul>');
        headings.each(function (index) {
            const currentLevel = parseInt(this.nodeName.substring(1));
            minLevel = Math.min(currentLevel, minLevel);
            const level = currentLevel - minLevel;
            const id = `catalog_${index + 1}`;
            $(this).attr('id', id);

            const text = $(this).text().trim();
            const listItem = $(`<li class="level-${level}" catalog="${id}">${text}</li>`);
            catalogBox.append(listItem);
        });

        if (catalogBox.length) {
            $('.directoryStructure').empty().append(catalogBox);
        }

        $('li[catalog]').on('click', function () {
            const id = $(this).attr('catalog');
            const targetElement = $(`#${id}`);
            const navHeight = $('.nav-layoutcontainer').outerHeight(true);
            const elementPosition = targetElement.offset().top;

            $('html, body').animate({
                scrollTop: elementPosition - navHeight - 10
            }, 'smooth');
        });

        $(window).on('scroll', function () {
            let currentId = "";
            headings.each(function () {
                const id = $(this).attr('id');
                if ($(window).scrollTop() >= $(this).offset().top - 100) {
                    currentId = id;
                }
            });

            $('li[catalog]').removeClass('current-catalog');
            $(`li[catalog="${currentId}"]`).addClass('current-catalog');
        });
    };

    treeStructure();

    let leftMargin;
    const adjustSocialIconsPosition = () => {
        const maxWidth = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--maxwidth'), 10);
        const screenWidth = $(window).width();
        const paddingValue = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--padding'), '70px');
        if (screenWidth > maxWidth) {
            leftMargin = (screenWidth - maxWidth) / 2 - 10;
        } else {
            leftMargin = paddingValue > 60 ? 10 : 0;
        }
        $('.social-icons').css('left', `${leftMargin}px`);
    };

    adjustSocialIconsPosition();
    $(window).resize(adjustSocialIconsPosition);

    $('#copy-link-img').click(async function (event) {
        event.preventDefault();
        const url = window.location.href,
            title = document.title,
            textToCopy = '文章标题：' + title + '，\n原文链接：' + url;

        try {
            await navigator.clipboard.writeText(textToCopy);
            cocoMessage.success(500, '链接已复制');
        } catch (err) {
            cocoMessage.error(1000, `链接复制失败: ${err}`);
        }
    });

    const viewHeight = $(window).height(),
        navHeight = $('.nav-layoutcontainer').height(),
        footerHeight = $('.footerContainer-h').height(),
        screenHeight = window.innerHeight;
    let maxScroll = viewHeight - navHeight - footerHeight - screenHeight;

    if (maxScroll < 0) maxScroll = 50;

    $(window).scroll(function () {
        if ($(this).scrollTop() > navHeight) {
            $('#top-img').css({opacity: '1', height: '40px'})
        } else {
            $('#top-img').css({opacity: '0', height: '0'})
        }

        if ($(this).scrollTop() > maxScroll) {
            $('.social-icons').css({opacity: '0', left: '-50px'})
        } else {
            $('.social-icons').css({opacity: '1', left: `${leftMargin}px`})
        }
    });

    $('#top-img').click(function () {
        $('body,html').animate({scrollTop: 0}, 500);
        return false;
    });

    const leftHeight = $('.et-left').height();
    $('.news-right-row').height(leftHeight);

    const $content = $('.news-txt-content');
    $content.find('img').each(function () {
        const src = $(this).attr('src');
        $(this).wrap(`<a class='LightGallery-txt--img' data-src='${src}'></a>`);
    });
    initializeLightGallery('.news-txt-content', '.LightGallery-txt--img');
}