<?php

namespace addons\VymCloud\common\models;

use common\behaviors\MerchantStoreBehavior;
use common\models\merchant\Merchant;
use Yii;

/**
 * This is the model class for table "{{%addon_cloud_firewall}}".
 *
 * @property int         $id
 * @property int         $server_id   云主机订单ID
 * @property int|null    $merchant_id 商户id
 * @property int|null    $store_id    店铺id
 * @property string|null $pos         策略名
 * @property string|null $type        出入方向
 * @property string|null $action      允许|拒绝
 * @property string|null $proto       协议类型
 * @property string|null $macro       宏
 * @property string|null $dest        本地IP
 * @property string|null $source      远程IP
 * @property string|null $dport       本地端口
 * @property string|null $sport       远程端口
 * @property int|null    $weight      权重
 * @property int|null    $enable      状态
 * @property string|null $created_at  创建时间
 * @property string|null $updated_at  更新时间
 * @property string|null $deleted_at  删除时间
 */
class CloudFirewall extends \yii\db\ActiveRecord
{

    use MerchantStoreBehavior;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%addon_cloud_firewall}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['pos', 'type', 'action', 'proto', 'macro', 'dest', 'source', 'dport', 'sport', 'created_at', 'updated_at', 'deleted_at'], 'default', 'value' => null],
            [['weight'], 'default', 'value' => 0],
            [['enable'], 'default', 'value' => 1],
            [['merchant_id', 'store_id'], 'default', 'value' => 0],
            [['server_id', 'weight', 'enable'], 'integer'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['pos', 'type', 'action', 'proto', 'macro', 'dest', 'source', 'dport', 'sport'], 'string', 'max' => 100],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'          => 'ID',
            'server_id'   => '云主机订单ID',
            'merchant_id' => '商户ID',
            'store_id'    => '店铺ID',
            'pos'         => '策略名',
            'type'        => '出入方向',
            'action'      => '允许|拒绝',
            'proto'       => '协议类型',
            'macro'       => '宏',
            'dest'        => '本地IP',
            'source'      => '远程IP',
            'dport'       => '本地端口',
            'sport'       => '远程端口',
            'weight'      => '权重',
            'enable'      => '状态',
            'created_at'  => '创建时间',
            'updated_at'  => '更新时间',
            'deleted_at'  => '删除时间',
        ];
    }

    /**
     * 关联商户
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }
}
