<?php

use addons\VymCloud\common\models\CloudCluster;
use common\enums\AppEnum;
use common\helpers\ArrayHelper;
use common\helpers\Html;
use common\helpers\Url;
use yii\grid\ActionColumn;
use yii\grid\GridView;

/** @var yii\web\View $this */
/** @var yii\data\ActiveDataProvider $dataProvider */
/** @var common\models\base\SearchModel $searchModel */
/** @var $areaList */

$this->title                   = '集群节点';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="row">
    <div class="col-12 col-xs-12">
        <div class="box">
            <div class="box-header">
                <h3 class="box-title"><?= Html::encode($this->title); ?></h3>
                <div class="box-tools">
                    <?= Html::create(['edit']); ?>
                </div>
            </div>
            <div class="box-body table-responsive">
                <?= GridView::widget([
                    'dataProvider' => $dataProvider,
                    'filterModel'  => $searchModel,
                    // 重新定义分页样式
                    'tableOptions' => [
                        'class'            => 'table table-hover rf-table',
                        'fixedNumber'      => 2,
                        'fixedRightNumber' => 1,
                    ],
                    'columns'      => [
                        'id',
                        [
                            'label'         => '所属商户',
                            'headerOptions' => ['class' => 'col-md-1'],
                            'format'        => 'raw',
                            'visible'       => Yii::$app->id == AppEnum::BACKEND,
                            'value'         => function ($model) {
                                if ($model->merchant && $mId = $model->merchant->id) {
                                    if ($mTitle = $model->merchant->title) {
                                        return Html::tag('span', $mTitle, ['class' => 'badge badge-primary']);
                                    }
                                    return Html::tag('span', "ID:{$mId}", ['class' => 'badge badge-primary']);
                                }
                                return Html::tag('span', '平台直属', ['class' => 'badge badge-secondary']);
                            },
                            'filter'        => Yii::$app->id == AppEnum::BACKEND ? Html::activeDropDownList($searchModel, 'merchant_id', 
                                ArrayHelper::map(Yii::$app->services->merchant->findAllByCondition([]), 'id', 'title'), [
                                    'prompt' => '全部',
                                    'class'  => 'form-control',
                                ]
                            ) : false,
                        ],
                        [
                            'attribute'     => 'area_id',
                            'headerOptions' => ['class' => 'col-md-1'],
                            'value'         => function ($model) use ($areaList) {
                                return $areaList[$model->area_id] ?? '';
                            },
                            'filter'        => Html::activeDropDownList($searchModel, 'area_id', Yii::$app->vymCloudService->area->getDropDownForEdit(0, false), [
                                    'prompt' => '全部',
                                    'class'  => 'form-control',
                                ]
                            ),
                        ],
                        ['attribute' => 'name', 'headerOptions' => ['class' => 'col-md-3'],],
                        ['attribute' => 'ip', 'headerOptions' => ['class' => 'col-md-2'],],
                        [
                            'attribute'     => 'cluster_st',
                            'format'        => 'raw',
                            'headerOptions' => ['class' => 'col-md-1'],
                            'value'         => function ($model, $key, $index, $column) {
                                return CloudCluster::getStatusHtml($model->cluster_st);
                            },
                            'filter'        => Html::activeDropDownList($searchModel, 'cluster_st', CloudCluster::getStatusMap(), [
                                    'prompt' => '全部',
                                    'class'  => 'form-control',
                                ]
                            ),
                        ],
                        [
                            'attribute'     => 'sort',
                            'format'        => 'raw',
                            'headerOptions' => ['class' => 'col-md-1'],
                            'filter'        => false, //不显示搜索框
                            'value'         => function ($model) {
                                return Html::sort($model->sort);
                            },
                        ],
                        ['attribute' => 'stock', 'filter' => false, 'headerOptions' => ['class' => 'col-md-1']],
                        ['attribute' => 'sales', 'filter' => false, 'headerOptions' => ['class' => 'col-md-1']],
                        [
                            'header'   => "操作",
                            'class'    => 'yii\grid\ActionColumn',
                            'template' => '{edit} {delete}',
                            'buttons'  => [
                                'edit'   => function ($url, $model, $key) {
                                    return Html::edit(['edit', 'id' => $model->id]);
                                },
                                'delete' => function ($url, $model, $key) {
                                    return Html::delete(['delete', 'id' => $model->id]);
                                },
                            ],
                        ],
                    ],
                ]); ?>
            </div>
        </div>
    </div>
</div>