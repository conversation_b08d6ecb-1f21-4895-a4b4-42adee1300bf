<template>
    <div class="detailDlg">
        <Tabs default-active-key="manageTab" v-model="curTab" @change="switchTabs">
            <Tabs.TabPane key="manageTab" tab="管理">
                <div class="manageCardPanel">
                    <Card class="manageCard">
                        <p style="display: flex; gap: 10px;">
                            <span style="width: 80px;">
                                当前状态:
                            </span>
                                <Select v-model:value="powerState" disabled style="min-width: 50%;">
                                    <Select.Option value="on">
                                        开机
                                    </Select.Option>
                                    <Select.Option value="off">
                                        关机
                                    </Select.Option>
                                    <Select.Option value="reset">
                                        重启
                                    </Select.Option>
                                </Select>
                        </p>
                        <p>对您的实例进行电源状态同步操作</p>
                        <p style="display: flex; gap: 10px; margin-top: 20px; justify-content: flex-end;">
                            <Button type="primary" @click="handlePowerStatusSync" :loading="manageCard.powerstatusSyncLoading">同步</Button>
                        </p>
                    </Card>
                    <Card class="manageCard">
                        <p style="display: flex; gap: 10px;">
                            <Select v-model:value="manageOpValue"
                                style="min-width: 50%;">
                                <Select.Option value="on">
                                    开机
                                </Select.Option>
                                <Select.Option value="off">
                                    关机
                                </Select.Option>
                                <Select.Option value="reset">
                                    重启
                                </Select.Option>
                            </Select>
                            <Button type="primary" @click="handleManageOperate" :loading="manageCard.powerLoading">
                                确认
                            </Button>
                        </p>
                        <p>对您的实例进行电源操作</p>
                    </Card>
                    <Card class="manageCard" v-if="!selfServer">
                        <p style="display: flex; gap: 10px;">
                            <span style="width: 80px;">
                                新密码：
                            </span>
                            <Input style="flex-grow: 1;" v-model="newPwd" />
                        </p>
                        <p>对您的实例进行修改密码操作</p>
                        <p style="display: flex; gap: 10px; margin-top: 20px; justify-content: flex-end;">
                            <Button type="primary" @click="handleChgpwd" :loading="manageCard.chgpwdLoading">确认</Button>
                        </p>
                    </Card>
                    <Card class="manageCard" v-if="selfServer">
                            <p v-if="selfServer" style="display: flex; gap: 10px;">
                                <span style="width: 80px; min-width: 80px;">
                                    破解模板:
                                </span>
                                <Select label-in-value v-model:value="crackTemplateVal" class="mirrors-select">
                                    <Select.Option v-for="option in crackTemplatesOptions" :title="option.label"
                                        :key="option.value" :value="option.value">
                                        {{ option.label }}
                                    </Select.Option>
                                </Select>
                            </p>
                        <p>对您的实例进行破解密码操作</p>
                        <p style="display: flex; gap: 10px; margin-top: 20px; justify-content: flex-end;">
                            <Button type="primary" @click="handleReqCrackTemplate" :loading="manageCard.crackTempLoading">获取模板</Button>
                            <Button type="primary" @click="handleCrack" :loading="manageCard.chgpwdLoading">破解密码</Button>
                            <Button type="primary" @click="handleCancelCrack" :loading="manageCard.cancelCrackLoading">撤销破解</Button>
                        </p>
                    </Card>
                    <Card class="manageCard">
                        <Space direction="vertical">
                            <p v-if="selfServer" style="display: flex; gap: 10px;">
                                <span style="width: 80px; min-width: 80px;">
                                    镜像分类：
                                </span>
                                <Select label-in-value v-model="mirrorImageTypeValue" class="mirrors-select" @change="handleGetMirros">
                                    <Select.Option v-for="option in mirrorTypesOptions" :title="option.label"
                                        :key="option.value" :value="option.value">
                                        {{ option.label }}
                                    </Select.Option>
                                </Select>
                            </p>
                            <p style="display: flex; gap: 10px;">
                                <span style="width: 80px; min-width: 80px;">
                                    选择镜像：
                                </span>
                                <Select label-in-value v-model:value="mirrorImageValue" class="mirrors-select">
                                    <Select.Option v-for="option in mirrorsOptions" :title="option.label"
                                        :key="option.value" :value="option.value">
                                        {{ option.label }}
                                    </Select.Option>
                                </Select>
                            </p>
                        </Space>
                        <p>对您的实例进行重装系统操作</p>
                        <p style="display: flex; gap: 10px; margin-top: 20px; justify-content: flex-end;">
                            <template v-if="selfServer">
                                <Button type="primary" @click="handleGetMirrosTypes"
                                    :loading="manageCard.mirrorLoading">获取镜像{{ selfServer ? '分类' : '' }}</Button>
                            </template>
                            <template v-if="!selfServer">
                                <Button type="primary" @click="handleGetMirros"
                                    :loading="manageCard.mirrorLoading">获取镜像{{ selfServer ? '分类' : '' }}</Button>
                            </template>
                            <Button type="primary" @click="handleRebuild" :loading="manageCard.rebuildLoading">重装</Button>
                            <template v-if="selfServer">
                                <Button type="primary" @click="handleCancelRebuild" :loading="manageCard.cancelRebuildLoading">撤销重装</Button>
                            </template>
                        </p>
                    </Card>
                    <Card class="manageCard">
                        <p>对您的实例进行远程控制操作</p>
                        <p style="display: flex; gap: 10px; margin-top: 20px; justify-content: flex-end;">
                            <Button type="primary" :loading="manageCard.remoteCtrlLoading" @click="handleRemoteCtrl">远程控制</Button>
                        </p>
                    </Card>
                    <Card class="manageCard" v-if="false">
                        <p>对您的实例进行测试操作</p>
                        <p style="display: flex; gap: 10px; margin-top: 20px; justify-content: flex-end;">
                            <Button type="primary" @click="handleTest">测试</Button>
                        </p>
                    </Card>
                </div>
            </Tabs.TabPane>
        </Tabs>
    </div>
</template>

<script lang="ts" setup>
import { apiConsole } from '#/api/business/console';
import { Button, Card, Input, message, Modal, Select, Space, Tabs } from 'ant-design-vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const curTab = ref('manageTab');
const isLoading = ref(false);
const manageOpValue = ref('on');
const manageCard = ref({
    powerLoading: false,
    rebuildLoading: false,
    mirrorLoading: false,
    chgpwdLoading: false,
    crackTempLoading: false,
    remoteCtrlLoading: false,
    cancelCrackLoading: false,
    cancelRebuildLoading: false,
    powerstatusSyncLoading: false,
});
const newPwd = ref('');

//镜像列表
const mirrorsOptions = ref<any[]>([]);
const mirrorTypesOptions = ref<any[]>([]);
const mirrorImageValue = ref();
const mirrorImageTypeValue = ref({key: ''});

//破解模板
const crackTemplatesOptions = ref<any[]>([]);
const crackTemplateVal = ref();


//vue props
const props = defineProps<{
    serverId: number;
    memberId?: number;  
    servicerprovider?: number | string;
    power_status?: number | string;
}>();

//是否为自有服务器
const selfServer = ref(! Number(props.servicerprovider));
const powerState = ref(props.power_status ? 'on' : 'off');

console.log("power_status", props.power_status ? 'on' : 'off');

//切换tab页签
function switchTabs(key: any) {
    console.log('switch ' + key);
    if (key == 'netTab') {
        //请求网络数据
        isLoading.value = true;
    }
}

async function reqApiConsole(url: string, params: any, before: any = null, after: any = null, resolve: any = null) {
    before && before();
    try {
        let resp: any = await apiConsole(url, params);
        if (resp.status == 1) {
            if(resolve){
                resolve(resp);
            }
            else {
                message.success(resp.msg || resp.info);
            }
        }
        else {
            message.error(resp.msg || resp.info);
        }
    }
    finally {
        after && after();
    }
}

/**
 * 电源状态同步
 */
function handlePowerStatusSync(){
    reqApiConsole('/vym-server/console/power-query', { member_id: props.memberId },
        () => manageCard.value.powerstatusSyncLoading = true, () => manageCard.value.powerstatusSyncLoading = false);
}

/**
 * 撤销重装
 */
function handleCancelRebuild(){
    reqApiConsole('/vym-server/console/cancel-reload', { member_id: props.memberId },
        () => manageCard.value.cancelRebuildLoading = true, () => manageCard.value.cancelRebuildLoading = false);
}

/**
 * 撤销破解
 */
function handleCancelCrack(){
    reqApiConsole('/vym-server/console/cancel-reload', { member_id: props.memberId },
        () => manageCard.value.cancelCrackLoading = true, () => manageCard.value.cancelCrackLoading = false);
}

/**
 * 电源操作
 */
function handleManageOperate() {
    let action = manageOpValue.value;
    reqApiConsole('/vym-server/console/power-operate', { member_id: props.memberId, action },
        () => manageCard.value.powerLoading = true, () => manageCard.value.powerLoading = false);
};
//重置密码
function handleChgpwd() {   
    let newPass = newPwd.value;
    if (!selfServer.value) {
        reqApiConsole('/vym-server/console/chgpwd', { member_id: props.memberId, 'password': newPass },
            () => manageCard.value.chgpwdLoading = true, () => manageCard.value.chgpwdLoading = false);
    }
    else {
        reqApiConsole('/vym-server/console/chgpwd', { member_id: props.memberId, 'password': newPass, 'class_category' : '破解密码' },
            () => manageCard.value.chgpwdLoading = true, () => manageCard.value.chgpwdLoading = false);
    }
}
/**
 * 破解密码
 */
function handleCrack(){
    let targetId = crackTemplateVal.value.key;
    if(!targetId){
        message.error('请选择破解模板');
        return;
    }
    let item = crackTemplatesOptions.value.find((v: any)=> v.value == targetId);
    console.log("targetId", targetId, crackTemplatesOptions.value, item)
    let cloudsystem = item.cloud_system_id;
    Modal.confirm({
        title: '破解密码',
        content: '确定要破解密码吗？',
        onOk: () => {
            reqApiConsole('/vym-server/console/rebuild', { member_id: props.memberId, cloudsystem },
                () => manageCard.value.chgpwdLoading = true, () => manageCard.value.chgpwdLoading = false
            );
        }
    })
}

/**
 * 获取破解密码模版
 */
function handleReqCrackTemplate(){
    reqApiConsole('/vym-server/console/crack', { member_id: props.memberId, 'class_category' : '破解密码' },
        () => manageCard.value.crackTempLoading = true, () => manageCard.value.crackTempLoading = false,
        (resp: any) => {
            crackTemplatesOptions.value = resp.info.map((item: any) => {
                return {
                    label: item.cloud_system_name,
                    value: item.id,
                    cloud_system_id: item.cloud_system_id
                }
            });
        }
    );
}

/**
 * 获取镜像列表
 */
function handleGetMirros(target: any = null){
    mirrorImageValue.value = null;
    let imageType = null;
    if(!selfServer.value){
        if (mirrorsOptions.value.length > 0) {
            message.error('镜像列表已获取');
            return;
        }
    }
    else {
        let typeValue = mirrorImageTypeValue.value.key || target.key;
        if(!typeValue){
            message.error('请求先选择镜像类型');
            return;
        }
        imageType = typeValue;
    }
    if(!selfServer){
        reqApiConsole('/vym-server/console/sys-mirrors', { member_id: props.memberId, class_id: imageType },
            () => manageCard.value.mirrorLoading = true, () => manageCard.value.mirrorLoading = false, 
            (res: any) => {
                mirrorsOptions.value = res.info.map((item: any) => ({
                    label: item.cloud_system_name,
                    value: item.id,
                }));
            }
        );
    }
    else {
        reqApiConsole('/vym-server/console/sys-mirrors', { member_id: props.memberId, class_id: imageType },
            () => manageCard.value.mirrorLoading = true, () => manageCard.value.mirrorLoading = false, 
            (res: any) => {
                mirrorsOptions.value = res.info.map((item: any) => ({
                    label: item.cloud_system_name,
                    value: item.id,
                    cloud_system_id: item.cloud_system_id,
                    class_id: item.class_id
                }));
            }
        );
    }
}

/**
 * 获取镜像分类
 */
function handleGetMirrosTypes(){
    if (mirrorsOptions.value.length > 0) {
        message.error('镜像列表已获取');
        return;
    }
    reqApiConsole('/vym-server/console/sys-mirror-types', { member_id: props.memberId },
        () => manageCard.value.mirrorLoading = true, () => manageCard.value.mirrorLoading = false, 
        (res: any) => {
            mirrorTypesOptions.value = res.info.map((item: any) => ({
                label: item.class_name,
                value: item.class_id,
            }));
        }
    );
}

//重装系统
function handleRebuild(){
    let imageValue = mirrorImageValue.value.key;
    if (!imageValue) {
        message.error('请选择镜像');
        return;
    }
    let item = mirrorsOptions.value.find((v:any) => v.id = imageValue);
    console.log("---重装系统---", imageValue, item);
    let cloud_system_id = item.cloud_system_id;
    let class_id = item.class_id;   
    Modal.confirm({
        title: '确定要重装系统吗？',
        content: '请确认是否要重装系统',
        onOk: () => {
                reqApiConsole('/vym-server/console/rebuild', { member_id: props.memberId, imageId: imageValue, cloud_system_id, class_id },
                    () => manageCard.value.rebuildLoading = true, () => manageCard.value.rebuildLoading = false
                );
            }
    });

}

/**
 * 远程控制
 */
function handleRemoteCtrl(){
    reqApiConsole('/vym-server/console/remote-ctrl', { member_id: props.memberId }, 
        ()=>manageCard.value.remoteCtrlLoading = true, 
        ()=>manageCard.value.remoteCtrlLoading = false,
        (res: any) => {
            if(res.info.type == 'web'){
                window.open(res.info.url);
            }
            else if(res.info.type == 'vnc'){
                router.push({
                    name: 'VncViewer',
                    state:{
                        url: res.info.url,
                        params: res.info.params,
                    }
                });
            }
        }
    );
}

/**
 * 测试
 */
function handleTest(){
    reqApiConsole('/vym-server/console/test', { member_id: props.memberId });
}

</script>

<style>
.detailDlg {
    z-index: 1;
}

.detailDlg .content {
    display: flex;
    flex-direction: column;
}

.detailDlg .manageCardPanel {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.detailDlg .manageCard {
    flex-grow: 1;
    max-width: 45%;
    min-width: 45%;
}

.detailDlg .ant-descriptions-item-label {
    color: rgba(0, 0, 0, 1);
    font-weight: normal;
    font-size: 14px;
    line-height: 1.5;
}

.detailDlg .mirrors-select {
    min-width: 200px;
    width: 100%;
    /* 根据需要调整宽度 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>