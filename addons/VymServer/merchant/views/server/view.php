<?php

use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/** @var yii\web\View $this */
/** @var addons\VymServer\common\models\Server $model */
/** @var addons\VymDesen\common\models\Member\MemberPdt $memberPdtModel */
/** @var array $config */
/** @var addons\VymDesen\common\models\UserMember\UserMember $userMemberModel */
/** @var addons\VymDesen\common\models\Member\InitialAccount $initialAccountModel */
/** @var addons\VymDesen\common\models\Member\MemberPdtAppointOff $appointOffModel */
/** @var array $transferRecordModel */
/** @var array $pdtManageList */
/** @var bool $isAllow */

$this->title                   = '服务器详情';
$this->params['breadcrumbs'][] = ['label' => '业务实例', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-server mr-1"></i>
                服务器详情 (ID: <?= Html::encode($model->id) ?>)
            </h3>
            <div class="card-tools">
                <?= Html::a('<i class="fas fa-arrow-left"></i> 返回列表', ['index'], ['class' => 'btn btn-secondary btn-sm']) ?>
            </div>
        </div>
        <div class="card-body">

            <?php if ($memberPdtModel): ?>
                <!-- 如果有关联的产品数据，显示详细信息 -->
                <div class="row">
                    <!-- 产品配置信息 -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-info">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-cogs"></i> 产品配置信息
                                </h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <strong>产品标识：</strong>
                                        <span class="text-danger"><?= Html::encode($memberPdtModel->unionid) ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>产品配置类别：</strong>
                                        <span class="text-danger"><?= Html::encode($memberPdtModel->pdtmanage[0]->name ?? '--') ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>CPU：</strong>
                                        <span class="text-danger"><?= Html::encode($config['cpu'] ?? '--') ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>内存大小：</strong>
                                        <span class="text-danger"><?= Html::encode($config['ram'] ?? '--') ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>硬盘：</strong>
                                        <span class="text-danger"><?= Html::encode($config['hdd'] ?? '--') ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>自有库带宽：</strong>
                                        <span class="text-danger"><?= Html::encode($config['configbandwidth'] ?? '--') ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>客户要求带宽：</strong>
                                        <span class="text-danger"><?= Html::encode($memberPdtModel->bandwidth ?? '--') ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>实际带宽：</strong>
                                        <span class="text-danger"><?= Html::encode($memberPdtModel->real_bandwidth ?? '--') ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>IP可用数：</strong>
                                        <span class="text-danger"><?= Html::encode($config['ipnumber'] ?? '--') ?></span>
                                    </li>
                                    <?php if (!empty($config['defense'])): ?>
                                        <li class="mb-2">
                                            <strong>防御流量：</strong>
                                            <span class="text-danger"><?= Html::encode($config['defense']) ?></span>
                                        </li>
                                    <?php endif; ?>
                                    <li class="mb-2">
                                        <strong>操作系统：</strong>
                                        <span class="text-danger"><?= Html::encode($config['operatsystem'] ?? '--') ?></span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 基本信息 -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-success">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle"></i> 基本信息
                                </h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <strong>所属用户：</strong>
                                        <?php if ($isAllow && !empty($userMemberModel)): ?>
                                            <span class="text-danger">
                                                <?= Html::encode($userMemberModel->email ?? '--') ?>
                                                <?php if (!empty($userMemberModel->truename)): ?>
                                                    (<?= Html::encode($userMemberModel->truename) ?>)
                                                <?php endif; ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">---</span>
                                        <?php endif; ?>
                                    </li>
                                    <li class="mb-2">
                                        <strong>用户联系QQ：</strong>
                                        <?php if ($isAllow && !empty($userMemberModel)): ?>
                                            <span class="text-danger"><?= Html::encode($userMemberModel->qq ?? '--') ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">---</span>
                                        <?php endif; ?>
                                    </li>
                                    <li class="mb-2">
                                        <strong>所属销售：</strong>
                                        <span class="text-danger"><?= Html::encode($userMemberModel->admin_name ?? '--') ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>服务提供者：</strong>
                                        <span class="text-danger"><?= $memberPdtModel->servicerprovider == 0 ? "自有" : "供应商" ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>服务器分类：</strong>
                                        <span class="text-danger"><?= Html::encode($memberPdtModel->servertype[0]->type_name ?? '--') ?></span>
                                    </li>
                                    <?php if (!empty($memberPdtModel->provider)): ?>
                                        <li class="mb-2">
                                            <strong>供应商：</strong>
                                            <span class="text-danger"><?= Html::encode($memberPdtModel->provider[0]->name) ?></span>
                                        </li>
                                    <?php endif; ?>
                                    <li class="mb-2">
                                        <strong>机房：</strong>
                                        <span class="text-danger"><?= Html::encode($memberPdtModel->pdtroom[0]->name ?? '--') ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>机柜：</strong>
                                        <span class="text-danger"><?= Html::encode($memberPdtModel->pdtcabinet[0]->name ?? '--') ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>机位：</strong>
                                        <span class="text-danger"><?= Html::encode($memberPdtModel->occupies_position ?? '--') ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>服务器状态属性：</strong>
                                        <span class="text-danger"><?= Html::encode($memberPdtModel->serverattribute[0]->name ?? '--') ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>售出价格：</strong>
                                        <?php if ($isAllow): ?>
                                            <span class="text-danger"><?= Html::encode($memberPdtModel->sell_price ?? '0.00') ?> 元</span>
                                        <?php else: ?>
                                            <span class="text-muted">---</span>
                                        <?php endif; ?>
                                    </li>
                                    <li class="mb-2">
                                        <strong>付款周期：</strong>
                                        <span class="text-danger"><?= Html::encode($memberPdtModel->payment_cycle ?? '--') ?> 个月</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <!-- IPMI与交换机信息 -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-info">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-network-wired"></i> IPMI与交换机信息
                                </h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <strong>IPMI地址：</strong>
                                        <span class="text-danger"><?= Html::encode($memberPdtModel->ipmi_ip ?? '--') ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>IPMI用户名：</strong>
                                        <span class="text-danger"><?= Html::encode($memberPdtModel->ipmi_name ?? '--') ?></span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>IPMI密码：</strong>
                                        <span class="text-danger"><?= Html::encode($memberPdtModel->ipmi_pwd ?? '--') ?></span>
                                    </li>
                                    <?php if ($memberPdtModel->servicerprovider == 0 && !empty($memberPdtModel->switch)): ?>
                                        <li class="mb-2">
                                            <strong>交换机地址：</strong>
                                            <span class="text-danger"><?= Html::encode($memberPdtModel->switch[0]->ip) ?></span>
                                        </li>
                                        <li class="mb-2">
                                            <strong>交换机端口：</strong>
                                            <span class="text-danger"><?= Html::encode($memberPdtModel->switch_port ?? '--') ?></span>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 产品IP地址与初始账户 -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-success">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-globe"></i> IP地址与初始账户
                                </h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li class="mb-3">
                                        <strong>IP地址：</strong>
                                        <div class="mt-1">
                                            <?php
                                            $ipRes = json_decode($memberPdtModel->ip ?? '[]', true);
                                            if (!empty($ipRes) && is_array($ipRes)):
                                                foreach ($ipRes as $ip):
                                                    echo '<span class="badge badge-info mr-1">' . Html::encode($ip) . '</span>';
                                                endforeach;
                                            else:
                                                echo '<span class="text-muted">暂无IP地址</span>';
                                            endif;
                                            ?>
                                        </div>
                                    </li>
                                    <?php if (empty($initialAccountModel)): ?>
                                        <li class="mb-2">
                                            <span class="text-warning">
                                                <i class="fas fa-exclamation-triangle"></i> 暂未设置登录账户
                                            </span>
                                        </li>
                                    <?php else: ?>
                                        <li class="mb-2">
                                            <strong>账户名：</strong>
                                            <span class="text-danger font-monospace"><?= Html::encode($initialAccountModel->name) ?></span>
                                        </li>
                                        <li class="mb-2">
                                            <strong>账户密码：</strong>
                                            <span class="text-danger font-monospace"><?= Html::encode($initialAccountModel->pwd) ?></span>
                                        </li>
                                        <li class="mb-2">
                                            <strong>远程端口：</strong>
                                            <span class="text-danger"><?= Html::encode($initialAccountModel->port) ?></span>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 服务时间信息 -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-warning">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-clock"></i> 服务时间信息
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>开始时间：</strong><br>
                                        <span class="text-info">
                                            <?= !empty($memberPdtModel->start_time) ? date('Y-m-d H:i:s', $memberPdtModel->start_time) : '--' ?>
                                        </span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>到期时间：</strong><br>
                                        <span class="text-danger">
                                            <?= !empty($memberPdtModel->end_time) ? date('Y-m-d H:i:s', $memberPdtModel->end_time) : '--' ?>
                                        </span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>服务状态：</strong><br>
                                        <?php
                                        $status = $memberPdtModel->status ?? 0;
                                        switch ($status) {
                                            case 1:
                                                echo '<span class="badge badge-success">正常</span>';
                                                break;
                                            case -1:
                                                echo '<span class="badge badge-danger">已删除</span>';
                                                break;
                                            case 0:
                                                echo '<span class="badge badge-warning">未开通</span>';
                                                break;
                                            case -2:
                                                echo '<span class="badge badge-warning">退款中</span>';
                                                break;
                                            case 2:
                                                echo '<span class="badge badge-warning">变配中</span>';
                                                break;
                                            case 3:
                                                echo '<span class="badge badge-warning">换机中</span>';
                                                break;
                                            case 4:
                                                echo '<span class="badge badge-warning">换IP中</span>';
                                                break;
                                            case 5:
                                                echo '<span class="badge badge-warning">过户中</span>';
                                                break;
                                            case 6:
                                                echo '<span class="badge badge-warning">关机下架中</span>';
                                                break;
                                            case 99:
                                                echo '<span class="badge badge-warning">工单处理中</span>';
                                                break;
                                            default:
                                                echo '<span class="badge badge-secondary">未知</span>';
                                        }
                                        ?>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>续费方式：</strong><br>
                                        <?php if ($memberPdtModel->is_auto == "Y"): ?>
                                            <span class="badge badge-success">自动续费</span>
                                        <?php else: ?>
                                            <span class="badge badge-secondary">手动续费</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 备注信息 -->
                <?php if (!empty($memberPdtModel->remark) || !empty($memberPdtModel->note)): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-sticky-note"></i> 备注信息
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($memberPdtModel->remark)): ?>
                                        <div class="mb-3">
                                            <strong>用户备注：</strong>
                                            <div class="mt-1 p-2 bg-light border rounded">
                                                <?= Html::encode($memberPdtModel->remark) ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if (!empty($memberPdtModel->note)): ?>
                                        <div>
                                            <strong>配置备注说明：</strong>
                                            <div class="mt-1 p-2 bg-light border rounded">
                                                <?= Html::encode($memberPdtModel->note) ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

            <?php else: ?>
                <!-- 如果没有关联的产品数据，显示基础服务器信息 -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 该服务器暂未关联产品配置信息，显示基础服务器信息。
                </div>

                <?= DetailView::widget([
                    'model'      => $model,
                    'options'    => ['class' => 'table table-striped table-bordered detail-view'],
                    'attributes' => [
                        'id',
                        'server_name',
                        'uid',
                        'cluster_id',
                        'order_id',
                        'order_no',
                        [
                            'attribute' => 'order_amount',
                            'format'    => 'currency',
                        ],
                        [
                            'attribute' => 'upgrade_amount',
                            'format'    => 'currency',
                        ],
                        [
                            'attribute' => 'renew_amount',
                            'format'    => 'currency',
                        ],
                        'renew_mod',
                        [
                            'attribute' => 'renew_auto',
                            'value'     => $model->renew_auto ? '是' : '否',
                        ],
                        'node',
                        'vmid',
                        'tpl_vmid',
                        'ip',
                        'mac',
                        [
                            'attribute' => 'server_detail',
                            'format'    => 'ntext',
                        ],
                        [
                            'attribute' => 'network_detail',
                            'format'    => 'ntext',
                        ],
                        [
                            'attribute' => 'extend_detail',
                            'format'    => 'ntext',
                        ],
                        [
                            'attribute' => 'tasks_detail',
                            'format'    => 'ntext',
                        ],
                        'status_server',
                        'status_power',
                        [
                            'attribute' => 'status_net_off',
                            'value'     => $model->status_net_off ? '是' : '否',
                        ],
                        [
                            'attribute' => 'server_remark',
                            'format'    => 'ntext',
                        ],
                        [
                            'attribute' => 'tips_time',
                            'format'    => 'datetime',
                        ],
                        'reinstall_count',
                        [
                            'attribute' => 'reinstall_time',
                            'format'    => 'datetime',
                        ],
                        'begin_at',
                        'end_at',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                    ],
                ]) ?>
            <?php endif; ?>

        </div>
    </div>
</div>
