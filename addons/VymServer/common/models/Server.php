<?php

namespace addons\VymServer\common\models;

use common\behaviors\MerchantStoreBehavior;
use common\helpers\StringHelper;
use common\models\member\Member;
use common\models\merchant\Merchant;
use Yii;
use addons\VymDesen\backend\models\IdlePdt;
use addons\VymDesen\common\models\Member\MemberPdt;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "{{%addon_server_list}}".
 *
 * @property int         $id
 * @property int|null    $merchant_id 商户ID
 * @property int|null    $store_id    店铺ID
 * @property string      $unionid     业务单号
 * @property int|null    $pdt_id      Desen产品ID
 * @property int|null    $mpdt_id     Desen业务ID
 * @property int|null    $kf_id       客服ID
 * @property int         $user_id     会员ID
 * @property string|null $created_at  创建时间
 * @property string|null $updated_at  更新时间
 * @property string|null $deleted_at  删除时间
 */
class Server extends \yii\db\ActiveRecord
{

    use MerchantStoreBehavior;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%addon_server_list}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['created_at', 'updated_at', 'deleted_at'], 'default', 'value' => null],
            [['user_id'], 'default', 'value' => 0],
            [['unionid'], 'string'],
            [['merchant_id', 'store_id', 'pdt_id', 'mpdt_id', 'kf_id', 'user_id'], 'integer'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'          => 'ID',
            'merchant_id' => '商户ID',
            'store_id'    => '店铺ID',
            'unionid'     => '业务单号',
            'pdt_id'      => 'Desen产品ID',
            'mpdt_id'     => 'Desen业务ID',
            'kf_id'       => '客服ID',
            'user_id'     => '会员ID',
            'created_at'  => '创建时间',
            'updated_at'  => '更新时间',
            'deleted_at'  => '删除时间',
        ];
    }

    /**
     * 关联Desen自营产品
     * @return ActiveQuery
     */
    public function getIdlePdt()
    {
        return $this->hasOne(IdlePdt::class, ['id' => 'pdt_id']);
    }

    /**
     * 关联Desen销售业务
     * @return ActiveQuery
     */
    public function getMemberPdt()
    {
        return $this->hasOne(MemberPdt::class, ['id' => 'mpdt_id']);
    }

    /**
     * 关联会员
     * @return ActiveQuery
     */
    public function getMember()
    {
        return $this->hasOne(Member::class, ['ds_uid' => 'user_id']);
    }

    /**
     * 关联会员
     * @return ActiveQuery
     */
    public function getMerchant()
    {
        return $this->hasOne(Merchant::class, ['id' => 'merchant_id']);
    }


    /**
     * @param bool $insert
     *
     * @return bool
     */
    public function beforeSave($insert)
    {
        $this->created_at = StringHelper::intToDate($this->created_at);
        $this->updated_at = StringHelper::intToDate(time());

        return parent::beforeSave($insert);
    }

    /**
     * 创建记录
     *
     * @param $data
     *
     * @return bool
     * @throws \yii\db\Exception
     */
    public static function create($data)
    {
        $model = new self();
        $model->setAttributes($data);
        return $model->save();
    }
}
