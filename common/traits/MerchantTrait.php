<?php

namespace common\traits;

use common\helpers\MerchantHelper;
use yii\db\ActiveQuery;

trait MerchantTrait
{
    /**
     * 获取商户ID
     *
     * @return int
     */
    public abstract function getMerchantId();

    /**
     * 添加商户ID的查询条件
     * @param $dataProvider
     */
    protected function addQueryParam($dataProvider, $alias = null, $closer = null){
        $merId = $this->getMerchantId();
        if(MerchantHelper::isValidMerId($merId)){
            $query = $dataProvider->query;
            $key = $alias ? $alias.'.merchant_id' : 'merchant_id';
            if($alias){
                $query->alias($alias);
            }
            $query->andWhere([$key => $merId]);
            if($closer){
                $closer($dataProvider->query);
            }
        }
    }

    protected function fillQuery(ActiveQuery $query){
        $merId = $this->getMerchantId();
        if(MerchantHelper::isValidMerId($merId)){
            $query->andWhere(['merchant_id' => $merId]);
        }
    }
}