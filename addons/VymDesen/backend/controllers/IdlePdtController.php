<?php

namespace addons\VymDesen\backend\controllers;

use addons\VymDesen\backend\models\CustomPartitionSetting;
use addons\VymDesen\backend\models\IdlePdt;
use addons\VymDesen\backend\models\ImportTemporary;
use addons\VymDesen\backend\models\IpManagement\PdtIpClass;
use addons\VymDesen\backend\models\IpManagement\PdtIpNetwork;
use addons\VymDesen\backend\models\IpRecord;
use addons\VymDesen\backend\models\Log\IdleMachineOperationRecord;
use addons\VymDesen\backend\models\Log\PdtLog;
use addons\VymDesen\backend\models\Provider;
use addons\VymDesen\backend\models\ReinstallConfirm;
use addons\VymDesen\backend\models\TestServer;
use addons\VymDesen\backend\models\UploadForm;
use addons\VymDesen\backend\models\UserAdmin\UserAdmin;
use addons\VymDesen\backend\controllers\BaseController;
use Behat\Gherkin\Exception\Exception;
use addons\VymDesen\common\components\CloudBootApi;
use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\components\IpmiApi;
use addons\VymDesen\common\components\NewPipe\PipeLine;
use addons\VymDesen\common\components\SwitchFlowChartApi;
use addons\VymDesen\common\components\ZabbixApis;
use addons\VymDesen\common\models\CloudSystemModel;
use addons\VymDesen\common\models\CloudSystemModelClass;
use addons\VymDesen\common\models\Member\MemberPdt;
use addons\VymDesen\common\models\Pdt\IntranetSwitchManage;
use addons\VymDesen\common\models\Pdt\PdtCabinetManage;
use addons\VymDesen\common\models\Pdt\PdtIp;
use addons\VymDesen\common\models\Pdt\PdtManage;
use addons\VymDesen\common\models\Pdt\PdtManageType;
use addons\VymDesen\common\models\Pdt\PdtOperatsystem;
use addons\VymDesen\common\models\Pdt\PdtRoomMange;
use addons\VymDesen\common\models\Pdt\PdtType;
use addons\VymDesen\common\models\Pdt\SwitchLine;
use addons\VymDesen\common\models\Pdt\SwitchManage;
use addons\VymDesen\common\models\Server\ServerAttribute;
use addons\VymDesen\common\models\ServerDorecord;
use moonland\phpexcel\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Yii;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\web\UploadedFile;


#工作流
#导出需要的

/**
 *产品控制器
 *
 * <AUTHOR>
 *
 */
class IdlePdtController extends BaseController
{
    /**
     * 列表
     *
     * @return Ambigous <string, string>
     */
    public function actionIndex()
    {
        $IdlePdtModel = new IdlePdt();

        $IdlePdtModelQuery = $IdlePdtModel->getListAll([], true);

        #创建搜索条件
        $IdlePdtModel->createSearchWhere($IdlePdtModelQuery, $this->get());
        //分页
        $iCount = $IdlePdtModelQuery->count();//echo $iCount;
        $oPage  = DataHelper::getPage($iCount);

        $arrRes = $IdlePdtModelQuery->offset($oPage->offset)->limit($oPage->limit)->orderBy('id desc')->asArray()->all();#die(print_r($arrRes));

        //获取机房列表
        $RoomManageModel = new PdtRoomMange();
        $roomRes         = $RoomManageModel->find()->where(['provider' => 0])->orderBy('orders asc,id desc')->asArray()->all();

        //获取机柜
        $PdtCabinetManageModel = new PdtCabinetManage();
        if ($this->get('room_id') == "") {
            $cabinetRes = [];
        } else {
            $condition  = ['room_id' => $this->get('room_id')];
            $cabinetRes = $PdtCabinetManageModel->getListAll($condition);
        }

        //获取产品配置类别
        $PdtManageModel = new PdtManage();
        if ($this->get('server_type_id') == "") {
            $pdtRes = $PdtManageModel->getListAll();
        } else {
            $condition1 = ['pdt_type_id' => $this->get('server_type_id')];
            $pdtRes     = $PdtManageModel->getListAll($condition1);
        }

        #获取服务器分类
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeList  = $PdtManageTypeModel->find()->asArray()->all();
        //获取服务器状态属性
        $ServerAttributeModel = new ServerAttribute();
        $ServerAttributeRes   = $ServerAttributeModel->getListAll();
        return $this->render('index',
            [
                'arrRes'             => $arrRes,
                'roomRes'            => $roomRes,
                'cabinetRes'         => $cabinetRes,
                'pdtRes'             => $pdtRes,
                'PdtManageTypeList'  => $PdtManageTypeList,
                'ServerAttributeRes' => $ServerAttributeRes,
                'iCount'             => $iCount,
                'page'               => $oPage,
            ]);
    }

    //用于Ifame
    public function actionSelect()
    {
        $IdlePdtModel = new IdlePdt();
        //$IdlePdtModelQuery = $IdlePdtModel->getListAll(['status'=>0,'attribute_id'=>1], true);    //print_r($this->get());die();
        $IdlePdtModelQuery = $IdlePdtModel->find()->select("*")->With('servertype')->With('pdtroom')->With('pdtcabinet')->
        With('pdtmanage')->With('pdttype')->With('serverattribute')->With('provider')->With('switch')->With('intranetswitch')->where(['in', 'attribute_id', [1]])->andwhere(['status' => 0])->orderBy('id desc');

        #创建搜索条件
        $IdlePdtModel->createSearchWhere($IdlePdtModelQuery, $this->get());

        $iCount = $IdlePdtModelQuery->count();//echo $iCount;

        $oPage = DataHelper::getPage($iCount);

        $arrRes = $IdlePdtModelQuery->offset($oPage->offset)->limit($oPage->limit)->orderBy('id desc')->asArray()->all();   //die(print_r($arrRes));

        //获取机房列表
        $RoomManageModel = new PdtRoomMange();

        if ($this->get('servicerprovider') != "" && $this->get('servicerprovider') == 0) {
            $roomRes = $RoomManageModel->getListAll(['provider' => 0]);
        } else {
            $roomRes = $RoomManageModel->getListAll();
        }

        //获取服务器分类
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeRes   = $PdtManageTypeModel->find()->AsArray()->All();

        return $this->render('select', ['arrRes' => $arrRes, 'page' => $oPage, 'iCount' => $iCount, 'roomRes' => $roomRes, 'PdtManageTypeRes' => $PdtManageTypeRes]);
    }

    /**
     *详情
     */
    public function actionInfoItem()
    {

        $IdlePdtModel         = new IdlePdt();
        $PdtManageModel       = new PdtManage();
        $PdtOperatsystemModel = new PdtOperatsystem();

        $id = $this->get('id');
        $id = intval($id);

        $arrRes = $IdlePdtModel->find()->With('servertype')->With('pdtroom')->With('pdtcabinet')->With('pdtmanage')->With('pdttype')->With('serverattribute')->
        With('provider')->With('switch')->With('intranetswitch')->Where(['id' => $id])->asArray()->one(); #print_r($arrRes);die();

        if (empty($arrRes))
            return $this->redirect(Url::to(['index']));
        //获取当前机器的配置
        $config = json_decode($arrRes['config'], true);                                                   //print_r($config);die();

        $RecordModel   = new ServerDorecord();
        $hasRecord     = $RecordModel->find()->where(['do_idleid' => $arrRes['id'], 'do_sn' => $arrRes['sn_code'], 'do_status' => '处理中'])->asArray()->one();
        $historySystem = $RecordModel->find()->where(['do_idleid' => $arrRes['id'], 'do_sn' => $arrRes['sn_code'], 'do_status' => '处理成功', 'do_operate' => '重装系统'])->orderBy('do_finish_time desc')->asArray()->one();

        $PdtManageList = $PdtManageModel->find()->where(['pdt_type_id' => $arrRes['server_type_id']])->AsArray()->all();

        $OperatsystemList = $PdtOperatsystemModel->find()->asArray()->all();

        /*#流量图
		$from = 'now-6h';
		$to = 'now';
		if( !empty($arrRes['switch'][0]) ) {

			$FlowChartPng_base = SwitchFlowChartApi::GetFlowChart($arrRes['switch'][0]['ip'],$arrRes['switch_port'], $from,$to);

			$dir = $_SERVER['DOCUMENT_ROOT']."/upload/flowchart/";
			if (!is_dir($dir)) {
				mkdir($dir,0777,true);
			}
			$png_name = str_replace('.','', $arrRes['switch'][0]['ip']).'_'.$arrRes['switch_port'];
			$fileName = $png_name.'.png';
			$FlowChartPng = $dir.$fileName;
			file_put_contents($FlowChartPng, $FlowChartPng_base);
			$FlowChartPng_link = '/upload/flowchart/'.$fileName;  //echo $pic_link;die;

		} else {
			$FlowChartPng_link = '';
		}*/
        # 获取服务器分类
        $PdtManageTypeModel = new PdtManageType();
        $PdtManageTypeList  = $PdtManageTypeModel->find()->orderBy('type_sort desc')->asArray()->all();
        return $this->render('info-item', [
            'arrRes'            => $arrRes,
            'config'            => $config,
            'serverDo'          => $hasRecord,
            'serverSystem'      => $historySystem,
            'PdtManageList'     => $PdtManageList,
            'OperatsystemList'  => $OperatsystemList,
            'PdtManageTypeList' => $PdtManageTypeList,
            #'FlowChartPng_link' => $FlowChartPng_link
        ]);
    }

    /**
     * 添加
     */
    public function actionCreate()
    {
        $IdlePdtModel    = new IdlePdt();
        $PdtIpModel      = new PdtIp();
        $MemberPdtModel  = new MemberPdt();
        $TestServerModel = new TestServer();
        $post            = $this->post();
        if ($post) {
            //根据选择的服务器供应商，获取将绑定的IP
            if ($post['servicerprovider'] == 0) {
                if (empty($post['have_ips'])) {
                    $ip         = [];
                    $IPResArray = $ip;
                } else {
                    $ip  = DataHelper::dotrim($post['have_ips']);
                    $ip2 = $ip;
                    //考虑为IP段，所以重新定义
                    foreach ($ip2 as $key => $value) {
                        if (strpos($value, '-') !== false) {
                            $value1 = explode('-', trim($value));//print_r($value1);
                            $num4   = substr(trim($value1[0]), strripos(trim($value1[0]), ".") + 1);
                            $str    = substr(trim($value1[0]), 0, strrpos(trim($value1[0]), '.'));
                            if ($num4 >= trim($value1[1])) {
                                $arrReturn = [
                                    'status' => 0,
                                    'info'   => 'IP段输入有误',
                                ];
                                return $this->renderJSON($arrReturn);
                            } else {
                                $num = trim($value1[1]) - ($num4);//echo $num;
                                for ($n = 0; $n <= $num; $n++) {
                                    $newnum = $num4 + $n;;
                                    $newip        = $str . "." . $newnum;
                                    $IPResArray[] = trim($newip);
                                }
                            }
                        } else {
                            $IPResArray[] = trim($value);
                        }
                    }
                }
                $scenario = "pdtadd_have";
                //提供商为自有时，将一些数据清空
                $post['provider_id'] = "";
            } elseif ($post['servicerprovider'] == 1) {
                $scenario = "pdtadd_provider";
                //提供商为供应商时，将一些数据清空
                $post['switch_location']   = "";
                $post['switch_port']       = "";
                $post['occupies_position'] = "";
                $post['cabinet_id']        = "";
                $post['property']          = "";

                if (empty($post['provider_ips'])) {
                    $ip         = [];
                    $IPResArray = [];
                } else {
                    $ip  = DataHelper::dotrim($post['provider_ips']);
                    $ip2 = $ip;
                    //考虑供应商的IP为IP段，所以重新定义
                    //获取详细的IP
                    foreach ($ip2 as $key => $value) {
                        if (strpos($value, '-') !== false) {
                            $value1 = explode('-', trim($value));//print_r($value1);
                            $num4   = substr(trim($value1[0]), strripos(trim($value1[0]), ".") + 1);
                            $str    = substr(trim($value1[0]), 0, strrpos(trim($value1[0]), '.'));
                            if ($num4 >= trim($value1[1])) {
                                $arrReturn = [
                                    'status' => 0,
                                    'info'   => 'IP段输入有误',
                                ];
                                return $this->renderJSON($arrReturn);
                            } else {
                                $num = trim($value1[1]) - ($num4);//echo $num;
                                for ($n = 0; $n <= $num; $n++) {
                                    $newnum = $num4 + $n;;
                                    $newip        = $str . "." . $newnum;
                                    $IPResArray[] = trim($newip);
                                }
                            }
                        } else {
                            $IPResArray[] = trim($value);
                        }
                    }

                    ##判断供应商时，IP是否使用（用户产品和测试服务器中查询）
                    if (!empty($IPResArray)) {
                        foreach ($IPResArray as $value) {
                            $MemberPdtIpAll = $MemberPdtModel->find()->select('unionid,ip,ip2')->where(['like', 'ip2', '"' . $value . '"'])->andWhere('status != -1')->asArray()->all();
                            if (!empty($MemberPdtIpAll)) {
                                return $this->renderJSON([
                                    'status' => 0,
                                    'info'   => 'IP地址 :' . $value . ' 已经被使用',
                                ]);
                            }
                            $TestServerIPAll = $TestServerModel->find()->select('ip,ip2')->where(['like', 'ip2', '"' . $value . '"'])->andWhere('status != 1')->andWhere('status != -1')->asArray()->all();
                            if (!empty($TestServerIPAll)) {
                                return $this->renderJSON([
                                    'status' => 0,
                                    'info'   => 'IP地址 :' . $value . ' 已经被使用(测试服务器)',
                                ]);
                            }
                        }
                    }
                    $PdtIpAll = $PdtIpModel->find()->where(['in', 'ip', $IPResArray])->asArray()->all();
                    if (!empty($PdtIpAll)) {
                        $PdtIpAll  = array_column($PdtIpAll, 'ip');
                        $res       = implode('，', $PdtIpAll);
                        $arrReturn = [
                            'status' => 0,
                            'info'   => 'IP地址：' . $res . '存在于IP库',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
            }
            #处理的得到的IP数组
            $ipArray1 = array_unique($ip);//去重 去空
            $ipArray  = array_filter($ipArray1);

            $ipArray2 = array_unique($IPResArray);
            $ip2Array = array_filter($ipArray2);
            #print_r($ipArray);print_r($ip2Array);

            #判断IP组2是否正常
            if (!empty($ip2Array)) {
                foreach ($ip2Array as $value) {
                    if (!filter_var(trim($value), FILTER_VALIDATE_IP)) {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => $value . '为不合法的IP地址',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
            }

            #判断IP所属机房是否与产品的机房一致（仅在为自有时）
            if ($post['servicerprovider'] == 0) {
                if (!empty($ip2Array)) {
                    $PdtIpAll = $PdtIpModel->find()->select('room_id')->where(['in', 'ip', $ip2Array])->asArray()->all();#print_r($PdtIpAll);die;
                    foreach ($PdtIpAll as $v) {
                        $v      = join(',', $v);
                        $temp[] = $v;
                    }
                    $temp = array_unique($temp); #去掉重复的字符串,也就是重复的一维数组
                    $temp = array_values($temp); #print_r($temp); #重新排序
                    if (count($temp) > 1) {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => 'IP中有属于其他机房的IP地址',
                        ];
                        return $this->renderJSON($arrReturn);
                    } else if (count($temp) == 1) {
                        if ($temp[0] != $post['room_id']) {
                            $arrReturn = [
                                'status' => 0,
                                'info'   => 'IP所属机房与产品选择机房不一致',
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                    }
                }
            }

            #将数组装为字符串  并赋值
            $ipRes       = trim(json_encode($ipArray, JSON_UNESCAPED_UNICODE));
            $ip2Res      = trim(json_encode($ip2Array, JSON_UNESCAPED_UNICODE));
            $post['ip']  = $ipRes;
            $post['ip2'] = $ip2Res;

            #获取选择的产品配置
            if ($post['pdt_id'] == "") {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '产品配置类别未选择',
                ];
                return $this->renderJSON($arrReturn);
            }
            //获取详细配置
            $serverconfig['cpu']             = $post['cpu'];
            $serverconfig['ram']             = $post['ram'];
            $serverconfig['hdd']             = $post['hdd'];
            $serverconfig['configbandwidth'] = $post['configbandwidth'];
            $serverconfig['ipnumber']        = $post['ipnumber'];
            if (!empty($post['defense'])) {
                $serverconfig['defense'] = $post['defense'];
            } else {
                $serverconfig['defense'] = "";
            }
            $serverconfig['operatsystem'] = $post['operatsystem'];
            $post['config']               = json_encode($serverconfig, JSON_UNESCAPED_UNICODE);//获取到配置，并解析成字符串

            #开启事务
            $transaction = \Yii::$app->db->beginTransaction();

            #进入场景
            $IdlePdtModel->scenario   = $scenario;
            $IdlePdtModel->attributes = $post;
            #数据验证
            if (!$IdlePdtModel->validate()) {
                #回滚事务
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => $IdlePdtModel->errors,
                ];
                return $this->renderJSON($arrReturn);
            }
            //添加是否是自有服务器，判断是否选择了IP   然后将库里面的IP改为正在使用中  同时添加IP记录
            if (!empty($ip2Array) && $post['servicerprovider'] == 0) {
                #$modifyRes = $PdtIpModel->modifyIPStatus($ip2Array,"1");
                $updatePdtIPRes = $PdtIpModel->updateAll(['status' => 1, 'update_time' => time()], ['ip' => $ip2Array]);

                $IpRecordModel  = new IpRecord();
                $describe       = "管理员：" . $this->getAdminInfo('uname') . " 通过添加自有服务器，进行了IP使用";
                $addIpRecordRes = $IpRecordModel->addrecord(null, $IdlePdtModel->ipmi_ip, $ip2Array, $describe, $this->getAdminInfo('admin_id'), $this->getAdminInfo('uname'));
                if (!$addIpRecordRes) {
                    #回滚事务
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => "IP记录添加失败",
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }
            if ($IdlePdtModel->doadd(0)) {
                $transaction->commit();
                return $this->renderJSON([
                    'status' => 1,
                    'info'   => '添加产品成功',
                ]);
            } else {
                #回滚事务
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => '添加产品失败',
                ]);
            }
            return $this->renderJSON($arrReturn);

        } else {
            //获取机房  默认显示自有的
            $RoomManageModel = new PdtRoomMange();
            $roomRes         = $RoomManageModel->getListAll(['provider' => 0]);
            //添加产品时，机柜为空，选择机房出现
            $cabinetRes = [];
            //获取有效的产品分组
            $PdtTypeModel = new PdtType();
            $PdtTypeRes   = $PdtTypeModel->getValidListAll();
            //获取服务器属性
            $ServerAttributeModel = new ServerAttribute();
            $ServerAttRes         = $ServerAttributeModel->getListAll();
            //获取服务器分类
            $PdtManageTypeModel = new PdtManageType();
            $PdtManageTypeRes   = $PdtManageTypeModel->find()->AsArray()->All();

            return $this->render('create', [
                    'roomRes'          => $roomRes,
                    'cabinetRes'       => $cabinetRes,
                    'PdtTypeRes'       => $PdtTypeRes,
                    'ServerAttRes'     => $ServerAttRes,
                    'PdtManageTypeRes' => $PdtManageTypeRes,
                ]
            );
        }

    }

    /**
     * 修改信息
     */
    public function actionUpdate()
    {
        $IdlePdtModel    = new IdlePdt();
        $PdtLogModel     = new PdtLog();
        $PdtManageModel  = new PdtManage();
        $PdtIpModel      = new PdtIp();
        $MemberPdtModel  = new MemberPdt();
        $TestServerModel = new TestServer();
        if (Yii::$app->request->post()) {
            $id = $this->post('id');
            $id = intval($id);

            $post         = DataHelper::dotrim($this->post());        #print_r($post);die();
            $IdlePdtQuery = $IdlePdtModel->findOne($id);              #print_r($IdlePdtQuery);die();

            if ($post['pdt_id'] == "") {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '产品配置类别未选择',
                ];
                return $this->renderJSON($arrReturn);
            }

            //根据选择的服务器供应商
            if ($post['servicerprovider'] == 0) {
                $scenario = "pdtmodify_have";
                #提供商为自有时，将一些数据清空
                $post['provider_id'] = "";

            } elseif ($post['servicerprovider'] == 1) {
                $scenario = "pdtmodify_provider";
                #提供商为供应商时，将一些数据清空
                $post['switch_location']   = "";
                $post['switch_port']       = "";
                $post['occupies_position'] = "";
                $post['cabinet_id']        = "";
                $post['property']          = "";
            }

            #获取详细配置
            $serverconfig           = json_decode($IdlePdtQuery->config, true);
            $post['config']         = json_encode($serverconfig, JSON_UNESCAPED_UNICODE);//获取到配置，并解析成字符串//echo $post['config'];die();
            $post['real_bandwidth'] = $serverconfig['configbandwidth'];

            $PdtLogModel = new PdtLog();

            //获取服务器配置信息
            $PdtManageModelRes = $PdtManageModel->decompose($post['pdt_id']);            //die(print_r($PdtManageModelRes));

            $PdtTypeModel = new PdtType();
            $PdtTypeRes   = $PdtTypeModel->getRowById($post['type_id']);
            #开启事务
            $transaction = Yii::$app->db->beginTransaction();
            #进入场景
            $IdlePdtQuery->scenario   = $scenario;
            $IdlePdtQuery->attributes = $post; #print_r($post);die();

            if (empty($IdlePdtQuery->dirtyAttributes)) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '未修改信息',
                ];
                return $this->renderJSON($arrReturn);
            }
            #数据验证
            if (!$IdlePdtQuery->validate()) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => $IdlePdtQuery->errors,
                ];
                return $this->renderJSON($arrReturn);
            }

            #判断当前的闲置产品是否是使用状态，如果是，将需同步修改到用户产品
            $MemberPdtModel = new MemberPdt();
            $MemberPdtQuery = $MemberPdtModel->find()->where(['idle_id' => $id])->andWhere('status != -1')->asArray()->one();

            if ($MemberPdtQuery && $IdlePdtQuery->status == "1") {
                if ($MemberPdtQuery['status'] != 1) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '机器绑定的用户产品不再正常状态中',
                    ];
                    return $this->renderJSON($arrReturn);
                }
                $MemberPdt_config = json_decode($MemberPdtQuery['config'], true);
                $config           = json_decode($post['config'], true);

                if (isset($MemberPdt_config['requirement_bandwidth'])) {
                    $config['requirement_bandwidth'] = $MemberPdt_config['requirement_bandwidth'];
                } else {
                    $config['requirement_bandwidth'] = $MemberPdtQuery['bandwidth'];
                }

                $post['config'] = json_encode($config, JSON_UNESCAPED_UNICODE);

                $updatememberPdtRes = $MemberPdtModel->doupdate($MemberPdtQuery['id'], $post);
                if (!$updatememberPdtRes) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '用户产品信息同步更新失败',
                    ];
                    return $this->renderJSON($arrReturn);
                }
            } else {

            }

            #同时判断当前闲置产品是否是在测试中
            $TestServerModel = new TestServer();
            $TestServerQuery = $TestServerModel->find()->where(['idle_id' => $id])->andwhere('status != 1')->andwhere('status != -1')->asArray()->one();
            if (!empty($TestServerQuery) && $IdlePdtQuery->status != "0") {
                if ($MemberPdtQuery['TestServerQuery'] != 0) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '机器绑定的测试机不在正常测试状态中',
                    ];
                    return $this->renderJSON($arrReturn);
                }
                $TestServer_config = json_decode($TestServerQuery['config'], true);
                $config            = json_decode($post['config'], true);

                if (isset($TestServer_config['requirement_bandwidth'])) {
                    $config['requirement_bandwidth'] = $TestServer_config['requirement_bandwidth'];
                } else {
                    $config['requirement_bandwidth'] = $TestServerQuery['bandwidth'];
                }
                $post['config'] = json_encode($config, JSON_UNESCAPED_UNICODE);

                $updateTestServerRes = $TestServerModel->doupdate($TestServerQuery['id'], $post);
                if (!$updateTestServerRes) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '测试服务器同步更新失败',
                    ];
                    return $this->renderJSON($arrReturn);
                }
            } else {

            }

            $newModel   = $IdlePdtQuery->Attributes;                                                //print_r($newModel);
            $oldModel   = $IdlePdtQuery->oldAttributes;                                             //print_r($oldModel);
            // $arrayModel = array_diff_assoc($IdlePdtQuery->Attributes, $IdlePdtQuery->oldAttributes);//print_r($arrayModel);
            // $message    = "管理员" . $this->getAdminInfo('uname') . "修改了闲置产品表ID为" . $id . "的信息，";

            if ($IdlePdtQuery->update(false)) {
                $transaction->commit();
                $arrReturn = [
                    'status' => 1,
                    'info'   => '信息修改成功',
                ];
            } else {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => '未修改信息',
                ];
            }
            return $this->renderJSON($arrReturn);
        } else {
            $id = $this->get('id');
            $id = intval($id);

            $arrRes = $IdlePdtModel->getRowById($id);       //print_r($arrRes);die();
            if (empty($arrRes))
                return $this->redirect(Url::to(['index']));
            //获取当前机器的配置
            $config = json_decode($arrRes['config'], true); //print_r($config);die();

            #获取当前机器的相关机房
            $RoomManageModel = new PdtRoomMange();
            $roomRes         = $RoomManageModel->getListAll(['provider' => $arrRes['servicerprovider']]);
            #获取当前机房下的机柜
            $PdtCabinetManageModel = new PdtCabinetManage();
            $cabinetRes            = $PdtCabinetManageModel->getRowByRoomId($arrRes["room_id"]);
            #获取当前机房下产品Model  根据是自有还是供应商来判别
            if ($arrRes['servicerprovider'] == 0) {
                $PdtManageList = $PdtManageModel->find()->where(new Expression('FIND_IN_SET(:pdt_type_id, pdt_type_id)'))->addParams([':pdt_type_id' => $arrRes['server_type_id']])->asArray()->all();
            } elseif ($arrRes['servicerprovider'] == 1) {
                $PdtManageList = $PdtManageModel->getListAll();
            }

            #获取服务器分类
            $PdtManageTypeModel = new PdtManageType();
            $PdtManageTypeRes   = $PdtManageTypeModel->find()->AsArray()->All();
            //获取有效的产品分组
            $PdtTypeModel          = new PdtType();
            $PdtTypeRes            = $PdtTypeModel->getValidListAll();
            $arrRes['extend_data'] = json_decode(ArrayHelper::getValue($arrRes, 'extend_data') ?: '{}', true);
            return $this->render('item', [
                'arrRes'           => $arrRes,
                'roomRes'          => $roomRes,
                'PdtManageTypeRes' => $PdtManageTypeRes,
                'cabinetRes'       => $cabinetRes,
                'PdtManageList'    => $PdtManageList,
                'config'           => $config,
                'PdtTypeRes'       => $PdtTypeRes,
            ]);
        }
    }

    #修改配置
    public function actionChangeconfig()
    {
        Yii::$app->request->isAjax || die('error');

        $IdlePdtModel = new IdlePdt();
        $post         = $this->post();

        $IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $post['id']])->one();

        if ($IdlePdtQuery->status != 0) {
            $arrReturn = ['status' => 0, 'info' => '当前机器状态不能做此操作'];
            return $this->renderJSON($arrReturn);
        }

        foreach ($post['config'] as $val) {
            if ($val == '' || !isset($val)) {
                $arrReturn = ['status' => 0, 'info' => '配置参数不能为空'];
                return $this->renderJSON($arrReturn);
            }
        }

        if ($IdlePdtQuery->server_type_id != $post['server_type_id']) {
            $IdlePdtQuery->server_type_id = $post['server_type_id'];
        }

        if ($post['pdt_id'] != '' && $post['pdt_id'] != null && isset($post['pdt_id'])) {
            if ($IdlePdtQuery->pdt_id != $post['pdt_id']) {
                $IdlePdtQuery->pdt_id = $post['pdt_id'];
            }
        } else {
            $arrReturn = ['status' => 0, 'info' => '产品配置类别必须选择'];
            return $this->renderJSON($arrReturn);
        }

        $IdlePdtQuery->config      = json_encode($post['config'], JSON_UNESCAPED_UNICODE);
        $IdlePdtQuery->update_time = time();
        if ($IdlePdtQuery->update()) {
            $arrReturn = ['status' => 1, 'info' => '更改配置成功'];
        } else {
            $arrReturn = ['status' => 0, 'info' => '更改配置失败'];
        }

        return $this->renderJSON($arrReturn);

    }

    //更改属性
    public function actionChangeAttributes()
    {
        $IdlePdtModel         = new IdlePdt();
        $ServerAttributeModel = new ServerAttribute();
        $MemberPdtModel       = new MemberPdt();
        $UserAdminModel       = new UserAdmin();
        if (Yii::$app->request->post()) {
            Yii::$app->request->isAjax || die('error');

            $post         = $this->post();
            $IdlePdtQuery = $IdlePdtModel->findOne($post['id']);
            if (empty($IdlePdtQuery)) {
                $arrReturn = ['status' => 0, 'info' => '未知的自有服务器'];
                return $this->renderJSON($arrReturn);
            }
            #判断当前的闲置产品是否是使用状态，如果是，则不能修改
            #$MemberPdtQuery = $MemberPdtModel->findOne(['idle_id'=>$post['id']]);//print_r($MemberPdtQuery);die();
            $MemberPdtQuery = $MemberPdtModel->find()->where(['idle_id' => $post['id']])->andwhere('status != -1')->asArray()->one();
            if ($MemberPdtQuery || $IdlePdtQuery->status != 0) {
                $arrReturn = ['status' => 0, 'info' => '该服务器正在使用，不能更改属性'];
                return $this->renderJSON($arrReturn);
            }

            if ($post['attribute_id'] != 4) {
                $post['test_server_admin']   = "";
                $post['test_server_remarks'] = "";
            } else {
                if ($post['test_server_admin'] == "" || $post['test_server_remarks'] == "") {
                    $arrReturn = ['status' => 0, 'info' => '请选择销售并填写备注'];
                    return $this->renderJSON($arrReturn);
                }
            }
            $IdlePdtQuery->attributes = $post;

            if ($IdlePdtQuery->update(false)) {
                $arrReturn = ['status' => 1, 'info' => '更改服务器属性成功'];
            } else {
                $arrReturn = ['status' => 0, 'info' => '服务器属性未做更改'];
            }

            return $this->renderJSON($arrReturn);

        } else {
            $id         = $this->get('id');
            $IdlePdtRes = $IdlePdtModel->find()->where(['id' => $id])->asArray()->one();
            //获取服务器属性
            $ServerAttRes = $ServerAttributeModel->getListAll();
            foreach ($ServerAttRes as $k => $v) {
                if ($v['name'] == "已售服务器") {
                    unset($ServerAttRes[$k]);
                }
            }
            #获取销售
            $UserAdminRes = $UserAdminModel->find()->where(new Expression('FIND_IN_SET(:roles, roles)'))->addParams([':roles' => 4])->asArray()->all();
            return $this->render('change-attributes', [
                'IdlePdtRes'   => $IdlePdtRes,
                'ServerAttRes' => $ServerAttRes,
                'UserAdminRes' => $UserAdminRes,
            ]);
        }
    }

    #更改机器状态
    public function actionChangeMachinestatus()
    {
        Yii::$app->request->isAjax || die('error');

        $IdlePdtModel = new IdlePdt();

        $post = $this->post();

        $IdlePdtQuery = $IdlePdtModel->findOne($post['idle_id']);

        if (empty($IdlePdtQuery)) {
            $arrReturn = ['status' => 0, 'info' => '未知的自有服务器'];
            return $this->renderJSON($arrReturn);
        }

        if ($IdlePdtQuery->machine_status == $post['machine_status']) {
            $arrReturn = ['status' => 0, 'info' => '状态未改变'];
            return $this->renderJSON($arrReturn);
        }

        $IdlePdtQuery->machine_status = $post['machine_status'];

        if ($IdlePdtQuery->update()) {
            $arrReturn = ['status' => 1, 'info' => '更改成功'];
        } else {
            $arrReturn = ['status' => 0, 'info' => '更改失败'];
        }

        return $this->renderJSON($arrReturn);

    }

    /**
     *    更换IP
     **/
    public function actionChangeip()
    {

        $PdtIpModel        = new PdtIp();
        $IdlePdtModel      = new IdlePdt();
        $MemberPdtModel    = new MemberPdt();
        $TestServerModel   = new TestServer();
        $PdtIpNetworkModel = new PdtIpNetwork();
        $PdtIpClassModel   = new PdtIpClass();
        $SwitchManageModel = new SwitchManage();
        $SwitchLineModel   = new SwitchLine();

        $post = $this->post();
        if ($post) {
            $IdlePdtRes = $IdlePdtModel->find()->where(['id' => $post['id']])->asArray()->one();
            if ($IdlePdtRes['status'] != 0) {
                $arrReturn = ['status' => 0, 'info' => '当前状态下不允许更换IP'];
                return $this->renderJSON($arrReturn);
            }

            $oldIP = json_decode($IdlePdtRes['ip2'], true);
            if (empty($oldIP) || !$oldIP) {
                $oldIP = [];
            }
            $IpArr = DataHelper::dotrim(ArrayHelper::getValue($post, 'ips'));
            #当提交的IP不为空时
            if (!empty($IpArr)) {
                //将提交的IP进行验证
                if ($IdlePdtRes['servicerprovider'] == 0) {
                    $ip2        = $IpArr;
                    $RetuenData = DataHelper::splitIP($ip2);
                    if ($RetuenData['status'] == 0) {
                        $arrReturn = ['status' => 0, 'info' => $RetuenData['info']];
                        return $this->renderJSON($arrReturn);
                    }
                    $IPResArray = $RetuenData['data'];

                } elseif ($IdlePdtRes['servicerprovider'] == 1) {
                    $ip2        = $IpArr;
                    $RetuenData = DataHelper::splitIP($ip2);
                    if ($RetuenData['status'] == 0) {
                        $arrReturn = ['status' => 0, 'info' => $RetuenData['info']];
                        return $this->renderJSON($arrReturn);
                    }
                    $IPResArray = $RetuenData['data'];

                    $diffArray = array_diff($IPResArray, $oldIP);

                    if (!empty($diffArray)) {
                        $check_return = DataHelper::CheckIP_isused($diffArray);
                        if ($check_return['status'] == 0) {
                            $arrReturn = ['status' => 0, 'info' => $check_return['info']];
                            return $this->renderJSON($arrReturn);
                        }
                    }
                }

                if (count($IPResArray) >= count($oldIP)) {
                    if (empty(array_diff($IPResArray, $oldIP))) {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => '未有IP改变',
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                } elseif (count($oldIP) >= count($IPResArray)) {
                    if (empty(array_diff($oldIP, $IPResArray))) {
                        $arrReturn = ['status' => 0, 'info' => '未有IP改变'];
                        return $this->renderJSON($arrReturn);
                    }
                }

                #处理的得到的IP数组1  //去重 去空
                $ipArray1 = array_unique($IpArr);
                $ipArray  = array_filter($ipArray1);

                $ip2Array = $IPResArray;

            } else {
                #当提交的IP为空时
                $ipArray  = [];
                $ip2Array = [];
            }

            #判断IP组2是否正常(再一次验证)
            if (!empty($ip2Array)) {

                if ($IdlePdtRes['servicerprovider'] == 0) {#判断IP所属机房是否与闲置库机器一致（仅在为自有时）
                    $PdtIpAll = $PdtIpModel->find()->select('room_id, line_type_id,vlan')->where(['in', 'ip', $ip2Array])->asArray()->all();

                    $temp = array_column($PdtIpAll, 'room_id');#print_r($temp);exit;

                    $temp = array_unique($temp); #去掉重复的字符串,也就是重复的一维数组
                    $temp = array_values($temp); #重新排序
                    if (count($temp) > 1) {
                        $arrReturn = ['status' => 0, 'info' => 'IP中有属于其他机房的IP地址'];
                        return $this->renderJSON($arrReturn);
                    } else if (count($temp) == 1) {
                        if ($temp[0] != $IdlePdtRes['room_id']) {
                            $arrReturn = ['status' => 0, 'info' => 'IP所属机房与闲置机器选择机房不一致'];
                            return $this->renderJSON($arrReturn);
                        }
                    }
                    $line_typeList = array_unique(array_column($PdtIpAll, 'line_type_id'));

                    if (count($line_typeList) > 1) {
                        $arrReturn = ['status' => 0, 'info' => '所选IP与其他IP的线路类型不匹配'];
                        return $this->renderJSON($arrReturn);
                    }

                    #获取机器对应交换机的线路信息
                    $SwitchLineList = $SwitchLineModel->find()->select('line_type_id,switch_vlan')->where(['switch_id' => $IdlePdtRes['switch_location'], 'line_type_id' => $line_typeList[0]])->asArray()->one();
                    if (empty($SwitchLineList)) {
                        $arrReturn = ['status' => 0, 'info' => '机器对应的交换机线路中未有IP的线路类型'];
                        return $this->renderJSON($arrReturn);
                    }
                }
            }

            #开启事务
            $transaction = Yii::$app->db->beginTransaction();

            #发起工作流
            $callBackRes = PipeLine::Line_IdleMachineReplaceIP($IdlePdtRes['id'], $IpArr);
            if ($callBackRes['status']) {
                $transaction->commit();
                $arrReturn = ['status' => 1, 'info' => $callBackRes['info']];
            } else {
                $transaction->rollBack();
                $arrReturn = ['status' => 0, 'info' => $callBackRes['info']];
            }

            return $this->renderJSON($arrReturn);

        } else {
            $id         = $this->get('id');
            $IdlePdtRes = $IdlePdtModel->find()->where(['id' => $id])->asArray()->one();
            $IPArr      = json_decode(ArrayHelper::getValue($IdlePdtRes, 'ip'), true);

            $IpClassList = $PdtIpClassModel->find()->asArray()->all();
            #网段
            $IpNetworkList = $PdtIpNetworkModel->find()->where(['room_id' => ArrayHelper::getValue($IdlePdtRes, 'room_id')])->asArray()->all();
            return $this->render('changeip', [
                'IdlePdtRes'    => $IdlePdtRes,
                'IPArr'         => $IPArr,
                'IpClassList'   => $IpClassList,
                'IpNetworkList' => $IpNetworkList,
            ]);
        }
    }

    //更改备注
    public function actionModifyRemarks()
    {
        Yii::$app->request->isAjax || die('error');
        $IdlePdtModel = new IdlePdt();
        $post         = $this->post();
        $IdlePdtQuery = $IdlePdtModel->findOne($post['id']);
        if (empty($IdlePdtQuery)) {
            $arrReturn = [
                'status' => 0,
                'info'   => '未知的自有服务器',
            ];
            return $this->renderJSON($arrReturn);
        }

        $IdlePdtQuery->attributes = $post;

        if ($IdlePdtQuery->update()) {
            $arrReturn = [
                'status' => 1,
                'info'   => '备注信息更新成功',
            ];
        } else {
            $arrReturn = [
                'status' => 0,
                'info'   => '备注信息未做更改',
            ];
        }
        return $this->renderJSON($arrReturn);
    }

    /**
     * 删除数据处理方法
     */
    public function actionDel()
    {
        Yii::$app->request->isAjax || die('error');

        $PdtLogModel  = new PdtLog();
        $IdlePdtModel = new IdlePdt();
        $PdtIpModel   = new PdtIp();

        if ($this->post('id') == '')
            return $this->renderJSON(['status' => 0, 'info' => '操作失效']);

        $IdlePdtRes = $IdlePdtModel->findOne($this->post('id'));
        //未找到对应信息
        if (empty($IdlePdtRes)) {
            $arrReturn = [
                'status' => 0,
                'info'   => '操作失效，未找到对应产品信息',
            ];
            return $this->renderJSON($arrReturn);
        }

        $MemberPdtModel = new MemberPdt();
        $MemberPdtQuery = $MemberPdtModel->findOne(['idle_id' => $this->post('id')]);

        if ($MemberPdtQuery && $IdlePdtRes['status'] != 0) {
            $arrReturn = ['status' => 0, 'info' => '操作失效，产品属于绑定中'];
            return $this->renderJSON($arrReturn);
        }
        #同时判断当前闲置产品是否是在测试中
        $TestServerModel = new TestServer();
        $TestServerQuery = $TestServerModel->find()->where(['idle_id' => $this->post('id')])->andwhere("status != '-1'")->andwhere("status != '1'")->asArray()->one();
        if (!empty($TestServerQuery)) {
            $arrReturn = ['status' => 0, 'info' => '操作失效，产品属于绑定于测试服务器中'];
            return $this->renderJSON($arrReturn);
        }

        #开启事务
        $transaction = \Yii::$app->db->beginTransaction();
        #获取IP，如果绑定有IP，将IP改为闲置
        $IP = json_decode($IdlePdtRes['ip2'], true);
        if ($IdlePdtRes['servicerprovider'] == 0) {
            if (!empty($IP)) {
                #$IPsystemRes = $PdtIpModel->modifyIPStatus($IP,0);
                $updatePdtIPRes = $PdtIpModel->updateAll(['status' => 0], ['ip' => $IP]);
                if ($updatePdtIPRes != count($IP)) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => "IP状态修改失败",
                    ];
                    $transaction->rollBack();
                    return $this->renderJSON($arrReturn);
                }
            }
        }
        #执行删除
        if ($IdlePdtModel->del($this->post('id')) > 0) {
            $transaction->commit();
            $arrReturn = [
                'status' => 1,
                'info'   => '闲置产品信息删除成功',
            ];
        } else {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => '闲置产品信息删除失败',
            ];
        }
        return $this->renderJSON($arrReturn);
    }

    /*
     * 导出下载
     */
    public function actionExport()
    {

        $IdlePdtModel = new IdlePdt();

        $IdlePdtModelQuery = $IdlePdtModel->getListAll([], true);
        //print_r($this->post());die();
        #创建搜索条件
        $IdlePdtModel->createSearchWhere($IdlePdtModelQuery, $this->get());

        $arrRes = $IdlePdtModelQuery->orderBy('id desc')->asArray()->all();#die(print_r($arrRes));
        $models = [];
        foreach ($arrRes as $key => $value) {
            $models[$key]['id'] = $value['id'];
            if (!empty($value['servertype'])) {
                $models[$key]['server_type_id'] = $value['servertype'][0]['type_name'];
            }
            if (!empty($value['pdtroom'])) {
                $models[$key]['room_id'] = $value['pdtroom'][0]['name'];
            }
            if (!empty($value['pdtcabinet'])) {
                $models[$key]['cabinet_id'] = $value['pdtcabinet'][0]['name'];
            }
            if (!empty($value['serverattribute'])) {
                $models[$key]['attribute_id'] = $value['serverattribute'][0]['name'];
            }
            if (!empty($value['provider'])) {
                $models[$key]['provider_id'] = $value['provider'][0]['name'];
            }
            if (!empty($value['switch'])) {
                $models[$key]['switch_location'] = $value['switch'][0]['ip'];
            }
            if (!empty($value['intranetswitch'])) {
                $models[$key]['intranet_switch_location'] = $value['intranetswitch']['ip'];
            } else {
                $models[$key]['intranet_switch_location'] = '';
            }
            if (!empty($value['pdtmanage'])) {
                $models[$key]['pdt_id'] = $value['pdtmanage'][0]['name'];
            }
            if ($value['property'] == "0") {
                $models[$key]['property'] = "公司所属";
            } else {
                $models[$key]['property'] = "客户所属";
            }
            if (!empty($value['type_id'])) {
                $models[$key]['type_id'] = $value['pdttype'][0]['name'];
            }
            if ($value['status'] == "1") {
                $models[$key]['status'] = "使用中";
            } elseif ($value['status'] == "0") {
                $models[$key]['status'] = "闲置中";
            } elseif ($value['status'] == "2") {
                $models[$key]['status'] = "待定中";
            }

            $models[$key]['idle_name']        = $value['idle_name'];
            $models[$key]['servicerprovider'] = $value['servicerprovider'] ? '供应商提供' : '自有';

            $config = json_decode($value['config'], true);
            #$models[$key]['config'] = implode(",", $config);
            if (!empty($config)) {
                #$models[$key]['config'] = implode(",  ", $config);
                $models[$key]['cpu']             = $config['cpu'];
                $models[$key]['ram']             = $config['ram'];
                $models[$key]['hdd']             = $config['hdd'];
                $models[$key]['configbandwidth'] = $config['configbandwidth'];
                $models[$key]['ipnumber']        = $config['ipnumber'];
                $models[$key]['defense']         = $config['defense'];
                $models[$key]['operatsystem']    = $config['operatsystem'];
            } else {
                $models[$key]['cpu']             = '';
                $models[$key]['ram']             = '';
                $models[$key]['hdd']             = '';
                $models[$key]['configbandwidth'] = '';
                $models[$key]['ipnumber']        = '';
                $models[$key]['defense']         = '';
                $models[$key]['operatsystem']    = '';
            }

            $models[$key]['occupies_position']    = $value['occupies_position'];
            $models[$key]['switch_port']          = $value['switch_port'];
            $models[$key]['intranet_switch_port'] = $value['intranet_switch_port'];
            $models[$key]['machine_model']        = $value['machine_model'];
            $models[$key]['remarks']              = $value['remarks'];
            $models[$key]['sn_code']              = $value['sn_code'];

            #$models[$key]['ip'] = implode(",", json_decode($value['ip'],true));

            if (!empty(json_decode($value['ip'], true))) {
                #$models[$key]['ip'] = implode(",  ", json_decode($value['ip'],true));
                $IParr = json_decode($value['ip'], true);
                $ip    = $IParr[0];
                if (strpos($ip, '-') !== false) {
                    $IParr2             = json_decode($value['ip2'], true);
                    $models[$key]['ip'] = $IParr2[0];
                } else {
                    $models[$key]['ip'] = $ip;
                }
            } else {
                $models[$key]['ip'] = "";
            }

            $models[$key]['ipmi_ip']   = $value['ipmi_ip'];
            $models[$key]['ipmi_type'] = $value['ipmi_type'];
            $models[$key]['ipmi_name'] = $value['ipmi_name'];
            $models[$key]['ipmi_pwd']  = $value['ipmi_pwd'];

        }

        $newExcel = new Spreadsheet;
        $objSheet = $newExcel->getActiveSheet(0);  //获取当前操作sheet的对象
        $objSheet->setTitle('自有机器信息表');            //设置当前sheet的标题
        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        #水平方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        #垂直方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        #设置列宽
        $newExcel->getActiveSheet()->getColumnDimension('A')->setWidth(6);
        $newExcel->getActiveSheet()->getColumnDimension('B')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('C')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('D')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('E')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('F')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('G')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('H')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('I')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('J')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('K')->setWidth(10);

        $newExcel->getActiveSheet()->getColumnDimension('L')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('M')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('N')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('O')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('P')->setWidth(12);
        $newExcel->getActiveSheet()->getColumnDimension('Q')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('R')->setWidth(25);

        $newExcel->getActiveSheet()->getColumnDimension('S')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('T')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('U')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('V')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('W')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('X')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('Y')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('Z')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('AA')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('AB')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('AC')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('AD')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('AE')->setWidth(10);

        #设置列名
        $objSheet->setCellValue('A1', 'ID号');
        $objSheet->setCellValue('B1', '产品名');
        $objSheet->setCellValue('C1', '服务器分类');
        $objSheet->setCellValue('D1', '机房');
        $objSheet->setCellValue('E1', '机柜');
        $objSheet->setCellValue('F1', '状态属性');
        $objSheet->setCellValue('G1', '服务提供商');
        $objSheet->setCellValue('H1', '供应商');
        $objSheet->setCellValue('I1', '产品配置类别');
        $objSheet->setCellValue('J1', '产品所属');
        $objSheet->setCellValue('K1', '类型');

        $objSheet->setCellValue('L1', 'CPU');
        $objSheet->setCellValue('M1', '内存大小');
        $objSheet->setCellValue('N1', '硬盘');
        $objSheet->setCellValue('O1', '带宽');
        $objSheet->setCellValue('P1', 'IP数');
        $objSheet->setCellValue('Q1', '防御流量');
        $objSheet->setCellValue('R1', '操作系统');

        $objSheet->setCellValue('S1', '所占机位');
        $objSheet->setCellValue('T1', '机器型号');
        $objSheet->setCellValue('U1', 'SN码');
        $objSheet->setCellValue('V1', '业务交换机');
        $objSheet->setCellValue('W1', '业务交换机端口');
        $objSheet->setCellValue('X1', '内网交换机');
        $objSheet->setCellValue('Y1', '内网交换机端口');
        $objSheet->setCellValue('Z1', 'IP地址');
        $objSheet->setCellValue('AA1', 'IPMI地址');
        $objSheet->setCellValue('AB1', 'IPMI类型');
        $objSheet->setCellValue('AC1', 'IPMI用户名');
        $objSheet->setCellValue('AD1', 'IPMI密码');
        $objSheet->setCellValue('AE1', '机器状态');
        $objSheet->setCellValue('AF1', '机器备注');

        $data = [];

        foreach ($models as $key => $val) {

            $k = $key + 2;

            $objSheet->setCellValue('A' . $k, $val['id']);
            $objSheet->setCellValue('B' . $k, $val['idle_name']);
            $objSheet->setCellValue('C' . $k, $val['server_type_id']);
            $objSheet->setCellValue('D' . $k, $val['room_id']);
            $objSheet->setCellValue('E' . $k, $val['cabinet_id']);
            $objSheet->setCellValue('F' . $k, $val['attribute_id']);
            $objSheet->setCellValue('G' . $k, $val['servicerprovider']);
            $objSheet->setCellValue('H' . $k, ArrayHelper::getValue($val, 'provider_id'));
            $objSheet->setCellValue('I' . $k, $val['pdt_id']);
            $objSheet->setCellValue('J' . $k, $val['property']);
            $objSheet->setCellValue('K' . $k, $val['type_id']);

            $objSheet->setCellValue('L' . $k, $val['cpu']);
            $objSheet->setCellValue('M' . $k, $val['ram']);
            $objSheet->setCellValue('N' . $k, $val['hdd']);
            $objSheet->setCellValue('O' . $k, $val['configbandwidth']);
            $objSheet->setCellValue('P' . $k, $val['ipnumber']);
            $objSheet->setCellValue('Q' . $k, $val['defense']);
            $objSheet->setCellValue('R' . $k, $val['operatsystem']);

            $objSheet->setCellValue('S' . $k, $val['occupies_position']);
            $objSheet->setCellValue('T' . $k, $val['machine_model']);
            $objSheet->setCellValue('U' . $k, $val['sn_code']);
            $objSheet->setCellValue('V' . $k, $val['switch_location']);
            $objSheet->setCellValue('W' . $k, $val['switch_port']);
            $objSheet->setCellValue('X' . $k, $val['intranet_switch_location']);
            $objSheet->setCellValue('Y' . $k, $val['intranet_switch_port']);
            $objSheet->setCellValue('Z' . $k, $val['ip']);
            $objSheet->setCellValue('AA' . $k, $val['ipmi_ip']);
            $objSheet->setCellValue('AB' . $k, $val['ipmi_type']);
            $objSheet->setCellValue('AC' . $k, $val['ipmi_name']);
            $objSheet->setCellValue('AD' . $k, $val['ipmi_pwd']);
            $objSheet->setCellValue('AE' . $k, ArrayHelper::getValue($val, 'status'));
            $objSheet->setCellValue('AF' . $k, $val['remarks']);

        }
        //设置第一栏的标题
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=自有机器信息表_" . date('Y-m-d', time()) . '.' . strtolower("Xlsx"));
        header('Cache-Control: max-age=0');
        $objWriter = IOFactory::createWriter($newExcel, 'Xlsx');
        $objWriter->save('php://output');
        die;
    }

    #cpu 资源下载
    public function actionResourcesExport()
    {
        $IdlePdtModel = new IdlePdt();

        $IdlePdtModelQuery = $IdlePdtModel->getListAll([], true);
        //print_r($this->post());die();
        #创建搜索条件
        $IdlePdtModel->createSearchWhere($IdlePdtModelQuery, $this->get());

        $IdleList = $IdlePdtModelQuery->orderBy('id desc')->asArray()->all();#die(print_r($arrRes));

        $data = [];
        #$data[0]['cpu'] = '';
        #$data[0]['total'] = 0;
        $cpuArr = [];

        foreach ($IdleList as $key => $val) {
            $config = json_decode($val['config'], true);
            $cpu    = trim($config['cpu']);
            if (!in_array($cpu, $cpuArr)) {
                $cpuArr[]             = $cpu;
                $data[$cpu]['cpu']    = $cpu;
                $data[$cpu]['total']  = 1;
                $data[$cpu]['unused'] = 0;
                $data[$cpu]['used']   = 0;
                $data[$cpu]['fault']  = 0;
                if ($val['attribute_id'] == 1) {
                    $data[$cpu]['unused'] += 1;
                } else if (in_array($val['attribute_id'], [2, 3, 4, 7])) {
                    $data[$cpu]['used'] += 1;
                } else if ($val['attribute_id'] == 5) {
                    $data[$cpu]['fault'] += 1;
                }
            } else {
                $data[$cpu]['total'] += 1;
                if ($val['attribute_id'] == 1) {
                    $data[$cpu]['unused'] += 1;
                } else if (in_array($val['attribute_id'], [2, 3, 4, 7])) {
                    $data[$cpu]['used'] += 1;
                } else if ($val['attribute_id'] == 5) {
                    $data[$cpu]['fault'] += 1;
                }
            }
        }

        $model = [];
        foreach ($data as $val) {
            $model[] = $val;
        }

        $newExcel = new Spreadsheet;
        $objSheet = $newExcel->getActiveSheet(0);  //获取当前操作sheet的对象
        $objSheet->setTitle('自有机器资源信息表');          //设置当前sheet的标题
        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        #水平方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        #垂直方向对齐
        $newExcel->getDefaultStyle()->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        #设置列宽
        $newExcel->getActiveSheet()->getColumnDimension('A')->setWidth(15);
        $newExcel->getActiveSheet()->getColumnDimension('B')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('C')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('D')->setWidth(10);
        $newExcel->getActiveSheet()->getColumnDimension('E')->setWidth(10);

        #设置列名
        $objSheet->setCellValue('A1', 'CPU');
        $objSheet->setCellValue('B1', '总数');
        $objSheet->setCellValue('C1', '闲置');
        $objSheet->setCellValue('D1', '已用');
        $objSheet->setCellValue('E1', '故障');

        foreach ($model as $key => $val) {
            $k = $key + 2;
            $objSheet->setCellValue('A' . $k, $val['cpu']);
            $objSheet->setCellValue('B' . $k, $val['total']);
            $objSheet->setCellValue('C' . $k, $val['unused']);
            $objSheet->setCellValue('D' . $k, $val['used']);
            $objSheet->setCellValue('E' . $k, $val['fault']);
        }
        //设置第一栏的标题

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=自有机器资源信息表" . date('Y-m-d', time()) . '.' . strtolower("Xlsx"));
        header('Cache-Control: max-age=0');
        $objWriter = IOFactory::createWriter($newExcel, 'Xlsx');
        $objWriter->save('php://output');
        die;
    }

    /*
     * 导入
     */
    public function actionImport()
    {
        $oSession = Yii::$app->getSession();
        $model    = new UploadForm();
        if (Yii::$app->request->post() && Yii::$app->request->isPost) {
            $model->excelfile = UploadedFile::getInstance($model, 'excelfile');

            $filepath = "upload/excel/";

            if ($model->excelfile) {
                if (!is_dir($filepath)) {
                    mkdir($filepath, 0777);
                    chmod($filepath, 0777);
                }
                if ($model->validate()) {
                    $excelfile = $model->excelfile;
                    $randname  = date("Y") . date("m") . date("d") . date("H") . date("i") . date("s") . rand(1000, 9999) . "." . $excelfile->getExtension();//文件名
                    $excelfile->saveAs($filepath . $randname);                                                                                               //上传
                    $uploadSuccessPath = $filepath . $randname;                                                                                              //路径
                } else {
                    $arrReturn = ['status' => 0, 'info' => '导入文件有误'];
                    $oSession->setFlash('return', $arrReturn);
                    return $this->redirect(Url::to(['idle-pdt/import']));
                }
            } else {
                $arrReturn = ['status' => 0, 'info' => '导入文件有误'];
                $oSession->setFlash('return', $arrReturn);
                return $this->redirect(Url::to(['idle-pdt/import']));
            }

            //读取文件
            $tag_data = Excel::import($uploadSuccessPath, [
                'setFirstRecordAsKeys' => true,
                'setIndexSheetByName'  => true,
                'getOnlySheet'         => 'sheet1',
            ]);
            #print_r($tag_data);#die();
            $data = [];
            if (!empty($tag_data)) {
                $key_name = array(
                    'id', 'idle_name', 'server_type_id', 'room_id', 'cabinet_id', 'attribute_id', 'servicerprovider', 'provider_id', 'pdt_id', 'property',
                    'type_id', 'cpu', 'ram', 'hdd', 'bandwidth', 'ipnumber', 'defense', 'operatsystem', 'occupies_position', 'machine_model', 'sn_code', 'switch_location', 'switch_port',
                    'intranet_switch_location', 'intranet_switch_port', 'ip', 'ipmi_ip', 'ipmi_type', 'ipmi_name', 'ipmi_pwd', 'status',
                );
                foreach ($tag_data as $key => $value) {
                    if (!empty($value)) {
                        //$value = array_values($value);
                        $value1       = array_combine($key_name, $value);
                        $value1['ip'] = json_encode(explode(',', $value1['ip']), JSON_UNESCAPED_UNICODE);
                        $data[$key]   = $value1;
                    }
                }

            } else {
                $arrReturn = ['status' => 0, 'info' => '导入文件为空'];
                $oSession->setFlash('return', $arrReturn);
                return $this->redirect(Url::to(['idle-pdt/import']));
            }
            #删除上传的文件
            if (!unlink($uploadSuccessPath)) {
                $arrReturn = ['status' => 0, 'info' => '文件处理失败'];
                $oSession->setFlash('return', $arrReturn);
                return $this->redirect(Url::to(['idle-pdt/import']));
            }

            Yii::$app->db->createCommand()->truncateTable(ImportTemporary::tableName())->execute();
            #开启事务
            $transaction = \Yii::$app->db->beginTransaction();
            if (!empty($data)) {
                try {
                    foreach ($data as $value2) {
                        $key_name             = array(
                            'id', 'idle_name', 'server_type_id', 'room_id', 'cabinet_id', 'attribute_id', 'servicerprovider', 'provider_id', 'pdt_id', 'property',
                            'type_id', 'config', 'occupies_position', 'machine_model', 'sn_code', 'switch_location', 'switch_port', 'intranet_switch_location', 'intranet_switch_port', 'ip', 'ipmi_ip', 'ipmi_type', 'ipmi_name', 'ipmi_pwd', 'status',
                        );
                        $ImportTemporaryModel = new ImportTemporary();

                        $ImportTemporaryModel->idle_name                = $value2['idle_name'];
                        $ImportTemporaryModel->server_type_id           = $value2['server_type_id'];
                        $ImportTemporaryModel->room_id                  = $value2['room_id'];
                        $ImportTemporaryModel->cabinet_id               = $value2['cabinet_id'];
                        $ImportTemporaryModel->attribute_id             = $value2['attribute_id'];
                        $ImportTemporaryModel->servicerprovider         = $value2['servicerprovider'];
                        $ImportTemporaryModel->provider_id              = $value2['provider_id'];
                        $ImportTemporaryModel->pdt_id                   = $value2['pdt_id'];
                        $ImportTemporaryModel->property                 = $value2['property'];
                        $ImportTemporaryModel->type_id                  = $value2['type_id'];
                        $ImportTemporaryModel->config                   = (string)$value2['cpu'] . ',' . $value2['ram'] . ',' . $value2['hdd'] . ',' . $value2['bandwidth'] . ',' . $value2['ipnumber'] . ',' . $value2['defense'] . ',' . $value2['operatsystem'];
                        $ImportTemporaryModel->occupies_position        = (string)$value2['occupies_position'];
                        $ImportTemporaryModel->machine_model            = (string)$value2['machine_model'];
                        $ImportTemporaryModel->sn_code                  = (string)$value2['sn_code'];
                        $ImportTemporaryModel->switch_location          = $value2['switch_location'];
                        $ImportTemporaryModel->switch_port              = (string)$value2['switch_port'];
                        $ImportTemporaryModel->intranet_switch_location = $value2['intranet_switch_location'];
                        $ImportTemporaryModel->intranet_switch_port     = (string)$value2['intranet_switch_port'];
                        $ImportTemporaryModel->ip                       = (string)$value2['ip'];
                        $ImportTemporaryModel->ipmi_ip                  = (string)$value2['ipmi_ip'];
                        $ImportTemporaryModel->ipmi_type                = (string)$value2['ipmi_type'];
                        $ImportTemporaryModel->ipmi_name                = (string)$value2['ipmi_name'];
                        $ImportTemporaryModel->ipmi_pwd                 = (string)$value2['ipmi_pwd'];
                        $ImportTemporaryModel->status                   = $value2['status'];
                        if ($ImportTemporaryModel->save()) {
                            $arrReturn = ['status' => 1, 'info' => '添加到临时成功'];

                        } else {
                            #事务回滚
                            $transaction->rollBack();
                            $arrReturn = ['status' => 0, 'info' => $ImportTemporaryModel->errors];
                            $oSession->setFlash('return', $arrReturn);
                            return $this->redirect(Url::to(['idle-pdt/import']));
                        }
                    }

                    $transaction->commit();

                } catch (Exception $e) {
                    $transaction->rollBack();
                }
            }

            return $this->redirect(Url::to(['idle-pdt/import-item']));

        } else {
            return $this->render('import');
        }

    }

    #开始导入或者取消导入
    public function actionImportItem()
    {
        $ImportTemporaryModel  = new ImportTemporary();
        $PdtRoomMangeModel     = new PdtRoomMange();
        $PdtCabinetManageModel = new PdtCabinetManage();
        $ServerAttributeModel  = new ServerAttribute();
        $PdtManageTypeModel    = new PdtManageType();
        $PdtIpModel            = new PdtIp();
        if (Yii::$app->request->post()) {
            $type = $this->post('type');

            if ($type == 'start_import') {
                #开启事务
                $transaction = \Yii::$app->db->beginTransaction();

                $ImportTemporaryRes = $ImportTemporaryModel->find()->asArray()->all();
                $IPdata             = [];
                $Postdata           = [];
                $IPRecord_datalist  = [];
                try {
                    foreach ($ImportTemporaryRes as $value) {
                        $data          = [];
                        $IPRecord_data = [];
                        $IdlePdtModel  = new IdlePdt();

                        $IdlePdtModel->idle_name = trim($value['idle_name']);
                        $data['idle_name']       = trim($value['idle_name']);

                        $PdtManageTypeRes = $PdtManageTypeModel->findOne(["type_name" => trim($value['server_type_id'])]);
                        if ($PdtManageTypeRes) {
                            $IdlePdtModel->server_type_id = $PdtManageTypeRes['type_id'];
                            $data['server_type_id']       = $PdtManageTypeRes['type_id'];
                        } else {
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '未知的服务器分类：' . $value['server_type_id'],
                            ];
                            return $this->renderJSON($arrReturn);
                        }

                        if (trim($value['servicerprovider']) == "自有") {
                            $IdlePdtModel->servicerprovider = 0;
                            $data['servicerprovider']       = 0;
                        } elseif (trim($value['servicerprovider']) == "供应商提供") {
                            $IdlePdtModel->servicerprovider = 1;
                            $data['servicerprovider']       = 1;
                        } else {
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '服务器提供商不规范：' . $value['servicerprovider'],
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                        //机房
                        $roomRes = $PdtRoomMangeModel->findOne(["name" => trim($value['room_id']), "provider" => $IdlePdtModel->servicerprovider]);
                        if ($roomRes) {
                            $IdlePdtModel->room_id = $roomRes['id'];
                            $data['room_id']       = $roomRes['id'];
                        } else {
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '未知的机房：' . $value['room_id'] . ',或机房所属不对应',
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                        //机柜  //供应商   自有是判断库里的
                        if ($IdlePdtModel->servicerprovider == 0) {
                            $cabinetRes = $PdtCabinetManageModel->findOne(["name" => trim($value['cabinet_id'])]);
                            if ($cabinetRes) {
                                $IdlePdtModel->cabinet_id = $cabinetRes['id'];
                                $data['cabinet_id']       = $cabinetRes['id'];
                            } else {
                                $arrReturn = [
                                    'status' => 0,
                                    'info'   => '未知的机柜：' . $value['cabinet_id'],
                                ];
                                return $this->renderJSON($arrReturn);
                            }
                            $IdlePdtModel->provider_id = "";
                            $data['provider_id']       = "";

                            //交换机
                            $SwitchManageModel = new SwitchManage();
                            $SwitchManageRes   = $SwitchManageModel->findOne(['ip' => trim($value['switch_location'])]);
                            if ($SwitchManageRes) {
                                $IdlePdtModel->switch_location = $SwitchManageRes['id'];
                                $data['switch_location']       = $SwitchManageRes['id'];
                            } else {
                                $arrReturn = [
                                    'status' => 0,
                                    'info'   => '未知的交换机：' . $value['switch_location'],
                                ];
                                return $this->renderJSON($arrReturn);
                            }
                            //交换机端口
                            $IdlePdtModel->switch_port = $value['switch_port'];
                            $data['switch_port']       = $value['switch_port'];
                            #内网交换机
                            if ($value['intranet_switch_location'] != '') {
                                $IntranetSwitchModel = new IntranetSwitchManage();
                                $IntranetSwitchRes   = $IntranetSwitchModel->findOne(['ip' => trim($value['intranet_switch_location'])]);
                                if ($IntranetSwitchRes) {
                                    $IdlePdtModel->intranet_switch_location = $IntranetSwitchRes['id'];
                                    $data['intranet_switch_location']       = $IntranetSwitchRes['id'];
                                } else {
                                    $arrReturn = [
                                        'status' => 0,
                                        'info'   => '未知的内网交换机：' . $value['intranet_switch_location'],
                                    ];
                                    return $this->renderJSON($arrReturn);
                                }
                            } else {
                                $IdlePdtModel->intranet_switch_location = null;
                                $data['intranet_switch_location']       = null;
                            }
                            #内网交换机端口
                            $IdlePdtModel->intranet_switch_port = $value['intranet_switch_port'];
                            $data['intranet_switch_port']       = $value['intranet_switch_port'];

                        } else {
                            $IdlePdtModel->cabinet_id = "";
                            $data['cabinet_id']       = "";
                            //供应商
                            $ProviderModel = new Provider();
                            if ($value['provider_id'] == "" || $value['provider_id'] == null) {
                                $IdlePdtModel->provider_id = "";
                                $data['provider_id']       = "";
                            } else {
                                $ProviderRes = $ProviderModel->findOne(['name' => trim($value['provider_id'])]);
                                if ($ProviderRes) {
                                    $IdlePdtModel->provider_id = $ProviderRes['id'];
                                    $data['provider_id']       = $ProviderRes['id'];
                                } else {
                                    $arrReturn = [
                                        'status' => 0,
                                        'info'   => '未知的供应商：' . $value['provider_id'],
                                    ];
                                    return $this->renderJSON($arrReturn);
                                }
                            }
                            $IdlePdtModel->switch_location          = "";
                            $data['switch_location']                = "";
                            $IdlePdtModel->intranet_switch_location = "";
                            $data['intranet_switch_location']       = "";

                            #业务 / 内网交换机端口
                            $IdlePdtModel->switch_port          = "";
                            $data['switch_port']                = "";
                            $IdlePdtModel->intranet_switch_port = null;
                            $data['intranet_switch_port']       = null;
                        }

                        //状态属性
                        $attributeRes = $ServerAttributeModel->findOne(["name" => trim($value['attribute_id'])]);
                        if ($attributeRes) {
                            $IdlePdtModel->attribute_id = $attributeRes['id'];
                            $data['attribute_id']       = $attributeRes['id'];
                        } else {
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '未知的状态属性：' . $value['attribute_id'],
                            ];
                            return $this->renderJSON($arrReturn);
                        }

                        $PdtManageModel = new PdtManage();
                        //产品配置类别
                        $PdtManageRes = $PdtManageModel->findOne(["name" => $value['pdt_id']]);
                        //print_r($PdtManageRes);die;
                        //echo trim($value['pdt_id']);die;
                        if ($PdtManageRes) {
                            $IdlePdtModel->pdt_id = $PdtManageRes['id'];
                            $data['pdt_id']       = $PdtManageRes['id'];
                        } else {
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '未知的产品配置类别:' . $value['pdt_id'],
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                        //property
                        if ($value['property'] == "公司所属") {
                            $IdlePdtModel->property = 0;
                            $data['property']       = 0;
                        } elseif ($value['property'] == "客户所属") {
                            $IdlePdtModel->property = 1;
                            $data['property']       = 1;
                        } else {
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '产品所属不规范：' . $value['property'],
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                        //产品类型
                        $PdtTypeModel = new PdtType();
                        $PdtTypeRes   = $PdtTypeModel->findOne(["name" => trim($value['type_id'])]);
                        if ($PdtTypeRes) {
                            $IdlePdtModel->type_id = $PdtTypeRes['id'];
                            $data['type_id']       = $PdtTypeRes['id'];
                        } else {
                            $arrReturn = [
                                'status' => 0,
                                'info'   => '未知的产品类型：' . $value['type_id'],
                            ];
                            return $this->renderJSON($arrReturn);
                        }
                        //配置
                        if ($value['config'] != null || $value['config'] != "") {
                            $configRes = explode(',', trim($value['config']));
                            if (count($configRes) < 7) {
                                $arrReturn = [
                                    'status' => 0,
                                    'info'   => '配置有误：' . $value['config'],
                                ];
                                return $this->renderJSON($arrReturn);
                            } else {
                                $idle_config['cpu']             = $configRes[0];
                                $idle_config['ram']             = $configRes[1];
                                $idle_config['hdd']             = $configRes[2];
                                $idle_config['configbandwidth'] = $configRes[3];
                                $idle_config['ipnumber']        = $configRes[4];
                                $idle_config['defense']         = $configRes[5];
                                $idle_config['operatsystem']    = $configRes[6];
                                $IdlePdtModel->config           = json_encode($idle_config, JSON_UNESCAPED_UNICODE);
                                $data['config']                 = json_encode($idle_config, JSON_UNESCAPED_UNICODE);
                            }
                        } else {
                            $idle_config['cpu']             = "";
                            $idle_config['ram']             = "";
                            $idle_config['hdd']             = "";
                            $idle_config['configbandwidth'] = "";
                            $idle_config['ipnumber']        = "";
                            $idle_config['defense']         = "";
                            $idle_config['operatsystem']    = "";
                            $IdlePdtModel->config           = json_encode($idle_config, JSON_UNESCAPED_UNICODE);
                            $data['config']                 = json_encode($idle_config, JSON_UNESCAPED_UNICODE);
                        }

                        $IdlePdtModel->occupies_position = $value['occupies_position'];
                        $data['occupies_position']       = $value['occupies_position'];

                        $IdlePdtModel->machine_model = $value['machine_model'];
                        $data['machine_model']       = $value['machine_model'];

                        $IdlePdtModel->machine_model = $value['sn_code'];
                        $data['sn_code']             = $value['sn_code'];

                        $IdlePdtModel->ipmi_ip = $value['ipmi_ip'];
                        $data['ipmi_ip']       = $value['ipmi_ip'];

                        $IdlePdtModel->ipmi_type = $value['ipmi_type'];
                        $data['ipmi_type']       = $value['ipmi_type'];

                        $IdlePdtModel->ipmi_name = $value['ipmi_name'];
                        $data['ipmi_name']       = $value['ipmi_name'];

                        $IdlePdtModel->ipmi_pwd = $value['ipmi_pwd'];
                        $data['ipmi_pwd']       = $value['ipmi_pwd'];

                        $ipArray = (json_decode(str_replace("\\n", "", $value['ip']), true));//print_r($ipArray);die;
                        $ipArray = array_unique($ipArray);
                        $ipArray = array_filter($ipArray);

                        if (!empty($ipArray)) {

                            $ReturnData = DataHelper::splitIP($ipArray);
                            if ($ReturnData['status'] == 0) {
                                $arrReturn = [
                                    'status' => 0,
                                    'info'   => $ReturnData['info'],
                                ];
                                return $this->renderJSON($arrReturn);
                            }

                            $IPResArray = $ReturnData['data'];#print_r($IPResArray);die;

                            $PdtIpAll = $PdtIpModel->find()->select('ip,status,room_id')->where(['in', 'ip', $IPResArray])->asArray()->all(); #print_r($PdtIpAll);die;
                            #echo $commandQuery->createCommand()->getRawSql();

                            $PdtIpAll_IP      = array_column($PdtIpAll, 'ip');            #print_r($PdtIpAll_IP);
                            $PdtIpAll_status  = array_column($PdtIpAll, 'status');        #print_r($PdtIpAll_status);exit;
                            $PdtIpAll_room_id = array_column($PdtIpAll, 'room_id');       #print_r($PdtIpAll_room_id);die;

                            foreach ($IPResArray as $val) {
                                if (!in_array($val, $PdtIpAll_IP)) {
                                    $arrReturn = [
                                        'status' => 0,
                                        'info'   => 'IP：' . $val . '不存在于IP库中',
                                    ];
                                    return $this->renderJSON($arrReturn);
                                }

                            }
                            foreach ($PdtIpAll_status as $key => $val) {
                                if ($val != 0) {
                                    $arrReturn = [
                                        'status' => 0,
                                        'info'   => 'IP：' . $PdtIpAll_IP[$key] . '未在闲置中',
                                    ];
                                    return $this->renderJSON($arrReturn);
                                }
                            }
                            foreach ($PdtIpAll_room_id as $key => $val) {
                                if ($val != $IdlePdtModel->room_id) {
                                    $arrReturn = [
                                        'status' => 0,
                                        'info'   => 'IP：' . $PdtIpAll_IP[$key] . '与库IP所对应的机房不一致',
                                    ];
                                    return $this->renderJSON($arrReturn);
                                }
                            }

                            $IdlePdtModel->ip  = str_replace(" ", "", json_encode($ipArray, JSON_UNESCAPED_UNICODE));
                            $data['ip']        = str_replace(" ", "", json_encode($ipArray, JSON_UNESCAPED_UNICODE));
                            $IdlePdtModel->ip2 = json_encode($IPResArray, JSON_UNESCAPED_UNICODE);
                            $data['ip2']       = json_encode($IPResArray, JSON_UNESCAPED_UNICODE);

                            //IP使用记录
                            $IPRecord_data['unionid']     = null;
                            $IPRecord_data['ipmi_ip']     = $value['ipmi_ip'];
                            $IPRecord_data['ip']          = implode(",", $IPResArray);
                            $IPRecord_data['describe']    = "管理员 " . $this->getAdminInfo('uname') . "通过自有导入操作，进行了IP使用";
                            $IPRecord_data['admin_id']    = $this->getAdminInfo('admin_id');
                            $IPRecord_data['admin_name']  = $this->getAdminInfo('uname');
                            $IPRecord_data['create_time'] = time();

                        } else {
                            $IdlePdtModel->ip = "";
                            $data['ip']       = "";

                            $IdlePdtModel->ip2 = "";
                            $data['ip2']       = "";
                            $IPResArray        = [];
                        }
                        //print_r($IPResArray);
                        //print_r($ipArray);
                        //die;
                        $IdlePdtModel->update_time = time();
                        if ($value['status'] != null || $value['status'] != "") {
                            if ($value['status'] == "使用中") {
                                $IdlePdtModel->status = 1;
                                $data['status']       = 1;
                            } elseif ($value['status'] == "闲置中") {
                                $IdlePdtModel->status = 0;
                                $data['status']       = 0;
                            } else {
                                $transaction->rollBack();
                                $arrReturn = [
                                    'status' => 0,
                                    'info'   => '状态：' . $value['status'] . '不规范',
                                ];
                                return $this->renderJSON($arrReturn);
                            }
                        } else {
                            $IdlePdtModel->status = 0;
                            $data['status']       = 0;
                        }
                        #验证
                        if (!$IdlePdtModel->validate()) {
                            #事务回滚
                            $transaction->rollBack();
                            $arrReturn = [
                                'status' => 0,
                                'info'   => $IdlePdtModel->errors,
                            ];
                            return $this->renderJSON($arrReturn);
                        } else {
                            //$postdata1 = $IdlePdtModel->attributes;
                            //unset($postdata1['id']);
                            $Postdata[]          = array_values($data);
                            $IPRecord_datalist[] = array_values($IPRecord_data);
                            $IPdata              = array_merge($IPdata, $IPResArray);
                            //print_r($Postdata);die;
                        }
                    }
                    #print_r($IPdata);exit;

                    #批量修改IP状态
                    $ipcount        = count($IPdata);                     ##echo $ipcount;die;
                    $updatePdtIPRes = $PdtIpModel->updateAll(['status' => 1], ['ip' => $IPdata]);
                    if ($updatePdtIPRes != $ipcount) {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => "IP状态修改失败",
                        ];
                        $transaction->rollBack();
                        return $this->renderJSON($arrReturn);
                    }

                    #批量加入记录
                    $IPRecord_datalist = array_filter($IPRecord_datalist);#去空
                    $IpRecordcount     = count($IPRecord_datalist);

                    $addIpRecordfilds = ['unionid', 'ipmi_ip', 'ip', 'describe', 'admin_id', 'admin_name', 'create_time'];
                    $addIpRecordRes   = Yii::$app->db->createCommand()->batchInsert(IpRecord::tableName(), $addIpRecordfilds, $IPRecord_datalist)->execute();
                    if ($addIpRecordRes != $IpRecordcount) {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => "IP记录添加失败",
                        ];
                        $transaction->rollBack();
                        return $this->renderJSON($arrReturn);
                    }

                    #批量加入机器
                    $addfilds      = [
                        'idle_name', 'server_type_id', 'servicerprovider', 'room_id', 'cabinet_id', 'provider_id', 'switch_location', 'switch_port', 'intranet_switch_location', 'intranet_switch_port', 'attribute_id', 'pdt_id', 'property', 'type_id',
                        'config', 'occupies_position', 'machine_model', 'sn_code', 'ipmi_ip', 'ipmi_type', 'ipmi_name', 'ipmi_pwd', 'ip', 'ip2', 'status',
                    ];
                    $Postdata      = array_filter($Postdata);#去空
                    $Postdatacount = count($Postdata);
                    $addIdlePdtRes = Yii::$app->db->createCommand()->batchInsert(IdlePdt::tableName(), $addfilds, $Postdata)->execute();

                    if ($addIdlePdtRes != $Postdatacount) {
                        $arrReturn = [
                            'status' => 0,
                            'info'   => "数据导入失败",
                        ];
                        $transaction->rollBack();
                        return $this->renderJSON($arrReturn);
                    }
                    //print_r($Postdata);print_r($IPdata);die;

                    if ($addIdlePdtRes == $Postdatacount && $updatePdtIPRes == $ipcount) {
                        $transaction->commit();
                        $arrReturn = [
                            'status' => 1,
                            'info'   => '数据导入成功',
                        ];
                    } else {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => "数据导入失败",
                        ];
                    }
                    return $this->renderJSON($arrReturn);

                } catch (\Exception $e) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => $e,
                    ];
                    return $this->renderJSON($arrReturn);
                }
                return $this->renderJSON($arrReturn);

            } elseif ($type == 'cancel_import') {
                Yii::$app->db->createCommand()->truncateTable(ImportTemporary::tableName())->execute();
                $arrReturn = [
                    'status' => 1,
                    'info'   => '取消成功',
                ];
                return $this->renderJSON($arrReturn);
            }
        } else {
            $ImportTemporaryRes = $ImportTemporaryModel->find();

            $iCount = $ImportTemporaryRes->count();
            $oPage  = DataHelper::getPage($iCount, 15);

            $arrRes = $ImportTemporaryRes->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();
            //获取机房列表
            $RoomManageModel = new PdtRoomMange();
            $roomRes         = $RoomManageModel->getListAll();

            return $this->render('import-item', ['data' => $arrRes, 'iCount' => $iCount, 'page' => $oPage]);

        }

        //print_r($data);
    }

    #接收选择的IP，然后返回到页面
    public function actionGetSelectip()
    {
        Yii::$app->request->isAjax || die('异常访问');

        //获取已经选择过的IP
        $oldIp = trim($this->post('ips'));//echo $oldIp;//die();
        $oldIp = str_replace(' ', '', $oldIp);
        $oldIp = array_filter(explode("\n", $oldIp), function ($v) {
            return !empty($v);
        });
        rsort($oldIp);

        if (!empty($oldIp)) {
            foreach ($oldIp as $key => $value) {
                $newIp[] = $value;//将原来写的IP也写入
            }
        }
        //print_r($newIp);
        //获取刚选择的IP数据
        $iparray = json_decode($this->post('data'), true);                                        //print_r($iparray);//die();
        if (empty($iparray)) {
            $arrReturn = [];
            return $this->renderJSON($arrReturn);
        } else {
            foreach ($iparray as $key => $value) {
                $newIp[] = trim($value['ip']);
            }

            $newIp = array_flip($newIp);
            $newIp = array_flip($newIp);
            //rsort($newIp);
            //print_r($newIp);
            // $strRes = implode("\r\n", $newIp);echo $strRes;
            //$strRes .= "\r\n";
            return $this->renderJSON($newIp);
        }

    }

    #异步联动获取配置
    public function actionAjaxPdt()
    {
        Yii::$app->request->isAjax || die('异常访问');

        $pdt_id = $this->post('id');//echo $pdt_id;die();
        if (empty($pdt_id)) {
            $arrReturn = [
                'status' => 0,
                'info'   => '未选择产品配置类别',
            ];
            return $this->renderJSON($arrReturn);
        }

        $PdtManageModel = new PdtManage();
        //获取整个的产品配置
        $PdtManageRes = $PdtManageModel->getRowById($pdt_id);
        if (empty($PdtManageRes)) {
            $pdtRes = "";
            return $this->renderJSON($pdtRes);
        }
        $cpuRes       = json_decode($PdtManageRes['cpu'], true);
        $ramRes       = json_decode($PdtManageRes['ram'], true);
        $hddRes       = json_decode($PdtManageRes['hdd'], true);
        $bandwidthRes = json_decode($PdtManageRes['bandwidth'], true);
        $ipnumberRes  = json_decode($PdtManageRes['ipnumber'], true);
        $defenseRes   = json_decode($PdtManageRes['defense'], true);
        $systemRes    = json_decode($PdtManageRes['operatsystem'], true);

        $pdtRes['cpu']       = $cpuRes;
        $pdtRes['ram']       = $ramRes;
        $pdtRes['hdd']       = $hddRes;
        $pdtRes['bandwidth'] = $bandwidthRes;
        $pdtRes['ipnumber']  = $ipnumberRes;
        $pdtRes['defense']   = $defenseRes;
        $pdtRes['system']    = $systemRes;
        //print_r($pdtRes);die();
        $pdtRes['status'] = 1;
        return $this->renderJSON($pdtRes);
    }

    #异步联动闲置的IP
    public function actionAjaxIplist()
    {
        Yii::$app->request->isAjax || die('异常访问');

        $IpModel = new PdtIp();

        $IpModelQuery = $IpModel->getListAll(['status' => 0], true);
        #创建搜索条件
        $IpModel->createSearchWhere($IpModelQuery, $this->get());
        $iCount = $IpModelQuery->count();
        $oPage  = DataHelper::getPage($iCount, $this->get('limit'));
        $arrRes = $IpModelQuery->orderBy('id desc')->offset($oPage->offset)->limit($oPage->limit)->asArray()->all();
        //获取机房列表
        $RoomManageModel = new PdtRoomMange();
        $roomRes         = $RoomManageModel->getListAll();
        //return $this->renderJSON($arrRes,"",$iCount,0);
        return json_encode([
            "code"  => "0",
            "msg"   => "",
            "data"  => $arrRes,
            "count" => $iCount,
        ], true);
    }

    #IPMI 请求（目前未用这个）
    public function actionIpmiRequest()
    {

        Yii::$app->request->isAjax || die('异常访问');

        $IdlePdtModel = new IdlePdt();
        $CabinetModel = new PdtCabinetManage();

        $idle_id    = $this->post('id');
        $str_action = $this->post('str_action');

        $str_action_allow = array('status', 'on', 'off', 'reset', 'pxe', 'bios', 'ipmireset');
        if (!in_array($str_action, $str_action_allow)) {
            $arrReturn = [
                'status' => 0,
                'info'   => '不允许的操作',
            ];
            return $this->renderJSON($arrReturn);
        }
        $IdlePdtRes = $IdlePdtModel->find()->where(['id' => $idle_id])->asArray()->one();

        if (!$IdlePdtRes) {
            $arrReturn = [
                'status' => 0,
                'info'   => '未知的自有服务器',
            ];
            return $this->renderJSON($arrReturn);
        }

        $cabinet_id = $IdlePdtRes['cabinet_id'];
        #获取机柜相关信息
        if ($cabinet_id == "" || $cabinet_id == null) {
            $arrReturn = [
                'status' => 0,
                'info'   => '该服务器未选择机柜',
            ];
            return $this->renderJSON($arrReturn);
        } else {
            $CabinetRes = $CabinetModel->find()->where(['id' => $cabinet_id])->AsArray()->one();
            if (!$CabinetRes) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '未知的机柜信息',
                ];
                return $this->renderJSON($arrReturn);
            } else {
                $cloudboot_key = $CabinetRes['cloudboot_key'];
                $cloudboot_api = $CabinetRes['cloudboot_api'];
                if ($cloudboot_api == "" || $cloudboot_api == null) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '对应机柜未填写云装机API地址',
                    ];
                    return $this->renderJSON($arrReturn);
                }
                if ($cloudboot_key == "" || $cloudboot_key == null) {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '对应机柜未填写云装机密钥',
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }
        }
        $ipmi_ip = $IdlePdtRes['ipmi_ip'];
        if ($ipmi_ip == "" || $ipmi_ip == null) {
            $arrReturn = [
                'status' => 0,
                'info'   => '该自有服务器未填写IPMI IP地址',
            ];
            return $this->renderJSON($arrReturn);
        } else {
            if (!filter_var($ipmi_ip, FILTER_VALIDATE_IP)) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => $ipmi_ip . '为不合法的IP地址',
                ];
                return $this->renderJSON($arrReturn);
            }
        }
        $ipmi_name = $IdlePdtRes['ipmi_name'];
        $ipmi_pwd  = $IdlePdtRes['ipmi_pwd'];
        if ($ipmi_name == "" || $ipmi_name == null) {
            $arrReturn = [
                'status' => 0,
                'info'   => 'IPMI 账户未设置',
            ];
            return $this->renderJSON($arrReturn);
        }
        if ($ipmi_pwd == "" || $ipmi_pwd == null) {
            $arrReturn = [
                'status' => 0,
                'info'   => 'IPMI 密码未设置',
            ];
            return $this->renderJSON($arrReturn);
        }

        $sendRes = IpmiApi::sendipmirequest($cloudboot_api, $str_action, $ipmi_ip, $ipmi_name, $ipmi_pwd, $cloudboot_key);  #print_r($sendRes);exit;

        if ($sendRes['Status'] == "success") {
            $status  = 1;
            $Message = $sendRes['Message'];
            switch (trim($Message)) {
                case 'Chassis Power is off':
                    $info = "服务器电源状态：已关闭";
                    break;
                case "Chassis Power is on":
                    $info = "服务器电源状态：已打开";
                    break;
                case "Chassis Power Control: Up/On":
                    $info = "服务器电源已打开";
                    break;
                case "Chassis Power Control: Down/Off":
                    $info = "服务器电源已关闭";
                    break;
                case "Chassis Power Control: Reset":
                    $info = "服务器已重启，请过2 - 5分钟后测试";
                    break;
                case "Set Boot Device to pxe":
                    $info = "系统下次重启时从PXE启动";
                    break;
                case "Set Boot Device to bios":
                    $info = "系统下次重启时进入BIOS";
                    break;
                case "Sent cold reset command to MC":
                    $info = "即将重启IPMI";
                    break;
            }
        } else {
            $status = 0;
            $info   = $sendRes['Message'];
        }

        $arrReturn = [
            'status' => $status,
            'info'   => $info,
        ];
        return $this->renderJSON($arrReturn);
    }

    #获取重装系统系统模板分类
    public function actionGetCloudsystemclass()
    {
        Yii::$app->request->isAjax || die('异常访问');

        $CloudSystemModel           = new CloudSystemModel();
        $CloudSystemModelClassModel = new CloudSystemModelClass();

        $CloudSystemModelClassRes = $CloudSystemModelClassModel->find()->where(['class_category' => '重装系统'])->orderBy('sort_number asc')->asArray()->all();

        $arrReturn = [
            'status' => 1,
            'info'   => $CloudSystemModelClassRes,
        ];
        return $this->renderJSON($arrReturn);

    }

    /**
     * 获取系统模板
     */
    public function actionGetCloudsystem()
    {
        Yii::$app->request->isAjax || die('异常访问');

        $post = $this->post();
        $class_id = ArrayHelper::getValue($post, 'class_id');
        $class_category = ArrayHelper::getValue($post, 'class_category');

        $CloudSystemModelClassModel = new CloudSystemModelClass();

        if ($class_id != '') {
            $condition = [
                'class_id' => $post['class_id'],
            ];
        } else if ($class_category != '') {
            $classid_arr = $CloudSystemModelClassModel->find()->select('class_id')->where(['class_category' => $post['class_category']])->column();
            $condition   = [
                'class_id' => $classid_arr,
            ];
        } else {
            $condition = [];
        }
        $CloudSystemModel = new CloudSystemModel();
        $CloudSystemRes   = $CloudSystemModel->find()->where(['status' => 'Y'])->andWhere($condition)->orderBy('sort_number asc')->asArray()->all();#print_r($CloudSystemRes);die;

        $arrReturn = [
            'status' => 1,
            'info'   => $CloudSystemRes,
        ];
        return $this->renderJSON($arrReturn);

    }

    /**
     *    重装系统
     */
    public function actionReloadSystem()
    {
        Yii::$app->request->isAjax || die('异常访问');

        $CloudSystemModel            = new CloudSystemModel();
        $CloudSystemModelClassModel  = new CloudSystemModelClass();
        $IdlePdtModel                = new IdlePdt();
        $CabinetModel                = new PdtCabinetManage();
        $ServerRecordModel           = new ServerDorecord();
        $SwitchManageModel           = new SwitchManage();
        $CustomPartitionSettingModel = new CustomPartitionSetting();

        $post = $this->post();

        #开启事务
        $transaction = \Yii::$app->db->beginTransaction();

        $CloudSystemRes = $CloudSystemModel->find()->where(['cloud_system_id' => $post['cloudsystem']])->asArray()->one();
        if (empty($CloudSystemRes)) {
            $arrReturn = [
                'status' => 0,
                'info'   => "未知的模板信息",
            ];
            return $this->renderJSON($arrReturn);
        }
        $SystemModelClassRes = $CloudSystemModelClassModel->find()->where(['class_id' => $CloudSystemRes['class_id']])->asArray()->one();
        if (empty($SystemModelClassRes)) {
            $arrReturn = [
                'status' => 0,
                'info'   => "未知的模板分类",
            ];
            return $this->renderJSON($arrReturn);
        }
        #如果选择为自定义分区类
        if ($SystemModelClassRes['custom_partition'] == 'Y') {
            $type             = $post['partition_type'];
            $file_system_type = $post['file_system_type'];
            $swap_size        = $post['swap_partition_size'];
            $root_size        = $post['root_partition_size'];
            $home_size        = $post['home_partition_size'];
            $www_size         = $post['www_partition_size'];
            $data_size        = $post['data_partition_size'];
            #判断参数
            if (!in_array($type, ['LVM', 'Stardard'])) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => "分区类型错误",
                ];
                return $this->renderJSON($arrReturn);
            }

            if (!in_array($file_system_type, ['ext4', 'xfs'])) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => "文件系统类型错误",
                ];
                return $this->renderJSON($arrReturn);
            }

            if (!$swap_size) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => "swap分区大小必须填写",
                ];
                return $this->renderJSON($arrReturn);
            } else {
                if (!preg_match("/^[1-9][0-9]*$/", $swap_size)) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => "swap分区大小填写有误",
                    ];
                    return $this->renderJSON($arrReturn);
                } else {
                    if ($swap_size < 2 || $swap_size > 32) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => "swap分区大小值范围在2-32之间",
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                }
                $swap_size = strval($swap_size * 1024);
            }

            if (!$root_size) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => "根分区大小必须填写",
                ];
                return $this->renderJSON($arrReturn);
            } else {
                if ($root_size == 'all') {
                    $root_size = 'all';
                } else {
                    if (!preg_match("/^[1-9][0-9]*$/", $swap_size)) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => "根分区大小填写有误",
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                    $root_size = strval($root_size * 1024);
                }
            }

            if ($home_size) {
                if ($home_size == 'all') {
                    $home_size = 'all';
                } else {
                    if (!preg_match("/^[1-9][0-9]*$/", $home_size)) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => "Home分区大小填写有误",
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                    $home_size = strval($home_size * 1024);
                }
            } else {
                $home_size = '0';
            }

            if ($www_size) {
                if ($www_size == 'all') {
                    $www_size = 'all';
                } else {
                    if (!preg_match("/^[1-9][0-9]*$/", $www_size)) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => "WWW分区大小填写有误",
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                    $www_size = strval($www_size * 1024);
                }
            } else {
                $www_size = '0';
            }

            if ($data_size) {
                if ($data_size == 'all') {
                    $data_size = 'all';
                } else {
                    if (!preg_match("/^[1-9][0-9]*$/", $data_size)) {
                        $transaction->rollBack();
                        $arrReturn = [
                            'status' => 0,
                            'info'   => "Data分区大小填写有误",
                        ];
                        return $this->renderJSON($arrReturn);
                    }
                    $data_size = strval($data_size * 1024);
                }
            } else {
                $data_size = '0';
            }

            #boot  home  www  data中只能存在一个all
            $check_arr = [$root_size, $home_size, $www_size, $data_size];
            $value_num = array_count_values($check_arr);
            if ($value_num['all'] > 1) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => "只能有一个参数值为all",
                ];
                return $this->renderJSON($arrReturn);
            }

        }
        $IdlePdtRes = $IdlePdtModel->find()->With('switch')->where(['id' => $post['id']])->asArray()->one();

        if (empty($IdlePdtRes)) {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => "未知的机器",
            ];
            return $this->renderJSON($arrReturn);
        }

        $Sn = $IdlePdtRes['sn_code'];
        if ($Sn == "" || $Sn == null) {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => "SN码为空",
            ];
            return $this->renderJSON($arrReturn);
        }
        //获取机柜信息
        $cabinet_id = $IdlePdtRes['cabinet_id'];
        if ($cabinet_id == "" || $cabinet_id == null) {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => "该机器未选择机柜信息",
            ];
            return $this->renderJSON($arrReturn);
        } else {
            $CabinetRes = $CabinetModel->find()->where(['id' => $cabinet_id])->asArray()->one();
            if (empty($CabinetRes)) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => "未知的机柜信息",
                ];
                return $this->renderJSON($arrReturn);
            } else {
                $cloudboot_api = $CabinetRes['cloudboot_api'];
                $cloudboot_key = $CabinetRes['cloudboot_key'];
                if ($cloudboot_api == "" || $cloudboot_api == null) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '对应机柜未填写云装机API地址',
                    ];
                    return $this->renderJSON($arrReturn);
                }
                if ($cloudboot_key == "" || $cloudboot_key == null) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '对应机柜未填写云装机密钥',
                    ];
                    return $this->renderJSON($arrReturn);
                }

            }
        }

        #
        $ipmi_ip   = $IdlePdtRes['ipmi_ip'];
        $ipmi_name = $IdlePdtRes['ipmi_name'];
        $ipmi_pwd  = $IdlePdtRes['ipmi_pwd']; #echo $ipmi_name;echo $ipmi_pwd;exit;
        if ($ipmi_ip == "" || $ipmi_ip == null) {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => '该自有服务器未填写IPMI IP地址',
            ];
            return $this->renderJSON($arrReturn);
        } else {
            if (!filter_var($ipmi_ip, FILTER_VALIDATE_IP)) {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => $ipmi_ip . '为不合法的IP地址',
                ];
                return $this->renderJSON($arrReturn);
            }
        }

        if ($ipmi_name == "" || $ipmi_name == null || $ipmi_pwd == "" || $ipmi_pwd == null) {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => 'IPMI 账户名或者密码未设置',
            ];
            return $this->renderJSON($arrReturn);
        }
        $ReinstallConfirmModel = new ReinstallConfirm();
        $ReinstallConfirmRes   = $ReinstallConfirmModel->find()->where(['idle_pdt_id' => $post['id'], 'status' => '待确认'])->asArray()->one();
        if ($ReinstallConfirmRes) {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => '该机器已有重装请求未处理，如若继续，请先取消重装',
            ];
            return $this->renderJSON($arrReturn);
        }

        #如果选择为自定义分区类，将分区设置新增到数据库中
        if ($SystemModelClassRes['custom_partition'] == 'Y') {
            $PartitionSettingQuery = $CustomPartitionSettingModel->find()->where(['sn_code' => $Sn])->one();
            if ($PartitionSettingQuery) {
                #更新
                $PartitionSettingQuery->type             = $type;
                $PartitionSettingQuery->file_system_type = $file_system_type;
                $PartitionSettingQuery->swap_size        = $swap_size;
                $PartitionSettingQuery->root_size        = $root_size;
                $PartitionSettingQuery->home_size        = $home_size;
                $PartitionSettingQuery->www_size         = $www_size;
                $PartitionSettingQuery->data_size        = $data_size;
                $PartitionSettingQuery->update_time      = time();

                $updatePartitionSetting = $PartitionSettingQuery->update();
                if (!$updatePartitionSetting) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '更新自定义分区设置信息失败',
                    ];
                    return $this->renderJSON($arrReturn);
                }
            } else {
                #新增
                $CustomPartitionSettingModel->sn_code          = $Sn;
                $CustomPartitionSettingModel->type             = $type;
                $CustomPartitionSettingModel->file_system_type = $file_system_type;
                $CustomPartitionSettingModel->swap_size        = $swap_size;
                $CustomPartitionSettingModel->root_size        = $root_size;
                $CustomPartitionSettingModel->home_size        = $home_size;
                $CustomPartitionSettingModel->www_size         = $www_size;
                $CustomPartitionSettingModel->data_size        = $data_size;
                $CustomPartitionSettingModel->create_time      = time();

                $insertPartitionSetting = $CustomPartitionSettingModel->insert();
                if (!$insertPartitionSetting) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '新增自定义分区设置信息失败',
                    ];
                    return $this->renderJSON($arrReturn);
                }
            }
        }
        #加入重装确认
        $ReinstallConfirmModel->idle_pdt_id  = $post['id'];
        $ReinstallConfirmModel->system_id    = $CloudSystemRes['cloud_system_id'];
        $ReinstallConfirmModel->system_name  = $CloudSystemRes['cloud_system_name'];
        $ReinstallConfirmModel->ipmi_ip      = $ipmi_ip;
        $ReinstallConfirmModel->admin_id     = $this->getAdminInfo('admin_id');
        $ReinstallConfirmModel->admin_name   = $this->getAdminInfo('uname');
        $ReinstallConfirmModel->request_time = time();
        $ReinstallConfirmModel->status       = '待确认';

        if ($ReinstallConfirmModel->insert()) {
            $transaction->commit();
            $arrReturn = [
                'status' => 1,
                'info'   => '已加入申请，请等待审核确认',
            ];
        } else {
            $transaction->rollBack();
            $arrReturn = [
                'status' => 0,
                'info'   => '加入申请失败，请稍后重试',
            ];
        }
        return $this->renderJSON($arrReturn);
        /*
		#获取交换机IP和端口
		$SwitchRes = $SwitchManageModel->find()->where(['id'=>$IdlePdtRes['switch_location']])->asArray()->one();
		$SwitchIp = $SwitchRes['ip'];
		$SwitchPort = $IdlePdtRes['switch_port'];
		#
		$SwitchSshPort = $SwitchRes['login_port'];
		$SwitchUsername = $SwitchRes['login_user'];
		$SwitchPassword = $SwitchRes['login_password'];

		#获取重装信息
		$Hostname = $IdlePdtRes['ipmi_ip'];
		$Ip = $IdlePdtRes['ipmi_ip'];
		$NetworkID = substr(trim($Ip),0,strrpos(trim($Ip),'.'));
		$NetworkID = str_replace('.','',$NetworkID);   ##echo $NetworkID;die;
		$SystemID = $CloudSystemRes['cloud_system_id'];
		$OsID = $SystemID;
		#记录操作日志记录		。将之前的记录改为取消
		$recordRes = $ServerRecordModel->find()->where(['do_idleid' => $IdlePdtRes['id'], 'do_status' => '处理中'])->one();
		if($recordRes) {
			$recordRes->do_status = '取消处理';
			$recordRes->save();
		}
		#重新记录此次重装记录
		$ServerRecordModel->do_adminid = $this->getAdminInfo('admin_id');
		$ServerRecordModel->do_idleid = $IdlePdtRes['id'];
		$ServerRecordModel->do_sn = $IdlePdtRes['sn_code'];
		$ServerRecordModel->do_operate = '重装系统';
		$ServerRecordModel->do_explain = $CloudSystemRes['cloud_system_name'];
		$ServerRecordModel->do_status = '处理中';
		$do_response['Title'] = "重装信息获取完毕，开始请求重装接口";
		$ServerRecordModel->do_response = json_encode($do_response);
		$ServerRecordModel->do_loading = 10;
		$ServerRecordModel->do_create_time = time();
		$ServerRecordModel->insert();
		//请求重装接口
		$ReloadSystemRequestRes = CloudBootApi::osinstall_batchAdd($cloudboot_api,$Sn,$Hostname,$Ip,$NetworkID,$OsID,$SystemID,$ipmi_name,$ipmi_pwd,$SwitchIp,$SwitchSshPort,$SwitchUsername,$SwitchPassword,$SwitchPort,$cloudboot_key);//print_r($ReloadSystemRequestRes);die;

		if( $ReloadSystemRequestRes['Status'] == "success") {

			#更新记录
			$ServerRecordQuery = $ServerRecordModel->find()->where(['do_idleid' => $IdlePdtRes['id'], 'do_status' => '处理中'])->one();
			$do_response['Title'] = "重装请求返回成功，开始重装";
			$ServerRecordQuery->do_response = json_encode($do_response);
			$ServerRecordQuery->do_loading = 10;
			$ServerRecordQuery->do_doing_time = time();
			$Res = $ServerRecordQuery->save();

			if( $Res ) {
				$arrReturn = [
					'status' => 1,
					'info' => "操作成功"
				];
			} else {
				$arrReturn = [
					'status' => 0,
					'info' => "新增重装记录更新失败！！"
				];
			}

		} else {
			$arrReturn = [
				'status' => 0,
				'info' => $ReloadSystemRequestRes['Message']
			];
		}

		return $this->renderJSON($arrReturn);*/
    }

    #取消重装
    public function actionCancelReload()
    {
        Yii::$app->request->isAjax || die('异常访问');
        $id                    = $this->post('id');
        $IdlePdtModel          = new IdlePdt();
        $ServerRecordModel     = new ServerDorecord();
        $ReinstallConfirmModel = new ReinstallConfirm();

        $IdlePdtRes = $IdlePdtModel->find()->With('switch')->With('pdtcabinet')->where(['id' => $id])->asArray()->one();
        if (empty($IdlePdtRes)) {
            $arrReturn = [
                'status' => 0,
                'info'   => "未知的机器",
            ];
            return $this->renderJSON($arrReturn);
        }
        $Sn = $IdlePdtRes['sn_code'];
        #开启事务
        $transaction = \Yii::$app->db->beginTransaction();
        //记录操作日志记录
        $recordRes = $ServerRecordModel->find()->where(['do_idleid' => $IdlePdtRes['id'], 'do_status' => '处理中'])->one();

        if ($recordRes) {
            $recordRes->do_status = '取消处理';
            $recordRes->save();

            $cloudboot_api = $IdlePdtRes['pdtcabinet'][0]['cloudboot_api'];
            $cloudboot_key = $IdlePdtRes['pdtcabinet'][0]['cloudboot_key'];

            $ReinstallConfirmRes = $ReinstallConfirmModel->find()->where(['idle_pdt_id' => $IdlePdtRes['id'], 'status' => '已确认'])->orderBy('request_time desc')->asArray()->one();
            #print_r($ReinstallConfirmRes);exit;
            $ReinstallConfirmQuery = $ReinstallConfirmModel->findone($ReinstallConfirmRes['id']);

            if (!empty($ReinstallConfirmQuery)) {
                $ReinstallConfirmQuery->status = '已取消';

                if (!$ReinstallConfirmQuery->update()) {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '重装确认记录更新失败，取消失败',
                    ];
                }
            }

            //请求取消重装接口
            $CancelRes = CloudBootApi::osinstall_cancelReload($cloudboot_api, $Sn, $cloudboot_key);//print_r($CancelRes);die;

            if ($CancelRes['Status'] == 'success') {
                $transaction->commit();
                $arrReturn = [
                    'status' => 1,
                    'info'   => '操作成功',
                ];
            } else {
                $transaction->rollBack();
                $arrReturn = [
                    'status' => 0,
                    'info'   => $CancelRes['Message'],
                ];
            }
        } else {
            $ReinstallConfirmRes = $ReinstallConfirmModel->find()->where(['idle_pdt_id' => $IdlePdtRes['id'], 'status' => '待确认'])->orderBy('request_time desc')->asArray()->one();
            if (!$ReinstallConfirmRes) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '系统重装未在处理中状态',
                ];
                return $this->renderJSON($arrReturn);
            } else {
                $ReinstallConfirmQuery         = $ReinstallConfirmModel->findone($ReinstallConfirmRes['id']);
                $ReinstallConfirmQuery->status = '已取消';
                if ($ReinstallConfirmQuery->update()) {
                    $transaction->commit();
                    $arrReturn = [
                        'status' => 1,
                        'info'   => '操作成功',
                    ];
                } else {
                    $transaction->rollBack();
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '重装确认记录更新失败，取消失败',
                    ];
                }
            }
        }

        return $this->renderJSON($arrReturn);
    }

    #新版流量图页面
    public function actionGetFlowChart()
    {
        Yii::$app->request->isAjax || die('异常访问');

        $IdlePdtModel = new IdlePdt();
        $post         = $this->post();
        $id           = $post['id'];

        $IdlePdtRes = $IdlePdtModel->find()->With('switch')->where(['id' => $id])->asArray()->one();
        if (empty($IdlePdtRes)) {
            $arrReturn = [
                'status' => 0,
                'info'   => "未知的机器",
            ];
            return $this->renderJSON($arrReturn);
        }

        #流量图
        $from = $post['fromcondition'];
        $to   = $post['endcondition'];
        #print_r($post);exit;
        if (!empty($IdlePdtRes['switch'][0])) {

            $FlowChartPng_base = SwitchFlowChartApi::GetFlowChart($IdlePdtRes['switch'][0]['ip'], $IdlePdtRes['switch_port'], $from, $to);
            if ($FlowChartPng_base == '' || $FlowChartPng_base == null || !isset($FlowChartPng_base)) {
                $FlowChartPng_link = '/upload/flowchart/nodatapoints.png';
                $arrReturn         = [
                    'status'  => 0,
                    'info'    => '未有流量信息',
                    'img_url' => $FlowChartPng_link,
                ];
                return $this->renderJSON($arrReturn);
            }
            $dir = $_SERVER['DOCUMENT_ROOT'] . "/upload/flowchart/";
            if (!is_dir($dir)) {
                mkdir($dir, 0777, true);
            }
            $png_name     = str_replace('.', '_', $IdlePdtRes['switch'][0]['ip']) . '_' . $IdlePdtRes['switch_port'] . '_' . $from . '_' . time();
            $fileName     = $png_name . '.png';
            $FlowChartPng = $dir . $fileName;
            file_put_contents($FlowChartPng, $FlowChartPng_base);
            $FlowChartPng_link = '/upload/flowchart/' . $fileName;  //echo $pic_link;die;

            $arrReturn = [
                'status'  => 1,
                'info'    => '获取成功',
                'img_url' => $FlowChartPng_link,
            ];
        } else {
            $FlowChartPng_link = '/upload/flowchart/nodatapoints.png';
            $arrReturn         = [
                'status'  => 0,
                'info'    => '未有交换机',
                'img_url' => $FlowChartPng_link,
            ];
        }

        return $this->renderJSON($arrReturn);

    }

    //进入流量图页面
    public function actionGetTrafficgraph()
    {

        $IdlePdtModel = new IdlePdt();
        $id           = $this->get('id');
        $id           = intval($id);
        $from         = $this->get('from');
        $to           = $this->get('to');

        $IdlePdtRes = $IdlePdtModel->find()->With('switch')->Where(['id' => $id])->asArray()->one(); #print_r($IdlePdtRes);die;
        if (empty($IdlePdtRes)) {
            echo "<script>alert('未找到服务器信息')</script>";
            echo '<script>window.close();</script>';
            return;
        }
        //获取交换机IP和端口
        $switch_ip   = $IdlePdtRes['switch'][0]['ip'];
        $switch_port = $IdlePdtRes['switch_port'];

        $GettokenRes = ZabbixApis::Gettoken();  #print_r($GettokenRes);die;
        $token       = $GettokenRes['result'];  #echo $token;die;

        $GetHostidRes = ZabbixApis::GetHostid($switch_ip, $token);
        $HostId       = $GetHostidRes['result'][0]['hostid']; //echo $HostId;die;
        if (empty($HostId)) {
            echo "<script>alert('未找到相应的HostID信息')</script>";
            echo '<script>window.close();</script>';
            return;
        }

        $GetGraphidRes = ZabbixApis::GetGraphid($HostId, $switch_port, $token);
        $GraphId       = $GetGraphidRes['result'][0]['graphid'];  #echo $GraphId;die;

        $GetCookieRes = ZabbixApis::GetCookie(); #echo $GetCookieRes;die;

        $graph = ZabbixApis::getGraph($GetCookieRes, $GraphId, $from, $to, "900");  #echo $graph;die;

        $pic_name = $_SERVER['DOCUMENT_ROOT'] . '/trafficgraph.png';
        file_put_contents($pic_name, $graph);

        $pic_link = Yii::$app->request->hostInfo . "/trafficgraph.png";  //echo $pic_link;die;

        $data['id'] = $id;
        $arrReturn  = [
            'status' => 1,
            'info'   => $data,
        ];
        return $this->render('trafficgraph', [
            'IdlePdtRes' => $IdlePdtRes,
            'pic_link'   => $pic_link,
            'from'       => $from,
            'to'         => $to,
        ]);
    }

    /**
     *更新 带宽的监控
     */
    public function actionUpdateBandwidthmonitoring()
    {

        Yii::$app->request->isAjax || die('异常访问');
        $IdlePdtModel = new IdlePdt();
        $id           = $this->post('id');
        $id           = intval($id);
        $IdlePdtRes   = $IdlePdtModel->find()->With('switch')->Where(['id' => $id])->asArray()->one(); #print_r($IdlePdtRes);die;
        if (empty($IdlePdtRes)) {
            $arrReturn = [
                'status' => 0,
                'info'   => '未找到服务器信息',
            ];
            return $this->renderJSON($arrReturn);
        }

        //获取交换机IP和端口
        $switch_ip   = $IdlePdtRes['switch'][0]['ip'];
        $switch_port = $IdlePdtRes['switch_port'];

        #登录获取令牌
        $GettokenRes = ZabbixApis::Gettoken();
        if (!$GettokenRes['result']) {
            $arrReturn = [
                'status' => 0,
                'info'   => '登录获取令牌失败',
            ];
            return $this->renderJSON($arrReturn);
        }
        $token = $GettokenRes['result'];

        #获取指定ip主机的hostid
        $GetHostidRes = ZabbixApis::GetHostid($switch_ip, $token);                                     #print_r($GetHostidRes);exit;
        $HostId       = $GetHostidRes['result'][0]['hostid'];
        if (empty($HostId)) {
            $arrReturn = [
                'status' => 0,
                'info'   => '未找到相应的HostID信息',
            ];
            return $this->renderJSON($arrReturn);
        }
        //要获取ID的主机宏
        $macro        = '{$PORT_' . $switch_port . '}';
        $hostmacroRes = ZabbixApis::GetHostmacroid($HostId, $macro, $token);
        //当返回值  result 为空  代表没有这个宏   如果error 不为空 代表出现错误
        if (empty($hostmacroRes['result']) || !empty($hostmacroRes['error'])) {
            if (empty($hostmacroRes['result'])) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '未找到相应的宏信息',
                ];
                return $this->renderJSON($arrReturn);
            } elseif (!empty($hostmacroRes['error'])) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '发生错误：' . $hostmacroRes['error']['message'],
                ];
                return $this->renderJSON($arrReturn);
            }
        }
        #取出宏ID
        $hostmacroid = $hostmacroRes['result'][0]['hostmacroid'];
        #获取要更新的值  （这里为带宽）
        $config          = json_decode($IdlePdtRes['config'], true);
        $configbandwidth = $config['configbandwidth'];

        if (preg_match('/\d+/', $configbandwidth, $arr)) {
            $hostmacro_value = $arr[0];
        }
        if ($hostmacro_value == "" || $hostmacro_value == null) {
            $arrReturn = [
                'status' => 0,
                'info'   => '更新的带宽值为空！',
            ];
            return $this->renderJSON($arrReturn);
        }
        if (!is_numeric($hostmacro_value)) {
            $arrReturn = [
                'status' => 0,
                'info'   => '更新的带宽值不是数字！',
            ];
            return $this->renderJSON($arrReturn);
        }
        #更新宏ID 的值
        $UpdateHostmacroResult = ZabbixApis::UpdateHostmacroValue($hostmacroid, $hostmacro_value, $token);

        if ($UpdateHostmacroResult['result']['hostmacroids'][0] == $hostmacroid) {
            #新增操作记录
            $OperationRecordModel = new IdleMachineOperationRecord();
            $recordRes            = $OperationRecordModel->adding_record($IdlePdtRes['id'], $IdlePdtRes['ipmi_ip'], $IdlePdtRes['ip'], $IdlePdtRes['ip2'], 'update_bandwidth_monitoring', $this->getAdminInfo('uname'), '管理员');

            $arrReturn = [
                'status' => 1,
                'info'   => '带宽监控值更新成功！',
            ];
        } else {
            $arrReturn = [
                'status' => 0,
                'info'   => '发生错误：' . $UpdateHostmacroResult['error']['message'],
            ];
        }
        return $this->renderJSON($arrReturn);
    }

    #KVM
    public function actionKvmManage()
    {

        Yii::$app->request->isAjax || die('异常访问');
        $idle_id = $this->post('idle_id');

        $IdlePdtModel = new IdlePdt();

        $IdlePdtRes = $IdlePdtModel->find()->where(['id' => $idle_id])->asArray()->one();

        if (empty($IdlePdtRes)) {
            return $this->renderJSON(['status' => 0, 'info' => '未知的自有机器信息']);
        }

        $ipmi_ip = $IdlePdtRes['ipmi_ip'];

        if ($ipmi_ip == '' || $ipmi_ip == null) {
            return $this->renderJSON(['status' => 0, 'info' => '未配置IPMI信息']);
        }

        $vnc_pwd = uniqid();           #随机密码
        $port    = rand(20000, 30000); #随机端口
        #判断是否已经存在这个IPMI的容器
        $CheckResult = IpmiApi::InspectContainer($ipmi_ip);
        if ($CheckResult == 500) {
            return $this->renderJSON(['status' => 0, 'info' => '检查容器出现错误']);
        }
        $container_name = null;
        if ($CheckResult == 200) {
            $container_name = $ipmi_ip . '-' . $port;
        }

        if ($CheckResult == 404) {
            $container_name = $ipmi_ip;
        }
        if(!$container_name){
            $container_name = $ipmi_ip . '-' . $port;
        }
        #
        $ListRes = IpmiApi::ListContainers();

        if (!empty($ListRes)) {
            $check_str = '/' . $ipmi_ip;
            $nams_list = [];
            $num       = 0;
            foreach ($ListRes as $key => $val) {
                $names = $val['Names'][0];
                if (strpos($names, $check_str) !== false) {
                    $num += 1;
                }
            }

            if ($num >= 3) {
                return $this->renderJSON(['status' => 0, 'info' => '当前连接数超过限制，请先关闭部分连接，稍后再重新连接']);
            }
        }
        #创建容器
        $CreateResult = IpmiApi::ContainerCreate($container_name, $ipmi_ip, $IdlePdtRes['ipmi_type'], $IdlePdtRes['ipmi_name'], $IdlePdtRes['ipmi_pwd'], $vnc_pwd, $port);

        if ($CreateResult != 201) {
            return $this->renderJSON(['status' => 0, 'info' => '创建容器失败']);
        }
        #启动容器
        $StartResult = IpmiApi::StartContainer($container_name);

        if ($StartResult != 204) {
            return $this->renderJSON(['status' => 0, 'info' => '创建容器失败，状态码：' . $StartResult]);
        } else {
            $kvm_config = Yii::$app->params['kvm'];
            $url        = $kvm_config['api_url'];

            $kvm_url = $url . ':' . $port . '/vnc.html?autoconnect=true&password=' . $vnc_pwd . '&resize=scale';

            return $this->renderJSON(['status' => 1, 'info' => $kvm_url]);
        }

    }

    #Mac地址导入
    public function actionMacImport()
    {
        $oSession = Yii::$app->getSession();
        $model    = new UploadForm();
        if (Yii::$app->request->post() && Yii::$app->request->isPost) {
            $model->excelfile = UploadedFile::getInstance($model, 'excelfile');

            $filepath = "upload/excel/";
            if ($model->excelfile) {
                if (!is_dir($filepath)) {
                    mkdir($filepath, 0777);
                    chmod($filepath, 0777);
                }
                if ($model->validate()) {
                    $excelfile = $model->excelfile;
                    $randname  = date("Y") . date("m") . date("d") . date("H") . date("i") . date("s") . rand(1000, 9999) . "." . $excelfile->getExtension();//文件名
                    $excelfile->saveAs($filepath . $randname);                                                                                               //上传
                    $uploadSuccessPath = $filepath . $randname;                                                                                              //路径
                } else {
                    $arrReturn = [
                        'status' => 0,
                        'info'   => '导入文件有误',
                    ];
                    $oSession->setFlash('return', $arrReturn);
                    return $this->redirect(Url::to(['idle-pdt/mac-import']));
                }
            } else {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '导入文件有误',
                ];
                $oSession->setFlash('return', $arrReturn);
                return $this->redirect(Url::to(['idle-pdt/mac-import']));
            }

            //读取文件
            $tag_data = Excel::import($uploadSuccessPath, [
                'setFirstRecordAsKeys' => true,
                'setIndexSheetByName'  => true,
                'getOnlySheet'         => 'sheet1',
            ]);
            #print_r($tag_data);exit;
            $data = [];
            if (!empty($tag_data)) {
                $key_name = array('ipmi_ip', 'mac_addr');
                foreach ($tag_data as $key => $value) {
                    if (!empty($value)) {
                        //$value = array_values($value);
                        $value1 = array_combine($key_name, $value);
                        if (!filter_var(trim($value1['ipmi_ip']), FILTER_VALIDATE_IP)) {
                            unlink($uploadSuccessPath);
                            $arrReturn = [
                                'status' => 0,
                                'info'   => $value1['ipmi_ip'] . '为不合法的IPMI地址',
                            ];
                            $oSession->setFlash('return', $arrReturn);
                            return $this->redirect(Url::to(['idle-pdt/mac-import']));
                        }

                        $pattern = "/^[A-Fa-f0-9]{4}\-[A-Fa-f0-9]{4}\-[A-Fa-f0-9]{4}$/";
                        if (!preg_match($pattern, trim($value1['mac_addr']))) {

                            unlink($uploadSuccessPath);
                            $arrReturn = [
                                'status' => 0,
                                'info'   => $value1['mac_addr'] . '为不合法的Mac地址',
                            ];
                            $oSession->setFlash('return', $arrReturn);
                            return $this->redirect(Url::to(['idle-pdt/mac-import']));
                        }

                        $value1['ipmi_ip']  = trim($value1['ipmi_ip']);
                        $value1['mac_addr'] = trim($value1['mac_addr']);
                        $data[$key]         = $value1;
                    }
                }
            } else {
                unlink($uploadSuccessPath);
                $arrReturn = [
                    'status' => 0,
                    'info'   => '导入文件为空',
                ];
                $oSession->setFlash('return', $arrReturn);
                return $this->redirect(Url::to(['idle-pdt/mac-import']));
            }

            //删除上传的文件
            if (!unlink($uploadSuccessPath)) {
                $arrReturn = [
                    'status' => 0,
                    'info'   => '文件处理失败',
                ];
                $oSession->setFlash('return', $arrReturn);
                return $this->redirect(Url::to(['idle-pdt/mac-import']));
            }
            #print_r($data);die;
            $IdlePdtModel = new IdlePdt();
            #开启事务
            $transaction = \Yii::$app->db->beginTransaction();
            if (!empty($data)) {
                try {
                    foreach ($data as $value2) {
                        $New_IdlePdtModel = clone $IdlePdtModel;
                        $IdlePdtQuery     = $New_IdlePdtModel->find()->where(['ipmi_ip' => $value2['ipmi_ip']])->one();
                        if (empty($IdlePdtQuery)) {
                            continue;
                        }
                        $IdlePdtQuery->mac_addr = $value2['mac_addr'];

                        if ($IdlePdtQuery->update()) {
                            $arrReturn = [
                                'status' => 1,
                                'info'   => 'Mac地址更新成功',
                            ];

                        } else {
                            #事务回滚
                            $transaction->rollBack();
                            $arrReturn = [
                                'status' => 0,
                                'info'   => 'IPMI地址为：' . $value2['ipmi_ip'] . '的Mac地址更新失败，可能Mac未做改变',
                            ];
                            $oSession->setFlash('return', $arrReturn);
                            return $this->redirect(Url::to(['idle-pdt/mac-import']));
                        }
                    }
                    $transaction->commit();
                } catch (Exception $e) {
                    $transaction->rollBack();
                }
            }

            $oSession->setFlash('return', $arrReturn);
            return $this->redirect(Url::to(['idle-pdt/mac-import']));

        } else {
            return $this->render('mac-import');
        }

    }

}
